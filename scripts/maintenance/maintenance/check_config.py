#!/usr/bin/env python3
"""
系统配置检查脚本

功能：
- 检查前端和后端配置文件
- 验证环境变量设置
- 检查数据库连接
- 验证第三方服务配置
- 生成配置报告和修复建议
"""

import os
import sys
import json
import yaml
import sqlite3
from pathlib import Path
from typing import Dict, List, Any
from dataclasses import dataclass, asdict
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/config_check.log', mode='w', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ConfigIssue:
    """配置问题"""
    category: str  # 'error', 'warning', 'suggestion'
    component: str  # 'frontend', 'backend', 'database', 'system'
    key: str
    message: str
    recommendation: str = ""

class SystemConfigChecker:
    """系统配置检查器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.issues: List[ConfigIssue] = []
        
    def check_all_config(self) -> Dict[str, Any]:
        """检查所有配置"""
        logger.info("🔍 开始系统配置检查...")
        
        results = {
            'timestamp': self._get_timestamp(),
            'project_root': str(self.project_root),
            'checks': {
                'frontend': self._check_frontend_config(),
                'backend': self._check_backend_config(),
                'database': self._check_database_config(),
                'system': self._check_system_config(),
                'third_party': self._check_third_party_config()
            },
            'summary': self._generate_summary(),
            'recommendations': self._generate_recommendations()
        }
        
        logger.info("✅ 配置检查完成")
        return results
    
    def _check_frontend_config(self) -> Dict[str, Any]:
        """检查前端配置"""
        logger.info("📱 检查前端配置...")
        
        frontend_dir = self.project_root / 'web_ui' / 'frontend'
        results = {
            'status': 'unknown',
            'files_checked': [],
            'issues': []
        }
        
        # 检查package.json
        package_json_path = frontend_dir / 'package.json'
        if package_json_path.exists():
            results['files_checked'].append('package.json')
            try:
                with open(package_json_path, 'r', encoding='utf-8') as f:
                    package_data = json.load(f)
                
                # 检查必需的依赖
                required_deps = [
                    'react', 'react-dom', 'antd', 'axios', 
                    'react-router-dom', '@reduxjs/toolkit'
                ]
                
                dependencies = package_data.get('dependencies', {})
                for dep in required_deps:
                    if dep not in dependencies:
                        self.issues.append(ConfigIssue(
                            category='warning',
                            component='frontend',
                            key=f'dependency_{dep}',
                            message=f'缺少推荐的依赖包: {dep}',
                            recommendation=f'运行 npm install {dep} 安装'
                        ))
                
                # 检查脚本配置
                scripts = package_data.get('scripts', {})
                required_scripts = ['dev', 'build', 'preview']
                for script in required_scripts:
                    if script not in scripts:
                        self.issues.append(ConfigIssue(
                            category='warning',
                            component='frontend',
                            key=f'script_{script}',
                            message=f'缺少脚本命令: {script}',
                            recommendation='添加必要的npm脚本命令'
                        ))
                        
            except Exception as e:
                self.issues.append(ConfigIssue(
                    category='error',
                    component='frontend',
                    key='package_json',
                    message=f'无法解析package.json: {e}',
                    recommendation='检查package.json文件格式'
                ))
        else:
            self.issues.append(ConfigIssue(
                category='error',
                component='frontend',
                key='package_json',
                message='package.json文件不存在',
                recommendation='在前端目录创建package.json文件'
            ))
        
        # 检查环境配置文件
        env_files = ['.env', '.env.example']
        for env_file in env_files:
            env_path = self.project_root / 'web_ui' / env_file
            if env_path.exists():
                results['files_checked'].append(env_file)
                self._check_env_file(env_path, 'frontend')
            else:
                self.issues.append(ConfigIssue(
                    category='warning',
                    component='frontend',
                    key=env_file,
                    message=f'环境配置文件不存在: {env_file}',
                    recommendation=f'创建{env_file}文件并配置必要的环境变量'
                ))
        
        # 检查Vite配置
        vite_config_path = frontend_dir / 'vite.config.ts'
        if vite_config_path.exists():
            results['files_checked'].append('vite.config.ts')
        else:
            self.issues.append(ConfigIssue(
                category='warning',
                component='frontend',
                key='vite_config',
                message='Vite配置文件不存在',
                recommendation='创建vite.config.ts配置文件'
            ))
        
        results['status'] = 'checked'
        results['issues'] = [asdict(issue) for issue in self.issues if issue.component == 'frontend']
        return results
    
    def _check_backend_config(self) -> Dict[str, Any]:
        """检查后端配置"""
        logger.info("🔧 检查后端配置...")
        
        backend_dir = self.project_root / 'web_ui' / 'backend'
        results = {
            'status': 'unknown',
            'files_checked': [],
            'issues': []
        }
        
        # 检查requirements.txt
        requirements_path = backend_dir / 'requirements.txt'
        if requirements_path.exists():
            results['files_checked'].append('requirements.txt')
            try:
                with open(requirements_path, 'r', encoding='utf-8') as f:
                    requirements = f.read()
                
                # 检查必需的依赖
                required_deps = [
                    'fastapi', 'uvicorn', 'pydantic', 'sqlalchemy',
                    'pandas', 'numpy', 'yfinance', 'akshare'
                ]
                
                for dep in required_deps:
                    if dep not in requirements.lower():
                        self.issues.append(ConfigIssue(
                            category='warning',
                            component='backend',
                            key=f'dependency_{dep}',
                            message=f'缺少推荐的依赖包: {dep}',
                            recommendation=f'在requirements.txt中添加{dep}'
                        ))
                        
            except Exception as e:
                self.issues.append(ConfigIssue(
                    category='error',
                    component='backend',
                    key='requirements',
                    message=f'无法读取requirements.txt: {e}',
                    recommendation='检查requirements.txt文件'
                ))
        else:
            self.issues.append(ConfigIssue(
                category='error',
                component='backend',
                key='requirements',
                message='requirements.txt文件不存在',
                recommendation='创建requirements.txt文件并添加必要的依赖'
            ))
        
        # 检查主程序文件
        main_py_path = backend_dir / 'main.py'
        if main_py_path.exists():
            results['files_checked'].append('main.py')
        else:
            self.issues.append(ConfigIssue(
                category='error',
                component='backend',
                key='main_py',
                message='main.py文件不存在',
                recommendation='创建main.py作为FastAPI应用入口'
            ))
        
        # 检查后端环境配置
        backend_env_path = backend_dir / '.env'
        if backend_env_path.exists():
            results['files_checked'].append('.env')
            self._check_env_file(backend_env_path, 'backend')
        else:
            self.issues.append(ConfigIssue(
                category='warning',
                component='backend',
                key='backend_env',
                message='后端.env文件不存在',
                recommendation='创建后端.env文件并配置必要的环境变量'
            ))
        
        results['status'] = 'checked'
        results['issues'] = [asdict(issue) for issue in self.issues if issue.component == 'backend']
        return results
    
    def _check_database_config(self) -> Dict[str, Any]:
        """检查数据库配置"""
        logger.info("🗄️ 检查数据库配置...")
        
        results = {
            'status': 'unknown',
            'database_type': 'unknown',
            'connection_test': False,
            'issues': []
        }
        
        # 检查数据库目录
        db_dir = self.project_root / 'data' / 'db'
        if not db_dir.exists():
            self.issues.append(ConfigIssue(
                category='warning',
                component='database',
                key='db_directory',
                message='数据库目录不存在',
                recommendation='创建data/db目录'
            ))
            db_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查SQLite数据库文件
        db_files = ['trading_system.db', 'risk_monitoring.db']
        for db_file in db_files:
            db_path = db_dir / db_file
            if db_path.exists():
                results['database_type'] = 'sqlite'
                try:
                    # 测试数据库连接
                    conn = sqlite3.connect(str(db_path))
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    conn.close()
                    
                    results['connection_test'] = True
                    logger.info(f"✅ 数据库连接测试成功: {db_file} (表数量: {len(tables)})")
                    
                except Exception as e:
                    self.issues.append(ConfigIssue(
                        category='error',
                        component='database',
                        key=f'db_connection_{db_file}',
                        message=f'数据库连接失败: {db_file} - {e}',
                        recommendation='检查数据库文件权限和完整性'
                    ))
            else:
                self.issues.append(ConfigIssue(
                    category='warning',
                    component='database',
                    key=f'db_file_{db_file}',
                    message=f'数据库文件不存在: {db_file}',
                    recommendation='运行数据库初始化脚本创建数据库'
                ))
        
        results['status'] = 'checked'
        results['issues'] = [asdict(issue) for issue in self.issues if issue.component == 'database']
        return results
    
    def _check_system_config(self) -> Dict[str, Any]:
        """检查系统配置"""
        logger.info("⚙️ 检查系统配置...")
        
        results = {
            'status': 'unknown',
            'files_checked': [],
            'issues': []
        }
        
        # 检查主配置文件
        config_files = ['config/config.yaml', 'config/data_sources.yaml']
        for config_file in config_files:
            config_path = self.project_root / config_file
            if config_path.exists():
                results['files_checked'].append(config_file)
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        yaml.safe_load(f)
                    logger.info(f"✅ 配置文件格式正确: {config_file}")
                except Exception as e:
                    self.issues.append(ConfigIssue(
                        category='error',
                        component='system',
                        key=config_file.replace('/', '_'),
                        message=f'配置文件格式错误: {config_file} - {e}',
                        recommendation='检查YAML文件格式'
                    ))
            else:
                self.issues.append(ConfigIssue(
                    category='warning',
                    component='system',
                    key=config_file.replace('/', '_'),
                    message=f'配置文件不存在: {config_file}',
                    recommendation=f'创建{config_file}配置文件'
                ))
        
        # 检查必要目录
        required_dirs = ['data', 'logs', 'backup', 'config']
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if not dir_path.exists():
                self.issues.append(ConfigIssue(
                    category='warning',
                    component='system',
                    key=f'directory_{dir_name}',
                    message=f'必要目录不存在: {dir_name}',
                    recommendation=f'创建{dir_name}目录'
                ))
                dir_path.mkdir(parents=True, exist_ok=True)
        
        # 检查启动脚本
        start_script = self.project_root / 'start_system.sh'
        if start_script.exists():
            results['files_checked'].append('start_system.sh')
            if not os.access(start_script, os.X_OK):
                self.issues.append(ConfigIssue(
                    category='warning',
                    component='system',
                    key='start_script_permission',
                    message='启动脚本没有执行权限',
                    recommendation='运行 chmod +x start_system.sh'
                ))
        else:
            self.issues.append(ConfigIssue(
                category='error',
                component='system',
                key='start_script',
                message='启动脚本不存在',
                recommendation='创建start_system.sh启动脚本'
            ))
        
        results['status'] = 'checked'
        results['issues'] = [asdict(issue) for issue in self.issues if issue.component == 'system']
        return results
    
    def _check_third_party_config(self) -> Dict[str, Any]:
        """检查第三方服务配置"""
        logger.info("🔌 检查第三方服务配置...")
        
        results = {
            'status': 'unknown',
            'services': {},
            'issues': []
        }
        
        # 检查数据源配置
        data_sources_path = self.project_root / 'config' / 'data_sources.yaml'
        if data_sources_path.exists():
            try:
                with open(data_sources_path, 'r', encoding='utf-8') as f:
                    data_sources = yaml.safe_load(f)
                
                for source_name, source_config in data_sources.items():
                    results['services'][source_name] = {
                        'enabled': source_config.get('enabled', False),
                        'has_credentials': bool(source_config.get('credentials', {}))
                    }
                    
                    # 检查API密钥
                    credentials = source_config.get('credentials', {})
                    if source_name == 'fred' and not credentials.get('api_key'):
                        self.issues.append(ConfigIssue(
                            category='warning',
                            component='third_party',
                            key='fred_api_key',
                            message='FRED API密钥未配置',
                            recommendation='在config/data_sources.yaml中配置FRED API密钥'
                        ))
                    
                    if source_name == 'binance':
                        if not credentials.get('api_key') or not credentials.get('api_secret'):
                            self.issues.append(ConfigIssue(
                                category='warning',
                                component='third_party',
                                key='binance_credentials',
                                message='Binance API凭据未配置',
                                recommendation='配置Binance API密钥和密钥'
                            ))
                            
            except Exception as e:
                self.issues.append(ConfigIssue(
                    category='error',
                    component='third_party',
                    key='data_sources_config',
                    message=f'无法读取数据源配置: {e}',
                    recommendation='检查config/data_sources.yaml文件'
                ))
        
        results['status'] = 'checked'
        results['issues'] = [asdict(issue) for issue in self.issues if issue.component == 'third_party']
        return results
    
    def _check_env_file(self, env_path: Path, component: str):
        """检查环境变量文件"""
        try:
            with open(env_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查必需的环境变量
            if component == 'frontend':
                required_vars = [
                    'VITE_API_BASE_URL',
                    'VITE_WS_URL',
                    'VITE_APP_TITLE'
                ]
            else:  # backend
                required_vars = [
                    'HOST',
                    'PORT',
                    'DATABASE_URL',
                    'SECRET_KEY'
                ]
            
            for var in required_vars:
                if f'{var}=' not in content:
                    self.issues.append(ConfigIssue(
                        category='warning',
                        component=component,
                        key=f'env_var_{var}',
                        message=f'环境变量未配置: {var}',
                        recommendation=f'在{env_path.name}中添加{var}配置'
                    ))
                    
        except Exception as e:
            self.issues.append(ConfigIssue(
                category='error',
                component=component,
                key='env_file_read',
                message=f'无法读取环境文件: {e}',
                recommendation=f'检查{env_path}文件权限和格式'
            ))
    
    def _generate_summary(self) -> Dict[str, Any]:
        """生成检查摘要"""
        summary = {
            'total_issues': len(self.issues),
            'by_category': {},
            'by_component': {},
            'critical_issues': 0
        }
        
        for issue in self.issues:
            # 按类别统计
            if issue.category not in summary['by_category']:
                summary['by_category'][issue.category] = 0
            summary['by_category'][issue.category] += 1
            
            # 按组件统计
            if issue.component not in summary['by_component']:
                summary['by_component'][issue.component] = 0
            summary['by_component'][issue.component] += 1
            
            # 统计严重问题
            if issue.category == 'error':
                summary['critical_issues'] += 1
        
        return summary
    
    def _generate_recommendations(self) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # 按优先级排序问题
        critical_issues = [issue for issue in self.issues if issue.category == 'error']
        warning_issues = [issue for issue in self.issues if issue.category == 'warning']
        
        if critical_issues:
            recommendations.append("🚨 优先修复以下严重问题:")
            for issue in critical_issues:
                recommendations.append(f"  - {issue.message}")
                if issue.recommendation:
                    recommendations.append(f"    建议: {issue.recommendation}")
        
        if warning_issues:
            recommendations.append("⚠️ 建议修复以下警告:")
            for issue in warning_issues[:5]:  # 只显示前5个警告
                recommendations.append(f"  - {issue.message}")
                if issue.recommendation:
                    recommendations.append(f"    建议: {issue.recommendation}")
        
        return recommendations
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def main():
    """主函数"""
    logger.info("🔍 量化交易系统配置检查工具")
    logger.info("=" * 50)
    
    # 确保日志目录存在
    log_dir = Path('logs')
    log_dir.mkdir(exist_ok=True)
    
    try:
        checker = SystemConfigChecker()
        results = checker.check_all_config()
        
        # 显示摘要
        summary = results['summary']
        logger.info(f"\n📊 检查摘要:")
        logger.info(f"  总问题数: {summary['total_issues']}")
        logger.info(f"  严重问题: {summary['critical_issues']}")
        logger.info(f"  按类别: {summary['by_category']}")
        logger.info(f"  按组件: {summary['by_component']}")
        
        # 显示建议
        if results['recommendations']:
            logger.info(f"\n💡 修复建议:")
            for rec in results['recommendations']:
                logger.info(rec)
        
        # 保存详细报告
        report_path = Path('logs/config_check_report.json')
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"\n📄 详细报告已保存到: {report_path}")
        
        # 返回退出码
        return 1 if summary['critical_issues'] > 0 else 0
        
    except Exception as e:
        logger.error(f"配置检查失败: {e}")
        logger.info(f"❌ 配置检查失败: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())