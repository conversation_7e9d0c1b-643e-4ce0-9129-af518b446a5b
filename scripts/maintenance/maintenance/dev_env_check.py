#!/usr/bin/env python3
"""
开发环境诊断脚本

功能：
- 检查开发环境依赖
- 验证端口可用性
- 测试热重载功能
- 检查代理配置
- 提供环境修复建议
"""

import os
import sys
import json
import subprocess
import socket
import time
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DevEnvironmentChecker:
    """开发环境检查器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.issues = []
        
    def check_all(self) -> Dict[str, Any]:
        """检查所有开发环境配置"""
        logger.info("🔍 开始开发环境检查...")
        
        results = {
            'timestamp': self._get_timestamp(),
            'checks': {
                'system_requirements': self._check_system_requirements(),
                'port_availability': self._check_port_availability(),
                'dependencies': self._check_dependencies(),
                'services': self._check_services(),
                'proxy_config': self._check_proxy_config(),
                'hot_reload': self._check_hot_reload_config()
            },
            'recommendations': self._generate_recommendations()
        }
        
        logger.info("✅ 开发环境检查完成")
        return results
    
    def _check_system_requirements(self) -> Dict[str, Any]:
        """检查系统要求"""
        logger.info("🖥️ 检查系统要求...")
        
        results = {
            'status': 'unknown',
            'python': {},
            'node': {},
            'npm': {},
            'issues': []
        }
        
        # 检查Python
        try:
            python_version = subprocess.check_output(
                ['python3', '--version'], 
                text=True
            ).strip()
            results['python'] = {
                'version': python_version,
                'available': True
            }
            logger.info(f"✅ {python_version}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            results['python'] = {'available': False}
            self.issues.append({
                'category': 'error',
                'component': 'system',
                'message': 'Python3 未安装或不可用',
                'recommendation': '安装Python 3.8或更高版本'
            })
        
        # 检查Node.js
        try:
            node_version = subprocess.check_output(
                ['node', '--version'], 
                text=True
            ).strip()
            results['node'] = {
                'version': node_version,
                'available': True
            }
            
            # 检查版本是否满足要求
            version_num = int(node_version.replace('v', '').split('.')[0])
            if version_num < 16:
                self.issues.append({
                    'category': 'warning',
                    'component': 'system',
                    'message': f'Node.js版本过低: {node_version}',
                    'recommendation': '建议升级到Node.js 16或更高版本'
                })
            else:
                logger.info(f"✅ Node.js {node_version}")
                
        except (subprocess.CalledProcessError, FileNotFoundError):
            results['node'] = {'available': False}
            self.issues.append({
                'category': 'error',
                'component': 'system',
                'message': 'Node.js 未安装或不可用',
                'recommendation': '安装Node.js 16或更高版本'
            })
        
        # 检查npm
        try:
            npm_version = subprocess.check_output(
                ['npm', '--version'], 
                text=True
            ).strip()
            results['npm'] = {
                'version': npm_version,
                'available': True
            }
            logger.info(f"✅ npm {npm_version}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            results['npm'] = {'available': False}
            self.issues.append({
                'category': 'error',
                'component': 'system',
                'message': 'npm 未安装或不可用',
                'recommendation': '安装npm包管理器'
            })
        
        results['status'] = 'checked'
        return results
    
    def _check_port_availability(self) -> Dict[str, Any]:
        """检查端口可用性"""
        logger.info("🔌 检查端口可用性...")
        
        results = {
            'status': 'unknown',
            'ports': {},
            'issues': []
        }
        
        # 需要检查的端口
        ports_to_check = {
            3000: '前端开发服务器',
            3001: 'HMR热重载',
            8000: '后端API服务器'
        }
        
        for port, description in ports_to_check.items():
            is_available = self._is_port_available(port)
            results['ports'][port] = {
                'description': description,
                'available': is_available,
                'process': self._get_port_process(port) if not is_available else None
            }
            
            if is_available:
                logger.info(f"✅ 端口 {port} ({description}) 可用")
            else:
                process_info = self._get_port_process(port)
                logger.warning(f"⚠️ 端口 {port} ({description}) 被占用: {process_info}")
                self.issues.append({
                    'category': 'warning',
                    'component': 'ports',
                    'message': f'端口 {port} ({description}) 被占用',
                    'recommendation': f'停止占用端口 {port} 的进程或使用其他端口'
                })
        
        results['status'] = 'checked'
        return results
    
    def _check_dependencies(self) -> Dict[str, Any]:
        """检查依赖安装情况"""
        logger.info("📦 检查依赖安装情况...")
        
        results = {
            'status': 'unknown',
            'frontend': {},
            'backend': {},
            'issues': []
        }
        
        # 检查前端依赖
        frontend_dir = self.project_root / 'web_ui' / 'frontend'
        node_modules_dir = frontend_dir / 'node_modules'
        
        if node_modules_dir.exists():
            results['frontend']['node_modules_exists'] = True
            
            # 检查关键依赖
            key_deps = ['react', 'antd', 'vite', '@vitejs/plugin-react']
            missing_deps = []
            
            for dep in key_deps:
                dep_path = node_modules_dir / dep
                if not dep_path.exists():
                    missing_deps.append(dep)
            
            results['frontend']['missing_dependencies'] = missing_deps
            
            if missing_deps:
                self.issues.append({
                    'category': 'warning',
                    'component': 'frontend',
                    'message': f'前端缺少依赖: {", ".join(missing_deps)}',
                    'recommendation': '运行 npm install 安装依赖'
                })
            else:
                logger.info("✅ 前端依赖完整")
        else:
            results['frontend']['node_modules_exists'] = False
            self.issues.append({
                'category': 'error',
                'component': 'frontend',
                'message': '前端依赖未安装',
                'recommendation': '在 web_ui/frontend 目录运行 npm install'
            })
        
        # 检查后端依赖
        backend_dir = self.project_root / 'web_ui' / 'backend'
        venv_dir = backend_dir / 'venv'
        
        if venv_dir.exists():
            results['backend']['venv_exists'] = True
            logger.info("✅ Python虚拟环境存在")
        else:
            results['backend']['venv_exists'] = False
            self.issues.append({
                'category': 'warning',
                'component': 'backend',
                'message': 'Python虚拟环境不存在',
                'recommendation': '在 web_ui/backend 目录运行 python3 -m venv venv'
            })
        
        results['status'] = 'checked'
        return results
    
    def _check_services(self) -> Dict[str, Any]:
        """检查服务状态"""
        logger.info("🚀 检查服务状态...")
        
        results = {
            'status': 'unknown',
            'backend_api': {},
            'frontend_dev': {},
            'issues': []
        }
        
        # 检查后端API服务
        try:
            response = requests.get('http://localhost:8000/health', timeout=5)
            results['backend_api'] = {
                'running': True,
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds()
            }
            logger.info(f"✅ 后端API服务运行正常 (响应时间: {response.elapsed.total_seconds():.2f}s)")
        except requests.exceptions.RequestException as e:
            results['backend_api'] = {
                'running': False,
                'error': str(e)
            }
            logger.warning("⚠️ 后端API服务未运行")
        
        # 检查前端开发服务器
        try:
            response = requests.get('http://localhost:3000', timeout=5)
            results['frontend_dev'] = {
                'running': True,
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds()
            }
            logger.info(f"✅ 前端开发服务器运行正常 (响应时间: {response.elapsed.total_seconds():.2f}s)")
        except requests.exceptions.RequestException as e:
            results['frontend_dev'] = {
                'running': False,
                'error': str(e)
            }
            logger.warning("⚠️ 前端开发服务器未运行")
        
        results['status'] = 'checked'
        return results
    
    def _check_proxy_config(self) -> Dict[str, Any]:
        """检查代理配置"""
        logger.info("🔄 检查代理配置...")
        
        results = {
            'status': 'unknown',
            'vite_config': {},
            'proxy_test': {},
            'issues': []
        }
        
        # 检查Vite配置文件
        vite_config_path = self.project_root / 'web_ui' / 'frontend' / 'vite.config.ts'
        if vite_config_path.exists():
            results['vite_config']['exists'] = True
            
            # 读取配置内容
            try:
                with open(vite_config_path, 'r', encoding='utf-8') as f:
                    config_content = f.read()
                
                # 检查代理配置
                if 'proxy' in config_content and '/api' in config_content:
                    results['vite_config']['has_api_proxy'] = True
                    logger.info("✅ API代理配置存在")
                else:
                    results['vite_config']['has_api_proxy'] = False
                    self.issues.append({
                        'category': 'warning',
                        'component': 'proxy',
                        'message': 'Vite配置中缺少API代理设置',
                        'recommendation': '在vite.config.ts中配置API代理'
                    })
                
                if '/ws' in config_content:
                    results['vite_config']['has_ws_proxy'] = True
                    logger.info("✅ WebSocket代理配置存在")
                else:
                    results['vite_config']['has_ws_proxy'] = False
                    self.issues.append({
                        'category': 'warning',
                        'component': 'proxy',
                        'message': 'Vite配置中缺少WebSocket代理设置',
                        'recommendation': '在vite.config.ts中配置WebSocket代理'
                    })
                    
            except Exception as e:
                self.issues.append({
                    'category': 'error',
                    'component': 'proxy',
                    'message': f'无法读取Vite配置文件: {e}',
                    'recommendation': '检查vite.config.ts文件格式'
                })
        else:
            results['vite_config']['exists'] = False
            self.issues.append({
                'category': 'error',
                'component': 'proxy',
                'message': 'Vite配置文件不存在',
                'recommendation': '创建vite.config.ts配置文件'
            })
        
        results['status'] = 'checked'
        return results
    
    def _check_hot_reload_config(self) -> Dict[str, Any]:
        """检查热重载配置"""
        logger.info("🔥 检查热重载配置...")
        
        results = {
            'status': 'unknown',
            'hmr_config': {},
            'issues': []
        }
        
        # 检查环境变量配置
        env_path = self.project_root / 'web_ui' / '.env'
        if env_path.exists():
            try:
                with open(env_path, 'r', encoding='utf-8') as f:
                    env_content = f.read()
                
                # 检查HMR端口配置
                if 'VITE_HMR_PORT' in env_content:
                    results['hmr_config']['has_hmr_port'] = True
                    logger.info("✅ HMR端口配置存在")
                else:
                    results['hmr_config']['has_hmr_port'] = False
                    self.issues.append({
                        'category': 'suggestion',
                        'component': 'hmr',
                        'message': '建议配置HMR端口',
                        'recommendation': '在.env文件中添加VITE_HMR_PORT配置'
                    })
                
            except Exception as e:
                self.issues.append({
                    'category': 'error',
                    'component': 'hmr',
                    'message': f'无法读取环境配置文件: {e}',
                    'recommendation': '检查.env文件格式'
                })
        
        results['status'] = 'checked'
        return results
    
    def _is_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                return result != 0
        except Exception:
            return True
    
    def _get_port_process(self, port: int) -> Optional[str]:
        """获取占用端口的进程信息"""
        try:
            if sys.platform == 'darwin' or sys.platform == 'linux':
                result = subprocess.check_output(
                    ['lsof', '-ti', f':{port}'], 
                    text=True, 
                    stderr=subprocess.DEVNULL
                ).strip()
                if result:
                    pid = result.split('\n')[0]
                    process_info = subprocess.check_output(
                        ['ps', '-p', pid, '-o', 'comm='], 
                        text=True,
                        stderr=subprocess.DEVNULL
                    ).strip()
                    return f"PID {pid} ({process_info})"
            return "未知进程"
        except Exception:
            return None
    
    def _generate_recommendations(self) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # 按类别分组问题
        errors = [issue for issue in self.issues if issue['category'] == 'error']
        warnings = [issue for issue in self.issues if issue['category'] == 'warning']
        suggestions = [issue for issue in self.issues if issue['category'] == 'suggestion']
        
        if errors:
            recommendations.append("🚨 需要立即修复的问题:")
            for issue in errors:
                recommendations.append(f"  - {issue['message']}")
                recommendations.append(f"    解决方案: {issue['recommendation']}")
        
        if warnings:
            recommendations.append("⚠️ 建议修复的问题:")
            for issue in warnings:
                recommendations.append(f"  - {issue['message']}")
                recommendations.append(f"    建议: {issue['recommendation']}")
        
        if suggestions:
            recommendations.append("💡 优化建议:")
            for issue in suggestions:
                recommendations.append(f"  - {issue['message']}")
                recommendations.append(f"    建议: {issue['recommendation']}")
        
        # 添加通用建议
        recommendations.extend([
            "",
            "🛠️ 开发环境启动步骤:",
            "  1. 确保所有依赖已安装",
            "  2. 检查端口是否可用",
            "  3. 启动后端服务: cd web_ui/backend && python main.py",
            "  4. 启动前端服务: cd web_ui/frontend && npm run dev",
            "  5. 访问 http://localhost:3000 查看应用"
        ])
        
        return recommendations
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

def main():
    """主函数"""
    logger.info("🔍 开发环境诊断工具")
    logger.info("=" * 50)
    
    try:
        checker = DevEnvironmentChecker()
        results = checker.check_all()
        
        # 显示检查结果
        checks = results['checks']
        
        logger.info(f"\n📊 检查结果:")
        logger.info(f"  系统要求: {'✅' if checks['system_requirements']['status'] == 'checked' else '❌'}")
        logger.info(f"  端口可用性: {'✅' if checks['port_availability']['status'] == 'checked' else '❌'}")
        logger.info(f"  依赖安装: {'✅' if checks['dependencies']['status'] == 'checked' else '❌'}")
        logger.info(f"  服务状态: {'✅' if checks['services']['status'] == 'checked' else '❌'}")
        logger.info(f"  代理配置: {'✅' if checks['proxy_config']['status'] == 'checked' else '❌'}")
        logger.info(f"  热重载: {'✅' if checks['hot_reload']['status'] == 'checked' else '❌'}")
        
        # 显示建议
        if results['recommendations']:
            logger.info(f"\n💡 修复建议:")
            for rec in results['recommendations']:
                logger.info(rec)
        
        # 保存详细报告
        report_path = Path('logs/dev_env_check_report.json')
        report_path.parent.mkdir(exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"\n📄 详细报告已保存到: {report_path}")
        
        # 返回退出码
        error_count = len([issue for issue in checker.issues if issue['category'] == 'error'])
        return 1 if error_count > 0 else 0
        
    except Exception as e:
        logger.error(f"开发环境检查失败: {e}")
        logger.info(f"❌ 开发环境检查失败: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())