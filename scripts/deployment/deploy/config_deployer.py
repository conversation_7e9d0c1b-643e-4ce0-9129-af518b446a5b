#!/usr/bin/env python3
"""
配置文件部署工具

支持环境特定配置部署，配置文件验证和模板管理。
确保配置文件在不同环境中的正确部署和管理。
"""

import os
import sys
import json
import yaml
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
import shutil
from datetime import datetime
import hashlib
import tempfile

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/config_deployment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ConfigDeployer:
    """配置文件部署器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.config_dir = self.project_root / "config"
        self.environments_dir = self.config_dir / "environments"
        self.templates_dir = self.config_dir / "templates"
        self.deployment_log = []
        
        # 确保必要目录存在
        self.config_dir.mkdir(exist_ok=True)
        self.environments_dir.mkdir(exist_ok=True)
        self.templates_dir.mkdir(exist_ok=True)
    
    def get_available_environments(self) -> List[str]:
        """获取可用的环境列表"""
        try:
            environments = []
            if self.environments_dir.exists():
                for env_dir in self.environments_dir.iterdir():
                    if env_dir.is_dir():
                        environments.append(env_dir.name)
            return sorted(environments)
        except Exception as e:
            logger.error(f"获取环境列表失败: {e}")
            return []
    
    def load_environment_config(self, environment: str) -> Dict:
        """加载环境特定配置"""
        try:
            env_config_file = self.environments_dir / environment / "config.yaml"
            if env_config_file.exists():
                with open(env_config_file, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f) or {}
            else:
                logger.warning(f"环境配置文件不存在: {env_config_file}")
                return {}
        except Exception as e:
            logger.error(f"加载环境配置失败 {environment}: {e}")
            return {}
    
    def get_config_templates(self) -> List[str]:
        """获取可用的配置模板列表"""
        try:
            templates = []
            if self.templates_dir.exists():
                for template_file in self.templates_dir.iterdir():
                    if template_file.is_file() and template_file.suffix in ['.yaml', '.json', '.env', '.template']:
                        templates.append(template_file.name)
            return sorted(templates)
        except Exception as e:
            logger.error(f"获取模板列表失败: {e}")
            return []
    
    def render_template(self, template_path: Path, variables: Dict) -> str:
        """渲染配置模板"""
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # 简单的变量替换
            for key, value in variables.items():
                placeholder = f"{{{{{key}}}}}"
                template_content = template_content.replace(placeholder, str(value))
            
            return template_content
            
        except Exception as e:
            logger.error(f"渲染模板失败 {template_path}: {e}")
            return ""
    
    def backup_existing_config(self, config_file: Path) -> Optional[Path]:
        """备份现有配置文件"""
        try:
            if not config_file.exists():
                return None
            
            backup_dir = self.project_root / "data" / "backups" / "configs"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"{config_file.name}.backup.{timestamp}"
            backup_path = backup_dir / backup_name
            
            shutil.copy2(config_file, backup_path)
            logger.info(f"配置文件已备份: {backup_path}")
            
            return backup_path
            
        except Exception as e:
            logger.error(f"备份配置文件失败 {config_file}: {e}")
            return None
    
    def validate_config_file(self, config_file: Path) -> bool:
        """验证配置文件格式和内容"""
        try:
            if not config_file.exists():
                logger.error(f"配置文件不存在: {config_file}")
                return False
            
            # 根据文件扩展名验证格式
            if config_file.suffix == '.yaml' or config_file.suffix == '.yml':
                with open(config_file, 'r', encoding='utf-8') as f:
                    yaml.safe_load(f)
            elif config_file.suffix == '.json':
                with open(config_file, 'r', encoding='utf-8') as f:
                    json.load(f)
            elif config_file.suffix == '.env':
                # 验证.env文件格式
                with open(config_file, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        line = line.strip()
                        if line and not line.startswith('#'):
                            if '=' not in line:
                                logger.error(f"环境变量格式错误 {config_file}:{line_num}: {line}")
                                return False
            
            logger.info(f"配置文件验证通过: {config_file}")
            return True
            
        except Exception as e:
            logger.error(f"配置文件验证失败 {config_file}: {e}")
            return False
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return ""
    
    def deploy_config_for_environment(self, environment: str, force: bool = False) -> bool:
        """为指定环境部署配置文件"""
        try:
            logger.info(f"开始为环境 '{environment}' 部署配置...")
            
            # 检查环境是否存在
            env_dir = self.environments_dir / environment
            if not env_dir.exists():
                logger.error(f"环境目录不存在: {env_dir}")
                return False
            
            # 加载环境配置
            env_config = self.load_environment_config(environment)
            if not env_config:
                logger.warning(f"环境 '{environment}' 没有配置文件")
            
            # 获取部署配置
            deployment_config = env_config.get('deployment', {})
            config_mappings = deployment_config.get('config_files', {})
            
            success_count = 0
            total_count = len(config_mappings)
            
            for target_path, source_config in config_mappings.items():
                if self._deploy_single_config(environment, target_path, source_config, env_config, force):
                    success_count += 1
            
            logger.info(f"环境 '{environment}' 配置部署完成: {success_count}/{total_count} 成功")
            return success_count == total_count
            
        except Exception as e:
            logger.error(f"环境配置部署失败 {environment}: {e}")
            return False
    
    def _deploy_single_config(self, environment: str, target_path: str, source_config: Dict, env_config: Dict, force: bool) -> bool:
        """部署单个配置文件"""
        try:
            target_file = self.project_root / target_path
            
            # 确保目标目录存在
            target_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 检查是否需要覆盖
            if target_file.exists() and not force:
                logger.info(f"配置文件已存在，跳过: {target_file}")
                return True
            
            # 备份现有文件
            backup_path = None
            if target_file.exists():
                backup_path = self.backup_existing_config(target_file)
            
            # 获取源配置
            source_type = source_config.get('type', 'template')
            source_path = source_config.get('source', '')
            
            if source_type == 'template':
                # 从模板生成配置
                template_path = self.templates_dir / source_path
                if not template_path.exists():
                    logger.error(f"模板文件不存在: {template_path}")
                    return False
                
                # 准备模板变量
                template_variables = env_config.get('variables', {})
                template_variables.update(source_config.get('variables', {}))
                
                # 渲染模板
                rendered_content = self.render_template(template_path, template_variables)
                if not rendered_content:
                    return False
                
                # 写入目标文件
                with open(target_file, 'w', encoding='utf-8') as f:
                    f.write(rendered_content)
                
            elif source_type == 'copy':
                # 直接复制文件
                source_file = self.environments_dir / environment / source_path
                if not source_file.exists():
                    logger.error(f"源文件不存在: {source_file}")
                    return False
                
                shutil.copy2(source_file, target_file)
            
            else:
                logger.error(f"不支持的源类型: {source_type}")
                return False
            
            # 验证生成的配置文件
            if not self.validate_config_file(target_file):
                # 如果验证失败，恢复备份
                if backup_path and backup_path.exists():
                    shutil.copy2(backup_path, target_file)
                    logger.info(f"配置验证失败，已恢复备份: {target_file}")
                return False
            
            # 记录部署日志
            self.deployment_log.append({
                "action": "deploy_config",
                "environment": environment,
                "target_path": str(target_file),
                "source_type": source_type,
                "source_path": source_path,
                "backup_path": str(backup_path) if backup_path else None,
                "file_hash": self.calculate_file_hash(target_file),
                "timestamp": datetime.now().isoformat()
            })
            
            logger.info(f"配置文件部署成功: {target_file}")
            return True
            
        except Exception as e:
            logger.error(f"部署配置文件失败 {target_path}: {e}")
            return False
    
    def create_environment_template(self, environment: str) -> bool:
        """创建环境配置模板"""
        try:
            env_dir = self.environments_dir / environment
            env_dir.mkdir(exist_ok=True)
            
            # 创建环境配置文件
            config_file = env_dir / "config.yaml"
            if not config_file.exists():
                template_config = {
                    "environment": environment,
                    "description": f"{environment}环境配置",
                    "variables": {
                        "environment": environment,
                        "debug": environment == "development",
                        "log_level": "DEBUG" if environment == "development" else "INFO"
                    },
                    "deployment": {
                        "config_files": {
                            "config/config.yaml": {
                                "type": "template",
                                "source": "config.yaml.template",
                                "variables": {}
                            }
                        }
                    }
                }
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    yaml.dump(template_config, f, default_flow_style=False, allow_unicode=True)
                
                logger.info(f"环境配置模板已创建: {config_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"创建环境模板失败 {environment}: {e}")
            return False
    
    def list_deployments(self) -> List[Dict]:
        """列出所有部署记录"""
        try:
            return self.deployment_log.copy()
        except Exception as e:
            logger.error(f"获取部署记录失败: {e}")
            return []
    
    def save_deployment_log(self):
        """保存部署日志"""
        try:
            log_dir = self.project_root / "logs" / "deployment"
            log_dir.mkdir(parents=True, exist_ok=True)
            
            log_file = log_dir / f"config_deployment_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(self.deployment_log, f, ensure_ascii=False, indent=2)
            
            logger.info(f"部署日志已保存: {log_file}")
            
        except Exception as e:
            logger.error(f"保存部署日志失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="配置文件部署工具")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    parser.add_argument("--environment", "-e", help="目标环境名称")
    parser.add_argument("--list-environments", action="store_true", help="列出可用环境")
    parser.add_argument("--list-templates", action="store_true", help="列出可用模板")
    parser.add_argument("--create-env", help="创建新环境模板")
    parser.add_argument("--force", "-f", action="store_true", help="强制覆盖现有配置")
    parser.add_argument("--validate-only", action="store_true", help="仅验证配置文件")
    
    args = parser.parse_args()
    
    try:
        deployer = ConfigDeployer(args.project_root)
        
        if args.list_environments:
            environments = deployer.get_available_environments()
            logger.info("可用环境:")
            for env in environments:
                logger.info(f"  - {env}")
            return
        
        if args.list_templates:
            templates = deployer.get_config_templates()
            logger.info("可用模板:")
            for template in templates:
                logger.info(f"  - {template}")
            return
        
        if args.create_env:
            success = deployer.create_environment_template(args.create_env)
            sys.exit(0 if success else 1)
        
        if args.environment:
            if args.validate_only:
                # 仅验证配置
                env_config = deployer.load_environment_config(args.environment)
                if env_config:
                    logger.info(f"环境 '{args.environment}' 配置验证通过")
                    sys.exit(0)
                else:
                    logger.error(f"环境 '{args.environment}' 配置验证失败")
                    sys.exit(1)
            else:
                # 部署配置
                success = deployer.deploy_config_for_environment(args.environment, args.force)
                deployer.save_deployment_log()
                sys.exit(0 if success else 1)
        
        parser.print_help()
        
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()