#!/usr/bin/env python3
"""
端到端测试持续集成脚本

本脚本用于自动化执行端到端功能测试，支持持续集成环境，
包括用户工作流测试、系统可靠性测试、数据完整性测试和安全措施测试。

功能特性：
1. 自动化测试环境准备
2. 端到端测试执行
3. 测试结果收集和分析
4. CI/CD集成支持
5. 测试报告生成
6. 失败测试自动重试

使用方法：
    python scripts/run_e2e_tests.py [options]

选项：
    --environment, -e   测试环境 (dev/staging/prod)
    --suite, -s         测试套件 (user_workflows/reliability/data_integrity/security)
    --parallel, -p      并行执行测试
    --retry, -r         失败重试次数 (默认: 2)
    --output, -o        输出目录 (默认: reports/e2e)
    --ci                CI模式（简化输出）
    --timeout           测试超时时间（秒，默认: 1800）

作者: 系统修复团队
版本: 1.0.0
创建日期: 2025-01-11
"""

import os
import sys
import argparse
import asyncio
import subprocess
import json
import time
import signal
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import logging
import tempfile

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class E2ETestRunner:
    """端到端测试运行器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        self.temp_dir = None
        
        # 设置日志
        self.setup_logging()
        
        # 测试套件定义
        self.test_suites = {
            'user_workflows': {
                'path': 'tests/end_to_end/test_user_workflows.py',
                'description': '用户工作流测试',
                'priority': 1,
                'timeout': 600,
                'dependencies': ['system_startup']
            },
            'system_reliability': {
                'path': 'tests/end_to_end/test_user_workflows.py::TestSystemReliability',
                'description': '系统可靠性测试',
                'priority': 2,
                'timeout': 900,
                'dependencies': ['user_workflows']
            },
            'data_integrity': {
                'path': 'tests/end_to_end/test_data_integrity.py',
                'description': '数据完整性测试',
                'priority': 3,
                'timeout': 720,
                'dependencies': []
            },
            'security_measures': {
                'path': 'tests/end_to_end/test_data_integrity.py::TestSecurityMeasures',
                'description': '安全措施测试',
                'priority': 4,
                'timeout': 480,
                'dependencies': ['data_integrity']
            },
            'comprehensive_system': {
                'path': 'tests/end_to_end/comprehensive_system_test.py',
                'description': '综合系统测试',
                'priority': 5,
                'timeout': 300,
                'dependencies': []
            }
        }
        
        # 环境配置
        self.environment_configs = {
            'dev': {
                'database_url': 'sqlite:///test_dev.db',
                'api_base_url': 'http://localhost:8000',
                'log_level': 'DEBUG',
                'timeout_multiplier': 1.0
            },
            'staging': {
                'database_url': 'sqlite:///test_staging.db',
                'api_base_url': 'http://staging.example.com',
                'log_level': 'INFO',
                'timeout_multiplier': 1.5
            },
            'prod': {
                'database_url': 'sqlite:///test_prod.db',
                'api_base_url': 'http://prod.example.com',
                'log_level': 'WARNING',
                'timeout_multiplier': 2.0
            }
        }
    
    def setup_logging(self):
        """设置日志配置"""
        log_level = logging.INFO if self.config.get('ci') else logging.DEBUG
        
        # 创建输出目录
        self.config['output_dir'].mkdir(parents=True, exist_ok=True)
        
        # 配置日志格式
        if self.config.get('ci'):
            # CI模式：简化格式
            log_format = '%(levelname)s: %(message)s'
        else:
            # 开发模式：详细格式
            log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(
                    self.config['output_dir'] / 'e2e_test.log'
                )
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    async def run_all_tests(self) -> Dict:
        """运行所有端到端测试"""
        self.logger.info("🚀 开始执行端到端测试...")
        self.start_time = datetime.now()
        
        try:
            # 准备测试环境
            await self.prepare_test_environment()
            
            # 检查系统状态
            await self.check_system_readiness()
            
            # 执行测试套件
            if self.config.get('parallel') and not self.has_dependencies():
                results = await self.run_tests_parallel()
            else:
                results = await self.run_tests_sequential()
            
            # 生成测试报告
            report = await self.generate_test_report(results)
            
            # 清理测试环境
            await self.cleanup_test_environment()
            
            self.end_time = datetime.now()
            duration = self.end_time - self.start_time
            
            if self.config.get('ci'):
                self.logger.info(f"E2E tests completed in {duration.total_seconds():.1f}s")
            else:
                self.logger.info(f"✅ 端到端测试完成，总耗时: {duration}")
            
            return report
            
        except Exception as e:
            self.logger.error(f"❌ 端到端测试执行失败: {e}")
            raise
        finally:
            # 确保清理临时资源
            if self.temp_dir and self.temp_dir.exists():
                import shutil
                shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def has_dependencies(self) -> bool:
        """检查是否有测试依赖关系"""
        return any(
            suite.get('dependencies', []) 
            for suite in self.test_suites.values()
        )
    
    async def prepare_test_environment(self):
        """准备测试环境"""
        if not self.config.get('ci'):
            self.logger.info("🔧 准备端到端测试环境...")
        
        # 创建临时目录
        self.temp_dir = Path(tempfile.mkdtemp(prefix='e2e_test_'))
        
        # 设置环境变量
        environment = self.config.get('environment', 'dev')
        env_config = self.environment_configs.get(environment, self.environment_configs['dev'])
        
        os.environ.update({
            'TEST_MODE': 'e2e',
            'TEST_ENVIRONMENT': environment,
            'TEST_OUTPUT_DIR': str(self.config['output_dir']),
            'TEST_TEMP_DIR': str(self.temp_dir),
            'DATABASE_URL': env_config['database_url'],
            'API_BASE_URL': env_config['api_base_url'],
            'LOG_LEVEL': env_config['log_level']
        })
        
        # 调整超时时间
        timeout_multiplier = env_config.get('timeout_multiplier', 1.0)
        for suite_name, suite_config in self.test_suites.items():
            suite_config['timeout'] = int(suite_config['timeout'] * timeout_multiplier)
        
        # 检查测试依赖
        await self.check_test_dependencies()
        
        # 初始化测试数据
        await self.initialize_test_data()
        
        if not self.config.get('ci'):
            self.logger.info("✅ 测试环境准备完成")
    
    async def check_test_dependencies(self):
        """检查测试依赖"""
        if not self.config.get('ci'):
            self.logger.info("🔍 检查测试依赖...")
        
        required_modules = [
            'pytest',
            'pytest-asyncio',
            'pytest-timeout',
            'pytest-xdist',
            'pytest-html'
        ]
        
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module.replace('-', '_'))
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            error_msg = f"缺少必要的测试依赖: {missing_modules}"
            self.logger.error(f"❌ {error_msg}")
            raise RuntimeError(f"请安装缺少的依赖: pip install {' '.join(missing_modules)}")
        
        if not self.config.get('ci'):
            self.logger.info("✅ 测试依赖检查通过")
    
    async def initialize_test_data(self):
        """初始化测试数据"""
        if not self.config.get('ci'):
            self.logger.info("📊 初始化测试数据...")
        
        # 创建测试数据文件
        test_data = {
            'users': [
                {'id': 1, 'username': 'test_user_1', 'email': '<EMAIL>'},
                {'id': 2, 'username': 'test_user_2', 'email': '<EMAIL>'}
            ],
            'configurations': {
                'system': {'version': '1.0.0', 'mode': 'test'},
                'features': {'feature_a': True, 'feature_b': False}
            },
            'test_scenarios': {
                'normal_load': {'concurrent_users': 10, 'duration': 60},
                'high_load': {'concurrent_users': 100, 'duration': 120}
            }
        }
        
        test_data_file = self.temp_dir / 'test_data.json'
        with open(test_data_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        
        os.environ['TEST_DATA_FILE'] = str(test_data_file)
        
        if not self.config.get('ci'):
            self.logger.info("✅ 测试数据初始化完成")
    
    async def check_system_readiness(self):
        """检查系统就绪状态"""
        if not self.config.get('ci'):
            self.logger.info("🏥 检查系统就绪状态...")
        
        # 检查系统组件
        system_checks = [
            self._check_database_connection,
            self._check_api_availability,
            self._check_file_system_access,
            self._check_memory_availability
        ]
        
        for check_func in system_checks:
            check_name = check_func.__name__.replace('_check_', '')
            
            try:
                result = await check_func()
                if not result:
                    raise RuntimeError(f"系统检查失败: {check_name}")
            except Exception as e:
                self.logger.error(f"❌ 系统检查失败 ({check_name}): {e}")
                raise
        
        if not self.config.get('ci'):
            self.logger.info("✅ 系统就绪状态检查通过")
    
    async def _check_database_connection(self) -> bool:
        """检查数据库连接"""
        try:
            # 模拟数据库连接检查
            await asyncio.sleep(0.1)
            return True
        except Exception:
            return False
    
    async def _check_api_availability(self) -> bool:
        """检查API可用性"""
        try:
            # 模拟API可用性检查
            await asyncio.sleep(0.1)
            return True
        except Exception:
            return False
    
    async def _check_file_system_access(self) -> bool:
        """检查文件系统访问"""
        try:
            # 检查临时目录写入权限
            test_file = self.temp_dir / 'access_test.txt'
            test_file.write_text('test')
            test_file.unlink()
            return True
        except Exception:
            return False
    
    async def _check_memory_availability(self) -> bool:
        """检查内存可用性"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            # 要求至少有1GB可用内存
            return memory.available > 1024 * 1024 * 1024
        except ImportError:
            # 如果没有psutil，假设内存充足
            return True
        except Exception:
            return False
    
    async def run_tests_sequential(self) -> Dict:
        """顺序执行测试"""
        if not self.config.get('ci'):
            self.logger.info("📋 顺序执行测试套件...")
        
        results = {}
        
        # 根据优先级和依赖关系排序
        sorted_suites = self._sort_suites_by_dependencies()
        
        for suite_name in sorted_suites:
            if self.should_run_suite(suite_name):
                suite_config = self.test_suites[suite_name]
                
                if not self.config.get('ci'):
                    self.logger.info(f"🧪 执行测试套件: {suite_config['description']}")
                
                result = await self.run_single_test_suite(suite_name, suite_config)
                results[suite_name] = result
                
                # 如果关键测试失败且不是CI模式，询问是否继续
                if not result['success'] and suite_config['priority'] <= 2:
                    if self.config.get('ci') or not self.config.get('continue_on_failure', True):
                        self.logger.error(f"关键测试套件失败: {suite_name}")
                        break
        
        return results
    
    def _sort_suites_by_dependencies(self) -> List[str]:
        """根据依赖关系排序测试套件"""
        sorted_suites = []
        remaining_suites = set(self.test_suites.keys())
        
        while remaining_suites:
            # 找到没有未满足依赖的套件
            ready_suites = []
            
            for suite_name in remaining_suites:
                dependencies = self.test_suites[suite_name].get('dependencies', [])
                if all(dep in sorted_suites for dep in dependencies):
                    ready_suites.append(suite_name)
            
            if not ready_suites:
                # 如果没有就绪的套件，可能存在循环依赖
                # 按优先级选择一个
                ready_suites = [min(remaining_suites, 
                                  key=lambda x: self.test_suites[x]['priority'])]
            
            # 按优先级排序就绪的套件
            ready_suites.sort(key=lambda x: self.test_suites[x]['priority'])
            
            for suite_name in ready_suites:
                sorted_suites.append(suite_name)
                remaining_suites.remove(suite_name)
        
        return sorted_suites
    
    async def run_tests_parallel(self) -> Dict:
        """并行执行测试"""
        if not self.config.get('ci'):
            self.logger.info("⚡ 并行执行测试套件...")
        
        # 创建测试任务
        test_tasks = []
        
        for suite_name, suite_config in self.test_suites.items():
            if self.should_run_suite(suite_name):
                task = asyncio.create_task(
                    self.run_single_test_suite(suite_name, suite_config)
                )
                test_tasks.append((suite_name, task))
        
        # 等待所有测试完成
        results = {}
        
        for suite_name, task in test_tasks:
            try:
                result = await task
                results[suite_name] = result
            except Exception as e:
                self.logger.error(f"❌ 测试套件 {suite_name} 执行异常: {e}")
                results[suite_name] = {
                    'success': False,
                    'error': str(e),
                    'duration': 0,
                    'tests_run': 0,
                    'tests_passed': 0,
                    'tests_failed': 1
                }
        
        return results
    
    async def run_single_test_suite(self, suite_name: str, suite_config: Dict) -> Dict:
        """运行单个测试套件"""
        start_time = time.time()
        
        try:
            # 构建pytest命令
            cmd = self.build_pytest_command(suite_name, suite_config)
            
            if not self.config.get('ci'):
                self.logger.debug(f"执行命令: {' '.join(cmd)}")
            
            # 执行测试
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=project_root
            )
            
            # 等待测试完成（带超时）
            timeout = min(suite_config.get('timeout', 600), self.config.get('timeout', 1800))
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                raise TimeoutError(f"测试套件 {suite_name} 执行超时 ({timeout}秒)")
            
            # 解析测试结果
            result = self.parse_test_output(stdout.decode(), stderr.decode())
            result['duration'] = time.time() - start_time
            result['success'] = process.returncode == 0
            
            if result['success']:
                if not self.config.get('ci'):
                    self.logger.info(f"✅ 测试套件 {suite_name} 执行成功")
            else:
                self.logger.error(f"❌ 测试套件 {suite_name} 执行失败")
                
                # 如果启用重试
                retry_count = self.config.get('retry', 0)
                if retry_count > 0:
                    result = await self.retry_failed_test(suite_name, suite_config, result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 测试套件 {suite_name} 执行异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time,
                'tests_run': 0,
                'tests_passed': 0,
                'tests_failed': 1
            }
    
    def build_pytest_command(self, suite_name: str, suite_config: Dict) -> List[str]:
        """构建pytest命令"""
        cmd = ['python', '-m', 'pytest']
        
        # 添加测试路径
        cmd.append(suite_config['path'])
        
        # 基本选项
        if self.config.get('ci'):
            cmd.extend(['-v', '--tb=short'])
        else:
            cmd.extend(['-v', '--tb=long'])
        
        # 超时设置
        timeout = suite_config.get('timeout', 600)
        cmd.extend(['--timeout', str(timeout)])
        
        # 输出格式
        output_dir = self.config['output_dir']
        
        # JUnit XML报告
        cmd.extend([
            '--junit-xml',
            str(output_dir / f'junit_{suite_name}.xml')
        ])
        
        # HTML报告
        if not self.config.get('ci'):
            cmd.extend([
                '--html',
                str(output_dir / f'report_{suite_name}.html'),
                '--self-contained-html'
            ])
        
        # 并行执行（如果支持）
        if self.config.get('parallel') and suite_name in ['data_integrity', 'security_measures']:
            cmd.extend(['-n', 'auto'])
        
        return cmd
    
    def parse_test_output(self, stdout: str, stderr: str) -> Dict:
        """解析测试输出"""
        result = {
            'tests_run': 0,
            'tests_passed': 0,
            'tests_failed': 0,
            'tests_skipped': 0,
            'output': stdout,
            'errors': stderr
        }
        
        # 解析pytest输出
        lines = stdout.split('\n')
        
        for line in lines:
            # 查找测试结果摘要行
            if ' passed' in line or ' failed' in line or ' skipped' in line:
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == 'passed' and i > 0:
                        try:
                            result['tests_passed'] = int(parts[i-1])
                        except (ValueError, IndexError):
                            pass
                    elif part == 'failed' and i > 0:
                        try:
                            result['tests_failed'] = int(parts[i-1])
                        except (ValueError, IndexError):
                            pass
                    elif part == 'skipped' and i > 0:
                        try:
                            result['tests_skipped'] = int(parts[i-1])
                        except (ValueError, IndexError):
                            pass
        
        result['tests_run'] = (
            result['tests_passed'] + 
            result['tests_failed'] + 
            result['tests_skipped']
        )
        
        return result
    
    async def retry_failed_test(self, suite_name: str, suite_config: Dict, 
                               original_result: Dict) -> Dict:
        """重试失败的测试"""
        retry_count = self.config.get('retry', 0)
        
        for attempt in range(1, retry_count + 1):
            if not self.config.get('ci'):
                self.logger.info(f"🔄 重试测试套件 {suite_name} (第 {attempt}/{retry_count} 次)")
            
            # 等待一段时间再重试
            await asyncio.sleep(min(2 ** attempt, 10))  # 指数退避，最大10秒
            
            result = await self.run_single_test_suite(suite_name, suite_config)
            
            if result['success']:
                if not self.config.get('ci'):
                    self.logger.info(f"✅ 测试套件 {suite_name} 重试成功")
                return result
        
        self.logger.error(f"❌ 测试套件 {suite_name} 重试 {retry_count} 次后仍然失败")
        return original_result
    
    def should_run_suite(self, suite_name: str) -> bool:
        """判断是否应该运行测试套件"""
        suite_filter = self.config.get('suite')
        
        if suite_filter:
            return suite_filter.lower() in suite_name.lower()
        
        return True
    
    async def generate_test_report(self, results: Dict) -> Dict:
        """生成测试报告"""
        if not self.config.get('ci'):
            self.logger.info("📊 生成测试报告...")
        
        # 计算总体统计
        total_tests = sum(r.get('tests_run', 0) for r in results.values())
        total_passed = sum(r.get('tests_passed', 0) for r in results.values())
        total_failed = sum(r.get('tests_failed', 0) for r in results.values())
        total_skipped = sum(r.get('tests_skipped', 0) for r in results.values())
        total_duration = sum(r.get('duration', 0) for r in results.values())
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'environment': self.config.get('environment', 'dev'),
            'duration': total_duration,
            'summary': {
                'total_suites': len(results),
                'successful_suites': sum(1 for r in results.values() if r.get('success')),
                'failed_suites': sum(1 for r in results.values() if not r.get('success')),
                'total_tests': total_tests,
                'tests_passed': total_passed,
                'tests_failed': total_failed,
                'tests_skipped': total_skipped,
                'success_rate': round(success_rate, 2)
            },
            'suites': results,
            'config': {k: str(v) for k, v in self.config.items()}  # 序列化配置
        }
        
        # 保存JSON报告
        report_file = self.config['output_dir'] / 'e2e_test_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        # 生成摘要报告（适合CI）
        if self.config.get('ci'):
            await self.generate_ci_summary(report)
        else:
            await self.generate_detailed_report(report)
        
        # 打印摘要
        self.print_test_summary(report)
        
        return report
    
    async def generate_ci_summary(self, report: Dict):
        """生成CI摘要报告"""
        summary_file = self.config['output_dir'] / 'e2e_summary.txt'
        
        summary = report['summary']
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(f"E2E Test Summary\n")
            f.write(f"================\n")
            f.write(f"Environment: {report['environment']}\n")
            f.write(f"Duration: {report['duration']:.1f}s\n")
            f.write(f"Success Rate: {summary['success_rate']}%\n")
            f.write(f"Tests: {summary['tests_passed']}/{summary['total_tests']} passed\n")
            f.write(f"Suites: {summary['successful_suites']}/{summary['total_suites']} passed\n")
            
            if summary['tests_failed'] > 0:
                f.write(f"Failed Tests: {summary['tests_failed']}\n")
                
                # 列出失败的套件
                failed_suites = [
                    name for name, result in report['suites'].items()
                    if not result.get('success')
                ]
                
                if failed_suites:
                    f.write(f"Failed Suites: {', '.join(failed_suites)}\n")
    
    async def generate_detailed_report(self, report: Dict):
        """生成详细报告"""
        # 这里可以生成更详细的HTML报告
        # 为简化，我们只生成基本的文本报告
        detailed_file = self.config['output_dir'] / 'e2e_detailed_report.txt'
        
        with open(detailed_file, 'w', encoding='utf-8') as f:
            f.write(f"端到端测试详细报告\n")
            f.write(f"==================\n\n")
            f.write(f"测试时间: {report['timestamp']}\n")
            f.write(f"测试环境: {report['environment']}\n")
            f.write(f"总耗时: {report['duration']:.2f} 秒\n\n")
            
            f.write(f"测试套件详情:\n")
            f.write(f"-" * 50 + "\n")
            
            for suite_name, result in report['suites'].items():
                status = "✅ 成功" if result.get('success') else "❌ 失败"
                f.write(f"{status} {suite_name}\n")
                f.write(f"  耗时: {result.get('duration', 0):.2f} 秒\n")
                f.write(f"  测试用例: {result.get('tests_run', 0)} 个\n")
                f.write(f"  通过: {result.get('tests_passed', 0)} 个\n")
                f.write(f"  失败: {result.get('tests_failed', 0)} 个\n")
                f.write(f"  跳过: {result.get('tests_skipped', 0)} 个\n")
                
                if result.get('error'):
                    f.write(f"  错误: {result['error']}\n")
                
                f.write("\n")
    
    def print_test_summary(self, report: Dict):
        """打印测试摘要"""
        summary = report['summary']
        
        if self.config.get('ci'):
            # CI模式：简化输出
            if summary['tests_failed'] > 0:
                logger.info(f"FAILED: {summary['tests_failed']} tests failed")
                sys.exit(1)
            else:
                logger.info(f"PASSED: {summary['tests_passed']}/{summary['total_tests']} tests passed")
        else:
            # 详细输出
            logger.info("\n" + "="*60)
            logger.info("🧪 端到端测试摘要")
            logger.info("="*60)
            logger.info(f"📊 总体统计:")
            logger.info(f"   测试环境: {report['environment']}")
            logger.info(f"   测试套件: {summary['successful_suites']}/{summary['total_suites']} 成功")
            logger.info(f"   测试用例: {summary['tests_passed']}/{summary['total_tests']} 通过")
            logger.info(f"   成功率: {summary['success_rate']}%")
            logger.info(f"   总耗时: {report['duration']:.2f} 秒")
            
            if summary['tests_failed'] > 0:
                logger.info(f"❌ 失败用例: {summary['tests_failed']} 个")
            
            if summary['tests_skipped'] > 0:
                logger.info(f"⏭️  跳过用例: {summary['tests_skipped']} 个")
            
            logger.info("\n📋 套件详情:")
            for suite_name, result in report['suites'].items():
                status = "✅" if result.get('success') else "❌"
                logger.info(f"   {status} {suite_name}: {result.get('tests_passed', 0)}/{result.get('tests_run', 0)} 通过")
            
            logger.info("="*60)
    
    async def cleanup_test_environment(self):
        """清理测试环境"""
        if not self.config.get('ci'):
            self.logger.info("🧹 清理测试环境...")
        
        # 清理环境变量
        env_vars_to_clean = [
            'TEST_MODE', 'TEST_ENVIRONMENT', 'TEST_OUTPUT_DIR', 
            'TEST_TEMP_DIR', 'DATABASE_URL', 'API_BASE_URL', 
            'LOG_LEVEL', 'TEST_DATA_FILE'
        ]
        
        for env_var in env_vars_to_clean:
            os.environ.pop(env_var, None)
        
        if not self.config.get('ci'):
            self.logger.info("✅ 测试环境清理完成")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='端到端测试持续集成脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--environment', '-e',
        choices=['dev', 'staging', 'prod'],
        default='dev',
        help='测试环境 (默认: dev)'
    )
    
    parser.add_argument(
        '--suite', '-s',
        choices=['user_workflows', 'system_reliability', 'data_integrity', 'security_measures', 'comprehensive_system'],
        help='指定测试套件'
    )
    
    parser.add_argument(
        '--parallel', '-p',
        action='store_true',
        help='并行执行测试'
    )
    
    parser.add_argument(
        '--retry', '-r',
        type=int,
        default=2,
        help='失败重试次数 (默认: 2)'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=Path,
        default=Path('reports/e2e'),
        help='输出目录 (默认: reports/e2e)'
    )
    
    parser.add_argument(
        '--ci',
        action='store_true',
        help='CI模式（简化输出）'
    )
    
    parser.add_argument(
        '--timeout',
        type=int,
        default=1800,
        help='测试超时时间（秒，默认: 1800）'
    )
    
    parser.add_argument(
        '--continue-on-failure',
        action='store_true',
        help='关键测试失败后继续执行'
    )
    
    return parser.parse_args()


async def main():
    """主函数"""
    args = parse_arguments()
    
    # 配置
    config = {
        'environment': args.environment,
        'suite': args.suite,
        'parallel': args.parallel,
        'retry': args.retry,
        'output_dir': args.output,
        'ci': args.ci,
        'timeout': args.timeout,
        'continue_on_failure': args.continue_on_failure
    }
    
    # 创建测试运行器
    runner = E2ETestRunner(config)
    
    try:
        # 执行测试
        report = await runner.run_all_tests()
        
        # 根据测试结果设置退出码
        if report['summary']['tests_failed'] > 0:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except KeyboardInterrupt:
        logger.info("\n⚠️ 测试被用户中断")
        sys.exit(130)
    except Exception as e:
        if config.get('ci'):
            logger.info(f"ERROR: {e}")
        else:
            logger.info(f"❌ 测试执行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    # 处理信号
    def signal_handler(signum, frame):
        logger.info("\n⚠️ 收到中断信号，正在清理...")
        sys.exit(130)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    asyncio.run(main())