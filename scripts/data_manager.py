#!/usr/bin/env python3
"""
数据管理工具
用于管理加密货币交易系统的数据生命周期
"""

import os
import json
import shutil
import sqlite3
import logging
from datetime import datetime, timedelta
from pathlib import Path
import gzip

class DataManager:
    def __init__(self, config_path="data/config/settings"):
        self.config_path = Path(config_path)
        self.load_config()
        self.setup_logging()
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path / "database_config.json", "r") as f:
                self.db_config = json.load(f)
            with open(self.config_path / "data_lifecycle.json", "r") as f:
                self.lifecycle_config = json.load(f)
        except FileNotFoundError as e:
            logger.info(f"配置文件未找到: {e}")
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置文件"""
        os.makedirs(self.config_path, exist_ok=True)
        
        # 默认数据库配置
        default_db_config = {
            "databases": {
                "trading_system": {
                    "path": "data/config/trading_system.db",
                    "description": "核心交易系统数据库",
                    "backup_schedule": "daily",
                    "retention_days": 30
                }
            },
            "backup_settings": {
                "daily_backup_time": "02:00",
                "compression": True
            }
        }
        
        with open(self.config_path / "database_config.json", "w") as f:
            json.dump(default_db_config, f, indent=2)
    
    def setup_logging(self):
        """设置日志"""
        log_dir = Path("data/logs/system")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "data_manager.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def backup_database(self, db_name, backup_type="daily"):
        """备份数据库"""
        if db_name not in self.db_config["databases"]:
            self.logger.error(f"数据库 {db_name} 未在配置中找到")
            return False
        
        db_info = self.db_config["databases"][db_name]
        db_path = Path(db_info["path"])
        
        if not db_path.exists():
            self.logger.warning(f"数据库文件不存在: {db_path}")
            return False
        
        # 创建备份目录
        backup_dir = Path(f"data/backups/{backup_type}")
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = backup_dir / f"{db_name}_{timestamp}.db"
        
        try:
            # 复制数据库文件
            shutil.copy2(db_path, backup_file)
            
            # 压缩备份
            if self.db_config["backup_settings"].get("compression", False):
                with open(backup_file, 'rb') as f_in:
                    with gzip.open(f"{backup_file}.gz", 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                backup_file.unlink()  # 删除未压缩文件
                backup_file = Path(f"{backup_file}.gz")
            
            self.logger.info(f"数据库备份成功: {backup_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            return False
    
    def cleanup_old_data(self, category):
        """清理过期数据"""
        if category not in self.lifecycle_config["data_categories"]:
            self.logger.error(f"数据类别 {category} 未在配置中找到")
            return False
        
        config = self.lifecycle_config["data_categories"][category]
        data_path = Path(config["path"])
        
        if not data_path.exists():
            return True
        
        cutoff_time = None
        if "retention_hours" in config:
            cutoff_time = datetime.now() - timedelta(hours=config["retention_hours"])
        elif "retention_days" in config:
            cutoff_time = datetime.now() - timedelta(days=config["retention_days"])
        
        if not cutoff_time:
            return True
        
        cleaned_count = 0
        for file_path in data_path.rglob("*"):
            if file_path.is_file():
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff_time:
                    try:
                        file_path.unlink()
                        cleaned_count += 1
                    except Exception as e:
                        self.logger.warning(f"无法删除文件 {file_path}: {e}")
        
        self.logger.info(f"清理 {category} 数据: 删除 {cleaned_count} 个文件")
        return True
    
    def validate_database(self, db_name):
        """验证数据库完整性"""
        if db_name not in self.db_config["databases"]:
            return False
        
        db_path = Path(self.db_config["databases"][db_name]["path"])
        
        if not db_path.exists():
            return False
        
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            cursor.execute("PRAGMA integrity_check;")
            result = cursor.fetchone()
            conn.close()
            
            is_valid = result and result[0] == "ok"
            if is_valid:
                self.logger.info(f"数据库 {db_name} 完整性检查通过")
            else:
                self.logger.error(f"数据库 {db_name} 完整性检查失败")
            
            return is_valid
            
        except Exception as e:
            self.logger.error(f"数据库验证失败: {e}")
            return False
    
    def get_storage_stats(self):
        """获取存储统计信息"""
        stats = {}
        
        for category, config in self.lifecycle_config["data_categories"].items():
            path = Path(config["path"])
            if path.exists():
                total_size = sum(f.stat().st_size for f in path.rglob("*") if f.is_file())
                file_count = len(list(path.rglob("*")))
                stats[category] = {
                    "path": str(path),
                    "size_bytes": total_size,
                    "size_mb": round(total_size / (1024 * 1024), 2),
                    "file_count": file_count
                }
        
        return stats
    
    def create_directory_structure(self):
        """创建完整的目录结构"""
        directories = [
            "data/market/realtime",
            "data/market/historical", 
            "data/market/derived",
            "data/portfolio/holdings",
            "data/portfolio/performance",
            "data/portfolio/transactions",
            "data/analysis/indicators",
            "data/analysis/signals",
            "data/analysis/backtests",
            "data/config/settings",
            "data/config/api_keys",
            "data/logs/system",
            "data/logs/trading",
            "data/logs/errors",
            "data/backups/daily",
            "data/backups/weekly",
            "data/backups/monthly",
            "data/cache/market",
            "data/cache/analysis",
            "data/cache/temp"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            self.logger.info(f"创建目录: {directory}")
    
    def run_maintenance(self):
        """运行完整的数据维护"""
        self.logger.info("开始数据维护任务")
        
        # 创建目录结构
        self.create_directory_structure()
        
        # 备份数据库
        for db_name in self.db_config["databases"]:
            self.backup_database(db_name)
        
        # 清理过期数据
        for category in self.lifecycle_config["data_categories"]:
            self.cleanup_old_data(category)
        
        # 验证数据库
        for db_name in self.db_config["databases"]:
            self.validate_database(db_name)
        
        # 显示存储统计
        stats = self.get_storage_stats()
        self.logger.info("存储统计:")
        for category, info in stats.items():
            self.logger.info(f"  {category}: {info['size_mb']}MB, {info['file_count']}个文件")
        
        self.logger.info("数据维护任务完成")

def main():
    """主函数"""
    manager = DataManager()
    
    import argparse
    parser = argparse.ArgumentParser(description="数据管理工具")
    parser.add_argument("--backup", help="备份指定数据库")
    parser.add_argument("--cleanup", help="清理指定类别数据")
    parser.add_argument("--validate", help="验证指定数据库")
    parser.add_argument("--stats", action="store_true", help="显示存储统计")
    parser.add_argument("--maintenance", action="store_true", help="运行完整维护")
    parser.add_argument("--init", action="store_true", help="初始化目录结构")
    
    args = parser.parse_args()
    
    if args.backup:
        manager.backup_database(args.backup)
    elif args.cleanup:
        manager.cleanup_old_data(args.cleanup)
    elif args.validate:
        manager.validate_database(args.validate)
    elif args.stats:
        stats = manager.get_storage_stats()
        logger.info(json.dumps(stats, indent=2))
    elif args.maintenance:
        manager.run_maintenance()
    elif args.init:
        manager.create_directory_structure()
    else:
        manager.run_maintenance()

if __name__ == "__main__":
    main()