import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
全面导入路径修复脚本
修复项目中所有的导入路径问题
"""

import os
import re
import sys
from pathlib import Path

def get_comprehensive_fixes():
    """获取所有需要修复的导入路径映射"""
    return [
        # 1. 修复重复路径
        (r'from src\.market\.data\.data\.', 'from src.market.data.'),
        (r'from src\.market\.strategies\.strategies\.', 'from src.market.strategies.'),
        
        # 2. 修复strategies路径
        (r'from src\.strategies\.', 'from src.market.strategies.'),
        (r'from strategies\.', 'from src.market.strategies.'),
        
        # 3. 修复data路径  
        (r'from src\.data\.adapters\.', 'from src.market.data.adapters.'),
        (r'from src\.data\.models\.', 'from src.market.data.models.'),
        (r'from src\.data\.', 'from src.market.data.'),
        
        # 4. 修复models路径
        (r'from src\.models\.market_data', 'from src.market.strategies.models.market_data'),
        (r'from src\.models\.portfolio', 'from src.market.strategies.models.portfolio'),  
        (r'from src\.models\.trading', 'from src.market.strategies.models.trading'),
        (r'from src\.models\.base', 'from src.market.strategies.models.base'),
        (r'from src\.models\.portfolio_manager', 'from src.domain.models.portfolio_manager'),
        
        # 5. 修复exceptions路径
        (r'from src\.exceptions', 'from src.common.exceptions'),
        
        # 6. 修复monitoring路径
        (r'from src\.monitoring\.api_fixer', 'from src.core.monitoring.api_fixer'),
        (r'from src\.monitoring\.backend_fix_manager', 'from src.core.monitoring.backend_fix_manager'),
        (r'from src\.monitoring\.database_fixer', 'from src.core.monitoring.database_fixer'),
        (r'from src\.monitoring\.audit_logger', 'from src.core.monitoring.audit_logger'),
        (r'from src\.monitoring\.health_monitor', 'from src.core.monitoring.health_monitor'),
        (r'from src\.monitoring\.logging_manager', 'from src.core.monitoring.logging_manager'),
        
        # 7. 修复core模块路径
        (r'from src\.core\.models\.', 'from src.domain.models.'),
        
        # 8. 修复trading路径
        (r'from src\.trading\.execution\.models', 'from src.trading.execution.models'),
        (r'from src\.live_trading\.', 'from src.trading.'),
        
        # 9. 修复工具路径
        (r'from tools\.file_organization\.config_loader', 'from tools.config.config_loader'),
    ]

def fix_fallback_imports(content: str) -> str:
    """修复Python文件中的fallback导入语句"""
    patterns = [
        # 修复相对导入的fallback
        (r'from strategies\.base import', 'from src.market.strategies.base import'),
        (r'from strategies\.multi_market import', 'from src.market.strategies.multi_market import'),
        (r'from strategies\.economic_base import', 'from src.market.strategies.economic_base import'),
        (r'from models\.market_data import', 'from src.market.strategies.models.market_data import'),
        (r'from models\.trading import', 'from src.market.strategies.models.trading import'),
        (r'from models\.portfolio import', 'from src.market.strategies.models.portfolio import'),
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    return content

def fix_model_imports(content: str) -> str:
    """修复模型相关的导入"""
    # 对于..models.market_data形式的相对导入，需要分情况处理
    if 'src/market/strategies/' in content or 'strategies/' in content:
        # 在strategies目录下，使用strategies的models
        content = re.sub(r'from \.\.models\.market_data import', 'from .models.market_data import', content)
        content = re.sub(r'from \.\.models\.trading import', 'from .models.trading import', content)
        content = re.sub(r'from \.\.models\.portfolio import', 'from .models.portfolio import', content)
    
    return content

def process_file(file_path: Path) -> bool:
    """处理单个文件的导入修复"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        original_content = content
        
        # 应用基础修复
        fixes = get_comprehensive_fixes()
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
        
        # 应用特殊修复
        content = fix_fallback_imports(content)
        content = fix_model_imports(content)
        
        # 如果内容发生变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
            
    except Exception as e:
        logger.info(f"❌ 处理文件 {file_path} 时出错: {e}")
        return False
    
    return False

def main():
    """主函数"""
    logger.info("🔧 开始全面修复导入路径...")
    
    project_root = Path(__file__).parent.parent.parent
    
    # 需要处理的目录
    target_dirs = [
        project_root / "tests",
        project_root / "src",
    ]
    
    fixed_files = []
    error_files = []
    
    for target_dir in target_dirs:
        if not target_dir.exists():
            logger.info(f"⚠️ 目录不存在: {target_dir}")
            continue
            
        logger.info(f"🔍 处理目录: {target_dir}")
        
        # 遍历所有Python文件
        for py_file in target_dir.rglob("*.py"):
            if py_file.name == "__pycache__":
                continue
                
            try:
                if process_file(py_file):
                    fixed_files.append(py_file)
                    logger.info(f"  ✅ 修复: {py_file.relative_to(project_root)}")
            except Exception as e:
                error_files.append((py_file, str(e)))
                logger.info(f"  ❌ 错误: {py_file.relative_to(project_root)} - {e}")
    
    logger.info(f"\\n📊 修复结果:")
    logger.info(f"✅ 成功修复 {len(fixed_files)} 个文件")
    logger.info(f"❌ 修复失败 {len(error_files)} 个文件")
    
    if error_files:
        logger.info("\\n❌ 失败的文件:")
        for file_path, error in error_files[:10]:  # 只显示前10个
            logger.info(f"  - {file_path.relative_to(project_root)}: {error}")
        if len(error_files) > 10:
            logger.info(f"  ... 以及其他 {len(error_files) - 10} 个文件")
    
    return 0 if not error_files else 1

if __name__ == "__main__":
    sys.exit(main())
