#!/usr/bin/env python3
"""
统一测试执行器
整合分散的测试脚本，使用pytest markers统一管理测试
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import List, Dict, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

class UnifiedTestRunner:
    """统一测试运行器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.test_types = {
            'unit': {
                'description': '单元测试 - 测试单个模块和函数',
                'markers': ['unit'],
                'paths': ['tests/unit/'],
                'timeout': 300,
                'parallel': True
            },
            'integration': {
                'description': '集成测试 - 测试模块间协作',
                'markers': ['integration'],
                'paths': ['tests/integration/'],
                'timeout': 600,
                'parallel': True
            },
            'e2e': {
                'description': '端到端测试 - 测试完整业务流程',
                'markers': ['e2e'],
                'paths': ['tests/end_to_end/'],
                'timeout': 1800,
                'parallel': False
            },
            'performance': {
                'description': '性能测试 - 测试系统性能指标',
                'markers': ['performance', 'slow'],
                'paths': ['tests/performance/'],
                'timeout': 3600,
                'parallel': False
            },
            'regression': {
                'description': '回归测试 - 防止功能退化',
                'markers': ['regression'],
                'paths': ['tests/regression/'],
                'timeout': 900,
                'parallel': True
            },
            'smoke': {
                'description': '冒烟测试 - 基本功能验证',
                'markers': ['smoke'],
                'paths': ['tests/'],
                'timeout': 180,
                'parallel': True
            }
        }
    
    def setup_pytest_markers(self) -> bool:
        """设置pytest markers配置"""
        
        pytest_ini_content = """[tool:pytest]
minversion = 7.0
addopts = -ra -q --strict-markers --strict-config --import-mode=importlib
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

markers =
    # 测试类型标记
    unit: 单元测试 - 测试单个模块和函数的功能
    integration: 集成测试 - 测试模块间的协作和接口
    e2e: 端到端测试 - 测试完整的业务流程和用户场景
    performance: 性能测试 - 测试系统性能指标和负载能力
    regression: 回归测试 - 防止已修复的问题再次出现
    smoke: 冒烟测试 - 快速验证基本功能是否正常
    
    # 执行特性标记
    slow: 耗时较长的测试（通常>10秒）
    fast: 快速测试（通常<1秒）
    db: 需要数据库的测试
    network: 需要网络连接的测试
    api: API接口测试
    ui: 用户界面测试
    
    # 环境标记
    dev: 开发环境测试
    staging: 测试环境测试
    prod: 生产环境测试
    
    # 组件标记
    backend: 后端相关测试
    frontend: 前端相关测试
    data: 数据处理相关测试
    strategy: 策略相关测试
    risk: 风险管理相关测试
    
    # 特殊标记
    skip_ci: CI环境中跳过的测试
    manual: 需要手动执行的测试
    experimental: 实验性功能测试

filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::FutureWarning

[tool:coverage:run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py",
    "*/venv/*",
    "*/backup/*"
]

[tool:coverage:report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
]
"""
        
        pytest_ini_path = self.project_root / "pytest.ini"
        with open(pytest_ini_path, 'w', encoding='utf-8') as f:
            f.write(pytest_ini_content)
        
        logger.info(f"✅ 创建pytest配置: {pytest_ini_path}")
        return True
    
    def run_tests(self, test_type: str, options: Dict) -> int:
        """运行指定类型的测试"""
        
        if test_type not in self.test_types:
            logger.error(f"未知的测试类型: {test_type}")
            return 1
        
        test_config = self.test_types[test_type]
        logger.info(f"🧪 运行{test_config['description']}")
        
        # 构建pytest命令
        cmd = [sys.executable, "-m", "pytest"]
        
        # 添加路径
        for path in test_config['paths']:
            test_path = self.project_root / path
            if test_path.exists():
                cmd.append(str(test_path))
        
        # 添加标记过滤
        if options.get('markers_only'):
            markers = test_config['markers']
            if len(markers) == 1:
                cmd.extend(["-m", markers[0]])
            else:
                cmd.extend(["-m", " or ".join(markers)])
        
        # 添加详细选项
        if options.get('verbose'):
            cmd.append("-v")
        else:
            cmd.append("-q")
        
        # 添加并行执行
        if test_config['parallel'] and options.get('parallel') and not options.get('debug'):
            try:
                import pytest_xdist
                cmd.extend(["-n", "auto"])
            except ImportError:
                logger.warning("pytest-xdist未安装，跳过并行执行")
        
        # 添加覆盖率报告
        if options.get('coverage') and test_type in ['unit', 'integration']:
            cmd.extend([
                "--cov=src",
                "--cov-report=term-missing",
                "--cov-report=html:reports/coverage"
            ])
        
        # 添加输出格式
        if options.get('xml_output'):
            cmd.extend(["--junitxml=reports/junit.xml"])
        
        # 添加最大失败数
        if options.get('maxfail'):
            cmd.extend(["--maxfail", str(options['maxfail'])])
        
        # 添加超时（如果pytest-timeout可用）
        if not options.get('debug'):
            try:
                import pytest_timeout
                cmd.extend(["--timeout", str(test_config['timeout'])])
            except ImportError:
                logger.warning("pytest-timeout未安装，跳过超时设置")
        
        # 执行测试
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        start_time = time.time()
        result = subprocess.run(cmd, cwd=self.project_root)
        end_time = time.time()
        
        duration = end_time - start_time
        if result.returncode == 0:
            logger.info(f"✅ {test_config['description']}通过 (耗时: {duration:.1f}秒)")
        else:
            logger.error(f"❌ {test_config['description']}失败 (耗时: {duration:.1f}秒)")
        
        return result.returncode
    
    def run_all_tests(self, options: Dict) -> int:
        """运行所有类型的测试"""
        
        logger.info("🚀 运行所有测试套件")
        
        # 测试执行顺序（从快到慢）
        test_order = ['smoke', 'unit', 'integration', 'regression', 'e2e']
        
        if options.get('include_performance'):
            test_order.append('performance')
        
        total_failures = 0
        
        for test_type in test_order:
            logger.info(f"\\n{'='*50}")
            result = self.run_tests(test_type, options)
            
            if result != 0:
                total_failures += 1
                if options.get('fail_fast'):
                    logger.error(f"测试失败，停止执行（fail-fast模式）")
                    break
        
        logger.info(f"\\n🎯 测试总结:")
        logger.info(f"  - 执行的测试类型: {len(test_order)}")
        logger.info(f"  - 失败的测试类型: {total_failures}")
        
        return total_failures
    
    def list_tests(self, test_type: Optional[str] = None) -> None:
        """列出可用的测试"""
        
        logger.info("📋 可用的测试类型:")
        
        if test_type:
            if test_type in self.test_types:
                config = self.test_types[test_type]
                logger.info(f"\\n{test_type}:")
                logger.info(f"  描述: {config['description']}")
                logger.info(f"  标记: {', '.join(config['markers'])}")
                logger.info(f"  路径: {', '.join(config['paths'])}")
                logger.info(f"  超时: {config['timeout']}秒")
                logger.info(f"  并行: {'是' if config['parallel'] else '否'}")
            else:
                logger.error(f"未知的测试类型: {test_type}")
        else:
            for name, config in self.test_types.items():
                logger.info(f"\\n{name}:")
                logger.info(f"  {config['description']}")
                logger.info(f"  标记: {', '.join(config['markers'])}")
    
    def generate_test_summary(self) -> str:
        """生成测试体系总结"""
        
        summary = """# 统一测试执行体系

## 🎯 测试统一化目标

将分散的测试脚本整合为统一的pytest-based测试体系，使用markers进行分类管理。

## 📊 测试类型分类

| 测试类型 | 标记 | 描述 | 执行时间 | 并行执行 |
|---------|------|------|---------|----------|
| 单元测试 | `unit` | 测试单个模块和函数 | ~5分钟 | ✅ |
| 集成测试 | `integration` | 测试模块间协作 | ~10分钟 | ✅ |
| 端到端测试 | `e2e` | 测试完整业务流程 | ~30分钟 | ❌ |
| 性能测试 | `performance` | 测试系统性能指标 | ~60分钟 | ❌ |
| 回归测试 | `regression` | 防止功能退化 | ~15分钟 | ✅ |
| 冒烟测试 | `smoke` | 快速基本功能验证 | ~3分钟 | ✅ |

## 🛠️ 使用方法

### 基本用法

```bash
# 运行所有测试
python scripts/utilities/unified_test_runner.py --all

# 运行特定类型测试
python scripts/utilities/unified_test_runner.py --type unit
python scripts/utilities/unified_test_runner.py --type integration

# 快速冒烟测试
python scripts/utilities/unified_test_runner.py --type smoke

# 带覆盖率的单元测试
python scripts/utilities/unified_test_runner.py --type unit --coverage
```

### 高级选项

```bash
# 并行执行
python scripts/utilities/unified_test_runner.py --type unit --parallel

# 详细输出
python scripts/utilities/unified_test_runner.py --type unit --verbose

# 生成XML报告
python scripts/utilities/unified_test_runner.py --type unit --xml-output

# 调试模式（禁用超时和并行）
python scripts/utilities/unified_test_runner.py --type unit --debug
```

### Pytest直接使用

```bash
# 使用标记运行测试
pytest -m unit                    # 只运行单元测试
pytest -m "integration or e2e"    # 运行集成和端到端测试
pytest -m "not slow"              # 跳过慢测试
pytest -m "smoke and not network" # 冒烟测试，跳过网络测试

# 组合条件
pytest -m "unit and not db"       # 单元测试，跳过数据库测试
pytest -m "backend and not ui"    # 后端测试，跳过UI测试
```

## 📁 测试目录结构

```
tests/
├── unit/                  # 单元测试
├── integration/           # 集成测试  
├── end_to_end/           # 端到端测试
├── performance/          # 性能测试
├── regression/           # 回归测试
└── fixtures/             # 测试数据和工具
```

## 🔧 配置文件

- `pytest.ini` - pytest配置和标记定义
- `pyproject.toml` - 项目配置和依赖
- `conftest.py` - 共享fixture和配置

## 📊 持续集成

### GitHub Actions示例
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run unit tests
        run: python scripts/utilities/unified_test_runner.py --type unit --xml-output
      - name: Run integration tests  
        run: python scripts/utilities/unified_test_runner.py --type integration
```

## 🎯 迁移指南

### 旧脚本对应关系
- `scripts/run_e2e_tests.py` → `pytest -m e2e`
- `scripts/run_performance_tests.py` → `pytest -m performance`
- `scripts/run_system_integration_tests.py` → `pytest -m integration`
- `web_ui/backend/run_tests.py` → `pytest -m backend`

### 迁移步骤
1. 为现有测试添加适当的pytest标记
2. 更新CI/CD配置使用新的统一入口
3. 逐步移除旧的测试脚本
4. 更新文档和开发指南

---

*测试统一化完成时间: 2025年1月18日*
"""
        
        return summary

def create_sample_conftest():
    """创建示例conftest.py文件"""
    
    conftest_content = '''"""
全局pytest配置和fixture
"""

import pytest
import asyncio
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

@pytest.fixture(scope="session")
def project_root():
    """项目根目录fixture"""
    return PROJECT_ROOT

@pytest.fixture(scope="session") 
def event_loop():
    """异步测试的事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def sample_market_data():
    """示例市场数据fixture"""
    return {
        "symbol": "AAPL",
        "price": 150.0,
        "volume": 1000000,
        "timestamp": "2024-01-01T00:00:00Z"
    }

@pytest.fixture
def mock_strategy():
    """模拟策略fixture"""
    class MockStrategy:
        def __init__(self):
            self.name = "test_strategy"
            self.parameters = {}
        
        def generate_signal(self, data):
            return {"action": "hold", "confidence": 0.5}
    
    return MockStrategy()

# 标记配置
pytestmark = [
    pytest.mark.filterwarnings("ignore::DeprecationWarning"),
    pytest.mark.filterwarnings("ignore::PendingDeprecationWarning"),
]
'''
    
    return conftest_content

def main():
    """主函数"""
    
    parser = argparse.ArgumentParser(description="统一测试执行器")
    parser.add_argument("--type", "-t", 
                       choices=['unit', 'integration', 'e2e', 'performance', 'regression', 'smoke'],
                       help="测试类型")
    parser.add_argument("--all", "-a", action="store_true", help="运行所有测试")
    parser.add_argument("--list", "-l", action="store_true", help="列出可用测试类型")
    parser.add_argument("--setup", action="store_true", help="设置pytest配置")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--parallel", "-p", action="store_true", help="并行执行")
    parser.add_argument("--coverage", "-c", action="store_true", help="生成覆盖率报告")
    parser.add_argument("--xml-output", action="store_true", help="生成XML报告")
    parser.add_argument("--maxfail", type=int, default=10, help="最大失败数")
    parser.add_argument("--fail-fast", action="store_true", help="遇到失败立即停止")
    parser.add_argument("--debug", action="store_true", help="调试模式")
    parser.add_argument("--include-performance", action="store_true", help="包含性能测试")
    parser.add_argument("--markers-only", action="store_true", help="只使用标记过滤，不使用路径")
    
    args = parser.parse_args()
    
    project_root = Path(__file__).parent.parent.parent
    runner = UnifiedTestRunner(project_root)
    
    # 设置pytest配置
    if args.setup:
        logger.info("🔧 设置pytest配置...")
        runner.setup_pytest_markers()
        
        # 创建示例conftest.py
        conftest_path = project_root / "conftest.py"
        if not conftest_path.exists():
            with open(conftest_path, 'w', encoding='utf-8') as f:
                f.write(create_sample_conftest())
            logger.info(f"✅ 创建conftest.py: {conftest_path}")
        
        # 生成总结文档
        summary = runner.generate_test_summary()
        summary_path = project_root / "UNIFIED_TEST_SYSTEM.md"
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary)
        logger.info(f"✅ 生成测试体系文档: {summary_path}")
        
        logger.info("🎉 pytest配置设置完成!")
        return 0
    
    # 列出测试类型
    if args.list:
        runner.list_tests(args.type)
        return 0
    
    # 准备选项
    options = {
        'verbose': args.verbose,
        'parallel': args.parallel,
        'coverage': args.coverage,
        'xml_output': args.xml_output,
        'maxfail': args.maxfail,
        'fail_fast': args.fail_fast,
        'debug': args.debug,
        'include_performance': args.include_performance,
        'markers_only': args.markers_only
    }
    
    # 运行测试
    if args.all:
        return runner.run_all_tests(options)
    elif args.type:
        return runner.run_tests(args.type, options)
    else:
        parser.print_help()
        return 1

if __name__ == "__main__":
    sys.exit(main())
