#!/usr/bin/env python3
"""
项目统计生成器
生成准确的项目文件统计，排除开发依赖
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple
import subprocess

class ProjectStatsGenerator:
    """项目统计生成器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        
        # 需要排除的目录（开发依赖）
        self.exclude_dirs = {
            'venv', '__pycache__', '.git', 'node_modules',
            '.pytest_cache', '.mypy_cache', '.coverage',
            'build', 'dist', '*.egg-info', 'backup'
        }
        
        # 文件类型分类
        self.file_categories = {
            'python_source': ['.py'],
            'python_test': ['test_*.py', '*_test.py'],
            'frontend_source': ['.ts', '.tsx', '.js', '.jsx'],
            'frontend_style': ['.css', '.scss', '.less'],
            'frontend_config': ['.json', '.yaml', '.yml'],
            'documentation': ['.md', '.rst', '.txt'],
            'config_files': ['.toml', '.ini', '.cfg', '.conf'],
            'data_files': ['.csv', '.json', '.xml', '.sql'],
            'image_files': ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico'],
            'script_files': ['.sh', '.bat', '.ps1'],
        }
    
    def should_exclude_path(self, path: Path) -> bool:
        """判断路径是否应该被排除"""
        path_str = str(path.relative_to(self.project_root))
        
        # 检查是否包含排除的目录
        for exclude_dir in self.exclude_dirs:
            if exclude_dir.replace('*', '') in path_str:
                return True
        
        return False
    
    def categorize_file(self, file_path: Path) -> str:
        """对文件进行分类"""
        
        file_name = file_path.name
        file_ext = file_path.suffix.lower()
        
        # 特殊处理测试文件
        if file_name.startswith('test_') or file_name.endswith('_test.py'):
            return 'python_test'
        
        # 按扩展名分类
        for category, extensions in self.file_categories.items():
            if category == 'python_test':  # 已经处理过
                continue
            for ext in extensions:
                if ext.startswith('*.') or ext.startswith('*'):
                    # 通配符模式
                    pattern = ext.replace('*', '')
                    if file_name.endswith(pattern) or file_ext == pattern:
                        return category
                elif file_ext == ext:
                    return category
        
        return 'other'
    
    def get_directory_stats(self, directory: Path) -> Dict:
        """获取目录统计信息"""
        
        stats = {
            'total_files': 0,
            'total_size': 0,
            'categories': {}
        }
        
        if not directory.exists():
            return stats
        
        for file_path in directory.rglob('*'):
            if file_path.is_file() and not self.should_exclude_path(file_path):
                try:
                    file_size = file_path.stat().st_size
                    category = self.categorize_file(file_path)
                    
                    stats['total_files'] += 1
                    stats['total_size'] += file_size
                    
                    if category not in stats['categories']:
                        stats['categories'][category] = {'count': 0, 'size': 0}
                    
                    stats['categories'][category]['count'] += 1
                    stats['categories'][category]['size'] += file_size
                    
                except (OSError, PermissionError):
                    # 跳过无法访问的文件
                    pass
        
        return stats
    
    def format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f}{unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f}TB"
    
    def generate_project_stats(self) -> Dict:
        """生成完整的项目统计"""
        
        print("📊 生成项目统计信息...")
        
        # 核心目录统计
        core_dirs = {
            'src': self.project_root / 'src',
            'tests': self.project_root / 'tests',
            'web_ui_backend': self.project_root / 'web_ui' / 'backend',
            'web_ui_frontend': self.project_root / 'web_ui' / 'frontend',
            'docs': self.project_root / 'docs',
            'config': self.project_root / 'config',
            'scripts': self.project_root / 'scripts',
            'tools': self.project_root / 'tools',
        }
        
        stats = {}
        total_stats = {
            'total_files': 0,
            'total_size': 0,
            'categories': {}
        }
        
        for dir_name, dir_path in core_dirs.items():
            dir_stats = self.get_directory_stats(dir_path)
            stats[dir_name] = dir_stats
            
            total_stats['total_files'] += dir_stats['total_files']
            total_stats['total_size'] += dir_stats['total_size']
            
            for category, cat_stats in dir_stats['categories'].items():
                if category not in total_stats['categories']:
                    total_stats['categories'][category] = {'count': 0, 'size': 0}
                total_stats['categories'][category]['count'] += cat_stats['count']
                total_stats['categories'][category]['size'] += cat_stats['size']
        
        stats['total'] = total_stats
        
        return stats
    
    def generate_readme_stats_section(self, stats: Dict) -> str:
        """生成README中的统计部分"""
        
        total = stats['total']
        categories = total['categories']
        
        # 计算主要文件类型
        python_files = categories.get('python_source', {}).get('count', 0)
        test_files = categories.get('python_test', {}).get('count', 0)
        frontend_files = (
            categories.get('frontend_source', {}).get('count', 0) +
            categories.get('frontend_style', {}).get('count', 0) +
            categories.get('frontend_config', {}).get('count', 0)
        )
        doc_files = categories.get('documentation', {}).get('count', 0)
        
        total_size_formatted = self.format_size(total['total_size'])
        
        stats_section = f"""## 📊 项目统计 (最新)

- **总文件数**: {total['total_files']:,}个文件 (排除开发依赖)
- **Python源码**: {python_files}个文件
- **测试文件**: {test_files}个测试文件
- **前端文件**: {frontend_files}个TypeScript/React文件  
- **文档文件**: {doc_files}个Markdown文档
- **项目大小**: {total_size_formatted} (纯源码，不含依赖)
- **Python版本**: 3.9-3.13 (推荐3.13)"""
        
        return stats_section
    
    def generate_detailed_report(self, stats: Dict) -> str:
        """生成详细的统计报告"""
        
        report_lines = [
            "# 项目文件统计报告",
            "",
            f"**生成时间**: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**项目路径**: {self.project_root}",
            "",
            "## 📊 总体统计",
            ""
        ]
        
        total = stats['total']
        total_size_formatted = self.format_size(total['total_size'])
        
        report_lines.extend([
            f"- **总文件数**: {total['total_files']:,}",
            f"- **总大小**: {total_size_formatted}",
            "",
            "## 📁 按目录统计",
            "",
            "| 目录 | 文件数 | 大小 | 主要内容 |",
            "|------|--------|------|----------|"
        ])
        
        dir_descriptions = {
            'src': '核心源代码',
            'tests': '测试文件',
            'web_ui_backend': '后端API',
            'web_ui_frontend': '前端界面',
            'docs': '项目文档',
            'config': '配置文件',
            'scripts': '工具脚本',
            'tools': '开发工具'
        }
        
        for dir_name, dir_stats in stats.items():
            if dir_name == 'total':
                continue
            
            count = dir_stats['total_files']
            size = self.format_size(dir_stats['total_size'])
            desc = dir_descriptions.get(dir_name, dir_name)
            
            report_lines.append(f"| {dir_name} | {count} | {size} | {desc} |")
        
        report_lines.extend([
            "",
            "## 📋 按文件类型统计",
            "",
            "| 文件类型 | 数量 | 大小 | 说明 |",
            "|----------|------|------|------|"
        ])
        
        category_descriptions = {
            'python_source': 'Python源码文件',
            'python_test': 'Python测试文件',
            'frontend_source': '前端源码文件',
            'frontend_style': '前端样式文件',
            'frontend_config': '前端配置文件',
            'documentation': '文档文件',
            'config_files': '配置文件',
            'data_files': '数据文件',
            'image_files': '图片文件',
            'script_files': '脚本文件',
            'other': '其他文件'
        }
        
        for category, cat_stats in total['categories'].items():
            count = cat_stats['count']
            size = self.format_size(cat_stats['size'])
            desc = category_descriptions.get(category, category)
            
            report_lines.append(f"| {desc} | {count} | {size} | {category} |")
        
        report_lines.extend([
            "",
            "## 📈 项目规模评估",
            "",
            f"根据统计结果，该项目包含 {total['total_files']:,} 个文件，",
            f"总大小为 {total_size_formatted}（不包含开发依赖）。",
            "",
            "**项目规模**: 中到大型项目",
            "**复杂度**: 高（包含前后端、测试、文档等完整体系）",
            "**维护性**: 良好（有完整的测试和文档体系）",
            "",
            "---",
            "",
            "*注意：统计结果已排除 venv、node_modules、__pycache__ 等开发依赖目录*"
        ])
        
        return "\n".join(report_lines)

def update_readme_stats(project_root: Path, new_stats_section: str) -> bool:
    """更新README.md中的统计部分"""
    
    readme_path = project_root / "README.md"
    
    if not readme_path.exists():
        print(f"❌ README.md文件不存在: {readme_path}")
        return False
    
    try:
        with open(readme_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换统计部分
        import re
        
        # 查找 "## 📊 项目统计" 到下一个 "##" 之间的内容
        pattern = r'(## 📊 项目统计.*?)(?=## |\Z)'
        
        if re.search(pattern, content, re.DOTALL):
            new_content = re.sub(pattern, new_stats_section + '\n\n', content, flags=re.DOTALL)
            
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✅ 更新README.md统计部分: {readme_path}")
            return True
        else:
            print("❌ 未找到项目统计部分，无法更新")
            return False
    
    except Exception as e:
        print(f"❌ 更新README.md时出错: {e}")
        return False

def main():
    """主函数"""
    
    print("📊 项目文件统计生成器")
    print("=" * 50)
    
    project_root = Path(__file__).parent.parent.parent
    generator = ProjectStatsGenerator(project_root)
    
    # 生成统计信息
    stats = generator.generate_project_stats()
    
    # 生成README统计部分
    readme_stats = generator.generate_readme_stats_section(stats)
    
    # 更新README.md
    if update_readme_stats(project_root, readme_stats):
        print("✅ README.md更新成功")
    else:
        print("❌ README.md更新失败")
    
    # 生成详细报告
    detailed_report = generator.generate_detailed_report(stats)
    report_path = project_root / "PROJECT_FILE_STATISTICS.md"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(detailed_report)
    
    print(f"✅ 生成详细统计报告: {report_path}")
    
    # 显示摘要
    total = stats['total']
    print(f"\\n📋 统计摘要:")
    print(f"  - 总文件数: {total['total_files']:,}")
    print(f"  - 总大小: {generator.format_size(total['total_size'])}")
    print(f"  - Python源码: {total['categories'].get('python_source', {}).get('count', 0)}个")
    print(f"  - 测试文件: {total['categories'].get('python_test', {}).get('count', 0)}个")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
