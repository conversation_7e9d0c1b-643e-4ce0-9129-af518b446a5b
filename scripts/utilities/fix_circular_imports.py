import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
修复循环导入问题的脚本
"""

import os
import sys
from pathlib import Path

def fix_market_data_models():
    """修复市场数据模型的循环导入问题"""
    
    project_root = Path(__file__).parent.parent.parent
    
    # 策略模型文件路径
    strategies_model = project_root / "src/market/strategies/models/market_data.py"
    data_model = project_root / "src/market/data/models/market_data.py"
    
    # 修复策略模型文件，导入真正的模型
    strategies_content = """# Market Data Models for Strategies
# Re-export from domain models for backward compatibility

from src.domain.models.market_data import (
    MarketData,
    UnifiedMarketData, 
    EconomicData,
    MarketInfo,
    MarketType,
    DataQuality,
    Interval,
    ValidationError
)

__all__ = [
    'MarketData',
    'UnifiedMarketData',
    'EconomicData', 
    'MarketInfo',
    'MarketType',
    'DataQuality',
    'Interval',
    'ValidationError'
]
"""
    
    # 修复数据模型文件，导入真正的模型  
    data_content = """# Market Data Models for Data Layer
# Re-export from domain models for backward compatibility

from src.domain.models.market_data import (
    MarketData,
    UnifiedMarketData,
    EconomicData,
    MarketInfo,
    MarketType,
    DataQuality,
    Interval,
    ValidationError
)

__all__ = [
    'MarketData',
    'UnifiedMarketData',
    'EconomicData',
    'MarketInfo', 
    'MarketType',
    'DataQuality',
    'Interval',
    'ValidationError'
]
"""
    
    try:
        # 写入策略模型文件
        with open(strategies_model, 'w', encoding='utf-8') as f:
            f.write(strategies_content)
        logger.info(f"✅ 修复了: {strategies_model}")
        
        # 写入数据模型文件
        with open(data_model, 'w', encoding='utf-8') as f:
            f.write(data_content)
        logger.info(f"✅ 修复了: {data_model}")
        
        return True
        
    except Exception as e:
        logger.info(f"❌ 修复失败: {e}")
        return False

def fix_trading_models():
    """修复交易模型的循环导入问题"""
    
    project_root = Path(__file__).parent.parent.parent
    
    # 策略交易模型文件路径
    strategies_trading = project_root / "src/market/strategies/models/trading.py"
    
    # 检查domain中是否有trading模型
    domain_trading = project_root / "src/domain/models/trading.py"
    
    if domain_trading.exists():
        # 如果domain中有trading模型，重新导出
        content = """# Trading Models for Strategies
# Re-export from domain models for backward compatibility

from src.domain.models.trading import *

"""
    else:
        # 如果没有，创建基础的交易模型
        content = """# Trading Models for Strategies
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum
from dataclasses import dataclass

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class OrderSide(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

@dataclass
class Order:
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    order_id: Optional[str] = None
    timestamp: Optional[datetime] = None
    status: OrderStatus = OrderStatus.PENDING
    metadata: Optional[Dict[str, Any]] = None

__all__ = ['Order', 'OrderType', 'OrderSide', 'OrderStatus']
"""
    
    try:
        with open(strategies_trading, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"✅ 修复了: {strategies_trading}")
        return True
        
    except Exception as e:
        logger.info(f"❌ 修复失败: {e}")
        return False

def fix_portfolio_models():
    """修复投资组合模型的循环导入问题"""
    
    project_root = Path(__file__).parent.parent.parent
    
    # 策略投资组合模型文件路径
    strategies_portfolio = project_root / "src/market/strategies/models/portfolio.py"
    
    # 检查domain中是否有portfolio模型
    domain_portfolio = project_root / "src/domain/models/portfolio.py"
    
    if domain_portfolio.exists():
        # 如果domain中有portfolio模型，重新导出
        content = """# Portfolio Models for Strategies
# Re-export from domain models for backward compatibility

from src.domain.models.portfolio import *

"""
    else:
        # 如果没有，创建基础的投资组合模型
        content = """# Portfolio Models for Strategies
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, field

@dataclass
class Position:
    symbol: str
    quantity: float
    entry_price: float
    current_price: float = 0.0
    timestamp: Optional[datetime] = None
    
    @property
    def market_value(self) -> float:
        return self.quantity * self.current_price
    
    @property
    def unrealized_pnl(self) -> float:
        return (self.current_price - self.entry_price) * self.quantity

@dataclass
class Portfolio:
    cash: float = 0.0
    positions: Dict[str, Position] = field(default_factory=dict)
    
    @property
    def total_value(self) -> float:
        return self.cash + sum(pos.market_value for pos in self.positions.values())
    
    @property
    def total_pnl(self) -> float:
        return sum(pos.unrealized_pnl for pos in self.positions.values())

__all__ = ['Position', 'Portfolio']
"""
    
    try:
        with open(strategies_portfolio, 'w', encoding='utf-8') as f:
            f.write(content)
        logger.info(f"✅ 修复了: {strategies_portfolio}")
        return True
        
    except Exception as e:
        logger.info(f"❌ 修复失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🔧 开始修复循环导入问题...")
    
    success = True
    
    # 修复市场数据模型
    success &= fix_market_data_models()
    
    # 修复交易模型
    success &= fix_trading_models()
    
    # 修复投资组合模型
    success &= fix_portfolio_models()
    
    if success:
        logger.info("✅ 所有循环导入问题已修复!")
        return 0
    else:
        logger.info("❌ 部分修复失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
