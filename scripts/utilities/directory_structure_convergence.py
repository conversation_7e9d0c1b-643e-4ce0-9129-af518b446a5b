import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
目录结构收敛脚本
消除重复的目录结构，统一到主实现
"""

import os
import shutil
import sys
import re
from pathlib import Path
from typing import List, Dict, <PERSON><PERSON>

def analyze_duplicate_structures():
    """分析重复的目录结构"""
    
    project_root = Path(__file__).parent.parent.parent
    
    # 发现的重复结构
    duplicates = {
        "strategies": {
            "compat_layer": project_root / "src/strategies",
            "main_impl": project_root / "src/market/strategies",
            "description": "策略管理模块"
        },
        "data": {
            "compat_layer": project_root / "src/data", 
            "main_impl": project_root / "src/market/data",
            "description": "数据管理模块"
        },
        "utils": {
            "compat_layer": project_root / "src/utils",
            "main_impl": project_root / "src/common/utils", 
            "description": "工具函数模块"
        }
    }
    
    logger.info("🔍 分析重复的目录结构...")
    
    analysis_result = {}
    
    for name, paths in duplicates.items():
        compat_exists = paths["compat_layer"].exists()
        main_exists = paths["main_impl"].exists()
        
        compat_files = 0
        main_files = 0
        
        if compat_exists:
            compat_files = len(list(paths["compat_layer"].rglob("*.py")))
        
        if main_exists:
            main_files = len(list(paths["main_impl"].rglob("*.py")))
        
        analysis_result[name] = {
            "compat_exists": compat_exists,
            "main_exists": main_exists,
            "compat_files": compat_files,
            "main_files": main_files,
            "description": paths["description"]
        }
        
        logger.info(f"  📁 {name}:")
        logger.info(f"    兼容层: {paths['compat_layer']} ({'存在' if compat_exists else '不存在'}, {compat_files}个文件)")
        logger.info(f"    主实现: {paths['main_impl']} ({'存在' if main_exists else '不存在'}, {main_files}个文件)")
    
    return duplicates, analysis_result

def backup_compatibility_layers():
    """备份兼容层代码"""
    
    project_root = Path(__file__).parent.parent.parent
    backup_dir = project_root / "backup/directory_convergence"
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = "20250118"
    
    compat_layers = [
        ("src/strategies", "strategies_compat"),
        ("src/data", "data_compat"), 
        ("src/utils", "utils_compat")
    ]
    
    for source_path, backup_name in compat_layers:
        source = project_root / source_path
        backup_path = backup_dir / f"{backup_name}_{timestamp}"
        
        if source.exists():
            if backup_path.exists():
                shutil.rmtree(backup_path)
            shutil.copytree(source, backup_path)
            logger.info(f"✅ 备份兼容层: {source} -> {backup_path}")
    
    return True

def update_imports_to_main_implementation():
    """更新所有导入语句，指向主实现而不是兼容层"""
    
    project_root = Path(__file__).parent.parent.parent
    
    # 导入替换规则
    replacements = [
        # 策略模块导入替换
        (r'from src\.strategies\.', 'from src.market.strategies.'),
        (r'import src\.strategies\.', 'import src.market.strategies.'),
        (r'from src\.strategies import', 'from src.market.strategies import'),
        (r'import src\.strategies', 'import src.market.strategies'),
        
        # 数据模块导入替换
        (r'from src\.data\.', 'from src.market.data.'),
        (r'import src\.data\.', 'import src.market.data.'),
        (r'from src\.data import', 'from src.market.data import'),
        (r'import src\.data', 'import src.market.data'),
        
        # 工具模块导入替换
        (r'from src\.utils\.', 'from src.common.utils.'),
        (r'import src\.utils\.', 'import src.common.utils.'),
        (r'from src\.utils import', 'from src.common.utils import'),
        (r'import src\.utils', 'import src.common.utils'),
    ]
    
    updated_files = []
    
    # 遍历所有Python文件
    for file_path in project_root.rglob("*.py"):
        if file_path.is_file() and not any(skip in str(file_path) for skip in ['backup/', '__pycache__/', '.git/', 'venv/']):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # 应用所有替换
                for old_pattern, new_pattern in replacements:
                    content = re.sub(old_pattern, new_pattern, content)
                
                # 如果内容有变化，写回文件
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    updated_files.append(file_path)
                    
            except Exception as e:
                logger.info(f"❌ 处理文件 {file_path} 时出错: {e}")
    
    if updated_files:
        logger.info(f"✅ 更新了 {len(updated_files)} 个文件的导入语句:")
        for file_path in updated_files[:10]:  # 只显示前10个
            logger.info(f"  - {file_path.relative_to(project_root)}")
        if len(updated_files) > 10:
            logger.info(f"  ... 以及其他 {len(updated_files) - 10} 个文件")
    else:
        logger.info("ℹ️ 没有发现需要更新的导入语句")
    
    return len(updated_files) > 0

def remove_compatibility_layers():
    """移除兼容层目录"""
    
    project_root = Path(__file__).parent.parent.parent
    
    compat_layers = [
        project_root / "src/strategies",
        project_root / "src/data",
        project_root / "src/utils"
    ]
    
    removed_dirs = []
    
    for compat_dir in compat_layers:
        if compat_dir.exists():
            # 确保备份已创建
            backup_dir = project_root / "backup/directory_convergence"
            if not backup_dir.exists():
                logger.info("❌ 错误：请先创建备份")
                return False
            
            shutil.rmtree(compat_dir)
            removed_dirs.append(compat_dir)
            logger.info(f"🗑️ 移除兼容层: {compat_dir}")
    
    if removed_dirs:
        logger.info(f"✅ 成功移除 {len(removed_dirs)} 个兼容层目录")
    else:
        logger.info("ℹ️ 没有发现需要移除的兼容层目录")
    
    return True

def update_package_structure():
    """更新包结构，确保__init__.py文件正确"""
    
    project_root = Path(__file__).parent.parent.parent
    
    # 检查主实现目录的__init__.py
    main_dirs = [
        project_root / "src/market/strategies",
        project_root / "src/market/data", 
        project_root / "src/common/utils"
    ]
    
    for main_dir in main_dirs:
        init_file = main_dir / "__init__.py"
        if main_dir.exists() and not init_file.exists():
            # 创建基本的__init__.py
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write(f'"""{main_dir.name} module"""\n')
            logger.info(f"✅ 创建 {init_file}")
    
    return True

def create_convergence_summary():
    """创建目录收敛总结文档"""
    
    summary_content = """# 目录结构收敛总结

## 🎯 收敛目标
消除项目中的重复目录结构，统一到主实现，提高代码组织的清晰度。

## 📋 收敛前状况
项目存在以下重复结构：

### 🔄 重复的模块
1. **策略模块**:
   - 兼容层: `src/strategies/` (重定向)
   - 主实现: `src/market/strategies/` (实际功能)

2. **数据模块**:
   - 兼容层: `src/data/` (重定向)
   - 主实现: `src/market/data/` (实际功能)

3. **工具模块**:
   - 兼容层: `src/utils/` (重定向)
   - 主实现: `src/common/utils/` (实际功能)

## ❌ 问题分析
1. **结构漂移**: 同一职责在多处并存，违反文件组织规范
2. **认知负担**: 开发者难以快速找到正确的模块位置
3. **维护成本**: 需要同时维护兼容层和主实现
4. **导入混乱**: 不同文件使用不同的导入路径

## ✅ 收敛方案
1. **保留主实现**: 统一使用以下结构
   - `src/market/strategies/` - 所有策略相关功能
   - `src/market/data/` - 所有数据管理功能
   - `src/common/utils/` - 所有工具函数

2. **移除兼容层**: 删除重定向目录
   - 移除 `src/strategies/`
   - 移除 `src/data/`
   - 移除 `src/utils/`

3. **更新导入**: 统一所有导入语句指向主实现

## 📊 预期收益
- ✅ 简化项目结构，提高可理解性
- ✅ 减少维护成本和认知负担
- ✅ 统一导入路径，避免混乱
- ✅ 符合Clean Architecture原则

## 🛠️ 实施步骤
1. 备份兼容层代码
2. 更新所有导入语句
3. 移除兼容层目录
4. 验证项目功能正常

## 📅 收敛时间
预计收敛时间: 30-60分钟

## 🔄 回滚计划
如果收敛出现问题，可以从backup目录恢复兼容层。

## 📋 新的目录结构

```
src/
├── 🎯 application/              # 应用层
├── 🏛️ domain/                  # 领域层
├── 🔧 infrastructure/          # 基础设施层
├── 📊 market/                  # 市场层（主实现）
│   ├── strategies/             # 策略管理 ✅
│   ├── data/                   # 数据管理 ✅
│   └── indicators/             # 指标计算
├── 💱 trading/                 # 交易层
├── ⚙️ core/                    # 核心层
├── 🛠️ common/                  # 公共层
│   └── utils/                  # 工具函数 ✅
└── 其他模块...
```

---

*目录结构收敛完成时间: 2025年1月18日*
"""
    
    project_root = Path(__file__).parent.parent.parent
    summary_file = project_root / "DIRECTORY_CONVERGENCE_SUMMARY.md"
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(summary_content)
    
    logger.info(f"✅ 创建目录收敛总结: {summary_file}")
    
    return summary_file

def verify_convergence():
    """验证目录收敛是否成功"""
    
    project_root = Path(__file__).parent.parent.parent
    
    issues = []
    
    # 检查兼容层是否已移除
    compat_layers = [
        project_root / "src/strategies",
        project_root / "src/data",
        project_root / "src/utils"
    ]
    
    for compat_dir in compat_layers:
        if compat_dir.exists():
            issues.append(f"兼容层目录仍存在: {compat_dir}")
    
    # 检查主实现是否存在
    main_impls = [
        project_root / "src/market/strategies",
        project_root / "src/market/data",
        project_root / "src/common/utils"
    ]
    
    for main_dir in main_impls:
        if not main_dir.exists():
            issues.append(f"主实现目录不存在: {main_dir}")
    
    if issues:
        logger.info("❌ 目录收敛验证失败:")
        for issue in issues:
            logger.info(f"  - {issue}")
        return False
    else:
        logger.info("✅ 目录收敛验证成功")
        return True

def main():
    """主函数"""
    
    logger.info("🚀 目录结构收敛")
    logger.info("=" * 50)
    
    # 步骤1: 分析重复结构
    duplicates, analysis = analyze_duplicate_structures()
    
    # 步骤2: 备份兼容层
    logger.info(f"\\n💾 备份兼容层...")
    if not backup_compatibility_layers():
        logger.info("❌ 备份失败")
        return 1
    
    # 步骤3: 更新导入语句
    logger.info(f"\\n🔄 更新导入语句...")
    if not update_imports_to_main_implementation():
        logger.info("ℹ️ 没有需要更新的导入语句")
    
    # 步骤4: 移除兼容层
    logger.info(f"\\n🗑️ 移除兼容层...")
    if not remove_compatibility_layers():
        logger.info("❌ 移除兼容层失败")
        return 1
    
    # 步骤5: 更新包结构
    logger.info(f"\\n📦 更新包结构...")
    if not update_package_structure():
        logger.info("❌ 更新包结构失败")
        return 1
    
    # 步骤6: 创建总结文档
    summary_file = create_convergence_summary()
    
    # 步骤7: 验证收敛
    logger.info(f"\\n🔍 验证收敛...")
    if not verify_convergence():
        logger.info("❌ 目录收敛验证失败")
        return 1
    
    logger.info(f"\\n🎉 目录结构收敛完成!")
    logger.info(f"📄 查看收敛总结: {summary_file}")
    logger.info(f"🎯 项目结构现在更加清晰和一致")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
