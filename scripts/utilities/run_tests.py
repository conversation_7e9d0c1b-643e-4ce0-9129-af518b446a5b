import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
测试运行器脚本
提供简化的测试执行和报告功能
"""

import sys
import subprocess
import argparse
from pathlib import Path

def run_command(cmd: list, description: str) -> tuple[int, str]:
    """执行命令并返回结果"""
    logger.info(f"🔄 {description}...")
    try:
        result = subprocess.run(
            cmd, 
            cwd=Path(__file__).parent.parent.parent,
            capture_output=True, 
            text=True, 
            timeout=300
        )
        return result.returncode, result.stdout + result.stderr
    except subprocess.TimeoutExpired:
        return -1, "命令执行超时"
    except Exception as e:
        return -2, f"执行错误: {e}"

def run_tests(test_type: str = "unit", verbose: bool = False, quick: bool = False):
    """运行指定类型的测试"""
    
    project_root = Path(__file__).parent.parent.parent
    
    # 基础pytest命令
    base_cmd = [sys.executable, "-m", "pytest"]
    
    # 根据测试类型设置参数
    if test_type == "unit":
        test_path = "tests/unit/"
        max_failures = 10
    elif test_type == "integration":
        test_path = "tests/integration/"
        max_failures = 5
    elif test_type == "e2e":
        test_path = "tests/end_to_end/"
        max_failures = 3
    elif test_type == "all":
        test_path = "tests/"
        max_failures = 20
    else:
        logger.info(f"❌ 未知的测试类型: {test_type}")
        return 1
    
    # 构建命令
    cmd = base_cmd + [
        test_path,
        f"--maxfail={max_failures}",
        "--tb=short",
        "-ra"
    ]
    
    # 添加详细输出
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    # 快速模式跳过慢测试
    if quick:
        cmd.extend(["-m", "not slow"])
    
    # 运行测试
    returncode, output = run_command(cmd, f"运行{test_type}测试")
    
    logger.info(output)
    
    if returncode == 0:
        logger.info(f"✅ {test_type}测试全部通过")
    else:
        logger.info(f"❌ {test_type}测试有失败，返回码: {returncode}")
    
    return returncode

def run_coverage_tests():
    """运行带覆盖率的测试"""
    cmd = [
        sys.executable, "-m", "pytest", 
        "tests/unit/",
        "--cov=src",
        "--cov-report=term-missing",
        "--cov-report=html:reports/coverage",
        "--tb=short",
        "-q"
    ]
    
    returncode, output = run_command(cmd, "运行覆盖率测试")
    logger.info(output)
    
    if returncode == 0:
        logger.info("✅ 覆盖率测试完成，报告已生成")
    else:
        logger.info(f"❌ 覆盖率测试失败，返回码: {returncode}")
    
    return returncode

def run_lint_checks():
    """运行代码质量检查"""
    logger.info("🔍 运行代码质量检查...")
    
    # 检查可用的linting工具
    checks = []
    
    # Black格式检查
    cmd = [sys.executable, "-m", "black", "--check", "src/", "tests/"]
    returncode, output = run_command(cmd, "Black格式检查")
    checks.append(("Black", returncode == 0))
    if returncode != 0:
        logger.info("📝 发现格式问题，运行 'python -m black src/ tests/' 来修复")
    
    return all(check[1] for check in checks)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="量化交易系统测试运行器")
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "e2e", "all", "coverage", "lint"],
        default="unit",
        nargs="?",
        help="测试类型"
    )
    parser.add_argument("-v", "--verbose", action="store_true", help="详细输出")
    parser.add_argument("-q", "--quick", action="store_true", help="快速模式(跳过慢测试)")
    
    args = parser.parse_args()
    
    logger.info("🧪 量化交易系统测试运行器")
    logger.info("=" * 50)
    
    if args.test_type == "coverage":
        return run_coverage_tests()
    elif args.test_type == "lint":
        return 0 if run_lint_checks() else 1
    else:
        return run_tests(args.test_type, args.verbose, args.quick)

if __name__ == "__main__":
    sys.exit(main())
