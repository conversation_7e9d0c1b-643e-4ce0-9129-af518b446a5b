import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
测试导入路径修复脚本
批量修复测试文件中的常见导入错误
"""

import os
import re
import sys
from pathlib import Path

def fix_import_paths(directory: str):
    """
    修复指定目录下的导入路径错误
    """
    
    # 定义需要修复的导入路径映射
    fixes = [
        # 修复重复的路径
        (r'from src\.market\.data\.data\.', 'from src.market.data.'),
        (r'from src\.market\.strategies\.strategies\.', 'from src.market.strategies.'),
        
        # 修复缺失的src前缀  
        (r'from strategies\.', 'from src.market.strategies.'),
        (r'from market\.', 'from src.market.'),
        (r'from data\.', 'from src.market.data.'),
        
        # 修复不存在的模块
        (r'from src\.data\.data_validator', 'from src.security.data_validator'),
        (r'from src\.core\.system_diagnostic_engine', 'from src.core.enhanced_health_checker'),
    ]
    
    fixed_files = []
    error_files = []
    
    # 遍历所有Python文件
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    
                    # 应用所有修复
                    for pattern, replacement in fixes:
                        content = re.sub(pattern, replacement, content)
                    
                    # 如果内容发生了变化，写回文件
                    if content != original_content:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        fixed_files.append(file_path)
                        logger.info(f"✅ 修复了: {file_path}")
                        
                except Exception as e:
                    error_files.append((file_path, str(e)))
                    logger.info(f"❌ 错误: {file_path} - {e}")
    
    return fixed_files, error_files

def main():
    """主函数"""
    logger.info("🔧 开始修复测试导入路径...")
    
    project_root = Path(__file__).parent.parent.parent
    tests_dir = project_root / "tests"
    
    if not tests_dir.exists():
        logger.info(f"❌ 测试目录不存在: {tests_dir}")
        return 1
    
    fixed_files, error_files = fix_import_paths(str(tests_dir))
    
    logger.info(f"\\n📊 修复结果:")
    logger.info(f"✅ 成功修复 {len(fixed_files)} 个文件")
    logger.info(f"❌ 修复失败 {len(error_files)} 个文件")
    
    if error_files:
        logger.info("\\n❌ 失败的文件:")
        for file_path, error in error_files:
            logger.info(f"  - {file_path}: {error}")
    
    if fixed_files:
        logger.info("\\n✅ 成功修复的文件:")
        for file_path in fixed_files:
            logger.info(f"  - {file_path}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
