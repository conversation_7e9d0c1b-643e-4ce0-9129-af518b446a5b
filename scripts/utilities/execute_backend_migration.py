import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
执行后端架构迁移
将双后端合并为统一的FastAPI后端
"""

import os
import shutil
import sys
from pathlib import Path
import re

def remove_infrastructure_api():
    """移除冗余的基础设施API"""
    
    project_root = Path(__file__).parent.parent.parent
    infra_api_path = project_root / "src/infrastructure/api_clients/api"
    
    if infra_api_path.exists():
        logger.info(f"🗑️ 移除冗余的基础设施API: {infra_api_path}")
        
        # 确保备份已经创建
        backup_path = project_root / "backup/backend_migration/infrastructure_api_20250118"
        if not backup_path.exists():
            logger.info("❌ 错误：请先运行备份脚本")
            return False
        
        # 移除目录
        shutil.rmtree(infra_api_path)
        logger.info("✅ 基础设施API已移除")
        
        # 检查是否还有其他API相关文件需要清理
        api_clients_dir = project_root / "src/infrastructure/api_clients"
        if api_clients_dir.exists() and not any(api_clients_dir.iterdir()):
            shutil.rmtree(api_clients_dir)
            logger.info("✅ 清理了空的api_clients目录")
        
        return True
    else:
        logger.info("⚠️ 基础设施API目录不存在，跳过移除")
        return True

def update_import_references():
    """更新所有引用基础设施API的导入语句"""
    
    project_root = Path(__file__).parent.parent.parent
    
    # 需要检查的文件模式
    patterns = [
        "**/*.py",
    ]
    
    # 需要替换的导入模式
    replacements = [
        # 将基础设施API的导入替换为Web UI后端
        (r'from src\.infrastructure\.api_clients\.api', 'from web_ui.backend.app.api'),
        (r'from \.\.\.infrastructure\.api_clients\.api', 'from web_ui.backend.app.api'),
        (r'import src\.infrastructure\.api_clients\.api', 'import web_ui.backend.app.api'),
        
        # 更新app创建的引用
        (r'from src\.infrastructure\.api_clients\.api\.app import create_app', 'from web_ui.backend.main import app'),
        (r'from \.\.infrastructure\.api_clients\.api\.app import create_app', 'from web_ui.backend.main import app'),
    ]
    
    updated_files = []
    
    for pattern in patterns:
        for file_path in project_root.rglob(pattern):
            if file_path.is_file() and not any(skip in str(file_path) for skip in ['backup/', '__pycache__/', '.git/', 'venv/']):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    original_content = content
                    
                    # 应用所有替换
                    for old_pattern, new_pattern in replacements:
                        content = re.sub(old_pattern, new_pattern, content)
                    
                    # 如果内容有变化，写回文件
                    if content != original_content:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        updated_files.append(file_path)
                        
                except Exception as e:
                    logger.info(f"❌ 处理文件 {file_path} 时出错: {e}")
    
    if updated_files:
        logger.info(f"✅ 更新了 {len(updated_files)} 个文件的导入引用:")
        for file_path in updated_files[:10]:  # 只显示前10个
            logger.info(f"  - {file_path.relative_to(project_root)}")
        if len(updated_files) > 10:
            logger.info(f"  ... 以及其他 {len(updated_files) - 10} 个文件")
    else:
        logger.info("ℹ️ 没有发现需要更新的导入引用")
    
    return True

def update_startup_scripts():
    """更新启动脚本，统一后端启动方式"""
    
    project_root = Path(__file__).parent.parent.parent
    
    # 更新主启动脚本
    main_startup = project_root / "start_system.sh"
    if main_startup.exists():
        with open(main_startup, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换可能的双后端启动逻辑为单一后端启动
        updates = [
            # 确保只启动Web UI后端
            ('# 启动基础设施API', '# 基础设施API已合并到Web UI后端'),
            ('uvicorn src.infrastructure.api_clients.api.app:app', '# 已合并到Web UI后端'),
        ]
        
        for old, new in updates:
            content = content.replace(old, new)
        
        with open(main_startup, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("✅ 更新了主启动脚本")
    
    return True

def create_unified_backend_info():
    """创建统一后端的信息文档"""
    
    content = """# 统一后端架构

## 🎯 架构优化完成

量化交易系统已成功从双后端架构迁移到统一的FastAPI后端架构。

### ✅ 迁移完成的工作

1. **移除冗余基础设施API**
   - 删除了 `src/infrastructure/api_clients/api/` 目录
   - 清理了相关的导入和依赖

2. **统一后端服务**
   - 保留 `web_ui/backend/` 作为唯一后端
   - 所有API功能现在统一通过FastAPI提供

3. **更新启动方式**
   - 简化了启动脚本，只需启动一个后端服务
   - 统一了配置管理和部署流程

### 🏗️ 新的架构

```
统一后端架构:
├── 🎯 web_ui/backend/           # 唯一后端服务（FastAPI）
│   ├── app/api/endpoints/      # API端点实现
│   ├── app/services/           # 业务服务层  
│   ├── app/models/             # 数据模型
│   └── app/core/               # 核心配置和工具
│
├── 📊 src/                     # 核心业务逻辑（直接导入）
│   ├── market/                 # 市场数据和策略
│   ├── backtest/               # 回测引擎
│   ├── risk/                   # 风险管理
│   └── core/                   # 核心引擎
│
└── 🌐 web_ui/frontend/         # React前端应用
```

### 📊 架构优势

- ✅ **简化技术栈**: 只有一个FastAPI后端
- ✅ **降低维护成本**: 统一的代码库和部署流程  
- ✅ **提高性能**: 减少了HTTP通信开销
- ✅ **统一开发体验**: 一致的开发和调试环境

### 🚀 启动方式

现在只需要一个命令就能启动整个系统：

```bash
./start_system.sh
```

这会自动启动：
- FastAPI后端服务 (端口8000)
- React前端服务 (端口3000)

### 🔧 开发指南

1. **后端开发**: 在 `web_ui/backend/` 目录下开发
2. **业务逻辑**: 在 `src/` 目录下开发，通过导入在后端中使用
3. **前端开发**: 在 `web_ui/frontend/` 目录下开发

### 📋 API文档

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### 🔄 版本历史

- **v1.0**: 双后端架构（Flask基础设施API + FastAPI Web UI后端）
- **v2.0**: 统一FastAPI后端架构 ✅

---

*架构迁移完成时间: 2025年1月18日*
"""
    
    project_root = Path(__file__).parent.parent.parent
    info_file = project_root / "UNIFIED_BACKEND_ARCHITECTURE.md"
    
    with open(info_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info(f"✅ 创建统一后端架构文档: {info_file}")
    
    return info_file

def verify_migration():
    """验证迁移是否成功"""
    
    project_root = Path(__file__).parent.parent.parent
    
    issues = []
    
    # 检查基础设施API是否已移除
    infra_api_path = project_root / "src/infrastructure/api_clients/api"
    if infra_api_path.exists():
        issues.append("基础设施API目录仍然存在")
    
    # 检查Web UI后端是否存在
    webui_backend = project_root / "web_ui/backend"
    if not webui_backend.exists():
        issues.append("Web UI后端目录不存在")
    
    # 检查main.py是否存在
    main_py = webui_backend / "main.py"
    if not main_py.exists():
        issues.append("Web UI后端main.py不存在")
    
    if issues:
        logger.info("❌ 迁移验证失败:")
        for issue in issues:
            logger.info(f"  - {issue}")
        return False
    else:
        logger.info("✅ 迁移验证成功")
        return True

def main():
    """主函数"""
    
    logger.info("🚀 执行后端架构迁移")
    logger.info("=" * 50)
    
    # 步骤1: 移除基础设施API
    if not remove_infrastructure_api():
        logger.info("❌ 移除基础设施API失败")
        return 1
    
    # 步骤2: 更新导入引用
    if not update_import_references():
        logger.info("❌ 更新导入引用失败")
        return 1
    
    # 步骤3: 更新启动脚本
    if not update_startup_scripts():
        logger.info("❌ 更新启动脚本失败")
        return 1
    
    # 步骤4: 创建文档
    info_file = create_unified_backend_info()
    
    # 步骤5: 验证迁移
    if not verify_migration():
        logger.info("❌ 迁移验证失败")
        return 1
    
    logger.info(f"\\n🎉 后端架构迁移完成!")
    logger.info(f"📄 查看架构文档: {info_file}")
    logger.info(f"🚀 现在可以使用 ./start_system.sh 启动统一后端")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
