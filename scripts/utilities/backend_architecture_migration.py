import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
后端架构迁移脚本
将双后端架构合并为统一的FastAPI后端
"""

import os
import shutil
import sys
from pathlib import Path
from typing import List, Dict

def analyze_current_architecture():
    """分析当前的双后端架构"""
    
    project_root = Path(__file__).parent.parent.parent
    
    # 基础设施API路径
    infra_api = project_root / "src/infrastructure/api_clients/api"
    
    # Web UI后端路径  
    webui_backend = project_root / "web_ui/backend"
    
    logger.info("🔍 分析当前后端架构...")
    logger.info(f"基础设施API: {infra_api}")
    logger.info(f"Web UI后端: {webui_backend}")
    
    # 分析基础设施API的路由
    infra_routers = []
    if (infra_api / "routers").exists():
        infra_routers = list((infra_api / "routers").glob("*.py"))
        infra_routers = [r.stem for r in infra_routers if r.stem != "__init__"]
    
    # 分析Web UI后端的路由
    webui_endpoints = []
    webui_endpoints_dir = webui_backend / "app/api/endpoints"
    if webui_endpoints_dir.exists():
        webui_endpoints = list(webui_endpoints_dir.glob("*.py"))
        webui_endpoints = [e.stem for e in webui_endpoints if e.stem != "__init__"]
    
    logger.info(f"\\n📊 架构分析结果:")
    logger.info(f"基础设施API路由: {infra_routers}")
    logger.info(f"Web UI后端端点: {webui_endpoints}")
    
    # 找出重叠的功能模块
    overlap = set(infra_routers) & set(webui_endpoints)
    logger.info(f"重叠功能模块: {list(overlap)}")
    
    return infra_api, webui_backend, infra_routers, webui_endpoints, overlap

def create_unified_backend_plan():
    """创建统一后端的迁移计划"""
    
    plan = {
        "actions": [
            {
                "action": "backup_current_backend",
                "description": "备份当前的双后端代码",
                "priority": 1
            },
            {
                "action": "merge_route_definitions", 
                "description": "合并路由定义，保留Web UI后端为主体",
                "priority": 2
            },
            {
                "action": "integrate_core_services",
                "description": "将核心服务直接集成到FastAPI后端",
                "priority": 3
            },
            {
                "action": "remove_redundant_infrastructure",
                "description": "移除多余的基础设施API",
                "priority": 4
            },
            {
                "action": "update_imports_and_dependencies",
                "description": "更新导入和依赖关系",
                "priority": 5
            },
            {
                "action": "update_startup_scripts",
                "description": "更新启动脚本，统一后端启动方式",
                "priority": 6
            }
        ]
    }
    
    return plan

def backup_current_backends():
    """备份当前的后端代码"""
    
    project_root = Path(__file__).parent.parent.parent
    backup_dir = project_root / "backup/backend_migration"
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = "20250118"
    
    # 备份基础设施API
    infra_backup = backup_dir / f"infrastructure_api_{timestamp}"
    if (project_root / "src/infrastructure/api_clients/api").exists():
        shutil.copytree(
            project_root / "src/infrastructure/api_clients/api",
            infra_backup,
            dirs_exist_ok=True
        )
        logger.info(f"✅ 备份基础设施API到: {infra_backup}")
    
    # 备份Web UI后端
    webui_backup = backup_dir / f"webui_backend_{timestamp}"
    if (project_root / "web_ui/backend").exists():
        shutil.copytree(
            project_root / "web_ui/backend",
            webui_backup,
            dirs_exist_ok=True
        )
        logger.info(f"✅ 备份Web UI后端到: {webui_backup}")
    
    return True

def create_migration_summary():
    """创建迁移总结文档"""
    
    summary_content = """# 后端架构迁移总结

## 🎯 迁移目标
将双后端架构（基础设施API + Web UI后端）合并为统一的FastAPI后端。

## 📋 迁移前状况
- **基础设施API**: `src/infrastructure/api_clients/api/` - 基于FastAPI的简单API
- **Web UI后端**: `web_ui/backend/` - 基于FastAPI的完整后端应用

## 🔧 架构问题
1. **技术栈重复**: 两个FastAPI应用做相似的事情
2. **维护成本高**: 需要同时维护两套路由和逻辑
3. **部署复杂**: 需要运行两个独立的服务
4. **通信开销**: 两个后端间可能存在HTTP通信开销

## ✅ 迁移方案
1. 保留 `web_ui/backend` 作为主后端
2. 将 `src/infrastructure/api_clients/api` 中的核心功能迁移到主后端
3. 核心服务（src/目录下的业务逻辑）直接作为Python模块导入，而不是通过API调用
4. 统一启动方式和配置管理

## 📊 预期收益
- ✅ 简化技术栈和架构
- ✅ 降低维护成本
- ✅ 减少部署复杂度  
- ✅ 提高性能（减少HTTP通信）
- ✅ 统一开发体验

## 🛠️ 实施步骤
1. 备份现有代码
2. 分析功能重叠
3. 合并路由定义
4. 更新导入和依赖
5. 测试验证
6. 更新文档和启动脚本

## 📅 迁移时间
预计迁移时间: 2-3小时

## 🔄 回滚计划
如果迁移出现问题，可以从backup目录恢复原有架构。
"""
    
    project_root = Path(__file__).parent.parent.parent
    summary_file = project_root / "BACKEND_MIGRATION_PLAN.md"
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(summary_content)
    
    logger.info(f"✅ 创建迁移计划文档: {summary_file}")
    
    return summary_file

def main():
    """主函数"""
    
    logger.info("🚀 后端架构迁移分析")
    logger.info("=" * 50)
    
    # 分析当前架构
    infra_api, webui_backend, infra_routers, webui_endpoints, overlap = analyze_current_architecture()
    
    # 创建迁移计划
    plan = create_unified_backend_plan()
    
    logger.info(f"\\n📋 迁移计划:")
    for action in plan["actions"]:
        logger.info(f"  {action['priority']}. {action['description']}")
    
    # 备份当前代码
    logger.info(f"\\n💾 开始备份...")
    backup_success = backup_current_backends()
    
    if backup_success:
        logger.info("✅ 备份完成")
    else:
        logger.info("❌ 备份失败")
        return 1
    
    # 创建迁移总结
    summary_file = create_migration_summary()
    
    logger.info(f"\\n🎉 迁移准备完成!")
    logger.info(f"📄 查看详细迁移计划: {summary_file}")
    logger.info(f"🔄 下一步: 执行实际的代码迁移")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
