#!/usr/bin/env python3
"""
占位代码清理脚本
分析和清理项目中的pass/NotImplementedError/print等占位代码
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple
import ast

class PlaceholderAnalyzer:
    """占位代码分析器"""
    
    def __init__(self):
        self.patterns = {
            'pass_statements': r'^\s*pass\s*$',
            'not_implemented': r'raise\s+NotImplementedError',
            'print_statements': r'^\s*print\s*\(',
            'todo_comments': r'#\s*TODO',
            'fixme_comments': r'#\s*FIXME',
        }
        
        self.issues = {
            'pass_statements': [],
            'not_implemented': [],
            'print_statements': [],
            'todo_comments': [],
            'fixme_comments': [],
        }
    
    def analyze_file(self, file_path: Path) -> Dict:
        """分析单个文件的占位代码"""
        
        issues = {key: [] for key in self.patterns.keys()}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                for issue_type, pattern in self.patterns.items():
                    if re.search(pattern, line, re.MULTILINE):
                        issues[issue_type].append({
                            'file': file_path,
                            'line': line_num,
                            'content': line.strip(),
                            'context': self._get_context(lines, line_num)
                        })
        
        except Exception as e:
            logger.info(f"❌ 分析文件 {file_path} 时出错: {e}")
        
        return issues
    
    def _get_context(self, lines: List[str], line_num: int, context_size: int = 2) -> List[str]:
        """获取代码上下文"""
        start = max(0, line_num - context_size - 1)
        end = min(len(lines), line_num + context_size)
        return [f"{i+1:3}: {lines[i].rstrip()}" for i in range(start, end)]
    
    def analyze_project(self, project_root: Path) -> Dict:
        """分析整个项目的占位代码"""
        
        logger.info("🔍 分析项目中的占位代码...")
        
        total_issues = {key: [] for key in self.patterns.keys()}
        
        # 遍历所有Python文件
        for py_file in project_root.rglob("*.py"):
            if any(skip in str(py_file) for skip in ['backup/', '__pycache__/', '.git/', 'venv/']):
                continue
            
            file_issues = self.analyze_file(py_file)
            
            for issue_type, issues in file_issues.items():
                total_issues[issue_type].extend(issues)
        
        return total_issues

def create_placeholder_replacements():
    """创建占位代码的替换规则"""
    
    replacements = {
        # 常见的占位代码替换
        'api_endpoints': {
            'pass': 'raise HTTPException(status_code=501, detail="功能开发中")',
            'print': 'logger.info',
            'context': 'API端点中'
        },
        'service_methods': {
            'pass': 'raise NotImplementedError("该功能尚未实现，请联系开发团队")',
            'print': 'logger.info',
            'context': '服务方法中'
        },
        'data_adapters': {
            'pass': 'return []  # 返回空数据，避免系统错误',
            'print': 'logger.warning',
            'context': '数据适配器中'
        },
        'error_handlers': {
            'pass': 'logger.error(f"未处理的错误: {str(e)}")',
            'print': 'logger.error',
            'context': '错误处理中'
        }
    }
    
    return replacements

def fix_common_placeholders(file_path: Path, content: str) -> Tuple[str, List[str]]:
    """修复常见的占位代码"""
    
    fixes = []
    original_content = content
    
    # 1. 修复API路由中的pass语句
    if 'router' in content and '@router.' in content:
        # API端点中的pass替换为HTTP 501错误
        pattern = r'(@router\.\w+\([^)]*\)\s*(?:async\s+)?def\s+\w+\([^)]*\):[^}]*?)\s*pass\s*$'
        def replace_api_pass(match):
            return match.group(1) + '\n    raise HTTPException(status_code=501, detail="功能开发中")'
        
        new_content = re.sub(pattern, replace_api_pass, content, flags=re.MULTILINE | re.DOTALL)
        if new_content != content:
            fixes.append("API端点pass语句 -> HTTP 501错误")
            content = new_content
    
    # 2. 修复print语句为logger
    if 'print(' in content:
        # 添加logging导入
        if 'import logging' not in content:
            content = 'import logging\n' + content
            fixes.append("添加logging导入")
        
        # 确保logger存在
        if 'logger = logging.getLogger(' not in content:
            lines = content.split('\n')
            # 找到合适的位置插入logger定义
            insert_pos = 0
            for i, line in enumerate(lines):
                if line.startswith('import ') or line.startswith('from '):
                    insert_pos = i + 1
                elif line.strip() and not line.startswith('#'):
                    break
            
            lines.insert(insert_pos, 'logger = logging.getLogger(__name__)')
            content = '\n'.join(lines)
            fixes.append("添加logger定义")
        
        # 替换print为logger.info
        content = re.sub(r'\bprint\s*\(', 'logger.info(', content)
        if 'logger.info(' in content and 'print(' not in content:
            fixes.append("print语句 -> logger.info")
    
    # 3. 修复NotImplementedError增加更友好的消息
    pattern = r'raise\s+NotImplementedError\(\s*\)'
    replacement = 'raise NotImplementedError("该功能尚未实现，请联系开发团队")'
    new_content = re.sub(pattern, replacement, content)
    if new_content != content:
        fixes.append("NotImplementedError -> 友好错误消息")
        content = new_content
    
    # 4. 修复数据适配器中的pass
    if 'adapter' in file_path.name.lower() or 'Adapter' in content:
        # 数据适配器中的pass替换为返回空数据
        pattern = r'(def\s+get_\w+.*?:\s*(?:\n\s*""".*?"""\s*)?)\s*pass\s*$'
        def replace_adapter_pass(match):
            return match.group(1) + '\n        return []  # 返回空数据，避免系统错误'
        
        new_content = re.sub(pattern, replace_adapter_pass, content, flags=re.MULTILINE | re.DOTALL)
        if new_content != content:
            fixes.append("数据适配器pass -> 返回空数据")
            content = new_content
    
    return content, fixes

def apply_placeholder_fixes(project_root: Path) -> Dict[str, List[str]]:
    """应用占位代码修复"""
    
    logger.info("🔧 应用占位代码修复...")
    
    fixed_files = {}
    
    # 遍历所有Python文件
    for py_file in project_root.rglob("*.py"):
        if any(skip in str(py_file) for skip in ['backup/', '__pycache__/', '.git/', 'venv/']):
            continue
        
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 应用修复
            new_content, fixes = fix_common_placeholders(py_file, content)
            
            # 如果有修复，写回文件
            if fixes and new_content != content:
                with open(py_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                fixed_files[str(py_file.relative_to(project_root))] = fixes
        
        except Exception as e:
            logger.info(f"❌ 修复文件 {py_file} 时出错: {e}")
    
    return fixed_files

def generate_placeholder_report(issues: Dict, project_root: Path) -> str:
    """生成占位代码分析报告"""
    
    report_lines = [
        "# 占位代码分析报告",
        "",
        f"**分析时间**: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        f"**项目路径**: {project_root}",
        "",
        "## 📊 问题统计",
        ""
    ]
    
    total_issues = sum(len(issues_list) for issues_list in issues.values())
    
    issue_descriptions = {
        'pass_statements': 'Pass语句',
        'not_implemented': 'NotImplementedError',
        'print_statements': 'Print语句',
        'todo_comments': 'TODO注释',
        'fixme_comments': 'FIXME注释'
    }
    
    report_lines.append("| 问题类型 | 数量 | 说明 |")
    report_lines.append("|---------|------|------|")
    
    for issue_type, issues_list in issues.items():
        desc = issue_descriptions.get(issue_type, issue_type)
        count = len(issues_list)
        explanation = {
            'pass_statements': '占位语句，需要实现具体功能',
            'not_implemented': '抛出未实现异常，需要添加实现',
            'print_statements': '调试输出，应该改为日志记录',
            'todo_comments': '待办事项，需要跟进处理',
            'fixme_comments': '需要修复的问题'
        }.get(issue_type, '需要处理的代码')
        
        report_lines.append(f"| {desc} | {count} | {explanation} |")
    
    report_lines.extend([
        "",
        f"**总计**: {total_issues} 个问题需要处理",
        "",
        "## 📋 详细问题列表",
        ""
    ])
    
    for issue_type, issues_list in issues.items():
        if not issues_list:
            continue
        
        desc = issue_descriptions.get(issue_type, issue_type)
        report_lines.extend([
            f"### {desc} ({len(issues_list)}个)",
            ""
        ])
        
        for issue in issues_list[:20]:  # 只显示前20个
            rel_path = issue['file'].relative_to(project_root)
            report_lines.extend([
                f"**文件**: `{rel_path}:{issue['line']}`",
                f"**代码**: `{issue['content']}`",
                ""
            ])
        
        if len(issues_list) > 20:
            report_lines.append(f"... 以及其他 {len(issues_list) - 20} 个类似问题")
            report_lines.append("")
    
    return "\n".join(report_lines)

def main():
    """主函数"""
    
    logger.info("🚀 占位代码清理")
    logger.info("=" * 50)
    
    project_root = Path(__file__).parent.parent.parent
    
    # 步骤1: 分析占位代码
    analyzer = PlaceholderAnalyzer()
    issues = analyzer.analyze_project(project_root)
    
    total_issues = sum(len(issues_list) for issues_list in issues.values())
    logger.info(f"\\n📊 发现 {total_issues} 个占位代码问题:")
    
    issue_descriptions = {
        'pass_statements': 'Pass语句',
        'not_implemented': 'NotImplementedError',
        'print_statements': 'Print语句', 
        'todo_comments': 'TODO注释',
        'fixme_comments': 'FIXME注释'
    }
    
    for issue_type, issues_list in issues.items():
        desc = issue_descriptions.get(issue_type, issue_type)
        logger.info(f"  - {desc}: {len(issues_list)}个")
    
    # 步骤2: 应用修复
    logger.info(f"\\n🔧 应用占位代码修复...")
    fixed_files = apply_placeholder_fixes(project_root)
    
    if fixed_files:
        logger.info(f"✅ 修复了 {len(fixed_files)} 个文件:")
        for file_path, fixes in list(fixed_files.items())[:10]:
            logger.info(f"  - {file_path}: {', '.join(fixes)}")
        if len(fixed_files) > 10:
            logger.info(f"  ... 以及其他 {len(fixed_files) - 10} 个文件")
    else:
        logger.info("ℹ️ 没有发现可以自动修复的占位代码")
    
    # 步骤3: 生成报告
    logger.info(f"\\n📄 生成占位代码分析报告...")
    report_content = generate_placeholder_report(issues, project_root)
    
    report_file = project_root / "PLACEHOLDER_CODE_ANALYSIS.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    logger.info(f"✅ 报告已生成: {report_file}")
    
    # 总结
    remaining_issues = sum(len(issues_list) for issues_list in issues.values())
    logger.info(f"\\n🎯 清理总结:")
    logger.info(f"  - 自动修复: {len(fixed_files)} 个文件")
    logger.info(f"  - 剩余问题: {remaining_issues} 个")
    logger.info(f"  - 详细报告: {report_file}")
    
    if remaining_issues > 0:
        logger.info(f"\\n⚠️  仍有 {remaining_issues} 个占位代码需要手动处理")
        return 1
    else:
        logger.info(f"\\n🎉 所有占位代码已清理完成！")
        return 0

if __name__ == "__main__":
    sys.exit(main())
