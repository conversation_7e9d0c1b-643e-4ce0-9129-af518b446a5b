#!/usr/bin/env python3
"""
系统状态监控脚本
"""

import sys
import os
import json
import time
import logging
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime, timedelta
import subprocess

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import config


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            import psutil
            
            # CPU信息
            cpu_info = {
                'count': psutil.cpu_count(),
                'usage_percent': psutil.cpu_percent(interval=1),
                'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None
            }
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_info = {
                'total_gb': round(memory.total / (1024**3), 2),
                'available_gb': round(memory.available / (1024**3), 2),
                'used_gb': round(memory.used / (1024**3), 2),
                'usage_percent': round(memory.percent, 1)
            }
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            disk_info = {
                'total_gb': round(disk.total / (1024**3), 2),
                'used_gb': round(disk.used / (1024**3), 2),
                'free_gb': round(disk.free / (1024**3), 2),
                'usage_percent': round((disk.used / disk.total) * 100, 1)
            }
            
            # 网络信息
            network = psutil.net_io_counters()
            network_info = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
            
            return {
                'timestamp': datetime.now().isoformat(),
                'cpu': cpu_info,
                'memory': memory_info,
                'disk': disk_info,
                'network': network_info,
                'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            return {'error': str(e)}
    
    def get_process_info(self) -> Dict[str, Any]:
        """获取进程信息"""
        try:
            import psutil
            
            processes = []
            
            # 查找相关进程
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    pinfo = proc.info
                    if any(keyword in pinfo['name'].lower() for keyword in ['python', 'uvicorn', 'celery']):
                        # 获取详细信息
                        process = psutil.Process(pinfo['pid'])
                        cmdline = ' '.join(process.cmdline())
                        
                        if 'trading' in cmdline.lower() or 'src.api' in cmdline:
                            processes.append({
                                'pid': pinfo['pid'],
                                'name': pinfo['name'],
                                'status': pinfo['status'],
                                'cpu_percent': round(pinfo['cpu_percent'], 1),
                                'memory_percent': round(pinfo['memory_percent'], 1),
                                'cmdline': cmdline,
                                'create_time': datetime.fromtimestamp(process.create_time()).isoformat()
                            })
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return {
                'timestamp': datetime.now().isoformat(),
                'processes': processes,
                'total_processes': len(processes)
            }
            
        except Exception as e:
            self.logger.error(f"获取进程信息失败: {e}")
            return {'error': str(e)}
    
    def get_database_status(self) -> Dict[str, Any]:
        """获取数据库状态"""
        try:
            from sqlalchemy import create_engine, text
            
            engine = create_engine(config.get_database_url())
            
            with engine.connect() as conn:
                # 数据库版本
                result = conn.execute(text("SELECT version()"))
                db_version = result.scalar()
                
                # 数据库大小
                result = conn.execute(text("""
                    SELECT pg_size_pretty(pg_database_size(current_database())) as size
                """))
                db_size = result.scalar()
                
                # 连接数
                result = conn.execute(text("""
                    SELECT count(*) as connections 
                    FROM pg_stat_activity 
                    WHERE datname = current_database()
                """))
                connections = result.scalar()
                
                # 表统计
                result = conn.execute(text("""
                    SELECT 
                        schemaname,
                        tablename,
                        n_tup_ins as inserts,
                        n_tup_upd as updates,
                        n_tup_del as deletes,
                        n_live_tup as live_tuples
                    FROM pg_stat_user_tables
                    ORDER BY n_live_tup DESC
                    LIMIT 10
                """))
                
                table_stats = [
                    {
                        'schema': row[0],
                        'table': row[1],
                        'inserts': row[2],
                        'updates': row[3],
                        'deletes': row[4],
                        'live_tuples': row[5]
                    }
                    for row in result
                ]
                
                # 最新数据时间
                result = conn.execute(text("""
                    SELECT MAX(timestamp) as latest_data
                    FROM market_data
                """))
                latest_data = result.scalar()
                
                return {
                    'timestamp': datetime.now().isoformat(),
                    'status': 'connected',
                    'version': db_version,
                    'size': db_size,
                    'connections': connections,
                    'table_stats': table_stats,
                    'latest_data': latest_data.isoformat() if latest_data else None
                }
                
        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e)
            }
    
    def get_redis_status(self) -> Dict[str, Any]:
        """获取Redis状态"""
        if not hasattr(config, 'cache') or not config.cache.redis_url:
            return {
                'timestamp': datetime.now().isoformat(),
                'status': 'not_configured'
            }
        
        try:
            import redis
            
            r = redis.from_url(config.cache.redis_url)
            info = r.info()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'status': 'connected',
                'version': info.get('redis_version'),
                'memory_used': info.get('used_memory_human'),
                'memory_peak': info.get('used_memory_peak_human'),
                'connected_clients': info.get('connected_clients'),
                'total_commands_processed': info.get('total_commands_processed'),
                'keyspace': {
                    db: info.get(f'db{db}', {})
                    for db in range(16)
                    if f'db{db}' in info
                }
            }
            
        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e)
            }
    
    def get_api_status(self) -> Dict[str, Any]:
        """获取API状态"""
        try:
            import requests
            
            url = f"http://{config.api.host}:{config.api.port}/health"
            start_time = time.time()
            
            response = requests.get(url, timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                return {
                    'timestamp': datetime.now().isoformat(),
                    'status': 'healthy',
                    'response_time': round(response_time, 3),
                    'status_code': response.status_code
                }
            else:
                return {
                    'timestamp': datetime.now().isoformat(),
                    'status': 'unhealthy',
                    'response_time': round(response_time, 3),
                    'status_code': response.status_code
                }
                
        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e)
            }
    
    def get_log_summary(self) -> Dict[str, Any]:
        """获取日志摘要"""
        try:
            log_file = Path(config.logging.file_path)
            
            if not log_file.exists():
                return {
                    'timestamp': datetime.now().isoformat(),
                    'status': 'no_log_file'
                }
            
            # 文件信息
            stat = log_file.stat()
            file_size = stat.st_size
            last_modified = datetime.fromtimestamp(stat.st_mtime)
            
            # 读取最后几行
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    recent_lines = lines[-10:] if len(lines) > 10 else lines
            except UnicodeDecodeError:
                recent_lines = ["无法读取日志文件（编码问题）"]
            
            # 统计日志级别
            level_counts = {'ERROR': 0, 'WARNING': 0, 'INFO': 0, 'DEBUG': 0}
            
            # 简单统计（仅最后1000行）
            check_lines = lines[-1000:] if len(lines) > 1000 else lines
            for line in check_lines:
                for level in level_counts:
                    if f' {level} ' in line:
                        level_counts[level] += 1
                        break
            
            return {
                'timestamp': datetime.now().isoformat(),
                'file_size_mb': round(file_size / (1024**2), 2),
                'last_modified': last_modified.isoformat(),
                'total_lines': len(lines),
                'level_counts': level_counts,
                'recent_lines': [line.strip() for line in recent_lines]
            }
            
        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e)
            }
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取系统服务状态"""
        try:
            # 检查systemd服务
            services = ['trading-system']
            service_status = {}
            
            for service in services:
                try:
                    result = subprocess.run(
                        ['systemctl', 'is-active', service],
                        capture_output=True,
                        text=True,
                        timeout=5
                    )
                    
                    status = result.stdout.strip()
                    service_status[service] = {
                        'status': status,
                        'active': status == 'active'
                    }
                    
                    # 获取详细信息
                    if status == 'active':
                        result = subprocess.run(
                            ['systemctl', 'show', service, '--property=MainPID,ActiveEnterTimestamp'],
                            capture_output=True,
                            text=True,
                            timeout=5
                        )
                        
                        for line in result.stdout.split('\n'):
                            if line.startswith('MainPID='):
                                service_status[service]['pid'] = line.split('=')[1]
                            elif line.startswith('ActiveEnterTimestamp='):
                                service_status[service]['start_time'] = line.split('=')[1]
                
                except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
                    service_status[service] = {
                        'status': 'unknown',
                        'active': False
                    }
            
            return {
                'timestamp': datetime.now().isoformat(),
                'services': service_status
            }
            
        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def get_comprehensive_status(self) -> Dict[str, Any]:
        """获取综合状态"""
        return {
            'timestamp': datetime.now().isoformat(),
            'environment': config.environment,
            'system': self.get_system_info(),
            'processes': self.get_process_info(),
            'database': self.get_database_status(),
            'redis': self.get_redis_status(),
            'api': self.get_api_status(),
            'logs': self.get_log_summary(),
            'services': self.get_service_status()
        }


def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    monitor = SystemMonitor()
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        component = sys.argv[1].lower()
        
        if component == 'system':
            status = monitor.get_system_info()
        elif component == 'processes':
            status = monitor.get_process_info()
        elif component == 'database':
            status = monitor.get_database_status()
        elif component == 'redis':
            status = monitor.get_redis_status()
        elif component == 'api':
            status = monitor.get_api_status()
        elif component == 'logs':
            status = monitor.get_log_summary()
        elif component == 'services':
            status = monitor.get_service_status()
        else:
            logger.info(f"未知组件: {component}")
            logger.info("可用组件: system, processes, database, redis, api, logs, services")
            sys.exit(1)
    else:
        # 获取综合状态
        status = monitor.get_comprehensive_status()
    
    # 输出结果
    logger.info(json.dumps(status, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()