import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
启动文件组织监控系统的便捷脚本

这个脚本提供了一个简单的方式来启动和管理文件组织监控系统。
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tools.file_organization.monitoring_system import FileOrganizationMonitoringSystem


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="启动文件组织监控系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s                           # 运行一次性检查
  %(prog)s --daemon                  # 启动守护进程监控
  %(prog)s --check-only              # 只运行检查，不启动监控
  %(prog)s --report                  # 生成监控报告
        """
    )
    
    parser.add_argument(
        '--daemon',
        action='store_true',
        help='以守护进程模式运行监控系统'
    )
    
    parser.add_argument(
        '--check-only',
        action='store_true',
        help='只运行一次检查，不启动持续监控'
    )
    
    parser.add_argument(
        '--report',
        action='store_true',
        help='生成监控报告'
    )
    
    parser.add_argument(
        '--project-root',
        default='.',
        help='项目根目录路径 (默认: 当前目录)'
    )
    
    args = parser.parse_args()
    
    logger.info("🚀 启动文件组织监控系统...")
    
    # 创建监控系统实例
    monitoring_system = FileOrganizationMonitoringSystem(args.project_root)
    
    try:
        if args.check_only:
            # 只运行检查
            logger.info("🔍 执行一次性检查...")
            
            violations = monitoring_system.run_compliance_check()
            metrics = monitoring_system.collect_storage_metrics()
            changes = monitoring_system.check_config_changes()
            
            logger.info(f"✅ 检查完成:")
            logger.info(f"  - 规范违规: {len(violations)} 个")
            logger.info(f"  - 存储使用率: {metrics.usage_percentage:.1f}%")
            logger.info(f"  - 配置变更: {len(changes)} 个")
            
        elif args.report:
            # 生成报告
            logger.info("📋 生成监控报告...")
            report = monitoring_system.generate_monitoring_report()
            
            logger.info("📊 报告摘要:")
            logger.info(f"  - 规范违规: {report['compliance_violations']['total']} 个")
            logger.info(f"  - 存储使用率: {report['storage_metrics']['usage_percentage']:.1f}%")
            logger.info(f"  - 配置变更: {report['config_changes']['count']} 个")
            logger.info(f"  - 目录变更: {report['directory_changes']['count']} 个")
            
        elif args.daemon:
            # 启动守护进程监控
            logger.info("📡 启动守护进程监控...")
            monitoring_system.start_monitoring()
            
            import time
            try:
                while True:
                    time.sleep(60)  # 每分钟检查一次
                    
                    # 定期执行检查
                    from datetime import datetime
                    if datetime.now().minute % 10 == 0:  # 每10分钟执行一次
                        monitoring_system.run_compliance_check()
                        monitoring_system.collect_storage_metrics()
                        monitoring_system.check_config_changes()
            except KeyboardInterrupt:
                logger.info("\n收到停止信号...")
            
        else:
            # 默认模式：启动监控并运行一段时间
            logger.info("⏱️  启动监控系统（运行30秒后自动停止）...")
            monitoring_system.start_monitoring()
            
            import time
            time.sleep(30)  # 运行30秒
            
            logger.info("📊 生成最终报告...")
            report = monitoring_system.generate_monitoring_report()
            
            logger.info("✅ 监控完成，报告摘要:")
            logger.info(f"  - 规范违规: {report['compliance_violations']['total']} 个")
            logger.info(f"  - 存储使用率: {report['storage_metrics']['usage_percentage']:.1f}%")
            logger.info(f"  - 配置变更: {report['config_changes']['count']} 个")
    
    except Exception as e:
        logger.info(f"❌ 监控系统运行时发生错误: {e}")
        sys.exit(1)
    
    finally:
        # 确保监控系统正确停止
        try:
            monitoring_system.stop_monitoring()
            logger.info("🛑 监控系统已停止")
        except:
            pass


if __name__ == "__main__":
    main()