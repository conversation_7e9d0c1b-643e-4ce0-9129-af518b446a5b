#!/usr/bin/env python3
"""
系统健康检查脚本
"""

import sys
import os
import time
import json
import logging
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from config.settings import config


class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.checks = []
        self.results = {}
        
        # 注册检查项
        self._register_checks()
    
    def _register_checks(self):
        """注册检查项"""
        self.checks = [
            self.check_database_connection,
            self.check_api_server,
            self.check_redis_connection,
            self.check_disk_space,
            self.check_memory_usage,
            self.check_cpu_usage,
            self.check_log_files,
            self.check_data_freshness
        ]
    
    def check_database_connection(self) -> Dict[str, Any]:
        """检查数据库连接"""
        try:
            from sqlalchemy import create_engine, text
            
            engine = create_engine(config.get_database_url())
            start_time = time.time()
            
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            
            response_time = time.time() - start_time
            
            return {
                'name': 'database_connection',
                'status': 'healthy',
                'response_time': response_time,
                'message': f'数据库连接正常 ({response_time:.3f}s)'
            }
            
        except Exception as e:
            return {
                'name': 'database_connection',
                'status': 'unhealthy',
                'error': str(e),
                'message': f'数据库连接失败: {e}'
            }
    
    def check_api_server(self) -> Dict[str, Any]:
        """检查API服务器"""
        try:
            url = f"http://{config.api.host}:{config.api.port}/health"
            start_time = time.time()
            
            response = requests.get(url, timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                return {
                    'name': 'api_server',
                    'status': 'healthy',
                    'response_time': response_time,
                    'message': f'API服务器正常 ({response_time:.3f}s)'
                }
            else:
                return {
                    'name': 'api_server',
                    'status': 'unhealthy',
                    'status_code': response.status_code,
                    'message': f'API服务器返回错误状态码: {response.status_code}'
                }
                
        except requests.exceptions.ConnectionError:
            return {
                'name': 'api_server',
                'status': 'unhealthy',
                'message': 'API服务器连接失败'
            }
        except Exception as e:
            return {
                'name': 'api_server',
                'status': 'unhealthy',
                'error': str(e),
                'message': f'API服务器检查失败: {e}'
            }
    
    def check_redis_connection(self) -> Dict[str, Any]:
        """检查Redis连接"""
        if not hasattr(config, 'cache') or not config.cache.redis_url:
            return {
                'name': 'redis_connection',
                'status': 'skipped',
                'message': 'Redis未配置'
            }
        
        try:
            import redis
            
            r = redis.from_url(config.cache.redis_url)
            start_time = time.time()
            
            r.ping()
            response_time = time.time() - start_time
            
            # 获取Redis信息
            info = r.info()
            memory_usage = info.get('used_memory_human', 'unknown')
            
            return {
                'name': 'redis_connection',
                'status': 'healthy',
                'response_time': response_time,
                'memory_usage': memory_usage,
                'message': f'Redis连接正常 ({response_time:.3f}s, 内存: {memory_usage})'
            }
            
        except Exception as e:
            return {
                'name': 'redis_connection',
                'status': 'unhealthy',
                'error': str(e),
                'message': f'Redis连接失败: {e}'
            }
    
    def check_disk_space(self) -> Dict[str, Any]:
        """检查磁盘空间"""
        try:
            import shutil
            
            # 检查项目根目录的磁盘空间
            total, used, free = shutil.disk_usage(project_root)
            
            # 转换为GB
            total_gb = total / (1024**3)
            used_gb = used / (1024**3)
            free_gb = free / (1024**3)
            usage_percent = (used / total) * 100
            
            # 判断健康状态
            if usage_percent > 90:
                status = 'critical'
                message = f'磁盘空间严重不足: {usage_percent:.1f}% 已使用'
            elif usage_percent > 80:
                status = 'warning'
                message = f'磁盘空间不足: {usage_percent:.1f}% 已使用'
            else:
                status = 'healthy'
                message = f'磁盘空间正常: {usage_percent:.1f}% 已使用'
            
            return {
                'name': 'disk_space',
                'status': status,
                'total_gb': round(total_gb, 2),
                'used_gb': round(used_gb, 2),
                'free_gb': round(free_gb, 2),
                'usage_percent': round(usage_percent, 1),
                'message': message
            }
            
        except Exception as e:
            return {
                'name': 'disk_space',
                'status': 'unhealthy',
                'error': str(e),
                'message': f'磁盘空间检查失败: {e}'
            }
    
    def check_memory_usage(self) -> Dict[str, Any]:
        """检查内存使用情况"""
        try:
            import psutil
            
            memory = psutil.virtual_memory()
            usage_percent = memory.percent
            
            # 判断健康状态
            if usage_percent > 90:
                status = 'critical'
                message = f'内存使用率过高: {usage_percent:.1f}%'
            elif usage_percent > 80:
                status = 'warning'
                message = f'内存使用率较高: {usage_percent:.1f}%'
            else:
                status = 'healthy'
                message = f'内存使用率正常: {usage_percent:.1f}%'
            
            return {
                'name': 'memory_usage',
                'status': status,
                'total_gb': round(memory.total / (1024**3), 2),
                'available_gb': round(memory.available / (1024**3), 2),
                'usage_percent': round(usage_percent, 1),
                'message': message
            }
            
        except Exception as e:
            return {
                'name': 'memory_usage',
                'status': 'unhealthy',
                'error': str(e),
                'message': f'内存使用检查失败: {e}'
            }
    
    def check_cpu_usage(self) -> Dict[str, Any]:
        """检查CPU使用情况"""
        try:
            import psutil
            
            # 获取1秒内的CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # 判断健康状态
            if cpu_percent > 90:
                status = 'critical'
                message = f'CPU使用率过高: {cpu_percent:.1f}%'
            elif cpu_percent > 80:
                status = 'warning'
                message = f'CPU使用率较高: {cpu_percent:.1f}%'
            else:
                status = 'healthy'
                message = f'CPU使用率正常: {cpu_percent:.1f}%'
            
            return {
                'name': 'cpu_usage',
                'status': status,
                'cpu_count': cpu_count,
                'usage_percent': round(cpu_percent, 1),
                'message': message
            }
            
        except Exception as e:
            return {
                'name': 'cpu_usage',
                'status': 'unhealthy',
                'error': str(e),
                'message': f'CPU使用检查失败: {e}'
            }
    
    def check_log_files(self) -> Dict[str, Any]:
        """检查日志文件"""
        try:
            log_file = Path(config.logging.file_path)
            
            if not log_file.exists():
                return {
                    'name': 'log_files',
                    'status': 'warning',
                    'message': '日志文件不存在'
                }
            
            # 检查日志文件大小
            file_size = log_file.stat().st_size
            file_size_mb = file_size / (1024**2)
            
            # 检查最后修改时间
            last_modified = datetime.fromtimestamp(log_file.stat().st_mtime)
            time_since_modified = datetime.now() - last_modified
            
            # 判断健康状态
            if file_size_mb > 100:  # 大于100MB
                status = 'warning'
                message = f'日志文件过大: {file_size_mb:.1f}MB'
            elif time_since_modified > timedelta(hours=1):  # 超过1小时未更新
                status = 'warning'
                message = f'日志文件长时间未更新: {time_since_modified}'
            else:
                status = 'healthy'
                message = f'日志文件正常: {file_size_mb:.1f}MB'
            
            return {
                'name': 'log_files',
                'status': status,
                'file_size_mb': round(file_size_mb, 1),
                'last_modified': last_modified.isoformat(),
                'message': message
            }
            
        except Exception as e:
            return {
                'name': 'log_files',
                'status': 'unhealthy',
                'error': str(e),
                'message': f'日志文件检查失败: {e}'
            }
    
    def check_data_freshness(self) -> Dict[str, Any]:
        """检查数据新鲜度"""
        try:
            from sqlalchemy import create_engine, text
            
            engine = create_engine(config.get_database_url())
            
            with engine.connect() as conn:
                # 检查最新的市场数据时间
                result = conn.execute(text(
                    "SELECT MAX(timestamp) as latest_timestamp FROM market_data"
                ))
                row = result.fetchone()
                
                if row and row[0]:
                    latest_timestamp = row[0]
                    time_since_update = datetime.now(latest_timestamp.tzinfo) - latest_timestamp
                    
                    # 判断数据新鲜度
                    if time_since_update > timedelta(days=7):
                        status = 'critical'
                        message = f'数据严重过期: {time_since_update.days} 天前'
                    elif time_since_update > timedelta(days=1):
                        status = 'warning'
                        message = f'数据较旧: {time_since_update.days} 天前'
                    else:
                        status = 'healthy'
                        message = f'数据较新: {time_since_update.seconds // 3600} 小时前'
                    
                    return {
                        'name': 'data_freshness',
                        'status': status,
                        'latest_timestamp': latest_timestamp.isoformat(),
                        'hours_since_update': round(time_since_update.total_seconds() / 3600, 1),
                        'message': message
                    }
                else:
                    return {
                        'name': 'data_freshness',
                        'status': 'warning',
                        'message': '没有找到市场数据'
                    }
                    
        except Exception as e:
            return {
                'name': 'data_freshness',
                'status': 'unhealthy',
                'error': str(e),
                'message': f'数据新鲜度检查失败: {e}'
            }
    
    def run_all_checks(self) -> Dict[str, Any]:
        """运行所有检查"""
        self.logger.info("开始系统健康检查...")
        
        results = []
        overall_status = 'healthy'
        
        for check_func in self.checks:
            try:
                result = check_func()
                results.append(result)
                
                # 更新整体状态
                if result['status'] == 'critical':
                    overall_status = 'critical'
                elif result['status'] in ['unhealthy', 'warning'] and overall_status == 'healthy':
                    overall_status = 'warning'
                
                self.logger.info(f"检查 {result['name']}: {result['status']} - {result['message']}")
                
            except Exception as e:
                error_result = {
                    'name': check_func.__name__,
                    'status': 'error',
                    'error': str(e),
                    'message': f'检查执行失败: {e}'
                }
                results.append(error_result)
                overall_status = 'critical'
                self.logger.error(f"检查 {check_func.__name__} 执行失败: {e}")
        
        # 汇总结果
        summary = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': overall_status,
            'environment': config.environment,
            'checks': results,
            'summary': {
                'total': len(results),
                'healthy': len([r for r in results if r['status'] == 'healthy']),
                'warning': len([r for r in results if r['status'] == 'warning']),
                'unhealthy': len([r for r in results if r['status'] == 'unhealthy']),
                'critical': len([r for r in results if r['status'] == 'critical']),
                'error': len([r for r in results if r['status'] == 'error'])
            }
        }
        
        self.logger.info(f"健康检查完成 - 整体状态: {overall_status}")
        return summary
    
    def save_results(self, results: Dict[str, Any], output_file: Optional[str] = None):
        """保存检查结果"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"health_check_{timestamp}.json"
        
        output_path = Path(output_file)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"健康检查结果已保存到: {output_path}")


def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    checker = HealthChecker()
    results = checker.run_all_checks()
    
    # 输出结果
    logger.info(json.dumps(results, indent=2, ensure_ascii=False))
    
    # 保存结果
    if len(sys.argv) > 1:
        checker.save_results(results, sys.argv[1])
    
    # 根据整体状态设置退出码
    if results['overall_status'] == 'critical':
        sys.exit(2)
    elif results['overall_status'] in ['warning', 'unhealthy']:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()