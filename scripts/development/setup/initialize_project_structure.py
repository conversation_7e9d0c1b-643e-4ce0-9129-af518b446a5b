#!/usr/bin/env python3
"""
项目结构初始化脚本

此脚本用于创建标准的项目目录结构，设置适当的权限，
并支持不同类型项目的结构初始化。
"""

import os
import sys
import json
import stat
import argparse
from pathlib import Path
from typing import Dict, List, Optional
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/project_structure_init.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class ProjectStructureInitializer:
    """项目结构初始化器"""
    
    def __init__(self, project_root: str = None):
        """
        初始化项目结构初始化器
        
        Args:
            project_root: 项目根目录路径，默认为当前目录
        """
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.templates_dir = Path(__file__).parent / "templates"
        
    def load_template(self, template_name: str) -> Dict:
        """
        加载项目结构模板
        
        Args:
            template_name: 模板名称
            
        Returns:
            模板配置字典
        """
        template_file = self.templates_dir / f"{template_name}.json"
        
        if not template_file.exists():
            logger.error(f"模板文件不存在: {template_file}")
            raise FileNotFoundError(f"模板文件不存在: {template_file}")
            
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                template = json.load(f)
            logger.info(f"成功加载模板: {template_name}")
            return template
        except json.JSONDecodeError as e:
            logger.error(f"模板文件格式错误: {e}")
            raise
        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            raise
    
    def create_directory_structure(self, structure: Dict, base_path: Path = None) -> None:
        """
        创建目录结构
        
        Args:
            structure: 目录结构配置
            base_path: 基础路径，默认为项目根目录
        """
        if base_path is None:
            base_path = self.project_root
            
        directories = structure.get('directories', [])
        
        for dir_config in directories:
            dir_path = base_path / dir_config['path']
            
            # 创建目录
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建目录: {dir_path}")
                
                # 设置目录权限
                if 'permissions' in dir_config:
                    self.set_directory_permissions(dir_path, dir_config['permissions'])
                
                # 创建描述文件
                if dir_config.get('create_readme', False):
                    self.create_directory_readme(dir_path, dir_config)
                
                # 创建.gitkeep文件（如果目录为空）
                if dir_config.get('create_gitkeep', False):
                    gitkeep_file = dir_path / '.gitkeep'
                    if not any(dir_path.iterdir()):
                        gitkeep_file.touch()
                        logger.info(f"创建.gitkeep文件: {gitkeep_file}")
                        
            except Exception as e:
                logger.error(f"创建目录失败 {dir_path}: {e}")
                raise
    
    def set_directory_permissions(self, dir_path: Path, permissions: str) -> None:
        """
        设置目录权限
        
        Args:
            dir_path: 目录路径
            permissions: 权限字符串（如 '755', '644'）
        """
        try:
            # 转换权限字符串为八进制数
            perm_octal = int(permissions, 8)
            os.chmod(dir_path, perm_octal)
            logger.info(f"设置目录权限 {dir_path}: {permissions}")
        except Exception as e:
            logger.error(f"设置目录权限失败 {dir_path}: {e}")
            # 权限设置失败不应该中断整个过程
    
    def create_directory_readme(self, dir_path: Path, dir_config: Dict) -> None:
        """
        创建目录说明文件
        
        Args:
            dir_path: 目录路径
            dir_config: 目录配置
        """
        readme_file = dir_path / 'README.md'
        
        if readme_file.exists():
            logger.info(f"README文件已存在，跳过: {readme_file}")
            return
            
        content = f"""# {dir_config.get('name', dir_path.name)}

## 目录用途

{dir_config.get('description', '此目录用于存放相关文件。')}

## 文件组织规范

{dir_config.get('organization_rules', '请按照项目文件组织规范放置文件。')}

## 注意事项

{dir_config.get('notes', '请保持目录结构整洁，遵循命名规范。')}
"""
        
        try:
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"创建README文件: {readme_file}")
        except Exception as e:
            logger.error(f"创建README文件失败 {readme_file}: {e}")
    
    def create_initial_files(self, files_config: List[Dict]) -> None:
        """
        创建初始文件
        
        Args:
            files_config: 文件配置列表
        """
        for file_config in files_config:
            file_path = self.project_root / file_config['path']
            
            # 确保父目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 如果文件已存在且不允许覆盖，跳过
            if file_path.exists() and not file_config.get('overwrite', False):
                logger.info(f"文件已存在，跳过: {file_path}")
                continue
                
            try:
                content = file_config.get('content', '')
                
                # 如果指定了模板文件，从模板加载内容
                if 'template_file' in file_config:
                    template_path = self.templates_dir / file_config['template_file']
                    if template_path.exists():
                        with open(template_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                # 设置文件权限
                if 'permissions' in file_config:
                    perm_octal = int(file_config['permissions'], 8)
                    os.chmod(file_path, perm_octal)
                    
                logger.info(f"创建文件: {file_path}")
                
            except Exception as e:
                logger.error(f"创建文件失败 {file_path}: {e}")
                raise
    
    def validate_structure(self, template: Dict) -> bool:
        """
        验证项目结构完整性
        
        Args:
            template: 项目模板配置
            
        Returns:
            验证是否通过
        """
        logger.info("开始验证项目结构完整性...")
        
        validation_passed = True
        required_dirs = template.get('validation', {}).get('required_directories', [])
        
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if not full_path.exists():
                logger.error(f"必需目录不存在: {full_path}")
                validation_passed = False
            elif not full_path.is_dir():
                logger.error(f"路径不是目录: {full_path}")
                validation_passed = False
            else:
                logger.info(f"验证通过: {full_path}")
        
        if validation_passed:
            logger.info("✅ 项目结构验证通过")
        else:
            logger.error("❌ 项目结构验证失败")
            
        return validation_passed
    
    def initialize_project(self, template_name: str = 'quantitative_trading') -> bool:
        """
        初始化项目结构
        
        Args:
            template_name: 模板名称
            
        Returns:
            初始化是否成功
        """
        try:
            logger.info(f"开始初始化项目结构，使用模板: {template_name}")
            
            # 加载模板
            template = self.load_template(template_name)
            
            # 创建目录结构
            self.create_directory_structure(template)
            
            # 创建初始文件
            if 'initial_files' in template:
                self.create_initial_files(template['initial_files'])
            
            # 验证结构
            if not self.validate_structure(template):
                logger.error("项目结构验证失败")
                return False
            
            logger.info("✅ 项目结构初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"项目结构初始化失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='项目结构初始化脚本')
    parser.add_argument(
        '--template', 
        default='quantitative_trading',
        help='项目模板名称 (默认: quantitative_trading)'
    )
    parser.add_argument(
        '--project-root',
        help='项目根目录路径 (默认: 当前目录)'
    )
    parser.add_argument(
        '--list-templates',
        action='store_true',
        help='列出可用的项目模板'
    )
    
    args = parser.parse_args()
    
    # 确保logs目录存在
    logs_dir = Path('logs')
    logs_dir.mkdir(exist_ok=True)
    
    initializer = ProjectStructureInitializer(args.project_root)
    
    if args.list_templates:
        # 列出可用模板
        templates_dir = initializer.templates_dir
        if templates_dir.exists():
            templates = [f.stem for f in templates_dir.glob('*.json')]
            logger.info("可用的项目模板:")
            for template in templates:
                logger.info(f"  - {template}")
        else:
            logger.info("未找到模板目录")
        return
    
    # 初始化项目结构
    success = initializer.initialize_project(args.template)
    
    if success:
        logger.info("✅ 项目结构初始化成功")
        sys.exit(0)
    else:
        logger.info("❌ 项目结构初始化失败")
        sys.exit(1)


if __name__ == '__main__':
    main()