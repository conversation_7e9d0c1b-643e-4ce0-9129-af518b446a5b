#!/usr/bin/env python3
"""
数据库迁移脚本
"""

import sys
import os
import logging
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text, MetaData, Table, Column, String, DateTime
from sqlalchemy.exc import SQLAlchemyError

from config.settings import config


class Migration:
    """迁移基类"""
    
    def __init__(self, version: str, description: str):
        self.version = version
        self.description = description
        self.timestamp = datetime.now()
    
    def up(self, engine):
        """执行迁移"""
        raise NotImplementedError
    
    def down(self, engine):
        """回滚迁移"""
        raise NotImplementedError


class Migration_001_AddIndexes(Migration):
    """添加性能优化索引"""
    
    def __init__(self):
        super().__init__("001", "添加性能优化索引")
    
    def up(self, engine):
        sql = """
        -- 添加复合索引优化查询性能
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_market_data_symbol_market_timestamp 
        ON market_data (symbol, market, timestamp DESC);
        
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trades_backtest_symbol_timestamp 
        ON trades (backtest_id, symbol, timestamp);
        
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_portfolio_history_backtest_timestamp 
        ON portfolio_history (backtest_id, timestamp);
        """
        
        with engine.connect() as conn:
            for stmt in sql.split(';'):
                if stmt.strip():
                    conn.execute(text(stmt))
            conn.commit()
    
    def down(self, engine):
        sql = """
        DROP INDEX IF EXISTS idx_market_data_symbol_market_timestamp;
        DROP INDEX IF EXISTS idx_trades_backtest_symbol_timestamp;
        DROP INDEX IF EXISTS idx_portfolio_history_backtest_timestamp;
        """
        
        with engine.connect() as conn:
            for stmt in sql.split(';'):
                if stmt.strip():
                    conn.execute(text(stmt))
            conn.commit()


class Migration_002_AddCacheTable(Migration):
    """添加缓存表"""
    
    def __init__(self):
        super().__init__("002", "添加缓存表")
    
    def up(self, engine):
        sql = """
        CREATE TABLE IF NOT EXISTS cache_entries (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            cache_key VARCHAR(500) NOT NULL UNIQUE,
            cache_value JSONB NOT NULL,
            expires_at TIMESTAMPTZ,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_cache_entries_key ON cache_entries (cache_key);
        CREATE INDEX IF NOT EXISTS idx_cache_entries_expires ON cache_entries (expires_at);
        
        CREATE TRIGGER update_cache_entries_updated_at BEFORE UPDATE ON cache_entries 
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        """
        
        with engine.connect() as conn:
            conn.execute(text(sql))
            conn.commit()
    
    def down(self, engine):
        sql = "DROP TABLE IF EXISTS cache_entries CASCADE;"
        
        with engine.connect() as conn:
            conn.execute(text(sql))
            conn.commit()


class Migration_003_AddAuditLog(Migration):
    """添加审计日志表"""
    
    def __init__(self):
        super().__init__("003", "添加审计日志表")
    
    def up(self, engine):
        sql = """
        CREATE TABLE IF NOT EXISTS audit_logs (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES users(id),
            action VARCHAR(100) NOT NULL,
            resource_type VARCHAR(100) NOT NULL,
            resource_id VARCHAR(100),
            old_values JSONB,
            new_values JSONB,
            ip_address INET,
            user_agent TEXT,
            timestamp TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs (user_id);
        CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs (timestamp);
        CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs (action);
        CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs (resource_type, resource_id);
        """
        
        with engine.connect() as conn:
            conn.execute(text(sql))
            conn.commit()
    
    def down(self, engine):
        sql = "DROP TABLE IF EXISTS audit_logs CASCADE;"
        
        with engine.connect() as conn:
            conn.execute(text(sql))
            conn.commit()


class MigrationManager:
    """迁移管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.engine = create_engine(config.get_database_url())
        self.migrations = [
            Migration_001_AddIndexes(),
            Migration_002_AddCacheTable(),
            Migration_003_AddAuditLog(),
        ]
    
    def setup_migration_table(self):
        """创建迁移记录表"""
        sql = """
        CREATE TABLE IF NOT EXISTS schema_migrations (
            version VARCHAR(50) PRIMARY KEY,
            description TEXT,
            applied_at TIMESTAMPTZ DEFAULT NOW()
        );
        """
        
        with self.engine.connect() as conn:
            conn.execute(text(sql))
            conn.commit()
    
    def get_applied_migrations(self) -> List[str]:
        """获取已应用的迁移"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT version FROM schema_migrations ORDER BY version"))
                return [row[0] for row in result]
        except SQLAlchemyError:
            return []
    
    def apply_migration(self, migration: Migration):
        """应用迁移"""
        try:
            self.logger.info(f"应用迁移 {migration.version}: {migration.description}")
            
            # 执行迁移
            migration.up(self.engine)
            
            # 记录迁移
            with self.engine.connect() as conn:
                conn.execute(text(
                    "INSERT INTO schema_migrations (version, description) VALUES (:version, :description)"
                ), {"version": migration.version, "description": migration.description})
                conn.commit()
            
            self.logger.info(f"迁移 {migration.version} 应用成功")
            
        except Exception as e:
            self.logger.error(f"迁移 {migration.version} 应用失败: {e}")
            raise
    
    def rollback_migration(self, migration: Migration):
        """回滚迁移"""
        try:
            self.logger.info(f"回滚迁移 {migration.version}: {migration.description}")
            
            # 执行回滚
            migration.down(self.engine)
            
            # 删除迁移记录
            with self.engine.connect() as conn:
                conn.execute(text(
                    "DELETE FROM schema_migrations WHERE version = :version"
                ), {"version": migration.version})
                conn.commit()
            
            self.logger.info(f"迁移 {migration.version} 回滚成功")
            
        except Exception as e:
            self.logger.error(f"迁移 {migration.version} 回滚失败: {e}")
            raise
    
    def migrate(self):
        """执行所有待应用的迁移"""
        self.setup_migration_table()
        applied_migrations = self.get_applied_migrations()
        
        for migration in self.migrations:
            if migration.version not in applied_migrations:
                self.apply_migration(migration)
            else:
                self.logger.info(f"迁移 {migration.version} 已应用，跳过")
    
    def rollback(self, target_version: str = None):
        """回滚到指定版本"""
        self.setup_migration_table()
        applied_migrations = self.get_applied_migrations()
        
        # 按版本倒序回滚
        for migration in reversed(self.migrations):
            if migration.version in applied_migrations:
                if target_version and migration.version <= target_version:
                    break
                self.rollback_migration(migration)
    
    def status(self):
        """显示迁移状态"""
        self.setup_migration_table()
        applied_migrations = self.get_applied_migrations()
        
        logger.info("迁移状态:")
        logger.info("-" * 60)
        
        for migration in self.migrations:
            status = "已应用" if migration.version in applied_migrations else "待应用"
            logger.info(f"{migration.version:10} {status:10} {migration.description}")


def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    manager = MigrationManager()
    
    if len(sys.argv) < 2:
        logger.info("用法: python migrate.py [migrate|rollback|status] [version]")
        sys.exit(1)
    
    command = sys.argv[1]
    
    try:
        if command == "migrate":
            manager.migrate()
        elif command == "rollback":
            target_version = sys.argv[2] if len(sys.argv) > 2 else None
            manager.rollback(target_version)
        elif command == "status":
            manager.status()
        else:
            logger.info(f"未知命令: {command}")
            sys.exit(1)
    
    except Exception as e:
        logging.error(f"执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()