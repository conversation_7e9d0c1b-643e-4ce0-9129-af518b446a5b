import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
量化交易系统完整测试套件

本脚本提供完整的系统测试功能，包括：
1. 单元测试 - 验证各个模块的基本功能
2. 集成测试 - 验证模块间的协作
3. 端到端测试 - 验证完整的业务流程
4. 性能测试 - 验证系统性能指标
5. 启动流程测试 - 验证系统启动和配置
6. 健康检查 - 验证系统运行状态

使用方法:
    python scripts/startup/run_complete_system_test.py [选项]

作者: 量化交易系统开发团队
版本: 3.0.0
创建日期: 2025-08-07
"""

import os
import sys
import subprocess
import argparse
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 颜色定义
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

def log_info(message: str):
    """输出信息日志"""
    logger.info(f"{Colors.BLUE}[信息]{Colors.NC} {message}")

def log_success(message: str):
    """输出成功日志"""
    logger.info(f"{Colors.GREEN}[成功]{Colors.NC} {message}")

def log_warning(message: str):
    """输出警告日志"""
    logger.info(f"{Colors.YELLOW}[警告]{Colors.NC} {message}")

def log_error(message: str):
    """输出错误日志"""
    logger.info(f"{Colors.RED}[错误]{Colors.NC} {message}")

def log_test(message: str):
    """输出测试日志"""
    logger.info(f"{Colors.PURPLE}[测试]{Colors.NC} {message}")

class SystemTestRunner:
    """系统测试运行器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
    def run_complete_test_suite(self, 
                              test_types: List[str] = None,
                              verbose: bool = False,
                              generate_report: bool = True,
                              quick_mode: bool = False) -> bool:
        """运行完整的测试套件"""
        
        self.start_time = datetime.now()
        
        logger.info("=" * 80)
        logger.info(f"{Colors.CYAN}🧪 量化交易系统 - 完整测试套件 v3.0.0{Colors.NC}")
        logger.info("=" * 80)
        logger.info(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"项目根目录: {self.project_root}")
        logger.info()
        
        # 默认测试类型
        if test_types is None:
            test_types = ['comprehensive', 'unit', 'integration', 'startup', 'health']
            if not quick_mode:
                test_types.extend(['performance', 'e2e'])
        
        overall_success = True
        
        # 1. 运行综合系统测试
        if 'comprehensive' in test_types:
            success = self._run_comprehensive_test(verbose)
            overall_success = overall_success and success
        
        # 2. 运行单元测试
        if 'unit' in test_types:
            success = self._run_unit_tests(verbose)
            overall_success = overall_success and success
        
        # 3. 运行集成测试
        if 'integration' in test_types:
            success = self._run_integration_tests(verbose)
            overall_success = overall_success and success
        
        # 4. 运行启动流程测试
        if 'startup' in test_types:
            success = self._run_startup_tests(verbose)
            overall_success = overall_success and success
        
        # 5. 运行健康检查
        if 'health' in test_types:
            success = self._run_health_checks(verbose)
            overall_success = overall_success and success
        
        # 6. 运行性能测试（非快速模式）
        if 'performance' in test_types and not quick_mode:
            success = self._run_performance_tests(verbose)
            overall_success = overall_success and success
        
        # 7. 运行端到端测试（非快速模式）
        if 'e2e' in test_types and not quick_mode:
            success = self._run_e2e_tests(verbose)
            overall_success = overall_success and success
        
        self.end_time = datetime.now()
        
        # 生成测试报告
        if generate_report:
            self._generate_test_report()
        
        # 打印测试摘要
        self._print_test_summary(overall_success)
        
        return overall_success
    
    def _run_comprehensive_test(self, verbose: bool = False) -> bool:
        """运行综合系统测试"""
        log_test("运行综合系统测试...")
        logger.info("-" * 60)
        
        test_file = self.project_root / "tests" / "comprehensive_system_test.py"
        
        if not test_file.exists():
            log_error(f"综合测试文件不存在: {test_file}")
            return False
        
        try:
            start_time = time.time()
            result = subprocess.run(
                [sys.executable, str(test_file)],
                cwd=str(self.project_root),
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            execution_time = time.time() - start_time
            
            self.test_results['comprehensive'] = {
                'success': result.returncode == 0,
                'execution_time': execution_time,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            if result.returncode == 0:
                log_success(f"综合系统测试通过 (耗时: {execution_time:.2f}秒)")
                self.passed_tests += 1
            else:
                log_error(f"综合系统测试失败 (耗时: {execution_time:.2f}秒)")
                if verbose:
                    logger.info(result.stdout)
                    logger.info(result.stderr)
                self.failed_tests += 1
            
            self.total_tests += 1
            logger.info()
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            log_error("综合系统测试超时")
            self.failed_tests += 1
            self.total_tests += 1
            return False
        except Exception as e:
            log_error(f"综合系统测试执行错误: {e}")
            self.failed_tests += 1
            self.total_tests += 1
            return False
    
    def _run_unit_tests(self, verbose: bool = False) -> bool:
        """运行单元测试"""
        log_test("运行单元测试...")
        logger.info("-" * 60)
        
        test_dir = self.project_root / "tests" / "unit"
        
        if not test_dir.exists():
            log_warning("单元测试目录不存在，跳过单元测试")
            return True
        
        return self._run_pytest_tests(test_dir, "单元测试", verbose)
    
    def _run_integration_tests(self, verbose: bool = False) -> bool:
        """运行集成测试"""
        log_test("运行集成测试...")
        logger.info("-" * 60)
        
        test_dir = self.project_root / "tests" / "integration"
        
        if not test_dir.exists():
            log_warning("集成测试目录不存在，跳过集成测试")
            return True
        
        return self._run_pytest_tests(test_dir, "集成测试", verbose)
    
    def _run_startup_tests(self, verbose: bool = False) -> bool:
        """运行启动流程测试"""
        log_test("运行启动流程测试...")
        logger.info("-" * 60)
        
        # 测试启动脚本的各个组件
        startup_tests = [
            self._test_virtual_environment,
            self._test_dependencies,
            self._test_configuration_files,
            self._test_directory_structure
        ]
        
        all_passed = True
        
        for test_func in startup_tests:
            try:
                success = test_func()
                if not success:
                    all_passed = False
            except Exception as e:
                log_error(f"启动测试执行错误: {e}")
                all_passed = False
        
        self.test_results['startup'] = {
            'success': all_passed,
            'execution_time': 0,
            'tests_run': len(startup_tests)
        }
        
        if all_passed:
            log_success("启动流程测试通过")
            self.passed_tests += 1
        else:
            log_error("启动流程测试失败")
            self.failed_tests += 1
        
        self.total_tests += 1
        logger.info()
        return all_passed
    
    def _run_health_checks(self, verbose: bool = False) -> bool:
        """运行健康检查"""
        log_test("运行系统健康检查...")
        logger.info("-" * 60)
        
        health_checks = [
            self._check_python_environment,
            self._check_project_structure,
            self._check_import_paths,
            self._check_configuration_validity
        ]
        
        all_passed = True
        
        for check_func in health_checks:
            try:
                success = check_func()
                if not success:
                    all_passed = False
            except Exception as e:
                log_error(f"健康检查执行错误: {e}")
                all_passed = False
        
        self.test_results['health'] = {
            'success': all_passed,
            'execution_time': 0,
            'checks_run': len(health_checks)
        }
        
        if all_passed:
            log_success("系统健康检查通过")
            self.passed_tests += 1
        else:
            log_error("系统健康检查失败")
            self.failed_tests += 1
        
        self.total_tests += 1
        logger.info()
        return all_passed
    
    def _run_performance_tests(self, verbose: bool = False) -> bool:
        """运行性能测试"""
        log_test("运行性能测试...")
        logger.info("-" * 60)
        
        test_dir = self.project_root / "tests" / "performance"
        
        if not test_dir.exists():
            log_warning("性能测试目录不存在，跳过性能测试")
            return True
        
        return self._run_pytest_tests(test_dir, "性能测试", verbose, timeout=600)
    
    def _run_e2e_tests(self, verbose: bool = False) -> bool:
        """运行端到端测试"""
        log_test("运行端到端测试...")
        logger.info("-" * 60)
        
        e2e_runner = self.project_root / "tests" / "end_to_end" / "run_e2e_tests.py"
        
        if not e2e_runner.exists():
            log_warning("端到端测试运行器不存在，跳过端到端测试")
            return True
        
        try:
            start_time = time.time()
            result = subprocess.run(
                [sys.executable, str(e2e_runner), '--quick'],
                cwd=str(self.project_root),
                capture_output=True,
                text=True,
                timeout=1800  # 30分钟超时
            )
            
            execution_time = time.time() - start_time
            
            self.test_results['e2e'] = {
                'success': result.returncode == 0,
                'execution_time': execution_time,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            if result.returncode == 0:
                log_success(f"端到端测试通过 (耗时: {execution_time:.2f}秒)")
                self.passed_tests += 1
            else:
                log_error(f"端到端测试失败 (耗时: {execution_time:.2f}秒)")
                if verbose:
                    logger.info(result.stdout)
                    logger.info(result.stderr)
                self.failed_tests += 1
            
            self.total_tests += 1
            logger.info()
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            log_error("端到端测试超时")
            self.failed_tests += 1
            self.total_tests += 1
            return False
        except Exception as e:
            log_error(f"端到端测试执行错误: {e}")
            self.failed_tests += 1
            self.total_tests += 1
            return False
    
    def _run_pytest_tests(self, test_dir: Path, test_name: str, verbose: bool = False, timeout: int = 300) -> bool:
        """运行pytest测试"""
        try:
            cmd = [
                sys.executable, '-m', 'pytest',
                str(test_dir),
                '-v' if verbose else '-q',
                '--tb=short',
                '--durations=10'
            ]
            
            # 添加覆盖率报告（如果可用）
            try:
                import pytest_cov
                cmd.extend(['--cov=src', '--cov-report=term-missing'])
            except ImportError:
                pass
            
            start_time = time.time()
            result = subprocess.run(
                cmd,
                cwd=str(self.project_root),
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            execution_time = time.time() - start_time
            
            test_key = test_name.lower().replace(' ', '_')
            self.test_results[test_key] = {
                'success': result.returncode == 0,
                'execution_time': execution_time,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
            if result.returncode == 0:
                log_success(f"{test_name}通过 (耗时: {execution_time:.2f}秒)")
                self.passed_tests += 1
            else:
                log_error(f"{test_name}失败 (耗时: {execution_time:.2f}秒)")
                if verbose:
                    logger.info(result.stdout)
                    logger.info(result.stderr)
                self.failed_tests += 1
            
            self.total_tests += 1
            logger.info()
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            log_error(f"{test_name}超时")
            self.failed_tests += 1
            self.total_tests += 1
            return False
        except Exception as e:
            log_error(f"{test_name}执行错误: {e}")
            self.failed_tests += 1
            self.total_tests += 1
            return False
    
    def _test_virtual_environment(self) -> bool:
        """测试虚拟环境配置"""
        log_info("检查虚拟环境配置...")
        
        venv_path = self.project_root / "web_ui" / "backend" / "venv"
        
        if not venv_path.exists():
            log_warning("虚拟环境不存在，这在某些配置下是正常的")
            return True
        
        # 检查虚拟环境结构
        activate_script = venv_path / "bin" / "activate"
        if not activate_script.exists():
            log_error("虚拟环境激活脚本不存在")
            return False
        
        log_success("虚拟环境配置正确")
        return True
    
    def _test_dependencies(self) -> bool:
        """测试依赖配置"""
        log_info("检查依赖配置...")
        
        # 检查Python依赖文件
        requirements_files = [
            self.project_root / "requirements.txt",
            self.project_root / "web_ui" / "backend" / "requirements.txt"
        ]
        
        found_requirements = False
        for req_file in requirements_files:
            if req_file.exists():
                found_requirements = True
                log_success(f"找到依赖文件: {req_file.relative_to(self.project_root)}")
        
        if not found_requirements:
            log_error("未找到Python依赖文件")
            return False
        
        # 检查Node.js依赖文件
        package_json = self.project_root / "web_ui" / "frontend" / "package.json"
        if package_json.exists():
            log_success(f"找到Node.js依赖文件: {package_json.relative_to(self.project_root)}")
        else:
            log_warning("未找到Node.js依赖文件")
        
        return True
    
    def _test_configuration_files(self) -> bool:
        """测试配置文件"""
        log_info("检查配置文件...")
        
        config_files = [
            self.project_root / "web_ui" / ".env.example",
            self.project_root / "config" / "config.yaml"
        ]
        
        all_good = True
        for config_file in config_files:
            if config_file.exists():
                log_success(f"配置文件存在: {config_file.relative_to(self.project_root)}")
            else:
                log_warning(f"配置文件不存在: {config_file.relative_to(self.project_root)}")
        
        return all_good
    
    def _test_directory_structure(self) -> bool:
        """测试目录结构"""
        log_info("检查项目目录结构...")
        
        required_dirs = [
            "src",
            "tests",
            "config",
            "data",
            "logs",
            "web_ui"
        ]
        
        all_good = True
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                log_success(f"目录存在: {dir_name}")
            else:
                log_error(f"必需目录不存在: {dir_name}")
                all_good = False
        
        return all_good
    
    def _check_python_environment(self) -> bool:
        """检查Python环境"""
        log_info("检查Python环境...")
        
        try:
            # 检查Python版本
            python_version = sys.version_info
            if python_version.major == 3 and python_version.minor >= 8:
                log_success(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
            else:
                log_error(f"Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}")
                return False
            
            # 检查关键模块导入
            critical_modules = ['json', 'os', 'sys', 'pathlib', 'datetime']
            for module in critical_modules:
                try:
                    __import__(module)
                    log_success(f"模块可用: {module}")
                except ImportError:
                    log_error(f"关键模块不可用: {module}")
                    return False
            
            return True
            
        except Exception as e:
            log_error(f"Python环境检查失败: {e}")
            return False
    
    def _check_project_structure(self) -> bool:
        """检查项目结构"""
        log_info("检查项目结构完整性...")
        
        # 检查关键文件
        critical_files = [
            "src/application/main.py",
            "start_system.sh",
            "README.md"
        ]
        
        all_good = True
        for file_path in critical_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                log_success(f"关键文件存在: {file_path}")
            else:
                log_error(f"关键文件缺失: {file_path}")
                all_good = False
        
        return all_good
    
    def _check_import_paths(self) -> bool:
        """检查导入路径"""
        log_info("检查模块导入路径...")
        
        try:
            # 尝试导入项目模块
            sys.path.insert(0, str(self.project_root / "src"))
            
            test_imports = [
                "main",
                "exceptions"
            ]
            
            for module in test_imports:
                try:
                    __import__(module)
                    log_success(f"模块导入成功: {module}")
                except ImportError as e:
                    log_warning(f"模块导入失败: {module} - {e}")
            
            return True
            
        except Exception as e:
            log_error(f"导入路径检查失败: {e}")
            return False
    
    def _check_configuration_validity(self) -> bool:
        """检查配置有效性"""
        log_info("检查配置文件有效性...")
        
        try:
            # 检查YAML配置文件
            config_file = self.project_root / "config" / "config.yaml"
            if config_file.exists():
                try:
                    import yaml
                    with open(config_file, 'r', encoding='utf-8') as f:
                        yaml.safe_load(f)
                    log_success("YAML配置文件格式正确")
                except Exception as e:
                    log_error(f"YAML配置文件格式错误: {e}")
                    return False
            
            return True
            
        except Exception as e:
            log_error(f"配置有效性检查失败: {e}")
            return False
    
    def _generate_test_report(self):
        """生成测试报告"""
        log_info("生成测试报告...")
        
        report_data = {
            'test_suite': '量化交易系统完整测试套件',
            'version': '3.0.0',
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'total_duration': (self.end_time - self.start_time).total_seconds(),
            'summary': {
                'total_tests': self.total_tests,
                'passed_tests': self.passed_tests,
                'failed_tests': self.failed_tests,
                'success_rate': self.passed_tests / self.total_tests if self.total_tests > 0 else 0
            },
            'test_results': self.test_results,
            'environment': {
                'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                'platform': sys.platform,
                'project_root': str(self.project_root)
            }
        }
        
        # 创建报告目录
        reports_dir = self.project_root / "tests" / "reports"
        reports_dir.mkdir(exist_ok=True)
        
        # 生成报告文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = reports_dir / f"complete_system_test_report_{timestamp}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            log_success(f"测试报告已生成: {report_file.relative_to(self.project_root)}")
            
        except Exception as e:
            log_error(f"生成测试报告失败: {e}")
    
    def _print_test_summary(self, overall_success: bool):
        """打印测试摘要"""
        logger.info("=" * 80)
        logger.info(f"{Colors.CYAN}📊 测试摘要{Colors.NC}")
        logger.info("=" * 80)
        
        duration = (self.end_time - self.start_time).total_seconds()
        
        logger.info(f"测试开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"测试结束时间: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"总执行时间: {duration:.2f}秒")
        logger.info()
        
        logger.info(f"总测试数: {self.total_tests}")
        logger.info(f"通过测试: {Colors.GREEN}{self.passed_tests}{Colors.NC}")
        logger.info(f"失败测试: {Colors.RED}{self.failed_tests}{Colors.NC}")
        
        if self.total_tests > 0:
            success_rate = self.passed_tests / self.total_tests
            color = Colors.GREEN if success_rate >= 0.9 else Colors.YELLOW if success_rate >= 0.7 else Colors.RED
            logger.info(f"成功率: {color}{success_rate:.1%}{Colors.NC}")
        
        logger.info()
        
        # 详细结果
        logger.info("详细测试结果:")
        for test_name, result in self.test_results.items():
            status_color = Colors.GREEN if result['success'] else Colors.RED
            status_text = "✅ 通过" if result['success'] else "❌ 失败"
            execution_time = result.get('execution_time', 0)
            logger.info(f"  {test_name}: {status_color}{status_text}{Colors.NC} ({execution_time:.2f}秒)")
        
        logger.info()
        
        if overall_success:
            logger.info(f"{Colors.GREEN}🎉 所有测试通过！系统运行正常。{Colors.NC}")
        else:
            logger.info(f"{Colors.RED}⚠️  部分测试失败，请检查详细日志和报告。{Colors.NC}")
        
        logger.info("=" * 80)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='运行量化交易系统完整测试套件',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
测试类型说明:
  comprehensive  - 综合系统测试
  unit          - 单元测试
  integration   - 集成测试
  startup       - 启动流程测试
  health        - 系统健康检查
  performance   - 性能测试
  e2e           - 端到端测试

示例:
  python scripts/startup/run_complete_system_test.py
  python scripts/startup/run_complete_system_test.py --types unit integration
  python scripts/startup/run_complete_system_test.py --quick --verbose
        """
    )
    
    parser.add_argument(
        '--types', '-t',
        nargs='+',
        choices=['comprehensive', 'unit', 'integration', 'startup', 'health', 'performance', 'e2e'],
        help='要运行的测试类型 (默认: 所有类型)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出模式'
    )
    
    parser.add_argument(
        '--no-report',
        action='store_true',
        help='不生成测试报告'
    )
    
    parser.add_argument(
        '--quick',
        action='store_true',
        help='快速模式，跳过耗时的性能测试和端到端测试'
    )
    
    args = parser.parse_args()
    
    # 创建并运行测试
    runner = SystemTestRunner()
    
    try:
        success = runner.run_complete_test_suite(
            test_types=args.types,
            verbose=args.verbose,
            generate_report=not args.no_report,
            quick_mode=args.quick
        )
        
        # 返回适当的退出码
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        log_warning("测试被用户中断")
        sys.exit(130)
    except Exception as e:
        log_error(f"测试执行过程中发生错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()