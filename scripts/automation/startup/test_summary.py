import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
测试结果汇总脚本

分析和汇总测试报告，生成测试趋势和统计信息。

功能包括：
1. 测试报告分析
2. 趋势统计
3. 性能分析
4. 问题识别
5. 建议生成

使用方法:
    python scripts/startup/test_summary.py [选项]

作者: 量化交易系统开发团队
版本: 3.0.0
创建日期: 2025-08-07
"""

import os
import sys
import json
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import statistics

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 颜色定义
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

class TestSummaryAnalyzer:
    """测试结果汇总分析器"""
    
    def __init__(self):
        self.project_root = project_root
        self.reports_dir = project_root / "tests" / "reports"
        self.reports_data = []
        
    def analyze_test_reports(self, days: int = 7) -> Dict:
        """分析测试报告"""
        
        logger.info(f"{Colors.CYAN}📊 测试报告分析{Colors.NC}")
        logger.info("=" * 60)
        
        # 加载报告数据
        self._load_reports(days)
        
        if not self.reports_data:
            logger.info(f"{Colors.YELLOW}⚠️  未找到测试报告数据{Colors.NC}")
            return {}
        
        # 执行分析
        analysis = {
            'summary': self._analyze_summary(),
            'trends': self._analyze_trends(),
            'performance': self._analyze_performance(),
            'issues': self._identify_issues(),
            'recommendations': self._generate_recommendations()
        }
        
        # 显示分析结果
        self._display_analysis(analysis)
        
        return analysis
    
    def _load_reports(self, days: int):
        """加载指定天数内的测试报告"""
        
        if not self.reports_dir.exists():
            return
        
        cutoff_date = datetime.now() - timedelta(days=days)
        report_files = list(self.reports_dir.glob("*.json"))
        
        logger.info(f"📁 扫描报告目录: {self.reports_dir.relative_to(self.project_root)}")
        logger.info(f"📅 时间范围: 最近{days}天")
        logger.info(f"📄 找到报告文件: {len(report_files)}个")
        logger.info()
        
        for report_file in report_files:
            try:
                # 检查文件修改时间
                file_time = datetime.fromtimestamp(report_file.stat().st_mtime)
                if file_time < cutoff_date:
                    continue
                
                # 加载报告数据
                with open(report_file, 'r', encoding='utf-8') as f:
                    report_data = json.load(f)
                
                # 添加文件信息
                report_data['_file_name'] = report_file.name
                report_data['_file_time'] = file_time.isoformat()
                
                self.reports_data.append(report_data)
                
            except Exception as e:
                logger.info(f"⚠️  读取报告失败 {report_file.name}: {e}")
        
        # 按时间排序
        self.reports_data.sort(key=lambda x: x.get('start_time', ''))
        
        logger.info(f"✅ 成功加载 {len(self.reports_data)} 个报告")
        logger.info()
    
    def _analyze_summary(self) -> Dict:
        """分析测试摘要"""
        
        if not self.reports_data:
            return {}
        
        # 统计数据
        total_runs = len(self.reports_data)
        total_tests = sum(report.get('summary', {}).get('total_tests', 0) for report in self.reports_data)
        total_passed = sum(report.get('summary', {}).get('passed_tests', 0) for report in self.reports_data)
        total_failed = sum(report.get('summary', {}).get('failed_tests', 0) for report in self.reports_data)
        
        # 成功率统计 - 基于实际通过和失败的测试数量计算
        if total_tests > 0:
            avg_success_rate = total_passed / total_tests
        else:
            avg_success_rate = 0
        
        # 获取各个报告的成功率用于趋势分析
        success_rates = []
        for report in self.reports_data:
            summary = report.get('summary', {})
            report_total = summary.get('total_tests', 0)
            report_passed = summary.get('passed_tests', 0)
            if report_total > 0:
                success_rates.append(report_passed / report_total)
            else:
                success_rates.append(0)
        
        # 执行时间统计
        execution_times = [
            report.get('total_duration', 0) 
            for report in self.reports_data
        ]
        
        avg_execution_time = statistics.mean(execution_times) if execution_times else 0
        
        return {
            'total_runs': total_runs,
            'total_tests': total_tests,
            'total_passed': total_passed,
            'total_failed': total_failed,
            'avg_success_rate': avg_success_rate,
            'avg_execution_time': avg_execution_time,
            'success_rates': success_rates,
            'execution_times': execution_times
        }
    
    def _analyze_trends(self) -> Dict:
        """分析测试趋势"""
        
        if len(self.reports_data) < 2:
            return {'trend': 'insufficient_data'}
        
        # 成功率趋势
        success_rates = [
            report.get('summary', {}).get('success_rate', 0) 
            for report in self.reports_data
        ]
        
        # 计算趋势
        recent_rates = success_rates[-3:] if len(success_rates) >= 3 else success_rates
        earlier_rates = success_rates[:-3] if len(success_rates) >= 6 else success_rates[:-len(recent_rates)]
        
        if earlier_rates:
            recent_avg = statistics.mean(recent_rates)
            earlier_avg = statistics.mean(earlier_rates)
            trend_direction = 'improving' if recent_avg > earlier_avg else 'declining' if recent_avg < earlier_avg else 'stable'
        else:
            trend_direction = 'stable'
        
        # 执行时间趋势
        execution_times = [
            report.get('total_duration', 0) 
            for report in self.reports_data
        ]
        
        recent_times = execution_times[-3:] if len(execution_times) >= 3 else execution_times
        earlier_times = execution_times[:-3] if len(execution_times) >= 6 else execution_times[:-len(recent_times)]
        
        if earlier_times:
            recent_time_avg = statistics.mean(recent_times)
            earlier_time_avg = statistics.mean(earlier_times)
            time_trend = 'faster' if recent_time_avg < earlier_time_avg else 'slower' if recent_time_avg > earlier_time_avg else 'stable'
        else:
            time_trend = 'stable'
        
        return {
            'success_rate_trend': trend_direction,
            'execution_time_trend': time_trend,
            'recent_success_rate': statistics.mean(recent_rates) if recent_rates else 0,
            'earlier_success_rate': statistics.mean(earlier_rates) if earlier_rates else 0,
            'recent_execution_time': statistics.mean(recent_times) if recent_times else 0,
            'earlier_execution_time': statistics.mean(earlier_times) if earlier_times else 0
        }
    
    def _analyze_performance(self) -> Dict:
        """分析性能指标"""
        
        if not self.reports_data:
            return {}
        
        # 执行时间分析
        execution_times = [
            report.get('total_duration', 0) 
            for report in self.reports_data
        ]
        
        if execution_times:
            min_time = min(execution_times)
            max_time = max(execution_times)
            avg_time = statistics.mean(execution_times)
            median_time = statistics.median(execution_times)
            
            # 性能等级
            if avg_time < 60:
                performance_grade = 'excellent'
            elif avg_time < 300:
                performance_grade = 'good'
            elif avg_time < 600:
                performance_grade = 'fair'
            else:
                performance_grade = 'poor'
        else:
            min_time = max_time = avg_time = median_time = 0
            performance_grade = 'unknown'
        
        # 测试类型性能分析
        test_type_performance = {}
        for report in self.reports_data:
            test_results = report.get('test_results', {})
            if isinstance(test_results, dict):
                for test_type, result in test_results.items():
                    if test_type not in test_type_performance:
                        test_type_performance[test_type] = []
                    
                    if isinstance(result, dict):
                        execution_time = result.get('execution_time', 0)
                        if execution_time > 0:
                            test_type_performance[test_type].append(execution_time)
        
        # 计算各测试类型的平均时间
        test_type_avg_times = {}
        for test_type, times in test_type_performance.items():
            if times:
                test_type_avg_times[test_type] = statistics.mean(times)
        
        return {
            'min_execution_time': min_time,
            'max_execution_time': max_time,
            'avg_execution_time': avg_time,
            'median_execution_time': median_time,
            'performance_grade': performance_grade,
            'test_type_avg_times': test_type_avg_times
        }
    
    def _identify_issues(self) -> List[Dict]:
        """识别问题"""
        
        issues = []
        
        if not self.reports_data:
            return issues
        
        # 分析最近的报告
        recent_reports = self.reports_data[-5:] if len(self.reports_data) >= 5 else self.reports_data
        
        # 检查成功率问题
        low_success_rates = [
            report for report in recent_reports
            if report.get('summary', {}).get('success_rate', 1.0) < 0.8
        ]
        
        if low_success_rates:
            issues.append({
                'type': 'low_success_rate',
                'severity': 'high',
                'description': f'最近{len(low_success_rates)}次测试成功率低于80%',
                'affected_reports': [report['_file_name'] for report in low_success_rates]
            })
        
        # 检查执行时间问题
        long_execution_times = [
            report for report in recent_reports
            if report.get('total_duration', 0) > 600  # 10分钟
        ]
        
        if long_execution_times:
            issues.append({
                'type': 'long_execution_time',
                'severity': 'medium',
                'description': f'最近{len(long_execution_times)}次测试执行时间超过10分钟',
                'affected_reports': [report['_file_name'] for report in long_execution_times]
            })
        
        # 检查失败的测试类型
        failed_test_types = {}
        for report in recent_reports:
            test_results = report.get('test_results', {})
            if isinstance(test_results, dict):
                for test_type, result in test_results.items():
                    if isinstance(result, dict) and not result.get('success', True):
                        if test_type not in failed_test_types:
                            failed_test_types[test_type] = 0
                        failed_test_types[test_type] += 1
        
        for test_type, failure_count in failed_test_types.items():
            if failure_count >= 2:  # 连续失败2次以上
                issues.append({
                    'type': 'recurring_test_failure',
                    'severity': 'high',
                    'description': f'测试类型 "{test_type}" 在最近{failure_count}次运行中失败',
                    'test_type': test_type,
                    'failure_count': failure_count
                })
        
        return issues
    
    def _generate_recommendations(self) -> List[str]:
        """生成建议"""
        
        recommendations = []
        
        if not self.reports_data:
            recommendations.append("建议运行一些测试以获得分析数据")
            return recommendations
        
        # 基于分析结果生成建议
        summary = self._analyze_summary()
        trends = self._analyze_trends()
        performance = self._analyze_performance()
        issues = self._identify_issues()
        
        # 成功率建议
        if summary.get('avg_success_rate', 1.0) < 0.9:
            recommendations.append("建议检查和修复失败的测试用例，提高测试成功率")
        
        # 性能建议
        performance_grade = performance.get('performance_grade', 'unknown')
        if performance_grade == 'poor':
            recommendations.append("测试执行时间较长，建议优化测试用例或使用并行执行")
        elif performance_grade == 'fair':
            recommendations.append("测试执行时间中等，可以考虑进一步优化")
        
        # 趋势建议
        if trends.get('success_rate_trend') == 'declining':
            recommendations.append("测试成功率呈下降趋势，建议及时修复问题")
        
        if trends.get('execution_time_trend') == 'slower':
            recommendations.append("测试执行时间呈上升趋势，建议检查性能回归")
        
        # 问题建议
        for issue in issues:
            if issue['type'] == 'recurring_test_failure':
                recommendations.append(f"测试类型 \"{issue['test_type']}\" 频繁失败，建议优先修复")
            elif issue['type'] == 'low_success_rate':
                recommendations.append("最近测试成功率较低，建议进行全面的问题排查")
            elif issue['type'] == 'long_execution_time':
                recommendations.append("测试执行时间过长，建议优化测试策略或增加资源")
        
        # 通用建议
        if len(self.reports_data) < 5:
            recommendations.append("建议增加测试运行频率以获得更好的趋势分析")
        
        if not recommendations:
            recommendations.append("测试状态良好，继续保持当前的测试质量")
        
        return recommendations
    
    def _display_analysis(self, analysis: Dict):
        """显示分析结果"""
        
        # 显示摘要
        summary = analysis.get('summary', {})
        if summary:
            logger.info(f"{Colors.GREEN}📈 测试摘要{Colors.NC}")
            logger.info("-" * 40)
            logger.info(f"测试运行次数: {summary.get('total_runs', 0)}")
            logger.info(f"总测试数: {summary.get('total_tests', 0)}")
            logger.info(f"通过测试: {Colors.GREEN}{summary.get('total_passed', 0)}{Colors.NC}")
            logger.info(f"失败测试: {Colors.RED}{summary.get('total_failed', 0)}{Colors.NC}")
            
            avg_success_rate = summary.get('avg_success_rate', 0)
            rate_color = Colors.GREEN if avg_success_rate >= 0.9 else Colors.YELLOW if avg_success_rate >= 0.7 else Colors.RED
            logger.info(f"平均成功率: {rate_color}{avg_success_rate:.1%}{Colors.NC}")
            
            avg_time = summary.get('avg_execution_time', 0)
            logger.info(f"平均执行时间: {avg_time:.2f}秒")
            logger.info()
        
        # 显示趋势
        trends = analysis.get('trends', {})
        if trends and trends.get('trend') != 'insufficient_data':
            logger.info(f"{Colors.BLUE}📊 趋势分析{Colors.NC}")
            logger.info("-" * 40)
            
            success_trend = trends.get('success_rate_trend', 'stable')
            trend_color = Colors.GREEN if success_trend == 'improving' else Colors.RED if success_trend == 'declining' else Colors.YELLOW
            logger.info(f"成功率趋势: {trend_color}{success_trend}{Colors.NC}")
            
            time_trend = trends.get('execution_time_trend', 'stable')
            time_color = Colors.GREEN if time_trend == 'faster' else Colors.RED if time_trend == 'slower' else Colors.YELLOW
            logger.info(f"执行时间趋势: {time_color}{time_trend}{Colors.NC}")
            logger.info()
        
        # 显示性能
        performance = analysis.get('performance', {})
        if performance:
            logger.info(f"{Colors.PURPLE}⚡ 性能分析{Colors.NC}")
            logger.info("-" * 40)
            
            grade = performance.get('performance_grade', 'unknown')
            grade_color = Colors.GREEN if grade == 'excellent' else Colors.BLUE if grade == 'good' else Colors.YELLOW if grade == 'fair' else Colors.RED
            logger.info(f"性能等级: {grade_color}{grade}{Colors.NC}")
            
            avg_time = performance.get('avg_execution_time', 0)
            min_time = performance.get('min_execution_time', 0)
            max_time = performance.get('max_execution_time', 0)
            
            logger.info(f"平均执行时间: {avg_time:.2f}秒")
            logger.info(f"最快执行时间: {min_time:.2f}秒")
            logger.info(f"最慢执行时间: {max_time:.2f}秒")
            
            # 显示各测试类型的性能
            test_type_times = performance.get('test_type_avg_times', {})
            if test_type_times:
                logger.info("\n各测试类型平均时间:")
                for test_type, avg_time in sorted(test_type_times.items(), key=lambda x: x[1], reverse=True):
                    logger.info(f"  {test_type}: {avg_time:.2f}秒")
            
            logger.info()
        
        # 显示问题
        issues = analysis.get('issues', [])
        if issues:
            logger.info(f"{Colors.RED}⚠️  发现的问题{Colors.NC}")
            logger.info("-" * 40)
            
            for i, issue in enumerate(issues, 1):
                severity = issue.get('severity', 'unknown')
                severity_color = Colors.RED if severity == 'high' else Colors.YELLOW if severity == 'medium' else Colors.BLUE
                
                logger.info(f"{i}. {severity_color}[{severity.upper()}]{Colors.NC} {issue.get('description', '')}")
            
            logger.info()
        
        # 显示建议
        recommendations = analysis.get('recommendations', [])
        if recommendations:
            logger.info(f"{Colors.CYAN}💡 改进建议{Colors.NC}")
            logger.info("-" * 40)
            
            for i, recommendation in enumerate(recommendations, 1):
                logger.info(f"{i}. {recommendation}")
            
            logger.info()
    
    def generate_html_report(self, analysis: Dict, output_file: Optional[str] = None) -> str:
        """生成HTML报告"""
        
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"test_summary_report_{timestamp}.html"
        
        # 简单的HTML模板
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试汇总报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        .success {{ color: #28a745; }}
        .warning {{ color: #ffc107; }}
        .danger {{ color: #dc3545; }}
        .info {{ color: #17a2b8; }}
        table {{ width: 100%; border-collapse: collapse; }}
        th, td {{ padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>量化交易系统 - 测试汇总报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="section">
        <h2>测试摘要</h2>
        <table>
            <tr><th>指标</th><th>数值</th></tr>
            <tr><td>测试运行次数</td><td>{analysis.get('summary', {}).get('total_runs', 0)}</td></tr>
            <tr><td>总测试数</td><td>{analysis.get('summary', {}).get('total_tests', 0)}</td></tr>
            <tr><td>通过测试</td><td class="success">{analysis.get('summary', {}).get('total_passed', 0)}</td></tr>
            <tr><td>失败测试</td><td class="danger">{analysis.get('summary', {}).get('total_failed', 0)}</td></tr>
            <tr><td>平均成功率</td><td>{analysis.get('summary', {}).get('avg_success_rate', 0):.1%}</td></tr>
            <tr><td>平均执行时间</td><td>{analysis.get('summary', {}).get('avg_execution_time', 0):.2f}秒</td></tr>
        </table>
    </div>
    
    <div class="section">
        <h2>改进建议</h2>
        <ul>
        """
        
        for recommendation in analysis.get('recommendations', []):
            html_content += f"<li>{recommendation}</li>"
        
        html_content += """
        </ul>
    </div>
</body>
</html>
        """
        
        # 写入文件
        output_path = self.reports_dir / output_file
        self.reports_dir.mkdir(exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"📄 HTML报告已生成: {output_path.relative_to(self.project_root)}")
        
        return str(output_path)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试结果汇总分析')
    
    parser.add_argument(
        '--days', '-d',
        type=int,
        default=7,
        help='分析最近几天的报告 (默认: 7天)'
    )
    
    parser.add_argument(
        '--html',
        action='store_true',
        help='生成HTML报告'
    )
    
    parser.add_argument(
        '--output', '-o',
        help='输出文件名'
    )
    
    args = parser.parse_args()
    
    try:
        analyzer = TestSummaryAnalyzer()
        analysis = analyzer.analyze_test_reports(args.days)
        
        if args.html and analysis:
            analyzer.generate_html_report(analysis, args.output)
        
    except KeyboardInterrupt:
        logger.info(f"\n{Colors.YELLOW}分析被用户中断{Colors.NC}")
        sys.exit(130)
    except Exception as e:
        logger.info(f"{Colors.RED}分析过程中发生错误: {e}{Colors.NC}")
        sys.exit(1)


if __name__ == '__main__':
    main()