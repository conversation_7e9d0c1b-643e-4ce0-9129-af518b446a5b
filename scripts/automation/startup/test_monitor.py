import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
测试状态监控脚本

提供实时的测试执行监控和状态报告功能，包括：
1. 测试进度监控
2. 资源使用监控
3. 测试结果统计
4. 实时状态更新

使用方法:
    python scripts/startup/test_monitor.py [选项]

作者: 量化交易系统开发团队
版本: 3.0.0
创建日期: 2025-08-07
"""

import os
import sys
import time
import json
import psutil
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 颜色定义
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

class TestMonitor:
    """测试监控器"""
    
    def __init__(self):
        self.project_root = project_root
        self.monitoring = False
        self.start_time = None
        self.test_processes = []
        self.test_stats = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'running_tests': 0,
            'execution_time': 0
        }
        
    def start_monitoring(self, refresh_interval: float = 1.0):
        """开始监控测试执行"""
        self.monitoring = True
        self.start_time = datetime.now()
        
        logger.info(f"{Colors.CYAN}🔍 测试监控器启动{Colors.NC}")
        logger.info("=" * 60)
        logger.info(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"刷新间隔: {refresh_interval}秒")
        logger.info()
        
        try:
            while self.monitoring:
                self._update_display()
                time.sleep(refresh_interval)
                
        except KeyboardInterrupt:
            logger.info(f"\n{Colors.YELLOW}监控被用户中断{Colors.NC}")
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds() if self.start_time else 0
        
        logger.info(f"\n{Colors.CYAN}监控结束{Colors.NC}")
        logger.info(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"监控时长: {duration:.2f}秒")
    
    def _update_display(self):
        """更新显示内容"""
        # 清屏
        os.system('clear' if os.name == 'posix' else 'cls')
        
        current_time = datetime.now()
        elapsed_time = (current_time - self.start_time).total_seconds() if self.start_time else 0
        
        # 显示标题
        logger.info(f"{Colors.CYAN}🔍 量化交易系统 - 测试监控器{Colors.NC}")
        logger.info("=" * 60)
        logger.info(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"运行时长: {elapsed_time:.0f}秒")
        logger.info()
        
        # 显示系统状态
        self._display_system_status()
        
        # 显示测试进程
        self._display_test_processes()
        
        # 显示测试统计
        self._display_test_statistics()
        
        # 显示最近的测试报告
        self._display_recent_reports()
        
        # 显示控制提示
        logger.info(f"\n{Colors.YELLOW}按 Ctrl+C 停止监控{Colors.NC}")
    
    def _display_system_status(self):
        """显示系统状态"""
        logger.info(f"{Colors.BLUE}📊 系统状态{Colors.NC}")
        logger.info("-" * 30)
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=0.1)
        cpu_color = Colors.GREEN if cpu_percent < 50 else Colors.YELLOW if cpu_percent < 80 else Colors.RED
        logger.info(f"CPU使用率: {cpu_color}{cpu_percent:.1f}%{Colors.NC}")
        
        # 内存使用率
        memory = psutil.virtual_memory()
        memory_color = Colors.GREEN if memory.percent < 70 else Colors.YELLOW if memory.percent < 85 else Colors.RED
        logger.info(f"内存使用率: {memory_color}{memory.percent:.1f}%{Colors.NC}")
        
        # 磁盘使用率
        disk = psutil.disk_usage('/')
        disk_color = Colors.GREEN if disk.percent < 80 else Colors.YELLOW if disk.percent < 90 else Colors.RED
        logger.info(f"磁盘使用率: {disk_color}{disk.percent:.1f}%{Colors.NC}")
        
        logger.info()
    
    def _display_test_processes(self):
        """显示测试进程"""
        logger.info(f"{Colors.PURPLE}🔄 测试进程{Colors.NC}")
        logger.info("-" * 30)
        
        # 查找测试相关进程
        test_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if any(keyword in cmdline.lower() for keyword in ['pytest', 'test', 'run_complete_system_test']):
                    test_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if test_processes:
            for proc in test_processes:
                pid = proc['pid']
                name = proc['name']
                cpu = proc['cpu_percent'] or 0
                memory = proc['memory_percent'] or 0
                
                status_color = Colors.GREEN if cpu < 50 else Colors.YELLOW
                logger.info(f"  PID {pid}: {name} - CPU: {status_color}{cpu:.1f}%{Colors.NC}, 内存: {memory:.1f}%")
        else:
            logger.info(f"  {Colors.YELLOW}未发现活动的测试进程{Colors.NC}")
        
        logger.info()
    
    def _display_test_statistics(self):
        """显示测试统计"""
        logger.info(f"{Colors.GREEN}📈 测试统计{Colors.NC}")
        logger.info("-" * 30)
        
        # 更新测试统计
        self._update_test_statistics()
        
        total = self.test_stats['total_tests']
        passed = self.test_stats['passed_tests']
        failed = self.test_stats['failed_tests']
        running = self.test_stats['running_tests']
        
        logger.info(f"总测试数: {total}")
        logger.info(f"通过测试: {Colors.GREEN}{passed}{Colors.NC}")
        logger.info(f"失败测试: {Colors.RED}{failed}{Colors.NC}")
        logger.info(f"运行中: {Colors.YELLOW}{running}{Colors.NC}")
        
        if total > 0:
            success_rate = passed / total
            rate_color = Colors.GREEN if success_rate >= 0.9 else Colors.YELLOW if success_rate >= 0.7 else Colors.RED
            logger.info(f"成功率: {rate_color}{success_rate:.1%}{Colors.NC}")
        
        logger.info()
    
    def _display_recent_reports(self):
        """显示最近的测试报告"""
        logger.info(f"{Colors.CYAN}📋 最近的测试报告{Colors.NC}")
        logger.info("-" * 30)
        
        reports_dir = self.project_root / "tests" / "reports"
        if reports_dir.exists():
            # 获取最近的报告文件
            report_files = list(reports_dir.glob("*.json"))
            report_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            if report_files:
                for i, report_file in enumerate(report_files[:3]):  # 显示最近3个报告
                    try:
                        with open(report_file, 'r', encoding='utf-8') as f:
                            report_data = json.load(f)
                        
                        # 提取报告信息
                        report_time = report_data.get('start_time', 'Unknown')
                        if report_time != 'Unknown':
                            report_time = datetime.fromisoformat(report_time.replace('Z', '+00:00'))
                            report_time = report_time.strftime('%m-%d %H:%M')
                        
                        summary = report_data.get('summary', {})
                        total_tests = summary.get('total_tests', 0)
                        passed_tests = summary.get('passed_tests', 0)
                        success_rate = summary.get('success_rate', 0)
                        
                        status_color = Colors.GREEN if success_rate >= 0.9 else Colors.YELLOW if success_rate >= 0.7 else Colors.RED
                        logger.info(f"  {report_time}: {total_tests}个测试, {status_color}{success_rate:.1%}通过{Colors.NC}")
                        
                    except Exception as e:
                        logger.info(f"  {report_file.name}: 读取失败 - {e}")
            else:
                logger.info(f"  {Colors.YELLOW}暂无测试报告{Colors.NC}")
        else:
            logger.info(f"  {Colors.YELLOW}报告目录不存在{Colors.NC}")
    
    def _update_test_statistics(self):
        """更新测试统计信息"""
        # 这里可以从测试报告文件或日志中读取统计信息
        # 目前使用简单的模拟数据
        
        reports_dir = self.project_root / "tests" / "reports"
        if reports_dir.exists():
            report_files = list(reports_dir.glob("*.json"))
            if report_files:
                # 读取最新的报告
                latest_report = max(report_files, key=lambda x: x.stat().st_mtime)
                try:
                    with open(latest_report, 'r', encoding='utf-8') as f:
                        report_data = json.load(f)
                    
                    summary = report_data.get('summary', {})
                    self.test_stats.update({
                        'total_tests': summary.get('total_tests', 0),
                        'passed_tests': summary.get('passed_tests', 0),
                        'failed_tests': summary.get('failed_tests', 0),
                        'execution_time': summary.get('total_execution_time', 0)
                    })
                    
                except Exception:
                    pass
    
    def monitor_test_execution(self, test_command: List[str]):
        """监控特定测试命令的执行"""
        import subprocess
        
        logger.info(f"{Colors.CYAN}🚀 开始执行测试命令{Colors.NC}")
        logger.info(f"命令: {' '.join(test_command)}")
        logger.info()
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self.start_monitoring, args=(2.0,))
        monitor_thread.daemon = True
        monitor_thread.start()
        
        try:
            # 执行测试命令
            result = subprocess.run(
                test_command,
                cwd=str(self.project_root),
                capture_output=False,  # 让输出直接显示
                text=True
            )
            
            # 停止监控
            self.stop_monitoring()
            
            return result.returncode == 0
            
        except KeyboardInterrupt:
            logger.info(f"\n{Colors.YELLOW}测试执行被用户中断{Colors.NC}")
            self.stop_monitoring()
            return False
        except Exception as e:
            logger.info(f"\n{Colors.RED}测试执行错误: {e}{Colors.NC}")
            self.stop_monitoring()
            return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试状态监控器')
    
    parser.add_argument(
        '--interval', '-i',
        type=float,
        default=1.0,
        help='刷新间隔（秒）'
    )
    
    parser.add_argument(
        '--monitor-command', '-c',
        nargs='+',
        help='监控特定测试命令的执行'
    )
    
    args = parser.parse_args()
    
    monitor = TestMonitor()
    
    try:
        if args.monitor_command:
            # 监控特定命令
            success = monitor.monitor_test_execution(args.monitor_command)
            sys.exit(0 if success else 1)
        else:
            # 普通监控模式
            monitor.start_monitoring(args.interval)
            
    except KeyboardInterrupt:
        logger.info(f"\n{Colors.YELLOW}监控被用户中断{Colors.NC}")
        sys.exit(130)
    except Exception as e:
        logger.info(f"{Colors.RED}监控器错误: {e}{Colors.NC}")
        sys.exit(1)


if __name__ == '__main__':
    main()