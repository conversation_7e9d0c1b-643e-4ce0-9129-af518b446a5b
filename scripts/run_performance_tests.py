#!/usr/bin/env python3
"""
性能测试自动化执行脚本

本脚本用于自动化执行性能基准测试和稳定性测试，
生成详细的性能分析报告和建议。

功能特性：
1. 自动化性能基准测试
2. 稳定性压力测试
3. 资源使用监控
4. 性能趋势分析
5. 自动化报告生成
6. 性能回归检测

使用方法：
    python scripts/run_performance_tests.py [options]

选项：
    --benchmark, -b     执行性能基准测试
    --stability, -s     执行稳定性测试
    --resource, -r      执行资源使用测试
    --scalability       执行可扩展性测试
    --all, -a           执行所有性能测试
    --output, -o        输出目录 (默认: reports/performance)
    --baseline          设置性能基线
    --compare           与基线对比
    --timeout           测试超时时间（秒，默认: 3600）

作者: 系统修复团队
版本: 1.0.0
创建日期: 2025-01-11
"""

import os
import sys
import argparse
import asyncio
import subprocess
import json
import time
import statistics
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class PerformanceTestRunner:
    """性能测试运行器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
        # 设置日志
        self.setup_logging()
        
        # 性能测试套件定义
        self.test_suites = {
            'benchmark': {
                'path': 'tests/performance/test_performance_benchmarks.py::TestPerformanceAndStability::test_performance_benchmark_tests',
                'description': '性能基准测试',
                'timeout': 1800,
                'category': 'performance'
            },
            'stability': {
                'path': 'tests/performance/test_performance_benchmarks.py::TestPerformanceAndStability::test_stability_stress_tests',
                'description': '稳定性压力测试',
                'timeout': 2400,
                'category': 'stability'
            },
            'resource': {
                'path': 'tests/performance/test_performance_benchmarks.py::TestPerformanceAndStability::test_resource_usage_tests',
                'description': '资源使用测试',
                'timeout': 900,
                'category': 'resource'
            },
            'scalability': {
                'path': 'tests/performance/test_performance_benchmarks.py::TestPerformanceAndStability::test_scalability_tests',
                'description': '可扩展性测试',
                'timeout': 1200,
                'category': 'scalability'
            }
        }
        
        # 性能阈值配置
        self.performance_thresholds = {
            'startup_time': 30.0,
            'api_response_time': 2.0,
            'db_query_time': 1.0,
            'memory_usage_limit': 80.0,
            'cpu_usage_limit': 70.0,
            'cache_hit_rate': 85.0,
            'throughput_min': 100.0,
            'concurrent_users': 50,
            'error_rate_limit': 1.0,
            'recovery_time_limit': 30.0
        }
        
    def setup_logging(self):
        """设置日志配置"""
        log_level = logging.INFO
        
        # 创建输出目录
        self.config['output_dir'].mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(
                    self.config['output_dir'] / 'performance_test.log'
                )
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    async def run_performance_tests(self) -> Dict:
        """运行性能测试"""
        self.logger.info("🚀 开始执行性能测试...")
        self.start_time = datetime.now()
        
        try:
            # 准备测试环境
            await self.prepare_test_environment()
            
            # 确定要运行的测试套件
            test_suites_to_run = self.determine_test_suites()
            
            # 执行测试套件
            results = await self.run_test_suites(test_suites_to_run)
            
            # 生成性能报告
            report = await self.generate_performance_report(results)
            
            # 如果启用了基线对比，执行对比分析
            if self.config.get('compare'):
                await self.compare_with_baseline(report)
            
            # 如果启用了基线设置，保存当前结果为基线
            if self.config.get('baseline'):
                await self.save_baseline(report)
            
            # 清理测试环境
            await self.cleanup_test_environment()
            
            self.end_time = datetime.now()
            duration = self.end_time - self.start_time
            self.logger.info(f"✅ 性能测试完成，总耗时: {duration}")
            
            return report
            
        except Exception as e:
            self.logger.error(f"❌ 性能测试执行失败: {e}")
            raise
    
    def determine_test_suites(self) -> List[str]:
        """确定要运行的测试套件"""
        if self.config.get('all'):
            return list(self.test_suites.keys())
        
        suites = []
        if self.config.get('benchmark'):
            suites.append('benchmark')
        if self.config.get('stability'):
            suites.append('stability')
        if self.config.get('resource'):
            suites.append('resource')
        if self.config.get('scalability'):
            suites.append('scalability')
        
        # 如果没有指定任何测试，默认运行基准测试
        if not suites:
            suites = ['benchmark']
        
        return suites
    
    async def prepare_test_environment(self):
        """准备测试环境"""
        self.logger.info("🔧 准备性能测试环境...")
        
        # 设置环境变量
        os.environ.update({
            'TEST_MODE': 'performance',
            'TEST_OUTPUT_DIR': str(self.config['output_dir']),
            'PERFORMANCE_TEST': 'true'
        })
        
        # 检查系统资源
        await self.check_system_resources()
        
        # 预热系统
        await self.warmup_system()
        
        self.logger.info("✅ 测试环境准备完成")
    
    async def check_system_resources(self):
        """检查系统资源"""
        try:
            import psutil
            
            # 检查可用内存
            memory = psutil.virtual_memory()
            if memory.available < 1024 * 1024 * 1024:  # 1GB
                self.logger.warning("⚠️ 可用内存不足1GB，可能影响测试结果")
            
            # 检查CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 50:
                self.logger.warning(f"⚠️ 当前CPU使用率较高: {cpu_percent}%")
            
            # 检查磁盘空间
            disk = psutil.disk_usage('/')
            if disk.free < 1024 * 1024 * 1024:  # 1GB
                self.logger.warning("⚠️ 可用磁盘空间不足1GB")
                
        except ImportError:
            self.logger.warning("⚠️ 无法检查系统资源，请确保安装了psutil")
    
    async def warmup_system(self):
        """预热系统"""
        self.logger.info("🔥 预热系统...")
        
        # 执行一些轻量级操作来预热系统
        warmup_operations = [
            self._warmup_cpu,
            self._warmup_memory,
            self._warmup_io
        ]
        
        for operation in warmup_operations:
            await operation()
        
        # 等待系统稳定
        await asyncio.sleep(2)
        
        self.logger.info("✅ 系统预热完成")
    
    async def _warmup_cpu(self):
        """CPU预热"""
        start_time = time.time()
        while time.time() - start_time < 1:
            _ = sum(i * i for i in range(1000))
    
    async def _warmup_memory(self):
        """内存预热"""
        temp_data = [i for i in range(10000)]
        await asyncio.sleep(0.1)
        del temp_data
    
    async def _warmup_io(self):
        """I/O预热"""
        temp_file = self.config['output_dir'] / 'warmup.tmp'
        with open(temp_file, 'w') as f:
            f.write('warmup data\n' * 100)
        
        with open(temp_file, 'r') as f:
            _ = f.read()
        
        temp_file.unlink(missing_ok=True)
    
    async def run_test_suites(self, test_suites: List[str]) -> Dict:
        """运行测试套件"""
        self.logger.info(f"📋 执行测试套件: {', '.join(test_suites)}")
        
        results = {}
        
        for suite_name in test_suites:
            if suite_name not in self.test_suites:
                self.logger.warning(f"⚠️ 未知的测试套件: {suite_name}")
                continue
            
            suite_config = self.test_suites[suite_name]
            self.logger.info(f"🧪 执行测试套件: {suite_config['description']}")
            
            result = await self.run_single_test_suite(suite_name, suite_config)
            results[suite_name] = result
            
            if result['success']:
                self.logger.info(f"✅ {suite_name} 测试套件执行成功")
            else:
                self.logger.error(f"❌ {suite_name} 测试套件执行失败")
        
        return results
    
    async def run_single_test_suite(self, suite_name: str, suite_config: Dict) -> Dict:
        """运行单个测试套件"""
        start_time = time.time()
        
        try:
            # 构建pytest命令
            cmd = self.build_pytest_command(suite_name, suite_config)
            
            self.logger.debug(f"执行命令: {' '.join(cmd)}")
            
            # 执行测试
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=project_root
            )
            
            # 等待测试完成（带超时）
            timeout = min(suite_config.get('timeout', 1800), self.config.get('timeout', 3600))
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                raise TimeoutError(f"测试套件 {suite_name} 执行超时 ({timeout}秒)")
            
            # 解析测试结果
            result = self.parse_test_output(stdout.decode(), stderr.decode())
            result['duration'] = time.time() - start_time
            result['success'] = process.returncode == 0
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 测试套件 {suite_name} 执行异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time,
                'tests_run': 0,
                'tests_passed': 0,
                'tests_failed': 1
            }
    
    def build_pytest_command(self, suite_name: str, suite_config: Dict) -> List[str]:
        """构建pytest命令"""
        cmd = ['python', '-m', 'pytest']
        
        # 添加测试路径
        cmd.append(suite_config['path'])
        
        # 基本选项
        cmd.extend(['-v', '--tb=short'])
        
        # 超时设置
        timeout = suite_config.get('timeout', 1800)
        cmd.extend(['--timeout', str(timeout)])
        
        # 输出格式
        output_dir = self.config['output_dir']
        
        # JUnit XML报告
        cmd.extend([
            '--junit-xml',
            str(output_dir / f'junit_{suite_name}.xml')
        ])
        
        # 性能测试特定选项
        if suite_name in ['benchmark', 'stability']:
            cmd.extend(['--capture=no'])  # 实时输出
        
        return cmd
    
    def parse_test_output(self, stdout: str, stderr: str) -> Dict:
        """解析测试输出"""
        result = {
            'tests_run': 0,
            'tests_passed': 0,
            'tests_failed': 0,
            'tests_skipped': 0,
            'output': stdout,
            'errors': stderr,
            'performance_metrics': {}
        }
        
        # 解析pytest输出
        lines = stdout.split('\n')
        
        for line in lines:
            # 查找测试结果摘要行
            if ' passed' in line or ' failed' in line or ' skipped' in line:
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == 'passed' and i > 0:
                        try:
                            result['tests_passed'] = int(parts[i-1])
                        except (ValueError, IndexError):
                            pass
                    elif part == 'failed' and i > 0:
                        try:
                            result['tests_failed'] = int(parts[i-1])
                        except (ValueError, IndexError):
                            pass
                    elif part == 'skipped' and i > 0:
                        try:
                            result['tests_skipped'] = int(parts[i-1])
                        except (ValueError, IndexError):
                            pass
        
        result['tests_run'] = (
            result['tests_passed'] + 
            result['tests_failed'] + 
            result['tests_skipped']
        )
        
        # 提取性能指标
        result['performance_metrics'] = self.extract_performance_metrics(stdout)
        
        return result
    
    def extract_performance_metrics(self, output: str) -> Dict:
        """从测试输出中提取性能指标"""
        metrics = {}
        
        lines = output.split('\n')
        
        for line in lines:
            # 查找性能指标行
            if '平均' in line and '秒' in line:
                # 提取时间指标
                try:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if '秒' in part and i > 0:
                            time_value = float(parts[i-1].replace('秒', ''))
                            if '启动' in line:
                                metrics['startup_time'] = time_value
                            elif 'API' in line:
                                metrics['api_response_time'] = time_value
                            elif '数据库' in line:
                                metrics['db_query_time'] = time_value
                except (ValueError, IndexError):
                    pass
            
            elif '命中率' in line and '%' in line:
                # 提取缓存命中率
                try:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if '%' in part:
                            hit_rate = float(part.replace('%', ''))
                            metrics['cache_hit_rate'] = hit_rate
                            break
                except (ValueError, IndexError):
                    pass
            
            elif '吞吐量' in line and 'tasks/s' in line:
                # 提取吞吐量
                try:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if 'tasks/s' in part and i > 0:
                            throughput = float(parts[i-1])
                            metrics['throughput'] = throughput
                            break
                except (ValueError, IndexError):
                    pass
        
        return metrics
    
    async def generate_performance_report(self, results: Dict) -> Dict:
        """生成性能报告"""
        self.logger.info("📊 生成性能测试报告...")
        
        # 计算总体统计
        total_tests = sum(r.get('tests_run', 0) for r in results.values())
        total_passed = sum(r.get('tests_passed', 0) for r in results.values())
        total_failed = sum(r.get('tests_failed', 0) for r in results.values())
        total_duration = sum(r.get('duration', 0) for r in results.values())
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        # 收集性能指标
        performance_metrics = {}
        for suite_name, result in results.items():
            if 'performance_metrics' in result:
                performance_metrics[suite_name] = result['performance_metrics']
        
        # 分析性能趋势
        performance_analysis = await self.analyze_performance_metrics(performance_metrics)
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'duration': total_duration,
            'summary': {
                'total_suites': len(results),
                'successful_suites': sum(1 for r in results.values() if r.get('success')),
                'failed_suites': sum(1 for r in results.values() if not r.get('success')),
                'total_tests': total_tests,
                'tests_passed': total_passed,
                'tests_failed': total_failed,
                'success_rate': round(success_rate, 2)
            },
            'suites': results,
            'performance_metrics': performance_metrics,
            'performance_analysis': performance_analysis,
            'thresholds': self.performance_thresholds,
            'config': {k: str(v) for k, v in self.config.items()}
        }
        
        # 保存JSON报告
        report_file = self.config['output_dir'] / 'performance_test_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        # 生成HTML报告
        await self.generate_html_report(report)
        
        # 打印摘要
        self.print_performance_summary(report)
        
        return report
    
    async def analyze_performance_metrics(self, metrics: Dict) -> Dict:
        """分析性能指标"""
        analysis = {
            'threshold_violations': [],
            'performance_score': 0.0,
            'recommendations': []
        }
        
        # 检查阈值违规
        all_metrics = {}
        for suite_metrics in metrics.values():
            all_metrics.update(suite_metrics)
        
        for metric_name, value in all_metrics.items():
            if metric_name in self.performance_thresholds:
                threshold = self.performance_thresholds[metric_name]
                
                # 根据指标类型判断是否违规
                if metric_name in ['startup_time', 'api_response_time', 'db_query_time', 
                                 'memory_usage_limit', 'cpu_usage_limit', 'error_rate_limit', 
                                 'recovery_time_limit']:
                    # 这些指标越小越好
                    if value > threshold:
                        analysis['threshold_violations'].append({
                            'metric': metric_name,
                            'value': value,
                            'threshold': threshold,
                            'severity': 'high' if value > threshold * 1.5 else 'medium'
                        })
                elif metric_name in ['cache_hit_rate', 'throughput_min', 'concurrent_users']:
                    # 这些指标越大越好
                    if value < threshold:
                        analysis['threshold_violations'].append({
                            'metric': metric_name,
                            'value': value,
                            'threshold': threshold,
                            'severity': 'high' if value < threshold * 0.7 else 'medium'
                        })
        
        # 计算性能分数
        analysis['performance_score'] = self.calculate_performance_score(all_metrics)
        
        # 生成优化建议
        analysis['recommendations'] = self.generate_recommendations(analysis['threshold_violations'])
        
        return analysis
    
    def calculate_performance_score(self, metrics: Dict) -> float:
        """计算性能分数"""
        if not metrics:
            return 0.0
        
        score = 100.0
        
        # 根据各项指标调整分数
        for metric_name, value in metrics.items():
            if metric_name in self.performance_thresholds:
                threshold = self.performance_thresholds[metric_name]
                
                if metric_name in ['startup_time', 'api_response_time', 'db_query_time']:
                    # 时间类指标：超过阈值扣分
                    if value > threshold:
                        penalty = min((value / threshold - 1) * 20, 30)
                        score -= penalty
                elif metric_name in ['cache_hit_rate', 'throughput_min']:
                    # 效率类指标：低于阈值扣分
                    if value < threshold:
                        penalty = min((1 - value / threshold) * 20, 30)
                        score -= penalty
        
        return max(0.0, min(100.0, score))
    
    def generate_recommendations(self, violations: List[Dict]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        for violation in violations:
            metric = violation['metric']
            
            if metric == 'startup_time':
                recommendations.append("优化系统启动流程，考虑延迟加载非关键组件")
            elif metric == 'api_response_time':
                recommendations.append("优化API响应时间，考虑添加缓存或优化数据库查询")
            elif metric == 'db_query_time':
                recommendations.append("优化数据库查询，考虑添加索引或重构复杂查询")
            elif metric == 'cache_hit_rate':
                recommendations.append("提高缓存命中率，优化缓存策略和缓存键设计")
            elif metric == 'throughput_min':
                recommendations.append("提高系统吞吐量，考虑增加并发处理能力")
            elif metric == 'memory_usage_limit':
                recommendations.append("优化内存使用，检查内存泄漏并优化数据结构")
            elif metric == 'cpu_usage_limit':
                recommendations.append("优化CPU使用，检查CPU密集型操作并考虑异步处理")
        
        # 去重
        return list(set(recommendations))
    
    async def generate_html_report(self, report: Dict):
        """生成HTML性能报告"""
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; }
        .metric.good { background: #d4edda; }
        .metric.warning { background: #fff3cd; }
        .metric.danger { background: #f8d7da; }
        .suite { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .suite.success { border-color: #28a745; }
        .suite.failure { border-color: #dc3545; }
        .recommendations { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .violations { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚡ 性能测试报告</h1>
        <p>生成时间: {timestamp}</p>
        <p>总耗时: {duration:.2f} 秒</p>
        <p>性能分数: {performance_score:.1f}/100</p>
    </div>
    
    <div class="summary">
        <div class="metric {success_class}">
            <h3>成功率</h3>
            <div style="font-size: 2em;">{success_rate}%</div>
        </div>
        <div class="metric">
            <h3>测试套件</h3>
            <div>{successful_suites}/{total_suites}</div>
        </div>
        <div class="metric">
            <h3>测试用例</h3>
            <div>{tests_passed}/{total_tests}</div>
        </div>
        <div class="metric {violations_class}">
            <h3>阈值违规</h3>
            <div>{violations_count}</div>
        </div>
    </div>
    
    {violations_html}
    
    {recommendations_html}
    
    <h2>📋 测试套件详情</h2>
    {suites_html}
</body>
</html>
        """
        
        # 获取性能分析数据
        analysis = report.get('performance_analysis', {})
        violations = analysis.get('threshold_violations', [])
        recommendations = analysis.get('recommendations', [])
        performance_score = analysis.get('performance_score', 0.0)
        
        # 生成违规信息HTML
        violations_html = ""
        if violations:
            violations_html = '<div class="violations"><h3>⚠️ 性能阈值违规</h3><ul>'
            for violation in violations:
                violations_html += f'<li>{violation["metric"]}: {violation["value"]} (阈值: {violation["threshold"]})</li>'
            violations_html += '</ul></div>'
        
        # 生成建议HTML
        recommendations_html = ""
        if recommendations:
            recommendations_html = '<div class="recommendations"><h3>💡 优化建议</h3><ul>'
            for rec in recommendations:
                recommendations_html += f'<li>{rec}</li>'
            recommendations_html += '</ul></div>'
        
        # 生成套件HTML
        suites_html = ""
        for suite_name, result in report['suites'].items():
            suite_class = "success" if result.get('success') else "failure"
            status_icon = "✅" if result.get('success') else "❌"
            
            suites_html += f"""
            <div class="suite {suite_class}">
                <h3>{status_icon} {suite_name}</h3>
                <p>耗时: {result.get('duration', 0):.2f} 秒</p>
                <p>测试用例: {result.get('tests_run', 0)} 个</p>
                <p>通过: {result.get('tests_passed', 0)} 个</p>
                <p>失败: {result.get('tests_failed', 0)} 个</p>
                {f'<p style="color: red;">错误: {result.get("error", "")}</p>' if result.get('error') else ''}
            </div>
            """
        
        # 填充模板
        html_content = html_template.format(
            timestamp=report['timestamp'],
            duration=report['duration'],
            performance_score=performance_score,
            success_rate=report['summary']['success_rate'],
            success_class='good' if report['summary']['success_rate'] >= 90 else 'warning' if report['summary']['success_rate'] >= 70 else 'danger',
            successful_suites=report['summary']['successful_suites'],
            total_suites=report['summary']['total_suites'],
            tests_passed=report['summary']['tests_passed'],
            total_tests=report['summary']['total_tests'],
            violations_count=len(violations),
            violations_class='danger' if len(violations) > 0 else 'good',
            violations_html=violations_html,
            recommendations_html=recommendations_html,
            suites_html=suites_html
        )
        
        # 保存HTML报告
        html_file = self.config['output_dir'] / 'performance_test_report.html'
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"📄 HTML报告已生成: {html_file}")
    
    def print_performance_summary(self, report: Dict):
        """打印性能摘要"""
        summary = report['summary']
        analysis = report.get('performance_analysis', {})
        
        logger.info("\n" + "="*60)
        logger.info("⚡ 性能测试摘要")
        logger.info("="*60)
        logger.info(f"📊 总体统计:")
        logger.info(f"   测试套件: {summary['successful_suites']}/{summary['total_suites']} 成功")
        logger.info(f"   测试用例: {summary['tests_passed']}/{summary['total_tests']} 通过")
        logger.info(f"   成功率: {summary['success_rate']}%")
        logger.info(f"   总耗时: {report['duration']:.2f} 秒")
        logger.info(f"   性能分数: {analysis.get('performance_score', 0):.1f}/100")
        
        # 显示阈值违规
        violations = analysis.get('threshold_violations', [])
        if violations:
            logger.info(f"\n⚠️ 性能阈值违规 ({len(violations)} 项):")
            for violation in violations:
                logger.info(f"   {violation['metric']}: {violation['value']} (阈值: {violation['threshold']})")
        
        # 显示优化建议
        recommendations = analysis.get('recommendations', [])
        if recommendations:
            logger.info(f"\n💡 优化建议:")
            for i, rec in enumerate(recommendations, 1):
                logger.info(f"   {i}. {rec}")
        
        logger.info("\n📋 套件详情:")
        for suite_name, result in report['suites'].items():
            status = "✅" if result.get('success') else "❌"
            logger.info(f"   {status} {suite_name}: {result.get('tests_passed', 0)}/{result.get('tests_run', 0)} 通过")
        
        logger.info("="*60)
    
    async def compare_with_baseline(self, report: Dict):
        """与基线对比"""
        baseline_file = self.config['output_dir'] / 'performance_baseline.json'
        
        if not baseline_file.exists():
            self.logger.warning("⚠️ 未找到性能基线文件，跳过对比")
            return
        
        self.logger.info("📈 与性能基线对比...")
        
        try:
            with open(baseline_file, 'r', encoding='utf-8') as f:
                baseline = json.load(f)
            
            comparison = await self.perform_baseline_comparison(report, baseline)
            
            # 保存对比结果
            comparison_file = self.config['output_dir'] / 'performance_comparison.json'
            with open(comparison_file, 'w', encoding='utf-8') as f:
                json.dump(comparison, f, indent=2, ensure_ascii=False, default=str)
            
            # 打印对比结果
            self.print_comparison_summary(comparison)
            
        except Exception as e:
            self.logger.error(f"❌ 基线对比失败: {e}")
    
    async def perform_baseline_comparison(self, current: Dict, baseline: Dict) -> Dict:
        """执行基线对比"""
        comparison = {
            'timestamp': datetime.now().isoformat(),
            'baseline_timestamp': baseline.get('timestamp'),
            'metrics_comparison': {},
            'performance_regression': False,
            'improvements': [],
            'regressions': []
        }
        
        # 对比性能指标
        current_metrics = {}
        baseline_metrics = {}
        
        # 收集当前指标
        for suite_metrics in current.get('performance_metrics', {}).values():
            current_metrics.update(suite_metrics)
        
        # 收集基线指标
        for suite_metrics in baseline.get('performance_metrics', {}).values():
            baseline_metrics.update(suite_metrics)
        
        # 对比各项指标
        for metric_name in set(current_metrics.keys()) | set(baseline_metrics.keys()):
            current_value = current_metrics.get(metric_name)
            baseline_value = baseline_metrics.get(metric_name)
            
            if current_value is not None and baseline_value is not None:
                change_percent = (current_value - baseline_value) / baseline_value * 100
                
                comparison['metrics_comparison'][metric_name] = {
                    'current': current_value,
                    'baseline': baseline_value,
                    'change_percent': change_percent,
                    'improved': self.is_improvement(metric_name, change_percent)
                }
                
                # 记录改进和回归
                if abs(change_percent) > 5:  # 变化超过5%才记录
                    if self.is_improvement(metric_name, change_percent):
                        comparison['improvements'].append({
                            'metric': metric_name,
                            'change_percent': change_percent
                        })
                    else:
                        comparison['regressions'].append({
                            'metric': metric_name,
                            'change_percent': change_percent
                        })
                        comparison['performance_regression'] = True
        
        return comparison
    
    def is_improvement(self, metric_name: str, change_percent: float) -> bool:
        """判断指标变化是否为改进"""
        # 对于时间类指标，减少是改进
        if metric_name in ['startup_time', 'api_response_time', 'db_query_time', 'recovery_time_limit']:
            return change_percent < 0
        
        # 对于效率类指标，增加是改进
        if metric_name in ['cache_hit_rate', 'throughput_min']:
            return change_percent > 0
        
        # 对于使用率类指标，减少是改进
        if metric_name in ['memory_usage_limit', 'cpu_usage_limit', 'error_rate_limit']:
            return change_percent < 0
        
        return False
    
    def print_comparison_summary(self, comparison: Dict):
        """打印对比摘要"""
        logger.info("\n" + "="*60)
        logger.info("📈 性能基线对比")
        logger.info("="*60)
        
        improvements = comparison.get('improvements', [])
        regressions = comparison.get('regressions', [])
        
        if improvements:
            logger.info(f"✅ 性能改进 ({len(improvements)} 项):")
            for improvement in improvements:
                logger.info(f"   {improvement['metric']}: {improvement['change_percent']:+.1f}%")
        
        if regressions:
            logger.info(f"❌ 性能回归 ({len(regressions)} 项):")
            for regression in regressions:
                logger.info(f"   {regression['metric']}: {regression['change_percent']:+.1f}%")
        
        if not improvements and not regressions:
            logger.info("➡️ 性能无显著变化")
        
        logger.info("="*60)
    
    async def save_baseline(self, report: Dict):
        """保存性能基线"""
        baseline_file = self.config['output_dir'] / 'performance_baseline.json'
        
        self.logger.info(f"💾 保存性能基线: {baseline_file}")
        
        with open(baseline_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    async def cleanup_test_environment(self):
        """清理测试环境"""
        self.logger.info("🧹 清理测试环境...")
        
        # 清理环境变量
        env_vars_to_clean = ['TEST_MODE', 'TEST_OUTPUT_DIR', 'PERFORMANCE_TEST']
        
        for env_var in env_vars_to_clean:
            os.environ.pop(env_var, None)
        
        self.logger.info("✅ 测试环境清理完成")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='性能测试自动化执行脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--benchmark', '-b',
        action='store_true',
        help='执行性能基准测试'
    )
    
    parser.add_argument(
        '--stability', '-s',
        action='store_true',
        help='执行稳定性测试'
    )
    
    parser.add_argument(
        '--resource', '-r',
        action='store_true',
        help='执行资源使用测试'
    )
    
    parser.add_argument(
        '--scalability',
        action='store_true',
        help='执行可扩展性测试'
    )
    
    parser.add_argument(
        '--all', '-a',
        action='store_true',
        help='执行所有性能测试'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=Path,
        default=Path('reports/performance'),
        help='输出目录 (默认: reports/performance)'
    )
    
    parser.add_argument(
        '--baseline',
        action='store_true',
        help='设置性能基线'
    )
    
    parser.add_argument(
        '--compare',
        action='store_true',
        help='与基线对比'
    )
    
    parser.add_argument(
        '--timeout',
        type=int,
        default=3600,
        help='测试超时时间（秒，默认: 3600）'
    )
    
    return parser.parse_args()


async def main():
    """主函数"""
    args = parse_arguments()
    
    # 配置
    config = {
        'benchmark': args.benchmark,
        'stability': args.stability,
        'resource': args.resource,
        'scalability': args.scalability,
        'all': args.all,
        'output_dir': args.output,
        'baseline': args.baseline,
        'compare': args.compare,
        'timeout': args.timeout
    }
    
    # 创建性能测试运行器
    runner = PerformanceTestRunner(config)
    
    try:
        # 执行性能测试
        report = await runner.run_performance_tests()
        
        # 根据测试结果设置退出码
        analysis = report.get('performance_analysis', {})
        violations = analysis.get('threshold_violations', [])
        
        if report['summary']['tests_failed'] > 0:
            sys.exit(1)
        elif len(violations) > 0:
            logger.info("⚠️ 存在性能阈值违规，请查看报告")
            sys.exit(2)
        else:
            sys.exit(0)
            
    except KeyboardInterrupt:
        logger.info("\n⚠️ 测试被用户中断")
        sys.exit(130)
    except Exception as e:
        logger.info(f"❌ 性能测试执行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())