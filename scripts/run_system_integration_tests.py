#!/usr/bin/env python3
"""
系统集成测试自动化执行脚本

本脚本用于自动化执行综合系统修复方案的集成测试，
包括测试环境准备、测试执行、结果收集和报告生成。

功能特性：
1. 自动化测试环境设置
2. 并行测试执行
3. 实时测试进度监控
4. 详细的测试报告生成
5. 测试失败自动重试
6. 测试覆盖率统计

使用方法：
    python scripts/run_system_integration_tests.py [options]

选项：
    --verbose, -v       详细输出模式
    --parallel, -p      并行执行测试
    --retry, -r         失败重试次数 (默认: 3)
    --output, -o        输出目录 (默认: reports/integration)
    --coverage          生成覆盖率报告
    --filter            测试过滤器 (例如: test_system_integration)

作者: 系统修复团队
版本: 1.0.0
创建日期: 2025-01-11
"""

import os
import sys
import argparse
import asyncio
import subprocess
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class IntegrationTestRunner:
    """集成测试运行器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
        # 设置日志
        self.setup_logging()
        
        # 测试套件定义
        self.test_suites = {
            'system_integration': {
                'path': 'tests/integration/test_comprehensive_system_integration.py',
                'description': '系统集成测试',
                'priority': 1,
                'timeout': 300
            },
            'component_interaction': {
                'path': 'tests/integration/test_comprehensive_system_integration.py::TestComponentInteractionScenarios',
                'description': '组件交互测试',
                'priority': 2,
                'timeout': 180
            },
            'startup_shutdown': {
                'path': 'tests/integration/test_system_startup_shutdown.py',
                'description': '启动关闭测试',
                'priority': 3,
                'timeout': 120
            },
            'error_handling': {
                'path': 'tests/integration/test_error_handling_integration.py',
                'description': '错误处理集成测试',
                'priority': 4,
                'timeout': 150
            },
            'performance_integration': {
                'path': 'tests/integration/test_performance_system_integration.py',
                'description': '性能系统集成测试',
                'priority': 5,
                'timeout': 240
            }
        }
    
    def setup_logging(self):
        """设置日志配置"""
        log_level = logging.DEBUG if self.config.get('verbose') else logging.INFO
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(
                    self.config['output_dir'] / 'integration_test.log'
                )
            ]
        )
        
        self.logger = logging.getLogger(__name__)
    
    async def run_all_tests(self) -> Dict:
        """运行所有集成测试"""
        self.logger.info("🚀 开始执行系统集成测试...")
        self.start_time = datetime.now()
        
        try:
            # 准备测试环境
            await self.prepare_test_environment()
            
            # 执行测试套件
            if self.config.get('parallel'):
                results = await self.run_tests_parallel()
            else:
                results = await self.run_tests_sequential()
            
            # 生成测试报告
            report = await self.generate_test_report(results)
            
            # 清理测试环境
            await self.cleanup_test_environment()
            
            self.end_time = datetime.now()
            self.logger.info(f"✅ 集成测试完成，总耗时: {self.end_time - self.start_time}")
            
            return report
            
        except Exception as e:
            self.logger.error(f"❌ 集成测试执行失败: {e}")
            raise
    
    async def prepare_test_environment(self):
        """准备测试环境"""
        self.logger.info("🔧 准备测试环境...")
        
        # 创建输出目录
        self.config['output_dir'].mkdir(parents=True, exist_ok=True)
        
        # 创建临时测试目录
        test_temp_dir = self.config['output_dir'] / 'temp'
        test_temp_dir.mkdir(exist_ok=True)
        
        # 设置环境变量
        os.environ['TEST_MODE'] = 'integration'
        os.environ['TEST_OUTPUT_DIR'] = str(self.config['output_dir'])
        os.environ['TEST_TEMP_DIR'] = str(test_temp_dir)
        
        # 检查依赖
        await self.check_test_dependencies()
        
        self.logger.info("✅ 测试环境准备完成")
    
    async def check_test_dependencies(self):
        """检查测试依赖"""
        self.logger.info("🔍 检查测试依赖...")
        
        required_modules = [
            'pytest',
            'pytest-asyncio',
            'pytest-cov',
            'pytest-xdist'
        ]
        
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module.replace('-', '_'))
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            self.logger.error(f"❌ 缺少必要的测试依赖: {missing_modules}")
            raise RuntimeError(f"请安装缺少的依赖: pip install {' '.join(missing_modules)}")
        
        self.logger.info("✅ 测试依赖检查通过")
    
    async def run_tests_sequential(self) -> Dict:
        """顺序执行测试"""
        self.logger.info("📋 顺序执行测试套件...")
        
        results = {}
        
        # 按优先级排序测试套件
        sorted_suites = sorted(
            self.test_suites.items(),
            key=lambda x: x[1]['priority']
        )
        
        for suite_name, suite_config in sorted_suites:
            if self.should_run_test(suite_name):
                self.logger.info(f"🧪 执行测试套件: {suite_config['description']}")
                
                result = await self.run_single_test_suite(suite_name, suite_config)
                results[suite_name] = result
                
                # 如果关键测试失败，可能需要停止后续测试
                if not result['success'] and suite_config['priority'] <= 2:
                    self.logger.warning(f"⚠️ 关键测试套件失败: {suite_name}")
                    if not self.config.get('continue_on_failure'):
                        break
        
        return results
    
    async def run_tests_parallel(self) -> Dict:
        """并行执行测试"""
        self.logger.info("⚡ 并行执行测试套件...")
        
        # 创建测试任务
        test_tasks = []
        
        for suite_name, suite_config in self.test_suites.items():
            if self.should_run_test(suite_name):
                task = asyncio.create_task(
                    self.run_single_test_suite(suite_name, suite_config)
                )
                test_tasks.append((suite_name, task))
        
        # 等待所有测试完成
        results = {}
        
        for suite_name, task in test_tasks:
            try:
                result = await task
                results[suite_name] = result
            except Exception as e:
                self.logger.error(f"❌ 测试套件 {suite_name} 执行异常: {e}")
                results[suite_name] = {
                    'success': False,
                    'error': str(e),
                    'duration': 0,
                    'tests_run': 0,
                    'tests_passed': 0,
                    'tests_failed': 1
                }
        
        return results
    
    async def run_single_test_suite(self, suite_name: str, suite_config: Dict) -> Dict:
        """运行单个测试套件"""
        start_time = time.time()
        
        try:
            # 构建pytest命令
            cmd = self.build_pytest_command(suite_config)
            
            self.logger.debug(f"执行命令: {' '.join(cmd)}")
            
            # 执行测试
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=project_root
            )
            
            # 等待测试完成（带超时）
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=suite_config.get('timeout', 300)
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                raise TimeoutError(f"测试套件 {suite_name} 执行超时")
            
            # 解析测试结果
            result = self.parse_test_output(stdout.decode(), stderr.decode())
            result['duration'] = time.time() - start_time
            result['success'] = process.returncode == 0
            
            if result['success']:
                self.logger.info(f"✅ 测试套件 {suite_name} 执行成功")
            else:
                self.logger.error(f"❌ 测试套件 {suite_name} 执行失败")
                
                # 如果启用重试
                if self.config.get('retry', 0) > 0:
                    result = await self.retry_failed_test(suite_name, suite_config, result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 测试套件 {suite_name} 执行异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'duration': time.time() - start_time,
                'tests_run': 0,
                'tests_passed': 0,
                'tests_failed': 1
            }
    
    def build_pytest_command(self, suite_config: Dict) -> List[str]:
        """构建pytest命令"""
        cmd = ['python', '-m', 'pytest']
        
        # 添加测试路径
        cmd.append(suite_config['path'])
        
        # 添加选项
        cmd.extend(['-v', '--tb=short'])
        
        # 如果启用覆盖率
        if self.config.get('coverage'):
            cmd.extend([
                '--cov=src',
                '--cov-report=html',
                f'--cov-report=html:{self.config["output_dir"]}/coverage',
                '--cov-report=term-missing'
            ])
        
        # 添加输出格式
        cmd.extend([
            '--junit-xml',
            str(self.config['output_dir'] / f'junit_{suite_config["path"].split("/")[-1]}.xml')
        ])
        
        return cmd
    
    def parse_test_output(self, stdout: str, stderr: str) -> Dict:
        """解析测试输出"""
        result = {
            'tests_run': 0,
            'tests_passed': 0,
            'tests_failed': 0,
            'tests_skipped': 0,
            'output': stdout,
            'errors': stderr
        }
        
        # 解析pytest输出
        lines = stdout.split('\n')
        
        for line in lines:
            if 'passed' in line and 'failed' in line:
                # 解析类似 "5 passed, 2 failed in 10.5s" 的行
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == 'passed' and i > 0:
                        result['tests_passed'] = int(parts[i-1])
                    elif part == 'failed' and i > 0:
                        result['tests_failed'] = int(parts[i-1])
                    elif part == 'skipped' and i > 0:
                        result['tests_skipped'] = int(parts[i-1])
        
        result['tests_run'] = (
            result['tests_passed'] + 
            result['tests_failed'] + 
            result['tests_skipped']
        )
        
        return result
    
    async def retry_failed_test(self, suite_name: str, suite_config: Dict, 
                               original_result: Dict) -> Dict:
        """重试失败的测试"""
        retry_count = self.config.get('retry', 0)
        
        for attempt in range(1, retry_count + 1):
            self.logger.info(f"🔄 重试测试套件 {suite_name} (第 {attempt}/{retry_count} 次)")
            
            # 等待一段时间再重试
            await asyncio.sleep(2 ** attempt)  # 指数退避
            
            result = await self.run_single_test_suite(suite_name, suite_config)
            
            if result['success']:
                self.logger.info(f"✅ 测试套件 {suite_name} 重试成功")
                return result
        
        self.logger.error(f"❌ 测试套件 {suite_name} 重试 {retry_count} 次后仍然失败")
        return original_result
    
    def should_run_test(self, suite_name: str) -> bool:
        """判断是否应该运行测试"""
        test_filter = self.config.get('filter')
        
        if test_filter:
            return test_filter.lower() in suite_name.lower()
        
        return True
    
    async def generate_test_report(self, results: Dict) -> Dict:
        """生成测试报告"""
        self.logger.info("📊 生成测试报告...")
        
        # 计算总体统计
        total_tests = sum(r.get('tests_run', 0) for r in results.values())
        total_passed = sum(r.get('tests_passed', 0) for r in results.values())
        total_failed = sum(r.get('tests_failed', 0) for r in results.values())
        total_skipped = sum(r.get('tests_skipped', 0) for r in results.values())
        total_duration = sum(r.get('duration', 0) for r in results.values())
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'duration': total_duration,
            'summary': {
                'total_suites': len(results),
                'successful_suites': sum(1 for r in results.values() if r.get('success')),
                'failed_suites': sum(1 for r in results.values() if not r.get('success')),
                'total_tests': total_tests,
                'tests_passed': total_passed,
                'tests_failed': total_failed,
                'tests_skipped': total_skipped,
                'success_rate': round(success_rate, 2)
            },
            'suites': results,
            'config': self.config
        }
        
        # 保存JSON报告
        report_file = self.config['output_dir'] / 'integration_test_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        # 生成HTML报告
        await self.generate_html_report(report)
        
        # 打印摘要
        self.print_test_summary(report)
        
        return report
    
    async def generate_html_report(self, report: Dict):
        """生成HTML测试报告"""
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统集成测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; }
        .metric.success { background: #d4edda; }
        .metric.failure { background: #f8d7da; }
        .suite { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .suite.success { border-color: #28a745; }
        .suite.failure { border-color: #dc3545; }
        .details { margin-top: 10px; font-size: 0.9em; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 系统集成测试报告</h1>
        <p>生成时间: {timestamp}</p>
        <p>总耗时: {duration:.2f} 秒</p>
    </div>
    
    <div class="summary">
        <div class="metric {success_class}">
            <h3>成功率</h3>
            <div style="font-size: 2em;">{success_rate}%</div>
        </div>
        <div class="metric">
            <h3>测试套件</h3>
            <div>{successful_suites}/{total_suites}</div>
        </div>
        <div class="metric">
            <h3>测试用例</h3>
            <div>{tests_passed}/{total_tests}</div>
        </div>
        <div class="metric {failure_class}">
            <h3>失败用例</h3>
            <div>{tests_failed}</div>
        </div>
    </div>
    
    <h2>📋 测试套件详情</h2>
    {suites_html}
</body>
</html>
        """
        
        # 生成套件HTML
        suites_html = ""
        for suite_name, result in report['suites'].items():
            suite_class = "success" if result.get('success') else "failure"
            status_icon = "✅" if result.get('success') else "❌"
            
            suites_html += f"""
            <div class="suite {suite_class}">
                <h3>{status_icon} {suite_name}</h3>
                <div class="details">
                    <p>耗时: {result.get('duration', 0):.2f} 秒</p>
                    <p>测试用例: {result.get('tests_run', 0)} 个</p>
                    <p>通过: {result.get('tests_passed', 0)} 个</p>
                    <p>失败: {result.get('tests_failed', 0)} 个</p>
                    <p>跳过: {result.get('tests_skipped', 0)} 个</p>
                    {f'<p style="color: red;">错误: {result.get("error", "")}</p>' if result.get('error') else ''}
                </div>
            </div>
            """
        
        # 填充模板
        html_content = html_template.format(
            timestamp=report['timestamp'],
            duration=report['duration'],
            success_rate=report['summary']['success_rate'],
            success_class='success' if report['summary']['success_rate'] >= 80 else 'failure',
            failure_class='failure' if report['summary']['tests_failed'] > 0 else '',
            successful_suites=report['summary']['successful_suites'],
            total_suites=report['summary']['total_suites'],
            tests_passed=report['summary']['tests_passed'],
            total_tests=report['summary']['total_tests'],
            tests_failed=report['summary']['tests_failed'],
            suites_html=suites_html
        )
        
        # 保存HTML报告
        html_file = self.config['output_dir'] / 'integration_test_report.html'
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"📄 HTML报告已生成: {html_file}")
    
    def print_test_summary(self, report: Dict):
        """打印测试摘要"""
        summary = report['summary']
        
        logger.info("\n" + "="*60)
        logger.info("🧪 系统集成测试摘要")
        logger.info("="*60)
        logger.info(f"📊 总体统计:")
        logger.info(f"   测试套件: {summary['successful_suites']}/{summary['total_suites']} 成功")
        logger.info(f"   测试用例: {summary['tests_passed']}/{summary['total_tests']} 通过")
        logger.info(f"   成功率: {summary['success_rate']}%")
        logger.info(f"   总耗时: {report['duration']:.2f} 秒")
        
        if summary['tests_failed'] > 0:
            logger.info(f"❌ 失败用例: {summary['tests_failed']} 个")
        
        if summary['tests_skipped'] > 0:
            logger.info(f"⏭️  跳过用例: {summary['tests_skipped']} 个")
        
        logger.info("\n📋 套件详情:")
        for suite_name, result in report['suites'].items():
            status = "✅" if result.get('success') else "❌"
            logger.info(f"   {status} {suite_name}: {result.get('tests_passed', 0)}/{result.get('tests_run', 0)} 通过")
        
        logger.info("="*60)
    
    async def cleanup_test_environment(self):
        """清理测试环境"""
        self.logger.info("🧹 清理测试环境...")
        
        # 清理临时文件
        test_temp_dir = self.config['output_dir'] / 'temp'
        if test_temp_dir.exists():
            import shutil
            shutil.rmtree(test_temp_dir, ignore_errors=True)
        
        # 清理环境变量
        for env_var in ['TEST_MODE', 'TEST_OUTPUT_DIR', 'TEST_TEMP_DIR']:
            os.environ.pop(env_var, None)
        
        self.logger.info("✅ 测试环境清理完成")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='系统集成测试自动化执行脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出模式'
    )
    
    parser.add_argument(
        '--parallel', '-p',
        action='store_true',
        help='并行执行测试'
    )
    
    parser.add_argument(
        '--retry', '-r',
        type=int,
        default=0,
        help='失败重试次数 (默认: 0)'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=Path,
        default=Path('reports/integration'),
        help='输出目录 (默认: reports/integration)'
    )
    
    parser.add_argument(
        '--coverage',
        action='store_true',
        help='生成覆盖率报告'
    )
    
    parser.add_argument(
        '--filter',
        type=str,
        help='测试过滤器 (例如: system_integration)'
    )
    
    parser.add_argument(
        '--continue-on-failure',
        action='store_true',
        help='关键测试失败后继续执行'
    )
    
    return parser.parse_args()


async def main():
    """主函数"""
    args = parse_arguments()
    
    # 配置
    config = {
        'verbose': args.verbose,
        'parallel': args.parallel,
        'retry': args.retry,
        'output_dir': args.output,
        'coverage': args.coverage,
        'filter': args.filter,
        'continue_on_failure': args.continue_on_failure
    }
    
    # 创建测试运行器
    runner = IntegrationTestRunner(config)
    
    try:
        # 执行测试
        report = await runner.run_all_tests()
        
        # 根据测试结果设置退出码
        if report['summary']['tests_failed'] > 0:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except KeyboardInterrupt:
        logger.info("\n⚠️ 测试被用户中断")
        sys.exit(130)
    except Exception as e:
        logger.info(f"❌ 测试执行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())