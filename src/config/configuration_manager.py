"""
Configuration Management System
Comprehensive configuration manager for system-wide configuration handling.
"""

import os
import json
import yaml
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum

from src.common.utils.logging import get_logger


class ConfigFormat(Enum):
    """Supported configuration formats."""
    YAML = "yaml"
    JSON = "json"
    ENV = "env"
    INI = "ini"


class ValidationSeverity(Enum):
    """Configuration validation severity levels."""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


@dataclass
class ValidationResult:
    """Configuration validation result."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    info: List[str] = field(default_factory=list)
    
    def add_issue(self, severity: ValidationSeverity, message: str):
        """Add validation issue."""
        if severity == ValidationSeverity.ERROR:
            self.errors.append(message)
            self.is_valid = False
        elif severity == ValidationSeverity.WARNING:
            self.warnings.append(message)
        else:
            self.info.append(message)
    
    def has_issues(self) -> bool:
        """Check if there are any validation issues."""
        return bool(self.errors or self.warnings)
    
    def get_summary(self) -> str:
        """Get validation summary."""
        summary = []
        if self.errors:
            summary.append(f"Errors: {len(self.errors)}")
        if self.warnings:
            summary.append(f"Warnings: {len(self.warnings)}")
        if self.info:
            summary.append(f"Info: {len(self.info)}")
        return ", ".join(summary) if summary else "No issues"


@dataclass
class ConfigBackup:
    """Configuration backup information."""
    timestamp: datetime
    backup_path: Path
    config_files: List[str]
    description: str = ""


class ConfigurationManager:
    """
    Comprehensive configuration management system.
    
    Handles validation, loading, backup, and repair of system configurations.
    Supports multiple configuration formats and environments.
    """
    
    def __init__(self, config_root: str = "config", environment: str = None):
        """
        Initialize configuration manager.
        
        Args:
            config_root: Root directory for configuration files
            environment: Current environment (development, production, etc.)
        """
        self.logger = get_logger("configuration_manager")
        self.config_root = Path(config_root)
        self.environment = environment or os.getenv('TRADING_ENV', 'development')
        
        # Configuration storage
        self._configs: Dict[str, Any] = {}
        self._config_files: Dict[str, Path] = {}
        self._backup_history: List[ConfigBackup] = []
        
        # Validation rules
        self._validation_rules: Dict[str, Dict[str, Any]] = {}
        
        # Initialize components
        from .config_validator import ConfigValidator
        from .config_loader import ConfigLoader
        from .config_generator import ConfigGenerator
        
        self.validator = ConfigValidator(self)
        self.loader = ConfigLoader(self)
        self.generator = ConfigGenerator(self)
        
        # Ensure config directories exist
        self._ensure_directories()
        
        # Load initial configurations
        self._load_initial_configs()
        
        self.logger.info(f"Configuration manager initialized for environment: {self.environment}")
    
    def _ensure_directories(self):
        """Ensure all required configuration directories exist."""
        directories = [
            self.config_root,
            self.config_root / "core",
            self.config_root / "environments",
            self.config_root / "datasources",
            self.config_root / "templates",
            self.config_root / "backups",
            self.config_root / "user",
            self.config_root / "security",
            self.config_root / "logging",
            self.config_root / "monitoring"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"Ensured directory exists: {directory}")
    
    def _load_initial_configs(self):
        """Load initial configuration files."""
        try:
            # Load core system configuration
            self._load_config_file("core/system.yaml", "system")
            
            # Load environment-specific configuration
            env_config_path = f"environments/{self.environment}.yaml"
            self._load_config_file(env_config_path, "environment")
            
            # Load data sources configuration
            self._load_config_file("datasources/sources.yaml", "datasources")
            
            # Load other core configurations
            config_files = [
                ("core/markets.yaml", "markets"),
                ("logging/logging_optimization.yaml", "logging"),
                ("monitoring/monitoring_optimization.yaml", "monitoring"),
                ("security/security.yaml", "security")
            ]
            
            for config_path, config_key in config_files:
                self._load_config_file(config_path, config_key, required=False)
                
        except Exception as e:
            self.logger.error(f"Failed to load initial configurations: {e}")
    
    def _load_config_file(self, relative_path: str, config_key: str, required: bool = True):
        """Load a specific configuration file."""
        config_path = self.config_root / relative_path
        
        if not config_path.exists():
            if required:
                self.logger.warning(f"Required config file not found: {config_path}")
            return
        
        try:
            config_data = self.loader.load_file(config_path)
            if config_data:
                self._configs[config_key] = config_data
                self._config_files[config_key] = config_path
                self.logger.debug(f"Loaded configuration: {config_key} from {config_path}")
        except Exception as e:
            self.logger.error(f"Failed to load config file {config_path}: {e}")
    
    def validate_all_configs(self) -> ValidationResult:
        """
        Validate all loaded configurations.
        
        Returns:
            ValidationResult: Comprehensive validation result
        """
        self.logger.info("Starting comprehensive configuration validation")
        
        overall_result = ValidationResult(is_valid=True)
        
        # Validate each configuration section
        for config_key, config_data in self._configs.items():
            try:
                result = self.validator.validate_config(config_key, config_data)
                
                # Merge results
                overall_result.errors.extend([f"{config_key}: {error}" for error in result.errors])
                overall_result.warnings.extend([f"{config_key}: {warning}" for warning in result.warnings])
                overall_result.info.extend([f"{config_key}: {info}" for info in result.info])
                
                if not result.is_valid:
                    overall_result.is_valid = False
                    
            except Exception as e:
                overall_result.add_issue(
                    ValidationSeverity.ERROR,
                    f"Failed to validate {config_key}: {str(e)}"
                )
        
        # Validate configuration dependencies
        dependency_result = self.validator.validate_dependencies(self._configs)
        overall_result.errors.extend(dependency_result.errors)
        overall_result.warnings.extend(dependency_result.warnings)
        
        if not dependency_result.is_valid:
            overall_result.is_valid = False
        
        # Log validation summary
        if overall_result.is_valid:
            self.logger.info("All configurations are valid")
        else:
            self.logger.error(f"Configuration validation failed: {overall_result.get_summary()}")
        
        return overall_result
    
    def fix_missing_configs(self) -> List[str]:
        """
        Fix missing configuration files by generating them from templates.
        
        Returns:
            List of fixed configuration files
        """
        self.logger.info("Checking for missing configurations")
        
        fixed_configs = []
        
        # Define required configuration files
        required_configs = [
            ("core/system.yaml", "system"),
            (f"environments/{self.environment}.yaml", "environment"),
            ("datasources/sources.yaml", "datasources"),
            ("core/markets.yaml", "markets"),
            ("logging/logging_optimization.yaml", "logging"),
            ("security/security.yaml", "security")
        ]
        
        for config_path, config_type in required_configs:
            full_path = self.config_root / config_path
            
            if not full_path.exists():
                self.logger.info(f"Missing configuration file: {config_path}")
                
                try:
                    # Generate missing configuration
                    generated_config = self.generator.generate_config(config_type, self.environment)
                    
                    if generated_config:
                        # Ensure parent directory exists
                        full_path.parent.mkdir(parents=True, exist_ok=True)
                        
                        # Write configuration file
                        self.loader.save_config(generated_config, full_path)
                        
                        # Load the new configuration
                        self._configs[config_type] = generated_config
                        self._config_files[config_type] = full_path
                        
                        fixed_configs.append(config_path)
                        self.logger.info(f"Generated missing configuration: {config_path}")
                        
                except Exception as e:
                    self.logger.error(f"Failed to generate configuration {config_path}: {e}")
        
        return fixed_configs
    
    def update_environment_configs(self, target_environment: str = None) -> bool:
        """
        Update environment-specific configurations.
        
        Args:
            target_environment: Target environment to update (defaults to current)
            
        Returns:
            True if update was successful
        """
        target_env = target_environment or self.environment
        
        self.logger.info(f"Updating environment configurations for: {target_env}")
        
        try:
            # Create backup before updating
            backup_info = self.backup_configurations(f"pre_env_update_{target_env}")
            
            # Generate updated environment configuration
            env_config = self.generator.generate_environment_config(target_env)
            
            if env_config:
                env_config_path = self.config_root / "environments" / f"{target_env}.yaml"
                
                # Save updated configuration
                self.loader.save_config(env_config, env_config_path)
                
                # If updating current environment, reload configuration
                if target_env == self.environment:
                    self._configs["environment"] = env_config
                    self._config_files["environment"] = env_config_path
                
                self.logger.info(f"Successfully updated environment configuration for: {target_env}")
                return True
            else:
                self.logger.error(f"Failed to generate environment configuration for: {target_env}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to update environment configurations: {e}")
            return False
    
    def backup_configurations(self, description: str = "") -> ConfigBackup:
        """
        Create a backup of all configuration files.
        
        Args:
            description: Optional description for the backup
            
        Returns:
            ConfigBackup information
        """
        timestamp = datetime.now()
        backup_name = f"config_backup_{timestamp.strftime('%Y%m%d_%H%M%S')}"
        backup_path = self.config_root / "backups" / backup_name
        
        self.logger.info(f"Creating configuration backup: {backup_name}")
        
        try:
            backup_path.mkdir(parents=True, exist_ok=True)
            
            backed_up_files = []
            
            # Backup all configuration files
            for config_key, config_file in self._config_files.items():
                if config_file.exists():
                    relative_path = config_file.relative_to(self.config_root)
                    backup_file_path = backup_path / relative_path
                    
                    # Ensure backup subdirectory exists
                    backup_file_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # Copy file to backup location
                    shutil.copy2(config_file, backup_file_path)
                    backed_up_files.append(str(relative_path))
            
            # Create backup metadata
            backup_info = ConfigBackup(
                timestamp=timestamp,
                backup_path=backup_path,
                config_files=backed_up_files,
                description=description
            )
            
            # Save backup metadata
            metadata_file = backup_path / "backup_metadata.json"
            with open(metadata_file, 'w') as f:
                json.dump({
                    'timestamp': timestamp.isoformat(),
                    'description': description,
                    'config_files': backed_up_files,
                    'environment': self.environment
                }, f, indent=2)
            
            self._backup_history.append(backup_info)
            
            self.logger.info(f"Configuration backup created successfully: {backup_path}")
            return backup_info
            
        except Exception as e:
            self.logger.error(f"Failed to create configuration backup: {e}")
            raise
    
    def restore_configurations(self, backup_path: Union[str, Path]) -> bool:
        """
        Restore configurations from a backup.
        
        Args:
            backup_path: Path to the backup directory
            
        Returns:
            True if restore was successful
        """
        backup_dir = Path(backup_path)
        
        if not backup_dir.exists():
            self.logger.error(f"Backup directory not found: {backup_dir}")
            return False
        
        self.logger.info(f"Restoring configurations from backup: {backup_dir}")
        
        try:
            # Load backup metadata
            metadata_file = backup_dir / "backup_metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                    backed_up_files = metadata.get('config_files', [])
            else:
                # Fallback: find all files in backup directory
                backed_up_files = [
                    str(f.relative_to(backup_dir))
                    for f in backup_dir.rglob('*')
                    if f.is_file() and f.name != 'backup_metadata.json'
                ]
            
            # Create backup of current configuration before restore
            self.backup_configurations("pre_restore_backup")
            
            # Restore each configuration file
            restored_files = []
            for relative_path in backed_up_files:
                backup_file = backup_dir / relative_path
                target_file = self.config_root / relative_path
                
                if backup_file.exists():
                    # Ensure target directory exists
                    target_file.parent.mkdir(parents=True, exist_ok=True)
                    
                    # Copy file from backup
                    shutil.copy2(backup_file, target_file)
                    restored_files.append(relative_path)
            
            # Reload configurations
            self._load_initial_configs()
            
            self.logger.info(f"Successfully restored {len(restored_files)} configuration files")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to restore configurations: {e}")
            return False
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value by key.
        
        Args:
            key: Configuration key (supports dot notation)
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        
        # Try to find in loaded configurations
        for config_section in self._configs.values():
            value = config_section
            
            try:
                for k in keys:
                    if isinstance(value, dict) and k in value:
                        value = value[k]
                    else:
                        value = None
                        break
                
                if value is not None:
                    return value
            except (KeyError, TypeError):
                continue
        
        return default
    
    def set_config(self, key: str, value: Any, config_section: str = "system") -> bool:
        """
        Set configuration value.
        
        Args:
            key: Configuration key (supports dot notation)
            value: Value to set
            config_section: Configuration section to update
            
        Returns:
            True if value was set successfully
        """
        try:
            if config_section not in self._configs:
                self._configs[config_section] = {}
            
            keys = key.split('.')
            config_dict = self._configs[config_section]
            
            # Navigate to the parent dictionary
            for k in keys[:-1]:
                if k not in config_dict:
                    config_dict[k] = {}
                config_dict = config_dict[k]
            
            # Set the value
            config_dict[keys[-1]] = value
            
            self.logger.debug(f"Set configuration: {key} = {value} in section {config_section}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to set configuration {key}: {e}")
            return False
    
    def get_all_configs(self) -> Dict[str, Any]:
        """Get all loaded configurations."""
        return self._configs.copy()
    
    def get_config_files(self) -> Dict[str, Path]:
        """Get all configuration file paths."""
        return self._config_files.copy()
    
    def get_backup_history(self) -> List[ConfigBackup]:
        """Get configuration backup history."""
        return self._backup_history.copy()
    
    def cleanup_old_backups(self, keep_count: int = 10) -> int:
        """
        Clean up old configuration backups.
        
        Args:
            keep_count: Number of recent backups to keep
            
        Returns:
            Number of backups removed
        """
        backup_dir = self.config_root / "backups"
        
        if not backup_dir.exists():
            return 0
        
        # Get all backup directories
        backup_dirs = [
            d for d in backup_dir.iterdir()
            if d.is_dir() and d.name.startswith('config_backup_')
        ]
        
        # Sort by creation time (newest first)
        backup_dirs.sort(key=lambda x: x.stat().st_ctime, reverse=True)
        
        # Remove old backups
        removed_count = 0
        for backup_path in backup_dirs[keep_count:]:
            try:
                shutil.rmtree(backup_path)
                removed_count += 1
                self.logger.debug(f"Removed old backup: {backup_path}")
            except Exception as e:
                self.logger.error(f"Failed to remove backup {backup_path}: {e}")
        
        if removed_count > 0:
            self.logger.info(f"Cleaned up {removed_count} old configuration backups")
        
        return removed_count
    
    def reload_configurations(self) -> bool:
        """
        Reload all configurations from files.
        
        Returns:
            True if reload was successful
        """
        self.logger.info("Reloading all configurations")
        
        try:
            # Clear current configurations
            self._configs.clear()
            self._config_files.clear()
            
            # Reload configurations
            self._load_initial_configs()
            
            self.logger.info("Successfully reloaded all configurations")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to reload configurations: {e}")
            return False
    
    def get_environment(self) -> str:
        """Get current environment."""
        return self.environment
    
    def set_environment(self, environment: str) -> bool:
        """
        Set current environment and reload configurations.
        
        Args:
            environment: New environment name
            
        Returns:
            True if environment was changed successfully
        """
        if environment == self.environment:
            return True
        
        self.logger.info(f"Changing environment from {self.environment} to {environment}")
        
        try:
            old_environment = self.environment
            self.environment = environment
            
            # Reload configurations for new environment
            success = self.reload_configurations()
            
            if not success:
                # Revert on failure
                self.environment = old_environment
                self.reload_configurations()
                return False
            
            self.logger.info(f"Successfully changed environment to: {environment}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to change environment: {e}")
            # Revert on error
            self.environment = old_environment
            return False