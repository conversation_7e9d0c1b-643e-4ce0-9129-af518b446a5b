"""
Configuration Validator
Validates configuration files for completeness, format, and dependencies.
"""

import re
import os
from typing import Dict, Any, List, Optional, Union, Callable, Set
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum

from src.common.utils.logging import get_logger


class FieldType(Enum):
    """Configuration field types."""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    LIST = "list"
    DICT = "dict"
    PATH = "path"
    URL = "url"
    EMAIL = "email"
    PORT = "port"
    IP_ADDRESS = "ip_address"


@dataclass
class ValidationRule:
    """Configuration validation rule."""
    field_path: str
    field_type: FieldType
    required: bool = True
    default_value: Any = None
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    min_length: Optional[int] = None
    max_length: Optional[int] = None
    pattern: Optional[str] = None
    allowed_values: Optional[List[Any]] = None
    custom_validator: Optional[Callable[[Any], bool]] = None
    description: str = ""
    dependencies: List[str] = field(default_factory=list)


@dataclass
class DependencyRule:
    """Configuration dependency rule."""
    source_field: str
    target_field: str
    condition: str  # "required_if", "forbidden_if", "equals", "not_equals"
    condition_value: Any = None
    description: str = ""


class ConfigValidator:
    """
    Configuration validator for comprehensive configuration validation.
    
    Validates configuration format, content, dependencies, and provides
    detailed error reporting and fix suggestions.
    """
    
    def __init__(self, config_manager=None):
        """
        Initialize configuration validator.
        
        Args:
            config_manager: Reference to configuration manager
        """
        self.logger = get_logger("config_validator")
        self.config_manager = config_manager
        
        # Validation rules for different configuration sections
        self._validation_rules: Dict[str, List[ValidationRule]] = {}
        self._dependency_rules: Dict[str, List[DependencyRule]] = {}
        
        # Initialize default validation rules
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """Initialize default validation rules for common configuration sections."""
        
        # System configuration rules
        system_rules = [
            ValidationRule("database.host", FieldType.STRING, required=True, description="Database host"),
            ValidationRule("database.port", FieldType.PORT, required=True, min_value=1, max_value=65535, description="Database port"),
            ValidationRule("database.database", FieldType.STRING, required=True, min_length=1, description="Database name"),
            ValidationRule("database.username", FieldType.STRING, required=True, min_length=1, description="Database username"),
            ValidationRule("database.password", FieldType.STRING, required=False, description="Database password"),
            ValidationRule("database.pool_size", FieldType.INTEGER, required=False, default_value=10, min_value=1, max_value=100, description="Database connection pool size"),
            ValidationRule("database.echo", FieldType.BOOLEAN, required=False, default_value=False, description="Database query logging"),
            
            ValidationRule("api.host", FieldType.IP_ADDRESS, required=True, description="API host address"),
            ValidationRule("api.port", FieldType.PORT, required=True, min_value=1, max_value=65535, description="API port"),
            ValidationRule("api.debug", FieldType.BOOLEAN, required=False, default_value=False, description="API debug mode"),
            ValidationRule("api.cors_origins", FieldType.LIST, required=False, description="CORS allowed origins"),
            ValidationRule("api.rate_limit", FieldType.STRING, required=False, pattern=r'^\d+/(second|minute|hour|day)$', description="API rate limit"),
            ValidationRule("api.auth_enabled", FieldType.BOOLEAN, required=False, default_value=True, description="API authentication enabled"),
            ValidationRule("api.jwt_secret", FieldType.STRING, required=True, min_length=32, description="JWT secret key"),
            ValidationRule("api.request_timeout", FieldType.INTEGER, required=False, default_value=30, min_value=1, max_value=300, description="API request timeout"),
            
            ValidationRule("logging.level", FieldType.STRING, required=False, default_value="INFO", 
                         allowed_values=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], description="Logging level"),
            ValidationRule("logging.format", FieldType.STRING, required=False, description="Log format string"),
            ValidationRule("logging.file_path", FieldType.PATH, required=False, description="Log file path"),
            ValidationRule("logging.max_file_size", FieldType.INTEGER, required=False, default_value=10485760, min_value=1024, description="Maximum log file size"),
            ValidationRule("logging.backup_count", FieldType.INTEGER, required=False, default_value=5, min_value=1, description="Log backup count"),
            
            ValidationRule("security.secret_key", FieldType.STRING, required=True, min_length=32, description="Application secret key"),
            ValidationRule("security.algorithm", FieldType.STRING, required=False, default_value="HS256", 
                         allowed_values=["HS256", "HS384", "HS512", "RS256"], description="JWT algorithm"),
            ValidationRule("security.access_token_expire_minutes", FieldType.INTEGER, required=False, default_value=30, min_value=1, description="Access token expiry"),
            ValidationRule("security.password_min_length", FieldType.INTEGER, required=False, default_value=8, min_value=4, description="Minimum password length"),
            ValidationRule("security.max_login_attempts", FieldType.INTEGER, required=False, default_value=5, min_value=1, description="Maximum login attempts"),
        ]
        
        self._validation_rules["system"] = system_rules
        
        # Environment configuration rules
        environment_rules = [
            ValidationRule("environment", FieldType.STRING, required=True, 
                         allowed_values=["development", "testing", "staging", "production"], description="Environment name"),
            ValidationRule("debug", FieldType.BOOLEAN, required=False, default_value=False, description="Debug mode"),
            ValidationRule("testing", FieldType.BOOLEAN, required=False, default_value=False, description="Testing mode"),
        ]
        
        self._validation_rules["environment"] = environment_rules
        
        # Data sources configuration rules
        datasources_rules = [
            ValidationRule("*.adapter_class", FieldType.STRING, required=True, description="Data source adapter class"),
            ValidationRule("*.enabled", FieldType.BOOLEAN, required=False, default_value=True, description="Data source enabled"),
            ValidationRule("*.priority", FieldType.INTEGER, required=False, default_value=1, min_value=1, description="Data source priority"),
            ValidationRule("*.rate_limit", FieldType.DICT, required=False, description="Rate limit configuration"),
            ValidationRule("*.credentials", FieldType.DICT, required=False, description="Data source credentials"),
        ]
        
        self._validation_rules["datasources"] = datasources_rules
        
        # Markets configuration rules - simplified for now
        markets_rules = [
            ValidationRule("default_market", FieldType.STRING, required=False, description="Default market"),
            ValidationRule("supported_asset_types", FieldType.LIST, required=False, description="Supported asset types"),
        ]
        
        self._validation_rules["markets"] = markets_rules
        
        # Initialize dependency rules
        self._initialize_dependency_rules()
    
    def _initialize_dependency_rules(self):
        """Initialize configuration dependency rules."""
        
        # System dependencies
        system_dependencies = [
            DependencyRule("api.auth_enabled", "api.jwt_secret", "required_if", True, 
                         "JWT secret is required when authentication is enabled"),
            DependencyRule("database.password", "database.username", "required_if", None,
                         "Database password should be provided when username is set"),
            DependencyRule("logging.file_path", "logging.max_file_size", "required_if", None,
                         "Max file size should be set when file logging is enabled"),
        ]
        
        self._dependency_rules["system"] = system_dependencies
    
    def add_validation_rule(self, config_section: str, rule: ValidationRule):
        """
        Add custom validation rule.
        
        Args:
            config_section: Configuration section name
            rule: Validation rule to add
        """
        if config_section not in self._validation_rules:
            self._validation_rules[config_section] = []
        
        self._validation_rules[config_section].append(rule)
        self.logger.debug(f"Added validation rule for {config_section}: {rule.field_path}")
    
    def add_dependency_rule(self, config_section: str, rule: DependencyRule):
        """
        Add custom dependency rule.
        
        Args:
            config_section: Configuration section name
            rule: Dependency rule to add
        """
        if config_section not in self._dependency_rules:
            self._dependency_rules[config_section] = []
        
        self._dependency_rules[config_section].append(rule)
        self.logger.debug(f"Added dependency rule for {config_section}: {rule.source_field} -> {rule.target_field}")
    
    def validate_config(self, config_section: str, config_data: Dict[str, Any]) -> 'ValidationResult':
        """
        Validate a configuration section.
        
        Args:
            config_section: Configuration section name
            config_data: Configuration data to validate
            
        Returns:
            ValidationResult: Validation result with errors and warnings
        """
        from .configuration_manager import ValidationResult, ValidationSeverity
        
        result = ValidationResult(is_valid=True)
        
        if not config_data:
            result.add_issue(ValidationSeverity.WARNING, f"Configuration section '{config_section}' is empty")
            return result
        
        # Get validation rules for this section
        rules = self._validation_rules.get(config_section, [])
        
        if not rules:
            result.add_issue(ValidationSeverity.INFO, f"No validation rules defined for section '{config_section}'")
            return result
        
        self.logger.debug(f"Validating configuration section '{config_section}' with {len(rules)} rules")
        
        # Validate each rule
        for rule in rules:
            try:
                self._validate_field(config_data, rule, result)
            except Exception as e:
                result.add_issue(ValidationSeverity.ERROR, f"Validation error for {rule.field_path}: {str(e)}")
        
        # Validate dependencies for this section
        if config_section in self._dependency_rules:
            self._validate_dependencies(config_data, self._dependency_rules[config_section], result)
        
        return result
    
    def _validate_field(self, config_data: Dict[str, Any], rule: ValidationRule, result: 'ValidationResult'):
        """Validate a single configuration field."""
        from .configuration_manager import ValidationSeverity
        
        field_path = rule.field_path
        
        # Handle wildcard patterns (e.g., "*.name")
        if '*' in field_path:
            self._validate_wildcard_field(config_data, rule, result)
            return
        
        # Get field value
        value = self._get_nested_value(config_data, field_path)
        
        # Check if field is required
        if value is None:
            if rule.required:
                result.add_issue(ValidationSeverity.ERROR, f"Required field '{field_path}' is missing")
                return
            elif rule.default_value is not None:
                result.add_issue(ValidationSeverity.INFO, f"Field '{field_path}' using default value: {rule.default_value}")
                return
            else:
                return  # Optional field not present
        
        # Validate field type
        if not self._validate_type(value, rule.field_type):
            result.add_issue(ValidationSeverity.ERROR, 
                           f"Field '{field_path}' has invalid type. Expected {rule.field_type.value}, got {type(value).__name__}")
            return
        
        # Validate field constraints
        self._validate_constraints(field_path, value, rule, result)
    
    def _validate_wildcard_field(self, config_data: Dict[str, Any], rule: ValidationRule, result: 'ValidationResult'):
        """Validate fields matching wildcard patterns."""
        from .configuration_manager import ValidationSeverity
        
        field_path = rule.field_path
        parts = field_path.split('.')
        
        if len(parts) < 2 or parts[0] != '*':
            result.add_issue(ValidationSeverity.ERROR, f"Invalid wildcard pattern: {field_path}")
            return
        
        # For patterns like "*.name", validate each top-level key
        remaining_path = '.'.join(parts[1:])
        
        for key, value in config_data.items():
            if isinstance(value, dict):
                full_path = f"{key}.{remaining_path}"
                nested_value = self._get_nested_value(value, remaining_path)
                
                if nested_value is None and rule.required:
                    result.add_issue(ValidationSeverity.ERROR, f"Required field '{full_path}' is missing")
                elif nested_value is not None:
                    if not self._validate_type(nested_value, rule.field_type):
                        result.add_issue(ValidationSeverity.ERROR, 
                                       f"Field '{full_path}' has invalid type. Expected {rule.field_type.value}, got {type(nested_value).__name__}")
                    else:
                        self._validate_constraints(full_path, nested_value, rule, result)
    
    def _get_nested_value(self, data: Dict[str, Any], path: str) -> Any:
        """Get nested value from dictionary using dot notation."""
        keys = path.split('.')
        value = data
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        
        return value
    
    def _validate_type(self, value: Any, field_type: FieldType) -> bool:
        """Validate field type."""
        if field_type == FieldType.STRING:
            return isinstance(value, str)
        elif field_type == FieldType.INTEGER:
            return isinstance(value, int)
        elif field_type == FieldType.FLOAT:
            return isinstance(value, (int, float))
        elif field_type == FieldType.BOOLEAN:
            return isinstance(value, bool)
        elif field_type == FieldType.LIST:
            return isinstance(value, list)
        elif field_type == FieldType.DICT:
            return isinstance(value, dict)
        elif field_type == FieldType.PATH:
            return isinstance(value, str)
        elif field_type == FieldType.URL:
            return isinstance(value, str) and self._is_valid_url(value)
        elif field_type == FieldType.EMAIL:
            return isinstance(value, str) and self._is_valid_email(value)
        elif field_type == FieldType.PORT:
            return isinstance(value, int) and 1 <= value <= 65535
        elif field_type == FieldType.IP_ADDRESS:
            return isinstance(value, str) and self._is_valid_ip(value)
        
        return False
    
    def _validate_constraints(self, field_path: str, value: Any, rule: ValidationRule, result: 'ValidationResult'):
        """Validate field constraints."""
        from .configuration_manager import ValidationSeverity
        
        # Validate numeric constraints
        if rule.min_value is not None and isinstance(value, (int, float)):
            if value < rule.min_value:
                result.add_issue(ValidationSeverity.ERROR, 
                               f"Field '{field_path}' value {value} is below minimum {rule.min_value}")
        
        if rule.max_value is not None and isinstance(value, (int, float)):
            if value > rule.max_value:
                result.add_issue(ValidationSeverity.ERROR, 
                               f"Field '{field_path}' value {value} is above maximum {rule.max_value}")
        
        # Validate string length constraints
        if rule.min_length is not None and isinstance(value, str):
            if len(value) < rule.min_length:
                result.add_issue(ValidationSeverity.ERROR, 
                               f"Field '{field_path}' length {len(value)} is below minimum {rule.min_length}")
        
        if rule.max_length is not None and isinstance(value, str):
            if len(value) > rule.max_length:
                result.add_issue(ValidationSeverity.ERROR, 
                               f"Field '{field_path}' length {len(value)} is above maximum {rule.max_length}")
        
        # Validate pattern
        if rule.pattern is not None and isinstance(value, str):
            if not re.match(rule.pattern, value):
                result.add_issue(ValidationSeverity.ERROR, 
                               f"Field '{field_path}' value '{value}' does not match pattern '{rule.pattern}'")
        
        # Validate allowed values
        if rule.allowed_values is not None:
            if value not in rule.allowed_values:
                result.add_issue(ValidationSeverity.ERROR, 
                               f"Field '{field_path}' value '{value}' is not in allowed values: {rule.allowed_values}")
        
        # Validate custom validator
        if rule.custom_validator is not None:
            try:
                if not rule.custom_validator(value):
                    result.add_issue(ValidationSeverity.ERROR, 
                                   f"Field '{field_path}' failed custom validation")
            except Exception as e:
                result.add_issue(ValidationSeverity.ERROR, 
                               f"Field '{field_path}' custom validation error: {str(e)}")
        
        # Validate path existence
        if rule.field_type == FieldType.PATH and isinstance(value, str):
            if not Path(value).exists():
                result.add_issue(ValidationSeverity.WARNING, 
                               f"Path '{field_path}' does not exist: {value}")
    
    def _validate_dependencies(self, config_data: Dict[str, Any], dependency_rules: List[DependencyRule], result: 'ValidationResult'):
        """Validate configuration dependencies."""
        from .configuration_manager import ValidationSeverity
        
        for rule in dependency_rules:
            source_value = self._get_nested_value(config_data, rule.source_field)
            target_value = self._get_nested_value(config_data, rule.target_field)
            
            if rule.condition == "required_if":
                if source_value == rule.condition_value and target_value is None:
                    result.add_issue(ValidationSeverity.ERROR, 
                                   f"Field '{rule.target_field}' is required when '{rule.source_field}' is {rule.condition_value}")
            
            elif rule.condition == "forbidden_if":
                if source_value == rule.condition_value and target_value is not None:
                    result.add_issue(ValidationSeverity.ERROR, 
                                   f"Field '{rule.target_field}' is forbidden when '{rule.source_field}' is {rule.condition_value}")
            
            elif rule.condition == "equals":
                if source_value is not None and target_value is not None and source_value != target_value:
                    result.add_issue(ValidationSeverity.ERROR, 
                                   f"Field '{rule.target_field}' must equal '{rule.source_field}'")
            
            elif rule.condition == "not_equals":
                if source_value is not None and target_value is not None and source_value == target_value:
                    result.add_issue(ValidationSeverity.ERROR, 
                                   f"Field '{rule.target_field}' must not equal '{rule.source_field}'")
    
    def validate_dependencies(self, all_configs: Dict[str, Dict[str, Any]]) -> 'ValidationResult':
        """
        Validate cross-section configuration dependencies.
        
        Args:
            all_configs: All configuration sections
            
        Returns:
            ValidationResult: Validation result for dependencies
        """
        from .configuration_manager import ValidationResult, ValidationSeverity
        
        result = ValidationResult(is_valid=True)
        
        # Check for common dependency issues
        self._validate_database_dependencies(all_configs, result)
        self._validate_api_dependencies(all_configs, result)
        self._validate_security_dependencies(all_configs, result)
        
        return result
    
    def _validate_database_dependencies(self, all_configs: Dict[str, Dict[str, Any]], result: 'ValidationResult'):
        """Validate database-related dependencies."""
        from .configuration_manager import ValidationSeverity
        
        system_config = all_configs.get("system", {})
        
        # Check database configuration completeness
        db_config = system_config.get("database", {})
        if db_config:
            required_fields = ["host", "port", "database"]
            for field in required_fields:
                if not db_config.get(field):
                    result.add_issue(ValidationSeverity.ERROR, 
                                   f"Database configuration missing required field: {field}")
    
    def _validate_api_dependencies(self, all_configs: Dict[str, Dict[str, Any]], result: 'ValidationResult'):
        """Validate API-related dependencies."""
        from .configuration_manager import ValidationSeverity
        
        system_config = all_configs.get("system", {})
        
        # Check API configuration
        api_config = system_config.get("api", {})
        if api_config:
            # Check JWT secret when auth is enabled
            if api_config.get("auth_enabled", True) and not api_config.get("jwt_secret"):
                result.add_issue(ValidationSeverity.ERROR, 
                               "JWT secret is required when API authentication is enabled")
    
    def _validate_security_dependencies(self, all_configs: Dict[str, Dict[str, Any]], result: 'ValidationResult'):
        """Validate security-related dependencies."""
        from .configuration_manager import ValidationSeverity
        
        system_config = all_configs.get("system", {})
        environment_config = all_configs.get("environment", {})
        
        # Check security configuration in production
        if environment_config.get("environment") == "production":
            security_config = system_config.get("security", {})
            
            if not security_config.get("secret_key") or len(security_config.get("secret_key", "")) < 32:
                result.add_issue(ValidationSeverity.ERROR, 
                               "Production environment requires a strong secret key (minimum 32 characters)")
            
            if system_config.get("api", {}).get("debug", False):
                result.add_issue(ValidationSeverity.ERROR, 
                               "Debug mode should be disabled in production environment")
    
    def _is_valid_url(self, url: str) -> bool:
        """Validate URL format."""
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return bool(url_pattern.match(url))
    
    def _is_valid_email(self, email: str) -> bool:
        """Validate email format."""
        email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        return bool(email_pattern.match(email))
    
    def _is_valid_ip(self, ip: str) -> bool:
        """Validate IP address format."""
        # Simple IP validation - could be enhanced with proper IPv4/IPv6 validation
        if ip in ["localhost", "0.0.0.0"]:
            return True
        
        ip_pattern = re.compile(r'^(\d{1,3}\.){3}\d{1,3}$')
        if not ip_pattern.match(ip):
            return False
        
        # Check each octet is 0-255
        octets = ip.split('.')
        return all(0 <= int(octet) <= 255 for octet in octets)
    
    def generate_fix_suggestions(self, validation_result: 'ValidationResult') -> List[str]:
        """
        Generate fix suggestions based on validation errors.
        
        Args:
            validation_result: Validation result with errors
            
        Returns:
            List of fix suggestions
        """
        suggestions = []
        
        for error in validation_result.errors:
            suggestion = self._generate_error_fix_suggestion(error)
            if suggestion:
                suggestions.append(suggestion)
        
        return suggestions
    
    def _generate_error_fix_suggestion(self, error: str) -> Optional[str]:
        """Generate fix suggestion for a specific error."""
        
        if "Required field" in error and "is missing" in error:
            field_match = re.search(r"Required field '([^']+)' is missing", error)
            if field_match:
                field_name = field_match.group(1)
                return f"Add the required field '{field_name}' to your configuration file"
        
        elif "has invalid type" in error:
            type_match = re.search(r"Expected (\w+), got (\w+)", error)
            if type_match:
                expected_type, actual_type = type_match.groups()
                return f"Convert the field value to {expected_type} type (currently {actual_type})"
        
        elif "is below minimum" in error:
            return "Increase the field value to meet the minimum requirement"
        
        elif "is above maximum" in error:
            return "Decrease the field value to meet the maximum requirement"
        
        elif "does not match pattern" in error:
            return "Update the field value to match the required format pattern"
        
        elif "is not in allowed values" in error:
            values_match = re.search(r"allowed values: (\[.*\])", error)
            if values_match:
                allowed_values = values_match.group(1)
                return f"Use one of the allowed values: {allowed_values}"
        
        elif "JWT secret is required" in error:
            return "Add a JWT secret key to your API configuration (minimum 32 characters)"
        
        elif "Production environment requires" in error:
            return "Update security configuration for production environment"
        
        return None
    
    def validate_file_format(self, file_path: Path) -> 'ValidationResult':
        """
        Validate configuration file format.
        
        Args:
            file_path: Path to configuration file
            
        Returns:
            ValidationResult: Format validation result
        """
        from .configuration_manager import ValidationResult, ValidationSeverity
        
        result = ValidationResult(is_valid=True)
        
        if not file_path.exists():
            result.add_issue(ValidationSeverity.ERROR, f"Configuration file does not exist: {file_path}")
            return result
        
        try:
            # Determine file format
            if file_path.suffix.lower() in ['.yaml', '.yml']:
                self._validate_yaml_format(file_path, result)
            elif file_path.suffix.lower() == '.json':
                self._validate_json_format(file_path, result)
            elif file_path.suffix.lower() == '.env':
                self._validate_env_format(file_path, result)
            else:
                result.add_issue(ValidationSeverity.WARNING, 
                               f"Unknown configuration file format: {file_path.suffix}")
        
        except Exception as e:
            result.add_issue(ValidationSeverity.ERROR, f"Error validating file format: {str(e)}")
        
        return result
    
    def _validate_yaml_format(self, file_path: Path, result: 'ValidationResult'):
        """Validate YAML file format."""
        from .configuration_manager import ValidationSeverity
        import yaml
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                yaml.safe_load(f)
            result.add_issue(ValidationSeverity.INFO, f"YAML format is valid: {file_path}")
        except yaml.YAMLError as e:
            result.add_issue(ValidationSeverity.ERROR, f"Invalid YAML format in {file_path}: {str(e)}")
    
    def _validate_json_format(self, file_path: Path, result: 'ValidationResult'):
        """Validate JSON file format."""
        from .configuration_manager import ValidationSeverity
        import json
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json.load(f)
            result.add_issue(ValidationSeverity.INFO, f"JSON format is valid: {file_path}")
        except json.JSONDecodeError as e:
            result.add_issue(ValidationSeverity.ERROR, f"Invalid JSON format in {file_path}: {str(e)}")
    
    def _validate_env_format(self, file_path: Path, result: 'ValidationResult'):
        """Validate .env file format."""
        from .configuration_manager import ValidationSeverity
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if line and not line.startswith('#'):
                    if '=' not in line:
                        result.add_issue(ValidationSeverity.ERROR, 
                                       f"Invalid .env format at line {line_num}: missing '=' separator")
            
            result.add_issue(ValidationSeverity.INFO, f".env format is valid: {file_path}")
        
        except Exception as e:
            result.add_issue(ValidationSeverity.ERROR, f"Error reading .env file {file_path}: {str(e)}")