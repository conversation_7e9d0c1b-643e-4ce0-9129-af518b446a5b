#!/usr/bin/env python3
"""
量化交易系统主入口 - 统一架构版本
使用统一服务总线整合所有功能模块
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent  # 从 src/application/main.py 向上三级到项目根目录
sys.path.insert(0, str(project_root))

from src.core.engine.service_bus import get_service_bus
from src.core.engine.unified_config import get_config_manager
from src.core.engine.unified_error_handler import get_error_handler
from src.common.utils.logging import get_logger, setup_system_logging

class UnifiedTradingSystem:
    """
    统一量化交易系统主类
    使用服务总线架构，整合所有功能模块
    """
    
    def __init__(self, use_sqlite=True):
        """初始化统一交易系统"""
        # 设置系统日志
        setup_system_logging()
        self.logger = get_logger("unified_trading_system")
        
        self.logger.info("🚀 开始初始化统一量化交易系统...")
        
        try:
            # 初始化核心组件
            self.logger.info("正在初始化服务总线...")
            self.service_bus = get_service_bus()
            self.logger.info("服务总线初始化完成")
            
            self.logger.info("正在初始化配置管理器...")
            self.config_manager = get_config_manager()
            self.logger.info("配置管理器初始化完成")
            
            self.logger.info("正在初始化错误处理器...")
            self.error_handler = get_error_handler()
            self.logger.info("错误处理器初始化完成")
            
            # 验证系统健康状态
            self.logger.info("正在检查系统健康状态...")
            if self.service_bus.check_health():
                self.logger.info("✅ 系统健康检查通过")
            else:
                self.logger.warning("⚠️  系统健康检查发现问题，但系统仍可运行")
            
            self.logger.info("✅ 统一量化交易系统初始化完成")
            
        except Exception as e:
            self.logger.error(f"系统初始化失败: {e}")
            # 使用错误处理器记录错误
            if hasattr(self, 'error_handler'):
                self.error_handler.handle_error(e, component="main", operation="init")
            raise
    
    # 统一接口方法 - 使用服务总线
    def get_market_data(self, symbol, start_date, end_date, market=None):
        """获取市场数据 - 通过服务总线"""
        self.logger.info(f"获取市场数据: {symbol} ({start_date} 至 {end_date})")
        try:
            return self.service_bus.get_market_data(symbol, start_date, end_date, market)
        except Exception as e:
            self.error_handler.handle_error(
                e, 
                context={"symbol": symbol, "start_date": start_date, "end_date": end_date, "market": market},
                component="data_manager", 
                operation="get_market_data"
            )
            raise
    
    def add_strategy(self, strategy, weight=1.0):
        """添加策略 - 通过服务总线"""
        strategy_name = getattr(strategy, 'name', str(strategy))
        self.logger.info(f"添加策略: {strategy_name}，权重: {weight}")
        try:
            return self.service_bus.add_strategy(strategy, weight)
        except Exception as e:
            self.error_handler.handle_error(
                e,
                context={"strategy_name": strategy_name, "weight": weight},
                component="strategy_manager",
                operation="add_strategy"
            )
            raise
    
    def run_backtest(self, strategy_id, start_date, end_date):
        """运行回测 - 通过服务总线"""
        self.logger.info(f"开始运行回测: 策略ID {strategy_id} ({start_date} 至 {end_date})")
        try:
            return self.service_bus.run_backtest(strategy_id, start_date, end_date)
        except Exception as e:
            self.error_handler.handle_error(
                e,
                context={"strategy_id": strategy_id, "start_date": start_date, "end_date": end_date},
                component="backtest_engine",
                operation="run_backtest"
            )
            raise
    
    def get_system_status(self):
        """获取系统状态 - 通过服务总线"""
        self.logger.debug("获取系统状态...")
        try:
            status = self.service_bus.get_system_status()
            
            # 添加统一系统特有的状态信息
            status["unified_system"] = {
                "version": "2.0.0",
                "architecture": "服务总线架构",
                "config_sections": len(self.config_manager.config_sections),
                "error_records": len(self.error_handler.error_records)
            }
            
            self.logger.debug("系统状态获取完成")
            return status
        except Exception as e:
            self.error_handler.handle_error(
                e,
                component="main",
                operation="get_system_status"
            )
            return {'error': f'状态获取失败: {e}'}
    
    def get_config(self, section=None, key=None, default=None):
        """获取配置 - 通过配置管理器"""
        try:
            return self.config_manager.get_config(section, key, default)
        except Exception as e:
            self.error_handler.handle_error(
                e,
                context={"section": section, "key": key},
                component="config_manager",
                operation="get_config"
            )
            return default
    
    def get_error_stats(self):
        """获取错误统计"""
        try:
            return self.error_handler.get_error_stats()
        except Exception as e:
            self.logger.error(f"获取错误统计失败: {e}")
            return {"total_errors": 0, "by_category": {}, "by_severity": {}, "by_component": {}}
    
    def validate_system(self):
        """验证系统完整性"""
        self.logger.info("开始系统完整性验证...")
        
        validation_results = {
            "overall_status": "healthy",
            "checks": {},
            "warnings": [],
            "errors": []
        }
        
        try:
            # 验证服务总线
            if self.service_bus.check_health():
                validation_results["checks"]["service_bus"] = "passed"
            else:
                validation_results["checks"]["service_bus"] = "failed"
                validation_results["errors"].append("服务总线健康检查失败")
                validation_results["overall_status"] = "degraded"
            
            # 验证配置完整性
            config_errors = self.config_manager.validate_config()
            if config_errors:
                validation_results["checks"]["configuration"] = "failed"
                validation_results["errors"].extend([f"配置错误: {section} - {', '.join(errors)}" 
                                                   for section, errors in config_errors.items()])
                validation_results["overall_status"] = "degraded"
            else:
                validation_results["checks"]["configuration"] = "passed"
            
            # 检查错误统计
            error_stats = self.error_handler.get_error_stats()
            if error_stats["total_errors"] > 10:
                validation_results["warnings"].append(f"系统累计错误数量较多: {error_stats['total_errors']}")
            
            validation_results["checks"]["error_handler"] = "passed"
            
            self.logger.info(f"系统验证完成，状态: {validation_results['overall_status']}")
            return validation_results
            
        except Exception as e:
            self.error_handler.handle_error(
                e,
                component="main",
                operation="validate_system"
            )
            validation_results["overall_status"] = "error"
            validation_results["errors"].append(f"系统验证过程失败: {str(e)}")
            return validation_results
    
    def shutdown(self):
        """优雅关闭系统"""
        self.logger.info("正在关闭统一交易系统...")
        
        try:
            # 导出错误报告
            self.error_handler.export_error_report()
            
            # 关闭服务总线
            self.service_bus.shutdown()
            
            self.logger.info("✅ 统一交易系统已关闭")
            
        except Exception as e:
            self.logger.error(f"系统关闭过程中出现错误: {e}")


# 保持向后兼容的别名
TradingSystem = UnifiedTradingSystem

def main():
    """主函数 - 演示统一交易系统使用"""
    # 设置系统日志
    setup_system_logging()
    logger = get_logger("main")
    
    logger.info("🚀 启动统一量化交易系统")
    
    try:
        # 初始化统一系统
        logger.info("正在初始化统一交易系统...")
        system = UnifiedTradingSystem()
        
        # 系统验证
        logger.info("正在进行系统完整性验证...")
        validation_results = system.validate_system()
        
        logger.info("📊 系统验证结果:")
        logger.info(f"  整体状态: {validation_results['overall_status']}")
        for check_name, result in validation_results["checks"].items():
            logger.info(f"  {check_name}: {result}")
        
        if validation_results["warnings"]:
            logger.warning("⚠️  系统警告:")
            for warning in validation_results["warnings"]:
                logger.warning(f"  {warning}")
        
        if validation_results["errors"]:
            logger.error("❌ 系统错误:")
            for error in validation_results["errors"]:
                logger.error(f"  {error}")
        
        # 显示系统状态
        logger.info("获取系统状态...")
        status = system.get_system_status()
        logger.info("📊 系统状态:")
        for component, status_info in status.items():
            if isinstance(status_info, dict):
                logger.info(f"  {component}:")
                for key, value in status_info.items():
                    logger.info(f"    {key}: {value}")
            else:
                logger.info(f"  {component}: {status_info}")
        
        # 显示配置信息
        logger.info("📋 配置信息:")
        config_info = system.config_manager.get_config_info()
        logger.info(f"  配置节数量: {len(config_info)}")
        for info in config_info[:3]:  # 只显示前3个
            logger.info(f"  {info['name']}: {info['keys_count']} 个配置项")
        
        # 显示错误统计
        error_stats = system.get_error_stats()
        logger.info("📈 错误统计:")
        logger.info(f"  总错误数: {error_stats['total_errors']}")
        if error_stats['by_category']:
            logger.info("  按类别统计:")
            for category, count in error_stats['by_category'].items():
                logger.info(f"    {category}: {count}")
        
        logger.info("💡 使用提示:")
        logger.info("  - 启动Web UI: cd web_ui/backend && python main.py")
        logger.info("  - 运行示例: python examples/basic_usage.py")
        logger.info("  - 系统诊断: system.validate_system()")
        logger.info("  - 查看配置: system.get_config()")
        logger.info("  - 错误统计: system.get_error_stats()")
        
        logger.info("✅ 统一量化交易系统启动完成，准备就绪")
        return system
        
    except Exception as e:
        logger.critical(f"系统启动失败: {e}")
        raise

if __name__ == "__main__":
    system = main()