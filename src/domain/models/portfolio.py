import logging
logger = logging.getLogger(__name__)
"""
量化交易系统 - 投资组合和持仓管理模型

本模块定义了投资组合管理的核心数据结构，包括持仓、投资组合快照、
现金流记录等，为投资组合管理和绩效分析提供完整的数据支持。

主要数据模型：
- Position: 持仓数据结构，表示单个标的的持仓信息
- Portfolio: 投资组合数据结构，管理现金和所有持仓
- PortfolioSnapshot: 投资组合快照，用于历史状态跟踪
- CashFlow: 现金流记录，跟踪资金流动

功能特性：
- 实时持仓跟踪和市值更新
- 已实现和未实现盈亏计算
- 投资组合历史状态记录
- 现金流管理和分析
- 绩效指标计算（收益率、夏普比率、最大回撤等）
- 风险指标监控

作者: 量化交易系统开发团队
版本: 2.0.0
创建日期: 2024-01-01
最后修改: 2024-07-31

示例:
    创建持仓:
    >>> position = Position(
    ...     symbol="AAPL",
    ...     quantity=100,
    ...     avg_price=150.0
    ... )
    
    创建投资组合:
    >>> portfolio = Portfolio(
    ...     initial_capital=100000.0,
    ...     cash=100000.0
    ... )
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Optional, List, Tuple
import pandas as pd
import numpy as np
from .base import BaseModel
from .trading import Trade


@dataclass
class Position(BaseModel):
    """
    持仓数据结构
    
    表示投资组合中单个证券的持仓信息，包括持仓数量、平均成本、
    市场价值和盈亏状况等关键信息。
    
    属性:
        symbol (str): 交易标的代码
        quantity (float): 持仓数量（正数为多头，负数为空头）
        avg_price (float): 平均持仓成本
        market_value (float): 当前市场价值，默认为0
        unrealized_pnl (float): 未实现盈亏，默认为0
        realized_pnl (float): 已实现盈亏，默认为0
        last_price (Optional[float]): 最新价格
        timestamp (Optional[datetime]): 最后更新时间
    
    主要方法:
        - update_market_value: 根据当前价格更新市值和未实现盈亏
        - add_trade: 添加交易记录并更新持仓状态
        - validate: 验证持仓数据有效性
    
    使用示例:
        创建持仓:
        >>> position = Position(
        ...     symbol="AAPL",
        ...     quantity=100,
        ...     avg_price=150.0
        ... )
        
        更新市值:
        >>> position.update_market_value(155.0)
        >>> logger.info(f"未实现盈亏: {position.unrealized_pnl}")  # 500.0
        
        添加交易:
        >>> from .trading import Trade, OrderSide
        >>> trade = Trade(
        ...     id="trade_001",
        ...     symbol="AAPL",
        ...     side=OrderSide.BUY,
        ...     quantity=50,
        ...     price=152.0,
        ...     timestamp=datetime.now()
        ... )
        >>> position.add_trade(trade)
    
    注意事项:
        - 平均成本会随着新交易自动调整
        - 卖出交易会产生已实现盈亏
        - 持仓数量为0时表示已平仓
    """
    
    symbol: str
    quantity: float
    avg_price: float
    market_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    last_price: Optional[float] = None
    timestamp: Optional[datetime] = None
    
    def update_market_value(self, current_price: float) -> None:
        """
        根据当前价格更新市场价值和未实现盈亏
        
        使用最新的市场价格计算持仓的当前市值和未实现盈亏，
        并更新时间戳。
        
        参数:
            current_price (float): 当前市场价格
            
        更新内容:
            - last_price: 最新价格
            - market_value: 市场价值 = 持仓数量 × 当前价格
            - unrealized_pnl: 未实现盈亏 = (当前价格 - 平均成本) × 持仓数量
            - timestamp: 更新时间
        """
        self.last_price = current_price
        self.market_value = self.quantity * current_price
        self.unrealized_pnl = (current_price - self.avg_price) * self.quantity
        self.timestamp = datetime.now()
    
    def add_trade(self, trade: Trade) -> None:
        """
        添加交易记录并更新持仓状态
        
        根据新的交易记录更新持仓数量、平均成本和已实现盈亏。
        买入交易会增加持仓并调整平均成本，卖出交易会减少持仓并产生已实现盈亏。
        
        参数:
            trade (Trade): 交易记录对象
            
        异常:
            ValueError: 当交易标的不匹配或卖出数量超过持仓时抛出
            
        处理逻辑:
            买入交易:
            - 增加持仓数量
            - 重新计算平均成本 = (原持仓成本 + 新交易成本) / 总持仓数量
            
            卖出交易:
            - 减少持仓数量
            - 计算已实现盈亏 = (卖出价格 - 平均成本) × 卖出数量
            - 持仓清零时重置平均成本
        """
        if trade.symbol != self.symbol:
            raise ValueError(f"交易标的 {trade.symbol} 与持仓标的 {self.symbol} 不匹配")
        
        if trade.side.value == "BUY":
            # 买入交易：增加持仓
            total_cost = (self.quantity * self.avg_price) + (trade.quantity * trade.price)
            self.quantity += trade.quantity
            self.avg_price = total_cost / self.quantity if self.quantity > 0 else 0
        else:
            # 卖出交易：减少持仓
            if trade.quantity > self.quantity:
                raise ValueError("卖出数量不能超过当前持仓")
            
            # 计算已实现盈亏
            realized_pnl = (trade.price - self.avg_price) * trade.quantity
            self.realized_pnl += realized_pnl
            self.quantity -= trade.quantity
            
            # 如果持仓清零，重置平均成本
            if self.quantity == 0:
                self.avg_price = 0
    
    def validate(self) -> bool:
        """
        验证持仓数据的有效性
        
        检查持仓数据的基本有效性，确保数据符合业务逻辑。
        
        返回:
            bool: 验证通过返回True，否则返回False
            
        验证项目:
            - 交易标的代码不能为空
            - 平均成本不能为负数
        """
        if not self.symbol:
            return False
        if self.avg_price < 0:
            return False
        return True


@dataclass
class PortfolioSnapshot:
    """
    投资组合快照
    
    记录投资组合在特定时间点的完整状态，用于历史跟踪和绩效分析。
    快照包含了投资组合的所有关键信息，便于后续的时间序列分析。
    
    属性:
        timestamp (datetime): 快照时间戳
        total_value (float): 投资组合总价值
        cash (float): 现金余额
        positions_value (float): 持仓总市值
        unrealized_pnl (float): 未实现盈亏总额
        realized_pnl (float): 已实现盈亏总额
        positions (Dict[str, Position]): 所有持仓的副本
    
    使用示例:
        创建快照:
        >>> snapshot = PortfolioSnapshot(
        ...     timestamp=datetime.now(),
        ...     total_value=105000.0,
        ...     cash=50000.0,
        ...     positions_value=55000.0,
        ...     unrealized_pnl=5000.0,
        ...     realized_pnl=0.0,
        ...     positions={"AAPL": position}
        ... )
        
        转换为字典:
        >>> data = snapshot.to_dict()
        >>> logger.info(data['total_value'])  # 105000.0
    """
    
    timestamp: datetime
    total_value: float
    cash: float
    positions_value: float
    unrealized_pnl: float
    realized_pnl: float
    positions: Dict[str, Position] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, any]:
        """
        将快照转换为字典格式
        
        将投资组合快照转换为便于序列化和存储的字典格式，
        包含所有关键信息和持仓详情。
        
        返回:
            Dict[str, any]: 包含快照所有信息的字典
            
        字典结构:
            - timestamp: 快照时间
            - total_value: 总价值
            - cash: 现金余额
            - positions_value: 持仓市值
            - unrealized_pnl: 未实现盈亏
            - realized_pnl: 已实现盈亏
            - positions: 持仓详情字典
        """
        return {
            'timestamp': self.timestamp,
            'total_value': self.total_value,
            'cash': self.cash,
            'positions_value': self.positions_value,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl,
            'positions': {symbol: {
                'symbol': pos.symbol,
                'quantity': pos.quantity,
                'avg_price': pos.avg_price,
                'market_value': pos.market_value,
                'unrealized_pnl': pos.unrealized_pnl
            } for symbol, pos in self.positions.items()}
        }


@dataclass
class CashFlow:
    """
    现金流记录
    
    跟踪投资组合中的所有资金流动，包括交易、手续费、分红、
    存取款等各种现金流动情况。
    
    属性:
        timestamp (datetime): 现金流发生时间
        amount (float): 现金流金额（正数为流入，负数为流出）
        flow_type (str): 现金流类型
        description (str): 现金流描述
        trade_id (Optional[str]): 关联的交易ID（如果适用）
        symbol (Optional[str]): 关联的交易标的（如果适用）
    
    现金流类型:
        - TRADE: 交易相关现金流
        - COMMISSION: 手续费支出
        - DIVIDEND: 分红收入
        - DEPOSIT: 资金存入
        - WITHDRAWAL: 资金提取
    
    使用示例:
        交易现金流:
        >>> cash_flow = CashFlow(
        ...     timestamp=datetime.now(),
        ...     amount=-15000.0,  # 买入支出
        ...     flow_type="TRADE",
        ...     description="买入100股AAPL",
        ...     trade_id="trade_001",
        ...     symbol="AAPL"
        ... )
        
        分红现金流:
        >>> dividend_flow = CashFlow(
        ...     timestamp=datetime.now(),
        ...     amount=200.0,  # 分红收入
        ...     flow_type="DIVIDEND",
        ...     description="AAPL季度分红",
        ...     symbol="AAPL"
        ... )
    """
    
    timestamp: datetime
    amount: float
    flow_type: str  # 'TRADE', 'COMMISSION', 'DIVIDEND', 'DEPOSIT', 'WITHDRAWAL'
    description: str
    trade_id: Optional[str] = None
    symbol: Optional[str] = None


@dataclass
class Portfolio(BaseModel):
    """
    增强型投资组合数据结构
    
    管理投资组合的现金、持仓和历史记录的核心数据结构。提供完整的
    投资组合管理功能，包括交易执行、绩效跟踪、风险监控等。
    
    核心属性:
        initial_capital (float): 初始资金
        cash (float): 当前现金余额
        positions (Dict[str, Position]): 持仓字典，键为交易标的代码
        total_value (float): 投资组合总价值
        unrealized_pnl (float): 未实现盈亏总额
        realized_pnl (float): 已实现盈亏总额
        trades (List[Trade]): 交易记录列表
        timestamp (Optional[datetime]): 最后更新时间
    
    增强功能:
        history (List[PortfolioSnapshot]): 历史快照列表
        cash_flows (List[CashFlow]): 现金流记录列表
        daily_returns (List[float]): 日收益率列表
    
    绩效跟踪:
        peak_value (float): 历史最高价值
        current_drawdown (float): 当前回撤
        max_drawdown (float): 最大回撤
    
    主要功能:
        - 交易执行和持仓管理
        - 实时市值更新和盈亏计算
        - 历史状态跟踪和快照记录
        - 现金流管理和分析
        - 绩效指标计算
        - 风险指标监控
    
    使用示例:
        创建投资组合:
        >>> portfolio = Portfolio(
        ...     initial_capital=100000.0,
        ...     cash=100000.0
        ... )
        
        添加交易:
        >>> trade = Trade(...)
        >>> portfolio.add_trade(trade)
        
        更新市值:
        >>> market_prices = {"AAPL": 155.0, "GOOGL": 2800.0}
        >>> portfolio.update_positions_market_value(market_prices)
        
        获取绩效指标:
        >>> metrics = portfolio.get_performance_metrics()
        >>> logger.info(f"总收益率: {metrics['total_return']:.2%}")
    """
    
    initial_capital: float
    cash: float
    positions: Dict[str, Position] = field(default_factory=dict)
    total_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    trades: List[Trade] = field(default_factory=list)
    timestamp: Optional[datetime] = None
    
    # 增强型投资组合管理功能
    history: List[PortfolioSnapshot] = field(default_factory=list)
    cash_flows: List[CashFlow] = field(default_factory=list)
    daily_returns: List[float] = field(default_factory=list)
    
    # 绩效跟踪指标
    peak_value: float = 0.0
    current_drawdown: float = 0.0
    max_drawdown: float = 0.0
    
    def __post_init__(self):
        """
        投资组合创建后的初始化
        
        设置初始状态，包括现金余额、峰值价值等，并记录初始快照。
        """
        # 兼容测试：若未提供 initial_capital，则从 cash 或 total_value 推断
        if not hasattr(self, 'initial_capital') or self.initial_capital is None:
            inferred = self.cash if self.cash else (self.total_value if self.total_value else 0.0)
            self.initial_capital = float(inferred)
        if self.cash == 0:
            self.cash = self.initial_capital
        self.peak_value = self.initial_capital
        self.update_total_value()
        self._record_snapshot()
    
    def update_total_value(self) -> None:
        """
        更新投资组合总价值和盈亏状况
        
        重新计算投资组合的总价值、未实现盈亏、已实现盈亏，
        并更新回撤指标和日收益率。
        
        计算内容:
            - 总价值 = 现金 + 持仓市值
            - 未实现盈亏 = 所有持仓未实现盈亏之和
            - 已实现盈亏 = 所有持仓已实现盈亏之和
            - 回撤指标更新
            - 日收益率计算
        """
        positions_value = sum(pos.market_value for pos in self.positions.values())
        previous_value = self.total_value
        
        self.total_value = self.cash + positions_value
        self.unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        self.realized_pnl = sum(pos.realized_pnl for pos in self.positions.values())
        self.timestamp = datetime.now()
        
        # 更新回撤跟踪
        if self.total_value > self.peak_value:
            self.peak_value = self.total_value
            self.current_drawdown = 0.0
        else:
            self.current_drawdown = (self.total_value - self.peak_value) / self.peak_value
            if self.current_drawdown < self.max_drawdown:
                self.max_drawdown = self.current_drawdown
        
        # 计算日收益率（如果有前值）
        if previous_value > 0 and self.total_value != previous_value:
            daily_return = (self.total_value - previous_value) / previous_value
            self.daily_returns.append(daily_return)
    
    def add_trade(self, trade: Trade) -> None:
        """
        添加交易记录到投资组合
        
        处理新的交易记录，更新现金余额、持仓状态，记录现金流，
        并更新投资组合总价值。
        
        参数:
            trade (Trade): 交易记录对象
            
        处理流程:
            1. 记录交易到交易历史
            2. 更新现金余额（买入减少现金，卖出增加现金）
            3. 记录交易相关的现金流
            4. 更新或创建持仓记录
            5. 更新投资组合总价值
            6. 记录投资组合快照
        
        现金流处理:
            - 买入：现金减少 = 交易金额 + 手续费
            - 卖出：现金增加 = 交易金额 - 手续费
            - 手续费单独记录为现金流
        
        持仓处理:
            - 如果已有持仓，调用持仓的add_trade方法更新
            - 如果是新标的的买入，创建新持仓
            - 如果持仓数量变为0，删除该持仓
        """
        self.trades.append(trade)
        
        # 记录交易现金流
        trade_amount = trade.quantity * trade.price
        if trade.side.value == "BUY":
            self.cash -= (trade_amount + trade.commission)
            self._record_cash_flow(-trade_amount, "TRADE", f"买入 {trade.quantity} 股 {trade.symbol}", trade.id, trade.symbol)
        else:
            self.cash += (trade_amount - trade.commission)
            self._record_cash_flow(trade_amount, "TRADE", f"卖出 {trade.quantity} 股 {trade.symbol}", trade.id, trade.symbol)
        
        # 单独记录手续费现金流
        if trade.commission > 0:
            self._record_cash_flow(-trade.commission, "COMMISSION", f"{trade.symbol} 交易手续费", trade.id, trade.symbol)
        
        # 更新或创建持仓
        if trade.symbol in self.positions:
            self.positions[trade.symbol].add_trade(trade)
            # 如果持仓数量变为零，删除持仓
            if self.positions[trade.symbol].quantity == 0:
                del self.positions[trade.symbol]
        else:
            # 为买入订单创建新持仓
            if trade.side.value == "BUY":
                self.positions[trade.symbol] = Position(
                    symbol=trade.symbol,
                    quantity=trade.quantity,
                    avg_price=trade.price
                )
        
        self.update_total_value()
        self._record_snapshot()
    
    def update_positions_market_value(self, market_prices: Dict[str, float]) -> None:
        """
        使用当前市场价格更新所有持仓的市值
        
        根据提供的市场价格字典更新所有持仓的市场价值和未实现盈亏，
        然后更新投资组合总价值并记录快照。
        
        参数:
            market_prices (Dict[str, float]): 市场价格字典，键为交易标的代码，值为当前价格
            
        处理流程:
            1. 遍历所有持仓
            2. 如果价格字典中有对应价格，更新持仓市值
            3. 更新投资组合总价值
            4. 记录投资组合快照
        """
        for symbol, position in self.positions.items():
            if symbol in market_prices:
                position.update_market_value(market_prices[symbol])
        self.update_total_value()
        self._record_snapshot()
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """
        获取指定标的的持仓信息
        
        参数:
            symbol (str): 交易标的代码
            
        返回:
            Optional[Position]: 持仓对象，如果不存在则返回None
        """
        return self.positions.get(symbol)
    
    def get_available_cash(self) -> float:
        """
        获取可用现金余额
        
        返回:
            float: 当前可用于交易的现金金额
        """
        return self.cash
    
    def get_total_return(self) -> float:
        """
        获取总收益率
        
        计算投资组合相对于初始资金的总收益率。
        
        返回:
            float: 总收益率（小数形式，如0.15表示15%）
            
        计算公式:
            总收益率 = (当前总价值 - 初始资金) / 初始资金
        """
        if self.initial_capital == 0:
            return 0.0
        return (self.total_value - self.initial_capital) / self.initial_capital
    
    def get_positions_summary(self) -> Dict[str, Dict[str, float]]:
        """Get summary of all positions."""
        summary = {}
        for symbol, position in self.positions.items():
            summary[symbol] = {
                'quantity': position.quantity,
                'avg_price': position.avg_price,
                'market_value': position.market_value,
                'unrealized_pnl': position.unrealized_pnl,
                'unrealized_pnl_pct': position.unrealized_pnl / (position.avg_price * position.quantity) if position.quantity > 0 else 0.0,
                'weight': position.market_value / self.total_value if self.total_value > 0 else 0.0
            }
        return summary
    
    def get_cash_flow_summary(self, start_date: Optional[datetime] = None, 
                             end_date: Optional[datetime] = None) -> Dict[str, float]:
        """Get cash flow summary for a period."""
        filtered_flows = self.cash_flows
        
        if start_date:
            filtered_flows = [cf for cf in filtered_flows if cf.timestamp >= start_date]
        if end_date:
            filtered_flows = [cf for cf in filtered_flows if cf.timestamp <= end_date]
        
        summary = {
            'total_inflow': sum(cf.amount for cf in filtered_flows if cf.amount > 0),
            'total_outflow': sum(cf.amount for cf in filtered_flows if cf.amount < 0),
            'net_flow': sum(cf.amount for cf in filtered_flows),
            'trade_flows': sum(cf.amount for cf in filtered_flows if cf.flow_type == 'TRADE'),
            'commission_paid': sum(abs(cf.amount) for cf in filtered_flows if cf.flow_type == 'COMMISSION'),
            'dividend_received': sum(cf.amount for cf in filtered_flows if cf.flow_type == 'DIVIDEND')
        }
        
        return summary
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Calculate and return performance metrics."""
        if not self.daily_returns:
            return {}
        
        returns_array = np.array(self.daily_returns)
        
        # Basic metrics
        total_return = self.get_total_return()
        avg_daily_return = np.mean(returns_array)
        volatility = np.std(returns_array)
        
        # Annualized metrics (assuming 252 trading days)
        annualized_return = (1 + avg_daily_return) ** 252 - 1
        annualized_volatility = volatility * np.sqrt(252)
        
        # Risk-adjusted metrics
        sharpe_ratio = annualized_return / annualized_volatility if annualized_volatility > 0 else 0.0
        
        # Downside metrics
        negative_returns = returns_array[returns_array < 0]
        downside_deviation = np.std(negative_returns) if len(negative_returns) > 0 else 0.0
        sortino_ratio = annualized_return / (downside_deviation * np.sqrt(252)) if downside_deviation > 0 else 0.0
        
        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': annualized_volatility,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'max_drawdown': self.max_drawdown,
            'current_drawdown': self.current_drawdown,
            'peak_value': self.peak_value
        }
    
    def get_history_dataframe(self) -> pd.DataFrame:
        """Get portfolio history as pandas DataFrame."""
        if not self.history:
            return pd.DataFrame()
        
        data = []
        for snapshot in self.history:
            data.append({
                'timestamp': snapshot.timestamp,
                'total_value': snapshot.total_value,
                'cash': snapshot.cash,
                'positions_value': snapshot.positions_value,
                'unrealized_pnl': snapshot.unrealized_pnl,
                'realized_pnl': snapshot.realized_pnl
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df.sort_index()
    
    def get_cash_flows_dataframe(self) -> pd.DataFrame:
        """Get cash flows as pandas DataFrame."""
        if not self.cash_flows:
            return pd.DataFrame()
        
        data = []
        for cf in self.cash_flows:
            data.append({
                'timestamp': cf.timestamp,
                'amount': cf.amount,
                'flow_type': cf.flow_type,
                'description': cf.description,
                'trade_id': cf.trade_id,
                'symbol': cf.symbol
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        return df.sort_index()
    
    def get_equity_curve(self) -> pd.Series:
        """Get equity curve as pandas Series."""
        df = self.get_history_dataframe()
        if df.empty:
            return pd.Series()
        return df['total_value']
    
    def get_drawdown_series(self) -> pd.Series:
        """Get drawdown series as pandas Series."""
        equity_curve = self.get_equity_curve()
        if equity_curve.empty:
            return pd.Series()
        
        # Calculate running maximum (peak)
        peak = equity_curve.expanding().max()
        
        # Calculate drawdown
        drawdown = (equity_curve - peak) / peak
        
        return drawdown
    
    def add_dividend(self, symbol: str, amount: float, timestamp: Optional[datetime] = None) -> None:
        """Add dividend payment to portfolio."""
        if timestamp is None:
            timestamp = datetime.now()
        
        self.cash += amount
        self._record_cash_flow(amount, "DIVIDEND", f"Dividend from {symbol}", symbol=symbol)
        self.update_total_value()
        self._record_snapshot()
    
    def add_deposit(self, amount: float, description: str = "Deposit", 
                   timestamp: Optional[datetime] = None) -> None:
        """Add cash deposit to portfolio."""
        if timestamp is None:
            timestamp = datetime.now()
        
        self.cash += amount
        self.initial_capital += amount  # Adjust initial capital for performance calculation
        self._record_cash_flow(amount, "DEPOSIT", description)
        self.update_total_value()
        self._record_snapshot()
    
    def add_withdrawal(self, amount: float, description: str = "Withdrawal", 
                      timestamp: Optional[datetime] = None) -> None:
        """Add cash withdrawal from portfolio."""
        if timestamp is None:
            timestamp = datetime.now()
        
        if amount > self.cash:
            raise ValueError(f"Insufficient cash for withdrawal: {amount} > {self.cash}")
        
        self.cash -= amount
        self.initial_capital -= amount  # Adjust initial capital for performance calculation
        self._record_cash_flow(-amount, "WITHDRAWAL", description)
        self.update_total_value()
        self._record_snapshot()
    
    def _record_cash_flow(self, amount: float, flow_type: str, description: str, 
                         trade_id: Optional[str] = None, symbol: Optional[str] = None) -> None:
        """Record a cash flow entry."""
        cash_flow = CashFlow(
            timestamp=datetime.now(),
            amount=amount,
            flow_type=flow_type,
            description=description,
            trade_id=trade_id,
            symbol=symbol
        )
        self.cash_flows.append(cash_flow)
    
    def _record_snapshot(self) -> None:
        """Record current portfolio state as snapshot."""
        positions_value = sum(pos.market_value for pos in self.positions.values())
        
        snapshot = PortfolioSnapshot(
            timestamp=datetime.now(),
            total_value=self.total_value,
            cash=self.cash,
            positions_value=positions_value,
            unrealized_pnl=self.unrealized_pnl,
            realized_pnl=self.realized_pnl,
            positions={symbol: Position(
                symbol=pos.symbol,
                quantity=pos.quantity,
                avg_price=pos.avg_price,
                market_value=pos.market_value,
                unrealized_pnl=pos.unrealized_pnl,
                realized_pnl=pos.realized_pnl,
                last_price=pos.last_price,
                timestamp=pos.timestamp
            ) for symbol, pos in self.positions.items()}
        )
        
        self.history.append(snapshot)
    
    def validate(self) -> bool:
        """
        验证投资组合数据的有效性
        
        检查投资组合的基本数据完整性和业务逻辑合理性。
        
        返回:
            bool: 验证通过返回True，否则返回False
            
        验证项目:
            - 初始资金必须大于0
            - 现金余额不能为负数（允许融资的情况除外）
            - 所有持仓数据必须有效
        """
        if self.initial_capital <= 0:
            return False
        if self.cash < 0:
            return False
        return all(pos.validate() for pos in self.positions.values())