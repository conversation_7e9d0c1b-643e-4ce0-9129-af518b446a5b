import logging
logger = logging.getLogger(__name__)
"""
量化交易系统 - 市场数据模型

本模块定义了不同资产类别和数据源的市场数据结构，为整个交易系统
提供统一的数据表示和验证机制。

主要数据模型：
- MarketData: 基础市场数据结构，包含OHLCV数据
- UnifiedMarketData: 统一市场数据结构，支持多市场
- EconomicData: 经济数据结构，用于宏观经济指标
- MarketInfo: 市场信息结构，包含交易规则和市场信息

支持的市场：
- US: 美国股票市场（NASDAQ, NYSE等）
- CN: 中国股票市场（上交所, 深交所等）
- CRYPTO: 加密货币市场（Binance, Coinbase等）
- ECONOMIC: 经济数据（FRED等）

数据验证：
- 价格数据的逻辑一致性检查
- 时间戳格式验证
- 市场和交易所代码验证
- 数据完整性检查

作者: 量化交易系统开发团队
版本: 2.0.0
创建日期: 2024-01-01
最后修改: 2024-07-31

示例:
    创建基础市场数据:
    >>> data = MarketData(
    ...     symbol="AAPL",
    ...     timestamp=datetime.now(),
    ...     open=150.0,
    ...     high=152.0,
    ...     low=149.0,
    ...     close=151.0,
    ...     volume=1000000
    ... )
    
    创建统一市场数据:
    >>> unified_data = UnifiedMarketData(
    ...     symbol="AAPL",
    ...     timestamp=datetime.now(),
    ...     open=150.0,
    ...     high=152.0,
    ...     low=149.0,
    ...     close=151.0,
    ...     volume=1000000,
    ...     market="US",
    ...     exchange="NASDAQ",
    ...     currency="USD"
    ... )
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, Any
from .base import BaseModel


@dataclass
class MarketData(BaseModel):
    """
    基础市场数据结构
    
    表示标准的OHLCV（开高低收量）市场数据，是量化分析的基础数据结构。
    适用于股票、期货、外汇等各种金融工具的价格数据。
    
    属性:
        symbol (str): 交易标的代码（如 'AAPL', '000001.SZ'）
        timestamp (datetime): 数据时间戳
        open (float): 开盘价
        high (float): 最高价
        low (float): 最低价
        close (float): 收盘价
        volume (float): 成交量
        adj_close (Optional[float]): 复权收盘价，可选
    
    验证规则:
        - 交易标的代码不能为空且必须为字符串
        - 时间戳必须为datetime对象
        - 价格和成交量必须为非负数值
        - 最高价不能低于开盘价和收盘价的最大值
        - 最低价不能高于开盘价和收盘价的最小值
    
    使用示例:
        创建日线数据:
        >>> data = MarketData(
        ...     symbol="AAPL",
        ...     timestamp=datetime(2024, 1, 1),
        ...     open=150.0,
        ...     high=152.0,
        ...     low=149.0,
        ...     close=151.0,
        ...     volume=1000000,
        ...     adj_close=151.0
        ... )
        
        验证数据:
        >>> if data.validate():
        ...     logger.info("数据验证通过")
    
    注意事项:
        - 价格数据应保持逻辑一致性
        - 成交量为0可能表示非交易日
        - 复权价格用于消除分红派息影响
    """
    
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    adj_close: Optional[float] = None
    
    def validate(self) -> bool:
        """
        验证市场数据的有效性
        
        检查数据的完整性和逻辑一致性，确保数据可用于后续分析。
        
        返回:
            bool: 验证通过返回True，否则返回False
            
        验证项目:
            - 交易标的代码格式检查
            - 时间戳类型检查
            - 价格和成交量非负检查
            - 价格逻辑一致性检查（高低价与开收价关系）
        """
        if not self.symbol or not isinstance(self.symbol, str):
            return False
        if not isinstance(self.timestamp, datetime):
            return False
        if any(not isinstance(x, (int, float)) or x < 0 for x in [self.open, self.high, self.low, self.close, self.volume]):
            return False
        if self.high < max(self.open, self.close) or self.low > min(self.open, self.close):
            return False
        return True


@dataclass
class UnifiedMarketData(BaseModel):
    """
    统一市场数据结构
    
    支持多市场的扩展市场数据结构，在基础OHLCV数据基础上增加了
    市场、交易所、货币等标识信息，适用于跨市场的数据管理和分析。
    
    属性:
        symbol (str): 交易标的代码
        timestamp (datetime): 数据时间戳
        open (float): 开盘价
        high (float): 最高价
        low (float): 最低价
        close (float): 收盘价
        volume (float): 成交量
        market (str): 市场代码（'US', 'CN', 'CRYPTO', 'ECONOMIC'）
        exchange (str): 交易所代码（'NASDAQ', 'SSE', 'BINANCE', 'FRED'）
        currency (str): 计价货币（'USD', 'CNY', 'BTC'等）
        adj_close (Optional[float]): 复权收盘价
        turnover (Optional[float]): 成交额
    
    支持的市场:
        - US: 美国股票市场
        - CN: 中国股票市场
        - CRYPTO: 加密货币市场
        - ECONOMIC: 经济数据
    
    验证规则:
        - 继承基础市场数据的所有验证规则
        - 交易标的、市场、交易所、货币不能为空
        - 市场代码必须在支持列表中
    
    使用示例:
        美股数据:
        >>> us_data = UnifiedMarketData(
        ...     symbol="AAPL",
        ...     timestamp=datetime.now(),
        ...     open=150.0, high=152.0, low=149.0, close=151.0,
        ...     volume=1000000,
        ...     market="US",
        ...     exchange="NASDAQ",
        ...     currency="USD"
        ... )
        
        A股数据:
        >>> cn_data = UnifiedMarketData(
        ...     symbol="000001.SZ",
        ...     timestamp=datetime.now(),
        ...     open=15.0, high=15.2, low=14.9, close=15.1,
        ...     volume=10000000,
        ...     market="CN",
        ...     exchange="SZSE",
        ...     currency="CNY",
        ...     turnover=150000000.0
        ... )
    
    注意事项:
        - 不同市场的数据格式可能有差异
        - 成交额字段主要用于中国市场
        - 货币信息用于跨市场比较分析
    """
    
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    market: str  # 'US', 'CN', 'CRYPTO', 'ECONOMIC'
    exchange: str  # 'NASDAQ', 'SSE', 'BINANCE', 'FRED'
    currency: str
    adj_close: Optional[float] = None
    turnover: Optional[float] = None  # 成交额
    
    def validate(self) -> bool:
        """
        验证统一市场数据的有效性
        
        在基础市场数据验证基础上，增加市场特定的验证规则。
        
        返回:
            bool: 验证通过返回True，否则返回False
            
        验证项目:
            - 基础市场数据验证
            - 市场、交易所、货币字段完整性检查
            - 市场代码有效性检查
        """
        if not all([self.symbol, self.market, self.exchange, self.currency]):
            return False
        if self.market not in ['US', 'CN', 'CRYPTO', 'ECONOMIC']:
            return False
        return super().validate()


@dataclass
class EconomicData(BaseModel):
    """
    经济数据结构
    
    用于存储宏观经济指标数据，如GDP、CPI、利率等经济统计数据。
    这些数据通常用于基本面分析和宏观策略制定。
    
    属性:
        series_id (str): 数据序列ID（如 'GDP', 'CPIAUCSL'）
        timestamp (datetime): 数据时间戳
        value (float): 数据值
        market (str): 市场标识，默认为'ECONOMIC'
        source (str): 数据源，默认为'FRED'
        unit (str): 数据单位（如 '%', '亿美元'）
        frequency (str): 数据频率（日/周/月/季/年）
        seasonal_adjustment (str): 季节性调整说明
        last_updated (Optional[datetime]): 最后更新时间
        notes (Optional[str]): 数据说明备注
    
    支持的数据频率:
        - Daily: 日度数据
        - Weekly: 周度数据
        - Monthly: 月度数据
        - Quarterly: 季度数据
        - Annual: 年度数据
    
    验证规则:
        - 数据序列ID不能为空且必须为字符串
        - 数据频率必须在支持列表中
    
    使用示例:
        GDP数据:
        >>> gdp_data = EconomicData(
        ...     series_id="GDP",
        ...     timestamp=datetime(2024, 1, 1),
        ...     value=25000.0,
        ...     unit="十亿美元",
        ...     frequency="Quarterly",
        ...     seasonal_adjustment="季节性调整",
        ...     notes="美国实际GDP"
        ... )
        
        CPI数据:
        >>> cpi_data = EconomicData(
        ...     series_id="CPIAUCSL",
        ...     timestamp=datetime(2024, 1, 1),
        ...     value=310.5,
        ...     unit="指数",
        ...     frequency="Monthly",
        ...     notes="美国消费者价格指数"
        ... )
    
    注意事项:
        - 经济数据通常有发布延迟
        - 数据可能会被修正，需要关注更新时间
        - 不同指标的单位和含义差异很大
    """
    
    series_id: str
    timestamp: datetime
    value: float
    market: str = 'ECONOMIC'
    source: str = 'FRED'
    unit: str = ''
    frequency: str = 'Daily'  # Daily, Weekly, Monthly, Quarterly, Annual
    seasonal_adjustment: str = 'Not Seasonally Adjusted'
    last_updated: Optional[datetime] = None
    notes: Optional[str] = None
    
    def validate(self) -> bool:
        """
        验证经济数据的有效性
        
        检查经济数据的格式和内容是否符合要求。
        
        返回:
            bool: 验证通过返回True，否则返回False
            
        验证项目:
            - 数据序列ID格式检查
            - 数据频率有效性检查
        """
        if not self.series_id or not isinstance(self.series_id, str):
            return False
        if self.frequency not in ['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Annual']:
            return False
        return True


@dataclass
class TickData(BaseModel):
    """
    高频tick数据结构
    
    用于存储实时的逐笔交易数据，包含最新价格、买卖价、成交量等信息。
    主要用于高频交易和实时市场分析。
    
    属性:
        symbol (str): 交易标的代码
        timestamp (datetime): 数据时间戳（精确到毫秒）
        last_price (float): 最新成交价
        bid_price (Optional[float]): 买一价
        ask_price (Optional[float]): 卖一价
        bid_size (Optional[float]): 买一量
        ask_size (Optional[float]): 卖一量
        volume (Optional[float]): 成交量
        turnover (Optional[float]): 成交额
        open_interest (Optional[float]): 持仓量（期货用）
    """
    
    symbol: str
    timestamp: datetime
    last_price: float
    bid_price: Optional[float] = None
    ask_price: Optional[float] = None
    bid_size: Optional[float] = None
    ask_size: Optional[float] = None
    volume: Optional[float] = None
    turnover: Optional[float] = None
    open_interest: Optional[float] = None
    
    def validate(self) -> bool:
        """验证tick数据的有效性"""
        if not self.symbol or not isinstance(self.symbol, str):
            return False
        if not isinstance(self.timestamp, datetime):
            return False
        if self.last_price <= 0:
            return False
        return True


@dataclass
class MarketInfo(BaseModel):
    """
    市场信息和交易规则结构
    
    存储交易标的的基本信息和交易规则，包括交易时间、最小交易单位、
    价格变动单位等重要的交易参数。
    
    属性:
        symbol (str): 交易标的代码
        name (str): 交易标的名称
        market (str): 所属市场（'US', 'CN', 'CRYPTO', 'ECONOMIC'）
        exchange (str): 交易所代码（'NASDAQ', 'SSE', 'BINANCE', 'FRED'）
        currency (str): 计价货币
        lot_size (float): 最小交易单位（手数）
        tick_size (float): 最小价格变动单位
        trading_hours (Dict[str, str]): 交易时间段
        timezone (str): 时区信息
        is_active (bool): 是否活跃交易，默认为True
    
    验证规则:
        - 基本信息字段不能为空
        - 最小交易单位和价格变动单位必须大于0
    
    使用示例:
        美股信息:
        >>> apple_info = MarketInfo(
        ...     symbol="AAPL",
        ...     name="Apple Inc.",
        ...     market="US",
        ...     exchange="NASDAQ",
        ...     currency="USD",
        ...     lot_size=1.0,
        ...     tick_size=0.01,
        ...     trading_hours={"open": "09:30", "close": "16:00"},
        ...     timezone="America/New_York"
        ... )
        
        A股信息:
        >>> stock_info = MarketInfo(
        ...     symbol="000001.SZ",
        ...     name="平安银行",
        ...     market="CN",
        ...     exchange="SZSE",
        ...     currency="CNY",
        ...     lot_size=100.0,
        ...     tick_size=0.01,
        ...     trading_hours={"open": "09:30", "close": "15:00"},
        ...     timezone="Asia/Shanghai"
        ... )
    
    注意事项:
        - 交易时间可能因节假日调整
        - 不同市场的交易规则差异很大
        - 最小交易单位影响订单数量计算
    """
    
    symbol: str
    name: str
    market: str  # 'US', 'CN', 'CRYPTO', 'ECONOMIC'
    exchange: str  # 'NASDAQ', 'SSE', 'BINANCE', 'FRED'
    currency: str
    lot_size: float
    tick_size: float
    trading_hours: Dict[str, str]
    timezone: str
    is_active: bool = True
    
    def validate(self) -> bool:
        """
        验证市场信息的有效性
        
        检查市场信息的完整性和合理性。
        
        返回:
            bool: 验证通过返回True，否则返回False
            
        验证项目:
            - 基本信息字段完整性检查
            - 交易参数合理性检查
        """
        if not all([self.symbol, self.name, self.market, self.exchange, self.currency, self.timezone]):
            return False
        if self.lot_size <= 0 or self.tick_size <= 0:
            return False
        return True