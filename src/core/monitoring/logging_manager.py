"""
日志管理和错误追踪系统
"""

import os
import sys
import json
import logging
import logging.handlers
import traceback
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import threading
from queue import Queue, Empty

from config.settings import config


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: str
    level: str
    logger: str
    message: str
    module: Optional[str] = None
    function_name: Optional[str] = None
    line_number: Optional[int] = None
    exception_info: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None
    thread_id: Optional[int] = None
    process_id: Optional[int] = None


class DatabaseLogHandler(logging.Handler):
    """数据库日志处理器"""
    
    def __init__(self, batch_size: int = 100, flush_interval: int = 30):
        super().__init__()
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.log_queue = Queue()
        self.batch_logs = []
        
        # 启动后台线程处理日志
        self.worker_thread = threading.Thread(target=self._process_logs, daemon=True)
        self.worker_thread.start()
        
        # 启动定时刷新线程
        self.flush_thread = threading.Thread(target=self._flush_periodically, daemon=True)
        self.flush_thread.start()
    
    def emit(self, record):
        """发送日志记录"""
        try:
            log_entry = self._create_log_entry(record)
            self.log_queue.put(log_entry)
        except Exception:
            self.handleError(record)
    
    def _create_log_entry(self, record) -> LogEntry:
        """创建日志条目"""
        # 获取异常信息
        exception_info = None
        if record.exc_info:
            try:
                exc_info_tuple = record.exc_info
                if exc_info_tuple is True:
                    exc_info_tuple = sys.exc_info()
                exception_info = ''.join(traceback.format_exception(*exc_info_tuple))
            except Exception:
                exception_info = None
        
        # 获取额外数据
        extra_data = {}
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'exc_info', 'exc_text', 'stack_info']:
                extra_data[key] = value
        
        return LogEntry(
            timestamp=datetime.fromtimestamp(record.created).isoformat(),
            level=record.levelname,
            logger=record.name,
            message=record.getMessage(),
            module=record.module,
            function_name=record.funcName,
            line_number=record.lineno,
            exception_info=exception_info,
            extra_data=extra_data if extra_data else None,
            thread_id=record.thread,
            process_id=record.process
        )
    
    def _process_logs(self):
        """处理日志队列"""
        while True:
            try:
                # 从队列获取日志
                log_entry = self.log_queue.get(timeout=1)
                self.batch_logs.append(log_entry)
                
                # 批量写入数据库
                if len(self.batch_logs) >= self.batch_size:
                    self._flush_to_database()
                    
            except Empty:
                continue
            except Exception as e:
                logger.info(f"处理日志失败: {e}", file=sys.stderr)
    
    def _flush_periodically(self):
        """定期刷新日志"""
        import time
        
        while True:
            time.sleep(self.flush_interval)
            if self.batch_logs:
                self._flush_to_database()
    
    def _flush_to_database(self):
        """刷新日志到数据库"""
        if not self.batch_logs:
            return
        
        try:
            from sqlalchemy import create_engine, text
            
            engine = create_engine(config.get_database_url())
            
            with engine.connect() as conn:
                for log_entry in self.batch_logs:
                    conn.execute(text("""
                        INSERT INTO system_logs 
                        (timestamp, level, logger, message, module, function_name, 
                         line_number, exception_info, extra_data)
                        VALUES (:timestamp, :level, :logger, :message, :module, 
                                :function_name, :line_number, :exception_info, :extra_data)
                    """), {
                        'timestamp': log_entry.timestamp,
                        'level': log_entry.level,
                        'logger': log_entry.logger,
                        'message': log_entry.message,
                        'module': log_entry.module,
                        'function_name': log_entry.function_name,
                        'line_number': log_entry.line_number,
                        'exception_info': log_entry.exception_info,
                        'extra_data': json.dumps(log_entry.extra_data) if log_entry.extra_data else None
                    })
                
                conn.commit()
            
            self.batch_logs.clear()
            
        except Exception as e:
            logger.info(f"写入数据库日志失败: {e}", file=sys.stderr)


class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record):
        """格式化日志记录为JSON"""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'process': record.process
        }
        
        # 添加异常信息
        if record.exc_info:
            try:
                exc_info_tuple = record.exc_info
                if exc_info_tuple is True:
                    exc_info_tuple = sys.exc_info()
                log_data['exception'] = ''.join(traceback.format_exception(*exc_info_tuple))
            except Exception:
                pass
        
        # 添加额外字段
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'exc_info', 'exc_text', 'stack_info']:
                log_data[key] = value
        
        return json.dumps(log_data, ensure_ascii=False)


class ErrorTracker:
    """错误追踪器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_counts = {}
        self.error_details = {}
    
    def track_error(self, error: Exception, context: Dict[str, Any] = None):
        """追踪错误"""
        error_type = type(error).__name__
        error_message = str(error)
        error_key = f"{error_type}:{error_message}"
        
        # 统计错误次数
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # 记录错误详情
        error_detail = {
            'type': error_type,
            'message': error_message,
            'traceback': traceback.format_exc(),
            'context': context or {},
            'timestamp': datetime.now().isoformat(),
            'count': self.error_counts[error_key]
        }
        
        self.error_details[error_key] = error_detail
        
        # 记录日志
        self.logger.error(
            f"错误追踪: {error_type} - {error_message}",
            extra={
                'error_type': error_type,
                'error_count': self.error_counts[error_key],
                'context': context
            },
            exc_info=True
        )
        
        # 如果错误频繁发生，发送告警
        if self.error_counts[error_key] >= 10:
            self._send_alert(error_detail)
    
    def _send_alert(self, error_detail: Dict[str, Any]):
        """发送告警"""
        try:
            # 通过日志管理器发送告警
            from . import logging_manager
            
            if hasattr(logging_manager, 'alert_manager') and logging_manager.alert_manager:
                alert = logging_manager.alert_manager.create_alert(
                    title=f"高频错误告警: {error_detail['type']}",
                    message=f"错误 {error_detail['type']} 已发生 {error_detail['count']} 次",
                    severity="high" if error_detail['count'] >= 20 else "medium",
                    source="error_tracker",
                    tags=["high_frequency", "error"],
                    metadata=error_detail
                )
                logging_manager.alert_manager.send_alert(alert)
            else:
                # 回退到日志记录
                self.logger.critical(
                    f"高频错误告警: {error_detail['type']} 已发生 {error_detail['count']} 次",
                    extra={'alert': True, 'error_detail': error_detail}
                )
        except Exception as e:
            self.logger.error(f"发送告警失败: {e}")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        return {
            'total_errors': len(self.error_counts),
            'total_occurrences': sum(self.error_counts.values()),
            'top_errors': sorted(
                self.error_counts.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10],
            'recent_errors': [
                detail for detail in self.error_details.values()
                if datetime.fromisoformat(detail['timestamp']) > 
                   datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            ]
        }


class LogRotator:
    """日志轮转器 - 实现日志文件轮转"""
    
    def __init__(self, log_dir: str = None):
        self.log_dir = Path(log_dir) if log_dir else Path(config.logging.file_path).parent
        self.max_file_size = getattr(config.logging, 'max_file_size', 10 * 1024 * 1024)  # 10MB
        self.backup_count = getattr(config.logging, 'backup_count', 5)
        self.logger = logging.getLogger(__name__)
    
    def rotate_by_size(self, file_path: Path, max_size: int = None) -> bool:
        """基于大小的轮转策略"""
        try:
            max_size = max_size or self.max_file_size
            
            if not file_path.exists():
                return False
            
            if file_path.stat().st_size <= max_size:
                return False
            
            # 创建轮转文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            rotated_name = f"{file_path.stem}_{timestamp}.log"
            rotated_path = file_path.parent / rotated_name
            
            # 移动当前文件
            file_path.rename(rotated_path)
            
            # 压缩文件
            self._compress_file(rotated_path)
            
            self.logger.info(f"基于大小轮转日志: {file_path.name} -> {rotated_name}.gz")
            return True
            
        except Exception as e:
            self.logger.error(f"基于大小轮转失败 {file_path}: {e}")
            return False
    
    def rotate_by_time(self, file_path: Path, max_age_hours: int = 24) -> bool:
        """基于时间的轮转策略"""
        try:
            if not file_path.exists():
                return False
            
            # 检查文件年龄
            file_age = datetime.now().timestamp() - file_path.stat().st_mtime
            max_age_seconds = max_age_hours * 3600
            
            if file_age <= max_age_seconds:
                return False
            
            # 创建轮转文件名
            timestamp = datetime.fromtimestamp(file_path.stat().st_mtime).strftime("%Y%m%d_%H%M%S")
            rotated_name = f"{file_path.stem}_{timestamp}.log"
            rotated_path = file_path.parent / rotated_name
            
            # 移动当前文件
            file_path.rename(rotated_path)
            
            # 压缩文件
            self._compress_file(rotated_path)
            
            self.logger.info(f"基于时间轮转日志: {file_path.name} -> {rotated_name}.gz")
            return True
            
        except Exception as e:
            self.logger.error(f"基于时间轮转失败 {file_path}: {e}")
            return False
    
    def _compress_file(self, file_path: Path):
        """压缩和归档功能"""
        try:
            import gzip
            
            compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
            
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    f_out.writelines(f_in)
            
            # 删除原始文件
            file_path.unlink()
            
            self.logger.debug(f"压缩日志文件: {file_path.name} -> {compressed_path.name}")
            
        except Exception as e:
            self.logger.error(f"压缩文件失败 {file_path}: {e}")
    
    def cleanup_old_files(self):
        """日志清理和存储管理机制"""
        try:
            # 获取所有压缩的日志文件
            compressed_files = list(self.log_dir.glob("*.log.gz"))
            
            # 按修改时间分组
            file_groups = {}
            for file in compressed_files:
                # 提取基础文件名（去掉时间戳）
                base_name = '_'.join(file.stem.split('_')[:-2]) if '_' in file.stem else file.stem.replace('.log', '')
                if base_name not in file_groups:
                    file_groups[base_name] = []
                file_groups[base_name].append(file)
            
            # 为每个文件组清理旧文件
            for base_name, files in file_groups.items():
                # 按修改时间排序
                files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                
                # 删除超过备份数量的文件
                files_to_delete = files[self.backup_count:]
                for file_to_delete in files_to_delete:
                    file_to_delete.unlink()
                    self.logger.info(f"删除旧日志文件: {file_to_delete.name}")
            
        except Exception as e:
            self.logger.error(f"清理旧日志文件失败: {e}")
    
    def get_rotation_config(self) -> Dict[str, Any]:
        """日志轮转的配置和监控"""
        return {
            'log_dir': str(self.log_dir),
            'max_file_size': self.max_file_size,
            'backup_count': self.backup_count,
            'total_log_files': len(list(self.log_dir.glob("*.log"))),
            'total_compressed_files': len(list(self.log_dir.glob("*.log.gz"))),
            'disk_usage': sum(f.stat().st_size for f in self.log_dir.iterdir() if f.is_file())
        }
    
    def rotate_all_logs(self):
        """轮转所有日志文件"""
        try:
            log_files = list(self.log_dir.glob("*.log"))
            
            for log_file in log_files:
                # 尝试基于大小轮转
                if not self.rotate_by_size(log_file):
                    # 如果大小不够，尝试基于时间轮转
                    self.rotate_by_time(log_file, max_age_hours=24)
            
            # 清理旧文件
            self.cleanup_old_files()
            
        except Exception as e:
            self.logger.error(f"轮转所有日志失败: {e}")


class LogAnalyzer:
    """日志分析器 - 分析日志内容和模式"""
    
    def __init__(self, log_dir: str = None):
        self.log_dir = Path(log_dir) if log_dir else Path(config.logging.file_path).parent
        self.logger = logging.getLogger(__name__)
        self.error_patterns = {}
        self.statistics = {}
    
    def analyze_error_patterns(self) -> List[Dict[str, Any]]:
        """错误模式识别和分类算法"""
        patterns = []
        
        try:
            # 分析所有错误日志文件
            error_files = list(self.log_dir.glob("*error*.log"))
            
            for error_file in error_files:
                file_patterns = self._analyze_single_file(error_file)
                patterns.extend(file_patterns)
            
            # 合并和分类模式
            classified_patterns = self._classify_patterns(patterns)
            
            return classified_patterns
            
        except Exception as e:
            self.logger.error(f"分析错误模式失败: {e}")
            return []
    
    def _analyze_single_file(self, file_path: Path) -> List[Dict[str, Any]]:
        """分析单个日志文件"""
        patterns = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            error_lines = [line for line in lines if 'ERROR' in line or 'CRITICAL' in line]
            
            # 提取错误模式
            for line in error_lines:
                pattern = self._extract_error_pattern(line, file_path.name)
                if pattern:
                    patterns.append(pattern)
            
        except Exception as e:
            self.logger.error(f"分析文件失败 {file_path}: {e}")
        
        return patterns
    
    def _extract_error_pattern(self, line: str, filename: str) -> Dict[str, Any]:
        """从日志行提取错误模式"""
        try:
            import re
            
            # 提取时间戳
            timestamp_match = re.search(r'\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}', line)
            timestamp = timestamp_match.group() if timestamp_match else None
            
            # 提取错误级别
            level_match = re.search(r'(ERROR|CRITICAL|WARNING)', line)
            level = level_match.group() if level_match else 'UNKNOWN'
            
            # 提取模块名
            module_match = re.search(r'(\w+\.py|\w+\.\w+)', line)
            module = module_match.group() if module_match else 'unknown'
            
            # 提取错误消息
            message_parts = line.split(' - ')
            message = message_parts[-1].strip() if len(message_parts) > 1 else line.strip()
            
            # 生成模式键
            pattern_key = self._generate_pattern_key(message)
            
            return {
                'pattern_key': pattern_key,
                'timestamp': timestamp,
                'level': level,
                'module': module,
                'message': message,
                'filename': filename,
                'raw_line': line.strip()
            }
            
        except Exception as e:
            self.logger.error(f"提取错误模式失败: {e}")
            return None
    
    def _generate_pattern_key(self, message: str) -> str:
        """生成模式键"""
        import re
        
        # 移除数字、时间戳、路径等变化的部分
        pattern = re.sub(r'\d+', 'NUM', message)
        pattern = re.sub(r'/[^\s]+', 'PATH', pattern)
        pattern = re.sub(r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', 'UUID', pattern)
        pattern = re.sub(r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b', 'IP', pattern)
        
        return pattern.lower().strip()
    
    def _classify_patterns(self, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """分类错误模式"""
        pattern_groups = {}
        
        # 按模式键分组
        for pattern in patterns:
            key = pattern['pattern_key']
            if key not in pattern_groups:
                pattern_groups[key] = []
            pattern_groups[key].append(pattern)
        
        classified = []
        
        for pattern_key, group in pattern_groups.items():
            if len(group) >= 2:  # 至少出现2次才认为是模式
                classification = {
                    'pattern_key': pattern_key,
                    'count': len(group),
                    'severity': self._calculate_severity(group),
                    'first_occurrence': min(p['timestamp'] for p in group if p['timestamp']),
                    'last_occurrence': max(p['timestamp'] for p in group if p['timestamp']),
                    'affected_modules': list(set(p['module'] for p in group)),
                    'sample_message': group[0]['message'],
                    'recommendation': self._get_pattern_recommendation(pattern_key, group)
                }
                classified.append(classification)
        
        # 按严重性和频率排序
        classified.sort(key=lambda x: (x['severity'], x['count']), reverse=True)
        
        return classified
    
    def _calculate_severity(self, group: List[Dict[str, Any]]) -> str:
        """计算严重性"""
        critical_count = sum(1 for p in group if p['level'] == 'CRITICAL')
        error_count = sum(1 for p in group if p['level'] == 'ERROR')
        total_count = len(group)
        
        if critical_count > 0:
            return 'critical'
        elif error_count >= total_count * 0.8:
            return 'high'
        elif total_count >= 10:
            return 'medium'
        else:
            return 'low'
    
    def _get_pattern_recommendation(self, pattern_key: str, group: List[Dict[str, Any]]) -> str:
        """获取模式修复建议"""
        if 'connection' in pattern_key or 'timeout' in pattern_key:
            return "检查网络连接稳定性，增加连接重试机制和超时配置"
        elif 'permission' in pattern_key or 'access denied' in pattern_key:
            return "检查文件和目录权限，确保应用有足够的访问权限"
        elif 'memory' in pattern_key or 'out of memory' in pattern_key:
            return "监控内存使用情况，优化内存管理，考虑增加内存限制"
        elif 'database' in pattern_key or 'sql' in pattern_key:
            return "检查数据库连接池配置，优化SQL查询，验证数据完整性"
        elif 'config' in pattern_key or 'configuration' in pattern_key:
            return "验证配置文件完整性，检查必需的配置项是否存在"
        elif 'import' in pattern_key or 'module' in pattern_key:
            return "检查依赖包安装，验证Python路径配置"
        else:
            return "分析错误堆栈信息，进行针对性代码修复"
    
    def get_log_statistics(self) -> Dict[str, Any]:
        """日志统计和趋势分析功能"""
        try:
            stats = {
                'total_files': 0,
                'total_size': 0,
                'level_counts': {'DEBUG': 0, 'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'CRITICAL': 0},
                'hourly_distribution': {},
                'module_stats': {},
                'file_stats': {}
            }
            
            # 分析所有日志文件
            log_files = list(self.log_dir.glob("*.log"))
            
            for log_file in log_files:
                try:
                    file_stats = self._analyze_file_statistics(log_file)
                    
                    stats['total_files'] += 1
                    stats['total_size'] += log_file.stat().st_size
                    stats['file_stats'][log_file.name] = file_stats
                    
                    # 合并级别统计
                    for level, count in file_stats['level_counts'].items():
                        stats['level_counts'][level] += count
                    
                    # 合并小时分布
                    for hour, count in file_stats['hourly_distribution'].items():
                        stats['hourly_distribution'][hour] = stats['hourly_distribution'].get(hour, 0) + count
                    
                    # 合并模块统计
                    for module, count in file_stats['module_stats'].items():
                        stats['module_stats'][module] = stats['module_stats'].get(module, 0) + count
                        
                except Exception as e:
                    self.logger.error(f"分析文件统计失败 {log_file}: {e}")
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取日志统计失败: {e}")
            return {}
    
    def _analyze_file_statistics(self, file_path: Path) -> Dict[str, Any]:
        """分析单个文件的统计信息"""
        stats = {
            'level_counts': {'DEBUG': 0, 'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'CRITICAL': 0},
            'hourly_distribution': {},
            'module_stats': {},
            'total_lines': 0
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    stats['total_lines'] += 1
                    
                    # 统计日志级别
                    for level in stats['level_counts'].keys():
                        if level in line:
                            stats['level_counts'][level] += 1
                            break
                    
                    # 提取小时信息
                    import re
                    hour_match = re.search(r'\d{2}:(\d{2}):\d{2}', line)
                    if hour_match:
                        hour = hour_match.group(1)
                        stats['hourly_distribution'][hour] = stats['hourly_distribution'].get(hour, 0) + 1
                    
                    # 提取模块信息
                    module_match = re.search(r'(\w+\.py|\w+\.\w+)', line)
                    if module_match:
                        module = module_match.group()
                        stats['module_stats'][module] = stats['module_stats'].get(module, 0) + 1
                        
        except Exception as e:
            self.logger.error(f"分析文件统计失败 {file_path}: {e}")
        
        return stats
    
    def detect_anomalies(self) -> List[Dict[str, Any]]:
        """异常检测和预警机制"""
        anomalies = []
        
        try:
            stats = self.get_log_statistics()
            
            # 检测错误率异常
            total_logs = sum(stats['level_counts'].values())
            if total_logs > 0:
                error_rate = (stats['level_counts']['ERROR'] + stats['level_counts']['CRITICAL']) / total_logs
                
                if error_rate > 0.1:  # 错误率超过10%
                    anomalies.append({
                        'type': 'high_error_rate',
                        'severity': 'high',
                        'value': error_rate,
                        'threshold': 0.1,
                        'description': f"错误率异常高: {error_rate:.2%}",
                        'recommendation': "立即检查系统状态，分析错误原因"
                    })
            
            # 检测日志量异常
            if stats['total_files'] > 100:  # 日志文件过多
                anomalies.append({
                    'type': 'too_many_log_files',
                    'severity': 'medium',
                    'value': stats['total_files'],
                    'threshold': 100,
                    'description': f"日志文件数量过多: {stats['total_files']}",
                    'recommendation': "启用日志轮转和清理机制"
                })
            
            # 检测磁盘使用异常
            if stats['total_size'] > 1024 * 1024 * 1024:  # 超过1GB
                anomalies.append({
                    'type': 'large_log_size',
                    'severity': 'medium',
                    'value': stats['total_size'],
                    'threshold': 1024 * 1024 * 1024,
                    'description': f"日志占用磁盘空间过大: {stats['total_size'] / (1024*1024*1024):.2f}GB",
                    'recommendation': "清理旧日志文件，优化日志级别配置"
                })
            
        except Exception as e:
            self.logger.error(f"异常检测失败: {e}")
        
        return anomalies
    
    def search_logs(self, query: str, start_time: str = None, end_time: str = None, 
                   level: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """日志搜索和查询功能"""
        results = []
        
        try:
            import re
            from datetime import datetime
            
            # 编译搜索模式
            pattern = re.compile(query, re.IGNORECASE) if query else None
            
            # 解析时间范围
            start_dt = datetime.fromisoformat(start_time) if start_time else None
            end_dt = datetime.fromisoformat(end_time) if end_time else None
            
            # 搜索所有日志文件
            log_files = list(self.log_dir.glob("*.log"))
            
            for log_file in log_files:
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        for line_num, line in enumerate(f, 1):
                            # 检查查询条件
                            if pattern and not pattern.search(line):
                                continue
                            
                            if level and level not in line:
                                continue
                            
                            # 提取时间戳并检查时间范围
                            timestamp_match = re.search(r'\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}', line)
                            if timestamp_match:
                                try:
                                    log_time = datetime.fromisoformat(timestamp_match.group().replace(' ', 'T'))
                                    if start_dt and log_time < start_dt:
                                        continue
                                    if end_dt and log_time > end_dt:
                                        continue
                                except:
                                    pass
                            
                            # 添加到结果
                            results.append({
                                'file': log_file.name,
                                'line_number': line_num,
                                'timestamp': timestamp_match.group() if timestamp_match else None,
                                'content': line.strip(),
                                'level': self._extract_log_level(line)
                            })
                            
                            # 限制结果数量
                            if len(results) >= limit:
                                break
                    
                    if len(results) >= limit:
                        break
                        
                except Exception as e:
                    self.logger.error(f"搜索文件失败 {log_file}: {e}")
            
        except Exception as e:
            self.logger.error(f"日志搜索失败: {e}")
        
        return results
    
    def _extract_log_level(self, line: str) -> str:
        """提取日志级别"""
        levels = ['CRITICAL', 'ERROR', 'WARNING', 'INFO', 'DEBUG']
        for level in levels:
            if level in line:
                return level
        return 'UNKNOWN'


@dataclass
class Alert:
    """告警数据模型"""
    id: str
    title: str
    message: str
    severity: str  # low, medium, high, critical
    source: str
    timestamp: str
    tags: List[str] = None
    metadata: Dict[str, Any] = None
    resolved: bool = False
    resolved_at: str = None


@dataclass
class AlertRule:
    """告警规则"""
    id: str
    name: str
    condition: str
    severity: str
    enabled: bool = True
    cooldown_minutes: int = 60
    channels: List[str] = None
    metadata: Dict[str, Any] = None


class AlertManager:
    """告警管理器 - 管理告警和通知"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.alert_rules = {}
        self.active_alerts = {}
        self.alert_history = []
        self.notification_channels = {}
        self.cooldown_tracker = {}
        
        # 初始化默认告警规则
        self._initialize_default_rules()
        
        # 初始化通知渠道
        self._initialize_notification_channels()
    
    def _initialize_default_rules(self):
        """初始化默认告警规则"""
        default_rules = [
            AlertRule(
                id="high_error_rate",
                name="高错误率告警",
                condition="error_rate > 0.1",
                severity="high",
                cooldown_minutes=30,
                channels=["email", "log"]
            ),
            AlertRule(
                id="critical_error",
                name="严重错误告警",
                condition="level == 'CRITICAL'",
                severity="critical",
                cooldown_minutes=5,
                channels=["email", "log", "webhook"]
            ),
            AlertRule(
                id="disk_space_low",
                name="磁盘空间不足",
                condition="disk_usage > 0.9",
                severity="high",
                cooldown_minutes=60,
                channels=["email", "log"]
            ),
            AlertRule(
                id="memory_usage_high",
                name="内存使用率过高",
                condition="memory_usage > 0.85",
                severity="medium",
                cooldown_minutes=30,
                channels=["log"]
            )
        ]
        
        for rule in default_rules:
            self.alert_rules[rule.id] = rule
    
    def _initialize_notification_channels(self):
        """初始化通知渠道"""
        self.notification_channels = {
            "log": self._send_log_notification,
            "email": self._send_email_notification,
            "webhook": self._send_webhook_notification,
            "slack": self._send_slack_notification
        }
    
    def add_alert_rule(self, rule: AlertRule):
        """添加告警规则配置和管理功能"""
        self.alert_rules[rule.id] = rule
        self.logger.info(f"添加告警规则: {rule.name}")
    
    def remove_alert_rule(self, rule_id: str):
        """移除告警规则"""
        if rule_id in self.alert_rules:
            rule = self.alert_rules.pop(rule_id)
            self.logger.info(f"移除告警规则: {rule.name}")
            return True
        return False
    
    def update_alert_rule(self, rule_id: str, updates: Dict[str, Any]):
        """更新告警规则"""
        if rule_id in self.alert_rules:
            rule = self.alert_rules[rule_id]
            for key, value in updates.items():
                if hasattr(rule, key):
                    setattr(rule, key, value)
            self.logger.info(f"更新告警规则: {rule.name}")
            return True
        return False
    
    def create_alert(self, title: str, message: str, severity: str, source: str, 
                    tags: List[str] = None, metadata: Dict[str, Any] = None) -> Alert:
        """创建告警"""
        import uuid
        
        alert = Alert(
            id=str(uuid.uuid4()),
            title=title,
            message=message,
            severity=severity,
            source=source,
            timestamp=datetime.now().isoformat(),
            tags=tags or [],
            metadata=metadata or {}
        )
        
        return alert
    
    def send_alert(self, alert: Alert):
        """发送告警"""
        try:
            # 检查是否在冷却期
            if self._is_in_cooldown(alert):
                self.logger.debug(f"告警在冷却期内，跳过: {alert.title}")
                return
            
            # 添加到活跃告警
            self.active_alerts[alert.id] = alert
            
            # 添加到历史记录
            self.alert_history.append(alert)
            
            # 发送通知
            self._send_notifications(alert)
            
            # 更新冷却期追踪
            self._update_cooldown_tracker(alert)
            
            self.logger.info(f"发送告警: {alert.title} - {alert.severity}")
            
        except Exception as e:
            self.logger.error(f"发送告警失败: {e}")
    
    def _is_in_cooldown(self, alert: Alert) -> bool:
        """检查是否在冷却期"""
        # 根据告警内容生成冷却键
        cooldown_key = f"{alert.source}:{alert.title}"
        
        if cooldown_key in self.cooldown_tracker:
            last_sent = datetime.fromisoformat(self.cooldown_tracker[cooldown_key])
            cooldown_minutes = self._get_cooldown_minutes(alert)
            
            if (datetime.now() - last_sent).total_seconds() < cooldown_minutes * 60:
                return True
        
        return False
    
    def _get_cooldown_minutes(self, alert: Alert) -> int:
        """获取冷却时间"""
        # 根据严重性确定默认冷却时间
        default_cooldowns = {
            'critical': 5,
            'high': 30,
            'medium': 60,
            'low': 120
        }
        
        return default_cooldowns.get(alert.severity, 60)
    
    def _update_cooldown_tracker(self, alert: Alert):
        """更新冷却期追踪"""
        cooldown_key = f"{alert.source}:{alert.title}"
        self.cooldown_tracker[cooldown_key] = alert.timestamp
    
    def _send_notifications(self, alert: Alert):
        """发送通知到各个渠道"""
        # 根据严重性确定通知渠道
        channels = self._get_notification_channels(alert)
        
        for channel in channels:
            try:
                if channel in self.notification_channels:
                    self.notification_channels[channel](alert)
                else:
                    self.logger.warning(f"未知的通知渠道: {channel}")
            except Exception as e:
                self.logger.error(f"发送通知失败 {channel}: {e}")
    
    def _get_notification_channels(self, alert: Alert) -> List[str]:
        """获取通知渠道"""
        # 根据严重性确定通知渠道
        if alert.severity == 'critical':
            return ['log', 'email', 'webhook']
        elif alert.severity == 'high':
            return ['log', 'email']
        elif alert.severity == 'medium':
            return ['log']
        else:
            return ['log']
    
    def _send_log_notification(self, alert: Alert):
        """发送日志通知"""
        log_level = getattr(logging, alert.severity.upper(), logging.INFO)
        self.logger.log(
            log_level,
            f"告警: {alert.title} - {alert.message}",
            extra={
                'alert_id': alert.id,
                'alert_severity': alert.severity,
                'alert_source': alert.source,
                'alert_tags': alert.tags,
                'alert_metadata': alert.metadata
            }
        )
    
    def _send_email_notification(self, alert: Alert):
        """发送邮件通知"""
        try:
            # 这里可以集成邮件发送服务
            # 例如使用 SMTP、SendGrid、AWS SES 等
            
            email_config = getattr(config, 'email', None)
            if not email_config:
                self.logger.warning("邮件配置未设置，跳过邮件通知")
                return
            
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            msg = MIMEMultipart()
            msg['From'] = email_config.get('from_address')
            msg['To'] = ', '.join(email_config.get('to_addresses', []))
            msg['Subject'] = f"[{alert.severity.upper()}] {alert.title}"
            
            body = f"""
告警详情:
- 标题: {alert.title}
- 消息: {alert.message}
- 严重性: {alert.severity}
- 来源: {alert.source}
- 时间: {alert.timestamp}
- 标签: {', '.join(alert.tags)}

元数据:
{json.dumps(alert.metadata, indent=2, ensure_ascii=False)}
            """
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            server = smtplib.SMTP(email_config.get('smtp_host'), email_config.get('smtp_port', 587))
            server.starttls()
            server.login(email_config.get('username'), email_config.get('password'))
            server.send_message(msg)
            server.quit()
            
            self.logger.info(f"邮件告警已发送: {alert.title}")
            
        except Exception as e:
            self.logger.error(f"发送邮件告警失败: {e}")
    
    def _send_webhook_notification(self, alert: Alert):
        """发送Webhook通知"""
        try:
            webhook_config = getattr(config, 'webhook', None)
            if not webhook_config:
                self.logger.warning("Webhook配置未设置，跳过Webhook通知")
                return
            
            import requests
            
            payload = {
                'alert_id': alert.id,
                'title': alert.title,
                'message': alert.message,
                'severity': alert.severity,
                'source': alert.source,
                'timestamp': alert.timestamp,
                'tags': alert.tags,
                'metadata': alert.metadata
            }
            
            response = requests.post(
                webhook_config.get('url'),
                json=payload,
                headers=webhook_config.get('headers', {}),
                timeout=30
            )
            
            response.raise_for_status()
            self.logger.info(f"Webhook告警已发送: {alert.title}")
            
        except Exception as e:
            self.logger.error(f"发送Webhook告警失败: {e}")
    
    def _send_slack_notification(self, alert: Alert):
        """发送Slack通知"""
        try:
            slack_config = getattr(config, 'slack', None)
            if not slack_config:
                self.logger.warning("Slack配置未设置，跳过Slack通知")
                return
            
            import requests
            
            color_map = {
                'critical': '#FF0000',
                'high': '#FF8C00',
                'medium': '#FFD700',
                'low': '#32CD32'
            }
            
            payload = {
                'text': f"告警: {alert.title}",
                'attachments': [{
                    'color': color_map.get(alert.severity, '#808080'),
                    'fields': [
                        {'title': '消息', 'value': alert.message, 'short': False},
                        {'title': '严重性', 'value': alert.severity, 'short': True},
                        {'title': '来源', 'value': alert.source, 'short': True},
                        {'title': '时间', 'value': alert.timestamp, 'short': True},
                        {'title': '标签', 'value': ', '.join(alert.tags), 'short': True}
                    ]
                }]
            }
            
            response = requests.post(
                slack_config.get('webhook_url'),
                json=payload,
                timeout=30
            )
            
            response.raise_for_status()
            self.logger.info(f"Slack告警已发送: {alert.title}")
            
        except Exception as e:
            self.logger.error(f"发送Slack告警失败: {e}")
    
    def resolve_alert(self, alert_id: str, resolution_note: str = None):
        """解决告警"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.resolved_at = datetime.now().isoformat()
            
            if resolution_note:
                alert.metadata = alert.metadata or {}
                alert.metadata['resolution_note'] = resolution_note
            
            # 从活跃告警中移除
            del self.active_alerts[alert_id]
            
            self.logger.info(f"告警已解决: {alert.title}")
            return True
        
        return False
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """获取告警历史记录"""
        return self.alert_history[-limit:]
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """告警历史记录和统计分析"""
        try:
            total_alerts = len(self.alert_history)
            active_count = len(self.active_alerts)
            
            # 按严重性统计
            severity_stats = {}
            for alert in self.alert_history:
                severity_stats[alert.severity] = severity_stats.get(alert.severity, 0) + 1
            
            # 按来源统计
            source_stats = {}
            for alert in self.alert_history:
                source_stats[alert.source] = source_stats.get(alert.source, 0) + 1
            
            # 按小时统计（最近24小时）
            hourly_stats = {}
            now = datetime.now()
            for alert in self.alert_history:
                try:
                    alert_time = datetime.fromisoformat(alert.timestamp)
                    if (now - alert_time).total_seconds() <= 24 * 3600:
                        hour = alert_time.hour
                        hourly_stats[hour] = hourly_stats.get(hour, 0) + 1
                except:
                    pass
            
            # 解决率统计
            resolved_count = sum(1 for alert in self.alert_history if alert.resolved)
            resolution_rate = resolved_count / total_alerts if total_alerts > 0 else 0
            
            return {
                'total_alerts': total_alerts,
                'active_alerts': active_count,
                'resolved_alerts': resolved_count,
                'resolution_rate': resolution_rate,
                'severity_distribution': severity_stats,
                'source_distribution': source_stats,
                'hourly_distribution': hourly_stats,
                'top_sources': sorted(source_stats.items(), key=lambda x: x[1], reverse=True)[:5]
            }
            
        except Exception as e:
            self.logger.error(f"获取告警统计失败: {e}")
            return {}
    
    def cleanup_old_alerts(self, days: int = 30):
        """清理旧告警记录"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            
            # 过滤历史记录
            self.alert_history = [
                alert for alert in self.alert_history
                if datetime.fromisoformat(alert.timestamp) > cutoff_time
            ]
            
            # 清理冷却期追踪
            old_cooldown_keys = []
            for key, timestamp_str in self.cooldown_tracker.items():
                try:
                    if datetime.fromisoformat(timestamp_str) < cutoff_time:
                        old_cooldown_keys.append(key)
                except:
                    old_cooldown_keys.append(key)
            
            for key in old_cooldown_keys:
                del self.cooldown_tracker[key]
            
            self.logger.info(f"清理了 {len(old_cooldown_keys)} 个旧告警记录")
            
        except Exception as e:
            self.logger.error(f"清理旧告警记录失败: {e}")


class LoggingManager:
    """日志管理器 - 统一管理系统日志"""
    
    def __init__(self):
        self.error_tracker = ErrorTracker()
        self.configured = False
        self.log_rotator = LogRotator()
        self.log_analyzer = LogAnalyzer()
        self.alert_manager = AlertManager()
    
    def setup_logging_configuration(self):
        """配置日志系统 - 统一的日志格式和结构化日志支持"""
        if self.configured:
            return
        
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志系统"""
        if self.configured:
            return
        
        # 创建日志目录
        log_dir = Path(config.logging.file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, config.logging.level.upper()))
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 创建格式化器
        if hasattr(config.logging, 'enable_json_format') and config.logging.enable_json_format:
            formatter = JSONFormatter()
        else:
            formatter = logging.Formatter(config.logging.format)
        
        # 文件处理器（轮转）
        file_handler = logging.handlers.RotatingFileHandler(
            config.logging.file_path,
            maxBytes=config.logging.max_file_size,
            backupCount=config.logging.backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        root_logger.addHandler(file_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(getattr(logging, config.logging.level.upper()))
        root_logger.addHandler(console_handler)
        
        # 错误文件处理器
        error_handler = logging.handlers.RotatingFileHandler(
            str(log_dir / "errors.log"),
            maxBytes=config.logging.max_file_size,
            backupCount=config.logging.backup_count,
            encoding='utf-8'
        )
        error_handler.setFormatter(formatter)
        error_handler.setLevel(logging.ERROR)
        root_logger.addHandler(error_handler)
        
        # 数据库处理器（生产环境）
        if config.is_production():
            try:
                db_handler = DatabaseLogHandler()
                db_handler.setFormatter(formatter)
                db_handler.setLevel(logging.WARNING)
                root_logger.addHandler(db_handler)
            except Exception as e:
                logging.error(f"设置数据库日志处理器失败: {e}")
        
        # Syslog处理器（生产环境）
        if (config.is_production() and 
            hasattr(config.logging, 'enable_syslog') and 
            config.logging.enable_syslog):
            try:
                syslog_handler = logging.handlers.SysLogHandler(
                    address=config.logging.syslog_address
                )
                syslog_handler.setFormatter(formatter)
                syslog_handler.setLevel(logging.WARNING)
                root_logger.addHandler(syslog_handler)
            except Exception as e:
                logging.error(f"设置Syslog处理器失败: {e}")
        
        # 设置第三方库日志级别
        self._configure_third_party_loggers()
        
        # 安装全局异常处理器
        self._install_exception_handler()
        
        self.configured = True
        logging.info(f"日志系统初始化完成 - 环境: {config.environment}")
    
    def rotate_log_files(self):
        """自动轮转日志文件"""
        if self.log_rotator:
            self.log_rotator.rotate_all_logs()
        else:
            logging.error("日志轮转器未初始化")
    
    def analyze_error_patterns(self) -> List[Dict[str, Any]]:
        """分析错误模式"""
        if self.log_analyzer:
            patterns = self.log_analyzer.analyze_error_patterns()
            
            # 检查是否需要发送告警
            self._check_pattern_alerts(patterns)
            
            return patterns
        else:
            logging.error("日志分析器未初始化")
            return []
    
    def _check_pattern_alerts(self, patterns: List[Dict[str, Any]]):
        """检查模式告警"""
        if not self.alert_manager:
            return
        
        for pattern in patterns:
            if pattern.get('severity') in ['critical', 'high']:
                alert = self.alert_manager.create_alert(
                    title=f"检测到{pattern.get('severity')}级别错误模式",
                    message=f"模式: {pattern.get('pattern_key', 'Unknown')}, 出现次数: {pattern.get('count', 0)}",
                    severity=pattern.get('severity', 'medium'),
                    source="log_analyzer",
                    tags=["error_pattern", "automated"],
                    metadata=pattern
                )
                self.alert_manager.send_alert(alert)
    
    def send_alert(self, title: str, message: str, severity: str = "medium", 
                  source: str = "logging_manager", tags: List[str] = None, 
                  metadata: Dict[str, Any] = None):
        """发送告警"""
        if self.alert_manager:
            alert = self.alert_manager.create_alert(
                title=title,
                message=message,
                severity=severity,
                source=source,
                tags=tags,
                metadata=metadata
            )
            self.alert_manager.send_alert(alert)
        else:
            logging.error("告警管理器未初始化")
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """获取告警统计"""
        if self.alert_manager:
            return self.alert_manager.get_alert_statistics()
        else:
            return {}
    
    def _configure_third_party_loggers(self):
        """配置第三方库日志级别"""
        third_party_loggers = {
            'urllib3': logging.WARNING,
            'requests': logging.WARNING,
            'sqlalchemy.engine': logging.WARNING,
            'sqlalchemy.pool': logging.WARNING,
            'celery': logging.INFO,
            'uvicorn': logging.INFO,
            'fastapi': logging.INFO
        }
        
        for logger_name, level in third_party_loggers.items():
            logging.getLogger(logger_name).setLevel(level)
    
    def _install_exception_handler(self):
        """安装全局异常处理器"""
        def handle_exception(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            # 追踪错误
            self.error_tracker.track_error(exc_value, {
                'type': 'unhandled_exception',
                'traceback': ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            })
            
            # 记录日志
            logging.critical(
                "未处理的异常",
                exc_info=(exc_type, exc_value, exc_traceback)
            )
        
        sys.excepthook = handle_exception
    
    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计"""
        try:
            from sqlalchemy import create_engine, text
            
            engine = create_engine(config.get_database_url())
            
            with engine.connect() as conn:
                # 获取日志级别统计
                result = conn.execute(text("""
                    SELECT level, COUNT(*) as count
                    FROM system_logs
                    WHERE timestamp >= NOW() - INTERVAL '24 hours'
                    GROUP BY level
                    ORDER BY count DESC
                """))
                
                level_stats = {row[0]: row[1] for row in result}
                
                # 获取最近错误
                result = conn.execute(text("""
                    SELECT timestamp, logger, message, exception_info
                    FROM system_logs
                    WHERE level IN ('ERROR', 'CRITICAL')
                    AND timestamp >= NOW() - INTERVAL '1 hour'
                    ORDER BY timestamp DESC
                    LIMIT 10
                """))
                
                recent_errors = [
                    {
                        'timestamp': row[0].isoformat(),
                        'logger': row[1],
                        'message': row[2],
                        'exception': row[3]
                    }
                    for row in result
                ]
                
                return {
                    'level_stats': level_stats,
                    'recent_errors': recent_errors,
                    'error_summary': self.error_tracker.get_error_summary()
                }
                
        except Exception as e:
            logging.error(f"获取日志统计失败: {e}")
            return {
                'error_summary': self.error_tracker.get_error_summary()
            }
    
    def cleanup_old_logs(self, days: int = 30):
        """清理旧日志"""
        try:
            from sqlalchemy import create_engine, text
            
            engine = create_engine(config.get_database_url())
            
            with engine.connect() as conn:
                result = conn.execute(text("""
                    DELETE FROM system_logs
                    WHERE timestamp < NOW() - INTERVAL '%s days'
                """), (days,))
                
                deleted_count = result.rowcount
                conn.commit()
                
                logging.info(f"清理了 {deleted_count} 条旧日志记录")
                return deleted_count
                
        except Exception as e:
            logging.error(f"清理旧日志失败: {e}")
            return 0


# 全局日志管理器实例
logging_manager = LoggingManager()


def setup_logging():
    """设置日志系统（便捷函数）"""
    logging_manager.setup_logging()


def track_error(error: Exception, context: Dict[str, Any] = None):
    """追踪错误（便捷函数）"""
    logging_manager.error_tracker.track_error(error, context)


def get_logger(name: str) -> logging.Logger:
    """获取日志器（便捷函数）"""
    return logging.getLogger(name)