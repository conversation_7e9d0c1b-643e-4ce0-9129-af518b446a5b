"""
增强错误处理器

整合错误分类、诊断和建议功能，提供统一的错误处理流程。
"""

import os
import json
import logging
import traceback
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict
import threading
import queue

from src.error_handling.error_classifier import ErrorClassifier, ClassifiedError
from src.error_handling.models import ErrorType as ErrorCategory, Severity as ErrorSeverity
from .diagnostic_engine import DiagnosticEngine, DiagnosticReport, Issue
from .recommendation_engine import RecommendationEngine, Recommendation
try:
    from src.common.exceptions import TradingSystemException  # legacy
except Exception:
    from src.common.exceptions import TradingSystemException


@dataclass
class ErrorReport:
    """错误报告"""
    report_id: str
    classified_error: ClassifiedError
    diagnostic_info: Optional[Dict[str, Any]] = None
    recommendations: List[Recommendation] = field(default_factory=list)
    resolution_status: str = "open"  # open, in_progress, resolved, ignored
    resolution_notes: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'report_id': self.report_id,
            'error': {
                'type': type(self.classified_error.original_error).__name__,
                'message': str(self.classified_error.original_error),
                'category': self.classified_error.category.value,
                'severity': self.classified_error.severity.value,
                'component': self.classified_error.component,
                'timestamp': self.classified_error.timestamp.isoformat(),
                'pattern_id': self.classified_error.pattern_id
            },
            'diagnostic_info': self.diagnostic_info,
            'recommendations': [rec.to_dict() for rec in self.recommendations],
            'resolution_status': self.resolution_status,
            'resolution_notes': self.resolution_notes,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }


@dataclass
class ErrorHandlingConfig:
    """错误处理配置"""
    auto_classify: bool = True
    auto_diagnose: bool = True
    auto_recommend: bool = True
    save_reports: bool = True
    report_directory: str = "data/error_reports"
    max_reports: int = 1000
    auto_resolve_patterns: List[str] = field(default_factory=list)
    notification_enabled: bool = False
    # 映射到本地 Severity 等级（CRITICAL/MAJOR/MINOR/WARNING/INFO）
    notification_threshold: ErrorSeverity = ErrorSeverity.MAJOR
    batch_processing: bool = True
    batch_size: int = 10
    batch_interval: int = 60  # seconds


class EnhancedErrorHandler:
    """
    增强错误处理器
    
    功能：
    1. 统一的错误处理流程
    2. 错误报告和追踪
    3. 自动化错误处理
    4. 错误趋势分析
    """
    
    def __init__(self, config: Optional[ErrorHandlingConfig] = None):
        self.logger = logging.getLogger(__name__)
        self.config = config or ErrorHandlingConfig()
        
        # 初始化组件
        self.error_classifier = ErrorClassifier()
        self.diagnostic_engine = DiagnosticEngine(self.error_classifier)
        self.recommendation_engine = RecommendationEngine()
        
        # 错误报告存储
        self.error_reports: Dict[str, ErrorReport] = {}
        self.error_queue = queue.Queue()
        
        # 回调函数
        self.error_callbacks: List[Callable[[ErrorReport], None]] = []
        self.resolution_callbacks: List[Callable[[ErrorReport], None]] = []
        
        # 统计信息
        self.error_stats = defaultdict(int)
        self.last_stats_reset = datetime.now()
        
        # 批处理线程
        self.batch_thread = None
        self.batch_stop_event = threading.Event()
        
        # 确保报告目录存在
        os.makedirs(self.config.report_directory, exist_ok=True)
        
        # 加载已有报告
        self._load_existing_reports()
        
        # 启动批处理
        if self.config.batch_processing:
            self._start_batch_processing()
    
    def handle_error(self, 
                    error: Exception, 
                    context: Optional[Dict[str, Any]] = None,
                    auto_process: bool = True) -> ErrorReport:
        """
        处理错误
        
        Args:
            error: 要处理的错误
            context: 错误上下文信息
            auto_process: 是否自动处理（分类、诊断、建议）
            
        Returns:
            ErrorReport: 错误报告
        """
        try:
            # 将原始异常包装为 SystemError 再进行分类
            from src.error_handling.models import SystemError, ErrorType, Severity
            sys_error = SystemError(
                error_type=ErrorType.UNKNOWN,
                severity=Severity.MINOR,
                message=str(error),
                component=(context or {}).get('component', 'general'),
                context=context or {}
            )
            classified_error_sys = self.error_classifier.classify_error(sys_error)

            # 统一为 ClassifiedError
            classified_wrapped = ClassifiedError(
                original_error=error,
                category=getattr(classified_error_sys, 'error_type', ErrorCategory.UNKNOWN),
                severity=getattr(classified_error_sys, 'severity', ErrorSeverity.MINOR),
                component=getattr(classified_error_sys, 'component', (context or {}).get('component', 'general')),
                context=getattr(classified_error_sys, 'context', (context or {}))
            )
            
            # 创建错误报告
            report_id = self._generate_report_id(classified_wrapped)
            error_report = ErrorReport(
                report_id=report_id,
                classified_error=classified_wrapped
            )
            
            # 更新统计
            # 使用系统错误对象统计（兼容字段）
            self._update_error_stats(classified_error_sys)
            
            if auto_process:
                if self.config.batch_processing:
                    # 添加到批处理队列
                    self.error_queue.put(error_report)
                else:
                    # 立即处理
                    self._process_error_report(error_report)
            
            # 保存报告
            self.error_reports[report_id] = error_report
            
            # 触发回调
            self._trigger_error_callbacks(error_report)
            
            # 记录日志
            try:
                self._log_error_report(error_report)
            except Exception:
                pass
            
            return error_report
            
        except Exception as e:
            self.logger.error(f"处理错误时出现异常: {e}")
            # 创建一个基础的错误报告
            return ErrorReport(
                report_id=f"ERROR_HANDLER_FAIL_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                classified_error=ClassifiedError(
                    original_error=error,
                    category=ErrorCategory.UNKNOWN,
                    severity=ErrorSeverity.MINOR,
                    component=(context or {}).get('component', 'error_handler'),
                    context=context or {}
                )
            )
    
    def _process_error_report(self, error_report: ErrorReport):
        """处理错误报告"""
        try:
            # 诊断分析
            if self.config.auto_diagnose:
                diagnostic_info = self._run_targeted_diagnostic(error_report.classified_error)
                error_report.diagnostic_info = diagnostic_info
            
            # 生成建议
            if self.config.auto_recommend:
                recommendations = self.recommendation_engine.generate_recommendations(
                    issues=[],  # 可以基于错误创建Issue
                    errors=[error_report.classified_error]
                )
                error_report.recommendations = recommendations
            
            # 检查自动解决
            if self._should_auto_resolve(error_report):
                self._attempt_auto_resolution(error_report)
            
            # 保存报告
            if self.config.save_reports:
                self._save_error_report(error_report)
            
            # 发送通知
            if self._should_notify(error_report):
                self._send_notification(error_report)
            
            error_report.updated_at = datetime.now()
            
        except Exception as e:
            self.logger.error(f"处理错误报告时出现异常: {e}")
    
    def _run_targeted_diagnostic(self, classified_error: ClassifiedError) -> Dict[str, Any]:
        """运行针对性诊断"""
        diagnostic_info = {
            'timestamp': datetime.now().isoformat(),
            'error_context': classified_error.context,
            'component_health': {},
            'related_issues': []
        }
        
        try:
            # 基于错误类别运行特定诊断
            if classified_error.category == ErrorCategory.DATABASE:
                diagnostic_info['component_health']['database'] = self._check_database_health()
            elif classified_error.category == ErrorCategory.NETWORK:
                diagnostic_info['component_health']['network'] = self._check_network_health()
            elif classified_error.category == ErrorCategory.CONFIGURATION:
                diagnostic_info['component_health']['configuration'] = self._check_config_health()
            
            # 查找相关问题
            related_errors = self._find_related_errors(classified_error)
            diagnostic_info['related_issues'] = [
                {
                    'error_id': err.error_signature,
                    'timestamp': err.timestamp.isoformat(),
                    'similarity': 'high'  # 简化的相似度
                }
                for err in related_errors[:5]  # 最多5个相关错误
            ]
            
        except Exception as e:
            self.logger.warning(f"针对性诊断失败: {e}")
            diagnostic_info['diagnostic_error'] = str(e)
        
        return diagnostic_info
    
    def _check_database_health(self) -> Dict[str, Any]:
        """检查数据库健康状态"""
        try:
            import sqlite3
            db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data/db/trading_system.db')
            
            if not os.path.exists(db_path):
                return {'status': 'error', 'message': '数据库文件不存在'}
            
            conn = sqlite3.connect(db_path, timeout=5)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            conn.close()
            
            return {'status': 'healthy', 'message': '数据库连接正常'}
            
        except Exception as e:
            return {'status': 'error', 'message': f'数据库检查失败: {str(e)}'}
    
    def _check_network_health(self) -> Dict[str, Any]:
        """检查网络健康状态"""
        try:
            import requests
            response = requests.get('https://8.8.8.8', timeout=5)
            return {'status': 'healthy', 'message': '网络连接正常'}
        except Exception as e:
            return {'status': 'error', 'message': f'网络检查失败: {str(e)}'}
    
    def _check_config_health(self) -> Dict[str, Any]:
        """检查配置健康状态"""
        try:
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config/config.yaml')
            
            if not os.path.exists(config_path):
                return {'status': 'error', 'message': '主配置文件不存在'}
            
            import yaml
            with open(config_path, 'r', encoding='utf-8') as f:
                yaml.safe_load(f)
            
            return {'status': 'healthy', 'message': '配置文件格式正确'}
            
        except Exception as e:
            return {'status': 'error', 'message': f'配置检查失败: {str(e)}'}
    
    def _find_related_errors(self, classified_error: ClassifiedError) -> List[ClassifiedError]:
        """查找相关错误"""
        related_errors = []
        
        for report in self.error_reports.values():
            other_error = report.classified_error
            
            # 检查相似性
            if (other_error.category == classified_error.category and
                other_error.component == classified_error.component and
                other_error.error_signature != classified_error.error_signature):
                related_errors.append(other_error)
        
        # 按时间排序，最近的在前
        related_errors.sort(key=lambda x: x.timestamp, reverse=True)
        
        return related_errors
    
    def _should_auto_resolve(self, error_report: ErrorReport) -> bool:
        """判断是否应该自动解决"""
        pattern_id = error_report.classified_error.pattern_id
        
        if not pattern_id:
            return False
        
        # 检查是否在自动解决列表中
        if pattern_id in self.config.auto_resolve_patterns:
            return True
        
        # 检查错误严重性（低严重性错误可以尝试自动解决）
        if error_report.classified_error.severity in [ErrorSeverity.LOW, ErrorSeverity.INFO]:
            return True
        
        return False
    
    def _attempt_auto_resolution(self, error_report: ErrorReport):
        """尝试自动解决错误"""
        try:
            error = error_report.classified_error
            
            # 基于错误类型尝试自动解决
            if error.category == ErrorCategory.DEPENDENCY:
                self._auto_resolve_dependency_error(error_report)
            elif error.category == ErrorCategory.CONFIGURATION:
                self._auto_resolve_config_error(error_report)
            elif error.category == ErrorCategory.FILE_SYSTEM:
                self._auto_resolve_filesystem_error(error_report)
            
        except Exception as e:
            self.logger.warning(f"自动解决错误失败: {e}")
            error_report.resolution_notes = f"自动解决失败: {str(e)}"
    
    def _auto_resolve_dependency_error(self, error_report: ErrorReport):
        """自动解决依赖错误"""
        error_msg = str(error_report.classified_error.original_error)
        
        # 提取模块名
        import re
        module_match = re.search(r"No module named '([^']+)'", error_msg)
        if module_match:
            module_name = module_match.group(1)
            
            try:
                # 尝试安装模块（仅在开发环境）
                import subprocess
                result = subprocess.run(['pip', 'install', module_name], 
                                      capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    error_report.resolution_status = "resolved"
                    error_report.resolution_notes = f"自动安装了缺失的模块: {module_name}"
                    self.logger.info(f"自动安装模块成功: {module_name}")
                else:
                    error_report.resolution_notes = f"自动安装模块失败: {result.stderr}"
                    
            except Exception as e:
                error_report.resolution_notes = f"自动安装模块异常: {str(e)}"
    
    def _auto_resolve_config_error(self, error_report: ErrorReport):
        """自动解决配置错误"""
        # 这里可以实现配置文件的自动修复逻辑
        error_report.resolution_notes = "配置错误需要手动修复"
    
    def _auto_resolve_filesystem_error(self, error_report: ErrorReport):
        """自动解决文件系统错误"""
        error_msg = str(error_report.classified_error.original_error)
        
        # 提取文件路径
        import re
        file_match = re.search(r"No such file or directory: '([^']+)'", error_msg)
        if file_match:
            file_path = file_match.group(1)
            
            try:
                # 尝试创建目录
                dir_path = os.path.dirname(file_path)
                if dir_path and not os.path.exists(dir_path):
                    os.makedirs(dir_path, exist_ok=True)
                    error_report.resolution_status = "resolved"
                    error_report.resolution_notes = f"自动创建了缺失的目录: {dir_path}"
                    self.logger.info(f"自动创建目录成功: {dir_path}")
                    
            except Exception as e:
                error_report.resolution_notes = f"自动创建目录失败: {str(e)}"
    
    def _should_notify(self, error_report: ErrorReport) -> bool:
        """判断是否应该发送通知"""
        if not self.config.notification_enabled:
            return False
        
        # 检查严重性阈值
        severity_levels = {
            ErrorSeverity.CRITICAL: 0,
            ErrorSeverity.HIGH: 1,
            ErrorSeverity.MEDIUM: 2,
            ErrorSeverity.LOW: 3,
            ErrorSeverity.INFO: 4
        }
        
        error_level = severity_levels.get(error_report.classified_error.severity, 5)
        threshold_level = severity_levels.get(self.config.notification_threshold, 5)
        
        return error_level <= threshold_level
    
    def _send_notification(self, error_report: ErrorReport):
        """发送通知"""
        try:
            # 这里可以实现邮件、Slack、钉钉等通知
            notification_msg = f"""
错误通知:
- 错误类型: {type(error_report.classified_error.original_error).__name__}
- 严重性: {error_report.classified_error.severity.value}
- 组件: {error_report.classified_error.component}
- 时间: {error_report.created_at.strftime('%Y-%m-%d %H:%M:%S')}
- 消息: {str(error_report.classified_error.original_error)}
"""
            
            self.logger.warning(f"错误通知: {notification_msg}")
            
        except Exception as e:
            self.logger.error(f"发送通知失败: {e}")
    
    def _start_batch_processing(self):
        """启动批处理线程"""
        if self.batch_thread and self.batch_thread.is_alive():
            return
        
        self.batch_thread = threading.Thread(target=self._batch_processor, daemon=True)
        self.batch_thread.start()
        self.logger.info("批处理线程已启动")
    
    def _batch_processor(self):
        """批处理器"""
        while not self.batch_stop_event.is_set():
            try:
                batch = []
                
                # 收集批处理项目
                while len(batch) < self.config.batch_size:
                    try:
                        error_report = self.error_queue.get(timeout=self.config.batch_interval)
                        batch.append(error_report)
                    except queue.Empty:
                        break
                
                # 处理批次
                if batch:
                    self.logger.debug(f"处理批次: {len(batch)} 个错误报告")
                    for error_report in batch:
                        self._process_error_report(error_report)
                
            except Exception as e:
                self.logger.error(f"批处理异常: {e}")
    
    def stop_batch_processing(self):
        """停止批处理"""
        if self.batch_thread and self.batch_thread.is_alive():
            self.batch_stop_event.set()
            self.batch_thread.join(timeout=5)
            self.logger.info("批处理线程已停止")
    
    def generate_diagnostic_report(self) -> DiagnosticReport:
        """生成诊断报告"""
        return self.diagnostic_engine.run_full_diagnostic()
    
    def get_error_statistics(self, time_window: Optional[timedelta] = None) -> Dict[str, Any]:
        """获取错误统计"""
        if time_window:
            cutoff_time = datetime.now() - time_window
            reports = [r for r in self.error_reports.values() if r.created_at >= cutoff_time]
        else:
            reports = list(self.error_reports.values())
        
        if not reports:
            return {'total_errors': 0, 'by_category': {}, 'by_severity': {}, 'by_status': {}}
        
        from collections import Counter
        
        categories = Counter(r.classified_error.category.value for r in reports)
        severities = Counter(r.classified_error.severity.value for r in reports)
        statuses = Counter(r.resolution_status for r in reports)
        
        return {
            'total_errors': len(reports),
            'by_category': dict(categories),
            'by_severity': dict(severities),
            'by_status': dict(statuses),
            'time_window': str(time_window) if time_window else 'all_time'
        }
    
    def get_error_trends(self, days: int = 7) -> Dict[str, Any]:
        """获取错误趋势"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        daily_counts = defaultdict(int)
        daily_severities = defaultdict(lambda: defaultdict(int))
        
        for report in self.error_reports.values():
            if start_date <= report.created_at <= end_date:
                date_key = report.created_at.strftime('%Y-%m-%d')
                daily_counts[date_key] += 1
                daily_severities[date_key][report.classified_error.severity.value] += 1
        
        return {
            'period': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
            'daily_counts': dict(daily_counts),
            'daily_severities': dict(daily_severities)
        }
    
    def add_error_callback(self, callback: Callable[[ErrorReport], None]):
        """添加错误回调"""
        self.error_callbacks.append(callback)
    
    def add_resolution_callback(self, callback: Callable[[ErrorReport], None]):
        """添加解决回调"""
        self.resolution_callbacks.append(callback)
    
    def _trigger_error_callbacks(self, error_report: ErrorReport):
        """触发错误回调"""
        for callback in self.error_callbacks:
            try:
                callback(error_report)
            except Exception as e:
                self.logger.error(f"错误回调执行失败: {e}")
    
    def _trigger_resolution_callbacks(self, error_report: ErrorReport):
        """触发解决回调"""
        for callback in self.resolution_callbacks:
            try:
                callback(error_report)
            except Exception as e:
                self.logger.error(f"解决回调执行失败: {e}")
    
    def _generate_report_id(self, classified_error: ClassifiedError) -> str:
        """生成报告ID"""
        timestamp = classified_error.timestamp.strftime('%Y%m%d_%H%M%S')
        component = classified_error.component[:10]  # 限制长度
        return f"ERR_{component.upper()}_{timestamp}_{id(classified_error.original_error) % 10000:04d}"
    
    def _update_error_stats(self, classified_error: ClassifiedError):
        """更新错误统计"""
        self.error_stats['total'] += 1
        self.error_stats[f'category_{classified_error.category.value}'] += 1
        self.error_stats[f'severity_{classified_error.severity.value}'] += 1
        self.error_stats[f'component_{classified_error.component}'] += 1
    
    def _log_error_report(self, error_report: ErrorReport):
        """记录错误报告日志"""
        error = error_report.classified_error
        log_msg = (f"错误报告 {error_report.report_id}: "
                  f"{error.category.value}/{error.severity.value} "
                  f"在 {error.component} - {str(error.original_error)}")
        
        if error.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_msg)
        elif error.severity == ErrorSeverity.HIGH:
            self.logger.error(log_msg)
        elif error.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_msg)
        else:
            self.logger.info(log_msg)
    
    def _save_error_report(self, error_report: ErrorReport):
        """保存错误报告到文件"""
        try:
            report_file = os.path.join(
                self.config.report_directory,
                f"{error_report.report_id}.json"
            )
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(error_report.to_dict(), f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存错误报告失败: {e}")
    
    def _load_existing_reports(self):
        """加载已有的错误报告"""
        try:
            if not os.path.exists(self.config.report_directory):
                return
            
            report_files = [f for f in os.listdir(self.config.report_directory) 
                           if f.endswith('.json')]
            
            for report_file in report_files[:self.config.max_reports]:
                try:
                    file_path = os.path.join(self.config.report_directory, report_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        report_data = json.load(f)
                    
                    # 这里可以重建ErrorReport对象，现在简化处理
                    report_id = report_data.get('report_id')
                    if report_id:
                        # 简化的报告对象，只保存基本信息
                        self.error_stats['loaded'] += 1
                        
                except Exception as e:
                    self.logger.warning(f"加载错误报告失败 {report_file}: {e}")
            
            self.logger.info(f"加载了 {self.error_stats['loaded']} 个历史错误报告")
            
        except Exception as e:
            self.logger.error(f"加载历史错误报告失败: {e}")
    
    def cleanup_old_reports(self, days: int = 30):
        """清理旧的错误报告"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            cleaned_count = 0
            
            # 清理内存中的报告
            reports_to_remove = []
            for report_id, report in self.error_reports.items():
                if report.created_at < cutoff_date:
                    reports_to_remove.append(report_id)
            
            for report_id in reports_to_remove:
                del self.error_reports[report_id]
                cleaned_count += 1
            
            # 清理文件系统中的报告
            if os.path.exists(self.config.report_directory):
                for filename in os.listdir(self.config.report_directory):
                    if filename.endswith('.json'):
                        file_path = os.path.join(self.config.report_directory, filename)
                        file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                        
                        if file_mtime < cutoff_date:
                            os.remove(file_path)
                            cleaned_count += 1
            
            self.logger.info(f"清理了 {cleaned_count} 个旧的错误报告")
            
        except Exception as e:
            self.logger.error(f"清理旧报告失败: {e}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.stop_batch_processing()
        except:
            pass