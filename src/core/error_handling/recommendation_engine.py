"""
修复建议引擎

提供自动修复建议生成、配置问题检测和最佳实践建议功能。
"""

import os
import re
import json
import logging
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import subprocess

from src.error_handling.error_classifier import ClassifiedError
from src.error_handling.models import ErrorType as ErrorCategory, Severity as ErrorSeverity
from .diagnostic_engine import Issue, IssueCategory, IssueSeverity


class RecommendationType(Enum):
    """建议类型"""
    IMMEDIATE_FIX = "immediate_fix"      # 立即修复
    CONFIGURATION = "configuration"      # 配置修复
    INSTALLATION = "installation"       # 安装修复
    OPTIMIZATION = "optimization"       # 优化建议
    BEST_PRACTICE = "best_practice"     # 最佳实践
    MONITORING = "monitoring"           # 监控建议
    SECURITY = "security"               # 安全建议


class RecommendationPriority(Enum):
    """建议优先级"""
    CRITICAL = "critical"    # 必须立即执行
    HIGH = "high"           # 高优先级
    MEDIUM = "medium"       # 中等优先级
    LOW = "low"            # 低优先级
    OPTIONAL = "optional"   # 可选


@dataclass
class Recommendation:
    """修复建议"""
    recommendation_id: str
    title: str
    description: str
    recommendation_type: RecommendationType
    priority: RecommendationPriority
    category: str
    steps: List[str] = field(default_factory=list)
    commands: List[str] = field(default_factory=list)
    files_to_modify: List[str] = field(default_factory=list)
    estimated_time: Optional[str] = None
    difficulty: str = "medium"  # easy, medium, hard
    prerequisites: List[str] = field(default_factory=list)
    risks: List[str] = field(default_factory=list)
    benefits: List[str] = field(default_factory=list)
    related_issues: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'recommendation_id': self.recommendation_id,
            'title': self.title,
            'description': self.description,
            'type': self.recommendation_type.value,
            'priority': self.priority.value,
            'category': self.category,
            'steps': self.steps,
            'commands': self.commands,
            'files_to_modify': self.files_to_modify,
            'estimated_time': self.estimated_time,
            'difficulty': self.difficulty,
            'prerequisites': self.prerequisites,
            'risks': self.risks,
            'benefits': self.benefits,
            'related_issues': self.related_issues,
            'created_at': self.created_at.isoformat()
        }


@dataclass
class ConfigurationIssue:
    """配置问题"""
    file_path: str
    issue_type: str
    description: str
    current_value: Optional[str] = None
    recommended_value: Optional[str] = None
    line_number: Optional[int] = None
    severity: str = "medium"


class RecommendationEngine:
    """
    修复建议引擎
    
    功能：
    1. 自动修复建议生成
    2. 配置问题检测和修复指导
    3. 最佳实践建议系统
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        
        # 建议模板
        self.recommendation_templates = self._load_recommendation_templates()
        
        # 配置检查规则
        self.config_rules = self._load_config_rules()
        
        # 最佳实践规则
        self.best_practices = self._load_best_practices()
        
        # 修复命令映射
        self.fix_commands = {
            'install_dependency': 'pip install {dependency}',
            'create_directory': 'mkdir -p {directory}',
            'fix_permissions': 'chmod {permissions} {file_path}',
            'restart_service': 'systemctl restart {service}',
            'clear_cache': 'rm -rf {cache_path}',
            'update_config': 'sed -i "s/{old_value}/{new_value}/g" {file_path}'
        }
    
    def generate_recommendations(self, 
                               issues: List[Issue], 
                               errors: Optional[List[ClassifiedError]] = None) -> List[Recommendation]:
        """
        生成修复建议
        
        Args:
            issues: 诊断发现的问题列表
            errors: 分类后的错误列表
            
        Returns:
            List[Recommendation]: 修复建议列表
        """
        recommendations = []
        
        try:
            # 基于问题生成建议
            for issue in issues:
                issue_recommendations = self._generate_issue_recommendations(issue)
                recommendations.extend(issue_recommendations)
            
            # 基于错误生成建议
            if errors:
                for error in errors:
                    error_recommendations = self._generate_error_recommendations(error)
                    recommendations.extend(error_recommendations)
            
            # 生成配置优化建议
            config_recommendations = self._generate_config_recommendations()
            recommendations.extend(config_recommendations)
            
            # 生成最佳实践建议
            best_practice_recommendations = self._generate_best_practice_recommendations()
            recommendations.extend(best_practice_recommendations)
            
            # 去重和排序
            recommendations = self._deduplicate_recommendations(recommendations)
            recommendations = self._prioritize_recommendations(recommendations)
            
            self.logger.info(f"生成了 {len(recommendations)} 个修复建议")
            
        except Exception as e:
            self.logger.error(f"生成建议时出现错误: {e}")
            # 添加一个通用的错误处理建议
            recommendations.append(Recommendation(
                recommendation_id="REC_ENGINE_ERROR",
                title="建议引擎错误",
                description=f"建议生成过程中出现错误: {str(e)}",
                recommendation_type=RecommendationType.IMMEDIATE_FIX,
                priority=RecommendationPriority.HIGH,
                category="system"
            ))
        
        return recommendations
    
    def _generate_issue_recommendations(self, issue: Issue) -> List[Recommendation]:
        """基于问题生成建议"""
        recommendations = []
        
        # 系统资源问题
        if issue.category == IssueCategory.SYSTEM_RESOURCE:
            if "CPU" in issue.title:
                recommendations.append(self._create_cpu_optimization_recommendation(issue))
            elif "内存" in issue.title:
                recommendations.append(self._create_memory_optimization_recommendation(issue))
            elif "磁盘" in issue.title:
                recommendations.append(self._create_disk_cleanup_recommendation(issue))
        
        # 配置问题
        elif issue.category == IssueCategory.CONFIGURATION:
            if "缺失" in issue.title:
                recommendations.append(self._create_missing_config_recommendation(issue))
            elif "语法错误" in issue.title:
                recommendations.append(self._create_config_syntax_recommendation(issue))
        
        # 依赖问题
        elif issue.category == IssueCategory.DEPENDENCY:
            if "缺少依赖" in issue.title:
                recommendations.append(self._create_dependency_installation_recommendation(issue))
            elif "Python版本" in issue.title:
                recommendations.append(self._create_python_upgrade_recommendation(issue))
        
        # 数据库问题
        elif issue.category == IssueCategory.DATABASE:
            if "缺失" in issue.title:
                recommendations.append(self._create_database_init_recommendation(issue))
            elif "连接失败" in issue.title:
                recommendations.append(self._create_database_connection_recommendation(issue))
            elif "完整性" in issue.title:
                recommendations.append(self._create_database_repair_recommendation(issue))
        
        # 网络问题
        elif issue.category == IssueCategory.NETWORK:
            recommendations.append(self._create_network_troubleshooting_recommendation(issue))
        
        # 文件系统问题
        elif issue.category == IssueCategory.FILE_SYSTEM:
            if "缺失" in issue.title:
                recommendations.append(self._create_file_creation_recommendation(issue))
            elif "权限" in issue.title:
                recommendations.append(self._create_permission_fix_recommendation(issue))
            elif "日志目录过大" in issue.title:
                recommendations.append(self._create_log_cleanup_recommendation(issue))
        
        # 安全问题
        elif issue.category == IssueCategory.SECURITY:
            if "权限过宽" in issue.title:
                recommendations.append(self._create_security_permission_recommendation(issue))
            elif "弱凭据" in issue.title:
                recommendations.append(self._create_credential_security_recommendation(issue))
        
        return recommendations
    
    def _generate_error_recommendations(self, error: ClassifiedError) -> List[Recommendation]:
        """基于错误生成建议"""
        recommendations = []
        
        error_type = type(error.original_error).__name__
        error_message = str(error.original_error)
        
        # 导入错误
        if "ImportError" in error_type or "ModuleNotFoundError" in error_type:
            module_match = re.search(r"No module named '([^']+)'", error_message)
            if module_match:
                module_name = module_match.group(1)
                recommendations.append(Recommendation(
                    recommendation_id=f"FIX_IMPORT_{module_name.upper()}",
                    title=f"安装缺失模块: {module_name}",
                    description=f"系统缺少Python模块 {module_name}",
                    recommendation_type=RecommendationType.INSTALLATION,
                    priority=RecommendationPriority.HIGH,
                    category="dependencies",
                    steps=[
                        f"安装缺失的Python模块 {module_name}",
                        "验证安装是否成功",
                        "重新启动应用程序"
                    ],
                    commands=[f"pip install {module_name}"],
                    estimated_time="2-5分钟",
                    difficulty="easy"
                ))
        
        # 文件不存在错误
        elif "FileNotFoundError" in error_type:
            file_match = re.search(r"No such file or directory: '([^']+)'", error_message)
            if file_match:
                file_path = file_match.group(1)
                recommendations.append(self._create_missing_file_recommendation(file_path, error))
        
        # 权限错误
        elif "PermissionError" in error_type:
            recommendations.append(self._create_permission_error_recommendation(error))
        
        # 连接错误
        elif "ConnectionError" in error_type or "TimeoutError" in error_type:
            recommendations.append(self._create_connection_error_recommendation(error))
        
        return recommendations
    
    def _generate_config_recommendations(self) -> List[Recommendation]:
        """生成配置优化建议"""
        recommendations = []
        
        # 检查配置文件
        config_issues = self._detect_configuration_issues()
        
        for config_issue in config_issues:
            recommendation = Recommendation(
                recommendation_id=f"CFG_OPT_{config_issue.issue_type.upper()}",
                title=f"优化配置: {os.path.basename(config_issue.file_path)}",
                description=config_issue.description,
                recommendation_type=RecommendationType.CONFIGURATION,
                priority=RecommendationPriority.MEDIUM,
                category="configuration",
                files_to_modify=[config_issue.file_path]
            )
            
            if config_issue.recommended_value:
                recommendation.steps = [
                    f"打开配置文件 {config_issue.file_path}",
                    f"找到第 {config_issue.line_number} 行" if config_issue.line_number else "找到相关配置项",
                    f"将 '{config_issue.current_value}' 修改为 '{config_issue.recommended_value}'",
                    "保存文件并重启服务"
                ]
            
            recommendations.append(recommendation)
        
        return recommendations
    
    def _generate_best_practice_recommendations(self) -> List[Recommendation]:
        """生成最佳实践建议"""
        recommendations = []
        
        # 日志管理最佳实践
        recommendations.append(Recommendation(
            recommendation_id="BP_LOGGING",
            title="优化日志管理",
            description="实施日志轮转和清理策略，避免日志文件过大",
            recommendation_type=RecommendationType.BEST_PRACTICE,
            priority=RecommendationPriority.MEDIUM,
            category="logging",
            steps=[
                "配置日志轮转策略",
                "设置日志保留期限",
                "实施日志级别管理",
                "定期清理旧日志文件"
            ],
            benefits=[
                "减少磁盘空间占用",
                "提高系统性能",
                "便于问题排查"
            ],
            estimated_time="30分钟"
        ))
        
        # 监控最佳实践
        recommendations.append(Recommendation(
            recommendation_id="BP_MONITORING",
            title="完善系统监控",
            description="建立全面的系统监控和告警机制",
            recommendation_type=RecommendationType.MONITORING,
            priority=RecommendationPriority.MEDIUM,
            category="monitoring",
            steps=[
                "配置系统资源监控",
                "设置关键指标告警",
                "建立健康检查机制",
                "定期生成监控报告"
            ],
            benefits=[
                "及时发现问题",
                "预防系统故障",
                "提高系统可靠性"
            ],
            estimated_time="1-2小时"
        ))
        
        # 安全最佳实践
        recommendations.append(Recommendation(
            recommendation_id="BP_SECURITY",
            title="加强系统安全",
            description="实施安全最佳实践，保护系统和数据安全",
            recommendation_type=RecommendationType.SECURITY,
            priority=RecommendationPriority.HIGH,
            category="security",
            steps=[
                "修改默认密码和密钥",
                "设置适当的文件权限",
                "启用访问日志记录",
                "定期更新依赖包"
            ],
            benefits=[
                "提高系统安全性",
                "防止数据泄露",
                "符合安全规范"
            ],
            estimated_time="45分钟"
        ))
        
        # 性能优化最佳实践
        recommendations.append(Recommendation(
            recommendation_id="BP_PERFORMANCE",
            title="性能优化建议",
            description="优化系统性能，提高响应速度和资源利用率",
            recommendation_type=RecommendationType.OPTIMIZATION,
            priority=RecommendationPriority.LOW,
            category="performance",
            steps=[
                "优化数据库查询",
                "实施缓存策略",
                "调整系统参数",
                "监控性能指标"
            ],
            benefits=[
                "提高系统响应速度",
                "减少资源消耗",
                "改善用户体验"
            ],
            estimated_time="2-3小时"
        ))
        
        return recommendations
    
    def _detect_configuration_issues(self) -> List[ConfigurationIssue]:
        """检测配置问题"""
        issues = []
        
        # 检查主配置文件
        config_yaml_path = os.path.join(self.project_root, 'config/config.yaml')
        if os.path.exists(config_yaml_path):
            yaml_issues = self._check_yaml_config(config_yaml_path)
            issues.extend(yaml_issues)
        
        # 检查环境配置文件
        env_files = [
            'config/project_specific/.env',
            'web_ui/.env'
        ]
        
        for env_file in env_files:
            env_path = os.path.join(self.project_root, env_file)
            if os.path.exists(env_path):
                env_issues = self._check_env_config(env_path)
                issues.extend(env_issues)
        
        return issues
    
    def _check_yaml_config(self, file_path: str) -> List[ConfigurationIssue]:
        """检查YAML配置文件"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines, 1):
                line = line.strip()
                
                # 检查日志级别配置
                if 'log_level:' in line.lower():
                    if 'debug' in line.lower():
                        issues.append(ConfigurationIssue(
                            file_path=file_path,
                            issue_type="log_level_debug",
                            description="生产环境建议使用INFO或WARNING日志级别",
                            current_value="DEBUG",
                            recommended_value="INFO",
                            line_number=i,
                            severity="low"
                        ))
                
                # 检查数据库连接池配置
                if 'pool_size:' in line.lower():
                    pool_size_match = re.search(r'pool_size:\s*(\d+)', line)
                    if pool_size_match:
                        pool_size = int(pool_size_match.group(1))
                        if pool_size < 5:
                            issues.append(ConfigurationIssue(
                                file_path=file_path,
                                issue_type="small_pool_size",
                                description="数据库连接池大小过小，可能影响性能",
                                current_value=str(pool_size),
                                recommended_value="10",
                                line_number=i,
                                severity="medium"
                            ))
                
        except Exception as e:
            self.logger.warning(f"检查YAML配置文件失败 {file_path}: {e}")
        
        return issues
    
    def _check_env_config(self, file_path: str) -> List[ConfigurationIssue]:
        """检查环境配置文件"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines, 1):
                line = line.strip()
                
                # 检查默认或弱密码
                if '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    key = key.strip().lower()
                    value = value.strip()
                    
                    if 'password' in key or 'secret' in key or 'key' in key:
                        weak_values = ['123456', 'password', 'secret', 'your_key', 'your_secret']
                        if value.lower() in weak_values:
                            issues.append(ConfigurationIssue(
                                file_path=file_path,
                                issue_type="weak_credential",
                                description=f"发现弱凭据配置: {key}",
                                current_value=value,
                                recommended_value="<strong_random_value>",
                                line_number=i,
                                severity="high"
                            ))
                
        except Exception as e:
            self.logger.warning(f"检查环境配置文件失败 {file_path}: {e}")
        
        return issues
    
    def _create_cpu_optimization_recommendation(self, issue: Issue) -> Recommendation:
        """创建CPU优化建议"""
        return Recommendation(
            recommendation_id=f"OPT_CPU_{issue.issue_id}",
            title="优化CPU使用率",
            description="系统CPU使用率过高，需要优化以提高性能",
            recommendation_type=RecommendationType.OPTIMIZATION,
            priority=RecommendationPriority.HIGH if issue.severity == IssueSeverity.CRITICAL else RecommendationPriority.MEDIUM,
            category="performance",
            steps=[
                "识别高CPU使用率的进程",
                "优化或重启占用资源过多的服务",
                "检查是否有死循环或无限递归",
                "考虑增加系统资源或优化算法"
            ],
            commands=[
                "top -o %CPU",
                "ps aux --sort=-%cpu | head -10",
                "htop"
            ],
            estimated_time="15-30分钟",
            difficulty="medium",
            benefits=["提高系统响应速度", "减少系统负载", "改善用户体验"],
            related_issues=[issue.issue_id]
        )
    
    def _create_memory_optimization_recommendation(self, issue: Issue) -> Recommendation:
        """创建内存优化建议"""
        return Recommendation(
            recommendation_id=f"OPT_MEM_{issue.issue_id}",
            title="优化内存使用",
            description="系统内存使用率过高，需要释放内存以避免系统不稳定",
            recommendation_type=RecommendationType.OPTIMIZATION,
            priority=RecommendationPriority.HIGH if issue.severity == IssueSeverity.CRITICAL else RecommendationPriority.MEDIUM,
            category="performance",
            steps=[
                "识别内存使用量大的进程",
                "清理系统缓存",
                "重启占用内存过多的服务",
                "检查是否有内存泄漏"
            ],
            commands=[
                "free -h",
                "ps aux --sort=-%mem | head -10",
                "sudo sync && sudo sysctl vm.drop_caches=3"
            ],
            estimated_time="10-20分钟",
            difficulty="easy",
            benefits=["提高系统稳定性", "避免内存不足错误", "改善系统性能"],
            related_issues=[issue.issue_id]
        )
    
    def _create_disk_cleanup_recommendation(self, issue: Issue) -> Recommendation:
        """创建磁盘清理建议"""
        return Recommendation(
            recommendation_id=f"CLEAN_DISK_{issue.issue_id}",
            title="清理磁盘空间",
            description="磁盘空间不足，需要清理以确保系统正常运行",
            recommendation_type=RecommendationType.IMMEDIATE_FIX,
            priority=RecommendationPriority.HIGH,
            category="maintenance",
            steps=[
                "清理日志文件",
                "删除临时文件",
                "清理缓存目录",
                "归档或删除旧数据"
            ],
            commands=[
                "du -sh logs/* | sort -hr",
                "find logs/ -name '*.log' -mtime +30 -delete",
                "rm -rf data/temp/*",
                "docker system prune -f"
            ],
            estimated_time="10-15分钟",
            difficulty="easy",
            benefits=["释放磁盘空间", "提高系统性能", "避免磁盘满错误"],
            risks=["可能删除重要数据，请先备份"],
            related_issues=[issue.issue_id]
        )
    
    def _create_missing_config_recommendation(self, issue: Issue) -> Recommendation:
        """创建缺失配置文件建议"""
        file_path = issue.details.get('file_path', '')
        file_name = os.path.basename(file_path) if file_path else 'config file'
        
        return Recommendation(
            recommendation_id=f"CREATE_CFG_{issue.issue_id}",
            title=f"创建配置文件: {file_name}",
            description=f"系统缺少必需的配置文件 {file_name}",
            recommendation_type=RecommendationType.CONFIGURATION,
            priority=RecommendationPriority.HIGH,
            category="configuration",
            steps=[
                f"创建配置文件 {file_path}",
                "从模板复制基础配置",
                "根据环境调整配置参数",
                "验证配置文件语法"
            ],
            files_to_modify=[file_path] if file_path else [],
            estimated_time="10-20分钟",
            difficulty="medium",
            prerequisites=["了解配置文件格式", "具有文件写入权限"],
            benefits=["修复配置错误", "确保系统正常启动", "提供完整功能"],
            related_issues=[issue.issue_id]
        )
    
    def _create_dependency_installation_recommendation(self, issue: Issue) -> Recommendation:
        """创建依赖安装建议"""
        dependency = issue.details.get('dependency', 'unknown')
        
        return Recommendation(
            recommendation_id=f"INSTALL_DEP_{dependency.upper()}",
            title=f"安装依赖: {dependency}",
            description=f"系统缺少必需的Python包 {dependency}",
            recommendation_type=RecommendationType.INSTALLATION,
            priority=RecommendationPriority.HIGH,
            category="dependencies",
            steps=[
                f"安装Python包 {dependency}",
                "验证安装是否成功",
                "重新启动应用程序",
                "测试相关功能"
            ],
            commands=[
                f"pip install {dependency}",
                f"python -c 'import {dependency}; logger.info(\"{dependency} installed successfully\")'"
            ],
            estimated_time="2-5分钟",
            difficulty="easy",
            prerequisites=["Python环境", "网络连接", "pip工具"],
            benefits=["修复导入错误", "启用相关功能", "确保系统完整性"],
            related_issues=[issue.issue_id]
        )
    
    def _deduplicate_recommendations(self, recommendations: List[Recommendation]) -> List[Recommendation]:
        """去重建议"""
        seen_titles = set()
        unique_recommendations = []
        
        for rec in recommendations:
            if rec.title not in seen_titles:
                seen_titles.add(rec.title)
                unique_recommendations.append(rec)
        
        return unique_recommendations
    
    def _prioritize_recommendations(self, recommendations: List[Recommendation]) -> List[Recommendation]:
        """按优先级排序建议"""
        priority_order = {
            RecommendationPriority.CRITICAL: 0,
            RecommendationPriority.HIGH: 1,
            RecommendationPriority.MEDIUM: 2,
            RecommendationPriority.LOW: 3,
            RecommendationPriority.OPTIONAL: 4
        }
        
        return sorted(recommendations, key=lambda r: priority_order.get(r.priority, 5))
    
    def _load_recommendation_templates(self) -> Dict[str, Any]:
        """加载建议模板"""
        # 这里可以从文件加载模板，现在使用硬编码
        return {
            'system_resource': {
                'cpu_high': "优化CPU使用率",
                'memory_high': "释放内存空间",
                'disk_full': "清理磁盘空间"
            },
            'configuration': {
                'missing_file': "创建缺失的配置文件",
                'syntax_error': "修复配置文件语法错误"
            },
            'dependency': {
                'missing_package': "安装缺失的依赖包",
                'version_conflict': "解决版本冲突"
            }
        }
    
    def _load_config_rules(self) -> Dict[str, Any]:
        """加载配置检查规则"""
        return {
            'yaml_rules': [
                {'pattern': r'log_level:\s*debug', 'severity': 'low', 'message': '生产环境建议使用INFO级别'},
                {'pattern': r'pool_size:\s*[1-4]', 'severity': 'medium', 'message': '连接池大小过小'}
            ],
            'env_rules': [
                {'pattern': r'PASSWORD=123456', 'severity': 'high', 'message': '使用了弱密码'},
                {'pattern': r'SECRET_KEY=secret', 'severity': 'high', 'message': '使用了默认密钥'}
            ]
        }
    
    def _load_best_practices(self) -> Dict[str, Any]:
        """加载最佳实践规则"""
        return {
            'logging': {
                'rotate_logs': '实施日志轮转',
                'log_levels': '使用适当的日志级别',
                'structured_logging': '使用结构化日志'
            },
            'security': {
                'strong_passwords': '使用强密码',
                'file_permissions': '设置适当的文件权限',
                'regular_updates': '定期更新依赖'
            },
            'performance': {
                'caching': '实施缓存策略',
                'database_optimization': '优化数据库查询',
                'resource_monitoring': '监控资源使用'
            }
        }
    
    def _create_missing_file_recommendation(self, file_path: str, error: ClassifiedError) -> Recommendation:
        """创建缺失文件建议"""
        return Recommendation(
            recommendation_id=f"CREATE_FILE_{os.path.basename(file_path).upper()}",
            title=f"创建缺失文件: {os.path.basename(file_path)}",
            description=f"系统尝试访问不存在的文件: {file_path}",
            recommendation_type=RecommendationType.IMMEDIATE_FIX,
            priority=RecommendationPriority.HIGH,
            category="file_system",
            steps=[
                f"检查文件路径是否正确: {file_path}",
                "创建必要的目录结构",
                "创建文件或从备份恢复",
                "验证文件权限"
            ],
            estimated_time="5-10分钟",
            difficulty="easy"
        )
    
    def _create_permission_error_recommendation(self, error: ClassifiedError) -> Recommendation:
        """创建权限错误建议"""
        return Recommendation(
            recommendation_id="FIX_PERMISSIONS",
            title="修复文件权限问题",
            description="系统遇到文件权限错误，需要调整权限设置",
            recommendation_type=RecommendationType.IMMEDIATE_FIX,
            priority=RecommendationPriority.HIGH,
            category="security",
            steps=[
                "识别权限不足的文件或目录",
                "检查当前用户权限",
                "调整文件权限或更改所有者",
                "验证权限修复是否成功"
            ],
            commands=[
                "ls -la {file_path}",
                "chmod 755 {file_path}",
                "chown {user}:{group} {file_path}"
            ],
            estimated_time="5分钟",
            difficulty="easy",
            risks=["不当的权限设置可能带来安全风险"]
        )
    
    def _create_connection_error_recommendation(self, error: ClassifiedError) -> Recommendation:
        """创建连接错误建议"""
        return Recommendation(
            recommendation_id="FIX_CONNECTION",
            title="解决连接问题",
            description="系统遇到网络或服务连接错误",
            recommendation_type=RecommendationType.IMMEDIATE_FIX,
            priority=RecommendationPriority.MEDIUM,
            category="network",
            steps=[
                "检查网络连接状态",
                "验证服务是否正在运行",
                "检查防火墙和端口设置",
                "重试连接或重启服务"
            ],
            commands=[
                "ping {host}",
                "telnet {host} {port}",
                "systemctl status {service}"
            ],
            estimated_time="10-15分钟",
            difficulty="medium"
        )