"""
错误分类器

提供错误类型分类、严重性评估、组件识别和错误模式学习功能。
"""

import re
import json
import logging
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import traceback

from src.common.exceptions import TradingSystemException


class ErrorSeverity(Enum):
    """错误严重性级别"""
    CRITICAL = "critical"    # 系统无法继续运行
    HIGH = "high"           # 核心功能受影响
    MEDIUM = "medium"       # 部分功能受影响
    LOW = "low"            # 轻微影响
    INFO = "info"          # 信息性错误


class ErrorCategory(Enum):
    """错误分类"""
    STARTUP = "startup"           # 启动相关错误
    RUNTIME = "runtime"           # 运行时错误
    SHUTDOWN = "shutdown"         # 关闭相关错误
    CONFIGURATION = "configuration"  # 配置错误
    DEPENDENCY = "dependency"     # 依赖错误
    NETWORK = "network"          # 网络错误
    DATABASE = "database"        # 数据库错误
    DATA_SOURCE = "data_source"  # 数据源错误
    STRATEGY = "strategy"        # 策略错误
    RISK = "risk"               # 风险管理错误
    ORDER = "order"             # 订单错误
    FRONTEND = "frontend"       # 前端错误
    BACKEND = "backend"         # 后端错误
    UNKNOWN = "unknown"         # 未知错误


@dataclass
class ErrorPattern:
    """错误模式"""
    pattern_id: str
    error_signature: str
    category: ErrorCategory
    severity: ErrorSeverity
    component: str
    frequency: int = 1
    first_seen: datetime = None
    last_seen: datetime = None
    description: str = ""
    
    def __post_init__(self):
        if self.first_seen is None:
            self.first_seen = datetime.now()
        if self.last_seen is None:
            self.last_seen = datetime.now()


@dataclass
class ClassifiedError:
    """分类后的错误"""
    original_error: Exception
    category: ErrorCategory
    severity: ErrorSeverity
    component: str
    context: Dict[str, Any]
    pattern_id: Optional[str] = None
    timestamp: datetime = None
    stack_trace: str = ""
    error_signature: str = ""
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if not self.stack_trace:
            self.stack_trace = traceback.format_exc()
        if not self.error_signature:
            self.error_signature = self._generate_signature()
    
    def _generate_signature(self) -> str:
        """生成错误签名用于模式识别"""
        error_type = type(self.original_error).__name__
        error_msg = str(self.original_error)[:100]  # 限制长度
        return f"{error_type}:{error_msg}"


class ErrorClassifier:
    """
    错误分类器
    
    功能：
    1. 错误类型和严重性分类
    2. 组件和上下文识别
    3. 错误模式识别和学习
    """
    
    def __init__(self, pattern_file: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.pattern_file = pattern_file or "data/error_patterns.json"
        
        # 错误模式存储
        self.error_patterns: Dict[str, ErrorPattern] = {}
        self.error_history: List[ClassifiedError] = []
        
        # 组件识别规则
        self.component_patterns = {
            'frontend': [r'web_ui', r'frontend', r'react', r'typescript', r'jsx', r'tsx'],
            'backend': [r'api', r'server', r'flask', r'fastapi', r'uvicorn'],
            'database': [r'sqlite', r'mysql', r'postgres', r'database', r'db'],
            'data_source': [r'adapter', r'yahoo', r'binance', r'akshare', r'fred'],
            'strategy': [r'strategy', r'backtest', r'signal'],
            'risk': [r'risk', r'position', r'portfolio'],
            'monitoring': [r'monitor', r'health', r'check'],
            'core': [r'core', r'engine', r'service_bus'],
            'utils': [r'utils', r'helper', r'tool']
        }
        
        # 严重性评估规则
        self.severity_rules = {
            ErrorSeverity.CRITICAL: [
                r'system.*crash',
                r'cannot.*start',
                r'fatal.*error',
                r'out.*of.*memory',
                r'database.*corrupt',
                r'connection.*refused.*database'
            ],
            ErrorSeverity.HIGH: [
                r'connection.*failed',
                r'authentication.*failed',
                r'permission.*denied',
                r'file.*not.*found.*config',
                r'import.*error.*core'
            ],
            ErrorSeverity.MEDIUM: [
                r'timeout',
                r'retry.*exceeded',
                r'validation.*failed',
                r'data.*missing',
                r'format.*error'
            ],
            ErrorSeverity.LOW: [
                r'warning',
                r'deprecated',
                r'minor.*issue',
                r'cache.*miss'
            ]
        }
        
        # 分类规则
        self.category_rules = {
            ErrorCategory.STARTUP: [
                r'startup', r'initialization', r'bootstrap', r'init'
            ],
            ErrorCategory.SHUTDOWN: [
                r'shutdown', r'cleanup', r'exit', r'close'
            ],
            ErrorCategory.CONFIGURATION: [
                r'config', r'setting', r'parameter', r'yaml', r'json'
            ],
            ErrorCategory.DEPENDENCY: [
                r'import.*error', r'module.*not.*found', r'dependency'
            ],
            ErrorCategory.NETWORK: [
                r'network', r'connection', r'timeout', r'http', r'ssl'
            ],
            ErrorCategory.DATABASE: [
                r'database', r'sqlite', r'mysql', r'postgres', r'sql'
            ],
            ErrorCategory.DATA_SOURCE: [
                r'data.*source', r'adapter', r'yahoo', r'binance'
            ],
            ErrorCategory.STRATEGY: [
                r'strategy', r'backtest', r'signal', r'indicator'
            ],
            ErrorCategory.RISK: [
                r'risk', r'position', r'portfolio', r'limit'
            ],
            ErrorCategory.ORDER: [
                r'order', r'trade', r'execution'
            ],
            ErrorCategory.FRONTEND: [
                r'frontend', r'react', r'typescript', r'web_ui'
            ],
            ErrorCategory.BACKEND: [
                r'backend', r'api', r'server', r'flask'
            ]
        }
        
        # 加载已有的错误模式
        self._load_patterns()
    
    def classify_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> ClassifiedError:
        """
        分类错误
        
        Args:
            error: 要分类的错误
            context: 错误上下文信息
            
        Returns:
            ClassifiedError: 分类后的错误对象
        """
        context = context or {}
        
        # 获取错误信息
        error_msg = str(error).lower()
        error_type = type(error).__name__
        stack_trace = traceback.format_exc()
        
        # 分类错误
        category = self._classify_category(error_msg, error_type, stack_trace, context)
        severity = self._classify_severity(error_msg, error_type, category, context)
        component = self._identify_component(error_msg, stack_trace, context)
        
        # 创建分类结果
        classified_error = ClassifiedError(
            original_error=error,
            category=category,
            severity=severity,
            component=component,
            context=context,
            stack_trace=stack_trace
        )
        
        # 模式识别和学习
        pattern_id = self._identify_pattern(classified_error)
        classified_error.pattern_id = pattern_id
        
        # 记录到历史
        self.error_history.append(classified_error)
        
        # 限制历史记录大小
        if len(self.error_history) > 1000:
            self.error_history = self.error_history[-500:]
        
        self.logger.debug(f"错误分类完成: {category.value}/{severity.value}/{component}")
        
        return classified_error
    
    def _classify_category(self, error_msg: str, error_type: str, stack_trace: str, context: Dict[str, Any]) -> ErrorCategory:
        """分类错误类别"""
        # 首先检查上下文
        if 'component' in context:
            component = context['component'].lower()
            for category, patterns in self.category_rules.items():
                if any(re.search(pattern, component) for pattern in patterns):
                    return category
        
        # 检查错误消息
        full_text = f"{error_msg} {error_type.lower()} {stack_trace.lower()}"
        
        for category, patterns in self.category_rules.items():
            if any(re.search(pattern, full_text) for pattern in patterns):
                return category
        
        # 基于异常类型的分类
        if isinstance(error_msg, str):
            if 'trading' in error_type.lower():
                return ErrorCategory.STRATEGY
            elif 'data' in error_type.lower():
                return ErrorCategory.DATA_SOURCE
            elif 'risk' in error_type.lower():
                return ErrorCategory.RISK
            elif 'config' in error_type.lower():
                return ErrorCategory.CONFIGURATION
        
        return ErrorCategory.UNKNOWN
    
    def _classify_severity(self, error_msg: str, error_type: str, category: ErrorCategory, context: Dict[str, Any]) -> ErrorSeverity:
        """分类错误严重性"""
        full_text = f"{error_msg} {error_type.lower()}"
        
        # 检查严重性规则
        for severity, patterns in self.severity_rules.items():
            if any(re.search(pattern, full_text) for pattern in patterns):
                return severity
        
        # 基于错误类别的默认严重性
        category_severity_map = {
            ErrorCategory.STARTUP: ErrorSeverity.HIGH,
            ErrorCategory.SHUTDOWN: ErrorSeverity.MEDIUM,
            ErrorCategory.CONFIGURATION: ErrorSeverity.HIGH,
            ErrorCategory.DEPENDENCY: ErrorSeverity.HIGH,
            ErrorCategory.NETWORK: ErrorSeverity.MEDIUM,
            ErrorCategory.DATABASE: ErrorSeverity.HIGH,
            ErrorCategory.DATA_SOURCE: ErrorSeverity.MEDIUM,
            ErrorCategory.STRATEGY: ErrorSeverity.MEDIUM,
            ErrorCategory.RISK: ErrorSeverity.HIGH,
            ErrorCategory.ORDER: ErrorSeverity.HIGH,
            ErrorCategory.FRONTEND: ErrorSeverity.LOW,
            ErrorCategory.BACKEND: ErrorSeverity.MEDIUM,
        }
        
        return category_severity_map.get(category, ErrorSeverity.MEDIUM)
    
    def _identify_component(self, error_msg: str, stack_trace: str, context: Dict[str, Any]) -> str:
        """识别错误组件"""
        # 首先检查上下文
        if 'component' in context:
            return context['component']
        
        # 从堆栈跟踪中提取文件路径
        stack_lines = stack_trace.split('\n')
        for line in stack_lines:
            if 'File "' in line:
                # 提取文件路径
                match = re.search(r'File "([^"]+)"', line)
                if match:
                    file_path = match.group(1).lower()
                    
                    # 匹配组件模式
                    for component, patterns in self.component_patterns.items():
                        if any(re.search(pattern, file_path) for pattern in patterns):
                            return component
        
        # 从错误消息中识别
        full_text = f"{error_msg} {stack_trace}".lower()
        for component, patterns in self.component_patterns.items():
            if any(re.search(pattern, full_text) for pattern in patterns):
                return component
        
        return 'unknown'
    
    def _identify_pattern(self, classified_error: ClassifiedError) -> Optional[str]:
        """识别错误模式"""
        signature = classified_error.error_signature
        
        # 查找现有模式
        for pattern_id, pattern in self.error_patterns.items():
            if self._match_pattern(signature, pattern.error_signature):
                # 更新模式统计
                pattern.frequency += 1
                pattern.last_seen = datetime.now()
                self._save_patterns()
                return pattern_id
        
        # 创建新模式
        pattern_id = f"pattern_{len(self.error_patterns) + 1}"
        new_pattern = ErrorPattern(
            pattern_id=pattern_id,
            error_signature=signature,
            category=classified_error.category,
            severity=classified_error.severity,
            component=classified_error.component,
            description=f"自动识别的错误模式: {signature[:50]}..."
        )
        
        self.error_patterns[pattern_id] = new_pattern
        self._save_patterns()
        
        return pattern_id
    
    def _match_pattern(self, signature1: str, signature2: str, threshold: float = 0.8) -> bool:
        """匹配错误模式"""
        # 简单的字符串相似度匹配
        if signature1 == signature2:
            return True
        
        # 提取错误类型和关键词
        def extract_keywords(sig: str) -> set:
            parts = sig.split(':')
            if len(parts) >= 2:
                error_type = parts[0]
                message = parts[1]
                keywords = set(re.findall(r'\w+', message.lower()))
                keywords.add(error_type.lower())
                return keywords
            return set()
        
        keywords1 = extract_keywords(signature1)
        keywords2 = extract_keywords(signature2)
        
        if not keywords1 or not keywords2:
            return False
        
        # 计算Jaccard相似度
        intersection = len(keywords1.intersection(keywords2))
        union = len(keywords1.union(keywords2))
        
        similarity = intersection / union if union > 0 else 0
        return similarity >= threshold
    
    def get_error_statistics(self, time_window: Optional[timedelta] = None) -> Dict[str, Any]:
        """获取错误统计信息"""
        if time_window:
            cutoff_time = datetime.now() - time_window
            errors = [e for e in self.error_history if e.timestamp >= cutoff_time]
        else:
            errors = self.error_history
        
        if not errors:
            return {
                'total_errors': 0,
                'by_category': {},
                'by_severity': {},
                'by_component': {},
                'top_patterns': []
            }
        
        # 统计分析
        categories = Counter(e.category.value for e in errors)
        severities = Counter(e.severity.value for e in errors)
        components = Counter(e.component for e in errors)
        patterns = Counter(e.pattern_id for e in errors if e.pattern_id)
        
        return {
            'total_errors': len(errors),
            'by_category': dict(categories),
            'by_severity': dict(severities),
            'by_component': dict(components),
            'top_patterns': patterns.most_common(10),
            'time_window': str(time_window) if time_window else 'all_time'
        }
    
    def get_pattern_analysis(self) -> Dict[str, Any]:
        """获取错误模式分析"""
        if not self.error_patterns:
            return {'patterns': [], 'insights': []}
        
        patterns_data = []
        for pattern in self.error_patterns.values():
            patterns_data.append({
                'pattern_id': pattern.pattern_id,
                'category': pattern.category.value,
                'severity': pattern.severity.value,
                'component': pattern.component,
                'frequency': pattern.frequency,
                'first_seen': pattern.first_seen.isoformat(),
                'last_seen': pattern.last_seen.isoformat(),
                'description': pattern.description
            })
        
        # 生成洞察
        insights = []
        
        # 最频繁的错误模式
        most_frequent = max(self.error_patterns.values(), key=lambda p: p.frequency)
        if most_frequent.frequency > 5:
            insights.append(f"最频繁的错误模式是 {most_frequent.pattern_id}，出现了 {most_frequent.frequency} 次")
        
        # 最近的新模式
        recent_patterns = [p for p in self.error_patterns.values() 
                          if (datetime.now() - p.first_seen).days <= 1]
        if recent_patterns:
            insights.append(f"最近24小时内出现了 {len(recent_patterns)} 个新的错误模式")
        
        # 严重错误模式
        critical_patterns = [p for p in self.error_patterns.values() 
                           if p.severity == ErrorSeverity.CRITICAL]
        if critical_patterns:
            insights.append(f"发现 {len(critical_patterns)} 个严重错误模式需要立即关注")
        
        return {
            'patterns': patterns_data,
            'insights': insights,
            'total_patterns': len(self.error_patterns)
        }
    
    def _load_patterns(self):
        """加载错误模式"""
        try:
            with open(self.pattern_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            for pattern_data in data.get('patterns', []):
                pattern = ErrorPattern(
                    pattern_id=pattern_data['pattern_id'],
                    error_signature=pattern_data['error_signature'],
                    category=ErrorCategory(pattern_data['category']),
                    severity=ErrorSeverity(pattern_data['severity']),
                    component=pattern_data['component'],
                    frequency=pattern_data.get('frequency', 1),
                    first_seen=datetime.fromisoformat(pattern_data['first_seen']),
                    last_seen=datetime.fromisoformat(pattern_data['last_seen']),
                    description=pattern_data.get('description', '')
                )
                self.error_patterns[pattern.pattern_id] = pattern
                
            self.logger.info(f"加载了 {len(self.error_patterns)} 个错误模式")
            
        except FileNotFoundError:
            self.logger.info("错误模式文件不存在，将创建新文件")
        except Exception as e:
            self.logger.error(f"加载错误模式失败: {e}")
    
    def _save_patterns(self):
        """保存错误模式"""
        try:
            import os
            os.makedirs(os.path.dirname(self.pattern_file), exist_ok=True)
            
            patterns_data = []
            for pattern in self.error_patterns.values():
                patterns_data.append({
                    'pattern_id': pattern.pattern_id,
                    'error_signature': pattern.error_signature,
                    'category': pattern.category.value,
                    'severity': pattern.severity.value,
                    'component': pattern.component,
                    'frequency': pattern.frequency,
                    'first_seen': pattern.first_seen.isoformat(),
                    'last_seen': pattern.last_seen.isoformat(),
                    'description': pattern.description
                })
            
            data = {
                'patterns': patterns_data,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.pattern_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存错误模式失败: {e}")