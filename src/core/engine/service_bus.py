#!/usr/bin/env python3
"""
统一服务总线
整合所有项目模块的核心架构组件
"""

import sys
import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.common.utils.logging import get_logger, setup_system_logging
from src.common.utils.messages import ChineseMessageManager


@dataclass
class ServiceStatus:
    """服务状态数据结构"""
    name: str
    status: str
    last_update: datetime
    details: Dict[str, Any]


class ServiceBus:
    """
    统一服务总线
    
    整合所有项目模块，提供统一的服务接口：
    - 量化交易系统核心功能
    - Web界面后端API
    - 配置管理系统
    - 错误处理和日志系统
    - 监控和健康检查
    - 启动和生命周期管理
    """
    
    def __init__(self):
        """初始化服务总线"""
        # 设置系统日志
        setup_system_logging()
        self.logger = get_logger("service_bus")
        
        # 初始化消息管理器
        self.message_manager = ChineseMessageManager()
        
        self.logger.info("🚀 初始化统一服务总线...")
        
        # 服务注册表
        self.services = {}
        self.service_status = {}
        
        # 核心组件
        self._data_manager = None
        self._api_client = None
        self._config_manager = None
        self._error_handler = None
        self._health_checker = None
        self._trading_system = None
        self._monitoring_system = None
        
        # 初始化核心服务
        self._initialize_core_services()
        
        self.logger.info("✅ 统一服务总线初始化完成")
    
    def _initialize_core_services(self):
        """初始化核心服务"""
        try:
            # 延迟导入避免循环依赖
            from src.market.data.manager import DataManager
            from src.market.strategies.multi_strategy_manager import StrategyPortfolioManager
            from src.backtest.engine import BacktestEngine
            from src.risk.manager import RiskManager
            from src.analysis.metrics import MetricsCalculator
            
            # 初始化量化交易系统核心组件
            self.logger.info("正在初始化数据管理器...")
            self._data_manager = DataManager(use_sqlite=True)
            self.register_service("data_manager", self._data_manager)
            
            self.logger.info("正在初始化策略管理器...")
            self._strategy_manager = StrategyPortfolioManager()
            self.register_service("strategy_manager", self._strategy_manager)
            
            self.logger.info("正在初始化回测引擎...")
            self._backtest_engine = BacktestEngine(initial_capital=100000)
            self.register_service("backtest_engine", self._backtest_engine)
            
            self.logger.info("正在初始化风险管理器...")
            self._risk_manager = RiskManager()
            self.register_service("risk_manager", self._risk_manager)
            
            self.logger.info("正在初始化指标计算器...")
            self._metrics_calculator = MetricsCalculator()
            self.register_service("metrics_calculator", self._metrics_calculator)
            
            # 尝试初始化配置管理系统
            self._initialize_config_manager()
            
            # 尝试初始化健康检查系统
            self._initialize_health_checker()
            
            # 初始化统一监控系统
            self._initialize_monitoring_system()
            
        except Exception as e:
            self.logger.error(f"核心服务初始化失败: {e}")
            self.logger.warning("将使用基础模式继续运行...")
            # 不抛出异常，允许系统继续运行
            self._initialize_fallback_services()

    def _initialize_fallback_services(self):
        """初始化后备服务（当主要服务无法初始化时）"""
        self.logger.info("正在初始化后备服务...")

        # 注册空的服务占位符
        self.register_service("data_manager", None)
        self.register_service("strategy_manager", None)
        self.register_service("backtest_engine", None)
        self.register_service("risk_manager", None)
        self.register_service("metrics_calculator", None)

        # 尝试初始化基础组件
        try:
            self._initialize_config_manager()
        except Exception as e:
            self.logger.warning(f"配置管理器初始化失败: {e}")

        try:
            self._initialize_health_checker()
        except Exception as e:
            self.logger.warning(f"健康检查器初始化失败: {e}")

        self.logger.info("✅ 后备服务初始化完成")

    def _initialize_config_manager(self):
        """初始化配置管理系统"""
        try:
            # 使用统一的配置管理器
            from src.config.configuration_manager import ConfigurationManager

            self.logger.info("正在初始化配置管理系统...")
            self._config_manager = ConfigurationManager()
            self.register_service("config_manager", self._config_manager)

        except ImportError:
            self.logger.warning("配置管理系统模块未找到，使用基础配置")
            self._config_manager = None
    
    def _initialize_health_checker(self):
        """初始化健康检查系统"""
        try:
            # 使用统一的健康检查器
            from src.core.monitoring.health_checker import HealthChecker

            self.logger.info("正在初始化健康检查系统...")
            self._health_checker = HealthChecker()
            self.register_service("health_checker", self._health_checker)

        except ImportError:
            self.logger.warning("健康检查系统模块未找到，创建基础版本")
            self._health_checker = BasicHealthChecker()
            self.register_service("health_checker", self._health_checker)
    
    def _initialize_monitoring_system(self):
        """初始化统一监控系统"""
        try:
            from src.core.engine.unified_monitoring import get_monitoring_system
            
            self.logger.info("正在初始化统一监控系统...")
            self._monitoring_system = get_monitoring_system()
            self.register_service("monitoring_system", self._monitoring_system)
            
            # 启动监控
            self._monitoring_system.start_monitoring()
            
        except Exception as e:
            self.logger.warning(f"统一监控系统初始化失败: {e}")
            self._monitoring_system = None
    
    def register_service(self, name: str, service: Any):
        """注册服务"""
        self.services[name] = service
        self.service_status[name] = ServiceStatus(
            name=name,
            status="active",
            last_update=datetime.now(),
            details={"registered": True}
        )
        self.logger.debug(f"服务已注册: {name}")
    
    def get_service(self, name: str) -> Optional[Any]:
        """获取服务"""
        return self.services.get(name)
    
    def get_service_status(self, name: str) -> Optional[ServiceStatus]:
        """获取服务状态"""
        return self.service_status.get(name)
    
    def list_services(self) -> Dict[str, ServiceStatus]:
        """列出所有服务状态"""
        return self.service_status.copy()
    
    # 统一接口方法 - 数据管理
    def get_market_data(self, symbol: str, start_date: str, end_date: str, market: str = None):
        """统一的市场数据获取接口"""
        if self._data_manager:
            return self._data_manager.get_market_data(symbol, start_date, end_date, market)
        else:
            raise RuntimeError("数据管理器未初始化")
    
    def get_data_sources(self):
        """获取数据源列表"""
        if self._data_manager:
            return self._data_manager.get_available_sources()
        else:
            return []
    
    # 统一接口方法 - 策略管理
    def add_strategy(self, strategy, weight: float = 1.0):
        """添加策略"""
        if self._strategy_manager:
            return self._strategy_manager.add_strategy(strategy, weight)
        else:
            raise RuntimeError("策略管理器未初始化")
    
    def get_strategies(self):
        """获取策略列表"""
        if self._strategy_manager:
            return self._strategy_manager.get_strategies()
        else:
            return []
    
    # 统一接口方法 - 回测执行
    def run_backtest(self, strategy_id: str, start_date: str, end_date: str):
        """运行回测"""
        if self._backtest_engine and self._strategy_manager:
            strategy = self._strategy_manager.get_strategy(strategy_id)
            if strategy:
                return self._backtest_engine.run_backtest(strategy, start_date, end_date)
            else:
                raise ValueError(f"策略 {strategy_id} 不存在")
        else:
            raise RuntimeError("回测引擎或策略管理器未初始化")
    
    # 统一接口方法 - 系统监控
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统整体状态"""
        status = {}
        
        for name, service_status in self.service_status.items():
            status[name] = {
                "status": service_status.status,
                "last_update": service_status.last_update.isoformat(),
                "details": service_status.details
            }
        
        # 添加健康检查信息
        if self._health_checker:
            try:
                health_info = self._health_checker.run_health_check()
                status["health_check"] = health_info
            except Exception as e:
                status["health_check"] = {"error": str(e)}
        
        return status
    
    def check_health(self) -> bool:
        """检查系统健康状态"""
        # 优先使用统一监控系统
        if self._monitoring_system:
            try:
                health_status = self._monitoring_system.get_overall_health_status()
                overall_status = health_status.get("overall_status")
                return overall_status.value == "healthy" if hasattr(overall_status, 'value') else overall_status == "healthy"
            except Exception as e:
                self.logger.warning(f"统一监控系统健康检查失败: {e}")
        
        # 回退到原有健康检查器
        if self._health_checker:
            try:
                result = self._health_checker.run_health_check()
                return result.get("overall_status", "error") == "healthy"
            except Exception:
                return False
        return True  # 如果没有健康检查器，默认认为健康
    
    # 统一接口方法 - 监控
    def get_system_health_status(self) -> Dict[str, Any]:
        """获取详细的系统健康状态"""
        if self._monitoring_system:
            return self._monitoring_system.get_overall_health_status()
        elif self._health_checker:
            try:
                return self._health_checker.run_health_check()
            except Exception as e:
                return {
                    "overall_status": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
        else:
            return {
                "overall_status": "unknown",
                "message": "无可用的健康检查系统",
                "timestamp": datetime.now().isoformat()
            }
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        if self._monitoring_system:
            return self._monitoring_system.get_system_metrics()
        else:
            return {}
    
    def record_metric(self, name: str, value: float, labels: Dict[str, str] = None):
        """记录系统指标"""
        if self._monitoring_system:
            try:
                from src.core.engine.unified_monitoring import MetricType
                self._monitoring_system.record_metric(
                    name=name,
                    value=value,
                    metric_type=MetricType.GAUGE,
                    labels=labels
                )
            except ImportError:
                # 如果统一监控不可用，直接记录日志
                self.logger.debug(f"指标记录 {name}: {value}")
        else:
            self.logger.debug(f"指标记录 {name}: {value}")
    
    def register_health_check(self, name: str, description: str, check_function, interval: int = 60):
        """注册自定义健康检查"""
        if self._monitoring_system:
            self._monitoring_system.register_health_check(
                name=name,
                description=description,
                check_function=check_function,
                interval=interval
            )
        else:
            self.logger.warning(f"无法注册健康检查 {name}：监控系统不可用")
    
    # 统一接口方法 - 错误处理
    def handle_error(self, error: Exception, context: Dict[str, Any] = None):
        """统一错误处理"""
        error_info = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context or {},
            "timestamp": datetime.now().isoformat()
        }
        
        # 记录错误日志
        self.logger.error(f"系统错误: {error_info}")
        
        # 如果有错误处理器，调用它
        if hasattr(self, '_error_handler') and self._error_handler:
            try:
                return self._error_handler.handle_error(error, context)
            except Exception as e:
                self.logger.error(f"错误处理器执行失败: {e}")
        
        return error_info
    
    def shutdown(self):
        """优雅关闭服务总线"""
        self.logger.info("正在关闭服务总线...")
        
        # 关闭各个服务
        for name, service in self.services.items():
            try:
                if hasattr(service, 'close'):
                    service.close()
                elif hasattr(service, 'shutdown'):
                    service.shutdown()
                self.logger.debug(f"服务已关闭: {name}")
            except Exception as e:
                self.logger.warning(f"关闭服务失败: {name} - {e}")
        
        self.logger.info("✅ 服务总线已关闭")


class BasicHealthChecker:
    """基础健康检查器（当专门的健康检查器不可用时使用）"""
    
    def __init__(self):
        self.logger = get_logger("basic_health_checker")
    
    def run_health_check(self) -> Dict[str, Any]:
        """运行基础健康检查"""
        return {
            "overall_status": "healthy",
            "checks": {
                "basic_check": "passed"
            },
            "timestamp": datetime.now().isoformat()
        }


# 全局服务总线实例
_service_bus_instance = None


def get_service_bus() -> ServiceBus:
    """获取全局服务总线实例（单例模式）"""
    global _service_bus_instance
    if _service_bus_instance is None:
        _service_bus_instance = ServiceBus()
    return _service_bus_instance