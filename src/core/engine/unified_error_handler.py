#!/usr/bin/env python3
"""
统一错误处理和日志系统
整合所有项目的错误处理需求，支持中文化消息
"""

import sys
import traceback
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from src.common.utils.logging import get_logger
from src.common.utils.messages import get_message_manager


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误类别"""
    SYSTEM = "system"
    DATA = "data"
    API = "api"
    USER = "user"
    NETWORK = "network"
    CONFIGURATION = "configuration"
    SECURITY = "security"


@dataclass
class ErrorContext:
    """错误上下文信息"""
    timestamp: datetime
    error_id: str
    category: ErrorCategory
    severity: ErrorSeverity
    component: str
    operation: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


@dataclass
class ErrorRecord:
    """错误记录"""
    context: ErrorContext
    exception: Exception
    message: str
    chinese_message: str
    stack_trace: str
    recovery_actions: List[str]
    is_recovered: bool = False
    recovery_timestamp: Optional[datetime] = None


class UnifiedErrorHandler:
    """
    统一错误处理系统
    
    功能：
    - 统一的错误分类和处理
    - 支持中文化错误消息
    - 错误恢复机制
    - 错误统计和分析
    - 集成到所有项目模块
    """
    
    def __init__(self):
        self.logger = get_logger("unified_error_handler")
        
        # 初始化中文消息管理器
        try:
            self.message_manager = get_message_manager()
        except Exception as e:
            self.logger.warning(f"中文消息管理器初始化失败，使用默认处理: {e}")
            self.message_manager = None
        
        # 错误记录存储
        self.error_records: List[ErrorRecord] = []
        self.error_handlers: Dict[str, List[Callable]] = {}
        self.recovery_handlers: Dict[str, List[Callable]] = {}
        
        # 错误统计
        self.error_stats = {
            "total_errors": 0,
            "by_category": {},
            "by_severity": {},
            "by_component": {}
        }
        
        self.logger.info("🛡️ 统一错误处理系统初始化完成")
    
    def register_handler(self, error_type: str, handler: Callable, category: str = "custom"):
        """注册错误处理器"""
        if category not in self.error_handlers:
            self.error_handlers[category] = []
        
        self.error_handlers[category].append({
            "type": error_type,
            "handler": handler
        })
        
        self.logger.debug(f"已注册错误处理器: {error_type} -> {category}")
    
    def register_recovery_handler(self, error_type: str, recovery_handler: Callable):
        """注册错误恢复处理器"""
        if error_type not in self.recovery_handlers:
            self.recovery_handlers[error_type] = []
        
        self.recovery_handlers[error_type].append(recovery_handler)
        self.logger.debug(f"已注册恢复处理器: {error_type}")
    
    def handle_error(self, 
                    exception: Exception, 
                    context: Dict[str, Any] = None,
                    component: str = "unknown",
                    operation: str = "unknown",
                    category: ErrorCategory = ErrorCategory.SYSTEM,
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                    user_id: str = None) -> ErrorRecord:
        """处理错误"""
        
        # 创建错误上下文
        error_context = ErrorContext(
            timestamp=datetime.now(),
            error_id=self._generate_error_id(),
            category=category,
            severity=severity,
            component=component,
            operation=operation,
            user_id=user_id,
            session_id=context.get("session_id") if context else None,
            additional_data=context
        )
        
        # 获取错误消息
        english_message = str(exception)
        chinese_message = self._get_chinese_message(exception, context)
        
        # 获取堆栈跟踪
        stack_trace = self._get_stack_trace(exception)
        
        # 获取恢复建议
        recovery_actions = self._get_recovery_actions(exception, category)
        
        # 创建错误记录
        error_record = ErrorRecord(
            context=error_context,
            exception=exception,
            message=english_message,
            chinese_message=chinese_message,
            stack_trace=stack_trace,
            recovery_actions=recovery_actions
        )
        
        # 记录错误
        self.error_records.append(error_record)
        self._update_stats(error_record)
        
        # 记录日志
        self._log_error(error_record)
        
        # 尝试自动恢复
        self._attempt_recovery(error_record)
        
        # 调用注册的处理器
        self._call_registered_handlers(error_record)
        
        return error_record
    
    def _generate_error_id(self) -> str:
        """生成错误ID"""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def _get_chinese_message(self, exception: Exception, context: Dict[str, Any] = None) -> str:
        """获取中文错误消息"""
        if self.message_manager:
            try:
                # 尝试使用消息管理器获取错误消息
                error_type = type(exception).__name__
                error_message = str(exception)
                
                # 构建消息参数
                params = {"error": error_message, "type": error_type}
                if context:
                    params.update(context)
                
                # 尝试获取特定的错误消息
                message_key = f"error.{error_type.lower()}"
                return self.message_manager.get_message(message_key, **params)
            except Exception:
                pass
        
        # 默认中文消息映射
        error_translations = {
            "ConnectionError": "连接错误",
            "TimeoutError": "操作超时",
            "FileNotFoundError": "文件未找到",
            "PermissionError": "权限不足",
            "ValueError": "数值错误",
            "KeyError": "键值错误",
            "ImportError": "导入错误",
            "ModuleNotFoundError": "模块未找到",
            "AttributeError": "属性错误",
            "TypeError": "类型错误",
            "RuntimeError": "运行时错误",
            "DatabaseError": "数据库错误",
            "APIError": "API错误",
            "ConfigurationError": "配置错误"
        }
        
        error_type = type(exception).__name__
        base_message = error_translations.get(error_type, f"{error_type}错误")
        
        return f"{base_message}: {str(exception)}"
    
    def _get_stack_trace(self, exception: Exception) -> str:
        """获取堆栈跟踪"""
        return traceback.format_exception(type(exception), exception, exception.__traceback__)
    
    def _get_recovery_actions(self, exception: Exception, category: ErrorCategory) -> List[str]:
        """获取恢复建议"""
        error_type = type(exception).__name__
        
        # 基础恢复建议
        basic_actions = {
            "ConnectionError": ["检查网络连接", "重试操作", "检查服务状态"],
            "TimeoutError": ["增加超时时间", "检查网络延迟", "重试操作"],
            "FileNotFoundError": ["检查文件路径", "确认文件存在", "检查权限"],
            "PermissionError": ["检查文件权限", "以管理员身份运行", "修改文件权限"],
            "ValueError": ["检查输入数据格式", "验证参数范围", "清理数据"],
            "KeyError": ["检查配置文件", "确认键值存在", "使用默认值"],
            "ImportError": ["检查模块安装", "更新依赖包", "检查Python路径"],
            "DatabaseError": ["检查数据库连接", "验证数据库状态", "重启数据库服务"]
        }
        
        # 按类别的恢复建议
        category_actions = {
            ErrorCategory.DATA: ["检查数据源", "验证数据格式", "清理缓存"],
            ErrorCategory.API: ["检查API状态", "验证API密钥", "重试请求"],
            ErrorCategory.NETWORK: ["检查网络连接", "使用代理", "切换网络"],
            ErrorCategory.CONFIGURATION: ["检查配置文件", "重置配置", "使用默认配置"],
            ErrorCategory.SECURITY: ["检查权限", "更新凭据", "联系管理员"]
        }
        
        actions = basic_actions.get(error_type, ["重启应用", "联系技术支持"])
        actions.extend(category_actions.get(category, []))
        
        return list(set(actions))  # 去重
    
    def _update_stats(self, error_record: ErrorRecord):
        """更新错误统计"""
        self.error_stats["total_errors"] += 1
        
        # 按类别统计
        category = error_record.context.category.value
        if category not in self.error_stats["by_category"]:
            self.error_stats["by_category"][category] = 0
        self.error_stats["by_category"][category] += 1
        
        # 按严重程度统计
        severity = error_record.context.severity.value
        if severity not in self.error_stats["by_severity"]:
            self.error_stats["by_severity"][severity] = 0
        self.error_stats["by_severity"][severity] += 1
        
        # 按组件统计
        component = error_record.context.component
        if component not in self.error_stats["by_component"]:
            self.error_stats["by_component"][component] = 0
        self.error_stats["by_component"][component] += 1
    
    def _log_error(self, error_record: ErrorRecord):
        """记录错误日志"""
        context = error_record.context
        
        # 构建日志消息
        log_message = f"[{context.error_id}] {context.component}.{context.operation}: {error_record.chinese_message}"
        
        # 根据严重程度选择日志级别
        if context.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message)
        elif context.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message)
        elif context.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
        
        # 记录详细信息（仅在DEBUG级别）
        self.logger.debug(f"错误详情 [{context.error_id}]: {error_record.message}")
        if context.additional_data:
            self.logger.debug(f"上下文数据 [{context.error_id}]: {context.additional_data}")
    
    def _attempt_recovery(self, error_record: ErrorRecord):
        """尝试自动恢复"""
        error_type = type(error_record.exception).__name__
        
        if error_type in self.recovery_handlers:
            for recovery_handler in self.recovery_handlers[error_type]:
                try:
                    success = recovery_handler(error_record)
                    if success:
                        error_record.is_recovered = True
                        error_record.recovery_timestamp = datetime.now()
                        self.logger.info(f"错误恢复成功 [{error_record.context.error_id}]")
                        break
                except Exception as e:
                    self.logger.warning(f"恢复处理器执行失败: {e}")
    
    def _call_registered_handlers(self, error_record: ErrorRecord):
        """调用注册的错误处理器"""
        error_type = type(error_record.exception).__name__
        category = error_record.context.category.value
        
        # 调用特定类别的处理器
        if category in self.error_handlers:
            for handler_info in self.error_handlers[category]:
                if handler_info["type"] == error_type or handler_info["type"] == "all":
                    try:
                        handler_info["handler"](error_record)
                    except Exception as e:
                        self.logger.warning(f"错误处理器执行失败: {e}")
    
    def get_error_stats(self) -> Dict[str, Any]:
        """获取错误统计"""
        return self.error_stats.copy()
    
    def get_recent_errors(self, limit: int = 10, category: str = None, severity: str = None) -> List[ErrorRecord]:
        """获取最近的错误记录"""
        filtered_errors = self.error_records
        
        if category:
            filtered_errors = [e for e in filtered_errors if e.context.category.value == category]
        
        if severity:
            filtered_errors = [e for e in filtered_errors if e.context.severity.value == severity]
        
        # 按时间降序排序并限制数量
        filtered_errors.sort(key=lambda e: e.context.timestamp, reverse=True)
        return filtered_errors[:limit]
    
    def clear_old_errors(self, days: int = 7):
        """清理旧的错误记录"""
        from datetime import timedelta
        cutoff_date = datetime.now() - timedelta(days=days)
        
        initial_count = len(self.error_records)
        self.error_records = [e for e in self.error_records if e.context.timestamp > cutoff_date]
        cleared_count = initial_count - len(self.error_records)
        
        if cleared_count > 0:
            self.logger.info(f"已清理 {cleared_count} 条旧错误记录")
    
    def export_error_report(self, filepath: str = None) -> str:
        """导出错误报告"""
        if not filepath:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"logs/error_report_{timestamp}.json"
        
        import json
        
        # 准备报告数据
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "summary": self.error_stats,
            "recent_errors": []
        }
        
        # 添加最近的错误记录
        for error_record in self.get_recent_errors(50):
            report_data["recent_errors"].append({
                "error_id": error_record.context.error_id,
                "timestamp": error_record.context.timestamp.isoformat(),
                "category": error_record.context.category.value,
                "severity": error_record.context.severity.value,
                "component": error_record.context.component,
                "operation": error_record.context.operation,
                "message": error_record.chinese_message,
                "is_recovered": error_record.is_recovered,
                "recovery_actions": error_record.recovery_actions
            })
        
        # 保存报告
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"错误报告已导出: {filepath}")
        return filepath


# 全局错误处理器实例
_error_handler_instance = None


def get_error_handler() -> UnifiedErrorHandler:
    """获取全局错误处理器实例（单例模式）"""
    global _error_handler_instance
    if _error_handler_instance is None:
        _error_handler_instance = UnifiedErrorHandler()
    return _error_handler_instance


def handle_system_error(func):
    """系统错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            error_handler = get_error_handler()
            error_record = error_handler.handle_error(
                e,
                context={"function": func.__name__, "args": str(args), "kwargs": str(kwargs)},
                component=func.__module__ or "unknown",
                operation=func.__name__
            )
            
            # 重新抛出严重错误
            if error_record.context.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
                raise
            
            return None
    
    return wrapper