#!/usr/bin/env python3
"""
统一配置管理系统
整合所有项目的配置需求
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from datetime import datetime

from src.common.utils.logging import get_logger


@dataclass
class ConfigSection:
    """配置节数据结构"""
    name: str
    data: Dict[str, Any]
    source_file: str
    last_updated: datetime


class UnifiedConfigManager:
    """
    统一配置管理器
    
    整合以下项目的配置需求：
    - 后端API服务配置
    - 前端界面配置
    - 数据源配置
    - 系统启动配置
    - 监控系统配置
    - 文件组织规范配置
    """
    
    def __init__(self):
        self.logger = get_logger("unified_config")
        
        # 项目根目录
        self.project_root = Path(__file__).parent.parent.parent
        self.config_root = self.project_root / "config"
        
        # 配置数据存储
        self.configs = {}
        self.config_sections = {}
        
        self.logger.info("🔧 初始化统一配置管理系统...")
        
        # 加载所有配置
        self._load_all_configs()
        
        self.logger.info("✅ 统一配置管理系统初始化完成")
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        try:
            # 主配置文件
            self._load_main_config()
            
            # 数据源配置
            self._load_data_source_config()
            
            # 市场配置
            self._load_market_config()
            
            # 环境特定配置
            self._load_environment_configs()
            
            # Web UI配置
            self._load_web_ui_configs()
            
            # 文件组织配置
            self._load_file_organization_configs()
            
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            raise
    
    def _load_main_config(self):
        """加载主配置文件"""
        config_file = self.config_root / "config.yaml"
        if config_file.exists():
            self._load_yaml_config("main", config_file)
        else:
            # 创建默认主配置
            default_config = {
                "system": {
                    "name": "量化交易系统",
                    "version": "1.0.0",
                    "environment": "development"
                },
                "logging": {
                    "level": "INFO",
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                },
                "database": {
                    "type": "sqlite",
                    "path": "data/active/db/trading_system.db"
                }
            }
            self._save_config("main", default_config, config_file)
    
    def _load_data_source_config(self):
        """加载数据源配置"""
        config_file = self.config_root / "data_sources.yaml"
        if config_file.exists():
            self._load_yaml_config("data_sources", config_file)
        else:
            # 创建默认数据源配置
            default_config = {
                "sources": {
                    "yahoo_finance": {
                        "enabled": True,
                        "rate_limit": 100,
                        "timeout": 30
                    },
                    "akshare": {
                        "enabled": True,
                        "rate_limit": 200,
                        "timeout": 30
                    },
                    "binance": {
                        "enabled": False,
                        "api_key": "",
                        "rate_limit": 1200,
                        "timeout": 10
                    },
                    "fred": {
                        "enabled": False,
                        "api_key": "",
                        "rate_limit": 120,
                        "timeout": 30
                    }
                }
            }
            self._save_config("data_sources", default_config, config_file)
    
    def _load_market_config(self):
        """加载市场配置"""
        config_file = self.config_root / "market_config.yaml"
        if config_file.exists():
            self._load_yaml_config("market", config_file)
    
    def _load_environment_configs(self):
        """加载环境特定配置"""
        env_dir = self.config_root / "environments"
        if env_dir.exists():
            for config_file in env_dir.glob("*.yaml"):
                env_name = config_file.stem
                self._load_yaml_config(f"env_{env_name}", config_file)
    
    def _load_web_ui_configs(self):
        """加载Web UI配置"""
        # 后端配置
        backend_config = self.project_root / "web_ui" / "backend" / "config" / "config.yaml"
        if backend_config.exists():
            self._load_yaml_config("web_ui_backend", backend_config)
        
        # 后端数据源配置（优先使用主配置，如果存在重复）
        backend_data_sources = self.project_root / "web_ui" / "backend" / "config" / "data_sources.yaml"
        if backend_data_sources.exists() and "data_sources" not in self.config_sections:
            # 只有在主数据源配置不存在时才加载后端的
            self._load_yaml_config("web_ui_backend_data_sources", backend_data_sources)
        
        # 前端配置 - 从package.json中读取相关配置
        frontend_package = self.project_root / "web_ui" / "frontend" / "package.json"
        if frontend_package.exists():
            with open(frontend_package, 'r', encoding='utf-8') as f:
                package_data = json.load(f)
            
            frontend_config = {
                "name": package_data.get("name", "unknown"),
                "version": package_data.get("version", "1.0.0"),
                "dependencies": package_data.get("dependencies", {}),
                "scripts": package_data.get("scripts", {})
            }
            
            self.config_sections["web_ui_frontend"] = ConfigSection(
                name="web_ui_frontend",
                data=frontend_config,
                source_file=str(frontend_package),
                last_updated=datetime.now()
            )
    
    def _load_file_organization_configs(self):
        """加载文件组织配置"""
        # 文件组织规范配置
        org_config_file = self.project_root / "tools" / "core" / "file_organization" / "config.json"
        if org_config_file.exists():
            self._load_json_config("file_organization", org_config_file)
        
        # 监控配置
        monitoring_config_file = self.project_root / "tools" / "core" / "file_organization" / "monitoring_config.yaml"
        if monitoring_config_file.exists():
            self._load_yaml_config("monitoring", monitoring_config_file)
        
        # 数据管理配置
        data_mgmt_config = self.project_root / "tools" / "core" / "data_management" / "config.yaml"
        if data_mgmt_config.exists():
            self._load_yaml_config("data_management", data_mgmt_config)
        
        # 合规规则配置
        compliance_file = self.project_root / "tools" / "core" / "file_organization" / "compliance_rules.json"
        if compliance_file.exists():
            self._load_json_config("compliance_rules", compliance_file)
    
    def _load_yaml_config(self, name: str, file_path: Path):
        """加载YAML配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f) or {}
            
            self.config_sections[name] = ConfigSection(
                name=name,
                data=data,
                source_file=str(file_path),
                last_updated=datetime.now()
            )
            
            self.logger.debug(f"已加载配置: {name} from {file_path}")
            
        except Exception as e:
            self.logger.error(f"加载YAML配置失败: {file_path} - {e}")
    
    def _load_json_config(self, name: str, file_path: Path):
        """加载JSON配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.config_sections[name] = ConfigSection(
                name=name,
                data=data,
                source_file=str(file_path),
                last_updated=datetime.now()
            )
            
            self.logger.debug(f"已加载配置: {name} from {file_path}")
            
        except Exception as e:
            self.logger.error(f"加载JSON配置失败: {file_path} - {e}")
    
    def _save_config(self, name: str, data: Dict[str, Any], file_path: Path):
        """保存配置到文件"""
        try:
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存YAML格式
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.safe_dump(data, f, default_flow_style=False, allow_unicode=True)
            
            # 更新内存中的配置
            self.config_sections[name] = ConfigSection(
                name=name,
                data=data,
                source_file=str(file_path),
                last_updated=datetime.now()
            )
            
            self.logger.info(f"配置已保存: {name} to {file_path}")
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {file_path} - {e}")
            raise
    
    def get_config(self, section_name: str, key: str = None, default: Any = None) -> Any:
        """获取配置值"""
        if section_name not in self.config_sections:
            self.logger.warning(f"配置节不存在: {section_name}")
            return default
        
        section_data = self.config_sections[section_name].data
        
        if key is None:
            return section_data
        
        # 支持嵌套键，如 "database.host"
        keys = key.split('.')
        current = section_data
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return default
        
        return current
    
    def set_config(self, section_name: str, key: str, value: Any, save_to_file: bool = True):
        """设置配置值"""
        if section_name not in self.config_sections:
            # 创建新的配置节
            self.config_sections[section_name] = ConfigSection(
                name=section_name,
                data={},
                source_file="",
                last_updated=datetime.now()
            )
        
        section = self.config_sections[section_name]
        
        # 支持嵌套键设置
        keys = key.split('.')
        current = section.data
        
        # 导航到最后一级
        for k in keys[:-1]:
            if k not in current or not isinstance(current[k], dict):
                current[k] = {}
            current = current[k]
        
        # 设置值
        current[keys[-1]] = value
        section.last_updated = datetime.now()
        
        self.logger.debug(f"配置已更新: {section_name}.{key} = {value}")
        
        # 如果需要，保存到文件
        if save_to_file and section.source_file:
            source_path = Path(section.source_file)
            if source_path.exists():
                self._save_config(section_name, section.data, source_path)
    
    def get_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取所有配置"""
        return {name: section.data for name, section in self.config_sections.items()}
    
    def get_config_info(self) -> List[Dict[str, Any]]:
        """获取配置信息"""
        info = []
        for name, section in self.config_sections.items():
            info.append({
                "name": name,
                "source_file": section.source_file,
                "last_updated": section.last_updated.isoformat(),
                "keys_count": len(section.data) if isinstance(section.data, dict) else 0
            })
        return info
    
    def reload_config(self, section_name: str):
        """重新加载指定配置"""
        if section_name not in self.config_sections:
            self.logger.warning(f"配置节不存在: {section_name}")
            return False
        
        section = self.config_sections[section_name]
        source_path = Path(section.source_file)
        
        if source_path.exists():
            if source_path.suffix.lower() == '.yaml':
                self._load_yaml_config(section_name, source_path)
            elif source_path.suffix.lower() == '.json':
                self._load_json_config(section_name, source_path)
            
            self.logger.info(f"配置已重新加载: {section_name}")
            return True
        else:
            self.logger.error(f"配置文件不存在: {source_path}")
            return False
    
    def validate_config(self) -> Dict[str, List[str]]:
        """验证配置完整性"""
        errors = {}
        
        # 检查必需的配置节
        required_sections = ["main", "data_sources"]
        for section in required_sections:
            if section not in self.config_sections:
                if section not in errors:
                    errors[section] = []
                errors[section].append(f"必需的配置节 '{section}' 缺失")
        
        # 检查数据源配置
        if "data_sources" in self.config_sections:
            sources_config = self.config_sections["data_sources"].data.get("sources", {})
            for source_name, source_config in sources_config.items():
                if source_config.get("enabled") and source_name in ["binance", "fred"]:
                    if not source_config.get("api_key"):
                        if "data_sources" not in errors:
                            errors["data_sources"] = []
                        errors["data_sources"].append(f"{source_name} 已启用但缺少 API 密钥")
        
        return errors
    
    def get_merged_config(self) -> Dict[str, Any]:
        """获取合并后的完整配置"""
        merged = {}
        
        # 按优先级合并配置
        for section_name in ["main", "data_sources", "market", "env_development", "env_production"]:
            if section_name in self.config_sections:
                section_data = self.config_sections[section_name].data
                self._deep_merge(merged, section_data)
        
        return merged
    
    def _deep_merge(self, target: Dict, source: Dict):
        """深度合并字典"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_merge(target[key], value)
            else:
                target[key] = value


# 全局配置管理器实例
_config_manager_instance = None


def get_config_manager() -> UnifiedConfigManager:
    """获取全局配置管理器实例（单例模式）"""
    global _config_manager_instance
    if _config_manager_instance is None:
        _config_manager_instance = UnifiedConfigManager()
    return _config_manager_instance