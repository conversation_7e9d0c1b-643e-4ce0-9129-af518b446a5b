"""
实时交易引擎

提供高性能的实时交易执行功能，包括：
- 实时订单管理和路由
- 多市场连接和执行
- 延迟优化和性能监控
- 风险控制和合规检查
- 交易生命周期管理
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor
import uuid

from src.market.strategies.models.trading import Order, OrderFill, Trade, OrderStatus, OrderSide
from src.market.strategies.models.market_data import MarketData
from src.market.strategies.models.portfolio import Portfolio
from src.trading.execution.live_trading.order_manager import LiveOrderManager
from src.trading.execution.live_trading.market_data_feed import LiveMarketDataFeed
from src.trading.execution.live_trading.account_manager import AccountManager
from ..risk.manager import RiskManager

logger = logging.getLogger(__name__)

class EngineState(Enum):
    """交易引擎状态"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"

class ExecutionPriority(Enum):
    """执行优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

@dataclass
class EngineMetrics:
    """引擎性能指标"""
    orders_per_second: float = 0.0
    average_latency_ms: float = 0.0
    fill_rate: float = 0.0
    error_rate: float = 0.0
    active_orders: int = 0
    total_volume: float = 0.0
    uptime_seconds: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)

@dataclass
class OrderRequest:
    """订单请求"""
    order: Order
    priority: ExecutionPriority = ExecutionPriority.NORMAL
    callback: Optional[Callable] = None
    timeout_seconds: int = 30
    created_at: datetime = field(default_factory=datetime.now)
    attempts: int = 0
    max_attempts: int = 3

class TradingEngine:
    """
    实时交易引擎
    
    高性能的实时交易执行引擎，提供订单管理、市场连接、
    风险控制和性能监控等核心交易功能。
    
    主要特性:
    - 异步事件驱动架构
    - 多市场连接支持
    - 低延迟订单执行
    - 实时风险控制
    - 性能监控和优化
    - 故障恢复机制
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化交易引擎
        
        Args:
            config: 引擎配置参数
        """
        self.config = config or {}
        
        # 引擎状态
        self.state = EngineState.STOPPED
        self.start_time: Optional[datetime] = None
        self.stop_time: Optional[datetime] = None
        
        # 核心组件
        from ..live_trading.error_handler import LiveTradingErrorHandler
        from ..live_trading.trade_logger import TradeLogger
        from ..live_trading.order_manager import BrokerConfig, BrokerType
        from ..risk.manager_chinese import 风险管理器
        
        # 创建默认配置用于测试
        broker_config = BrokerConfig(
            broker_type=BrokerType.MOCK,
            api_key="test_key",
            api_secret="test_secret"
        )
        
        risk_manager = 风险管理器()
        error_handler = LiveTradingErrorHandler()
        trade_logger = TradeLogger()
        
        self.order_manager = LiveOrderManager(broker_config, risk_manager, error_handler, trade_logger)
        from ..live_trading.market_data_feed import LiveMarketDataFeed, MarketDataConfig
        from ..live_trading.account_manager import AccountManager
        
        # 创建Market Data Feed配置
        market_config = MarketDataConfig(
            symbols=["AAPL", "MSFT"],  # 默认测试符号
            data_types=["quote", "trade"],
            buffer_size=1000
        )
        
        self.market_data_feed = LiveMarketDataFeed(
            broker_client=None,  # 测试时使用None
            config=market_config,
            error_handler=error_handler
        )
        self.account_manager = AccountManager(
            broker_client=None,  # 测试时使用None
            account_id="test_account",
            error_handler=error_handler
        )
        self.risk_manager = RiskManager()
        
        # 订单管理
        self.pending_orders: Dict[str, OrderRequest] = {}
        self.active_orders: Dict[str, Order] = {}
        self.completed_orders: Dict[str, Order] = {}
        self.order_history: List[Order] = []
        
        # 交易记录
        self.trades: List[Trade] = []
        self.fills: List[OrderFill] = []
        
        # 性能指标
        self.metrics = EngineMetrics()
        self.performance_history: List[EngineMetrics] = []
        
        # 事件队列和处理
        self.event_queue = asyncio.Queue()
        self.order_queue = asyncio.Queue()
        self.priority_queue = asyncio.PriorityQueue()
        
        # 线程池
        self.executor = ThreadPoolExecutor(
            max_workers=self.config.get('max_workers', 8)
        )
        
        # 任务管理
        self.running_tasks: Set[asyncio.Task] = set()
        self._shutdown_event = asyncio.Event()
        
        # 回调函数
        self.order_callbacks: Dict[str, Callable] = {}
        self.event_callbacks: Dict[str, List[Callable]] = {}
        
        # 配置参数
        self.max_orders_per_second = self.config.get('max_orders_per_second', 100)
        self.order_timeout = self.config.get('order_timeout_seconds', 30)
        self.risk_check_enabled = self.config.get('risk_check_enabled', True)
        self.performance_monitoring = self.config.get('performance_monitoring', True)
        
    async def start(self) -> bool:
        """
        启动交易引擎
        
        Returns:
            bool: 启动成功返回True
        """
        try:
            if self.state != EngineState.STOPPED:
                logger.warning(f"引擎已在运行，当前状态: {self.state}")
                return False
            
            logger.info("🚀 正在启动交易引擎...")
            self.state = EngineState.STARTING
            self.start_time = datetime.now()
            
            # 初始化核心组件
            await self._initialize_components()
            
            # 启动后台任务
            await self._start_background_tasks()
            
            # 设置引擎状态
            self.state = EngineState.RUNNING
            logger.info("✅ 交易引擎启动成功")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 交易引擎启动失败: {e}")
            self.state = EngineState.ERROR
            return False
    
    async def stop(self) -> bool:
        """
        停止交易引擎
        
        Returns:
            bool: 停止成功返回True
        """
        try:
            if self.state != EngineState.RUNNING:
                logger.warning(f"引擎未在运行，当前状态: {self.state}")
                return False
            
            logger.info("⏹️ 正在停止交易引擎...")
            self.state = EngineState.STOPPING
            
            # 设置停止信号
            self._shutdown_event.set()
            
            # 等待所有任务完成
            await self._wait_for_tasks_completion()
            
            # 取消所有待处理订单
            await self._cancel_pending_orders()
            
            # 关闭组件
            await self._shutdown_components()
            
            # 设置最终状态
            self.state = EngineState.STOPPED
            self.stop_time = datetime.now()
            
            logger.info("✅ 交易引擎已停止")
            return True
            
        except Exception as e:
            logger.error(f"❌ 交易引擎停止失败: {e}")
            self.state = EngineState.ERROR
            return False
    
    async def submit_order(self, order: Order, 
                          priority: ExecutionPriority = ExecutionPriority.NORMAL,
                          callback: Optional[Callable] = None) -> str:
        """
        提交订单
        
        Args:
            order: 订单对象
            priority: 执行优先级
            callback: 完成回调函数
            
        Returns:
            str: 订单请求ID
        """
        try:
            if self.state != EngineState.RUNNING:
                raise RuntimeError(f"引擎未运行，当前状态: {self.state}")
            
            # 验证订单
            if not order.validate():
                raise ValueError("订单验证失败")
            
            # 风险检查
            if self.risk_check_enabled:
                risk_result = await self.risk_manager.check_order_risk(order)
                if not risk_result.approved:
                    raise ValueError(f"订单风险检查失败: {risk_result.reason}")
            
            # 创建订单请求
            request_id = str(uuid.uuid4())
            order_request = OrderRequest(
                order=order,
                priority=priority,
                callback=callback
            )
            
            # 添加到队列
            self.pending_orders[request_id] = order_request
            await self.priority_queue.put((priority.value, time.time(), request_id))
            
            # 注册回调
            if callback:
                self.order_callbacks[order.id] = callback
            
            logger.info(f"📝 订单已提交: {order.id} (请求ID: {request_id})")
            return request_id
            
        except Exception as e:
            logger.error(f"❌ 订单提交失败: {e}")
            raise
    
    async def cancel_order(self, order_id: str) -> bool:
        """
        取消订单
        
        Args:
            order_id: 订单ID
            
        Returns:
            bool: 取消成功返回True
        """
        try:
            # 检查活跃订单
            if order_id in self.active_orders:
                order = self.active_orders[order_id]
                success = await self.order_manager.cancel_order(order_id)
                
                if success:
                    order.status = OrderStatus.CANCELLED
                    self.completed_orders[order_id] = order
                    del self.active_orders[order_id]
                    
                    logger.info(f"✅ 订单已取消: {order_id}")
                    
                    # 执行回调
                    await self._execute_order_callback(order_id, 'cancelled', order)
                    
                return success
            
            # 检查待处理订单
            for request_id, request in list(self.pending_orders.items()):
                if request.order.id == order_id:
                    del self.pending_orders[request_id]
                    logger.info(f"✅ 待处理订单已取消: {order_id}")
                    return True
            
            logger.warning(f"⚠️ 未找到订单: {order_id}")
            return False
            
        except Exception as e:
            logger.error(f"❌ 取消订单失败 {order_id}: {e}")
            return False
    
    async def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """
        获取订单状态
        
        Args:
            order_id: 订单ID
            
        Returns:
            Dict: 订单状态信息
        """
        try:
            # 查找订单
            order = None
            status = "unknown"
            
            if order_id in self.active_orders:
                order = self.active_orders[order_id]
                status = "active"
            elif order_id in self.completed_orders:
                order = self.completed_orders[order_id]
                status = "completed"
            else:
                # 在待处理订单中查找
                for request in self.pending_orders.values():
                    if request.order.id == order_id:
                        order = request.order
                        status = "pending"
                        break
            
            if order:
                return {
                    'order_id': order.id,
                    'symbol': order.symbol,
                    'side': order.side.value,
                    'quantity': order.quantity,
                    'price': order.price,
                    'status': order.status.value,
                    'engine_status': status,
                    'timestamp': order.timestamp.isoformat() if order.timestamp else None
                }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 获取订单状态失败 {order_id}: {e}")
            return None
    
    def get_engine_metrics(self) -> Dict[str, Any]:
        """
        获取引擎性能指标
        
        Returns:
            Dict: 性能指标
        """
        try:
            # 更新指标
            self._update_metrics()
            
            return {
                'state': self.state.value,
                'uptime_seconds': self.metrics.uptime_seconds,
                'orders_per_second': self.metrics.orders_per_second,
                'average_latency_ms': self.metrics.average_latency_ms,
                'fill_rate': self.metrics.fill_rate,
                'error_rate': self.metrics.error_rate,
                'active_orders': len(self.active_orders),
                'pending_orders': len(self.pending_orders),
                'completed_orders': len(self.completed_orders),
                'total_trades': len(self.trades),
                'total_volume': self.metrics.total_volume,
                'memory_usage_mb': self.metrics.memory_usage_mb,
                'cpu_usage_percent': self.metrics.cpu_usage_percent,
                'last_updated': self.metrics.last_updated.isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 获取引擎指标失败: {e}")
            return {}
    
    async def _initialize_components(self):
        """初始化核心组件"""
        try:
            # 初始化订单管理器
            await self.order_manager.initialize()
            
            # 初始化市场数据源
            await self.market_data_feed.initialize()
            
            # 初始化账户管理器
            await self.account_manager.initialize()
            
            # 设置事件回调
            self.market_data_feed.subscribe_to_fills(self._on_order_fill)
            self.market_data_feed.subscribe_to_market_data(self._on_market_data)
            
            logger.info("✅ 核心组件初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 组件初始化失败: {e}")
            raise
    
    async def _start_background_tasks(self):
        """启动后台任务"""
        try:
            # 订单处理任务
            task = asyncio.create_task(self._order_processing_loop())
            self.running_tasks.add(task)
            
            # 性能监控任务
            if self.performance_monitoring:
                task = asyncio.create_task(self._performance_monitoring_loop())
                self.running_tasks.add(task)
            
            # 事件处理任务
            task = asyncio.create_task(self._event_processing_loop())
            self.running_tasks.add(task)
            
            # 健康检查任务
            task = asyncio.create_task(self._health_check_loop())
            self.running_tasks.add(task)
            
            logger.info(f"✅ 启动了 {len(self.running_tasks)} 个后台任务")
            
        except Exception as e:
            logger.error(f"❌ 启动后台任务失败: {e}")
            raise
    
    async def _order_processing_loop(self):
        """订单处理循环"""
        while not self._shutdown_event.is_set():
            try:
                # 从优先级队列获取订单
                if not self.priority_queue.empty():
                    _, timestamp, request_id = await asyncio.wait_for(
                        self.priority_queue.get(), timeout=1.0
                    )
                    
                    if request_id in self.pending_orders:
                        request = self.pending_orders[request_id]
                        await self._process_order_request(request_id, request)
                
                else:
                    await asyncio.sleep(0.01)  # 短暂休息
                    
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"❌ 订单处理循环错误: {e}")
                await asyncio.sleep(1)
    
    async def _process_order_request(self, request_id: str, request: OrderRequest):
        """处理订单请求"""
        try:
            order = request.order
            
            # 检查超时
            if datetime.now() - request.created_at > timedelta(seconds=request.timeout_seconds):
                logger.warning(f"⚠️ 订单请求超时: {order.id}")
                del self.pending_orders[request_id]
                return
            
            # 提交订单到经纪商
            order_id = await self.order_manager.submit_order(order)
            
            if order_id:
                # 移动到活跃订单
                order.status = OrderStatus.PENDING
                self.active_orders[order.id] = order
                del self.pending_orders[request_id]
                
                logger.info(f"✅ 订单已提交到经纪商: {order.id}")
                
                # 执行回调
                await self._execute_order_callback(order.id, 'submitted', order)
                
            else:
                # 重试或失败
                request.attempts += 1
                if request.attempts >= request.max_attempts:
                    logger.error(f"❌ 订单提交最终失败: {order.id}")
                    del self.pending_orders[request_id]
                    await self._execute_order_callback(order.id, 'failed', order)
                else:
                    # 重新排队
                    await self.priority_queue.put((
                        request.priority.value, 
                        time.time(), 
                        request_id
                    ))
                    
        except Exception as e:
            logger.error(f"❌ 处理订单请求失败 {request_id}: {e}")
    
    async def _performance_monitoring_loop(self):
        """性能监控循环"""
        while not self._shutdown_event.is_set():
            try:
                # 更新性能指标
                self._update_metrics()
                
                # 保存历史记录（每分钟）
                self.performance_history.append(self.metrics)
                if len(self.performance_history) > 1440:  # 保留24小时
                    self.performance_history.pop(0)
                
                # 检查性能阈值
                await self._check_performance_thresholds()
                
                await asyncio.sleep(60)  # 每分钟更新
                
            except Exception as e:
                logger.error(f"❌ 性能监控循环错误: {e}")
                await asyncio.sleep(60)
    
    async def _event_processing_loop(self):
        """事件处理循环"""
        while not self._shutdown_event.is_set():
            try:
                if not self.event_queue.empty():
                    event = await asyncio.wait_for(
                        self.event_queue.get(), timeout=1.0
                    )
                    await self._handle_event(event)
                else:
                    await asyncio.sleep(0.01)
                    
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"❌ 事件处理循环错误: {e}")
                await asyncio.sleep(1)
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while not self._shutdown_event.is_set():
            try:
                # 检查组件健康状态
                await self._check_component_health()
                
                # 清理过期数据
                await self._cleanup_expired_data()
                
                await asyncio.sleep(30)  # 每30秒检查
                
            except Exception as e:
                logger.error(f"❌ 健康检查循环错误: {e}")
                await asyncio.sleep(30)
    
    async def _on_order_fill(self, fill_data: Dict[str, Any]):
        """处理订单成交事件"""
        try:
            order_id = fill_data.get('order_id')
            if order_id and order_id in self.active_orders:
                order = self.active_orders[order_id]
                
                # 创建成交记录
                fill = OrderFill(
                    id=str(uuid.uuid4()),
                    order_id=order_id,
                    symbol=order.symbol,
                    side=order.side,
                    quantity=fill_data.get('quantity', 0),
                    price=fill_data.get('price', 0),
                    timestamp=datetime.now(),
                    commission=fill_data.get('commission', 0),
                    strategy_id=order.strategy_id
                )
                
                # 更新订单状态
                if fill_data.get('is_complete', True):
                    order.status = OrderStatus.FILLED
                    self.completed_orders[order_id] = order
                    del self.active_orders[order_id]
                else:
                    order.status = OrderStatus.PARTIALLY_FILLED
                
                # 保存记录
                self.fills.append(fill)
                
                # 创建交易记录
                trade = Trade(
                    id=str(uuid.uuid4()),
                    symbol=fill.symbol,
                    side=fill.side,
                    quantity=fill.quantity,
                    price=fill.price,
                    timestamp=fill.timestamp,
                    commission=fill.commission,
                    strategy_id=fill.strategy_id,
                    order_id=order_id
                )
                
                self.trades.append(trade)
                
                logger.info(f"💰 订单成交: {order_id}, 数量: {fill.quantity}, 价格: {fill.price}")
                
                # 执行回调
                await self._execute_order_callback(order_id, 'filled', order, fill)
                
        except Exception as e:
            logger.error(f"❌ 处理订单成交事件失败: {e}")
    
    async def _on_market_data(self, market_data: MarketData):
        """处理市场数据事件"""
        try:
            # 添加到事件队列
            await self.event_queue.put({
                'type': 'market_data',
                'data': market_data,
                'timestamp': datetime.now()
            })
            
        except Exception as e:
            logger.error(f"❌ 处理市场数据事件失败: {e}")
    
    async def _handle_event(self, event: Dict[str, Any]):
        """处理通用事件"""
        try:
            event_type = event.get('type')
            
            # 执行注册的回调函数
            if event_type in self.event_callbacks:
                for callback in self.event_callbacks[event_type]:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(event)
                        else:
                            callback(event)
                    except Exception as e:
                        logger.error(f"❌ 事件回调执行失败: {e}")
                        
        except Exception as e:
            logger.error(f"❌ 事件处理失败: {e}")
    
    async def _execute_order_callback(self, order_id: str, event_type: str, 
                                    order: Order, fill: Optional[OrderFill] = None):
        """执行订单回调"""
        try:
            if order_id in self.order_callbacks:
                callback = self.order_callbacks[order_id]
                callback_data = {
                    'order_id': order_id,
                    'event_type': event_type,
                    'order': order,
                    'fill': fill,
                    'timestamp': datetime.now()
                }
                
                if asyncio.iscoroutinefunction(callback):
                    await callback(callback_data)
                else:
                    callback(callback_data)
                    
                # 如果订单完成，移除回调
                if event_type in ['filled', 'cancelled', 'failed']:
                    del self.order_callbacks[order_id]
                    
        except Exception as e:
            logger.error(f"❌ 执行订单回调失败 {order_id}: {e}")
    
    def _update_metrics(self):
        """更新性能指标"""
        try:
            current_time = datetime.now()
            
            # 计算运行时间
            if self.start_time:
                self.metrics.uptime_seconds = (current_time - self.start_time).total_seconds()
            
            # 计算订单速率（简化计算）
            if self.metrics.uptime_seconds > 0:
                total_orders = len(self.completed_orders) + len(self.active_orders)
                self.metrics.orders_per_second = total_orders / self.metrics.uptime_seconds
            
            # 计算成交率
            if len(self.completed_orders) > 0:
                filled_orders = sum(1 for order in self.completed_orders.values() 
                                  if order.status == OrderStatus.FILLED)
                self.metrics.fill_rate = filled_orders / len(self.completed_orders)
            
            # 计算总交易量
            self.metrics.total_volume = sum(trade.quantity * trade.price for trade in self.trades)
            
            # 更新活跃订单数
            self.metrics.active_orders = len(self.active_orders)
            
            # 更新时间戳
            self.metrics.last_updated = current_time
            
        except Exception as e:
            logger.error(f"❌ 更新性能指标失败: {e}")
    
    async def _check_performance_thresholds(self):
        """检查性能阈值"""
        try:
            # 检查延迟阈值
            if self.metrics.average_latency_ms > 1000:  # 1秒
                logger.warning(f"⚠️ 高延迟告警: {self.metrics.average_latency_ms:.2f}ms")
            
            # 检查错误率阈值
            if self.metrics.error_rate > 0.05:  # 5%
                logger.warning(f"⚠️ 高错误率告警: {self.metrics.error_rate:.2%}")
            
            # 检查内存使用
            if self.metrics.memory_usage_mb > 1000:  # 1GB
                logger.warning(f"⚠️ 高内存使用告警: {self.metrics.memory_usage_mb:.2f}MB")
                
        except Exception as e:
            logger.error(f"❌ 检查性能阈值失败: {e}")
    
    async def _check_component_health(self):
        """检查组件健康状态"""
        try:
            # 检查订单管理器
            if hasattr(self.order_manager, 'health_check'):
                await self.order_manager.health_check()
            
            # 检查市场数据源
            if hasattr(self.market_data_feed, 'health_check'):
                await self.market_data_feed.health_check()
            
            # 检查账户管理器
            if hasattr(self.account_manager, 'health_check'):
                await self.account_manager.health_check()
                
        except Exception as e:
            logger.warning(f"⚠️ 组件健康检查失败: {e}")
    
    async def _cleanup_expired_data(self):
        """清理过期数据"""
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(hours=24)
            
            # 清理过期的订单历史
            self.order_history = [
                order for order in self.order_history
                if order.timestamp and order.timestamp > cutoff_time
            ]
            
            # 清理过期的性能历史
            self.performance_history = [
                metrics for metrics in self.performance_history
                if metrics.last_updated > cutoff_time
            ]
            
        except Exception as e:
            logger.error(f"❌ 清理过期数据失败: {e}")
    
    async def _wait_for_tasks_completion(self):
        """等待所有任务完成"""
        try:
            if self.running_tasks:
                await asyncio.wait(self.running_tasks, timeout=30)
                
        except Exception as e:
            logger.error(f"❌ 等待任务完成失败: {e}")
    
    async def _cancel_pending_orders(self):
        """取消所有待处理订单"""
        try:
            for request_id in list(self.pending_orders.keys()):
                order = self.pending_orders[request_id].order
                order.status = OrderStatus.CANCELLED
                await self._execute_order_callback(order.id, 'cancelled', order)
                
            self.pending_orders.clear()
            logger.info(f"✅ 已取消所有待处理订单")
            
        except Exception as e:
            logger.error(f"❌ 取消待处理订单失败: {e}")
    
    async def _shutdown_components(self):
        """关闭组件"""
        try:
            # 关闭订单管理器
            if hasattr(self.order_manager, 'shutdown'):
                await self.order_manager.shutdown()
            
            # 关闭市场数据源
            if hasattr(self.market_data_feed, 'shutdown'):
                await self.market_data_feed.shutdown()
            
            # 关闭账户管理器
            if hasattr(self.account_manager, 'shutdown'):
                await self.account_manager.shutdown()
            
            # 关闭线程池
            self.executor.shutdown(wait=True)
            
            logger.info("✅ 组件关闭完成")
            
        except Exception as e:
            logger.error(f"❌ 关闭组件失败: {e}")
    
    # 事件订阅接口
    def subscribe_to_event(self, event_type: str, callback: Callable):
        """订阅事件"""
        if event_type not in self.event_callbacks:
            self.event_callbacks[event_type] = []
        self.event_callbacks[event_type].append(callback)
    
    def unsubscribe_from_event(self, event_type: str, callback: Callable):
        """取消事件订阅"""
        if event_type in self.event_callbacks:
            if callback in self.event_callbacks[event_type]:
                self.event_callbacks[event_type].remove(callback)