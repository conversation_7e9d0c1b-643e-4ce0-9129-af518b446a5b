#!/usr/bin/env python3
"""
统一监控和健康检查系统
整合所有项目的监控需求，提供统一的系统健康监测
"""

import os
import sys
import time
import psutil
import threading
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, deque

from src.common.utils.logging import get_logger
from src.core.engine.unified_config import get_config_manager
from src.core.engine.unified_error_handler import get_error_handler, ErrorSeverity, ErrorCategory


class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class MetricType(Enum):
    """指标类型枚举"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class HealthCheck:
    """健康检查项"""
    name: str
    description: str
    check_function: Callable
    interval: int  # 检查间隔（秒）
    timeout: int   # 超时时间（秒）
    enabled: bool = True
    last_check: Optional[datetime] = None
    last_status: HealthStatus = HealthStatus.UNKNOWN
    last_result: Optional[Dict[str, Any]] = None
    failure_count: int = 0
    max_failures: int = 3


@dataclass
class SystemMetric:
    """系统指标"""
    name: str
    type: MetricType
    value: float
    timestamp: datetime
    labels: Dict[str, str] = None
    description: str = ""


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    condition: str  # 告警条件表达式
    severity: ErrorSeverity
    message_template: str
    enabled: bool = True
    cooldown: int = 3600  # 冷却时间（秒）
    last_triggered: Optional[datetime] = None


class UnifiedMonitoringSystem:
    """
    统一监控和健康检查系统
    
    功能：
    - 系统健康检查
    - 性能指标收集
    - 告警管理
    - 服务状态监控
    - 资源使用监控
    """
    
    def __init__(self):
        self.logger = get_logger("unified_monitoring")
        self.config_manager = get_config_manager()
        self.error_handler = get_error_handler()
        
        # 监控状态
        self._running = False
        self._monitor_thread = None
        
        # 健康检查
        self.health_checks: Dict[str, HealthCheck] = {}
        
        # 指标收集
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.metric_handlers: Dict[str, List[Callable]] = defaultdict(list)
        
        # 告警规则
        self.alert_rules: Dict[str, AlertRule] = {}
        
        # 系统信息缓存
        self._system_info_cache = {}
        self._cache_update_time = None
        self._cache_ttl = 60  # 缓存TTL（秒）
        
        # 初始化
        self._initialize_default_checks()
        self._initialize_default_alerts()
        
        self.logger.info("🔍 统一监控系统初始化完成")
    
    def _initialize_default_checks(self):
        """初始化默认健康检查"""
        # CPU使用率检查
        self.register_health_check(
            "cpu_usage",
            "CPU使用率检查",
            self._check_cpu_usage,
            interval=30,
            timeout=5
        )
        
        # 内存使用检查
        self.register_health_check(
            "memory_usage",
            "内存使用率检查",
            self._check_memory_usage,
            interval=30,
            timeout=5
        )
        
        # 磁盘空间检查
        self.register_health_check(
            "disk_space",
            "磁盘空间检查",
            self._check_disk_space,
            interval=60,
            timeout=10
        )
        
        # 网络连接检查
        self.register_health_check(
            "network_connectivity",
            "网络连接检查",
            self._check_network_connectivity,
            interval=120,
            timeout=10
        )
        
        # 数据库连接检查
        self.register_health_check(
            "database_connection",
            "数据库连接检查",
            self._check_database_connection,
            interval=60,
            timeout=10
        )
        
        # 配置文件完整性检查
        self.register_health_check(
            "config_integrity",
            "配置文件完整性检查",
            self._check_config_integrity,
            interval=300,
            timeout=15
        )
    
    def _initialize_default_alerts(self):
        """初始化默认告警规则"""
        # CPU使用率告警
        self.register_alert_rule(
            "high_cpu_usage",
            "cpu_usage > 80",
            ErrorSeverity.HIGH,
            "CPU使用率过高: {cpu_usage:.1f}%"
        )
        
        # 内存使用告警
        self.register_alert_rule(
            "high_memory_usage",
            "memory_usage > 85",
            ErrorSeverity.HIGH,
            "内存使用率过高: {memory_usage:.1f}%"
        )
        
        # 磁盘空间告警
        self.register_alert_rule(
            "low_disk_space",
            "disk_usage > 90",
            ErrorSeverity.CRITICAL,
            "磁盘空间不足: {disk_usage:.1f}%"
        )
        
        # 错误率告警
        self.register_alert_rule(
            "high_error_rate",
            "error_rate > 10",
            ErrorSeverity.MEDIUM,
            "错误率过高: {error_rate:.1f}/min"
        )
    
    def register_health_check(self, 
                            name: str, 
                            description: str,
                            check_function: Callable,
                            interval: int = 60,
                            timeout: int = 30,
                            max_failures: int = 3):
        """注册健康检查"""
        health_check = HealthCheck(
            name=name,
            description=description,
            check_function=check_function,
            interval=interval,
            timeout=timeout,
            max_failures=max_failures
        )
        
        self.health_checks[name] = health_check
        self.logger.debug(f"已注册健康检查: {name}")
    
    def register_alert_rule(self,
                          name: str,
                          condition: str,
                          severity: ErrorSeverity,
                          message_template: str,
                          cooldown: int = 3600):
        """注册告警规则"""
        alert_rule = AlertRule(
            name=name,
            condition=condition,
            severity=severity,
            message_template=message_template,
            cooldown=cooldown
        )
        
        self.alert_rules[name] = alert_rule
        self.logger.debug(f"已注册告警规则: {name}")
    
    def record_metric(self,
                     name: str,
                     value: float,
                     metric_type: MetricType = MetricType.GAUGE,
                     labels: Dict[str, str] = None,
                     description: str = ""):
        """记录指标"""
        metric = SystemMetric(
            name=name,
            type=metric_type,
            value=value,
            timestamp=datetime.now(),
            labels=labels or {},
            description=description
        )
        
        # 存储指标
        self.metrics[name].append(metric)
        
        # 调用指标处理器
        for handler in self.metric_handlers[name]:
            try:
                handler(metric)
            except Exception as e:
                self.logger.warning(f"指标处理器执行失败: {e}")
        
        # 检查告警规则
        self._check_alert_rules(name, value, labels or {})
    
    def register_metric_handler(self, metric_name: str, handler: Callable):
        """注册指标处理器"""
        self.metric_handlers[metric_name].append(handler)
        self.logger.debug(f"已注册指标处理器: {metric_name}")
    
    def _check_alert_rules(self, metric_name: str, value: float, labels: Dict[str, str]):
        """检查告警规则"""
        current_time = datetime.now()
        
        # 构建评估上下文
        context = {
            metric_name: value,
            **labels,
            'timestamp': current_time
        }
        
        for rule_name, rule in self.alert_rules.items():
            if not rule.enabled:
                continue
                
            # 检查冷却时间
            if rule.last_triggered:
                if (current_time - rule.last_triggered).total_seconds() < rule.cooldown:
                    continue
            
            try:
                # 评估告警条件
                if self._evaluate_condition(rule.condition, context):
                    # 触发告警
                    self._trigger_alert(rule, context)
                    rule.last_triggered = current_time
                    
            except Exception as e:
                self.logger.warning(f"告警规则评估失败: {rule_name} - {e}")
    
    def _evaluate_condition(self, condition: str, context: Dict[str, Any]) -> bool:
        """评估告警条件"""
        try:
            # 简单的条件评估，支持基本的比较操作
            # 安全起见，只允许特定的操作
            allowed_names = {
                **context,
                'abs': abs,
                'max': max,
                'min': min,
                'len': len
            }
            
            # 替换常见的变量名
            condition = condition.replace('cpu_usage', str(context.get('cpu_usage', 0)))
            condition = condition.replace('memory_usage', str(context.get('memory_usage', 0)))
            condition = condition.replace('disk_usage', str(context.get('disk_usage', 0)))
            condition = condition.replace('error_rate', str(context.get('error_rate', 0)))
            
            return eval(condition, {"__builtins__": {}}, allowed_names)
            
        except Exception as e:
            self.logger.warning(f"条件评估失败: {condition} - {e}")
            return False
    
    def _trigger_alert(self, rule: AlertRule, context: Dict[str, Any]):
        """触发告警"""
        try:
            # 格式化告警消息
            message = rule.message_template.format(**context)
            
            # 通过错误处理器记录告警
            alert_exception = Exception(f"ALERT: {message}")
            
            self.error_handler.handle_error(
                alert_exception,
                context=context,
                component="monitoring_system",
                operation="alert",
                severity=rule.severity
            )
            
            self.logger.warning(f"告警触发: {rule.name} - {message}")
            
        except Exception as e:
            self.logger.error(f"告警触发失败: {rule.name} - {e}")
    
    # 默认健康检查方法
    def _check_cpu_usage(self) -> Dict[str, Any]:
        """检查CPU使用率"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            status = HealthStatus.HEALTHY
            
            if cpu_percent > 90:
                status = HealthStatus.CRITICAL
            elif cpu_percent > 80:
                status = HealthStatus.WARNING
            
            # 记录指标
            self.record_metric("cpu_usage", cpu_percent, MetricType.GAUGE, description="CPU使用率")
            
            return {
                "status": status,
                "cpu_usage": cpu_percent,
                "message": f"CPU使用率: {cpu_percent:.1f}%"
            }
            
        except Exception as e:
            self.logger.error(f"CPU检查失败: {e}")
            return {
                "status": HealthStatus.UNKNOWN,
                "message": f"CPU检查失败: {e}"
            }
    
    def _check_memory_usage(self) -> Dict[str, Any]:
        """检查内存使用"""
        try:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            status = HealthStatus.HEALTHY
            
            if memory_percent > 90:
                status = HealthStatus.CRITICAL
            elif memory_percent > 85:
                status = HealthStatus.WARNING
            
            # 记录指标
            self.record_metric("memory_usage", memory_percent, MetricType.GAUGE, description="内存使用率")
            self.record_metric("memory_total", memory.total / (1024**3), MetricType.GAUGE, description="总内存(GB)")
            self.record_metric("memory_available", memory.available / (1024**3), MetricType.GAUGE, description="可用内存(GB)")
            
            return {
                "status": status,
                "memory_usage": memory_percent,
                "memory_total_gb": memory.total / (1024**3),
                "memory_available_gb": memory.available / (1024**3),
                "message": f"内存使用率: {memory_percent:.1f}%"
            }
            
        except Exception as e:
            self.logger.error(f"内存检查失败: {e}")
            return {
                "status": HealthStatus.UNKNOWN,
                "message": f"内存检查失败: {e}"
            }
    
    def _check_disk_space(self) -> Dict[str, Any]:
        """检查磁盘空间"""
        try:
            current_path = Path.cwd()
            disk_usage = psutil.disk_usage(current_path)
            usage_percent = (disk_usage.used / disk_usage.total) * 100
            status = HealthStatus.HEALTHY
            
            if usage_percent > 95:
                status = HealthStatus.CRITICAL
            elif usage_percent > 90:
                status = HealthStatus.WARNING
            
            # 记录指标
            self.record_metric("disk_usage", usage_percent, MetricType.GAUGE, description="磁盘使用率")
            self.record_metric("disk_total", disk_usage.total / (1024**3), MetricType.GAUGE, description="磁盘总容量(GB)")
            self.record_metric("disk_free", disk_usage.free / (1024**3), MetricType.GAUGE, description="磁盘可用空间(GB)")
            
            return {
                "status": status,
                "disk_usage": usage_percent,
                "disk_total_gb": disk_usage.total / (1024**3),
                "disk_free_gb": disk_usage.free / (1024**3),
                "message": f"磁盘使用率: {usage_percent:.1f}%"
            }
            
        except Exception as e:
            self.logger.error(f"磁盘检查失败: {e}")
            return {
                "status": HealthStatus.UNKNOWN,
                "message": f"磁盘检查失败: {e}"
            }
    
    def _check_network_connectivity(self) -> Dict[str, Any]:
        """检查网络连接"""
        try:
            import socket
            
            # 测试DNS解析
            socket.gethostbyname('google.com')
            
            # 记录指标
            self.record_metric("network_status", 1, MetricType.GAUGE, description="网络连接状态(1=正常,0=异常)")
            
            return {
                "status": HealthStatus.HEALTHY,
                "message": "网络连接正常"
            }
            
        except Exception as e:
            self.record_metric("network_status", 0, MetricType.GAUGE, description="网络连接状态(1=正常,0=异常)")
            return {
                "status": HealthStatus.WARNING,
                "message": f"网络连接异常: {e}"
            }
    
    def _check_database_connection(self) -> Dict[str, Any]:
        """检查数据库连接"""
        try:
            # 这里应该根据实际使用的数据库类型进行连接测试
            # 暂时返回健康状态
            
            # 记录指标
            self.record_metric("database_status", 1, MetricType.GAUGE, description="数据库连接状态(1=正常,0=异常)")
            
            return {
                "status": HealthStatus.HEALTHY,
                "message": "数据库连接正常"
            }
            
        except Exception as e:
            self.record_metric("database_status", 0, MetricType.GAUGE, description="数据库连接状态(1=正常,0=异常)")
            return {
                "status": HealthStatus.WARNING,
                "message": f"数据库连接异常: {e}"
            }
    
    def _check_config_integrity(self) -> Dict[str, Any]:
        """检查配置文件完整性"""
        try:
            # 验证配置完整性
            config_errors = self.config_manager.validate_config()
            
            if config_errors:
                error_count = sum(len(errors) for errors in config_errors.values())
                self.record_metric("config_errors", error_count, MetricType.GAUGE, description="配置错误数量")
                
                return {
                    "status": HealthStatus.WARNING,
                    "config_errors": config_errors,
                    "error_count": error_count,
                    "message": f"发现 {error_count} 个配置错误"
                }
            else:
                self.record_metric("config_errors", 0, MetricType.GAUGE, description="配置错误数量")
                
                return {
                    "status": HealthStatus.HEALTHY,
                    "message": "配置文件完整性检查通过"
                }
                
        except Exception as e:
            self.logger.error(f"配置检查失败: {e}")
            return {
                "status": HealthStatus.UNKNOWN,
                "message": f"配置检查失败: {e}"
            }
    
    def run_health_check(self, check_name: str) -> Dict[str, Any]:
        """运行单个健康检查"""
        if check_name not in self.health_checks:
            return {
                "status": HealthStatus.UNKNOWN,
                "message": f"健康检查不存在: {check_name}"
            }
        
        health_check = self.health_checks[check_name]
        
        if not health_check.enabled:
            return {
                "status": HealthStatus.UNKNOWN,
                "message": f"健康检查已禁用: {check_name}"
            }
        
        try:
            # 运行健康检查
            result = health_check.check_function()
            
            # 更新健康检查状态
            health_check.last_check = datetime.now()
            health_check.last_status = result.get("status", HealthStatus.UNKNOWN)
            health_check.last_result = result
            
            # 更新失败计数
            if health_check.last_status in [HealthStatus.WARNING, HealthStatus.CRITICAL]:
                health_check.failure_count += 1
            else:
                health_check.failure_count = 0
            
            return result
            
        except Exception as e:
            health_check.failure_count += 1
            self.logger.error(f"健康检查执行失败: {check_name} - {e}")
            
            return {
                "status": HealthStatus.CRITICAL,
                "message": f"健康检查执行失败: {e}"
            }
    
    def run_all_health_checks(self) -> Dict[str, Dict[str, Any]]:
        """运行所有健康检查"""
        results = {}
        
        for check_name in self.health_checks:
            results[check_name] = self.run_health_check(check_name)
        
        return results
    
    def get_overall_health_status(self) -> Dict[str, Any]:
        """获取整体健康状态"""
        health_results = self.run_all_health_checks()
        
        # 统计各种状态
        status_counts = {status.value: 0 for status in HealthStatus}
        
        for result in health_results.values():
            status = result.get("status", HealthStatus.UNKNOWN)
            if isinstance(status, HealthStatus):
                status_counts[status.value] += 1
            else:
                status_counts[status] += 1
        
        # 确定整体状态
        overall_status = HealthStatus.HEALTHY
        if status_counts["critical"] > 0:
            overall_status = HealthStatus.CRITICAL
        elif status_counts["warning"] > 0:
            overall_status = HealthStatus.WARNING
        elif status_counts["unknown"] > 0:
            overall_status = HealthStatus.WARNING
        
        return {
            "overall_status": overall_status,
            "status_counts": status_counts,
            "health_checks": health_results,
            "timestamp": datetime.now().isoformat(),
            "uptime": self._get_system_uptime()
        }
    
    def _get_system_uptime(self) -> float:
        """获取系统运行时间"""
        try:
            return time.time() - psutil.boot_time()
        except:
            return 0.0
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标摘要"""
        metrics_summary = {}
        
        for metric_name, metric_list in self.metrics.items():
            if not metric_list:
                continue
                
            latest_metric = metric_list[-1]
            values = [m.value for m in metric_list]
            
            metrics_summary[metric_name] = {
                "current_value": latest_metric.value,
                "timestamp": latest_metric.timestamp.isoformat(),
                "description": latest_metric.description,
                "type": latest_metric.type.value,
                "statistics": {
                    "min": min(values),
                    "max": max(values),
                    "avg": sum(values) / len(values),
                    "count": len(values)
                }
            }
        
        return metrics_summary
    
    def start_monitoring(self):
        """启动监控"""
        if self._running:
            self.logger.warning("监控系统已在运行")
            return
        
        self._running = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        self.logger.info("✅ 监控系统已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self._running = False
        
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5)
        
        self.logger.info("🛑 监控系统已停止")
    
    def _monitor_loop(self):
        """监控主循环"""
        self.logger.info("监控主循环开始")
        
        while self._running:
            try:
                current_time = datetime.now()
                
                # 检查需要运行的健康检查
                for check_name, health_check in self.health_checks.items():
                    if not health_check.enabled:
                        continue
                    
                    # 检查是否到了执行时间
                    if (health_check.last_check is None or 
                        (current_time - health_check.last_check).total_seconds() >= health_check.interval):
                        
                        # 在后台执行健康检查
                        threading.Thread(
                            target=self.run_health_check,
                            args=(check_name,),
                            daemon=True
                        ).start()
                
                # 休眠一段时间
                time.sleep(10)
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(10)
        
        self.logger.info("监控主循环结束")


# 全局监控系统实例
_monitoring_instance = None


def get_monitoring_system() -> UnifiedMonitoringSystem:
    """获取全局监控系统实例（单例模式）"""
    global _monitoring_instance
    if _monitoring_instance is None:
        _monitoring_instance = UnifiedMonitoringSystem()
    return _monitoring_instance