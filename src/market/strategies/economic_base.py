"""
宏观经济策略基础类

扩展策略框架以支持经济数据输入和宏观经济分析
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import logging

try:
    from .base import BaseStrategy, StrategyContext
    from .signals import Signal
    from .parameters import StrategyParameter, ParameterType, ParameterValidator
    from ..models.market_data import MarketData, EconomicData
    from ..data.adapters.fred_adapter import FREDAdapter
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from src.market.strategies.base import BaseStrategy, StrategyContext
    from src.market.strategies.signals import Signal
    from src.market.strategies.parameters import StrategyParameter, ParameterType, ParameterValidator
    from src.market.strategies.models.market_data import MarketData, EconomicData
    from data.adapters.fred_adapter import FREDAdapter


class EconomicContext(StrategyContext):
    """
    扩展的策略上下文，支持经济数据访问
    """
    
    def __init__(self, data_manager=None, indicator_engine=None, portfolio=None, 
                 economic_data_manager=None):
        super().__init__(data_manager, indicator_engine, portfolio)
        self.economic_data_manager = economic_data_manager
        self.logger = logging.getLogger(f"{__name__}.EconomicContext")
        
        # 经济数据缓存
        self._economic_data_cache = {}
        self._cache_expiry = {}
        
    def get_economic_data(self, series_id: str, lookback_days: int = 365,
                         end_date: Optional[datetime] = None) -> pd.DataFrame:
        """
        获取经济数据
        
        Args:
            series_id: 经济数据序列ID (如 'GDP', 'UNRATE', 'FEDFUNDS')
            lookback_days: 回看天数
            end_date: 结束日期，默认为当前时间
            
        Returns:
            经济数据DataFrame，包含timestamp和value列
        """
        if not self.economic_data_manager:
            raise ValueError("经济数据管理器未初始化")
        
        # 检查缓存
        cache_key = f"{series_id}_{lookback_days}_{end_date}"
        if cache_key in self._economic_data_cache:
            cache_time = self._cache_expiry.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=1):  # 缓存1小时
                return self._economic_data_cache[cache_key]
        
        try:
            if end_date is None:
                end_date = datetime.now()
            start_date = end_date - timedelta(days=lookback_days)
            
            economic_data_list = self.economic_data_manager.get_economic_data(
                series_id=series_id,
                start_date=start_date,
                end_date=end_date
            )
            
            # 转换为DataFrame
            if economic_data_list:
                df = pd.DataFrame([
                    {
                        'timestamp': data.timestamp,
                        'value': data.value,
                        'series_id': data.series_id,
                        'unit': data.unit,
                        'frequency': data.frequency
                    }
                    for data in economic_data_list
                ])
                df.set_index('timestamp', inplace=True)
                df.sort_index(inplace=True)
            else:
                df = pd.DataFrame(columns=['value', 'series_id', 'unit', 'frequency'])
            
            # 缓存结果
            self._economic_data_cache[cache_key] = df
            self._cache_expiry[cache_key] = datetime.now()
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取经济数据失败: {series_id}, {e}")
            return pd.DataFrame()
    
    def get_multiple_economic_data(self, series_ids: List[str], 
                                  lookback_days: int = 365,
                                  end_date: Optional[datetime] = None) -> Dict[str, pd.DataFrame]:
        """
        获取多个经济数据序列
        
        Args:
            series_ids: 经济数据序列ID列表
            lookback_days: 回看天数
            end_date: 结束日期
            
        Returns:
            序列ID到DataFrame的映射
        """
        results = {}
        for series_id in series_ids:
            try:
                data = self.get_economic_data(series_id, lookback_days, end_date)
                results[series_id] = data
            except Exception as e:
                self.logger.error(f"获取经济数据失败 {series_id}: {e}")
                results[series_id] = pd.DataFrame()
        
        return results
    
    def get_economic_indicator_correlation(self, series_id1: str, series_id2: str,
                                        lookback_days: int = 365) -> float:
        """
        计算两个经济指标的相关性
        
        Args:
            series_id1: 第一个经济指标ID
            series_id2: 第二个经济指标ID
            lookback_days: 回看天数
            
        Returns:
            相关系数 (-1到1之间)
        """
        try:
            data1 = self.get_economic_data(series_id1, lookback_days)
            data2 = self.get_economic_data(series_id2, lookback_days)
            
            if data1.empty or data2.empty:
                return 0.0
            
            # 对齐时间序列
            combined = pd.merge(data1[['value']], data2[['value']], 
                              left_index=True, right_index=True, 
                              suffixes=('_1', '_2'), how='inner')
            
            if len(combined) < 2:
                return 0.0
            
            correlation = combined['value_1'].corr(combined['value_2'])
            return correlation if not pd.isna(correlation) else 0.0
            
        except Exception as e:
            self.logger.error(f"计算相关性失败 {series_id1} vs {series_id2}: {e}")
            return 0.0
    
    def get_economic_trend(self, series_id: str, lookback_days: int = 90) -> str:
        """
        分析经济指标趋势
        
        Args:
            series_id: 经济指标ID
            lookback_days: 分析周期天数
            
        Returns:
            趋势描述: 'RISING', 'FALLING', 'STABLE', 'UNKNOWN'
        """
        try:
            data = self.get_economic_data(series_id, lookback_days)
            
            if data.empty or len(data) < 3:
                return 'UNKNOWN'
            
            values = data['value'].dropna()
            if len(values) < 3:
                return 'UNKNOWN'
            
            # 计算线性回归斜率
            x = np.arange(len(values))
            slope = np.polyfit(x, values, 1)[0]
            
            # 计算相对变化率
            relative_change = slope / values.mean() if values.mean() != 0 else 0
            
            # 判断趋势
            if abs(relative_change) < 0.001:  # 变化小于0.1%
                return 'STABLE'
            elif relative_change > 0:
                return 'RISING'
            else:
                return 'FALLING'
                
        except Exception as e:
            self.logger.error(f"分析趋势失败 {series_id}: {e}")
            return 'UNKNOWN'
    
    def get_economic_cycle_phase(self) -> str:
        """
        识别当前经济周期阶段
        
        基于GDP增长率、失业率、通胀率等关键指标判断经济周期
        
        Returns:
            经济周期阶段: 'EXPANSION', 'PEAK', 'CONTRACTION', 'TROUGH', 'UNKNOWN'
        """
        try:
            # 获取关键经济指标
            gdp_data = self.get_economic_data('GDPC1', 730)  # 2年GDP数据
            unemployment_data = self.get_economic_data('UNRATE', 365)  # 1年失业率
            inflation_data = self.get_economic_data('CPIAUCSL', 365)  # 1年CPI
            
            if gdp_data.empty or unemployment_data.empty or inflation_data.empty:
                return 'UNKNOWN'
            
            # 分析GDP趋势
            gdp_trend = self.get_economic_trend('GDPC1', 180)
            unemployment_trend = self.get_economic_trend('UNRATE', 90)
            
            # 计算通胀率
            if len(inflation_data) >= 12:
                current_cpi = inflation_data['value'].iloc[-1]
                year_ago_cpi = inflation_data['value'].iloc[-12] if len(inflation_data) >= 12 else inflation_data['value'].iloc[0]
                inflation_rate = (current_cpi - year_ago_cpi) / year_ago_cpi * 100
            else:
                inflation_rate = 0
            
            # 经济周期判断逻辑
            if gdp_trend == 'RISING' and unemployment_trend == 'FALLING':
                if inflation_rate < 3:
                    return 'EXPANSION'
                else:
                    return 'PEAK'
            elif gdp_trend == 'FALLING' and unemployment_trend == 'RISING':
                return 'CONTRACTION'
            elif gdp_trend == 'STABLE' and unemployment_trend == 'STABLE':
                if inflation_rate < 1:
                    return 'TROUGH'
                else:
                    return 'PEAK'
            else:
                return 'UNKNOWN'
                
        except Exception as e:
            self.logger.error(f"识别经济周期失败: {e}")
            return 'UNKNOWN'


class EconomicStrategy(BaseStrategy):
    """
    宏观经济策略基础类
    
    为基于经济数据的策略提供基础功能
    """
    
    def __init__(self, name: str, parameters: Optional[Dict[str, Any]] = None):
        super().__init__(name, parameters)
        self.economic_context: Optional[EconomicContext] = None
        
        # 经济指标配置
        self.economic_indicators = self.get_economic_indicators()
        self.correlation_threshold = self.get_parameter('correlation_threshold', 0.7)
        self.trend_lookback_days = self.get_parameter('trend_lookback_days', 90)
    
    @abstractmethod
    def get_economic_indicators(self) -> List[str]:
        """
        获取策略使用的经济指标列表
        
        Returns:
            经济指标ID列表
        """
        
        return []  # 返回空数据，避免系统错误
    @abstractmethod
    def analyze_economic_conditions(self, economic_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        分析经济状况
        
        Args:
            economic_data: 经济数据字典
            
        Returns:
            经济分析结果
        """
        pass
    
    def initialize(self, context: EconomicContext) -> None:
        """
        初始化经济策略
        
        Args:
            context: 经济策略上下文
        """
        self.set_context(context)
        self.is_initialized = True
        self.logger.info(f"经济策略 {self.name} 初始化完成")
    
    def set_context(self, context: StrategyContext) -> None:
        """
        设置策略上下文
        
        Args:
            context: 策略上下文，应该是EconomicContext实例
        """
        if not isinstance(context, EconomicContext):
            raise ValueError("宏观经济策略需要EconomicContext")
        
        super().set_context(context)
        self.economic_context = context
    
    def get_economic_data_for_analysis(self) -> Dict[str, pd.DataFrame]:
        """
        获取分析所需的经济数据
        
        Returns:
            经济数据字典
        """
        if not self.economic_context:
            return {}
        
        return self.economic_context.get_multiple_economic_data(
            self.economic_indicators,
            self.trend_lookback_days
        )
    
    def calculate_economic_score(self, economic_analysis: Dict[str, Any]) -> float:
        """
        计算经济评分
        
        Args:
            economic_analysis: 经济分析结果
            
        Returns:
            经济评分 (-1到1之间，-1表示极度悲观，1表示极度乐观)
        """
        try:
            score = 0.0
            weight_sum = 0.0
            
            # 经济周期权重
            cycle_phase = economic_analysis.get('cycle_phase', 'UNKNOWN')
            cycle_weights = {
                'EXPANSION': 0.8,
                'PEAK': 0.2,
                'CONTRACTION': -0.8,
                'TROUGH': -0.2,
                'UNKNOWN': 0.0
            }
            cycle_score = cycle_weights.get(cycle_phase, 0.0)
            score += cycle_score * 0.4
            weight_sum += 0.4
            
            # 趋势分析权重
            trends = economic_analysis.get('trends', {})
            positive_trends = sum(1 for trend in trends.values() if trend == 'RISING')
            negative_trends = sum(1 for trend in trends.values() if trend == 'FALLING')
            total_trends = len(trends)
            
            if total_trends > 0:
                trend_score = (positive_trends - negative_trends) / total_trends
                score += trend_score * 0.3
                weight_sum += 0.3
            
            # 相关性分析权重
            correlations = economic_analysis.get('correlations', {})
            if correlations:
                avg_correlation = np.mean(list(correlations.values()))
                correlation_score = min(max(avg_correlation, -1), 1)  # 限制在-1到1之间
                score += correlation_score * 0.3
                weight_sum += 0.3
            
            # 标准化评分
            if weight_sum > 0:
                score = score / weight_sum
            
            return min(max(score, -1), 1)  # 确保在-1到1之间
            
        except Exception as e:
            self.logger.error(f"计算经济评分失败: {e}")
            return 0.0
    
    def generate_economic_signals(self, market_data: Union[MarketData, Dict[str, MarketData]],
                                economic_analysis: Dict[str, Any]) -> List[Signal]:
        """
        基于经济分析生成交易信号
        
        Args:
            market_data: 市场数据
            economic_analysis: 经济分析结果
            
        Returns:
            交易信号列表
        """
        signals = []
        
        try:
            economic_score = self.calculate_economic_score(economic_analysis)
            
            # 根据经济评分生成信号
            if isinstance(market_data, dict):
                # 多标的策略
                for symbol, data in market_data.items():
                    signal = self._create_signal_from_economic_score(
                        symbol, data, economic_score, economic_analysis
                    )
                    if signal:
                        signals.append(signal)
            else:
                # 单标的策略
                signal = self._create_signal_from_economic_score(
                    market_data.symbol, market_data, economic_score, economic_analysis
                )
                if signal:
                    signals.append(signal)
            
        except Exception as e:
            self.logger.error(f"生成经济信号失败: {e}")
        
        return signals
    
    def _create_signal_from_economic_score(self, symbol: str, market_data: MarketData,
                                         economic_score: float, 
                                         economic_analysis: Dict[str, Any]) -> Optional[Signal]:
        """
        根据经济评分创建交易信号
        
        Args:
            symbol: 交易标的
            market_data: 市场数据
            economic_score: 经济评分
            economic_analysis: 经济分析结果
            
        Returns:
            交易信号或None
        """
        try:
            # 信号阈值
            buy_threshold = self.get_parameter('buy_threshold', 0.3)
            sell_threshold = self.get_parameter('sell_threshold', -0.3)
            
            # 当前持仓
            current_position = self.context.get_position(symbol) if self.context else 0.0
            
            # 生成信号
            if economic_score > buy_threshold and current_position <= 0:
                from .signals import SignalType
                
                return Signal(
                    symbol=symbol,
                    signal_type=SignalType.BUY,
                    quantity=self.get_parameter('position_size', 100),
                    price=market_data.close,
                    timestamp=market_data.timestamp,
                    confidence=abs(economic_score),
                    strategy_name=self.name,
                    metadata={
                        'economic_score': economic_score,
                        'cycle_phase': economic_analysis.get('cycle_phase'),
                        'signal_category': 'ECONOMIC_BUY'
                    }
                )
            elif economic_score < sell_threshold and current_position >= 0:
                return Signal(
                    symbol=symbol,
                    signal_type=SignalType.SELL,
                    quantity=abs(current_position) if current_position != 0 else self.get_parameter('position_size', 100),
                    price=market_data.close,
                    timestamp=market_data.timestamp,
                    confidence=abs(economic_score),
                    strategy_name=self.name,
                    metadata={
                        'economic_score': economic_score,
                        'cycle_phase': economic_analysis.get('cycle_phase'),
                        'signal_category': 'ECONOMIC_SELL'
                    }
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"创建经济信号失败 {symbol}: {e}")
            return None
    
    def get_parameter_definitions(self) -> Dict[str, StrategyParameter]:
        """
        获取宏观经济策略的通用参数定义
        """
        return {
            'correlation_threshold': StrategyParameter(
                name='correlation_threshold',
                param_type=ParameterType.FLOAT,
                default_value=0.7,
                description='相关性阈值',
                min_value=0.0,
                max_value=1.0
            ),
            'trend_lookback_days': StrategyParameter(
                name='trend_lookback_days',
                param_type=ParameterType.INTEGER,
                default_value=90,
                description='趋势分析回看天数',
                min_value=30,
                max_value=365,
                validator=ParameterValidator.time_period
            ),
            'buy_threshold': StrategyParameter(
                name='buy_threshold',
                param_type=ParameterType.FLOAT,
                default_value=0.3,
                description='买入信号阈值',
                min_value=-1.0,
                max_value=1.0
            ),
            'sell_threshold': StrategyParameter(
                name='sell_threshold',
                param_type=ParameterType.FLOAT,
                default_value=-0.3,
                description='卖出信号阈值',
                min_value=-1.0,
                max_value=1.0
            ),
            'position_size': StrategyParameter(
                name='position_size',
                param_type=ParameterType.INTEGER,
                default_value=100,
                description='仓位大小',
                min_value=1,
                validator=ParameterValidator.positive_number
            )
        }