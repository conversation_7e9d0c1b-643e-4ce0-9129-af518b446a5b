"""
量化交易系统 - 策略基础类和接口定义

本模块提供策略开发的核心抽象类、上下文管理器和基础设施。
是整个策略框架的基础，定义了策略的标准接口和生命周期管理。

主要组件:
- BaseStrategy: 策略抽象基类，定义策略标准接口
- StrategyContext: 策略上下文管理器，提供数据和服务访问
- StrategyMetadata: 策略元数据管理
- StrategyParameter: 策略参数定义和验证

策略生命周期:
1. 初始化 (initialize): 策略参数设置和资源准备
2. 数据处理 (on_data): 处理市场数据，生成交易信号
3. 订单处理 (on_order_fill): 处理订单成交事件
4. 清理 (cleanup): 策略结束时的资源清理

支持的策略类型:
- 技术分析策略: 基于技术指标的交易策略
- 基本面策略: 基于财务数据的价值投资策略
- 量化策略: 基于数学模型的系统化交易
- 多因子策略: 结合多个因子的综合策略
- 机器学习策略: 基于AI模型的预测策略

作者: 量化交易系统开发团队
版本: 2.0.0
创建日期: 2024-01-01
最后修改: 2024-07-24

示例:
    创建自定义策略:
    >>> class MyStrategy(BaseStrategy):
    ...     def initialize(self, context):
    ...         self.ma_period = self.parameters.get('ma_period', 20)
    ...     
    ...     def on_data(self, market_data):
    ...         # 策略逻辑实现
    ...         return []
    
    使用策略:
    >>> strategy = MyStrategy('my_strategy', {'ma_period': 25})
    >>> context = StrategyContext(data_manager, indicator_engine, portfolio)
    >>> strategy.initialize(context)
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import pandas as pd
from src.common.utils.logging import get_logger
import logging

try:
    from ..models.market_data import MarketData
    from ..models.portfolio import Portfolio
    from .signals import Signal
    from .parameters import StrategyParameter
except ImportError:
    # Fallbacks to top-level src models and strategies modules
    from src.market.strategies.models.market_data import MarketData  # type: ignore
    from src.market.strategies.models.portfolio import Portfolio  # type: ignore
    from .parameters import StrategyParameter  # type: ignore
    from .signals import Signal  # type: ignore


class StrategyContext:
    """
    策略上下文管理器 - 为策略提供数据访问和服务接口
    
    StrategyContext是策略与系统其他组件交互的桥梁，提供了策略运行
    所需的所有数据访问和计算服务。它封装了数据管理、指标计算、
    投资组合管理等功能，使策略开发者可以专注于交易逻辑的实现。
    
    主要功能:
        - 历史数据查询: 获取指定时间范围的市场数据
        - 技术指标计算: 提供常用技术指标的计算服务
        - 投资组合访问: 获取当前持仓和资金状况
        - 市场信息查询: 获取交易所信息、交易时间等
        - 缓存管理: 自动缓存计算结果提升性能
    
    设计模式:
        - 外观模式: 为复杂的子系统提供简化接口
        - 代理模式: 代理访问底层数据和计算服务
        - 单例模式: 确保策略运行期间上下文的一致性
    
    属性:
        data_manager: 数据管理器，提供数据访问服务
        indicator_engine: 指标计算引擎
        portfolio: 投资组合管理器
        logger: 日志记录器
    
    使用场景:
        - 策略初始化时获取历史数据进行预处理
        - 策略运行时计算技术指标
        - 获取当前投资组合状态进行风险控制
        - 记录策略运行日志和调试信息
    
    示例:
        在策略中使用上下文:
        >>> def on_data(self, market_data):
        ...     # 获取历史数据
        ...     hist_data = self.context.get_historical_data('AAPL', 20)
        ...     
        ...     # 计算技术指标
        ...     sma = self.context.get_indicator('SMA', period=20)
        ...     rsi = self.context.get_indicator('RSI', period=14)
        ...     
        ...     # 获取投资组合信息
        ...     portfolio = self.context.get_portfolio()
        ...     current_position = portfolio.get_position('AAPL')
        
        创建上下文:
        >>> context = StrategyContext(
        ...     data_manager=data_manager,
        ...     indicator_engine=indicator_engine,
        ...     portfolio=portfolio
        ... )
    
    注意事项:
        - 上下文对象在策略生命周期内保持不变
        - 数据查询可能涉及网络请求，注意处理异常
        - 指标计算结果会被自动缓存
        - 避免在循环中重复查询相同数据
    
    性能优化:
        - 合理使用缓存减少重复计算
        - 批量查询数据而非逐条查询
        - 预加载常用的历史数据
        - 使用异步方法处理耗时操作
    """
    
    def __init__(self, data_manager=None, indicator_engine=None, portfolio=None):
        self.data_manager = data_manager
        self.indicator_engine = indicator_engine
        self.portfolio = portfolio
        self.logger = get_logger("strategy_context")
        
    def get_historical_data(self, symbol: str, lookback: int, 
                          end_date: Optional[datetime] = None) -> pd.DataFrame:
        """
        获取历史数据
        
        Args:
            symbol: 交易标的代码
            lookback: 回看天数
            end_date: 结束日期，默认为当前时间
            
        Returns:
            历史OHLCV数据DataFrame
        """
        if not self.data_manager:
            raise ValueError("数据管理器未初始化")
            
        try:
            return self.data_manager.get_historical_data(
                symbol=symbol,
                lookback=lookback,
                end_date=end_date
            )
        except Exception as e:
            self.logger.error(f"获取历史数据失败: {symbol}, {e}")
            return pd.DataFrame()
    
    def get_current_data(self, symbol: str) -> Optional[MarketData]:
        """
        获取当前市场数据
        
        Args:
            symbol: 交易标的代码
            
        Returns:
            当前市场数据
        """
        if not self.data_manager:
            return None
            
        try:
            return self.data_manager.get_current_data(symbol)
        except Exception as e:
            self.logger.error(f"获取当前数据失败: {symbol}, {e}")
            return None
    
    def get_indicator(self, name: str, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        计算技术指标
        
        Args:
            name: 指标名称
            data: 价格数据
            **kwargs: 指标参数
            
        Returns:
            指标计算结果
        """
        if not self.indicator_engine:
            raise ValueError("指标引擎未初始化")
            
        try:
            return self.indicator_engine.calculate(name, data, **kwargs)
        except Exception as e:
            self.logger.error(f"计算指标失败: {name}, {e}")
            return pd.Series()
    
    def get_portfolio(self) -> Portfolio:
        """
        获取当前投资组合信息
        
        Returns:
            投资组合对象
        """
        return self.portfolio
    
    def get_position(self, symbol: str) -> Optional[float]:
        """
        获取特定标的的持仓数量
        
        Args:
            symbol: 交易标的代码
            
        Returns:
            持仓数量，正数为多头，负数为空头
        """
        if not self.portfolio:
            return 0.0
            
        position = self.portfolio.positions.get(symbol)
        return position.quantity if position else 0.0
    
    def get_cash(self) -> float:
        """
        获取可用现金
        
        Returns:
            可用现金金额
        """
        return self.portfolio.cash if self.portfolio else 0.0


class BaseStrategy(ABC):
    """
    策略基础抽象类
    
    所有策略都应该继承此类并实现必要的抽象方法
    """
    
    def __init__(self, name: str, parameters: Optional[Dict[str, Any]] = None):
        self.name = name
        self.parameters = parameters or {}
        self.context: Optional[StrategyContext] = None
        self.is_initialized = False
        self.is_active = False
        self.logger = get_logger(f"strategy_{self.__class__.__name__.lower()}")
        
        # 验证参数
        self._validate_parameters()
    
    @abstractmethod
    def get_parameter_definitions(self) -> Dict[str, StrategyParameter]:
        """
        获取策略参数定义
        
        Returns:
            参数名称到参数定义的映射
        """
        pass
    
    @abstractmethod
    def initialize(self, context: StrategyContext) -> None:
        """
        策略初始化
        
        Args:
            context: 策略上下文
        """
        pass
    
    @abstractmethod
    def on_data(self, data: Union[MarketData, Dict[str, MarketData]]) -> List[Signal]:
        """
        数据更新时的处理逻辑
        
        Args:
            data: 市场数据，可以是单个标的或多个标的的数据
            
        Returns:
            生成的交易信号列表
        """
        pass
    
    def on_signal(self, signal: Signal) -> None:
        """
        信号生成后的处理逻辑（可选重写）
        
        Args:
            signal: 生成的交易信号
        """
        self.logger.info(f"策略 {self.name} 生成信号: {signal}")
    
    def on_order_fill(self, fill_info: Dict[str, Any]) -> None:
        """
        订单成交后的处理逻辑（可选重写）
        
        Args:
            fill_info: 成交信息
        """
        self.logger.info(f"策略 {self.name} 订单成交: {fill_info}")
    
    def cleanup(self) -> None:
        """
        策略清理逻辑（可选重写）
        """
        self.is_active = False
        self.logger.info(f"策略 {self.name} 已清理")
    
    def set_context(self, context: StrategyContext) -> None:
        """
        设置策略上下文
        
        Args:
            context: 策略上下文
        """
        self.context = context
    
    def get_parameter(self, name: str, default: Any = None) -> Any:
        """
        获取策略参数值
        
        Args:
            name: 参数名称
            default: 默认值
            
        Returns:
            参数值
        """
        return self.parameters.get(name, default)
    
    def set_parameter(self, name: str, value: Any) -> None:
        """
        设置策略参数值
        
        Args:
            name: 参数名称
            value: 参数值
        """
        # 验证参数
        param_def = self.get_parameter_definitions().get(name)
        if param_def:
            param_def.validate(value)
        
        self.parameters[name] = value
    
    def _validate_parameters(self) -> None:
        """
        验证策略参数
        """
        param_definitions = self.get_parameter_definitions()
        
        for name, param_def in param_definitions.items():
            value = self.parameters.get(name, param_def.default_value)
            
            if value is None and param_def.required:
                raise ValueError(f"必需参数 {name} 未设置")
            
            if value is not None:
                param_def.validate(value)
                self.parameters[name] = value
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取策略状态信息
        
        Returns:
            策略状态字典
        """
        return {
            'name': self.name,
            'is_initialized': self.is_initialized,
            'is_active': self.is_active,
            'parameters': self.parameters.copy(),
            'class_name': self.__class__.__name__
        }
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(name='{self.name}')"
    
    def __repr__(self) -> str:
        return self.__str__()