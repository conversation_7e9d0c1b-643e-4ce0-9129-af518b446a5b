"""
经济周期识别和趋势分析策略

基于经济周期阶段调整投资策略
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import logging

try:
    from ..economic_base import EconomicStrategy, EconomicContext
    from ..signals import Signal
    from ..parameters import StrategyParameter, ParameterType, ParameterValidator
    from ...models.market_data import MarketData
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from src.market.strategies.economic_base import EconomicStrategy, EconomicContext
    from src.market.strategies.signals import Signal
    from src.market.strategies.parameters import StrategyParameter, ParameterType, ParameterValidator
    from src.market.strategies.models.market_data import MarketData


class EconomicCycleStrategy(EconomicStrategy):
    """
    经济周期识别和趋势分析策略
    
    根据经济周期的不同阶段采用不同的投资策略
    """
    
    def __init__(self, name: str = "Economic Cycle Strategy", 
                 parameters: Optional[Dict[str, Any]] = None):
        super().__init__(name, parameters)
        
        # 经济周期历史记录
        self.cycle_history = []
        self.last_cycle_check = None
        
        # 周期特定的投资偏好
        self.cycle_preferences = {
            'EXPANSION': {
                'sectors': ['technology', 'consumer_discretionary', 'industrials'],
                'risk_level': 'HIGH',
                'position_multiplier': 1.2
            },
            'PEAK': {
                'sectors': ['utilities', 'consumer_staples', 'healthcare'],
                'risk_level': 'MEDIUM',
                'position_multiplier': 0.8
            },
            'CONTRACTION': {
                'sectors': ['utilities', 'consumer_staples', 'bonds'],
                'risk_level': 'LOW',
                'position_multiplier': 0.5
            },
            'TROUGH': {
                'sectors': ['financials', 'materials', 'energy'],
                'risk_level': 'MEDIUM',
                'position_multiplier': 1.0
            }
        }
        
        self.logger = logging.getLogger(f"{__name__}.EconomicCycleStrategy")
    
    def initialize(self, context: EconomicContext) -> None:
        """
        初始化经济周期策略
        
        Args:
            context: 经济策略上下文
        """
        super().initialize(context)
        self.logger.info(f"经济周期策略 {self.name} 初始化完成")
    
    def get_economic_indicators(self) -> List[str]:
        """获取经济周期分析所需的指标"""
        return [
            # GDP和增长指标
            'GDPC1',        # 实际GDP
            'GDPPOT',       # 潜在GDP
            
            # 就业指标
            'UNRATE',       # 失业率
            'CIVPART',      # 劳动参与率
            'PAYEMS',       # 非农就业人数
            
            # 通胀指标
            'CPIAUCSL',     # CPI
            'CPILFESL',     # 核心CPI
            'PCEPI',        # PCE价格指数
            
            # 利率和货币政策
            'FEDFUNDS',     # 联邦基金利率
            'DGS10',        # 10年期国债收益率
            'DGS2',         # 2年期国债收益率
            
            # 先行指标
            'HOUST',        # 新屋开工
            'INDPRO',       # 工业生产指数
            'NAPMPI',       # ISM制造业PMI
            
            # 金融市场指标
            'SP500',        # 标普500
            'VIXCLS',       # VIX恐慌指数
            
            # 消费者信心
            'UMCSENT'       # 密歇根消费者信心指数
        ]
    
    def analyze_economic_conditions(self, economic_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        深度分析经济周期状态
        """
        analysis = {
            'cycle_phase': 'UNKNOWN',
            'cycle_confidence': 0.0,
            'phase_duration': 0,
            'leading_indicators': {},
            'lagging_indicators': {},
            'coincident_indicators': {},
            'cycle_momentum': 'NEUTRAL',
            'transition_probability': {},
            'sector_recommendations': []
        }
        
        try:
            # 1. 基础周期识别
            analysis['cycle_phase'] = self.economic_context.get_economic_cycle_phase()
            
            # 2. 详细指标分析
            analysis['leading_indicators'] = self._analyze_leading_indicators(economic_data)
            analysis['lagging_indicators'] = self._analyze_lagging_indicators(economic_data)
            analysis['coincident_indicators'] = self._analyze_coincident_indicators(economic_data)
            
            # 3. 周期置信度计算
            analysis['cycle_confidence'] = self._calculate_cycle_confidence(
                analysis['leading_indicators'],
                analysis['lagging_indicators'],
                analysis['coincident_indicators']
            )
            
            # 4. 周期持续时间分析
            analysis['phase_duration'] = self._estimate_phase_duration(analysis['cycle_phase'])
            
            # 5. 周期动量分析
            analysis['cycle_momentum'] = self._analyze_cycle_momentum(economic_data)
            
            # 6. 转换概率预测
            analysis['transition_probability'] = self._calculate_transition_probabilities(
                analysis['cycle_phase'], analysis['leading_indicators']
            )
            
            # 7. 行业推荐
            analysis['sector_recommendations'] = self._generate_sector_recommendations(
                analysis['cycle_phase'], analysis['cycle_confidence']
            )
            
        except Exception as e:
            self.logger.error(f"经济周期分析失败: {e}")
        
        return analysis
    
    def _analyze_leading_indicators(self, economic_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """分析先行指标"""
        leading_analysis = {}
        
        try:
            # 新屋开工
            if 'HOUST' in economic_data and not economic_data['HOUST'].empty:
                housing_trend = self.economic_context.get_economic_trend('HOUST', 90)
                leading_analysis['housing_starts'] = {
                    'trend': housing_trend,
                    'signal': 'POSITIVE' if housing_trend == 'RISING' else 'NEGATIVE' if housing_trend == 'FALLING' else 'NEUTRAL'
                }
            
            # 股市表现
            if 'SP500' in economic_data and not economic_data['SP500'].empty:
                stock_trend = self.economic_context.get_economic_trend('SP500', 60)
                leading_analysis['stock_market'] = {
                    'trend': stock_trend,
                    'signal': 'POSITIVE' if stock_trend == 'RISING' else 'NEGATIVE' if stock_trend == 'FALLING' else 'NEUTRAL'
                }
            
            # 收益率曲线
            if 'DGS10' in economic_data and 'DGS2' in economic_data:
                yield_curve = self._analyze_yield_curve(economic_data['DGS10'], economic_data['DGS2'])
                leading_analysis['yield_curve'] = yield_curve
            
            # 消费者信心
            if 'UMCSENT' in economic_data and not economic_data['UMCSENT'].empty:
                confidence_trend = self.economic_context.get_economic_trend('UMCSENT', 90)
                leading_analysis['consumer_confidence'] = {
                    'trend': confidence_trend,
                    'signal': 'POSITIVE' if confidence_trend == 'RISING' else 'NEGATIVE' if confidence_trend == 'FALLING' else 'NEUTRAL'
                }
            
        except Exception as e:
            self.logger.error(f"先行指标分析失败: {e}")
        
        return leading_analysis
    
    def _analyze_lagging_indicators(self, economic_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """分析滞后指标"""
        lagging_analysis = {}
        
        try:
            # 失业率
            if 'UNRATE' in economic_data and not economic_data['UNRATE'].empty:
                unemployment_trend = self.economic_context.get_economic_trend('UNRATE', 180)
                current_rate = economic_data['UNRATE']['value'].iloc[-1]
                lagging_analysis['unemployment'] = {
                    'trend': unemployment_trend,
                    'current_rate': current_rate,
                    'signal': 'NEGATIVE' if unemployment_trend == 'RISING' else 'POSITIVE' if unemployment_trend == 'FALLING' else 'NEUTRAL'
                }
            
            # 通胀率
            if 'CPIAUCSL' in economic_data and not economic_data['CPIAUCSL'].empty:
                inflation_trend = self.economic_context.get_economic_trend('CPIAUCSL', 180)
                lagging_analysis['inflation'] = {
                    'trend': inflation_trend,
                    'signal': 'NEGATIVE' if inflation_trend == 'RISING' else 'POSITIVE' if inflation_trend == 'FALLING' else 'NEUTRAL'
                }
            
            # 利率
            if 'FEDFUNDS' in economic_data and not economic_data['FEDFUNDS'].empty:
                rate_trend = self.economic_context.get_economic_trend('FEDFUNDS', 180)
                current_rate = economic_data['FEDFUNDS']['value'].iloc[-1]
                lagging_analysis['interest_rates'] = {
                    'trend': rate_trend,
                    'current_rate': current_rate,
                    'signal': 'NEGATIVE' if rate_trend == 'RISING' else 'POSITIVE' if rate_trend == 'FALLING' else 'NEUTRAL'
                }
            
        except Exception as e:
            self.logger.error(f"滞后指标分析失败: {e}")
        
        return lagging_analysis
    
    def _analyze_coincident_indicators(self, economic_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """分析同步指标"""
        coincident_analysis = {}
        
        try:
            # GDP
            if 'GDPC1' in economic_data and not economic_data['GDPC1'].empty:
                gdp_trend = self.economic_context.get_economic_trend('GDPC1', 180)
                coincident_analysis['gdp'] = {
                    'trend': gdp_trend,
                    'signal': 'POSITIVE' if gdp_trend == 'RISING' else 'NEGATIVE' if gdp_trend == 'FALLING' else 'NEUTRAL'
                }
            
            # 工业生产
            if 'INDPRO' in economic_data and not economic_data['INDPRO'].empty:
                industrial_trend = self.economic_context.get_economic_trend('INDPRO', 90)
                coincident_analysis['industrial_production'] = {
                    'trend': industrial_trend,
                    'signal': 'POSITIVE' if industrial_trend == 'RISING' else 'NEGATIVE' if industrial_trend == 'FALLING' else 'NEUTRAL'
                }
            
            # 就业
            if 'PAYEMS' in economic_data and not economic_data['PAYEMS'].empty:
                employment_trend = self.economic_context.get_economic_trend('PAYEMS', 90)
                coincident_analysis['employment'] = {
                    'trend': employment_trend,
                    'signal': 'POSITIVE' if employment_trend == 'RISING' else 'NEGATIVE' if employment_trend == 'FALLING' else 'NEUTRAL'
                }
            
        except Exception as e:
            self.logger.error(f"同步指标分析失败: {e}")
        
        return coincident_analysis
    
    def _analyze_yield_curve(self, long_term: pd.DataFrame, short_term: pd.DataFrame) -> Dict[str, Any]:
        """分析收益率曲线"""
        try:
            if long_term.empty or short_term.empty:
                return {'shape': 'UNKNOWN', 'signal': 'NEUTRAL'}
            
            # 对齐数据
            combined = pd.merge(long_term[['value']], short_term[['value']], 
                              left_index=True, right_index=True, 
                              suffixes=('_10y', '_2y'), how='inner')
            
            if combined.empty:
                return {'shape': 'UNKNOWN', 'signal': 'NEUTRAL'}
            
            # 计算收益率差
            spread = combined['value_10y'] - combined['value_2y']
            current_spread = spread.iloc[-1]
            
            # 判断收益率曲线形状
            if current_spread > 1.5:
                shape = 'STEEP'
                signal = 'POSITIVE'  # 陡峭曲线通常预示经济增长
            elif current_spread < 0:
                shape = 'INVERTED'
                signal = 'NEGATIVE'  # 倒挂曲线通常预示经济衰退
            elif current_spread < 0.5:
                shape = 'FLAT'
                signal = 'NEUTRAL'
            else:
                shape = 'NORMAL'
                signal = 'NEUTRAL'
            
            return {
                'shape': shape,
                'spread': current_spread,
                'signal': signal
            }
            
        except Exception as e:
            self.logger.error(f"收益率曲线分析失败: {e}")
            return {'shape': 'UNKNOWN', 'signal': 'NEUTRAL'}
    
    def _calculate_cycle_confidence(self, leading: Dict, lagging: Dict, coincident: Dict) -> float:
        """计算周期识别的置信度"""
        try:
            positive_signals = 0
            negative_signals = 0
            total_signals = 0
            
            # 统计各类指标的信号
            for indicator_group in [leading, lagging, coincident]:
                for indicator, data in indicator_group.items():
                    if isinstance(data, dict) and 'signal' in data:
                        total_signals += 1
                        if data['signal'] == 'POSITIVE':
                            positive_signals += 1
                        elif data['signal'] == 'NEGATIVE':
                            negative_signals += 1
            
            if total_signals == 0:
                return 0.5
            
            # 计算信号一致性
            dominant_signals = max(positive_signals, negative_signals)
            consistency = dominant_signals / total_signals
            
            # 先行指标权重更高
            leading_weight = 0.5
            coincident_weight = 0.3
            lagging_weight = 0.2
            
            weighted_confidence = (
                len(leading) * leading_weight +
                len(coincident) * coincident_weight +
                len(lagging) * lagging_weight
            ) / (len(leading) + len(coincident) + len(lagging))
            
            return min(1.0, consistency * weighted_confidence)
            
        except Exception as e:
            self.logger.error(f"置信度计算失败: {e}")
            return 0.5
    
    def _estimate_phase_duration(self, cycle_phase: str) -> int:
        """估计当前周期阶段的持续时间"""
        try:
            # 更新周期历史
            current_time = datetime.now()
            
            if (not self.cycle_history or 
                self.cycle_history[-1]['phase'] != cycle_phase):
                self.cycle_history.append({
                    'phase': cycle_phase,
                    'start_time': current_time,
                    'duration': 0
                })
            else:
                # 更新当前阶段持续时间
                start_time = self.cycle_history[-1]['start_time']
                duration = (current_time - start_time).days
                self.cycle_history[-1]['duration'] = duration
                return duration
            
            return 0
            
        except Exception as e:
            self.logger.error(f"周期持续时间估计失败: {e}")
            return 0
    
    def _analyze_cycle_momentum(self, economic_data: Dict[str, pd.DataFrame]) -> str:
        """分析经济周期动量"""
        try:
            momentum_score = 0
            indicators_count = 0
            
            # 关键指标的动量分析
            key_indicators = ['GDPC1', 'UNRATE', 'INDPRO', 'SP500']
            
            for indicator in key_indicators:
                if indicator in economic_data and not economic_data[indicator].empty:
                    trend = self.economic_context.get_economic_trend(indicator, 60)
                    if trend == 'RISING':
                        momentum_score += 1 if indicator != 'UNRATE' else -1  # 失业率上升是负面的
                    elif trend == 'FALLING':
                        momentum_score -= 1 if indicator != 'UNRATE' else 1
                    indicators_count += 1
            
            if indicators_count == 0:
                return 'NEUTRAL'
            
            avg_momentum = momentum_score / indicators_count
            
            if avg_momentum > 0.3:
                return 'ACCELERATING'
            elif avg_momentum < -0.3:
                return 'DECELERATING'
            else:
                return 'NEUTRAL'
                
        except Exception as e:
            self.logger.error(f"周期动量分析失败: {e}")
            return 'NEUTRAL'
    
    def _calculate_transition_probabilities(self, current_phase: str, 
                                         leading_indicators: Dict[str, Any]) -> Dict[str, float]:
        """计算周期转换概率"""
        try:
            # 基础转换概率矩阵
            base_transitions = {
                'EXPANSION': {'PEAK': 0.2, 'EXPANSION': 0.8},
                'PEAK': {'CONTRACTION': 0.4, 'PEAK': 0.6},
                'CONTRACTION': {'TROUGH': 0.3, 'CONTRACTION': 0.7},
                'TROUGH': {'EXPANSION': 0.5, 'TROUGH': 0.5}
            }
            
            if current_phase not in base_transitions:
                return {}
            
            probabilities = base_transitions[current_phase].copy()
            
            # 根据先行指标调整概率
            positive_signals = sum(1 for data in leading_indicators.values() 
                                 if isinstance(data, dict) and data.get('signal') == 'POSITIVE')
            negative_signals = sum(1 for data in leading_indicators.values() 
                                 if isinstance(data, dict) and data.get('signal') == 'NEGATIVE')
            
            total_signals = positive_signals + negative_signals
            if total_signals > 0:
                signal_ratio = (positive_signals - negative_signals) / total_signals
                
                # 调整转换概率
                if current_phase == 'EXPANSION' and signal_ratio < -0.5:
                    probabilities['PEAK'] += 0.2
                    probabilities['EXPANSION'] -= 0.2
                elif current_phase == 'PEAK' and signal_ratio < -0.3:
                    probabilities['CONTRACTION'] += 0.3
                    probabilities['PEAK'] -= 0.3
                elif current_phase == 'CONTRACTION' and signal_ratio > 0.3:
                    probabilities['TROUGH'] += 0.2
                    probabilities['CONTRACTION'] -= 0.2
                elif current_phase == 'TROUGH' and signal_ratio > 0.5:
                    probabilities['EXPANSION'] += 0.3
                    probabilities['TROUGH'] -= 0.3
            
            # 确保概率和为1
            total_prob = sum(probabilities.values())
            if total_prob > 0:
                for phase in probabilities:
                    probabilities[phase] /= total_prob
            
            return probabilities
            
        except Exception as e:
            self.logger.error(f"转换概率计算失败: {e}")
            return {}
    
    def _generate_sector_recommendations(self, cycle_phase: str, confidence: float) -> List[Dict[str, Any]]:
        """生成行业推荐"""
        try:
            if cycle_phase not in self.cycle_preferences:
                return []
            
            preferences = self.cycle_preferences[cycle_phase]
            recommendations = []
            
            for sector in preferences['sectors']:
                recommendations.append({
                    'sector': sector,
                    'recommendation': 'OVERWEIGHT' if confidence > 0.7 else 'NEUTRAL',
                    'confidence': confidence,
                    'risk_level': preferences['risk_level'],
                    'position_multiplier': preferences['position_multiplier']
                })
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"行业推荐生成失败: {e}")
            return []
    
    def on_data(self, data: Union[MarketData, Dict[str, MarketData]]) -> List[Signal]:
        """
        基于经济周期分析生成交易信号
        """
        try:
            # 获取经济数据
            economic_data = self.get_economic_data_for_analysis()
            if not economic_data:
                return []
            
            # 分析经济周期
            cycle_analysis = self.analyze_economic_conditions(economic_data)
            
            # 生成基于周期的信号
            signals = self._generate_cycle_based_signals(data, cycle_analysis)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"经济周期策略数据处理失败: {e}")
            return []
    
    def _generate_cycle_based_signals(self, market_data: Union[MarketData, Dict[str, MarketData]],
                                    cycle_analysis: Dict[str, Any]) -> List[Signal]:
        """基于经济周期生成交易信号"""
        signals = []
        
        try:
            cycle_phase = cycle_analysis.get('cycle_phase', 'UNKNOWN')
            confidence = cycle_analysis.get('cycle_confidence', 0.0)
            momentum = cycle_analysis.get('cycle_momentum', 'NEUTRAL')
            
            # 只有在置信度足够高时才生成信号
            min_confidence = self.get_parameter('min_cycle_confidence', 0.6)
            if confidence < min_confidence:
                return signals
            
            # 获取周期偏好
            if cycle_phase not in self.cycle_preferences:
                return signals
            
            preferences = self.cycle_preferences[cycle_phase]
            position_multiplier = preferences['position_multiplier']
            
            # 根据动量调整
            if momentum == 'ACCELERATING':
                position_multiplier *= 1.2
            elif momentum == 'DECELERATING':
                position_multiplier *= 0.8
            
            # 生成信号
            if isinstance(market_data, dict):
                for symbol, data in market_data.items():
                    signal = self._create_cycle_signal(
                        symbol, data, cycle_phase, position_multiplier, confidence, cycle_analysis
                    )
                    if signal:
                        signals.append(signal)
            else:
                signal = self._create_cycle_signal(
                    market_data.symbol, market_data, cycle_phase, 
                    position_multiplier, confidence, cycle_analysis
                )
                if signal:
                    signals.append(signal)
            
        except Exception as e:
            self.logger.error(f"周期信号生成失败: {e}")
        
        return signals
    
    def _create_cycle_signal(self, symbol: str, market_data: MarketData, 
                           cycle_phase: str, position_multiplier: float,
                           confidence: float, cycle_analysis: Dict[str, Any]) -> Optional[Signal]:
        """创建基于经济周期的交易信号"""
        try:
            base_position_size = self.get_parameter('base_position_size', 100)
            adjusted_position_size = int(base_position_size * position_multiplier)
            
            current_position = self.context.get_position(symbol) if self.context else 0.0
            
            # 根据周期阶段决定行动
            if cycle_phase in ['EXPANSION', 'TROUGH'] and position_multiplier > 0.8:
                # 扩张期和谷底期：增加风险资产
                if current_position < adjusted_position_size:
                    from ..signals import SignalType
                    
                    return Signal(
                        symbol=symbol,
                        signal_type=SignalType.BUY,
                        quantity=adjusted_position_size - current_position,
                        price=market_data.close,
                        timestamp=market_data.timestamp,
                        confidence=confidence,
                        strategy_name=self.name,
                        metadata={
                            'cycle_phase': cycle_phase,
                            'position_multiplier': position_multiplier,
                            'cycle_confidence': confidence,
                            'signal_category': 'CYCLE_BUY'
                        }
                    )
            elif cycle_phase in ['PEAK', 'CONTRACTION'] and position_multiplier < 1.0:
                # 峰值期和收缩期：减少风险资产
                if current_position > adjusted_position_size:
                    return Signal(
                        symbol=symbol,
                        signal_type=SignalType.SELL,
                        quantity=current_position - adjusted_position_size,
                        price=market_data.close,
                        timestamp=market_data.timestamp,
                        confidence=confidence,
                        strategy_name=self.name,
                        metadata={
                            'cycle_phase': cycle_phase,
                            'position_multiplier': position_multiplier,
                            'cycle_confidence': confidence,
                            'signal_category': 'CYCLE_SELL'
                        }
                    )
            
            return None
            
        except Exception as e:
            self.logger.error(f"周期信号创建失败 {symbol}: {e}")
            return None
    
    def get_parameter_definitions(self) -> Dict[str, StrategyParameter]:
        """获取策略参数定义"""
        base_params = super().get_parameter_definitions()
        
        cycle_params = {
            'min_cycle_confidence': StrategyParameter(
                name='min_cycle_confidence',
                param_type=ParameterType.FLOAT,
                default_value=0.6,
                description='最小周期识别置信度',
                min_value=0.0,
                max_value=1.0
            ),
            'base_position_size': StrategyParameter(
                name='base_position_size',
                param_type=ParameterType.INTEGER,
                default_value=100,
                description='基础仓位大小',
                min_value=1,
                validator=ParameterValidator.positive_number
            ),
            'cycle_check_frequency': StrategyParameter(
                name='cycle_check_frequency',
                param_type=ParameterType.INTEGER,
                default_value=7,
                description='周期检查频率（天）',
                min_value=1,
                max_value=30
            )
        }
        
        base_params.update(cycle_params)
        return base_params
    
    def _estimate_phase_duration(self, cycle_phase: str) -> int:
        """估计当前周期阶段的持续时间"""
        try:
            # 更新周期历史
            current_time = datetime.now()
            
            if (not self.cycle_history or 
                self.cycle_history[-1]['phase'] != cycle_phase):
                self.cycle_history.append({
                    'phase': cycle_phase,
                    'start_time': current_time,
                    'duration': 0
                })
            else:
                # 更新当前阶段持续时间
                start_time = self.cycle_history[-1]['start_time']
                duration = (current_time - start_time).days
                self.cycle_history[-1]['duration'] = duration
                return duration
            
            return 0
            
        except Exception as e:
            self.logger.error(f"周期持续时间估计失败: {e}")
            return 0
    
    def _analyze_cycle_momentum(self, economic_data: Dict[str, pd.DataFrame]) -> str:
        """分析经济周期动量"""
        try:
            momentum_score = 0
            indicators_count = 0
            
            # 关键指标的动量分析
            key_indicators = ['GDPC1', 'UNRATE', 'INDPRO', 'SP500']
            
            for indicator in key_indicators:
                if indicator in economic_data and not economic_data[indicator].empty:
                    trend = self.economic_context.get_economic_trend(indicator, 60)
                    if trend == 'RISING':
                        momentum_score += 1 if indicator != 'UNRATE' else -1  # 失业率上升是负面的
                    elif trend == 'FALLING':
                        momentum_score -= 1 if indicator != 'UNRATE' else 1
                    indicators_count += 1
            
            if indicators_count == 0:
                return 'NEUTRAL'
            
            avg_momentum = momentum_score / indicators_count
            
            if avg_momentum > 0.3:
                return 'ACCELERATING'
            elif avg_momentum < -0.3:
                return 'DECELERATING'
            else:
                return 'NEUTRAL'
                
        except Exception as e:
            self.logger.error(f"周期动量分析失败: {e}")
            return 'NEUTRAL'
    
    def _calculate_transition_probabilities(self, current_phase: str, 
                                         leading_indicators: Dict[str, Any]) -> Dict[str, float]:
        """计算周期转换概率"""
        try:
            # 基础转换概率矩阵
            base_transitions = {
                'EXPANSION': {'PEAK': 0.2, 'EXPANSION': 0.8},
                'PEAK': {'CONTRACTION': 0.4, 'PEAK': 0.6},
                'CONTRACTION': {'TROUGH': 0.3, 'CONTRACTION': 0.7},
                'TROUGH': {'EXPANSION': 0.5, 'TROUGH': 0.5}
            }
            
            if current_phase not in base_transitions:
                return {}
            
            probabilities = base_transitions[current_phase].copy()
            
            # 根据先行指标调整概率
            positive_signals = sum(1 for data in leading_indicators.values() 
                                 if isinstance(data, dict) and data.get('signal') == 'POSITIVE')
            negative_signals = sum(1 for data in leading_indicators.values() 
                                 if isinstance(data, dict) and data.get('signal') == 'NEGATIVE')
            
            total_signals = positive_signals + negative_signals
            if total_signals > 0:
                signal_ratio = (positive_signals - negative_signals) / total_signals
                
                # 调整转换概率
                if current_phase == 'EXPANSION' and signal_ratio < -0.5:
                    probabilities['PEAK'] += 0.2
                    probabilities['EXPANSION'] -= 0.2
                elif current_phase == 'PEAK' and signal_ratio < -0.3:
                    probabilities['CONTRACTION'] += 0.3
                    probabilities['PEAK'] -= 0.3
                elif current_phase == 'CONTRACTION' and signal_ratio > 0.3:
                    probabilities['TROUGH'] += 0.2
                    probabilities['CONTRACTION'] -= 0.2
                elif current_phase == 'TROUGH' and signal_ratio > 0.5:
                    probabilities['EXPANSION'] += 0.3
                    probabilities['TROUGH'] -= 0.3
            
            # 确保概率和为1
            total_prob = sum(probabilities.values())
            if total_prob > 0:
                for phase in probabilities:
                    probabilities[phase] /= total_prob
            
            return probabilities
            
        except Exception as e:
            self.logger.error(f"转换概率计算失败: {e}")
            return {}
    
    def _generate_sector_recommendations(self, cycle_phase: str, confidence: float) -> List[Dict[str, Any]]:
        """生成行业推荐"""
        try:
            if cycle_phase not in self.cycle_preferences:
                return []
            
            preferences = self.cycle_preferences[cycle_phase]
            recommendations = []
            
            for sector in preferences['sectors']:
                recommendations.append({
                    'sector': sector,
                    'recommendation': 'OVERWEIGHT' if confidence > 0.7 else 'NEUTRAL',
                    'confidence': confidence,
                    'risk_level': preferences['risk_level'],
                    'position_multiplier': preferences['position_multiplier']
                })
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"行业推荐生成失败: {e}")
            return []
    
    def on_data(self, data: Union[MarketData, Dict[str, MarketData]]) -> List[Signal]:
        """
        基于经济周期分析生成交易信号
        """
        try:
            # 获取经济数据
            economic_data = self.get_economic_data_for_analysis()
            if not economic_data:
                return []
            
            # 分析经济周期
            cycle_analysis = self.analyze_economic_conditions(economic_data)
            
            # 生成基于周期的信号
            signals = self._generate_cycle_based_signals(data, cycle_analysis)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"经济周期策略数据处理失败: {e}")
            return []
    
    def _generate_cycle_based_signals(self, market_data: Union[MarketData, Dict[str, MarketData]],
                                    cycle_analysis: Dict[str, Any]) -> List[Signal]:
        """基于经济周期生成交易信号"""
        signals = []
        
        try:
            cycle_phase = cycle_analysis.get('cycle_phase', 'UNKNOWN')
            confidence = cycle_analysis.get('cycle_confidence', 0.0)
            momentum = cycle_analysis.get('cycle_momentum', 'NEUTRAL')
            
            # 只有在置信度足够高时才生成信号
            min_confidence = self.get_parameter('min_cycle_confidence', 0.6)
            if confidence < min_confidence:
                return signals
            
            # 获取周期偏好
            if cycle_phase not in self.cycle_preferences:
                return signals
            
            preferences = self.cycle_preferences[cycle_phase]
            position_multiplier = preferences['position_multiplier']
            
            # 根据动量调整
            if momentum == 'ACCELERATING':
                position_multiplier *= 1.2
            elif momentum == 'DECELERATING':
                position_multiplier *= 0.8
            
            # 生成信号
            if isinstance(market_data, dict):
                for symbol, data in market_data.items():
                    signal = self._create_cycle_signal(
                        symbol, data, cycle_phase, position_multiplier, confidence, cycle_analysis
                    )
                    if signal:
                        signals.append(signal)
            else:
                signal = self._create_cycle_signal(
                    market_data.symbol, market_data, cycle_phase, 
                    position_multiplier, confidence, cycle_analysis
                )
                if signal:
                    signals.append(signal)
            
        except Exception as e:
            self.logger.error(f"周期信号生成失败: {e}")
        
        return signals
    
    def _create_cycle_signal(self, symbol: str, market_data: MarketData, 
                           cycle_phase: str, position_multiplier: float,
                           confidence: float, cycle_analysis: Dict[str, Any]) -> Optional[Signal]:
        """创建基于经济周期的交易信号"""
        try:
            base_position_size = self.get_parameter('base_position_size', 100)
            adjusted_position_size = int(base_position_size * position_multiplier)
            
            current_position = self.context.get_position(symbol) if self.context else 0.0
            
            # 根据周期阶段决定行动
            if cycle_phase in ['EXPANSION', 'TROUGH'] and position_multiplier > 0.8:
                # 扩张期和谷底期：增加风险资产
                if current_position < adjusted_position_size:
                    from ..signals import SignalType
                    
                    return Signal(
                        symbol=symbol,
                        signal_type=SignalType.BUY,
                        quantity=adjusted_position_size - current_position,
                        price=market_data.close,
                        timestamp=market_data.timestamp,
                        confidence=confidence,
                        strategy_name=self.name,
                        metadata={
                            'cycle_phase': cycle_phase,
                            'position_multiplier': position_multiplier,
                            'cycle_confidence': confidence,
                            'signal_category': 'CYCLE_BUY'
                        }
                    )
            elif cycle_phase in ['PEAK', 'CONTRACTION'] and position_multiplier < 1.0:
                # 峰值期和收缩期：减少风险资产
                if current_position > adjusted_position_size:
                    return Signal(
                        symbol=symbol,
                        signal_type=SignalType.SELL,
                        quantity=current_position - adjusted_position_size,
                        price=market_data.close,
                        timestamp=market_data.timestamp,
                        confidence=confidence,
                        strategy_name=self.name,
                        metadata={
                            'cycle_phase': cycle_phase,
                            'position_multiplier': position_multiplier,
                            'cycle_confidence': confidence,
                            'signal_category': 'CYCLE_SELL'
                        }
                    )
            
            return None
            
        except Exception as e:
            self.logger.error(f"周期信号创建失败 {symbol}: {e}")
            return None
    
    def get_parameter_definitions(self) -> Dict[str, StrategyParameter]:
        """获取策略参数定义"""
        base_params = super().get_parameter_definitions()
        
        cycle_params = {
            'min_cycle_confidence': StrategyParameter(
                name='min_cycle_confidence',
                param_type=ParameterType.FLOAT,
                default_value=0.6,
                description='最小周期识别置信度',
                min_value=0.0,
                max_value=1.0
            ),
            'base_position_size': StrategyParameter(
                name='base_position_size',
                param_type=ParameterType.INTEGER,
                default_value=100,
                description='基础仓位大小',
                min_value=1,
                validator=ParameterValidator.positive_number
            ),
            'cycle_check_frequency': StrategyParameter(
                name='cycle_check_frequency',
                param_type=ParameterType.INTEGER,
                default_value=7,
                description='周期检查频率（天）',
                min_value=1,
                max_value=30
            )
        }
        
        base_params.update(cycle_params)
        return base_params
