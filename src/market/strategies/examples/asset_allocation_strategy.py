"""
基于经济指标的资产配置策略

根据宏观经济指标动态调整资产配置权重
"""

from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import pandas as pd
import numpy as np
import logging

try:
    from ..economic_base import EconomicStrategy, EconomicContext
    from ..signals import Signal
    from ..parameters import StrategyParameter, ParameterType, ParameterValidator
    from ...models.market_data import MarketData
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from src.market.strategies.economic_base import EconomicStrategy, EconomicContext
    from src.market.strategies.signals import Signal
    from src.market.strategies.parameters import StrategyParameter, ParameterType, ParameterValidator
    from src.market.strategies.models.market_data import MarketData


class AssetAllocationStrategy(EconomicStrategy):
    """
    基于经济指标的资产配置策略
    
    根据经济周期和关键指标调整股票、债券、商品等资产的配置权重
    """
    
    def __init__(self, name: str = "Asset Allocation Strategy", 
                 parameters: Optional[Dict[str, Any]] = None):
        super().__init__(name, parameters)
        
        # 资产类别配置
        self.asset_symbols = self.get_parameter('asset_symbols', {
            'stocks': ['SPY'],      # 股票ETF
            'bonds': ['TLT'],       # 长期国债ETF
            'commodities': ['GLD'], # 黄金ETF
            'cash': []              # 现金等价物
        })
        
        # 配置权重范围
        self.weight_ranges = self.get_parameter('weight_ranges', {
            'stocks': {'min': 0.2, 'max': 0.8},
            'bonds': {'min': 0.1, 'max': 0.6},
            'commodities': {'min': 0.0, 'max': 0.3},
            'cash': {'min': 0.0, 'max': 0.4}
        })
        
        # 当前配置权重
        self.current_weights = {}
        
        self.logger = logging.getLogger(f"{__name__}.AssetAllocationStrategy")
    
    def initialize(self, context: EconomicContext) -> None:
        """
        初始化资产配置策略
        
        Args:
            context: 经济策略上下文
        """
        super().initialize(context)
        self.logger.info(f"资产配置策略 {self.name} 初始化完成")
    
    def get_economic_indicators(self) -> List[str]:
        """获取资产配置策略使用的经济指标"""
        return [
            'GDPC1',        # 实际GDP
            'UNRATE',       # 失业率
            'CPIAUCSL',     # CPI通胀率
            'FEDFUNDS',     # 联邦基金利率
            'DGS10',        # 10年期国债收益率
            'DGS2',         # 2年期国债收益率
            'VIXCLS',       # VIX恐慌指数
            'SP500',        # 标普500指数
            'DCOILWTICO'    # 原油价格
        ]
    
    def analyze_economic_conditions(self, economic_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        分析经济状况并确定资产配置策略
        """
        analysis = {
            'cycle_phase': 'UNKNOWN',
            'inflation_regime': 'NORMAL',
            'interest_rate_trend': 'STABLE',
            'market_volatility': 'NORMAL',
            'risk_sentiment': 'NEUTRAL',
            'recommended_weights': {},
            'confidence_score': 0.5
        }
        
        try:
            # 1. 经济周期分析
            analysis['cycle_phase'] = self.economic_context.get_economic_cycle_phase()
            
            # 2. 通胀分析
            analysis['inflation_regime'] = self._analyze_inflation(economic_data)
            
            # 3. 利率趋势分析
            analysis['interest_rate_trend'] = self._analyze_interest_rates(economic_data)
            
            # 4. 市场波动性分析
            analysis['market_volatility'] = self._analyze_market_volatility(economic_data)
            
            # 5. 风险情绪分析
            analysis['risk_sentiment'] = self._analyze_risk_sentiment(economic_data)
            
            # 6. 计算推荐权重
            analysis['recommended_weights'] = self._calculate_recommended_weights(analysis)
            
            # 7. 计算置信度
            analysis['confidence_score'] = self._calculate_confidence_score(economic_data, analysis)
            
        except Exception as e:
            self.logger.error(f"经济状况分析失败: {e}")
        
        return analysis
    
    def _analyze_inflation(self, economic_data: Dict[str, pd.DataFrame]) -> str:
        """分析通胀环境"""
        try:
            cpi_data = economic_data.get('CPIAUCSL')
            if cpi_data is None or cpi_data.empty:
                return 'NORMAL'
            
            # 计算年化通胀率
            if len(cpi_data) >= 12:
                current_cpi = cpi_data['value'].iloc[-1]
                year_ago_cpi = cpi_data['value'].iloc[-12]
                inflation_rate = (current_cpi - year_ago_cpi) / year_ago_cpi * 100
                
                if inflation_rate > 4:
                    return 'HIGH'
                elif inflation_rate < 1:
                    return 'LOW'
                else:
                    return 'NORMAL'
            
            return 'NORMAL'
            
        except Exception as e:
            self.logger.error(f"通胀分析失败: {e}")
            return 'NORMAL'
    
    def _analyze_interest_rates(self, economic_data: Dict[str, pd.DataFrame]) -> str:
        """分析利率趋势"""
        try:
            fed_funds_data = economic_data.get('FEDFUNDS')
            if fed_funds_data is None or fed_funds_data.empty:
                return 'STABLE'
            
            # 分析联邦基金利率趋势
            trend = self.economic_context.get_economic_trend('FEDFUNDS', 90)
            return trend
            
        except Exception as e:
            self.logger.error(f"利率趋势分析失败: {e}")
            return 'STABLE'
    
    def _analyze_market_volatility(self, economic_data: Dict[str, pd.DataFrame]) -> str:
        """分析市场波动性"""
        try:
            vix_data = economic_data.get('VIXCLS')
            if vix_data is None or vix_data.empty:
                return 'NORMAL'
            
            current_vix = vix_data['value'].iloc[-1]
            
            if current_vix > 30:
                return 'HIGH'
            elif current_vix < 15:
                return 'LOW'
            else:
                return 'NORMAL'
                
        except Exception as e:
            self.logger.error(f"波动性分析失败: {e}")
            return 'NORMAL'
    
    def _analyze_risk_sentiment(self, economic_data: Dict[str, pd.DataFrame]) -> str:
        """分析风险情绪"""
        try:
            # 综合多个指标判断风险情绪
            sp500_data = economic_data.get('SP500')
            vix_data = economic_data.get('VIXCLS')
            
            sentiment_score = 0
            factors = 0
            
            # 股市趋势
            if sp500_data is not None and not sp500_data.empty:
                sp500_trend = self.economic_context.get_economic_trend('SP500', 30)
                if sp500_trend == 'RISING':
                    sentiment_score += 1
                elif sp500_trend == 'FALLING':
                    sentiment_score -= 1
                factors += 1
            
            # VIX水平
            if vix_data is not None and not vix_data.empty:
                current_vix = vix_data['value'].iloc[-1]
                if current_vix < 20:
                    sentiment_score += 1
                elif current_vix > 25:
                    sentiment_score -= 1
                factors += 1
            
            # 标准化情绪评分
            if factors > 0:
                avg_sentiment = sentiment_score / factors
                if avg_sentiment > 0.3:
                    return 'RISK_ON'
                elif avg_sentiment < -0.3:
                    return 'RISK_OFF'
                else:
                    return 'NEUTRAL'
            
            return 'NEUTRAL'
            
        except Exception as e:
            self.logger.error(f"风险情绪分析失败: {e}")
            return 'NEUTRAL'
    
    def _calculate_recommended_weights(self, analysis: Dict[str, Any]) -> Dict[str, float]:
        """根据经济分析计算推荐的资产配置权重"""
        try:
            # 基础权重配置
            base_weights = {
                'stocks': 0.6,
                'bonds': 0.3,
                'commodities': 0.05,
                'cash': 0.05
            }
            
            weights = base_weights.copy()
            
            # 根据经济周期调整
            cycle_phase = analysis.get('cycle_phase', 'UNKNOWN')
            if cycle_phase == 'EXPANSION':
                weights['stocks'] += 0.1
                weights['bonds'] -= 0.05
                weights['commodities'] += 0.05
            elif cycle_phase == 'CONTRACTION':
                weights['stocks'] -= 0.15
                weights['bonds'] += 0.1
                weights['cash'] += 0.05
            elif cycle_phase == 'PEAK':
                weights['stocks'] -= 0.05
                weights['commodities'] += 0.1
                weights['cash'] += 0.05
            elif cycle_phase == 'TROUGH':
                weights['stocks'] += 0.05
                weights['bonds'] += 0.05
                weights['cash'] -= 0.1
            
            # 根据通胀环境调整
            inflation_regime = analysis.get('inflation_regime', 'NORMAL')
            if inflation_regime == 'HIGH':
                weights['commodities'] += 0.1
                weights['bonds'] -= 0.1
            elif inflation_regime == 'LOW':
                weights['bonds'] += 0.05
                weights['commodities'] -= 0.05
            
            # 根据利率趋势调整
            rate_trend = analysis.get('interest_rate_trend', 'STABLE')
            if rate_trend == 'RISING':
                weights['bonds'] -= 0.1
                weights['stocks'] += 0.05
                weights['cash'] += 0.05
            elif rate_trend == 'FALLING':
                weights['bonds'] += 0.1
                weights['cash'] -= 0.05
                weights['stocks'] -= 0.05
            
            # 根据风险情绪调整
            risk_sentiment = analysis.get('risk_sentiment', 'NEUTRAL')
            if risk_sentiment == 'RISK_ON':
                weights['stocks'] += 0.1
                weights['cash'] -= 0.05
                weights['bonds'] -= 0.05
            elif risk_sentiment == 'RISK_OFF':
                weights['stocks'] -= 0.1
                weights['cash'] += 0.1
                weights['bonds'] += 0.05
            
            # 确保权重在合理范围内
            weights = self._normalize_weights(weights)
            
            return weights
            
        except Exception as e:
            self.logger.error(f"计算推荐权重失败: {e}")
            return {'stocks': 0.6, 'bonds': 0.3, 'commodities': 0.05, 'cash': 0.05}
    
    def _normalize_weights(self, weights: Dict[str, float]) -> Dict[str, float]:
        """标准化权重，确保总和为1且在合理范围内"""
        try:
            # 应用权重范围限制
            for asset, weight in weights.items():
                if asset in self.weight_ranges:
                    min_weight = self.weight_ranges[asset]['min']
                    max_weight = self.weight_ranges[asset]['max']
                    weights[asset] = max(min_weight, min(max_weight, weight))
            
            # 标准化使总和为1
            total_weight = sum(weights.values())
            if total_weight > 0:
                for asset in weights:
                    weights[asset] /= total_weight
            
            return weights
            
        except Exception as e:
            self.logger.error(f"权重标准化失败: {e}")
            return weights
    
    def _calculate_confidence_score(self, economic_data: Dict[str, pd.DataFrame], 
                                  analysis: Dict[str, Any]) -> float:
        """计算分析结果的置信度"""
        try:
            confidence = 0.5  # 基础置信度
            
            # 数据完整性评分
            available_indicators = sum(1 for key in self.get_economic_indicators() 
                                     if key in economic_data and not economic_data[key].empty)
            data_completeness = available_indicators / len(self.get_economic_indicators())
            confidence += (data_completeness - 0.5) * 0.3
            
            # 分析一致性评分
            consistent_signals = 0
            total_signals = 0
            
            # 检查各种分析结果的一致性
            if analysis.get('cycle_phase') != 'UNKNOWN':
                total_signals += 1
                if analysis.get('risk_sentiment') != 'NEUTRAL':
                    consistent_signals += 1
            
            if total_signals > 0:
                consistency_score = consistent_signals / total_signals
                confidence += (consistency_score - 0.5) * 0.2
            
            return max(0.0, min(1.0, confidence))
            
        except Exception as e:
            self.logger.error(f"置信度计算失败: {e}")
            return 0.5
    
    def on_data(self, data: Union[MarketData, Dict[str, MarketData]]) -> List[Signal]:
        """
        处理市场数据并生成资产配置信号
        """
        try:
            # 获取经济数据
            economic_data = self.get_economic_data_for_analysis()
            if not economic_data:
                self.logger.warning("无法获取经济数据")
                return []
            
            # 分析经济状况
            economic_analysis = self.analyze_economic_conditions(economic_data)
            
            # 生成配置调整信号
            signals = self._generate_allocation_signals(data, economic_analysis)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"数据处理失败: {e}")
            return []
    
    def _generate_allocation_signals(self, market_data: Union[MarketData, Dict[str, MarketData]],
                                   economic_analysis: Dict[str, Any]) -> List[Signal]:
        """生成资产配置调整信号"""
        signals = []
        
        try:
            recommended_weights = economic_analysis.get('recommended_weights', {})
            confidence = economic_analysis.get('confidence_score', 0.5)
            
            # 只有在置信度足够高时才生成信号
            min_confidence = self.get_parameter('min_confidence', 0.6)
            if confidence < min_confidence:
                return signals
            
            # 为每个资产类别生成信号
            for asset_class, target_weight in recommended_weights.items():
                if asset_class == 'cash':
                    continue  # 现金不需要交易信号
                
                symbols = self.asset_symbols.get(asset_class, [])
                for symbol in symbols:
                    # 获取当前权重
                    current_weight = self.current_weights.get(symbol, 0.0)
                    weight_diff = target_weight - current_weight
                    
                    # 只有权重变化超过阈值时才生成信号
                    min_weight_change = self.get_parameter('min_weight_change', 0.05)
                    if abs(weight_diff) > min_weight_change:
                        # 确定市场数据
                        if isinstance(market_data, dict):
                            symbol_data = market_data.get(symbol)
                        else:
                            symbol_data = market_data if market_data.symbol == symbol else None
                        
                        if symbol_data:
                            action = 'BUY' if weight_diff > 0 else 'SELL'
                            
                            from ..signals import SignalType
                            
                            signal_type = SignalType.BUY if action == 'BUY' else SignalType.SELL
                            
                            signal = Signal(
                                symbol=symbol,
                                signal_type=signal_type,
                                quantity=abs(weight_diff) * 1000,  # 假设总资产为1000单位
                                price=symbol_data.close,
                                timestamp=symbol_data.timestamp,
                                confidence=confidence,
                                strategy_name=self.name,
                                metadata={
                                    'asset_class': asset_class,
                                    'target_weight': target_weight,
                                    'current_weight': current_weight,
                                    'weight_change': weight_diff,
                                    'cycle_phase': economic_analysis.get('cycle_phase'),
                                    'signal_category': 'ASSET_ALLOCATION'
                                }
                            )
                            
                            signals.append(signal)
                            
                            # 更新当前权重
                            self.current_weights[symbol] = target_weight
            
        except Exception as e:
            self.logger.error(f"生成配置信号失败: {e}")
        
        return signals
    
    def get_parameter_definitions(self) -> Dict[str, StrategyParameter]:
        """获取策略参数定义"""
        base_params = super().get_parameter_definitions()
        
        asset_allocation_params = {
            'min_confidence': StrategyParameter(
                name='min_confidence',
                param_type=ParameterType.FLOAT,
                default_value=0.6,
                description='最小置信度阈值',
                min_value=0.0,
                max_value=1.0
            ),
            'min_weight_change': StrategyParameter(
                name='min_weight_change',
                param_type=ParameterType.FLOAT,
                default_value=0.05,
                description='最小权重变化阈值',
                min_value=0.01,
                max_value=0.2
            ),
            'rebalance_frequency': StrategyParameter(
                name='rebalance_frequency',
                param_type=ParameterType.INTEGER,
                default_value=30,
                description='再平衡频率（天）',
                min_value=7,
                max_value=90
            )
        }
        
        base_params.update(asset_allocation_params)
        return base_params