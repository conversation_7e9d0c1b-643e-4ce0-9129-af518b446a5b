"""
Multi-market risk management system.

This module provides enhanced risk management capabilities for multi-market
trading strategies, including currency exposure limits, correlation-based
risk controls, and cross-market position limits.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import numpy as np

try:
    from ..models.portfolio import Portfolio, Position
    from ..models.trading import Order
    from .signals import Signal
    from .multi_market import CurrencyConverter
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from src.market.strategies.models.portfolio import Portfolio, Position
    from src.market.strategies.models.trading import Order
    from src.market.strategies.signals import Signal
    from src.market.strategies.multi_market import CurrencyConverter


class RiskLevel(Enum):
    """Risk level enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RiskLimit:
    """Risk limit configuration."""
    name: str
    limit_type: str  # 'percentage', 'absolute', 'ratio'
    value: float
    currency: str = 'USD'
    scope: str = 'portfolio'  # 'portfolio', 'market', 'symbol'
    enabled: bool = True


@dataclass
class RiskViolation:
    """Risk violation record."""
    rule_name: str
    violation_type: str
    current_value: float
    limit_value: float
    severity: RiskLevel
    timestamp: datetime
    details: Dict[str, Any]


@dataclass
class MarketExposure:
    """Market exposure information."""
    market: str
    total_value: float
    currency: str
    positions: Dict[str, float]  # symbol -> value
    percentage_of_portfolio: float


@dataclass
class CurrencyExposure:
    """Currency exposure information."""
    currency: str
    total_value: float
    value_usd: float
    positions: Dict[str, float]  # symbol -> value
    percentage_of_portfolio: float


class MultiMarketRiskManager:
    """Enhanced risk manager for multi-market strategies."""
    
    def __init__(self, currency_converter: Optional[CurrencyConverter] = None):
        self.currency_converter = currency_converter or CurrencyConverter()
        self.risk_limits: Dict[str, RiskLimit] = {}
        self.risk_violations: List[RiskViolation] = []
        self.logger = logging.getLogger(f"{__name__}.MultiMarketRiskManager")
        
        # Risk monitoring state
        self.last_risk_check = None
        self.risk_metrics_history: List[Dict[str, Any]] = []
        self.correlation_matrix: Optional[pd.DataFrame] = None
        self.correlation_update_time: Optional[datetime] = None
        
        # Initialize default risk limits
        self._initialize_default_limits()
    
    def _initialize_default_limits(self) -> None:
        """Initialize default risk limits."""
        default_limits = [
            RiskLimit(
                name='max_portfolio_risk',
                limit_type='percentage',
                value=0.02,  # 2% max portfolio risk per trade
                scope='portfolio'
            ),
            RiskLimit(
                name='max_market_exposure',
                limit_type='percentage',
                value=0.6,  # 60% max exposure to any single market
                scope='market'
            ),
            RiskLimit(
                name='max_currency_exposure',
                limit_type='percentage',
                value=0.8,  # 80% max exposure to any single currency
                scope='portfolio'
            ),
            RiskLimit(
                name='max_position_size',
                limit_type='percentage',
                value=0.1,  # 10% max position size
                scope='portfolio'
            ),
            RiskLimit(
                name='max_correlation_exposure',
                limit_type='ratio',
                value=0.7,  # Max 70% in highly correlated positions
                scope='portfolio'
            ),
            RiskLimit(
                name='max_drawdown',
                limit_type='percentage',
                value=0.15,  # 15% max drawdown
                scope='portfolio'
            ),
            RiskLimit(
                name='max_leverage',
                limit_type='ratio',
                value=2.0,  # 2:1 max leverage
                scope='portfolio'
            ),
            RiskLimit(
                name='max_overnight_exposure',
                limit_type='percentage',
                value=0.3,  # 30% max overnight exposure
                scope='portfolio'
            )
        ]
        
        for limit in default_limits:
            self.risk_limits[limit.name] = limit
    
    def add_risk_limit(self, limit: RiskLimit) -> None:
        """Add or update a risk limit."""
        self.risk_limits[limit.name] = limit
        self.logger.info(f"Added risk limit: {limit.name} = {limit.value}")
    
    def remove_risk_limit(self, limit_name: str) -> bool:
        """Remove a risk limit."""
        if limit_name in self.risk_limits:
            del self.risk_limits[limit_name]
            self.logger.info(f"Removed risk limit: {limit_name}")
            return True
        return False
    
    def check_order_risk(self, order: Order, portfolio: Portfolio) -> Tuple[bool, List[RiskViolation]]:
        """Check if an order violates risk limits."""
        violations = []
        
        try:
            # Simulate portfolio after order execution
            simulated_portfolio = self._simulate_order_execution(order, portfolio)
            
            # Check all risk limits
            for limit_name, limit in self.risk_limits.items():
                if not limit.enabled:
                    continue
                
                violation = self._check_risk_limit(limit, simulated_portfolio, order)
                if violation:
                    violations.append(violation)
            
            # Store violations
            self.risk_violations.extend(violations)
            
            # Determine if order should be rejected
            critical_violations = [v for v in violations if v.severity == RiskLevel.CRITICAL]
            should_reject = len(critical_violations) > 0
            
            if should_reject:
                self.logger.warning(f"Order rejected due to {len(critical_violations)} critical risk violations")
            
            return not should_reject, violations
            
        except Exception as e:
            self.logger.error(f"Error checking order risk: {e}")
            return False, [RiskViolation(
                rule_name='system_error',
                violation_type='error',
                current_value=0,
                limit_value=0,
                severity=RiskLevel.CRITICAL,
                timestamp=datetime.now(timezone.utc),
                details={'error': str(e)}
            )]
    
    def _simulate_order_execution(self, order: Order, portfolio: Portfolio) -> Portfolio:
        """Simulate portfolio state after order execution."""
        # Create a copy of the portfolio
        simulated_portfolio = Portfolio(
            initial_capital=portfolio.initial_capital,
            cash=portfolio.cash,
            positions=portfolio.positions.copy(),
            total_value=portfolio.total_value,
            unrealized_pnl=portfolio.unrealized_pnl,
            realized_pnl=portfolio.realized_pnl
        )
        
        # Calculate order value
        order_value = order.quantity * order.price
        
        if order.side.upper() == 'BUY':
            # Reduce cash, add/increase position
            simulated_portfolio.cash -= order_value
            
            if order.symbol in simulated_portfolio.positions:
                existing_pos = simulated_portfolio.positions[order.symbol]
                new_quantity = existing_pos.quantity + order.quantity
                new_avg_price = ((existing_pos.quantity * existing_pos.avg_price) + 
                               (order.quantity * order.price)) / new_quantity
                
                simulated_portfolio.positions[order.symbol] = Position(
                    symbol=order.symbol,
                    quantity=new_quantity,
                    avg_price=new_avg_price,
                    market_value=new_quantity * order.price,
                    unrealized_pnl=0  # Simplified
                )
            else:
                simulated_portfolio.positions[order.symbol] = Position(
                    symbol=order.symbol,
                    quantity=order.quantity,
                    avg_price=order.price,
                    market_value=order_value,
                    unrealized_pnl=0
                )
        
        else:  # SELL
            # Increase cash, reduce/remove position
            simulated_portfolio.cash += order_value
            
            if order.symbol in simulated_portfolio.positions:
                existing_pos = simulated_portfolio.positions[order.symbol]
                new_quantity = existing_pos.quantity - order.quantity
                
                if new_quantity <= 0:
                    del simulated_portfolio.positions[order.symbol]
                else:
                    simulated_portfolio.positions[order.symbol] = Position(
                        symbol=order.symbol,
                        quantity=new_quantity,
                        avg_price=existing_pos.avg_price,
                        market_value=new_quantity * order.price,
                        unrealized_pnl=0
                    )
        
        # Recalculate total value
        positions_value = sum(pos.market_value for pos in simulated_portfolio.positions.values())
        simulated_portfolio.total_value = simulated_portfolio.cash + positions_value
        
        return simulated_portfolio
    
    def _check_risk_limit(self, limit: RiskLimit, portfolio: Portfolio, 
                         order: Optional[Order] = None) -> Optional[RiskViolation]:
        """Check a specific risk limit."""
        try:
            if limit.name == 'max_portfolio_risk':
                return self._check_portfolio_risk_limit(limit, portfolio, order)
            elif limit.name == 'max_market_exposure':
                return self._check_market_exposure_limit(limit, portfolio)
            elif limit.name == 'max_currency_exposure':
                return self._check_currency_exposure_limit(limit, portfolio)
            elif limit.name == 'max_position_size':
                return self._check_position_size_limit(limit, portfolio, order)
            elif limit.name == 'max_correlation_exposure':
                return self._check_correlation_limit(limit, portfolio)
            elif limit.name == 'max_drawdown':
                return self._check_drawdown_limit(limit, portfolio)
            elif limit.name == 'max_leverage':
                return self._check_leverage_limit(limit, portfolio)
            elif limit.name == 'max_overnight_exposure':
                return self._check_overnight_exposure_limit(limit, portfolio)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error checking risk limit {limit.name}: {e}")
            return None
    
    def _check_portfolio_risk_limit(self, limit: RiskLimit, portfolio: Portfolio, 
                                  order: Optional[Order]) -> Optional[RiskViolation]:
        """Check portfolio risk limit."""
        if not order:
            return None
        
        order_value = abs(order.quantity * order.price)
        portfolio_risk = order_value / portfolio.total_value if portfolio.total_value > 0 else 0
        
        if portfolio_risk > limit.value:
            return RiskViolation(
                rule_name=limit.name,
                violation_type='portfolio_risk',
                current_value=portfolio_risk,
                limit_value=limit.value,
                severity=RiskLevel.HIGH if portfolio_risk > limit.value * 1.5 else RiskLevel.MEDIUM,
                timestamp=datetime.now(timezone.utc),
                details={
                    'order_value': order_value,
                    'portfolio_value': portfolio.total_value,
                    'symbol': order.symbol
                }
            )
        
        return None
    
    def _check_market_exposure_limit(self, limit: RiskLimit, portfolio: Portfolio) -> Optional[RiskViolation]:
        """Check market exposure limits."""
        market_exposures = self.calculate_market_exposures(portfolio)
        
        for market, exposure in market_exposures.items():
            if exposure.percentage_of_portfolio > limit.value:
                return RiskViolation(
                    rule_name=limit.name,
                    violation_type='market_exposure',
                    current_value=exposure.percentage_of_portfolio,
                    limit_value=limit.value,
                    severity=RiskLevel.HIGH if exposure.percentage_of_portfolio > limit.value * 1.2 else RiskLevel.MEDIUM,
                    timestamp=datetime.now(timezone.utc),
                    details={
                        'market': market,
                        'exposure_value': exposure.total_value,
                        'portfolio_value': portfolio.total_value
                    }
                )
        
        return None
    
    def _check_currency_exposure_limit(self, limit: RiskLimit, portfolio: Portfolio) -> Optional[RiskViolation]:
        """Check currency exposure limits."""
        currency_exposures = self.calculate_currency_exposures(portfolio)
        
        for currency, exposure in currency_exposures.items():
            if exposure.percentage_of_portfolio > limit.value:
                return RiskViolation(
                    rule_name=limit.name,
                    violation_type='currency_exposure',
                    current_value=exposure.percentage_of_portfolio,
                    limit_value=limit.value,
                    severity=RiskLevel.MEDIUM,
                    timestamp=datetime.now(timezone.utc),
                    details={
                        'currency': currency,
                        'exposure_value': exposure.total_value,
                        'exposure_value_usd': exposure.value_usd,
                        'portfolio_value': portfolio.total_value
                    }
                )
        
        return None
    
    def _check_position_size_limit(self, limit: RiskLimit, portfolio: Portfolio, 
                                 order: Optional[Order]) -> Optional[RiskViolation]:
        """Check individual position size limits."""
        for symbol, position in portfolio.positions.items():
            position_percentage = position.market_value / portfolio.total_value if portfolio.total_value > 0 else 0
            
            if position_percentage > limit.value:
                return RiskViolation(
                    rule_name=limit.name,
                    violation_type='position_size',
                    current_value=position_percentage,
                    limit_value=limit.value,
                    severity=RiskLevel.MEDIUM,
                    timestamp=datetime.now(timezone.utc),
                    details={
                        'symbol': symbol,
                        'position_value': position.market_value,
                        'portfolio_value': portfolio.total_value
                    }
                )
        
        return None
    
    def _check_correlation_limit(self, limit: RiskLimit, portfolio: Portfolio) -> Optional[RiskViolation]:
        """Check correlation-based risk limits."""
        if not self.correlation_matrix or len(portfolio.positions) < 2:
            return None
        
        try:
            # Calculate correlation-weighted exposure
            symbols = list(portfolio.positions.keys())
            total_correlated_exposure = 0
            
            for i, symbol1 in enumerate(symbols):
                for j, symbol2 in enumerate(symbols[i+1:], i+1):
                    if symbol1 in self.correlation_matrix.index and symbol2 in self.correlation_matrix.columns:
                        correlation = self.correlation_matrix.loc[symbol1, symbol2]
                        
                        if abs(correlation) > 0.7:  # High correlation threshold
                            pos1_value = portfolio.positions[symbol1].market_value
                            pos2_value = portfolio.positions[symbol2].market_value
                            correlated_exposure = (pos1_value + pos2_value) * abs(correlation)
                            total_correlated_exposure += correlated_exposure
            
            correlation_percentage = total_correlated_exposure / portfolio.total_value if portfolio.total_value > 0 else 0
            
            if correlation_percentage > limit.value:
                return RiskViolation(
                    rule_name=limit.name,
                    violation_type='correlation_exposure',
                    current_value=correlation_percentage,
                    limit_value=limit.value,
                    severity=RiskLevel.MEDIUM,
                    timestamp=datetime.now(timezone.utc),
                    details={
                        'correlated_exposure': total_correlated_exposure,
                        'portfolio_value': portfolio.total_value
                    }
                )
        
        except Exception as e:
            self.logger.warning(f"Error checking correlation limit: {e}")
        
        return None
    
    def _check_drawdown_limit(self, limit: RiskLimit, portfolio: Portfolio) -> Optional[RiskViolation]:
        """Check maximum drawdown limit."""
        # This would require historical portfolio values to calculate properly
        # For now, we'll use unrealized PnL as a proxy
        if portfolio.unrealized_pnl < 0:
            drawdown_percentage = abs(portfolio.unrealized_pnl) / portfolio.total_value if portfolio.total_value > 0 else 0
            
            if drawdown_percentage > limit.value:
                return RiskViolation(
                    rule_name=limit.name,
                    violation_type='drawdown',
                    current_value=drawdown_percentage,
                    limit_value=limit.value,
                    severity=RiskLevel.HIGH,
                    timestamp=datetime.now(timezone.utc),
                    details={
                        'unrealized_pnl': portfolio.unrealized_pnl,
                        'portfolio_value': portfolio.total_value
                    }
                )
        
        return None
    
    def _check_leverage_limit(self, limit: RiskLimit, portfolio: Portfolio) -> Optional[RiskViolation]:
        """Check leverage limit."""
        total_position_value = sum(abs(pos.market_value) for pos in portfolio.positions.values())
        leverage = total_position_value / portfolio.total_value if portfolio.total_value > 0 else 0
        
        if leverage > limit.value:
            return RiskViolation(
                rule_name=limit.name,
                violation_type='leverage',
                current_value=leverage,
                limit_value=limit.value,
                severity=RiskLevel.HIGH if leverage > limit.value * 1.5 else RiskLevel.MEDIUM,
                timestamp=datetime.now(timezone.utc),
                details={
                    'total_position_value': total_position_value,
                    'portfolio_value': portfolio.total_value
                }
            )
        
        return None
    
    def _check_overnight_exposure_limit(self, limit: RiskLimit, portfolio: Portfolio) -> Optional[RiskViolation]:
        """Check overnight exposure limit."""
        # This would require market session information to determine what constitutes "overnight"
        # For now, we'll assume all positions are overnight exposure
        total_exposure = sum(pos.market_value for pos in portfolio.positions.values())
        exposure_percentage = total_exposure / portfolio.total_value if portfolio.total_value > 0 else 0
        
        if exposure_percentage > limit.value:
            return RiskViolation(
                rule_name=limit.name,
                violation_type='overnight_exposure',
                current_value=exposure_percentage,
                limit_value=limit.value,
                severity=RiskLevel.MEDIUM,
                timestamp=datetime.now(timezone.utc),
                details={
                    'total_exposure': total_exposure,
                    'portfolio_value': portfolio.total_value
                }
            )
        
        return None
    
    def calculate_market_exposures(self, portfolio: Portfolio) -> Dict[str, MarketExposure]:
        """Calculate exposure by market."""
        market_exposures = {}
        
        # This would require market information for each symbol
        # For now, we'll create a simplified version
        for symbol, position in portfolio.positions.items():
            # Determine market from symbol (simplified logic)
            if symbol.endswith('.SS') or symbol.endswith('.SZ'):
                market = 'CN'
                currency = 'CNY'
            elif 'USDT' in symbol or 'BTC' in symbol or 'ETH' in symbol:
                market = 'CRYPTO'
                currency = 'USDT'
            else:
                market = 'US'
                currency = 'USD'
            
            if market not in market_exposures:
                market_exposures[market] = MarketExposure(
                    market=market,
                    total_value=0,
                    currency=currency,
                    positions={},
                    percentage_of_portfolio=0
                )
            
            market_exposures[market].total_value += position.market_value
            market_exposures[market].positions[symbol] = position.market_value
        
        # Calculate percentages
        for exposure in market_exposures.values():
            exposure.percentage_of_portfolio = (exposure.total_value / portfolio.total_value 
                                              if portfolio.total_value > 0 else 0)
        
        return market_exposures
    
    def calculate_currency_exposures(self, portfolio: Portfolio) -> Dict[str, CurrencyExposure]:
        """Calculate exposure by currency."""
        currency_exposures = {}
        
        for symbol, position in portfolio.positions.items():
            # Determine currency from symbol (simplified logic)
            if symbol.endswith('.SS') or symbol.endswith('.SZ'):
                currency = 'CNY'
            elif 'USDT' in symbol:
                currency = 'USDT'
            elif 'BTC' in symbol:
                currency = 'BTC'
            elif 'ETH' in symbol:
                currency = 'ETH'
            else:
                currency = 'USD'
            
            if currency not in currency_exposures:
                currency_exposures[currency] = CurrencyExposure(
                    currency=currency,
                    total_value=0,
                    value_usd=0,
                    positions={},
                    percentage_of_portfolio=0
                )
            
            currency_exposures[currency].total_value += position.market_value
            currency_exposures[currency].positions[symbol] = position.market_value
        
        # Convert to USD and calculate percentages
        for currency, exposure in currency_exposures.items():
            try:
                exposure.value_usd = self.currency_converter.convert(
                    exposure.total_value, currency, 'USD'
                )
            except:
                exposure.value_usd = exposure.total_value  # Fallback
            
            exposure.percentage_of_portfolio = (exposure.value_usd / portfolio.total_value 
                                              if portfolio.total_value > 0 else 0)
        
        return currency_exposures
    
    def update_correlation_matrix(self, correlation_data: pd.DataFrame) -> None:
        """Update the correlation matrix for risk calculations."""
        self.correlation_matrix = correlation_data
        self.correlation_update_time = datetime.now(timezone.utc)
        self.logger.info(f"Updated correlation matrix with {len(correlation_data)} symbols")
    
    def get_risk_summary(self, portfolio: Portfolio) -> Dict[str, Any]:
        """Get comprehensive risk summary."""
        market_exposures = self.calculate_market_exposures(portfolio)
        currency_exposures = self.calculate_currency_exposures(portfolio)
        
        # Calculate key risk metrics
        total_position_value = sum(abs(pos.market_value) for pos in portfolio.positions.values())
        leverage = total_position_value / portfolio.total_value if portfolio.total_value > 0 else 0
        
        recent_violations = [
            v for v in self.risk_violations 
            if (datetime.now(timezone.utc) - v.timestamp).total_seconds() < 3600  # Last hour
        ]
        
        return {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'portfolio_value': portfolio.total_value,
            'cash': portfolio.cash,
            'leverage': leverage,
            'total_positions': len(portfolio.positions),
            'market_exposures': {
                market: {
                    'value': exp.total_value,
                    'percentage': exp.percentage_of_portfolio,
                    'currency': exp.currency
                }
                for market, exp in market_exposures.items()
            },
            'currency_exposures': {
                currency: {
                    'value': exp.total_value,
                    'value_usd': exp.value_usd,
                    'percentage': exp.percentage_of_portfolio
                }
                for currency, exp in currency_exposures.items()
            },
            'recent_violations': len(recent_violations),
            'total_violations': len(self.risk_violations),
            'risk_limits': {
                name: {
                    'value': limit.value,
                    'enabled': limit.enabled,
                    'type': limit.limit_type
                }
                for name, limit in self.risk_limits.items()
            }
        }
    
    def get_recent_violations(self, hours: int = 24) -> List[RiskViolation]:
        """Get recent risk violations."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        return [v for v in self.risk_violations if v.timestamp > cutoff_time]
    
    def clear_old_violations(self, days: int = 7) -> None:
        """Clear old risk violations."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=days)
        old_count = len(self.risk_violations)
        self.risk_violations = [v for v in self.risk_violations if v.timestamp > cutoff_time]
        new_count = len(self.risk_violations)
        
        if old_count > new_count:
            self.logger.info(f"Cleared {old_count - new_count} old risk violations")