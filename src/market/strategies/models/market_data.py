# Market Data Models for Strategies
# Re-export from domain models for backward compatibility

from src.domain.models.market_data import (
    MarketData,
    UnifiedMarketData, 
    EconomicData,
    MarketInfo
)
# Define missing types locally for compatibility
from enum import Enum

class MarketType(Enum):
    """Supported market types."""
    US = "US"
    CN = "CN"
    CRYPTO = "CRYPTO"
    ECONOMIC = "ECONOMIC"

class DataQuality(Enum):
    HIGH = "high"
    MEDIUM = "medium" 
    LOW = "low"

class Interval(Enum):
    MIN_1 = "1m"
    MIN_5 = "5m"
    MIN_15 = "15m"
    HOUR_1 = "1h"
    DAY_1 = "1d"

class ValidationError(Exception):
    pass

__all__ = [
    'MarketData',
    'UnifiedMarketData',
    'EconomicData', 
    'MarketInfo',
    'MarketType',
    'DataQuality',
    'Interval',
    'ValidationError'
]
