"""
多市场配置管理工具模块

本模块为多市场交易策略提供配置管理功能，包括市场交易时段、
交易时间、手续费结构和滑点模型的统一管理。

主要功能：
- 市场交易时段配置和管理
- 多时区交易时间处理
- 手续费结构配置
- 滑点模型参数管理
- 市场节假日管理
- 配置文件的加载和保存

支持的市场：
- 美股市场 (US): NYSE, NASDAQ
- A股市场 (CN): SSE, SZSE  
- 加密货币市场 (CRYPTO): Binance, Coinbase

配置管理特性：
- YAML格式配置文件
- 动态配置加载和更新
- 默认配置自动初始化
- 配置验证和错误处理
- 多市场统一接口

作者: 量化交易系统开发团队
版本: 2.0.0
创建日期: 2024-01-01
最后修改: 2024-07-31

使用示例:
    # 获取市场配置管理器
    config_manager = MarketConfigManager()
    
    # 获取市场交易时段
    us_session = config_manager.get_market_session('US', 'NYSE')
    
    # 获取手续费结构
    fees = config_manager.get_market_fees('US', 'NYSE')
    
    # 检查是否为节假日
    is_holiday = config_manager.is_market_holiday('US', datetime.now())
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, time
from dataclasses import dataclass, field
import json
import yaml
from pathlib import Path

try:
    from .multi_market import MarketSession, MarketFees, SlippageModel
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from src.market.strategies.multi_market import MarketSession, MarketFees, SlippageModel


logger = logging.getLogger(__name__)


@dataclass
class MarketHoliday:
    """Market holiday information."""
    date: datetime
    name: str
    market: str
    is_partial_day: bool = False
    partial_hours: Optional[Dict[str, str]] = None


class MarketConfigManager:
    """
    市场配置管理器
    
    负责管理多市场的配置信息，包括交易时段、手续费结构、滑点模型和节假日安排。
    提供统一的配置接口，支持动态加载和更新配置。
    
    配置管理功能：
    - 从YAML文件加载配置
    - 提供默认配置初始化
    - 支持配置的动态更新
    - 配置验证和错误处理
    - 多市场统一访问接口
    
    数据结构：
    - sessions: 市场交易时段配置
    - fees: 手续费结构配置
    - slippage_models: 滑点模型配置
    - holidays: 节假日安排配置
    
    使用场景：
    - 策略初始化时获取市场配置
    - 交易时间判断和验证
    - 手续费和滑点计算
    - 节假日检查和处理
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化市场配置管理器
        
        Args:
            config_path: 配置文件路径，默认为 "config/market_config.yaml"
        """
        self.config_path = config_path or "config/market_config.yaml"
        
        # 初始化配置存储字典
        self.sessions: Dict[str, MarketSession] = {}           # 交易时段配置
        self.fees: Dict[str, MarketFees] = {}                  # 手续费配置
        self.slippage_models: Dict[str, SlippageModel] = {}    # 滑点模型配置
        self.holidays: Dict[str, List[MarketHoliday]] = {}     # 节假日配置
        
        # 加载配置文件
        self._load_configuration()
        
        # 如果配置为空，初始化默认配置
        if not self.sessions:
            self._initialize_default_sessions()
        if not self.fees:
            self._initialize_default_fees()
        if not self.slippage_models:
            self._initialize_default_slippage_models()
    
    def _load_configuration(self) -> None:
        """Load market configuration from file."""
        try:
            config_file = Path(self.config_path)
            if not config_file.exists():
                logger.warning(f"Market config file not found: {self.config_path}")
                return
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # Load market sessions
            if 'sessions' in config_data:
                for key, session_data in config_data['sessions'].items():
                    self.sessions[key] = MarketSession(**session_data)
            
            # Load fee structures
            if 'fees' in config_data:
                for key, fee_data in config_data['fees'].items():
                    self.fees[key] = MarketFees(**fee_data)
            
            # Load slippage models
            if 'slippage_models' in config_data:
                for key, slippage_data in config_data['slippage_models'].items():
                    self.slippage_models[key] = SlippageModel(**slippage_data)
            
            # Load holidays
            if 'holidays' in config_data:
                for market, holiday_list in config_data['holidays'].items():
                    self.holidays[market] = []
                    for holiday_data in holiday_list:
                        holiday = MarketHoliday(
                            date=datetime.fromisoformat(holiday_data['date']),
                            name=holiday_data['name'],
                            market=market,
                            is_partial_day=holiday_data.get('is_partial_day', False),
                            partial_hours=holiday_data.get('partial_hours')
                        )
                        self.holidays[market].append(holiday)
            
            logger.info(f"Loaded market configuration from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load market configuration: {e}")
    
    def _initialize_default_sessions(self) -> None:
        """Initialize default market sessions."""
        default_sessions = {
            'US_NYSE': MarketSession(
                market='US',
                exchange='NYSE',
                timezone='US/Eastern',
                open_time='09:30',
                close_time='16:00',
                trading_days=[0, 1, 2, 3, 4]  # Monday to Friday
            ),
            'US_NASDAQ': MarketSession(
                market='US',
                exchange='NASDAQ',
                timezone='US/Eastern',
                open_time='09:30',
                close_time='16:00',
                trading_days=[0, 1, 2, 3, 4]
            ),
            'CN_SSE': MarketSession(
                market='CN',
                exchange='SSE',
                timezone='Asia/Shanghai',
                open_time='09:30',
                close_time='15:00',
                trading_days=[0, 1, 2, 3, 4]
            ),
            'CN_SZSE': MarketSession(
                market='CN',
                exchange='SZSE',
                timezone='Asia/Shanghai',
                open_time='09:30',
                close_time='15:00',
                trading_days=[0, 1, 2, 3, 4]
            ),
            'CRYPTO_BINANCE': MarketSession(
                market='CRYPTO',
                exchange='BINANCE',
                timezone='UTC',
                open_time='00:00',
                close_time='23:59',
                is_24h=True,
                trading_days=list(range(7))  # All days
            ),
            'CRYPTO_COINBASE': MarketSession(
                market='CRYPTO',
                exchange='COINBASE',
                timezone='UTC',
                open_time='00:00',
                close_time='23:59',
                is_24h=True,
                trading_days=list(range(7))
            )
        }
        
        self.sessions.update(default_sessions)
        logger.info("Initialized default market sessions")
    
    def _initialize_default_fees(self) -> None:
        """Initialize default fee structures."""
        default_fees = {
            'US_NYSE': MarketFees(
                market='US',
                exchange='NYSE',
                maker_fee=0.0025,  # 0.25%
                taker_fee=0.0035,  # 0.35%
                min_fee=1.0,
                currency='USD'
            ),
            'US_NASDAQ': MarketFees(
                market='US',
                exchange='NASDAQ',
                maker_fee=0.0025,
                taker_fee=0.0035,
                min_fee=1.0,
                currency='USD'
            ),
            'CN_SSE': MarketFees(
                market='CN',
                exchange='SSE',
                maker_fee=0.0003,  # 0.03%
                taker_fee=0.0003,
                min_fee=5.0,
                currency='CNY'
            ),
            'CN_SZSE': MarketFees(
                market='CN',
                exchange='SZSE',
                maker_fee=0.0003,
                taker_fee=0.0003,
                min_fee=5.0,
                currency='CNY'
            ),
            'CRYPTO_BINANCE': MarketFees(
                market='CRYPTO',
                exchange='BINANCE',
                maker_fee=0.001,  # 0.1%
                taker_fee=0.001,
                min_fee=0.0,
                currency='USDT'
            ),
            'CRYPTO_COINBASE': MarketFees(
                market='CRYPTO',
                exchange='COINBASE',
                maker_fee=0.005,  # 0.5%
                taker_fee=0.005,
                min_fee=0.0,
                currency='USD'
            )
        }
        
        self.fees.update(default_fees)
        logger.info("Initialized default fee structures")
    
    def _initialize_default_slippage_models(self) -> None:
        """Initialize default slippage models."""
        default_slippage = {
            'US_NYSE': SlippageModel(
                market='US',
                exchange='NYSE',
                base_slippage=0.0005,  # 0.05%
                volume_impact=0.000001,
                volatility_multiplier=1.2,
                max_slippage=0.01
            ),
            'US_NASDAQ': SlippageModel(
                market='US',
                exchange='NASDAQ',
                base_slippage=0.0005,
                volume_impact=0.000001,
                volatility_multiplier=1.2,
                max_slippage=0.01
            ),
            'CN_SSE': SlippageModel(
                market='CN',
                exchange='SSE',
                base_slippage=0.001,  # 0.1%
                volume_impact=0.000002,
                volatility_multiplier=1.5,
                max_slippage=0.02
            ),
            'CN_SZSE': SlippageModel(
                market='CN',
                exchange='SZSE',
                base_slippage=0.001,
                volume_impact=0.000002,
                volatility_multiplier=1.5,
                max_slippage=0.02
            ),
            'CRYPTO_BINANCE': SlippageModel(
                market='CRYPTO',
                exchange='BINANCE',
                base_slippage=0.0002,  # 0.02%
                volume_impact=0.0000005,
                volatility_multiplier=2.0,
                max_slippage=0.05
            ),
            'CRYPTO_COINBASE': SlippageModel(
                market='CRYPTO',
                exchange='COINBASE',
                base_slippage=0.0005,
                volume_impact=0.000001,
                volatility_multiplier=1.8,
                max_slippage=0.03
            )
        }
        
        self.slippage_models.update(default_slippage)
        logger.info("Initialized default slippage models")
    
    def get_market_session(self, market: str, exchange: str = None) -> Optional[MarketSession]:
        """Get market session information."""
        if exchange:
            key = f"{market}_{exchange}"
            if key in self.sessions:
                return self.sessions[key]
        
        # Return first session for the market
        for key, session in self.sessions.items():
            if session.market == market:
                return session
        
        return None
    
    def get_market_fees(self, market: str, exchange: str = None) -> Optional[MarketFees]:
        """Get market fee structure."""
        if exchange:
            key = f"{market}_{exchange}"
            if key in self.fees:
                return self.fees[key]
        
        # Return first fee structure for the market
        for key, fees in self.fees.items():
            if fees.market == market:
                return fees
        
        return None
    
    def get_slippage_model(self, market: str, exchange: str = None) -> Optional[SlippageModel]:
        """Get slippage model for market."""
        if exchange:
            key = f"{market}_{exchange}"
            if key in self.slippage_models:
                return self.slippage_models[key]
        
        # Return first slippage model for the market
        for key, model in self.slippage_models.items():
            if model.market == market:
                return model
        
        return None
    
    def is_market_holiday(self, market: str, date: datetime) -> bool:
        """Check if a date is a market holiday."""
        market_holidays = self.holidays.get(market, [])
        
        for holiday in market_holidays:
            if holiday.date.date() == date.date():
                return True
        
        return False
    
    def get_market_holidays(self, market: str, year: Optional[int] = None) -> List[MarketHoliday]:
        """Get market holidays for a specific year."""
        market_holidays = self.holidays.get(market, [])
        
        if year:
            return [h for h in market_holidays if h.date.year == year]
        
        return market_holidays.copy()
    
    def add_market_session(self, key: str, session: MarketSession) -> None:
        """Add or update a market session."""
        self.sessions[key] = session
        logger.info(f"Added market session: {key}")
    
    def add_market_fees(self, key: str, fees: MarketFees) -> None:
        """Add or update market fees."""
        self.fees[key] = fees
        logger.info(f"Added market fees: {key}")
    
    def add_slippage_model(self, key: str, model: SlippageModel) -> None:
        """Add or update slippage model."""
        self.slippage_models[key] = model
        logger.info(f"Added slippage model: {key}")
    
    def add_market_holiday(self, market: str, holiday: MarketHoliday) -> None:
        """Add a market holiday."""
        if market not in self.holidays:
            self.holidays[market] = []
        
        self.holidays[market].append(holiday)
        logger.info(f"Added holiday for {market}: {holiday.name} on {holiday.date.date()}")
    
    def save_configuration(self, file_path: Optional[str] = None) -> bool:
        """Save current configuration to file."""
        try:
            save_path = file_path or self.config_path
            
            # Prepare data for serialization
            config_data = {
                'sessions': {},
                'fees': {},
                'slippage_models': {},
                'holidays': {}
            }
            
            # Convert sessions to dict
            for key, session in self.sessions.items():
                config_data['sessions'][key] = {
                    'market': session.market,
                    'exchange': session.exchange,
                    'timezone': session.timezone,
                    'open_time': session.open_time,
                    'close_time': session.close_time,
                    'is_24h': session.is_24h,
                    'trading_days': session.trading_days
                }
            
            # Convert fees to dict
            for key, fees in self.fees.items():
                config_data['fees'][key] = {
                    'market': fees.market,
                    'exchange': fees.exchange,
                    'maker_fee': fees.maker_fee,
                    'taker_fee': fees.taker_fee,
                    'min_fee': fees.min_fee,
                    'max_fee': fees.max_fee,
                    'currency': fees.currency
                }
            
            # Convert slippage models to dict
            for key, model in self.slippage_models.items():
                config_data['slippage_models'][key] = {
                    'market': model.market,
                    'exchange': model.exchange,
                    'base_slippage': model.base_slippage,
                    'volume_impact': model.volume_impact,
                    'volatility_multiplier': model.volatility_multiplier,
                    'max_slippage': model.max_slippage
                }
            
            # Convert holidays to dict
            for market, holiday_list in self.holidays.items():
                config_data['holidays'][market] = []
                for holiday in holiday_list:
                    holiday_data = {
                        'date': holiday.date.isoformat(),
                        'name': holiday.name,
                        'is_partial_day': holiday.is_partial_day
                    }
                    if holiday.partial_hours:
                        holiday_data['partial_hours'] = holiday.partial_hours
                    config_data['holidays'][market].append(holiday_data)
            
            # Ensure directory exists
            Path(save_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Save to file
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"Market configuration saved to {save_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save market configuration: {e}")
            return False
    
    def get_all_markets(self) -> List[str]:
        """Get list of all configured markets."""
        markets = set()
        for session in self.sessions.values():
            markets.add(session.market)
        return sorted(list(markets))
    
    def get_exchanges_for_market(self, market: str) -> List[str]:
        """Get list of exchanges for a specific market."""
        exchanges = []
        for session in self.sessions.values():
            if session.market == market:
                exchanges.append(session.exchange)
        return sorted(list(set(exchanges)))
    
    def get_configuration_summary(self) -> Dict[str, Any]:
        """Get summary of current configuration."""
        return {
            'total_sessions': len(self.sessions),
            'total_fee_structures': len(self.fees),
            'total_slippage_models': len(self.slippage_models),
            'markets': self.get_all_markets(),
            'sessions_by_market': {
                market: self.get_exchanges_for_market(market)
                for market in self.get_all_markets()
            },
            'total_holidays': sum(len(holidays) for holidays in self.holidays.values())
        }


# Global instance for easy access
market_config_manager = MarketConfigManager()