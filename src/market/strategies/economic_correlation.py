"""
经济数据与市场数据的相关性分析工具

提供经济指标与市场表现之间的相关性分析功能
"""

from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from scipy import stats
from scipy.stats import pearsonr, spearmanr
import logging

try:
    from .economic_base import EconomicContext
    from ..models.market_data import MarketData, EconomicData
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from src.market.strategies.economic_base import EconomicContext
    from src.market.strategies.models.market_data import MarketData, EconomicData


class EconomicCorrelationAnalyzer:
    """
    经济数据相关性分析器
    
    分析经济指标与市场数据之间的相关性，支持滞后性分析
    """
    
    def __init__(self, economic_context: EconomicContext):
        self.economic_context = economic_context
        self.logger = logging.getLogger(f"{__name__}.EconomicCorrelationAnalyzer")
        
        # 相关性缓存
        self._correlation_cache = {}
        self._cache_expiry = {}
        
        # 预定义的经济指标分类
        self.indicator_categories = {
            'growth': ['GDPC1', 'INDPRO', 'PAYEMS'],
            'inflation': ['CPIAUCSL', 'CPILFESL', 'PCEPI'],
            'employment': ['UNRATE', 'CIVPART', 'EMRATIO'],
            'monetary': ['FEDFUNDS', 'M1SL', 'M2SL'],
            'interest_rates': ['DGS10', 'DGS2', 'DGS5'],
            'housing': ['HOUST', 'CSUSHPISA', 'MORTGAGE30US'],
            'commodities': ['DCOILWTICO', 'GOLDAMGBD228NLBM'],
            'sentiment': ['UMCSENT', 'VIXCLS']
        }
    
    def calculate_correlation_matrix(self, 
                                   market_symbols: List[str],
                                   economic_indicators: List[str],
                                   lookback_days: int = 365,
                                   method: str = 'pearson') -> pd.DataFrame:
        """
        计算市场数据与经济指标的相关性矩阵
        
        Args:
            market_symbols: 市场标的列表
            economic_indicators: 经济指标列表
            lookback_days: 回看天数
            method: 相关性计算方法 ('pearson', 'spearman')
            
        Returns:
            相关性矩阵DataFrame
        """
        try:
            # 获取数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=lookback_days)
            
            # 获取经济数据
            economic_data = self.economic_context.get_multiple_economic_data(
                economic_indicators, lookback_days, end_date
            )
            
            # 获取市场数据
            market_data = {}
            for symbol in market_symbols:
                try:
                    data = self.economic_context.get_historical_data(symbol, lookback_days, end_date)
                    if not data.empty:
                        market_data[symbol] = data
                except Exception as e:
                    self.logger.warning(f"获取市场数据失败 {symbol}: {e}")
            
            # 构建相关性矩阵
            correlation_matrix = pd.DataFrame(
                index=market_symbols,
                columns=economic_indicators,
                dtype=float
            )
            
            for market_symbol in market_symbols:
                if market_symbol not in market_data:
                    continue
                    
                market_df = market_data[market_symbol]
                
                for econ_indicator in economic_indicators:
                    if econ_indicator not in economic_data:
                        continue
                    
                    econ_df = economic_data[econ_indicator]
                    
                    # 计算相关性
                    correlation = self._calculate_correlation(
                        market_df, econ_df, method
                    )
                    
                    correlation_matrix.loc[market_symbol, econ_indicator] = correlation
            
            return correlation_matrix
            
        except Exception as e:
            self.logger.error(f"相关性矩阵计算失败: {e}")
            return pd.DataFrame()
    
    def analyze_lagged_correlation(self,
                                 market_symbol: str,
                                 economic_indicator: str,
                                 max_lag_months: int = 12,
                                 lookback_days: int = 730) -> Dict[str, Any]:
        """
        分析滞后相关性
        
        Args:
            market_symbol: 市场标的
            economic_indicator: 经济指标
            max_lag_months: 最大滞后月数
            lookback_days: 回看天数
            
        Returns:
            滞后相关性分析结果
        """
        try:
            # 获取数据
            end_date = datetime.now()
            
            market_data = self.economic_context.get_historical_data(
                market_symbol, lookback_days, end_date
            )
            economic_data = self.economic_context.get_economic_data(
                economic_indicator, lookback_days, end_date
            )
            
            if market_data.empty or economic_data.empty:
                return {'error': '数据不足'}
            
            # 计算不同滞后期的相关性
            lag_correlations = {}
            
            for lag_months in range(0, max_lag_months + 1):
                lag_days = lag_months * 30  # 近似月份转天数
                
                # 滞后经济数据
                lagged_econ_data = economic_data.shift(lag_days)
                
                # 计算相关性
                correlation = self._calculate_correlation(
                    market_data, lagged_econ_data, 'pearson'
                )
                
                if not np.isnan(correlation):
                    lag_correlations[lag_months] = correlation
            
            # 找到最佳滞后期
            if lag_correlations:
                best_lag = max(lag_correlations.keys(), 
                             key=lambda k: abs(lag_correlations[k]))
                best_correlation = lag_correlations[best_lag]
            else:
                best_lag = 0
                best_correlation = 0.0
            
            return {
                'market_symbol': market_symbol,
                'economic_indicator': economic_indicator,
                'lag_correlations': lag_correlations,
                'best_lag_months': best_lag,
                'best_correlation': best_correlation,
                'correlation_strength': self._classify_correlation_strength(abs(best_correlation))
            }
            
        except Exception as e:
            self.logger.error(f"滞后相关性分析失败: {e}")
            return {'error': str(e)}
    
    def find_leading_indicators(self,
                              market_symbol: str,
                              lookback_days: int = 730,
                              min_correlation: float = 0.3) -> List[Dict[str, Any]]:
        """
        寻找市场的先行指标
        
        Args:
            market_symbol: 市场标的
            lookback_days: 回看天数
            min_correlation: 最小相关性阈值
            
        Returns:
            先行指标列表，按相关性强度排序
        """
        try:
            leading_indicators = []
            
            # 获取所有经济指标
            all_indicators = []
            for category_indicators in self.indicator_categories.values():
                all_indicators.extend(category_indicators)
            
            # 分析每个指标的滞后相关性
            for indicator in all_indicators:
                lag_analysis = self.analyze_lagged_correlation(
                    market_symbol, indicator, max_lag_months=6, lookback_days=lookback_days
                )
                
                if 'error' not in lag_analysis:
                    best_correlation = lag_analysis.get('best_correlation', 0)
                    best_lag = lag_analysis.get('best_lag_months', 0)
                    
                    # 只考虑先行指标（滞后期 > 0）且相关性足够强
                    if best_lag > 0 and abs(best_correlation) >= min_correlation:
                        leading_indicators.append({
                            'indicator': indicator,
                            'correlation': best_correlation,
                            'lag_months': best_lag,
                            'strength': self._classify_correlation_strength(abs(best_correlation)),
                            'category': self._get_indicator_category(indicator)
                        })
            
            # 按相关性强度排序
            leading_indicators.sort(key=lambda x: abs(x['correlation']), reverse=True)
            
            return leading_indicators
            
        except Exception as e:
            self.logger.error(f"先行指标分析失败: {e}")
            return []
    
    def analyze_sector_correlations(self,
                                  sector_symbols: Dict[str, List[str]],
                                  lookback_days: int = 365) -> Dict[str, Dict[str, float]]:
        """
        分析不同行业与经济指标的相关性
        
        Args:
            sector_symbols: 行业到股票列表的映射
            lookback_days: 回看天数
            
        Returns:
            行业相关性分析结果
        """
        try:
            sector_correlations = {}
            
            # 关键经济指标
            key_indicators = [
                'GDPC1', 'UNRATE', 'CPIAUCSL', 'FEDFUNDS', 'DGS10',
                'INDPRO', 'HOUST', 'UMCSENT', 'DCOILWTICO'
            ]
            
            for sector, symbols in sector_symbols.items():
                sector_correlations[sector] = {}
                
                # 计算行业平均相关性
                for indicator in key_indicators:
                    correlations = []
                    
                    for symbol in symbols:
                        try:
                            correlation = self.economic_context.get_economic_indicator_correlation(
                                symbol, indicator, lookback_days
                            )
                            if not np.isnan(correlation):
                                correlations.append(correlation)
                        except Exception as e:
                            self.logger.warning(f"相关性计算失败 {symbol}-{indicator}: {e}")
                    
                    # 计算平均相关性
                    if correlations:
                        avg_correlation = np.mean(correlations)
                        sector_correlations[sector][indicator] = avg_correlation
                    else:
                        sector_correlations[sector][indicator] = 0.0
            
            return sector_correlations
            
        except Exception as e:
            self.logger.error(f"行业相关性分析失败: {e}")
            return {}
    
    def generate_correlation_report(self,
                                  market_symbols: List[str],
                                  lookback_days: int = 365) -> Dict[str, Any]:
        """
        生成综合相关性分析报告
        
        Args:
            market_symbols: 市场标的列表
            lookback_days: 回看天数
            
        Returns:
            综合分析报告
        """
        try:
            report = {
                'analysis_date': datetime.now(),
                'lookback_days': lookback_days,
                'market_symbols': market_symbols,
                'correlation_matrix': {},
                'leading_indicators': {},
                'key_relationships': [],
                'summary': {}
            }
            
            # 1. 计算相关性矩阵
            all_indicators = []
            for category_indicators in self.indicator_categories.values():
                all_indicators.extend(category_indicators)
            
            correlation_matrix = self.calculate_correlation_matrix(
                market_symbols, all_indicators, lookback_days
            )
            report['correlation_matrix'] = correlation_matrix.to_dict()
            
            # 2. 寻找先行指标
            for symbol in market_symbols:
                leading_indicators = self.find_leading_indicators(symbol, lookback_days)
                report['leading_indicators'][symbol] = leading_indicators[:5]  # 前5个
            
            # 3. 识别关键关系
            key_relationships = self._identify_key_relationships(correlation_matrix)
            report['key_relationships'] = key_relationships
            
            # 4. 生成摘要
            summary = self._generate_correlation_summary(
                correlation_matrix, report['leading_indicators']
            )
            report['summary'] = summary
            
            return report
            
        except Exception as e:
            self.logger.error(f"相关性报告生成失败: {e}")
            return {'error': str(e)}
    
    def _calculate_correlation(self, 
                             market_data: pd.DataFrame,
                             economic_data: pd.DataFrame,
                             method: str = 'pearson') -> float:
        """计算两个时间序列的相关性"""
        try:
            # 对齐数据
            if 'close' in market_data.columns:
                market_values = market_data['close']
            elif 'value' in market_data.columns:
                market_values = market_data['value']
            else:
                return np.nan
            
            if 'value' in economic_data.columns:
                econ_values = economic_data['value']
            else:
                return np.nan
            
            # 合并数据
            combined = pd.merge(
                market_values.to_frame('market'),
                econ_values.to_frame('economic'),
                left_index=True, right_index=True,
                how='inner'
            )
            
            if len(combined) < 10:  # 需要足够的数据点
                return np.nan
            
            # 计算相关性
            if method == 'pearson':
                correlation, _ = pearsonr(combined['market'], combined['economic'])
            elif method == 'spearman':
                correlation, _ = spearmanr(combined['market'], combined['economic'])
            else:
                correlation = combined['market'].corr(combined['economic'])
            
            return correlation if not np.isnan(correlation) else 0.0
            
        except Exception as e:
            self.logger.error(f"相关性计算失败: {e}")
            return np.nan
    
    def _classify_correlation_strength(self, correlation: float) -> str:
        """分类相关性强度"""
        abs_corr = abs(correlation)
        
        if abs_corr >= 0.8:
            return 'VERY_STRONG'
        elif abs_corr >= 0.6:
            return 'STRONG'
        elif abs_corr >= 0.4:
            return 'MODERATE'
        elif abs_corr >= 0.2:
            return 'WEAK'
        else:
            return 'VERY_WEAK'
    
    def _get_indicator_category(self, indicator: str) -> str:
        """获取指标类别"""
        for category, indicators in self.indicator_categories.items():
            if indicator in indicators:
                return category
        return 'OTHER'
    
    def _identify_key_relationships(self, correlation_matrix: pd.DataFrame) -> List[Dict[str, Any]]:
        """识别关键相关性关系"""
        key_relationships = []
        
        try:
            # 找到强相关性关系
            for market_symbol in correlation_matrix.index:
                for econ_indicator in correlation_matrix.columns:
                    correlation = correlation_matrix.loc[market_symbol, econ_indicator]
                    
                    if not pd.isna(correlation) and abs(correlation) >= 0.5:
                        key_relationships.append({
                            'market_symbol': market_symbol,
                            'economic_indicator': econ_indicator,
                            'correlation': correlation,
                            'strength': self._classify_correlation_strength(abs(correlation)),
                            'direction': 'POSITIVE' if correlation > 0 else 'NEGATIVE',
                            'category': self._get_indicator_category(econ_indicator)
                        })
            
            # 按相关性强度排序
            key_relationships.sort(key=lambda x: abs(x['correlation']), reverse=True)
            
        except Exception as e:
            self.logger.error(f"关键关系识别失败: {e}")
        
        return key_relationships[:20]  # 返回前20个
    
    def _generate_correlation_summary(self,
                                    correlation_matrix: pd.DataFrame,
                                    leading_indicators: Dict[str, List]) -> Dict[str, Any]:
        """生成相关性分析摘要"""
        try:
            summary = {
                'total_correlations': 0,
                'strong_correlations': 0,
                'average_correlation': 0.0,
                'most_correlated_indicators': [],
                'least_correlated_indicators': [],
                'category_analysis': {}
            }
            
            # 统计相关性
            all_correlations = []
            for market_symbol in correlation_matrix.index:
                for econ_indicator in correlation_matrix.columns:
                    correlation = correlation_matrix.loc[market_symbol, econ_indicator]
                    if not pd.isna(correlation):
                        all_correlations.append(abs(correlation))
                        
                        if abs(correlation) >= 0.5:
                            summary['strong_correlations'] += 1
            
            summary['total_correlations'] = len(all_correlations)
            summary['average_correlation'] = np.mean(all_correlations) if all_correlations else 0.0
            
            # 按类别分析
            for category, indicators in self.indicator_categories.items():
                category_correlations = []
                
                for indicator in indicators:
                    if indicator in correlation_matrix.columns:
                        for market_symbol in correlation_matrix.index:
                            correlation = correlation_matrix.loc[market_symbol, indicator]
                            if not pd.isna(correlation):
                                category_correlations.append(abs(correlation))
                
                if category_correlations:
                    summary['category_analysis'][category] = {
                        'average_correlation': np.mean(category_correlations),
                        'max_correlation': np.max(category_correlations),
                        'indicator_count': len([i for i in indicators if i in correlation_matrix.columns])
                    }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"摘要生成失败: {e}")
            return {}


class EconomicEventAnalyzer:
    """
    经济事件影响分析器
    
    分析重大经济事件对市场的影响
    """
    
    def __init__(self, economic_context: EconomicContext):
        self.economic_context = economic_context
        self.logger = logging.getLogger(f"{__name__}.EconomicEventAnalyzer")
        
        # 预定义的重要经济事件
        self.important_events = {
            'fed_rate_decision': {
                'indicators': ['FEDFUNDS'],
                'impact_window': 5,  # 影响窗口天数
                'significance_threshold': 0.25  # 变化阈值
            },
            'employment_report': {
                'indicators': ['UNRATE', 'PAYEMS'],
                'impact_window': 3,
                'significance_threshold': 0.1
            },
            'inflation_report': {
                'indicators': ['CPIAUCSL', 'CPILFESL'],
                'impact_window': 3,
                'significance_threshold': 0.1
            },
            'gdp_release': {
                'indicators': ['GDPC1'],
                'impact_window': 5,
                'significance_threshold': 0.5
            }
        }
    
    def detect_economic_events(self,
                             lookback_days: int = 90) -> List[Dict[str, Any]]:
        """
        检测重要经济事件
        
        Args:
            lookback_days: 回看天数
            
        Returns:
            检测到的经济事件列表
        """
        events = []
        
        try:
            end_date = datetime.now()
            
            for event_type, config in self.important_events.items():
                indicators = config['indicators']
                threshold = config['significance_threshold']
                
                for indicator in indicators:
                    # 获取经济数据
                    data = self.economic_context.get_economic_data(
                        indicator, lookback_days, end_date
                    )
                    
                    if data.empty:
                        continue
                    
                    # 检测显著变化
                    significant_changes = self._detect_significant_changes(
                        data, threshold
                    )
                    
                    for change in significant_changes:
                        events.append({
                            'event_type': event_type,
                            'indicator': indicator,
                            'date': change['date'],
                            'change': change['change'],
                            'significance': change['significance'],
                            'impact_window': config['impact_window']
                        })
            
            # 按日期排序
            events.sort(key=lambda x: x['date'], reverse=True)
            
        except Exception as e:
            self.logger.error(f"经济事件检测失败: {e}")
        
        return events
    
    def analyze_event_impact(self,
                           event: Dict[str, Any],
                           market_symbols: List[str]) -> Dict[str, Any]:
        """
        分析经济事件对市场的影响
        
        Args:
            event: 经济事件
            market_symbols: 市场标的列表
            
        Returns:
            事件影响分析结果
        """
        try:
            event_date = event['date']
            impact_window = event.get('impact_window', 5)
            
            # 分析窗口
            pre_window_start = event_date - timedelta(days=impact_window)
            post_window_end = event_date + timedelta(days=impact_window)
            
            impact_analysis = {
                'event': event,
                'market_impacts': {},
                'summary': {}
            }
            
            for symbol in market_symbols:
                # 获取事件前后的市场数据
                pre_data = self.economic_context.get_historical_data(
                    symbol, impact_window, event_date
                )
                post_data = self.economic_context.get_historical_data(
                    symbol, impact_window, post_window_end
                )
                
                if not pre_data.empty and not post_data.empty:
                    # 计算影响
                    impact = self._calculate_market_impact(pre_data, post_data)
                    impact_analysis['market_impacts'][symbol] = impact
            
            # 生成摘要
            impact_analysis['summary'] = self._summarize_event_impact(
                impact_analysis['market_impacts']
            )
            
            return impact_analysis
            
        except Exception as e:
            self.logger.error(f"事件影响分析失败: {e}")
            return {'error': str(e)}
    
    def _detect_significant_changes(self,
                                  data: pd.DataFrame,
                                  threshold: float) -> List[Dict[str, Any]]:
        """检测显著变化"""
        changes = []
        
        try:
            values = data['value'].dropna()
            
            for i in range(1, len(values)):
                current_value = values.iloc[i]
                previous_value = values.iloc[i-1]
                
                if previous_value != 0:
                    change_pct = (current_value - previous_value) / previous_value
                    
                    if abs(change_pct) >= threshold:
                        changes.append({
                            'date': values.index[i],
                            'change': change_pct,
                            'significance': abs(change_pct) / threshold
                        })
            
        except Exception as e:
            self.logger.error(f"显著变化检测失败: {e}")
        
        return changes
    
    def _calculate_market_impact(self,
                               pre_data: pd.DataFrame,
                               post_data: pd.DataFrame) -> Dict[str, float]:
        """计算市场影响"""
        try:
            # 计算事件前后的收益率
            if 'close' in pre_data.columns and 'close' in post_data.columns:
                pre_return = (pre_data['close'].iloc[-1] - pre_data['close'].iloc[0]) / pre_data['close'].iloc[0]
                post_return = (post_data['close'].iloc[-1] - post_data['close'].iloc[0]) / post_data['close'].iloc[0]
                
                return {
                    'pre_event_return': pre_return,
                    'post_event_return': post_return,
                    'impact_magnitude': abs(post_return - pre_return),
                    'direction': 'POSITIVE' if post_return > pre_return else 'NEGATIVE'
                }
            
            return {}
            
        except Exception as e:
            self.logger.error(f"市场影响计算失败: {e}")
            return {}
    
    def _summarize_event_impact(self, market_impacts: Dict[str, Dict]) -> Dict[str, Any]:
        """汇总事件影响"""
        try:
            if not market_impacts:
                return {}
            
            impact_magnitudes = [
                impact.get('impact_magnitude', 0)
                for impact in market_impacts.values()
                if 'impact_magnitude' in impact
            ]
            
            positive_impacts = sum(1 for impact in market_impacts.values()
                                 if impact.get('direction') == 'POSITIVE')
            
            return {
                'average_impact_magnitude': np.mean(impact_magnitudes) if impact_magnitudes else 0,
                'max_impact_magnitude': np.max(impact_magnitudes) if impact_magnitudes else 0,
                'positive_impact_ratio': positive_impacts / len(market_impacts) if market_impacts else 0,
                'affected_markets': len(market_impacts)
            }
            
        except Exception as e:
            self.logger.error(f"影响汇总失败: {e}")
            return {}