"""
Multi-market strategy framework supporting cross-market trading and analysis.

This module provides enhanced strategy capabilities for trading across multiple markets
including timezone handling, currency conversion, and cross-market arbitrage.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timezone
from dataclasses import dataclass
from enum import Enum
import pandas as pd
import pytz

try:
    from .signals import Signal, SignalType
    from .parameters import StrategyParameter
    from .models.market_data import UnifiedMarketData, MarketInfo, MarketType
    from .models.trading import Order
    from src.common.exceptions import TradingSystemException
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from src.market.strategies.signals import Signal, SignalType
    from src.market.strategies.parameters import StrategyParameter
    from src.market.strategies.models.market_data import UnifiedMarketData, MarketInfo, MarketType
    from src.market.strategies.models.trading import Order
    from src.common.exceptions import TradingSystemException

# Import MarketType from models (defined there to avoid circular imports)
from src.market.strategies.models.market_data import MarketType


@dataclass
class MarketSession:
    """Market trading session information."""
    market: str
    exchange: str
    timezone: str
    open_time: str  # Format: "HH:MM"
    close_time: str  # Format: "HH:MM"
    is_24h: bool = False
    trading_days: List[int] = None  # 0=Monday, 6=Sunday
    
    def __post_init__(self):
        if self.trading_days is None:
            # Default to weekdays for most markets
            self.trading_days = [0, 1, 2, 3, 4] if not self.is_24h else list(range(7))


@dataclass
class CurrencyRate:
    """Currency exchange rate information."""
    from_currency: str
    to_currency: str
    rate: float
    timestamp: datetime
    source: str = "manual"


@dataclass
class MarketFees:
    """Market-specific fee structure."""
    market: str
    exchange: str
    maker_fee: float  # Percentage
    taker_fee: float  # Percentage
    min_fee: float = 0.0
    max_fee: Optional[float] = None
    currency: str = "USD"


@dataclass
class SlippageModel:
    """Market-specific slippage model."""
    market: str
    exchange: str
    base_slippage: float  # Base slippage percentage
    volume_impact: float  # Additional slippage per unit volume
    volatility_multiplier: float = 1.0
    max_slippage: float = 0.05  # Maximum slippage cap


class MultiMarketContext(StrategyContext):
    """Enhanced strategy context for multi-market operations."""
    
    def __init__(self, data_manager=None, indicator_engine=None, portfolio=None,
                 currency_converter=None, market_sessions=None, fee_models=None,
                 slippage_models=None, risk_manager=None):
        super().__init__(data_manager, indicator_engine, portfolio)
        self.currency_converter = currency_converter or CurrencyConverter()
        self.market_sessions = market_sessions or {}
        self.fee_models = fee_models or {}
        self.slippage_models = slippage_models or {}
        self.risk_manager = risk_manager
        self.logger = logging.getLogger(f"{__name__}.MultiMarketContext")
        
        # Enhanced market data cache for cross-market analysis
        self.market_data_cache: Dict[str, Dict[str, UnifiedMarketData]] = {}
        self.last_cache_update: Dict[str, datetime] = {}
        self.cache_ttl_seconds = 60  # Cache TTL in seconds
        
        # Market synchronization tracking
        self.market_sync_status: Dict[str, bool] = {}
        self.last_sync_check: Dict[str, datetime] = {}
        
        # Cross-market correlation tracking
        self.correlation_cache: Dict[str, float] = {}
        self.correlation_update_time: Optional[datetime] = None
    
    def get_market_data(self, symbol: str, market: str, 
                       lookback: int, end_date: Optional[datetime] = None) -> pd.DataFrame:
        """Get historical data for a specific market."""
        try:
            # Add market filter to data request
            data = self.get_historical_data(symbol, lookback, end_date)
            
            # Filter by market if data contains market information
            if not data.empty and 'market' in data.columns:
                data = data[data['market'] == market]
            
            return data
            
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {symbol} 在 {market}: {e}")
            return pd.DataFrame()
    
    def get_current_market_data(self, symbol: str, market: str) -> Optional[UnifiedMarketData]:
        """Get current market data for a specific symbol and market."""
        cache_key = f"{symbol}_{market}"
        
        # Check cache first
        if cache_key in self.market_data_cache:
            cached_data = self.market_data_cache[cache_key]
            last_update = self.last_cache_update.get(cache_key)
            
            if last_update and (datetime.now(timezone.utc) - last_update).total_seconds() < self.cache_ttl_seconds:
                return cached_data.get(symbol)
        
        # Fetch fresh data
        try:
            current_data = self.get_current_data(symbol)
            if current_data and hasattr(current_data, 'market') and current_data.market == market:
                # Update cache
                if cache_key not in self.market_data_cache:
                    self.market_data_cache[cache_key] = {}
                
                self.market_data_cache[cache_key][symbol] = current_data
                self.last_cache_update[cache_key] = datetime.now(timezone.utc)
                
                return current_data
        except Exception as e:
            self.logger.error(f"获取当前市场数据失败: {symbol} 在 {market}: {e}")
        
        return None
    
    def get_synchronized_market_data(self, symbols_markets: Dict[str, str], 
                                   max_age_seconds: int = 30) -> Dict[str, UnifiedMarketData]:
        """Get synchronized market data across multiple markets."""
        results = {}
        current_time = datetime.now(timezone.utc)
        
        for symbol, market in symbols_markets.items():
            data = self.get_current_market_data(symbol, market)
            if data:
                # Check data freshness
                data_age = (current_time - data.timestamp).total_seconds()
                if data_age <= max_age_seconds:
                    results[f"{symbol}_{market}"] = data
                else:
                    self.logger.warning(f"Data for {symbol} in {market} is {data_age:.1f}s old")
        
        return results
    
    def get_cross_market_data(self, symbols_markets: Dict[str, str], 
                             lookback: int, end_date: Optional[datetime] = None) -> Dict[str, pd.DataFrame]:
        """Get data for multiple symbols across different markets."""
        results = {}
        
        for symbol, market in symbols_markets.items():
            try:
                data = self.get_market_data(symbol, market, lookback, end_date)
                results[f"{symbol}_{market}"] = data
            except Exception as e:
                self.logger.error(f"获取跨市场数据失败: {symbol} 在 {market}: {e}")
                results[f"{symbol}_{market}"] = pd.DataFrame()
        
        return results
    
    def convert_currency(self, amount: float, from_currency: str, 
                        to_currency: str, timestamp: Optional[datetime] = None) -> float:
        """Convert amount from one currency to another."""
        if from_currency == to_currency:
            return amount
        
        if not self.currency_converter:
            self.logger.warning(f"No currency converter available, returning original amount")
            return amount
        
        try:
            return self.currency_converter.convert(amount, from_currency, to_currency, timestamp)
        except Exception as e:
            self.logger.error(f"货币转换失败: {e}")
            return amount
    
    def is_market_open(self, market: str, timestamp: Optional[datetime] = None) -> bool:
        """Check if a market is currently open."""
        if timestamp is None:
            timestamp = datetime.now(timezone.utc)
        
        session = self.market_sessions.get(market)
        if not session:
            self.logger.warning(f"No session info for market {market}")
            return True  # Assume open if no info
        
        if session.is_24h:
            return True
        
        try:
            # Convert timestamp to market timezone
            market_tz = pytz.timezone(session.timezone)
            market_time = timestamp.astimezone(market_tz)
            
            # Check if it's a trading day
            if market_time.weekday() not in session.trading_days:
                return False
            
            # Check if within trading hours
            open_time = datetime.strptime(session.open_time, "%H:%M").time()
            close_time = datetime.strptime(session.close_time, "%H:%M").time()
            current_time = market_time.time()
            
            return open_time <= current_time <= close_time
            
        except Exception as e:
            self.logger.error(f"检查市场交易时间失败: {market}: {e}")
            return True
    
    def get_market_fees(self, market: str, exchange: str) -> Optional[MarketFees]:
        """Get fee structure for a specific market and exchange."""
        key = f"{market}_{exchange}"
        return self.fee_models.get(key)
    
    def get_slippage_model(self, market: str, exchange: str) -> Optional[SlippageModel]:
        """Get slippage model for a specific market and exchange."""
        key = f"{market}_{exchange}"
        return self.slippage_models.get(key)
    
    def calculate_order_cost(self, symbol: str, market: str, exchange: str,
                           quantity: float, price: float, side: str) -> Dict[str, float]:
        """Calculate total order cost including fees and slippage."""
        fees = self.get_market_fees(market, exchange)
        slippage_model = self.get_slippage_model(market, exchange)
        
        base_cost = quantity * price
        total_fees = 0.0
        total_slippage = 0.0
        
        # Calculate fees
        if fees:
            fee_rate = fees.taker_fee  # Assume market order (taker)
            calculated_fee = base_cost * (fee_rate / 100)
            total_fees = max(fees.min_fee, calculated_fee)
            if fees.max_fee:
                total_fees = min(total_fees, fees.max_fee)
        
        # Calculate slippage
        if slippage_model:
            base_slippage = slippage_model.base_slippage
            volume_impact = slippage_model.volume_impact * abs(quantity)
            total_slippage_rate = min(
                base_slippage + volume_impact,
                slippage_model.max_slippage
            )
            total_slippage = base_cost * total_slippage_rate
        
        return {
            'base_cost': base_cost,
            'fees': total_fees,
            'slippage': total_slippage,
            'total_cost': base_cost + total_fees + total_slippage
        }
    
    def check_market_synchronization(self, markets: List[str], 
                                   max_time_diff_seconds: int = 30) -> Dict[str, bool]:
        """Check if markets are synchronized within acceptable time difference."""
        sync_status = {}
        current_time = datetime.now(timezone.utc)
        
        # Get latest data timestamp for each market
        market_timestamps = {}
        for market in markets:
            latest_timestamp = None
            
            # Check cache for latest data
            for cache_key, cache_data in self.market_data_cache.items():
                if market in cache_key:
                    for symbol, data in cache_data.items():
                        if data.market == market:
                            if latest_timestamp is None or data.timestamp > latest_timestamp:
                                latest_timestamp = data.timestamp
            
            market_timestamps[market] = latest_timestamp
        
        # Check synchronization between markets
        for market in markets:
            is_synchronized = True
            market_time = market_timestamps.get(market)
            
            if market_time is None:
                sync_status[market] = False
                continue
            
            # Check against other markets
            for other_market in markets:
                if other_market == market:
                    continue
                
                other_time = market_timestamps.get(other_market)
                if other_time is None:
                    continue
                
                time_diff = abs((market_time - other_time).total_seconds())
                if time_diff > max_time_diff_seconds:
                    is_synchronized = False
                    break
            
            sync_status[market] = is_synchronized
        
        # Update sync status cache
        self.market_sync_status.update(sync_status)
        self.last_sync_check[f"{'_'.join(markets)}"] = current_time
        
        return sync_status
    
    def calculate_cross_market_correlation(self, symbol1: str, market1: str,
                                         symbol2: str, market2: str,
                                         lookback_periods: int = 100) -> Optional[float]:
        """Calculate correlation between symbols across different markets."""
        try:
            # Get historical data for both symbols
            data1 = self.get_market_data(symbol1, market1, lookback_periods)
            data2 = self.get_market_data(symbol2, market2, lookback_periods)
            
            if data1.empty or data2.empty:
                return None
            
            # Align data by timestamp
            data1_indexed = data1.set_index('timestamp')['close'] if 'timestamp' in data1.columns else data1['close']
            data2_indexed = data2.set_index('timestamp')['close'] if 'timestamp' in data2.columns else data2['close']
            
            # Calculate correlation
            correlation = data1_indexed.corr(data2_indexed)
            
            # Cache the result
            cache_key = f"{symbol1}_{market1}_{symbol2}_{market2}"
            self.correlation_cache[cache_key] = correlation
            self.correlation_update_time = datetime.now(timezone.utc)
            
            return correlation if not pd.isna(correlation) else None
            
        except Exception as e:
            self.logger.error(f"Error calculating cross-market correlation: {e}")
            return None
    
    def get_market_overlap_hours(self, market1: str, market2: str) -> List[Tuple[str, str]]:
        """Get overlapping trading hours between two markets."""
        session1 = self.market_sessions.get(market1)
        session2 = self.market_sessions.get(market2)
        
        if not session1 or not session2:
            return []
        
        try:
            # Convert to common timezone (UTC)
            tz1 = pytz.timezone(session1.timezone)
            tz2 = pytz.timezone(session2.timezone)
            
            # Create datetime objects for today's trading hours
            today = datetime.now().date()
            
            # Market 1 hours in UTC
            open1_local = datetime.combine(today, datetime.strptime(session1.open_time, "%H:%M").time())
            close1_local = datetime.combine(today, datetime.strptime(session1.close_time, "%H:%M").time())
            
            open1_utc = tz1.localize(open1_local).astimezone(pytz.UTC)
            close1_utc = tz1.localize(close1_local).astimezone(pytz.UTC)
            
            # Market 2 hours in UTC
            open2_local = datetime.combine(today, datetime.strptime(session2.open_time, "%H:%M").time())
            close2_local = datetime.combine(today, datetime.strptime(session2.close_time, "%H:%M").time())
            
            open2_utc = tz2.localize(open2_local).astimezone(pytz.UTC)
            close2_utc = tz2.localize(close2_local).astimezone(pytz.UTC)
            
            # Find overlap
            overlap_start = max(open1_utc, open2_utc)
            overlap_end = min(close1_utc, close2_utc)
            
            if overlap_start < overlap_end:
                return [(overlap_start.strftime("%H:%M"), overlap_end.strftime("%H:%M"))]
            else:
                return []  # No overlap
                
        except Exception as e:
            self.logger.error(f"Error calculating market overlap: {e}")
            return []
    
    def estimate_execution_delay(self, market: str, order_size: float) -> float:
        """Estimate execution delay for an order in a specific market."""
        # Simple model - could be enhanced with real market microstructure data
        base_delay = 0.1  # 100ms base delay
        
        # Add delay based on market characteristics
        if market == 'CRYPTO':
            base_delay += 0.05  # Crypto markets are generally faster
        elif market == 'CN':
            base_delay += 0.2   # A-share markets may have higher latency
        
        # Add delay based on order size (larger orders take longer)
        size_delay = min(order_size / 10000 * 0.1, 1.0)  # Max 1 second additional delay
        
        return base_delay + size_delay


class BaseMultiMarketStrategy(BaseStrategy):
    """Base class for multi-market trading strategies."""
    
    def __init__(self, name: str, parameters: Optional[Dict[str, Any]] = None,
                 supported_markets: Optional[List[str]] = None):
        super().__init__(name, parameters)
        self.supported_markets = supported_markets or []
        self.market_contexts: Dict[str, MultiMarketContext] = {}
        self.active_markets: List[str] = []
        
    def set_multi_market_context(self, context: MultiMarketContext) -> None:
        """Set multi-market context."""
        self.context = context
        
        # Initialize market-specific contexts if needed
        for market in self.supported_markets:
            self.market_contexts[market] = context
    
    def add_market(self, market: str) -> bool:
        """Add a market to the strategy."""
        if market not in self.supported_markets:
            self.supported_markets.append(market)
            if self.context:
                self.market_contexts[market] = self.context
            return True
        return False
    
    def remove_market(self, market: str) -> bool:
        """Remove a market from the strategy."""
        if market in self.supported_markets:
            self.supported_markets.remove(market)
            if market in self.market_contexts:
                del self.market_contexts[market]
            if market in self.active_markets:
                self.active_markets.remove(market)
            return True
        return False
    
    def is_market_supported(self, market: str) -> bool:
        """Check if a market is supported by this strategy."""
        return market in self.supported_markets
    
    def get_active_markets(self) -> List[str]:
        """Get list of currently active markets."""
        if not self.context:
            return []
        
        active = []
        for market in self.supported_markets:
            if self.context.is_market_open(market):
                active.append(market)
        
        return active
    
    @abstractmethod
    def on_multi_market_data(self, market_data: Dict[str, List[UnifiedMarketData]]) -> List[Signal]:
        """Process data from multiple markets and generate signals."""
        pass
    
    def on_data(self, data: Union[UnifiedMarketData, Dict[str, UnifiedMarketData]]) -> List[Signal]:
        """Process incoming data and route to multi-market handler."""
        try:
            # Convert single data to multi-market format
            if isinstance(data, UnifiedMarketData):
                market_data = {data.market: [data]}
            else:
                # Group data by market
                market_data = {}
                for symbol, market_data_item in data.items():
                    market = market_data_item.market
                    if market not in market_data:
                        market_data[market] = []
                    market_data[market].append(market_data_item)
            
            # Filter to supported markets only
            filtered_data = {
                market: data_list for market, data_list in market_data.items()
                if self.is_market_supported(market)
            }
            
            if not filtered_data:
                return []
            
            return self.on_multi_market_data(filtered_data)
            
        except Exception as e:
            self.logger.error(f"Error processing multi-market data: {e}")
            return []
    
    def create_cross_market_signal(self, primary_symbol: str, primary_market: str,
                                  secondary_symbol: str, secondary_market: str,
                                  action: str, quantity: float,
                                  confidence: float = 1.0,
                                  metadata: Optional[Dict[str, Any]] = None) -> Signal:
        """Create a signal for cross-market operations."""
        signal_metadata = metadata or {}
        signal_metadata.update({
            'strategy_type': 'cross_market',
            'primary_market': primary_market,
            'secondary_market': secondary_market,
            'secondary_symbol': secondary_symbol
        })
        
        # Map action string to SignalType enum used by Signal dataclass
        try:
            signal_type = SignalType[action]
        except Exception:
            # Default to BUY if unknown action is provided
            signal_type = SignalType.BUY

        return Signal(
            symbol=primary_symbol,
            signal_type=signal_type,
            quantity=quantity,
            timestamp=datetime.now(timezone.utc),
            confidence=confidence,
            metadata=signal_metadata,
            strategy_name=self.name
        )
    
    def validate_cross_market_opportunity(self, symbol1: str, market1: str,
                                        symbol2: str, market2: str,
                                        price1: float, price2: float,
                                        min_profit_threshold: float = 0.01) -> bool:
        """Validate if a cross-market arbitrage opportunity exists."""
        if not self.context:
            return False
        
        try:
            # Get market info for both symbols
            market_info1 = self.context.data_manager.get_market_info(symbol1) if self.context.data_manager else None
            market_info2 = self.context.data_manager.get_market_info(symbol2) if self.context.data_manager else None
            
            if not market_info1 or not market_info2:
                return False
            
            # Convert prices to common currency
            price1_usd = self.context.convert_currency(price1, market_info1.currency, 'USD')
            price2_usd = self.context.convert_currency(price2, market_info2.currency, 'USD')
            
            # Calculate potential profit
            price_diff = abs(price1_usd - price2_usd)
            avg_price = (price1_usd + price2_usd) / 2
            profit_percentage = price_diff / avg_price if avg_price > 0 else 0
            
            # Check if profit exceeds threshold
            if profit_percentage < min_profit_threshold:
                return False
            
            # Check if both markets are open
            if not (self.context.is_market_open(market1) and self.context.is_market_open(market2)):
                return False
            
            # Calculate transaction costs
            quantity = 100  # Example quantity for cost calculation
            cost1 = self.context.calculate_order_cost(symbol1, market1, market_info1.exchange, quantity, price1, 'BUY')
            cost2 = self.context.calculate_order_cost(symbol2, market2, market_info2.exchange, quantity, price2, 'SELL')
            
            total_costs = cost1['fees'] + cost1['slippage'] + cost2['fees'] + cost2['slippage']
            net_profit = (price_diff * quantity) - total_costs
            
            return net_profit > 0
            
        except Exception as e:
            self.logger.error(f"Error validating cross-market opportunity: {e}")
            return False


class CurrencyConverter:
    """Currency conversion utility for multi-market strategies."""
    
    def __init__(self):
        self.rates: Dict[str, CurrencyRate] = {}
        self.base_currency = 'USD'
        self.logger = logging.getLogger(f"{__name__}.CurrencyConverter")
        
        # Initialize with some common rates (should be updated from real sources)
        self._initialize_default_rates()
    
    def _initialize_default_rates(self) -> None:
        """Initialize with default exchange rates."""
        default_rates = [
            CurrencyRate('USD', 'USD', 1.0, datetime.now(timezone.utc)),
            CurrencyRate('CNY', 'USD', 0.14, datetime.now(timezone.utc)),  # Approximate
            CurrencyRate('EUR', 'USD', 1.08, datetime.now(timezone.utc)),  # Approximate
            CurrencyRate('GBP', 'USD', 1.25, datetime.now(timezone.utc)),  # Approximate
            CurrencyRate('JPY', 'USD', 0.0067, datetime.now(timezone.utc)),  # Approximate
        ]
        
        for rate in default_rates:
            self.add_rate(rate)
    
    def add_rate(self, rate: CurrencyRate) -> None:
        """Add or update a currency exchange rate."""
        key = f"{rate.from_currency}_{rate.to_currency}"
        self.rates[key] = rate
        
        # Also add inverse rate
        if rate.rate != 0:
            inverse_key = f"{rate.to_currency}_{rate.from_currency}"
            inverse_rate = CurrencyRate(
                from_currency=rate.to_currency,
                to_currency=rate.from_currency,
                rate=1.0 / rate.rate,
                timestamp=rate.timestamp,
                source=rate.source
            )
            self.rates[inverse_key] = inverse_rate
    
    def get_rate(self, from_currency: str, to_currency: str, 
                timestamp: Optional[datetime] = None) -> Optional[float]:
        """Get exchange rate between two currencies."""
        if from_currency == to_currency:
            return 1.0
        
        key = f"{from_currency}_{to_currency}"
        rate_info = self.rates.get(key)
        
        if rate_info:
            # Check if rate is not too old (within 24 hours)
            if timestamp:
                age = abs((timestamp - rate_info.timestamp).total_seconds())
                if age > 86400:  # 24 hours
                    self.logger.warning(f"Exchange rate {key} is {age/3600:.1f} hours old")
            
            return rate_info.rate
        
        # Try to find indirect rate through base currency
        if from_currency != self.base_currency and to_currency != self.base_currency:
            rate_to_base = self.get_rate(from_currency, self.base_currency, timestamp)
            rate_from_base = self.get_rate(self.base_currency, to_currency, timestamp)
            
            if rate_to_base and rate_from_base:
                return rate_to_base * rate_from_base
        
        self.logger.warning(f"No exchange rate found for {from_currency} to {to_currency}")
        return None
    
    def convert(self, amount: float, from_currency: str, to_currency: str,
               timestamp: Optional[datetime] = None) -> float:
        """Convert amount from one currency to another."""
        rate = self.get_rate(from_currency, to_currency, timestamp)
        if rate is None:
            raise ValueError(f"Cannot convert {from_currency} to {to_currency}: no rate available")
        
        # Round to avoid floating point noise in tests and downstream logic
        return round(amount * rate, 10)
    
    def update_rates_from_source(self, rates_data: Dict[str, float], 
                               source: str = "external") -> None:
        """Update rates from external data source."""
        timestamp = datetime.now(timezone.utc)
        
        for rate_key, rate_value in rates_data.items():
            try:
                from_currency, to_currency = rate_key.split('_')
                rate = CurrencyRate(
                    from_currency=from_currency,
                    to_currency=to_currency,
                    rate=rate_value,
                    timestamp=timestamp,
                    source=source
                )
                self.add_rate(rate)
            except Exception as e:
                self.logger.error(f"Failed to update rate {rate_key}: {e}")
    
    def get_all_rates(self) -> Dict[str, CurrencyRate]:
        """Get all available exchange rates."""
        return self.rates.copy()
    
    def cleanup_old_rates(self, max_age_hours: int = 48) -> None:
        """Remove rates older than specified age."""
        cutoff_time = datetime.now(timezone.utc) - pd.Timedelta(hours=max_age_hours)
        
        old_keys = []
        for key, rate in self.rates.items():
            if rate.timestamp < cutoff_time:
                old_keys.append(key)
        
        for key in old_keys:
            del self.rates[key]
        
        if old_keys:
            self.logger.info(f"Cleaned up {len(old_keys)} old exchange rates")
    
    def get_cross_rate_chain(self, from_currency: str, to_currency: str) -> Optional[List[str]]:
        """Find the shortest path for currency conversion through available rates."""
        if from_currency == to_currency:
            return [from_currency]
        
        # Simple BFS to find conversion path
        from collections import deque
        
        queue = deque([(from_currency, [from_currency])])
        visited = {from_currency}
        
        while queue:
            current_currency, path = queue.popleft()
            
            # Check all available rates from current currency
            for rate_key, rate_info in self.rates.items():
                if rate_info.from_currency == current_currency:
                    next_currency = rate_info.to_currency
                    
                    if next_currency == to_currency:
                        return path + [next_currency]
                    
                    if next_currency not in visited:
                        visited.add(next_currency)
                        queue.append((next_currency, path + [next_currency]))
        
        return None  # No conversion path found
    
    def get_conversion_cost(self, from_currency: str, to_currency: str) -> float:
        """Estimate the cost of currency conversion (spread/fees)."""
        if from_currency == to_currency:
            return 0.0
        
        # Simple cost model - could be enhanced with real spread data
        major_currencies = {'USD', 'EUR', 'GBP', 'JPY'}
        
        if from_currency in major_currencies and to_currency in major_currencies:
            return 0.001  # 0.1% for major pairs
        elif from_currency in major_currencies or to_currency in major_currencies:
            return 0.002  # 0.2% for major-minor pairs
        else:
            return 0.005  # 0.5% for exotic pairs