"""
策略生命周期管理器

负责策略的初始化、运行、暂停、恢复和清理等生命周期管理
"""

from enum import Enum
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import logging
import threading
import time

try:
    from .base import BaseStrategy, StrategyContext
    from .signals import Signal, SignalManager
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from src.market.strategies.base import BaseStrategy, StrategyContext
    from src.market.strategies.signals import Signal, SignalManager


class StrategyState(Enum):
    """策略状态枚举"""
    CREATED = "created"           # 已创建
    INITIALIZING = "initializing" # 初始化中
    INITIALIZED = "initialized"   # 已初始化
    RUNNING = "running"          # 运行中
    PAUSED = "paused"           # 已暂停
    STOPPING = "stopping"       # 停止中
    STOPPED = "stopped"         # 已停止
    ERROR = "error"             # 错误状态


class StrategyLifecycleManager:
    """
    策略生命周期管理器
    
    管理策略的完整生命周期，包括状态转换、错误处理和资源管理
    """
    
    def __init__(self):
        self.strategies: Dict[str, BaseStrategy] = {}
        self.strategy_states: Dict[str, StrategyState] = {}
        self.strategy_contexts: Dict[str, StrategyContext] = {}
        self.signal_manager = SignalManager()
        self.logger = logging.getLogger(f"{__name__}.StrategyLifecycleManager")
        
        # 事件回调
        self.state_change_callbacks: List[Callable[[str, StrategyState, StrategyState], None]] = []
        self.error_callbacks: List[Callable[[str, Exception], None]] = []
        
        # 线程安全锁
        self._lock = threading.RLock()
    
    def register_strategy(self, strategy: BaseStrategy, 
                         context: StrategyContext) -> bool:
        """
        注册策略到生命周期管理器
        
        策略注册业务逻辑：
        策略注册是策略生命周期的起点，将策略实例纳入管理器的统一管理。
        这个过程包括策略验证、资源分配和状态初始化。
        
        注册流程：
        1. 线程安全检查：
           - 获取管理器锁，确保并发安全
           - 防止多线程同时注册相同策略
        
        2. 策略冲突处理：
           - 检查策略名称是否已存在
           - 如存在则记录警告并覆盖
           - 确保策略名称的唯一性
        
        3. 资源分配：
           - 存储策略实例引用
           - 关联策略上下文
           - 设置初始状态为CREATED
        
        4. 异常处理：
           - 捕获注册过程中的异常
           - 记录详细错误信息
           - 返回操作结果状态
        
        业务规则：
        - 策略名称必须唯一
        - 策略和上下文都不能为空
        - 注册成功后策略状态为CREATED
        - 支持策略的重新注册（覆盖）
        
        Args:
            strategy: 待注册的策略实例，必须继承自BaseStrategy
            context: 策略运行上下文，提供数据和服务访问
            
        Returns:
            bool: 注册是否成功
            
        异常处理：
            - 捕获所有异常并记录日志
            - 不会抛出异常，通过返回值表示结果
            
        示例:
            strategy = MyTradingStrategy("my_strategy")
            context = StrategyContext(data_manager, portfolio)
            success = manager.register_strategy(strategy, context)
            if success:
                logger.info("策略注册成功")
        """
        with self._lock:
            # 检查策略名称冲突
            if strategy.name in self.strategies:
                self.logger.warning(f"策略 {strategy.name} 已存在，将被覆盖")
            
            try:
                # 存储策略实例和相关信息
                self.strategies[strategy.name] = strategy
                self.strategy_contexts[strategy.name] = context
                self.strategy_states[strategy.name] = StrategyState.CREATED
                
                self.logger.info(f"策略 {strategy.name} 注册成功")
                return True
                
            except Exception as e:
                self.logger.error(f"注册策略 {strategy.name} 失败: {e}")
                return False
    
    def unregister_strategy(self, strategy_name: str) -> bool:
        """
        注销策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            是否注销成功
        """
        with self._lock:
            if strategy_name not in self.strategies:
                self.logger.warning(f"策略 {strategy_name} 不存在")
                return False
            
            try:
                # 如果策略正在运行，先停止它
                if self.strategy_states[strategy_name] == StrategyState.RUNNING:
                    self.stop_strategy(strategy_name)
                
                # 清理资源
                strategy = self.strategies[strategy_name]
                strategy.cleanup()
                
                # 移除引用
                del self.strategies[strategy_name]
                del self.strategy_contexts[strategy_name]
                del self.strategy_states[strategy_name]
                
                self.logger.info(f"策略 {strategy_name} 注销成功")
                return True
                
            except Exception as e:
                self.logger.error(f"注销策略 {strategy_name} 失败: {e}")
                return False
    
    def initialize_strategy(self, strategy_name: str) -> bool:
        """
        初始化策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            是否初始化成功
        """
        with self._lock:
            if strategy_name not in self.strategies:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return False
            
            strategy = self.strategies[strategy_name]
            context = self.strategy_contexts[strategy_name]
            current_state = self.strategy_states[strategy_name]
            
            if current_state != StrategyState.CREATED:
                self.logger.warning(f"策略 {strategy_name} 状态不正确: {current_state}")
                return False
            
            try:
                self._change_state(strategy_name, StrategyState.INITIALIZING)
                
                # 设置上下文
                strategy.set_context(context)
                
                # 调用初始化方法
                strategy.initialize(context)
                strategy.is_initialized = True
                
                self._change_state(strategy_name, StrategyState.INITIALIZED)
                self.logger.info(f"策略 {strategy_name} 初始化成功")
                return True
                
            except Exception as e:
                self._change_state(strategy_name, StrategyState.ERROR)
                self._handle_error(strategy_name, e)
                return False
    
    def start_strategy(self, strategy_name: str) -> bool:
        """
        启动策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            是否启动成功
        """
        with self._lock:
            if strategy_name not in self.strategies:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return False
            
            current_state = self.strategy_states[strategy_name]
            
            if current_state not in [StrategyState.INITIALIZED, StrategyState.PAUSED]:
                self.logger.warning(f"策略 {strategy_name} 状态不正确: {current_state}")
                return False
            
            try:
                strategy = self.strategies[strategy_name]
                strategy.is_active = True
                
                self._change_state(strategy_name, StrategyState.RUNNING)
                self.logger.info(f"策略 {strategy_name} 启动成功")
                return True
                
            except Exception as e:
                self._change_state(strategy_name, StrategyState.ERROR)
                self._handle_error(strategy_name, e)
                return False
    
    def pause_strategy(self, strategy_name: str) -> bool:
        """
        暂停策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            是否暂停成功
        """
        with self._lock:
            if strategy_name not in self.strategies:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return False
            
            current_state = self.strategy_states[strategy_name]
            
            if current_state != StrategyState.RUNNING:
                self.logger.warning(f"策略 {strategy_name} 状态不正确: {current_state}")
                return False
            
            try:
                strategy = self.strategies[strategy_name]
                strategy.is_active = False
                
                self._change_state(strategy_name, StrategyState.PAUSED)
                self.logger.info(f"策略 {strategy_name} 暂停成功")
                return True
                
            except Exception as e:
                self._change_state(strategy_name, StrategyState.ERROR)
                self._handle_error(strategy_name, e)
                return False
    
    def stop_strategy(self, strategy_name: str) -> bool:
        """
        停止策略
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            是否停止成功
        """
        with self._lock:
            if strategy_name not in self.strategies:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return False
            
            current_state = self.strategy_states[strategy_name]
            
            if current_state in [StrategyState.STOPPED, StrategyState.CREATED]:
                self.logger.info(f"策略 {strategy_name} 已经停止")
                return True
            
            try:
                self._change_state(strategy_name, StrategyState.STOPPING)
                
                strategy = self.strategies[strategy_name]
                strategy.is_active = False
                strategy.cleanup()
                
                self._change_state(strategy_name, StrategyState.STOPPED)
                self.logger.info(f"策略 {strategy_name} 停止成功")
                return True
                
            except Exception as e:
                self._change_state(strategy_name, StrategyState.ERROR)
                self._handle_error(strategy_name, e)
                return False
    
    def process_data(self, strategy_name: str, data: Any) -> List[Signal]:
        """
        处理市场数据并生成交易信号
        
        数据处理业务逻辑：
        这是策略运行的核心方法，负责将市场数据传递给策略并处理生成的交易信号。
        该过程涉及数据验证、策略执行、信号处理和异常管理。
        
        处理流程：
        1. 策略状态验证：
           - 检查策略是否存在
           - 验证策略是否处于运行状态
           - 确保策略可以接收数据
        
        2. 数据传递与处理：
           - 调用策略的on_data方法
           - 传递市场数据给策略
           - 获取策略生成的信号列表
        
        3. 信号处理与验证：
           - 验证信号的有效性
           - 将有效信号添加到信号管理器
           - 通知策略信号已生成
           - 收集处理成功的信号
        
        4. 异常处理：
           - 捕获策略执行异常
           - 将策略状态设为ERROR
           - 触发错误处理流程
           - 返回空信号列表
        
        业务规则：
        - 只有运行状态的策略才能处理数据
        - 所有生成的信号都需要验证
        - 异常不会中断其他策略的运行
        - 错误策略会被自动标记为ERROR状态
        
        Args:
            strategy_name: 目标策略的名称
            data: 市场数据，可以是单个数据点或数据集合
            
        Returns:
            List[Signal]: 成功处理的交易信号列表
            
        信号处理说明：
            - 信号会经过信号管理器的验证
            - 只有有效信号才会被保留
            - 策略会收到信号生成的通知
            - 无效信号会被自动过滤
        
        异常处理：
            - 策略不存在：记录错误并返回空列表
            - 策略状态错误：静默返回空列表
            - 策略执行异常：标记错误状态并返回空列表
        
        示例:
            signals = manager.process_data("my_strategy", market_data)
            for signal in signals:
                logger.info(f"生成信号: {signal.action} {signal.symbol}")
        """
        with self._lock:
            # 第一步：验证策略存在性
            if strategy_name not in self.strategies:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return []
            
            # 第二步：检查策略状态
            current_state = self.strategy_states[strategy_name]
            if current_state != StrategyState.RUNNING:
                # 非运行状态的策略不处理数据，静默返回
                return []
            
            try:
                # 第三步：执行策略数据处理
                strategy = self.strategies[strategy_name]
                signals = strategy.on_data(data)
                
                # 第四步：处理生成的信号
                processed_signals = []
                for signal in signals:
                    # 验证并添加信号到管理器
                    if self.signal_manager.add_signal(signal):
                        # 通知策略信号已生成
                        strategy.on_signal(signal)
                        processed_signals.append(signal)
                
                return processed_signals
                
            except Exception as e:
                # 第五步：异常处理
                self._change_state(strategy_name, StrategyState.ERROR)
                self._handle_error(strategy_name, e)
                return []
    
    def get_strategy_state(self, strategy_name: str) -> Optional[StrategyState]:
        """
        获取策略状态
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            策略状态，如果策略不存在则返回None
        """
        return self.strategy_states.get(strategy_name)
    
    def get_all_strategies(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有策略的信息
        
        Returns:
            策略信息字典
        """
        with self._lock:
            result = {}
            for name, strategy in self.strategies.items():
                result[name] = {
                    'strategy': strategy,
                    'state': self.strategy_states[name],
                    'status': strategy.get_status()
                }
            return result
    
    def get_running_strategies(self) -> List[str]:
        """
        获取正在运行的策略列表
        
        Returns:
            策略名称列表
        """
        with self._lock:
            return [name for name, state in self.strategy_states.items() 
                   if state == StrategyState.RUNNING]
    
    def add_state_change_callback(self, callback: Callable[[str, StrategyState, StrategyState], None]) -> None:
        """
        添加状态变化回调
        
        Args:
            callback: 回调函数，参数为(策略名称, 旧状态, 新状态)
        """
        self.state_change_callbacks.append(callback)
    
    def add_error_callback(self, callback: Callable[[str, Exception], None]) -> None:
        """
        添加错误回调
        
        Args:
            callback: 回调函数，参数为(策略名称, 异常对象)
        """
        self.error_callbacks.append(callback)
    
    def _change_state(self, strategy_name: str, new_state: StrategyState) -> None:
        """
        改变策略状态
        
        Args:
            strategy_name: 策略名称
            new_state: 新状态
        """
        old_state = self.strategy_states.get(strategy_name)
        self.strategy_states[strategy_name] = new_state
        
        # 调用状态变化回调
        for callback in self.state_change_callbacks:
            try:
                callback(strategy_name, old_state, new_state)
            except Exception as e:
                self.logger.error(f"状态变化回调异常: {e}")
    
    def _handle_error(self, strategy_name: str, error: Exception) -> None:
        """
        处理策略错误
        
        Args:
            strategy_name: 策略名称
            error: 异常对象
        """
        self.logger.error(f"策略 {strategy_name} 发生错误: {error}")
        
        # 调用错误回调
        for callback in self.error_callbacks:
            try:
                callback(strategy_name, error)
            except Exception as e:
                self.logger.error(f"错误回调异常: {e}")
    
    def shutdown(self) -> None:
        """
        关闭生命周期管理器
        """
        with self._lock:
            self.logger.info("开始关闭策略生命周期管理器")
            
            # 停止所有运行中的策略
            running_strategies = self.get_running_strategies()
            for strategy_name in running_strategies:
                self.stop_strategy(strategy_name)
            
            # 清理所有策略
            strategy_names = list(self.strategies.keys())
            for strategy_name in strategy_names:
                self.unregister_strategy(strategy_name)
            
            self.logger.info("策略生命周期管理器关闭完成")