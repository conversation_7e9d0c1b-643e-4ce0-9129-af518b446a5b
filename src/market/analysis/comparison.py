"""
Strategy comparison analysis module.

This module provides comprehensive strategy comparison capabilities including
multi-strategy performance comparison, correlation analysis, and portfolio
optimization recommendations.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import itertools

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.analysis.metrics import PerformanceMetrics, MetricsCalculator
from src.market.strategies.models.trading import Trade


class 对比方法(Enum):
    """策略对比方法。"""
    夏普比率 = "sharpe_ratio"
    索提诺比率 = "sortino_ratio" 
    卡尔玛比率 = "calmar_ratio"
    总收益率 = "total_return"
    最大回撤 = "max_drawdown"
    胜率 = "win_rate"
    盈利因子 = "profit_factor"


class 排名方法(Enum):
    """策略排名方法。"""
    加权评分 = "weighted_score"
    风险调整收益 = "risk_adjusted_return"
    一致性 = "consistency"
    分散化收益 = "diversification_benefit"
    多目标优化 = "multi_objective"


@dataclass
class 策略数据:
    """策略数据容器。"""
    名称: str
    组合价值: pd.Series
    交易记录: Optional[List[Trade]] = None
    基准价值: Optional[pd.Series] = None
    元数据: Dict = field(default_factory=dict)


@dataclass
class 对比结果:
    """策略对比分析结果。"""
    
    # 个别策略指标
    策略指标: Dict[str, PerformanceMetrics] = field(default_factory=dict)
    
    # API兼容的排名与评分
    排名: List[Tuple[str, float]] = field(default_factory=list)
    
    # 详细指标（API兼容）
    详细指标: Dict[str, Dict[str, float]] = field(default_factory=dict)
    
    # 对比矩阵
    对比矩阵: pd.DataFrame = field(default_factory=pd.DataFrame)
    
    # 各方法排名
    各方法排名: Dict[str, List[str]] = field(default_factory=dict)
    
    # 相关性分析
    相关性矩阵: Dict[str, Dict[str, float]] = field(default_factory=dict)
    
    # 投资组合优化
    最优权重: Dict[str, float] = field(default_factory=dict)
    
    # 组合投资组合指标
    组合指标: Optional[PerformanceMetrics] = None
    
    # 统计显著性检验
    显著性检验: Dict = field(default_factory=dict)
    
    # 建议
    建议: List[Dict] = field(default_factory=list)
    
    def to_dict(self) -> Dict:
        """将结果转换为字典。"""
        return {
            'strategy_metrics': {
                name: metrics.to_dict() 
                for name, metrics in self.策略指标.items()
            },
            'comparison_matrix': self.对比矩阵.to_dict() if not self.对比矩阵.empty else {},
            'rankings': self.各方法排名,
            'correlation_matrix': self.相关性矩阵,
            'optimal_weights': self.最优权重,
            'combined_metrics': self.组合指标.to_dict() if self.组合指标 else None,
            'significance_tests': self.显著性检验,
            'recommendations': self.建议
        }


class 策略对比器:
    """策略对比和分析引擎。"""
    
    def __init__(self, 无风险利率: float = 0.02):
        """
        初始化策略对比器。
        
        Args:
            无风险利率: 计算使用的无风险利率
        """
        self.无风险利率 = 无风险利率
        self.指标计算器 = MetricsCalculator(无风险利率)
    
    def 对比策略(
        self,
        策略列表: List[策略数据],
        对比方法列表: List[对比方法] = None,
        排名方法: 排名方法 = 排名方法.加权评分
    ) -> 对比结果:
        """
        执行综合策略对比。
        
        Args:
            策略列表: 策略数据列表
            对比方法列表: 用于对比的方法
            排名方法: 策略排名方法
            
        Returns:
            包含分析结果的对比结果
        """
        if len(策略列表) < 2:
            raise ValueError("需要至少两个策略进行对比")
        
        if 对比方法列表 is None:
            对比方法列表 = [
                对比方法.夏普比率,
                对比方法.总收益率,
                对比方法.最大回撤,
                对比方法.胜率
            ]
        
        结果 = 对比结果()
        
        # 计算个别策略指标
        结果.策略指标 = self._计算个别指标(策略列表)
        
        # 创建对比矩阵
        结果.对比矩阵 = self._创建对比矩阵(
            结果.策略指标, 对比方法列表
        )
        
        # 计算排名
        结果.各方法排名 = self._计算排名(
            结果.策略指标, 对比方法列表, 排名方法
        )
        
        # 生成API兼容的排名和评分
        结果.排名, 结果.详细指标 = self._generate_api_ranking(
            结果.策略指标, 排名方法
        )
        
        # 相关性分析
        相关性数据框 = self._calculate_correlation_matrix(策略列表)
        结果.相关性矩阵 = self._convert_correlation_to_dict(相关性数据框)
        
        # 投资组合优化
        结果.最优权重 = self._calculate_optimal_weights(
            策略列表, 结果.策略指标
        )
        
        # 组合投资组合分析
        if 结果.最优权重:
            结果.组合指标 = self._calculate_combined_metrics(
                策略列表, 结果.最优权重
            )
        
        # 统计显著性检验
        结果.显著性检验 = self._perform_significance_tests(策略列表)
        
        # 生成建议
        结果.建议 = self._generate_recommendations(结果)
        
        return 结果
    
    def _计算个别指标(
        self,
        策略列表: List[策略数据]
    ) -> Dict[str, PerformanceMetrics]:
        """计算个别策略的指标。"""
        指标 = {}
        
        for 策略 in 策略列表:
            指标[策略.名称] = self.指标计算器.calculate_metrics(
                策略.组合价值,
                策略.交易记录,
                策略.基准价值
            )
        
        return 指标
    
    def _创建对比矩阵(
        self,
        策略指标: Dict[str, PerformanceMetrics],
        对比方法列表: List[对比方法]
    ) -> pd.DataFrame:
        """为策略创建对比矩阵。"""
        策略名称列表 = list(策略指标.keys())
        指标数据 = []
        
        for 方法 in 对比方法列表:
            行数据 = {}
            for 名称 in 策略名称列表:
                指标 = 策略指标[名称]
                
                if 方法 == 对比方法.夏普比率:
                    行数据[名称] = 指标.sharpe_ratio
                elif 方法 == 对比方法.索提诺比率:
                    行数据[名称] = 指标.sortino_ratio
                elif 方法 == 对比方法.卡尔玛比率:
                    行数据[名称] = 指标.calmar_ratio
                elif 方法 == 对比方法.总收益率:
                    行数据[名称] = 指标.total_return
                elif 方法 == 对比方法.最大回撤:
                    行数据[名称] = 指标.max_drawdown
                elif 方法 == 对比方法.胜率:
                    行数据[名称] = 指标.win_rate
                elif 方法 == 对比方法.盈利因子:
                    行数据[名称] = 指标.profit_factor
            
            指标数据.append(行数据)
        
        对比数据框 = pd.DataFrame(
            指标数据,
            index=[方法.value for 方法 in 对比方法列表]
        )
        
        return 对比数据框
    
    def _计算排名(
        self,
        策略指标: Dict[str, PerformanceMetrics],
        对比方法列表: List[对比方法],
        排名方法: 排名方法
    ) -> Dict[str, List[str]]:
        """使用各种方法计算策略排名。"""
        排名结果 = {}
        策略名称列表 = list(策略指标.keys())
        
        # 各指标排名
        for 方法 in 对比方法列表:
            指标值列表 = []
            
            for 名称 in 策略名称列表:
                指标 = 策略指标[名称]
                
                if 方法 == 对比方法.夏普比率:
                    指标值列表.append((名称, 指标.sharpe_ratio))
                elif 方法 == 对比方法.索提诺比率:
                    指标值列表.append((名称, 指标.sortino_ratio))
                elif 方法 == 对比方法.卡尔玛比率:
                    指标值列表.append((名称, 指标.calmar_ratio))
                elif 方法 == 对比方法.总收益率:
                    指标值列表.append((名称, 指标.total_return))
                elif 方法 == 对比方法.最大回撤:
                    # 对于回撤，较小（较少的负值）较好
                    指标值列表.append((名称, -指标.max_drawdown))
                elif 方法 == 对比方法.胜率:
                    指标值列表.append((名称, 指标.win_rate))
                elif 方法 == 对比方法.盈利因子:
                    指标值列表.append((名称, 指标.profit_factor))
            
            # 按值排序（大多数指标为降序）
            指标值列表.sort(key=lambda x: x[1], reverse=True)
            排名结果[方法.value] = [名称 for 名称, _ in 指标值列表]
        
        # 基于方法的总体排名
        if 排名方法 == 排名方法.加权评分:
            排名结果['overall'] = self._calculate_weighted_ranking(策略指标)
        elif 排名方法 == 排名方法.风险调整收益:
            排名结果['overall'] = self._calculate_risk_adjusted_ranking(策略指标)
        elif 排名方法 == 排名方法.一致性:
            排名结果['overall'] = self._calculate_consistency_ranking(策略指标)
        elif 排名方法 == 排名方法.分散化收益:
            排名结果['overall'] = self._calculate_diversification_ranking(策略指标)
        elif 排名方法 == 排名方法.多目标优化:
            排名结果['overall'] = self._calculate_multi_objective_ranking(策略指标)
        
        return 排名结果
    
    def _calculate_weighted_ranking(
        self,
        strategy_metrics: Dict[str, PerformanceMetrics]
    ) -> List[str]:
        """Calculate weighted score ranking."""
        # Define weights for different metrics
        weights = {
            'sharpe_ratio': 0.3,
            'total_return': 0.2,
            'max_drawdown': 0.2,  # Will be inverted
            'win_rate': 0.1,
            'calmar_ratio': 0.1,
            'sortino_ratio': 0.1
        }
        
        scores = {}
        
        # Normalize metrics across strategies
        all_metrics = list(strategy_metrics.values())
        
        # Get ranges for normalization
        sharpe_values = [m.sharpe_ratio for m in all_metrics]
        return_values = [m.total_return for m in all_metrics]
        drawdown_values = [m.max_drawdown for m in all_metrics]
        winrate_values = [m.win_rate for m in all_metrics]
        calmar_values = [m.calmar_ratio for m in all_metrics]
        sortino_values = [m.sortino_ratio for m in all_metrics]
        
        sharpe_range = max(sharpe_values) - min(sharpe_values)
        return_range = max(return_values) - min(return_values)
        drawdown_range = max(drawdown_values) - min(drawdown_values)
        winrate_range = max(winrate_values) - min(winrate_values)
        calmar_range = max(calmar_values) - min(calmar_values)
        sortino_range = max(sortino_values) - min(sortino_values)
        
        for name, metrics in strategy_metrics.items():
            score = 0
            
            # Normalize and weight each metric
            if sharpe_range > 0:
                norm_sharpe = (metrics.sharpe_ratio - min(sharpe_values)) / sharpe_range
                score += weights['sharpe_ratio'] * norm_sharpe
            
            if return_range > 0:
                norm_return = (metrics.total_return - min(return_values)) / return_range
                score += weights['total_return'] * norm_return
            
            if drawdown_range > 0:
                # For drawdown, lower (less negative) is better
                norm_drawdown = (max(drawdown_values) - metrics.max_drawdown) / drawdown_range
                score += weights['max_drawdown'] * norm_drawdown
            
            if winrate_range > 0:
                norm_winrate = (metrics.win_rate - min(winrate_values)) / winrate_range
                score += weights['win_rate'] * norm_winrate
            
            if calmar_range > 0:
                norm_calmar = (metrics.calmar_ratio - min(calmar_values)) / calmar_range
                score += weights['calmar_ratio'] * norm_calmar
                
            if sortino_range > 0:
                norm_sortino = (metrics.sortino_ratio - min(sortino_values)) / sortino_range
                score += weights['sortino_ratio'] * norm_sortino
            
            scores[name] = score
        
        # Sort by score (descending)
        return sorted(scores.keys(), key=lambda x: scores[x], reverse=True)
    
    def _calculate_risk_adjusted_ranking(
        self,
        strategy_metrics: Dict[str, PerformanceMetrics]
    ) -> List[str]:
        """Calculate ranking based on risk-adjusted returns."""
        risk_adjusted_scores = {}
        
        for name, metrics in strategy_metrics.items():
            # Use Sharpe ratio as primary, but consider other factors
            score = metrics.sharpe_ratio
            
            # Penalty for high drawdown
            if metrics.max_drawdown < -0.1:  # More than 10% drawdown
                score *= (1 + metrics.max_drawdown)  # Reduce score
            
            # Bonus for low volatility
            if metrics.volatility < 0.15:  # Less than 15% volatility
                score *= 1.1
            
            risk_adjusted_scores[name] = score
        
        return sorted(risk_adjusted_scores.keys(), 
                     key=lambda x: risk_adjusted_scores[x], reverse=True)
    
    def _calculate_consistency_ranking(
        self,
        strategy_metrics: Dict[str, PerformanceMetrics]
    ) -> List[str]:
        """Calculate ranking based on consistency metrics."""
        consistency_scores = {}
        
        for name, metrics in strategy_metrics.items():
            score = 0
            
            # Reward high win rate
            score += metrics.win_rate * 0.4
            
            # Reward low volatility
            if metrics.volatility > 0:
                score += (1 / metrics.volatility) * 0.2
            
            # Reward low drawdown
            score += (1 / max(abs(metrics.max_drawdown), 0.01)) * 0.2
            
            # Reward positive skewness
            if metrics.skewness > 0:
                score += metrics.skewness * 0.1
            
            # Reward low kurtosis (less extreme returns)
            if metrics.kurtosis < 3:
                score += (3 - metrics.kurtosis) * 0.1
            
            consistency_scores[name] = score
        
        return sorted(consistency_scores.keys(),
                     key=lambda x: consistency_scores[x], reverse=True)
    
    def _calculate_correlation_matrix(
        self,
        strategies: List[策略数据]
    ) -> pd.DataFrame:
        """Calculate correlation matrix between strategies."""
        returns_data = {}
        
        # Calculate returns for each strategy
        for strategy in strategies:
            returns = strategy.组合价值.pct_change().dropna()
            returns_data[strategy.名称] = returns
        
        # Create DataFrame and calculate correlation
        returns_df = pd.DataFrame(returns_data)
        
        # Handle misaligned indices
        returns_df = returns_df.dropna()
        
        if returns_df.empty:
            return pd.DataFrame()
        
        return returns_df.corr()
    
    def _calculate_optimal_weights(
        self,
        strategies: List[策略数据],
        strategy_metrics: Dict[str, PerformanceMetrics]
    ) -> Dict[str, float]:
        """Calculate optimal portfolio weights using mean-variance optimization."""
        try:
            # Get returns data
            returns_data = {}
            for strategy in strategies:
                returns = strategy.组合价值.pct_change().dropna()
                returns_data[strategy.名称] = returns
            
            returns_df = pd.DataFrame(returns_data).dropna()
            
            if len(returns_df) < 10:  # Need sufficient data
                return self._calculate_equal_weights(strategies)
            
            # Calculate expected returns and covariance matrix
            expected_returns = returns_df.mean() * 252  # Annualized
            cov_matrix = returns_df.cov() * 252  # Annualized
            
            # Simple mean-variance optimization (equal risk contribution)
            n_assets = len(strategies)
            
            # Equal risk contribution approach
            volatilities = np.sqrt(np.diag(cov_matrix))
            inv_vol_weights = 1.0 / volatilities
            weights = inv_vol_weights / inv_vol_weights.sum()
            
            return dict(zip([s.name for s in strategies], weights))
            
        except Exception:
            # Fallback to equal weights
            return self._calculate_equal_weights(strategies)
    
    def _calculate_equal_weights(self, strategies: List[策略数据]) -> Dict[str, float]:
        """Calculate equal weights for strategies."""
        n_strategies = len(strategies)
        equal_weight = 1.0 / n_strategies
        return {strategy.名称: equal_weight for strategy in strategies}
    
    def _calculate_combined_metrics(
        self,
        strategies: List[策略数据],
        weights: Dict[str, float]
    ) -> PerformanceMetrics:
        """Calculate metrics for the combined portfolio."""
        # Create combined portfolio values
        combined_values = None
        
        for strategy in strategies:
            weight = weights.get(strategy.名称, 0)
            if weight > 0:
                strategy_contribution = strategy.组合价值 * weight
                if combined_values is None:
                    combined_values = strategy_contribution
                else:
                    # Align indices
                    common_index = combined_values.index.intersection(strategy_contribution.index)
                    combined_values = combined_values.loc[common_index] + strategy_contribution.loc[common_index]
        
        if combined_values is None or len(combined_values) < 2:
            return PerformanceMetrics()
        
        # Calculate metrics for combined portfolio
        return self.指标计算器.calculate_metrics(combined_values)
    
    def _perform_significance_tests(
        self,
        strategies: List[策略数据]
    ) -> Dict:
        """Perform statistical significance tests."""
        # This is a simplified implementation
        # In practice, you'd use proper statistical tests like t-tests
        
        results = {}
        
        # Compare each pair of strategies
        for i, strategy1 in enumerate(strategies):
            for j, strategy2 in enumerate(strategies[i+1:], i+1):
                returns1 = strategy1.组合价值.pct_change().dropna()
                returns2 = strategy2.组合价值.pct_change().dropna()
                
                # Align the returns
                common_index = returns1.index.intersection(returns2.index)
                if len(common_index) > 30:  # Need sufficient data
                    aligned_returns1 = returns1.loc[common_index]
                    aligned_returns2 = returns2.loc[common_index]
                    
                    # Simple t-test approximation
                    diff_returns = aligned_returns1 - aligned_returns2
                    mean_diff = diff_returns.mean()
                    std_diff = diff_returns.std()
                    
                    if std_diff > 0:
                        t_stat = mean_diff / (std_diff / np.sqrt(len(diff_returns)))
                        # Very simplified p-value approximation
                        p_value = 2 * (1 - 0.95) if abs(t_stat) > 1.96 else 0.5
                    else:
                        t_stat = 0
                        p_value = 1.0
                    
                    pair_key = f"{strategy1.名称}_vs_{strategy2.名称}"
                    results[pair_key] = {
                        'mean_difference': mean_diff,
                        't_statistic': t_stat,
                        'p_value': p_value,
                        'significant': p_value < 0.05
                    }
        
        return results
    
    def _generate_recommendations(self, result: 对比结果) -> List[Dict]:
        """Generate strategy recommendations based on analysis."""
        recommendations = []
        
        # Get best performing strategy
        if 'overall' in result.各方法排名:
            best_strategy = result.各方法排名['overall'][0]
            recommendations.append({
                'type': 'best_single_strategy',
                'strategy': best_strategy,
                'reason': f"Highest overall ranking based on multiple metrics",
                'action': 'consider_primary_allocation'
            })
        
        # Portfolio diversification recommendation
        if result.相关性矩阵:
            # 简化处理，因为相关性矩阵已经是字典格式
            min_correlation = 1.0
            for strategy1_name, correlations in result.相关性矩阵.items():
                for strategy2_name, corr_value in correlations.items():
                    if strategy1_name != strategy2_name and corr_value < min_correlation:
                        min_correlation = corr_value
            
            if min_correlation < 0.7:
                recommendations.append({
                    'type': 'diversification_benefit',
                    'value': min_correlation,
                    'reason': f"Low correlation ({min_correlation:.3f}) suggests diversification benefits",
                    'action': 'consider_multi_strategy_portfolio'
                })
        
        # High drawdown warning
        for name, metrics in result.策略指标.items():
            if metrics.max_drawdown < -0.25:  # More than 25% drawdown
                recommendations.append({
                    'type': 'high_risk_warning',
                    'strategy': name,
                    'value': metrics.max_drawdown,
                    'reason': f"Maximum drawdown of {metrics.max_drawdown:.1%} may be too high",
                    'action': 'review_risk_management'
                })
        
        # Consistency recommendation
        consistent_strategies = []
        for name, metrics in result.策略指标.items():
            if (metrics.sharpe_ratio > 1.0 and 
                metrics.max_drawdown > -0.15 and 
                metrics.win_rate > 0.5):
                consistent_strategies.append(name)
        
        if consistent_strategies:
            recommendations.append({
                'type': 'consistent_performance',
                'strategies': consistent_strategies,
                'reason': "These strategies show consistent risk-adjusted returns",
                'action': 'consider_core_allocation'
            })
        
        return recommendations
    
    def generate_comparison_report(
        self,
        result: 对比结果,
        include_details: bool = True
    ) -> Dict:
        """
        Generate formatted comparison report.
        
        Args:
            result: Comparison result
            include_details: Whether to include detailed analysis
            
        Returns:
            Formatted report dictionary
        """
        report = {
            'summary': {
                'strategies_compared': len(result.策略指标),
                'comparison_date': datetime.now().isoformat(),
                'top_strategy': result.各方法排名.get('overall', ['Unknown'])[0] if result.各方法排名 else 'Unknown'
            },
            'rankings': result.各方法排名,
            'key_metrics': {}
        }
        
        # Add key metrics for each strategy
        for name, metrics in result.策略指标.items():
            report['key_metrics'][name] = {
                'total_return_pct': round(metrics.total_return * 100, 2),
                'sharpe_ratio': round(metrics.sharpe_ratio, 3),
                'max_drawdown_pct': round(metrics.max_drawdown * 100, 2),
                'volatility_pct': round(metrics.volatility * 100, 2),
                'win_rate_pct': round(metrics.win_rate * 100, 1) if metrics.total_trades > 0 else None
            }
        
        if include_details:
            report['detailed_analysis'] = {
                'correlation_matrix': result.相关性矩阵,
                'optimal_weights': {k: round(v, 3) for k, v in result.最优权重.items()},
                'combined_portfolio': result.组合指标.to_dict() if result.组合指标 else None,
                'recommendations': result.建议
            }
        
        return report
    
    def _generate_api_ranking(
        self,
        strategy_metrics: Dict[str, PerformanceMetrics],
        ranking_method: 排名方法
    ) -> Tuple[List[Tuple[str, float]], Dict[str, Dict[str, float]]]:
        """生成API兼容的排名和详细指标"""
        
        # 计算每个策略的评分
        scores = {}
        detailed_metrics = {}
        
        for name, metrics in strategy_metrics.items():
            # 提取详细指标
            detailed_metrics[name] = {
                'sharpe_ratio': metrics.sharpe_ratio,
                'sortino_ratio': metrics.sortino_ratio,
                'calmar_ratio': metrics.calmar_ratio,
                'total_return': metrics.total_return,
                'max_drawdown': metrics.max_drawdown,
                'volatility': metrics.volatility,
                'win_rate': metrics.win_rate,
                'profit_factor': metrics.profit_factor,
                'skewness': metrics.skewness,
                'kurtosis': metrics.kurtosis,
                'total_trades': metrics.total_trades,
                'avg_drawdown_duration': getattr(metrics, 'avg_drawdown_duration', 0)
            }
            
            # 根据排名方法计算评分
            if ranking_method == 排名方法.加权评分:
                scores[name] = self._calculate_weighted_score(metrics, strategy_metrics)
            elif ranking_method == 排名方法.风险调整收益:
                scores[name] = self._calculate_risk_adjusted_score(metrics)
            elif ranking_method == 排名方法.一致性:
                scores[name] = self._calculate_consistency_score(metrics)
            elif ranking_method == 排名方法.分散化收益:
                scores[name] = self._calculate_diversification_score(metrics)
            elif ranking_method == 排名方法.多目标优化:
                scores[name] = self._calculate_multi_objective_score(metrics, strategy_metrics)
            else:
                scores[name] = metrics.sharpe_ratio  # 默认使用夏普比率
        
        # 按评分排序
        ranking = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        return ranking, detailed_metrics
    
    def _calculate_weighted_score(
        self, 
        metrics: PerformanceMetrics, 
        all_metrics: Dict[str, PerformanceMetrics]
    ) -> float:
        """计算加权评分"""
        # 定义权重
        weights = {
            'sharpe_ratio': 0.25,
            'total_return': 0.20,
            'max_drawdown': 0.20,  # 将被反转
            'win_rate': 0.15,
            'calmar_ratio': 0.10,
            'sortino_ratio': 0.10
        }
        
        # 获取所有策略的指标范围用于标准化
        all_values = list(all_metrics.values())
        
        # 计算标准化评分
        score = 0
        
        # 夏普比率
        sharpe_values = [m.sharpe_ratio for m in all_values]
        if max(sharpe_values) > min(sharpe_values):
            norm_sharpe = (metrics.sharpe_ratio - min(sharpe_values)) / (max(sharpe_values) - min(sharpe_values))
            score += weights['sharpe_ratio'] * norm_sharpe
        
        # 总收益
        return_values = [m.total_return for m in all_values]
        if max(return_values) > min(return_values):
            norm_return = (metrics.total_return - min(return_values)) / (max(return_values) - min(return_values))
            score += weights['total_return'] * norm_return
        
        # 最大回撤（越小越好）
        drawdown_values = [m.max_drawdown for m in all_values]
        if max(drawdown_values) > min(drawdown_values):
            norm_drawdown = (max(drawdown_values) - metrics.max_drawdown) / (max(drawdown_values) - min(drawdown_values))
            score += weights['max_drawdown'] * norm_drawdown
        
        # 胜率
        winrate_values = [m.win_rate for m in all_values]
        if max(winrate_values) > min(winrate_values):
            norm_winrate = (metrics.win_rate - min(winrate_values)) / (max(winrate_values) - min(winrate_values))
            score += weights['win_rate'] * norm_winrate
        
        # 卡尔玛比率
        calmar_values = [m.calmar_ratio for m in all_values]
        if max(calmar_values) > min(calmar_values):
            norm_calmar = (metrics.calmar_ratio - min(calmar_values)) / (max(calmar_values) - min(calmar_values))
            score += weights['calmar_ratio'] * norm_calmar
        
        # 索提诺比率
        sortino_values = [m.sortino_ratio for m in all_values]
        if max(sortino_values) > min(sortino_values):
            norm_sortino = (metrics.sortino_ratio - min(sortino_values)) / (max(sortino_values) - min(sortino_values))
            score += weights['sortino_ratio'] * norm_sortino
        
        return score
    
    def _calculate_risk_adjusted_score(self, metrics: PerformanceMetrics) -> float:
        """计算风险调整评分"""
        base_score = metrics.sharpe_ratio
        
        # 高回撤惩罚
        if metrics.max_drawdown < -0.15:
            base_score *= (1 + metrics.max_drawdown * 0.5)
        
        # 低波动奖励
        if metrics.volatility < 0.15:
            base_score *= 1.1
        
        # 高胜率奖励
        if metrics.win_rate > 0.6:
            base_score *= 1.05
        
        return base_score
    
    def _calculate_consistency_score(self, metrics: PerformanceMetrics) -> float:
        """计算一致性评分"""
        score = 0
        
        # 胜率权重
        score += metrics.win_rate * 0.3
        
        # 低波动性奖励
        if metrics.volatility > 0:
            score += (1 / (1 + metrics.volatility)) * 0.2
        
        # 低回撤奖励
        score += (1 / (1 + abs(metrics.max_drawdown))) * 0.2
        
        # 正偏度奖励
        if metrics.skewness > 0:
            score += min(metrics.skewness, 2.0) * 0.1
        
        # 低峰度奖励（避免极端收益）
        if metrics.kurtosis < 3:
            score += (3 - min(metrics.kurtosis, 3)) * 0.1
        
        # 基础夏普比率
        score += max(metrics.sharpe_ratio, 0) * 0.1
        
        return score
    
    def _calculate_diversification_score(self, metrics: PerformanceMetrics) -> float:
        """计算多样化收益评分"""
        # 基础评分基于夏普比率
        base_score = metrics.sharpe_ratio
        
        # 适中波动性奖励（不要太高也不要太低）
        if 0.10 <= metrics.volatility <= 0.20:
            base_score *= 1.1
        
        # 适中收益奖励
        if 0.05 <= metrics.total_return <= 0.50:
            base_score *= 1.05
        
        return base_score
    
    def _calculate_multi_objective_score(
        self, 
        metrics: PerformanceMetrics,
        all_metrics: Dict[str, PerformanceMetrics]
    ) -> float:
        """计算多目标优化评分"""
        # 收益目标权重：30%
        return_score = max(metrics.total_return, 0)
        
        # 风险目标权重：30%
        risk_score = max(metrics.sharpe_ratio, 0)
        
        # 稳定性目标权重：25%
        stability_score = metrics.win_rate * (1 / (1 + abs(metrics.max_drawdown)))
        
        # 一致性目标权重：15%
        consistency_score = 0
        if metrics.volatility > 0:
            consistency_score = min(metrics.total_return / metrics.volatility, 5.0)
        
        # 加权组合
        total_score = (
            return_score * 0.30 +
            risk_score * 0.30 +
            stability_score * 0.25 +
            consistency_score * 0.15
        )
        
        return total_score
    
    def _calculate_diversification_ranking(
        self,
        strategy_metrics: Dict[str, PerformanceMetrics]
    ) -> List[str]:
        """计算多样化收益排名"""
        scores = {}
        
        for name, metrics in strategy_metrics.items():
            scores[name] = self._calculate_diversification_score(metrics)
        
        return sorted(scores.keys(), key=lambda x: scores[x], reverse=True)
    
    def _calculate_multi_objective_ranking(
        self,
        strategy_metrics: Dict[str, PerformanceMetrics]
    ) -> List[str]:
        """计算多目标优化排名"""
        scores = {}
        
        for name, metrics in strategy_metrics.items():
            scores[name] = self._calculate_multi_objective_score(metrics, strategy_metrics)
        
        return sorted(scores.keys(), key=lambda x: scores[x], reverse=True)
    
    def _convert_correlation_to_dict(self, correlation_df: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """将相关性矩阵转换为字典格式"""
        if correlation_df.empty:
            return {}
        
        result = {}
        for index in correlation_df.index:
            result[index] = {}
            for column in correlation_df.columns:
                result[index][column] = float(correlation_df.loc[index, column])
        
        return result


# 英文别名，用于API兼容性
StrategyComparator = 策略对比器
StrategyData = 策略数据
ComparisonMethod = 对比方法
RankingMethod = 排名方法
ComparisonResult = 对比结果