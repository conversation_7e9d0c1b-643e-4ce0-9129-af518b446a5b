import logging
logger = logging.getLogger(__name__)
"""
Custom Indicator Framework

This module provides a framework for creating and managing custom technical indicators
with formula parsing, validation, testing, and documentation generation capabilities.
"""

import pandas as pd
import numpy as np
import ast
import operator
import re
import inspect
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Callable, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import json

from .engine import IndicatorConfig, IndicatorEngine


@dataclass
class IndicatorMetadata:
    """Metadata for custom indicators"""
    name: str
    description: str
    category: str
    author: str = ""
    version: str = "1.0.0"
    created_date: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)
    references: List[str] = field(default_factory=list)
    examples: List[Dict[str, Any]] = field(default_factory=list)


class BaseCustomIndicator(ABC):
    """Abstract base class for custom indicators"""
    
    def __init__(self, name: str, metadata: Optional[IndicatorMetadata] = None):
        self.name = name
        self.metadata = metadata or IndicatorMetadata(
            name=name,
            description="Custom indicator",
            category="custom"
        )
        self._parameters = {}
        self._required_columns = []
        self._parameter_validators = {}
    
    @abstractmethod
    def calculate(self, data: Union[pd.Series, pd.DataFrame], **params) -> Union[pd.Series, Dict[str, pd.Series]]:
        """Calculate the indicator values"""
        pass
    
    def get_default_parameters(self) -> Dict[str, Any]:
        """Get default parameters for the indicator"""
        return self._parameters.copy()
    
    def get_required_columns(self) -> List[str]:
        """Get required data columns"""
        return self._required_columns.copy()
    
    def get_parameter_validators(self) -> Dict[str, Callable]:
        """Get parameter validators"""
        return self._parameter_validators.copy()
    
    def validate_data(self, data: Union[pd.Series, pd.DataFrame]) -> None:
        """Validate input data"""
        if isinstance(data, pd.DataFrame):
            missing_columns = [col for col in self._required_columns if col not in data.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
        elif self._required_columns and self._required_columns != ["close"]:
            raise ValueError(f"DataFrame required for columns: {self._required_columns}")
    
    def validate_parameters(self, params: Dict[str, Any]) -> None:
        """Validate parameters"""
        for param_name, validator in self._parameter_validators.items():
            if param_name in params and not validator(params[param_name]):
                raise ValueError(f"Invalid parameter {param_name}: {params[param_name]}")
    
    def to_config(self) -> IndicatorConfig:
        """Convert to IndicatorConfig for engine registration"""
        return IndicatorConfig(
            name=self.name,
            function=self.calculate,
            default_params=self.get_default_parameters(),
            required_columns=self.get_required_columns(),
            param_validators=self.get_parameter_validators(),
            description=self.metadata.description,
            category=self.metadata.category
        )
    
    def generate_documentation(self) -> str:
        """Generate documentation for the indicator"""
        doc = f"# {self.name}\n\n"
        doc += f"**Description:** {self.metadata.description}\n\n"
        doc += f"**Category:** {self.metadata.category}\n\n"
        
        if self.metadata.author:
            doc += f"**Author:** {self.metadata.author}\n\n"
        
        if self.metadata.tags:
            doc += f"**Tags:** {', '.join(self.metadata.tags)}\n\n"
        
        # Parameters
        if self._parameters:
            doc += "## Parameters\n\n"
            for param, default_value in self._parameters.items():
                doc += f"- **{param}**: {default_value} (default)\n"
            doc += "\n"
        
        # Required columns
        if self._required_columns:
            doc += "## Required Data Columns\n\n"
            for col in self._required_columns:
                doc += f"- {col}\n"
            doc += "\n"
        
        # Examples
        if self.metadata.examples:
            doc += "## Examples\n\n"
            for i, example in enumerate(self.metadata.examples, 1):
                doc += f"### Example {i}\n\n"
                if 'description' in example:
                    doc += f"{example['description']}\n\n"
                if 'code' in example:
                    doc += f"```python\n{example['code']}\n```\n\n"
        
        # References
        if self.metadata.references:
            doc += "## References\n\n"
            for ref in self.metadata.references:
                doc += f"- {ref}\n"
            doc += "\n"
        
        return doc


class FormulaIndicator(BaseCustomIndicator):
    """Indicator defined by a mathematical formula"""
    
    def __init__(self, name: str, formula: str, metadata: Optional[IndicatorMetadata] = None):
        super().__init__(name, metadata)
        self.formula = formula
        self._compiled_formula = None
        self._parse_formula()
    
    def _parse_formula(self) -> None:
        """Parse and validate the formula"""
        try:
            # Parse the formula to AST
            parsed = ast.parse(self.formula, mode='eval')
            self._compiled_formula = compile(parsed, '<formula>', 'eval')
            
            # Extract variable names from formula
            self._extract_variables(parsed)
            
        except SyntaxError as e:
            raise ValueError(f"Invalid formula syntax: {e}")
    
    def _extract_variables(self, node: ast.AST) -> None:
        """Extract variable names from AST"""
        variables = set()
        
        for child in ast.walk(node):
            if isinstance(child, ast.Name):
                variables.add(child.id)
        
        # Filter out built-in functions, operators, and numpy/pandas
        builtin_names = {'abs', 'max', 'min', 'sum', 'len', 'round', 'pow', 'np', 'pd'}
        variables = variables - builtin_names
        
        # Assume variables are either column names or parameters
        common_columns = {'open', 'high', 'low', 'close', 'volume'}
        self._required_columns = [var for var in variables if var in common_columns]
        
        # Remaining variables are parameters (with default values)
        param_vars = variables - set(self._required_columns)
        for var in param_vars:
            if var not in self._parameters:
                self._parameters[var] = 1.0  # Default parameter value
    
    def calculate(self, data: Union[pd.Series, pd.DataFrame], **params) -> pd.Series:
        """Calculate the formula-based indicator"""
        self.validate_data(data)
        self.validate_parameters(params)
        
        # Prepare namespace for formula evaluation
        namespace = {
            'np': np,
            'pd': pd,
            'abs': abs,
            'max': max,
            'min': min,
            'sum': sum,
            'len': len,
            'round': round,
            'pow': pow,
        }
        
        # Add data columns
        if isinstance(data, pd.DataFrame):
            for col in data.columns:
                namespace[col] = data[col]
        else:
            namespace['close'] = data
        
        # Add parameters (but don't override numpy/pandas)
        final_params = {**self._parameters, **params}
        for key, value in final_params.items():
            if key not in ['np', 'pd']:  # Don't override numpy/pandas
                namespace[key] = value
        
        # Evaluate formula
        try:
            result = eval(self._compiled_formula, namespace)
            
            # Ensure result is a pandas Series
            if isinstance(result, (int, float)):
                result = pd.Series([result] * len(data), index=data.index)
            elif isinstance(result, np.ndarray):
                result = pd.Series(result, index=data.index)
            elif not isinstance(result, pd.Series):
                raise ValueError(f"Formula must return a numeric value, array, or Series")
            
            return result
            
        except Exception as e:
            raise RuntimeError(f"Error evaluating formula: {e}")


class CompositeIndicator(BaseCustomIndicator):
    """Indicator composed of multiple other indicators"""
    
    def __init__(self, name: str, components: List[Tuple[str, Dict[str, Any]]], 
                 combination_func: Callable, metadata: Optional[IndicatorMetadata] = None):
        super().__init__(name, metadata)
        self.components = components  # List of (indicator_name, params)
        self.combination_func = combination_func
        self._engine = None
    
    def set_engine(self, engine: IndicatorEngine) -> None:
        """Set the indicator engine for calculating components"""
        self._engine = engine
    
    def calculate(self, data: Union[pd.Series, pd.DataFrame], **params) -> Union[pd.Series, Dict[str, pd.Series]]:
        """Calculate the composite indicator"""
        if self._engine is None:
            raise RuntimeError("Engine not set. Call set_engine() first.")
        
        self.validate_data(data)
        self.validate_parameters(params)
        
        # Calculate all component indicators
        component_results = {}
        for indicator_name, indicator_params in self.components:
            result = self._engine.calculate(indicator_name, data, **indicator_params)
            component_results[indicator_name] = result.data
        
        # Combine results using the combination function
        try:
            combined_result = self.combination_func(component_results, **params)
            return combined_result
        except Exception as e:
            raise RuntimeError(f"Error combining indicators: {e}")


class IndicatorTemplate:
    """Template for creating similar indicators with different parameters"""
    
    def __init__(self, name: str, base_indicator: BaseCustomIndicator, 
                 parameter_sets: List[Dict[str, Any]]):
        self.name = name
        self.base_indicator = base_indicator
        self.parameter_sets = parameter_sets
    
    def create_indicators(self) -> List[BaseCustomIndicator]:
        """Create indicator instances for each parameter set"""
        indicators = []
        
        for i, params in enumerate(self.parameter_sets):
            # Create a copy of the base indicator
            indicator_name = f"{self.name}_{i+1}"
            
            if isinstance(self.base_indicator, FormulaIndicator):
                indicator = FormulaIndicator(
                    indicator_name,
                    self.base_indicator.formula,
                    self.base_indicator.metadata
                )
            else:
                # For other types, we'd need specific copying logic
                raise NotImplementedError("Template copying not implemented for this indicator type")
            
            # Update default parameters
            indicator._parameters.update(params)
            indicators.append(indicator)
        
        return indicators


class CustomIndicatorManager:
    """Manager for custom indicators"""
    
    def __init__(self, engine: Optional[IndicatorEngine] = None):
        self.engine = engine
        self.indicators: Dict[str, BaseCustomIndicator] = {}
        self.templates: Dict[str, IndicatorTemplate] = {}
    
    def register_indicator(self, indicator: BaseCustomIndicator) -> None:
        """Register a custom indicator"""
        self.indicators[indicator.name] = indicator
        
        # Register with engine if available
        if self.engine:
            if isinstance(indicator, CompositeIndicator):
                indicator.set_engine(self.engine)
            self.engine.register_indicator(indicator.to_config())
    
    def create_formula_indicator(self, name: str, formula: str, 
                               metadata: Optional[IndicatorMetadata] = None) -> FormulaIndicator:
        """Create and register a formula-based indicator"""
        indicator = FormulaIndicator(name, formula, metadata)
        self.register_indicator(indicator)
        return indicator
    
    def create_composite_indicator(self, name: str, components: List[Tuple[str, Dict[str, Any]]], 
                                 combination_func: Callable,
                                 metadata: Optional[IndicatorMetadata] = None) -> CompositeIndicator:
        """Create and register a composite indicator"""
        indicator = CompositeIndicator(name, components, combination_func, metadata)
        self.register_indicator(indicator)
        return indicator
    
    def get_indicator(self, name: str) -> Optional[BaseCustomIndicator]:
        """Get a registered indicator"""
        return self.indicators.get(name)
    
    def list_indicators(self) -> List[str]:
        """List all registered custom indicators"""
        return list(self.indicators.keys())
    
    def remove_indicator(self, name: str) -> bool:
        """Remove a custom indicator"""
        if name in self.indicators:
            del self.indicators[name]
            return True
        return False
    
    def export_indicator(self, name: str, filepath: str) -> None:
        """Export indicator definition to file"""
        if name not in self.indicators:
            raise ValueError(f"Indicator '{name}' not found")
        
        indicator = self.indicators[name]
        
        export_data = {
            'name': indicator.name,
            'type': indicator.__class__.__name__,
            'metadata': {
                'description': indicator.metadata.description,
                'category': indicator.metadata.category,
                'author': indicator.metadata.author,
                'version': indicator.metadata.version,
                'tags': indicator.metadata.tags,
                'references': indicator.metadata.references,
                'examples': indicator.metadata.examples
            },
            'parameters': indicator.get_default_parameters(),
            'required_columns': indicator.get_required_columns()
        }
        
        if isinstance(indicator, FormulaIndicator):
            export_data['formula'] = indicator.formula
        
        with open(filepath, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
    
    def import_indicator(self, filepath: str) -> BaseCustomIndicator:
        """Import indicator definition from file"""
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        # Create metadata
        metadata = IndicatorMetadata(
            name=data['name'],
            description=data['metadata']['description'],
            category=data['metadata']['category'],
            author=data['metadata'].get('author', ''),
            version=data['metadata'].get('version', '1.0.0'),
            tags=data['metadata'].get('tags', []),
            references=data['metadata'].get('references', []),
            examples=data['metadata'].get('examples', [])
        )
        
        # Create indicator based on type
        if data['type'] == 'FormulaIndicator':
            indicator = FormulaIndicator(data['name'], data['formula'], metadata)
        else:
            raise ValueError(f"Unsupported indicator type: {data['type']}")
        
        # Update parameters
        indicator._parameters.update(data.get('parameters', {}))
        indicator._required_columns = data.get('required_columns', [])
        
        self.register_indicator(indicator)
        return indicator
    
    def generate_documentation(self, output_dir: str) -> None:
        """Generate documentation for all custom indicators"""
        import os
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate individual indicator docs
        for name, indicator in self.indicators.items():
            doc_content = indicator.generate_documentation()
            
            with open(os.path.join(output_dir, f"{name}.md"), 'w') as f:
                f.write(doc_content)
        
        # Generate index file
        index_content = "# Custom Indicators\n\n"
        index_content += "This directory contains documentation for custom technical indicators.\n\n"
        index_content += "## Available Indicators\n\n"
        
        for name, indicator in self.indicators.items():
            index_content += f"- [{name}]({name}.md) - {indicator.metadata.description}\n"
        
        with open(os.path.join(output_dir, "README.md"), 'w') as f:
            f.write(index_content)


class IndicatorTestFramework:
    """Framework for testing custom indicators"""
    
    def __init__(self):
        self.test_cases: List[Dict[str, Any]] = []
    
    def add_test_case(self, name: str, indicator: BaseCustomIndicator, 
                     test_data: Union[pd.Series, pd.DataFrame],
                     expected_result: Union[pd.Series, Dict[str, pd.Series], Callable],
                     params: Optional[Dict[str, Any]] = None,
                     tolerance: float = 1e-10) -> None:
        """Add a test case for an indicator"""
        self.test_cases.append({
            'name': name,
            'indicator': indicator,
            'test_data': test_data,
            'expected_result': expected_result,
            'params': params or {},
            'tolerance': tolerance
        })
    
    def run_tests(self) -> Dict[str, bool]:
        """Run all test cases"""
        results = {}
        
        for test_case in self.test_cases:
            try:
                # Calculate indicator
                result = test_case['indicator'].calculate(
                    test_case['test_data'], 
                    **test_case['params']
                )
                
                # Check result
                expected = test_case['expected_result']
                
                if callable(expected):
                    # Custom validation function
                    passed = expected(result)
                elif isinstance(expected, dict) and isinstance(result, dict):
                    # Multiple series comparison
                    passed = True
                    for key in expected:
                        if key not in result:
                            passed = False
                            break
                        if not self._compare_series(result[key], expected[key], test_case['tolerance']):
                            passed = False
                            break
                elif isinstance(expected, pd.Series) and isinstance(result, pd.Series):
                    # Single series comparison
                    passed = self._compare_series(result, expected, test_case['tolerance'])
                else:
                    passed = False
                
                results[test_case['name']] = passed
                
            except Exception as e:
                results[test_case['name']] = False
                logger.info(f"Test '{test_case['name']}' failed with error: {e}")
        
        return results
    
    def _compare_series(self, actual: pd.Series, expected: pd.Series, tolerance: float) -> bool:
        """Compare two pandas Series with tolerance"""
        try:
            # Handle NaN values
            actual_clean = actual.dropna()
            expected_clean = expected.dropna()
            
            if len(actual_clean) != len(expected_clean):
                return False
            
            # Compare values with tolerance
            diff = abs(actual_clean - expected_clean)
            return (diff <= tolerance).all()
            
        except Exception:
            return False
    
    def generate_test_report(self) -> str:
        """Generate a test report"""
        results = self.run_tests()
        
        report = "# Indicator Test Report\n\n"
        report += f"**Total Tests:** {len(results)}\n"
        report += f"**Passed:** {sum(results.values())}\n"
        report += f"**Failed:** {len(results) - sum(results.values())}\n\n"
        
        report += "## Test Results\n\n"
        
        for test_name, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            report += f"- {test_name}: {status}\n"
        
        return report