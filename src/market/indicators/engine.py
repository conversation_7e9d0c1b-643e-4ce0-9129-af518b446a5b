"""
Indicator Calculation Engine

This module provides a high-performance calculation engine for technical indicators
with vectorized processing, parameter validation, caching, and optimization features.
"""

import pandas as pd
import numpy as np
import hashlib
import pickle
import time
from typing import Dict, Any, Optional, Callable, Union, List, Tuple
from dataclasses import dataclass, field
from functools import wraps
import logging

from . import moving_averages, momentum, volatility, volume


@dataclass
class IndicatorConfig:
    """Configuration for an indicator"""
    name: str
    function: Callable
    default_params: Dict[str, Any] = field(default_factory=dict)
    required_columns: List[str] = field(default_factory=list)
    param_validators: Dict[str, Callable] = field(default_factory=dict)
    description: str = ""
    category: str = "general"


@dataclass
class CalculationResult:
    """Result of an indicator calculation"""
    indicator_name: str
    data: Union[pd.Series, pd.DataFrame, Dict[str, pd.Series]]
    params: Dict[str, Any]
    calculation_time: float
    cache_hit: bool = False
    data_hash: str = ""


class IndicatorCache:
    """Simple in-memory cache for indicator results"""
    
    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, CalculationResult] = {}
        self.access_times: Dict[str, float] = {}
        self.max_size = max_size
    
    def _generate_key(self, data_hash: str, indicator_name: str, params: Dict[str, Any]) -> str:
        """Generate cache key from data hash, indicator name, and parameters"""
        param_str = str(sorted(params.items()))
        key_str = f"{data_hash}_{indicator_name}_{param_str}"
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, data_hash: str, indicator_name: str, params: Dict[str, Any]) -> Optional[CalculationResult]:
        """Get cached result if available"""
        key = self._generate_key(data_hash, indicator_name, params)
        if key in self.cache:
            self.access_times[key] = time.time()
            result = self.cache[key]
            result.cache_hit = True
            return result
        return None
    
    def put(self, result: CalculationResult) -> None:
        """Store result in cache"""
        key = self._generate_key(result.data_hash, result.indicator_name, result.params)
        
        # Remove oldest entries if cache is full
        if len(self.cache) >= self.max_size:
            oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            del self.cache[oldest_key]
            del self.access_times[oldest_key]
        
        self.cache[key] = result
        self.access_times[key] = time.time()
    
    def clear(self) -> None:
        """Clear all cached results"""
        self.cache.clear()
        self.access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'hit_rate': sum(1 for r in self.cache.values() if r.cache_hit) / max(len(self.cache), 1)
        }


class IndicatorEngine:
    """High-performance indicator calculation engine"""
    
    def __init__(self, enable_cache: bool = True, cache_size: int = 1000):
        self.indicators: Dict[str, IndicatorConfig] = {}
        self.cache = IndicatorCache(cache_size) if enable_cache else None
        self.logger = logging.getLogger(__name__)
        
        # Register built-in indicators
        self._register_builtin_indicators()
    
    def _register_builtin_indicators(self) -> None:
        """Register all built-in indicators"""
        
        # Moving Averages
        self.register_indicator(IndicatorConfig(
            name="sma",
            function=moving_averages.sma,
            default_params={"period": 20},
            required_columns=["close"],
            param_validators={"period": lambda x: isinstance(x, int) and x > 0},
            description="Simple Moving Average",
            category="moving_averages"
        ))
        
        self.register_indicator(IndicatorConfig(
            name="ema",
            function=moving_averages.ema,
            default_params={"period": 20},
            required_columns=["close"],
            param_validators={"period": lambda x: isinstance(x, int) and x > 0},
            description="Exponential Moving Average",
            category="moving_averages"
        ))
        
        self.register_indicator(IndicatorConfig(
            name="wma",
            function=moving_averages.wma,
            default_params={"period": 20},
            required_columns=["close"],
            param_validators={"period": lambda x: isinstance(x, int) and x > 0},
            description="Weighted Moving Average",
            category="moving_averages"
        ))
        
        # Momentum Indicators
        self.register_indicator(IndicatorConfig(
            name="rsi",
            function=momentum.rsi,
            default_params={"period": 14},
            required_columns=["close"],
            param_validators={"period": lambda x: isinstance(x, int) and x > 0},
            description="Relative Strength Index",
            category="momentum"
        ))
        
        self.register_indicator(IndicatorConfig(
            name="macd",
            function=momentum.macd,
            default_params={"fast_period": 12, "slow_period": 26, "signal_period": 9},
            required_columns=["close"],
            param_validators={
                "fast_period": lambda x: isinstance(x, int) and x > 0,
                "slow_period": lambda x: isinstance(x, int) and x > 0,
                "signal_period": lambda x: isinstance(x, int) and x > 0
            },
            description="Moving Average Convergence Divergence",
            category="momentum"
        ))
        
        self.register_indicator(IndicatorConfig(
            name="kdj",
            function=momentum.kdj,
            default_params={"k_period": 9, "d_period": 3, "j_period": 3},
            required_columns=["high", "low", "close"],
            param_validators={
                "k_period": lambda x: isinstance(x, int) and x > 0,
                "d_period": lambda x: isinstance(x, int) and x > 0,
                "j_period": lambda x: isinstance(x, int) and x > 0
            },
            description="KDJ Stochastic Oscillator",
            category="momentum"
        ))
        
        # Volatility Indicators
        self.register_indicator(IndicatorConfig(
            name="bollinger_bands",
            function=volatility.bollinger_bands,
            default_params={"period": 20, "std_dev": 2.0},
            required_columns=["close"],
            param_validators={
                "period": lambda x: isinstance(x, int) and x > 1,
                "std_dev": lambda x: isinstance(x, (int, float)) and x > 0
            },
            description="Bollinger Bands",
            category="volatility"
        ))
        
        self.register_indicator(IndicatorConfig(
            name="atr",
            function=volatility.atr,
            default_params={"period": 14},
            required_columns=["high", "low", "close"],
            param_validators={"period": lambda x: isinstance(x, int) and x > 0},
            description="Average True Range",
            category="volatility"
        ))
        
        # Volume Indicators
        self.register_indicator(IndicatorConfig(
            name="obv",
            function=volume.obv,
            default_params={},
            required_columns=["close", "volume"],
            param_validators={},
            description="On-Balance Volume",
            category="volume"
        ))
        
        self.register_indicator(IndicatorConfig(
            name="vwap",
            function=volume.vwap,
            default_params={"period": None},
            required_columns=["high", "low", "close", "volume"],
            param_validators={"period": lambda x: x is None or (isinstance(x, int) and x > 0)},
            description="Volume Weighted Average Price",
            category="volume"
        ))
    
    def register_indicator(self, config: IndicatorConfig) -> None:
        """Register a new indicator"""
        self.indicators[config.name] = config
        self.logger.info(f"Registered indicator: {config.name}")
    
    def get_available_indicators(self) -> Dict[str, str]:
        """Get dictionary of available indicators with descriptions"""
        return {name: config.description or f"{name} indicator" 
                for name, config in self.indicators.items()}
    
    def get_indicator_names(self) -> List[str]:
        """Get list of available indicator names"""
        return list(self.indicators.keys())
    
    def get_indicator_info(self, name: str) -> Optional[IndicatorConfig]:
        """Get information about a specific indicator"""
        return self.indicators.get(name)
    
    def get_indicators_by_category(self, category: str) -> List[str]:
        """Get indicators by category"""
        return [name for name, config in self.indicators.items() if config.category == category]
    
    def _validate_data(self, data: Union[pd.Series, pd.DataFrame], required_columns: List[str]) -> None:
        """Validate input data"""
        if isinstance(data, pd.Series):
            if required_columns and required_columns != ["close"]:
                raise ValueError(f"提供了序列数据，但指标需要列: {required_columns}")
        elif isinstance(data, pd.DataFrame):
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                raise ValueError(f"缺少必需的列: {missing_columns}")
        else:
            raise ValueError("数据必须是pandas序列或数据框")
        
        if len(data) == 0:
            raise ValueError("数据不能为空")
    
    def _validate_params(self, params: Dict[str, Any], validators: Dict[str, Callable]) -> None:
        """Validate indicator parameters"""
        for param_name, validator in validators.items():
            if param_name in params:
                if not validator(params[param_name]):
                    raise ValueError(f"参数 '{param_name}' 的值无效: {params[param_name]}")
    
    def _generate_data_hash(self, data: Union[pd.Series, pd.DataFrame]) -> str:
        """Generate hash of data for caching"""
        if isinstance(data, pd.Series):
            data_bytes = data.values.tobytes() + data.index.values.tobytes()
        else:
            data_bytes = data.values.tobytes() + data.index.values.tobytes()
        return hashlib.md5(data_bytes).hexdigest()
    
    def calculate(self, indicator_name: str, data: Union[pd.Series, pd.DataFrame], 
                 **params) -> CalculationResult:
        """
        计算单个技术指标
        
        指标计算算法流程：
        这是指标引擎的核心计算方法，实现了高性能的指标计算流程：
        
        1. 指标验证阶段：
           - 检查指标是否已注册
           - 验证输入数据格式和必需列
           - 合并默认参数和用户参数
           - 验证参数有效性
        
        2. 缓存优化阶段：
           - 生成数据哈希值作为缓存键
           - 检查缓存中是否有相同计算结果
           - 缓存命中则直接返回，避免重复计算
        
        3. 指标计算阶段：
           - 调用指标的具体计算函数
           - 记录计算耗时用于性能分析
           - 处理计算过程中的异常
        
        4. 结果处理阶段：
           - 封装计算结果和元数据
           - 将结果存入缓存供后续使用
           - 返回完整的计算结果对象
        
        性能优化特性：
        - 智能缓存：避免重复计算相同数据和参数的指标
        - 参数验证：提前发现参数错误，避免无效计算
        - 异常处理：提供详细的错误信息便于调试
        - 性能监控：记录计算时间用于性能分析
        
        Args:
            indicator_name: 指标名称（如'sma', 'rsi', 'macd'等）
            data: 输入数据，可以是Series（单列）或DataFrame（多列）
            **params: 指标参数，会与默认参数合并
            
        Returns:
            CalculationResult: 包含计算结果、参数、耗时等信息的对象
            
        Raises:
            ValueError: 指标不存在或数据格式错误
            RuntimeError: 指标计算过程中出现错误
            
        示例:
            # 计算20日简单移动平均
            result = engine.calculate('sma', price_data, period=20)
            
            # 计算RSI指标
            result = engine.calculate('rsi', price_data, period=14)
            
            # 计算MACD指标
            result = engine.calculate('macd', price_data, 
                                    fast_period=12, slow_period=26)
        """
        # 第一步：验证指标是否存在
        if indicator_name not in self.indicators:
            raise ValueError(f"未知指标: {indicator_name}")
        
        # 获取指标配置信息
        config = self.indicators[indicator_name]
        
        # 第二步：验证输入数据
        self._validate_data(data, config.required_columns)
        
        # 第三步：合并参数（用户参数覆盖默认参数）
        final_params = {**config.default_params, **params}
        
        # 第四步：验证参数有效性
        self._validate_params(final_params, config.param_validators)
        
        # 第五步：生成数据哈希值用于缓存
        data_hash = self._generate_data_hash(data)
        
        # 第六步：检查缓存（性能优化）
        if self.cache:
            cached_result = self.cache.get(data_hash, indicator_name, final_params)
            if cached_result:
                return cached_result
        
        # 第七步：执行指标计算
        start_time = time.time()
        
        try:
            # 根据数据类型调用相应的计算函数
            if isinstance(data, pd.Series):
                result_data = config.function(data, **final_params)
            else:
                result_data = config.function(data, **final_params)
        except Exception as e:
            raise RuntimeError(f"计算 {indicator_name} 时出错: {str(e)}")
        
        # 记录计算耗时
        calculation_time = time.time() - start_time
        
        # 第八步：创建结果对象
        result = CalculationResult(
            indicator_name=indicator_name,
            data=result_data,
            params=final_params,
            calculation_time=calculation_time,
            data_hash=data_hash
        )
        
        # 第九步：缓存结果（为后续相同计算提速）
        if self.cache:
            self.cache.put(result)
        
        return result
    
    def calculate_multiple(self, indicators: List[Tuple[str, Dict[str, Any]]], 
                          data: Union[pd.Series, pd.DataFrame]) -> Dict[str, CalculationResult]:
        """Calculate multiple indicators on the same data"""
        results = {}
        
        for indicator_name, params in indicators:
            try:
                result = self.calculate(indicator_name, data, **params)
                results[indicator_name] = result
            except Exception as e:
                self.logger.error(f"Failed to calculate {indicator_name}: {str(e)}")
                continue
        
        return results
    
    def batch_calculate(self, indicator_name: str, datasets: List[Union[pd.Series, pd.DataFrame]], 
                       **params) -> List[CalculationResult]:
        """Calculate the same indicator on multiple datasets"""
        results = []
        
        for i, data in enumerate(datasets):
            try:
                result = self.calculate(indicator_name, data, **params)
                results.append(result)
            except Exception as e:
                self.logger.error(f"Failed to calculate {indicator_name} for dataset {i}: {str(e)}")
                continue
        
        return results
    
    def optimize_parameters(self, indicator_name: str, data: Union[pd.Series, pd.DataFrame],
                           param_ranges: Dict[str, List], optimization_metric: Callable) -> Dict[str, Any]:
        """
        使用网格搜索优化指标参数
        
        参数优化算法：
        参数优化是量化分析中的重要环节，通过系统性地测试不同参数组合，
        找到在特定数据集上表现最佳的参数设置。
        
        算法流程：
        1. 参数空间构建：
           - 根据用户提供的参数范围构建搜索空间
           - 使用笛卡尔积生成所有可能的参数组合
           - 确保参数组合的完整性和有效性
        
        2. 网格搜索执行：
           - 遍历所有参数组合
           - 对每个组合计算指标值
           - 使用优化指标函数评估结果质量
           - 记录最佳参数和对应得分
        
        3. 异常处理：
           - 跳过导致计算错误的参数组合
           - 记录警告信息便于调试
           - 确保优化过程的鲁棒性
        
        4. 结果返回：
           - 返回最佳参数组合
           - 返回对应的优化得分
           - 提供完整的优化结果信息
        
        优化策略：
        - 穷举搜索：保证找到全局最优解
        - 并行计算：可扩展支持多进程加速
        - 缓存利用：重复计算会自动使用缓存
        - 异常容错：单个参数组合失败不影响整体优化
        
        Args:
            indicator_name: 要优化的指标名称
            data: 用于优化的历史数据
            param_ranges: 参数搜索范围，格式为 {'param_name': [value1, value2, ...]}
            optimization_metric: 优化目标函数，接收指标结果返回得分
            
        Returns:
            Dict[str, Any]: 包含最佳参数和得分的字典
            
        示例:
            # 优化RSI指标的周期参数
            param_ranges = {'period': [10, 12, 14, 16, 18, 20]}
            
            def sharpe_ratio_metric(indicator_data):
                # 计算基于指标的策略夏普比率
                signals = generate_signals(indicator_data)
                returns = calculate_returns(signals)
                return returns.mean() / returns.std()
            
            result = engine.optimize_parameters(
                'rsi', price_data, param_ranges, sharpe_ratio_metric
            )
            
            logger.info(f"最佳参数: {result['best_params']}")
            logger.info(f"最佳得分: {result['best_score']}")
        """
        # 验证指标是否存在
        if indicator_name not in self.indicators:
            raise ValueError(f"未知指标: {indicator_name}")
        
        # 初始化最优结果
        best_score = float('-inf')  # 最佳得分（初始为负无穷）
        best_params = {}            # 最佳参数组合
        
        # 生成所有参数组合（笛卡尔积）
        import itertools
        param_names = list(param_ranges.keys())    # 参数名称列表
        param_values = list(param_ranges.values()) # 参数值列表
        
        # 遍历所有可能的参数组合
        for param_combination in itertools.product(*param_values):
            # 构建当前参数字典
            params = dict(zip(param_names, param_combination))
            
            try:
                # 使用当前参数计算指标
                result = self.calculate(indicator_name, data, **params)
                
                # 使用优化指标函数评估结果
                score = optimization_metric(result.data)
                
                # 更新最佳结果（如果当前得分更高）
                if score > best_score:
                    best_score = score
                    best_params = params
                    
            except Exception as e:
                # 记录失败的参数组合，但不中断优化过程
                self.logger.warning(f"参数组合 {params} 计算失败: {str(e)}")
                continue
        
        # 返回优化结果
        return {
            'best_params': best_params,  # 最佳参数组合
            'best_score': best_score     # 对应的最佳得分
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        stats = {
            'registered_indicators': len(self.indicators),
            'cache_enabled': self.cache is not None
        }
        
        if self.cache:
            stats.update(self.cache.get_stats())
        
        return stats
    
    def clear_cache(self) -> None:
        """Clear the indicator cache"""
        if self.cache:
            self.cache.clear()


# Global engine instance
_engine = None


def get_engine() -> IndicatorEngine:
    """Get the global indicator engine instance"""
    global _engine
    if _engine is None:
        _engine = IndicatorEngine()
    return _engine


def calculate_indicator(indicator_name: str, data: Union[pd.Series, pd.DataFrame], 
                       **params) -> CalculationResult:
    """Convenience function to calculate an indicator using the global engine"""
    return get_engine().calculate(indicator_name, data, **params)


def performance_timer(func):
    """Decorator to time function execution"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        if hasattr(result, 'calculation_time'):
            result.calculation_time = end_time - start_time
        
        return result
    return wrapper


class VectorizedCalculator:
    """Utility class for vectorized calculations"""
    
    @staticmethod
    def rolling_apply_parallel(data: pd.Series, window: int, func: Callable, 
                              n_jobs: int = -1) -> pd.Series:
        """Apply function to rolling windows in parallel"""
        try:
            from joblib import Parallel, delayed
            
            def apply_window(i):
                if i < window - 1:
                    return np.nan
                window_data = data.iloc[i-window+1:i+1]
                return func(window_data)
            
            results = Parallel(n_jobs=n_jobs)(
                delayed(apply_window)(i) for i in range(len(data))
            )
            
            return pd.Series(results, index=data.index)
            
        except ImportError:
            # Fallback to regular rolling apply if joblib not available
            return data.rolling(window=window).apply(func, raw=False)
    
    @staticmethod
    def vectorized_conditions(data: pd.DataFrame, conditions: List[Tuple[str, str, float]]) -> pd.Series:
        """Apply multiple conditions vectorized"""
        result = pd.Series(True, index=data.index)
        
        for column, operator, value in conditions:
            if column not in data.columns:
                continue
                
            if operator == '>':
                result &= data[column] > value
            elif operator == '<':
                result &= data[column] < value
            elif operator == '>=':
                result &= data[column] >= value
            elif operator == '<=':
                result &= data[column] <= value
            elif operator == '==':
                result &= data[column] == value
            elif operator == '!=':
                result &= data[column] != value
        
        return result