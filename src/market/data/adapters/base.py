"""
数据源适配器基础类

本模块提供数据源适配器的抽象基类和通用功能，
包括速率限制、健康检查、数据标准化等功能。

主要类：
- DataSourceAdapter: 数据源适配器抽象基类
- RateLimiter: API调用速率限制器
- MockDataSourceAdapter: 用于测试的模拟适配器

作者: 量化交易系统开发团队
版本: 1.0.0
创建日期: 2025-07-30
"""

import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import pandas as pd
import time
from threading import Lock

from ..models.market_data import UnifiedMarketData, MarketInfo
from ..exceptions import DataException
from ..utils.messages import get_message


logger = logging.getLogger(__name__)


@dataclass
class DataSourceConfig:
    """数据源适配器配置类"""
    
    name: str
    adapter_class: str
    enabled: bool
    priority: int  # 数字越小优先级越高
    rate_limit: Dict[str, int]  # API速率限制
    credentials: Dict[str, str]  # API凭证
    default_params: Dict[str, Any]  # 默认参数
    
    def __post_init__(self):
        """初始化后验证配置"""
        if self.priority < 0:
            raise ValueError("优先级必须为非负数")
        
        if not self.rate_limit:
            self.rate_limit = {}
        
        if not self.credentials:
            self.credentials = {}
        
        if not self.default_params:
            self.default_params = {}


class RateLimiter:
    """API调用速率限制器"""
    
    def __init__(self, rate_limit_config: Dict[str, int]):
        self.config = rate_limit_config
        self.call_history = []
        self.lock = Lock()
    
    def can_make_request(self) -> bool:
        """检查是否可以发起请求而不超过速率限制"""
        with self.lock:
            now = time.time()
            
            # Clean old entries
            self._clean_history(now)
            
            # Check each rate limit
            for period, limit in self.config.items():
                if period == 'requests_per_second':
                    window = 1
                elif period == 'requests_per_minute':
                    window = 60
                elif period == 'requests_per_hour':
                    window = 3600
                elif period == 'requests_per_day':
                    window = 86400
                else:
                    continue
                
                # Count requests in the window
                count = sum(1 for timestamp in self.call_history 
                           if now - timestamp <= window)
                
                if count >= limit:
                    return False
            
            return True
    
    def record_request(self) -> None:
        """记录请求时间戳"""
        with self.lock:
            self.call_history.append(time.time())
    
    def _clean_history(self, now: float) -> None:
        """清理历史调用记录中的过期条目"""
        # 保留最近一天的记录（最长可能的时间窗口）
        cutoff = now - 86400
        self.call_history = [t for t in self.call_history if t > cutoff]
    
    def get_wait_time(self) -> float:
        """获取下次请求前需要等待的时间"""
        with self.lock:
            now = time.time()
            self._clean_history(now)
            
            max_wait = 0
            
            for period, limit in self.config.items():
                if period == 'requests_per_second':
                    window = 1
                elif period == 'requests_per_minute':
                    window = 60
                elif period == 'requests_per_hour':
                    window = 3600
                elif period == 'requests_per_day':
                    window = 86400
                else:
                    continue
                
                # Count requests in the window
                window_requests = [t for t in self.call_history 
                                 if now - t <= window]
                
                if len(window_requests) >= limit and window_requests:
                    # 计算窗口中最早请求何时过期
                    oldest_request = min(window_requests)
                    wait_time = window - (now - oldest_request) + 0.1  # 添加小缓冲时间
                    max_wait = max(max_wait, wait_time)
            
            return max(0, max_wait)


class DataSourceAdapter(ABC):
    """数据源适配器抽象基类"""
    
    def __init__(self, config: DataSourceConfig):
        self.config = config
        self.rate_limiter = RateLimiter(config.rate_limit)
        self.is_healthy = True
        self.last_error = None
        self.last_health_check = None
    
    @abstractmethod
    def fetch_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime, 
        interval: str = '1d'
    ) -> List[UnifiedMarketData]:
        """获取历史市场数据"""
        
        return []  # 返回空数据，避免系统错误
    @abstractmethod
    def get_available_symbols(self) -> List[str]:
        """获取可用交易标的列表"""
        
        return []  # 返回空数据，避免系统错误
    @abstractmethod
    def validate_symbol(self, symbol: str) -> bool:
        """验证交易标的是否受支持"""
        pass
    
    @abstractmethod
    def get_market_info(self, symbol: str) -> Optional[MarketInfo]:
        """获取交易标的的市场信息"""
        
        return []  # 返回空数据，避免系统错误
    def normalize_data(self, raw_data: Any) -> List[UnifiedMarketData]:
        """将原始数据标准化为UnifiedMarketData格式"""
        # 默认实现 - 应由具体适配器重写
        if isinstance(raw_data, pd.DataFrame):
            return self._dataframe_to_unified_data(raw_data)
        else:
            raise NotImplementedError("子类必须实现normalize_data方法")
    
    def _dataframe_to_unified_data(self, df: pd.DataFrame) -> List[UnifiedMarketData]:
        """将DataFrame转换为UnifiedMarketData列表"""
        market_data_list = []
        
        for _, row in df.iterrows():
            try:
                market_data = UnifiedMarketData(
                    symbol=str(row.get('symbol', '')),
                    timestamp=pd.to_datetime(row['timestamp']).to_pydatetime(),
                    open=float(row['open']),
                    high=float(row['high']),
                    low=float(row['low']),
                    close=float(row['close']),
                    volume=float(row['volume']),
                    market=self.get_market_type(),
                    exchange=self.get_exchange_name(),
                    currency=self.get_default_currency(),
                    adj_close=float(row['adj_close']) if 'adj_close' in row and pd.notna(row['adj_close']) else None,
                    turnover=float(row['turnover']) if 'turnover' in row and pd.notna(row['turnover']) else None
                )
                
                if market_data.validate():
                    market_data_list.append(market_data)
                
            except Exception as e:
                logger.warning(f"转换数据行为UnifiedMarketData失败: {e}")
                continue
        
        return market_data_list
    
    @abstractmethod
    def get_market_type(self) -> str:
        """获取市场类型 (US, CN, CRYPTO等)"""
        
        return []  # 返回空数据，避免系统错误
    @abstractmethod
    def get_exchange_name(self) -> str:
        """获取交易所名称"""
        
        return []  # 返回空数据，避免系统错误
    @abstractmethod
    def get_default_currency(self) -> str:
        """获取该市场的默认货币"""
        
        return []  # 返回空数据，避免系统错误
    def wait_for_rate_limit(self) -> None:
        """如果会超过速率限制则等待"""
        if not self.rate_limiter.can_make_request():
            wait_time = self.rate_limiter.get_wait_time()
            if wait_time > 0:
                logger.info(f"达到速率限制，等待 {wait_time:.2f} 秒")
                time.sleep(wait_time)
    
    def make_request(self, request_func, *args, **kwargs):
        """发起受速率限制的请求"""
        self.wait_for_rate_limit()
        
        try:
            result = request_func(*args, **kwargs)
            self.rate_limiter.record_request()
            self.is_healthy = True
            self.last_error = None
            return result
            
        except Exception as e:
            self.is_healthy = False
            self.last_error = str(e)
            logger.error(get_message("network.connection_failed", service=self.config.name, error=str(e)))
            raise
    
    def health_check(self) -> Tuple[bool, Optional[str]]:
        """对数据源执行健康检查"""
        try:
            self.last_health_check = datetime.now()
            
            # 尝试获取少量数据以测试连接性
            symbols = self.get_available_symbols()
            if not symbols:
                self.is_healthy = False
                self.last_error = "无可用交易标的"
                return False, "无可用交易标的"
            
            # 尝试获取第一个标的的最新数据
            test_symbol = symbols[0]
            end_date = datetime.now()
            start_date = datetime(end_date.year, end_date.month, end_date.day)
            
            try:
                data = self.fetch_historical_data(test_symbol, start_date, end_date)
                self.is_healthy = True
                self.last_error = None
                return True, f"健康检查通过，获取到 {len(data)} 条记录"
                
            except Exception as e:
                self.is_healthy = False
                self.last_error = f"数据获取失败: {str(e)}"
                return False, self.last_error
            
        except Exception as e:
            self.is_healthy = False
            self.last_error = f"健康检查失败: {str(e)}"
            logger.error(get_message("network.connection_failed", service=self.config.name, error=str(e)))
            return False, self.last_error
    
    def get_status(self) -> Dict[str, Any]:
        """获取适配器状态信息"""
        return {
            'name': self.config.name,
            'enabled': self.config.enabled,
            'healthy': self.is_healthy,
            'last_error': self.last_error,
            'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None,
            'priority': self.config.priority,
            'rate_limits': self.config.rate_limit
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            'total_requests': len(self.rate_limiter.call_history),
            'requests_last_hour': sum(1 for t in self.rate_limiter.call_history 
                                    if time.time() - t <= 3600),
            'requests_last_day': sum(1 for t in self.rate_limiter.call_history 
                                   if time.time() - t <= 86400),
            'can_make_request': self.rate_limiter.can_make_request(),
            'wait_time': self.rate_limiter.get_wait_time()
        }


class MockDataSourceAdapter(DataSourceAdapter):
    """用于测试的模拟数据源适配器"""
    
    def __init__(self, config: DataSourceConfig):
        super().__init__(config)
        self.mock_symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA']
    
    def fetch_historical_data(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime, 
        interval: str = '1d'
    ) -> List[UnifiedMarketData]:
        """获取模拟历史数据"""
        if not self.validate_symbol(symbol):
            raise DataException(get_message("data.invalid", details=f"无效的交易标的: {symbol}"))
        
        # 生成模拟数据
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        mock_data = []
        
        base_price = 100.0
        for i, date in enumerate(dates):
            price = base_price + (i * 0.5)  # 简单的上升趋势
            
            market_data = UnifiedMarketData(
                symbol=symbol,
                timestamp=date.to_pydatetime(),
                open=price,
                high=price * 1.02,
                low=price * 0.98,
                close=price * 1.01,
                volume=1000000,
                market=self.get_market_type(),
                exchange=self.get_exchange_name(),
                currency=self.get_default_currency()
            )
            mock_data.append(market_data)
        
        return mock_data
    
    def get_available_symbols(self) -> List[str]:
        """获取模拟可用交易标的"""
        return self.mock_symbols.copy()
    
    def validate_symbol(self, symbol: str) -> bool:
        """验证模拟交易标的"""
        return symbol in self.mock_symbols
    
    def get_market_info(self, symbol: str) -> Optional[MarketInfo]:
        """获取模拟市场信息"""
        if not self.validate_symbol(symbol):
            return None
        
        return MarketInfo(
            symbol=symbol,
            name=f"模拟公司 {symbol}",
            market=self.get_market_type(),
            exchange=self.get_exchange_name(),
            currency=self.get_default_currency(),
            lot_size=1.0,
            tick_size=0.01,
            trading_hours={'open': '09:30', 'close': '16:00'},
            timezone='US/Eastern'
        )
    
    def get_market_type(self) -> str:
        return 'US'
    
    def get_exchange_name(self) -> str:
        return 'MOCK'
    
    def get_default_currency(self) -> str:
        return 'USD'