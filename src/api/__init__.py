# Shim package to expose API under src.api

# Provide compatibility re-exports expected by tests
try:
    # app instance for tests expecting src.api.main:app
    from web_ui.backend.app.api.main import app as app  # type: ignore
except Exception:  # pragma: no cover
    app = None  # will be created in main module

try:
    from web_ui.backend.app.api import models as models  # type: ignore
except Exception:  # pragma: no cover
    models = None
