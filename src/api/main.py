"""
Compatibility main module to expose FastAPI `app` at `src.api.main` for tests.
"""

try:  # pragma: no cover
    from web_ui.backend.app.api.main import app  # type: ignore
except Exception:  # pragma: no cover
    # Fallback: try to create via create_app
    try:
        from web_ui.backend.app.api.app import create_app  # type: ignore
        app = create_app()
    except Exception:
        app = None  # type: ignore


