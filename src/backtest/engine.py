"""
事件驱动的回测引擎模块。

本模块提供了核心的回测引擎，按时间顺序处理历史数据并模拟交易策略的执行。
该引擎采用事件驱动架构，能够真实模拟交易环境中的订单处理和策略执行过程。

主要功能：
- 按时间顺序处理历史市场数据
- 模拟策略的信号生成和订单执行
- 实时跟踪投资组合状态
- 提供详细的回测结果分析
- 支持多种订单类型和执行策略

使用示例：
    engine = BacktestEngine(initial_capital=100000)
    engine.set_data_feed(market_data)
    engine.add_strategy(my_strategy)
    result = engine.run_backtest()
"""

import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Callable
from queue import Queue
import logging
import pandas as pd
from copy import deepcopy

from .events import Event, EventType, MarketEvent, SignalEvent, OrderEvent, FillEvent
from .executor import OrderExecutor, ExecutionConfig
from .result import BacktestResult

from src.domain.models.market_data import MarketData
from src.domain.models.trading import Order, OrderFill, Trade, OrderSide, OrderType, OrderStatus, Signal, SignalAction
from src.domain.models.portfolio import Portfolio, Position
from src.market.strategies.base import BaseStrategy, StrategyContext


class BacktestProgress:
    """回测进度跟踪器。
    
    用于实时监控回测执行进度，提供完成百分比、已用时间和预计剩余时间等信息。
    
    属性：
        total_steps: 回测总步数（数据点数量）
        current_step: 当前处理到的步数
        start_time: 回测开始时间
        last_update: 最后更新时间
    
    方法：
        update: 更新当前进度
        get_progress_pct: 获取完成百分比
        get_elapsed_time: 获取已用时间
        get_eta: 获取预计剩余时间
    """
    
    def __init__(self, total_steps: int):
        """
        初始化回测进度跟踪器。
        
        参数：
            total_steps: 回测总步数（数据点数量）
        """
        self.total_steps = total_steps
        self.current_step = 0
        self.start_time = datetime.now()
        self.last_update = self.start_time
        
    def update(self, step: int) -> None:
        """更新当前进度到指定步数。"""
        self.current_step = step
        self.last_update = datetime.now()
    
    def get_progress_pct(self) -> float:
        """获取当前完成百分比（0-100）。"""
        if self.total_steps == 0:
            return 100.0
        return (self.current_step / self.total_steps) * 100.0
    
    def get_elapsed_time(self) -> timedelta:
        """获取从开始到现在的已用时间。"""
        return self.last_update - self.start_time
    
    def get_eta(self) -> Optional[timedelta]:
        """获取预计剩余完成时间。"""
        if self.current_step == 0:
            return None
        
        elapsed = self.get_elapsed_time()
        rate = self.current_step / elapsed.total_seconds()
        remaining_steps = self.total_steps - self.current_step
        
        if rate > 0:
            eta_seconds = remaining_steps / rate
            return timedelta(seconds=eta_seconds)
        
        return None


class BacktestEngine:
    """
    事件驱动回测引擎。
    
    按时间顺序处理历史市场数据，模拟策略执行过程，包括真实的订单处理和成交逻辑。
    该引擎支持多策略并行运行，提供完整的投资组合跟踪和详细的回测结果分析。
    
    主要特性：
    - 事件驱动的架构设计，模拟真实交易环境
    - 支持多种订单类型（市价单、限价单等）
    - 实时投资组合状态跟踪
    - 详细的交易记录和性能分析
    - 支持策略级别的风险控制和资金管理
    - 可配置的交易成本和滑点模型
    
    使用示例：
        # 创建回测引擎
        engine = BacktestEngine(initial_capital=100000)
        
        # 设置市场数据
        engine.set_data_feed(market_data_df)
        
        # 添加交易策略
        strategy_id = engine.add_strategy(my_strategy)
        
        # 运行回测
        result = engine.run_backtest(start_date, end_date)
        
        # 查看结果
        logger.info(f"总收益率: {result.total_return:.2%}")
        logger.info(f"交易次数: {result.total_trades}")
    """
    
    def __init__(self, initial_capital: float = 100000.0,
                 execution_config: Optional[ExecutionConfig] = None):
        """
        初始化回测引擎。
        
        参数：
            initial_capital: 初始资金，默认为100,000
            execution_config: 订单执行配置，使用默认配置如果未提供
            
        初始化内容包括：
        - 设置初始资金和现金余额
        - 创建事件队列和订单执行器
        - 初始化投资组合管理器
        - 设置日志记录器
        - 初始化策略和交易记录存储
        """
        self.initial_capital = initial_capital
        self.execution_config = execution_config or ExecutionConfig()
        
        # Core components
        self.event_queue = Queue()
        self.executor = OrderExecutor(self.execution_config)
        self.portfolio = Portfolio(initial_capital=initial_capital, cash=initial_capital)
        
        # Import here to avoid circular imports
        from src.domain.models.portfolio_manager import PortfolioManager
        self.portfolio_manager = PortfolioManager(self.portfolio)
        
        # Strategy management
        self.strategies: Dict[str, BaseStrategy] = {}
        self.strategy_contexts: Dict[str, StrategyContext] = {}
        
        # Data and state
        self.market_data_feed: Optional[pd.DataFrame] = None
        self.current_data: Dict[str, MarketData] = {}
        self.current_timestamp: Optional[datetime] = None
        
        # Results tracking
        self.trades: List[Trade] = []
        self.fills: List[OrderFill] = []
        self.portfolio_history: List[Portfolio] = []
        self.pending_orders: Dict[str, Order] = {}
        
        # Progress tracking
        self.progress: Optional[BacktestProgress] = None
        self.progress_callback: Optional[Callable[[BacktestProgress], None]] = None
        
        # Logging
        self.logger = logging.getLogger(f"{__name__}.BacktestEngine")
        
        # State management
        self.is_running = False
        self.is_paused = False
        self.should_stop = False
    
    def add_strategy(self, strategy: BaseStrategy, strategy_id: Optional[str] = None) -> str:
        """
        向回测引擎添加交易策略。
        
        该方法将策略实例添加到回测引擎中，并为其创建独立的策略上下文。
        策略会在添加时立即初始化，并准备好接收市场数据。
        
        参数：
            strategy: 策略实例，必须是BaseStrategy的子类
            strategy_id: 可选的策略ID，如果未提供则自动生成
            
        返回：
            str: 策略的唯一标识符
            
        示例：
            strategy = MyTradingStrategy()
            strategy_id = engine.add_strategy(strategy, "my_strategy_001")
            
        注意：
            - 每个策略必须有唯一的ID
            - 策略会在添加时立即初始化
            - 策略上下文会自动创建并关联
        """
        if strategy_id is None:
            strategy_id = f"{strategy.__class__.__name__}_{uuid.uuid4().hex[:8]}"
        
        # Create strategy context
        context = StrategyContext(
            data_manager=self,  # Engine acts as data manager for backtesting
            portfolio=self.portfolio
        )
        
        # Initialize strategy
        strategy.set_context(context)
        strategy.initialize(context)
        strategy.is_initialized = True
        strategy.is_active = True
        
        # Store strategy and context
        self.strategies[strategy_id] = strategy
        self.strategy_contexts[strategy_id] = context
        
        self.logger.info(f"✅ 策略已添加: {strategy_id}")
        return strategy_id
    
    def set_data_feed(self, data: pd.DataFrame) -> None:
        """
        设置回测用的市场数据。
        
        设置历史市场数据作为回测的数据源。数据必须包含完整的OHLCV信息，
        并且按时间戳索引排序。
        
        参数：
            data: 包含OHLCV数据的DataFrame，必须按时间戳索引
            
        必需列：
            - symbol: 交易标的代码
            - open: 开盘价
            - high: 最高价
            - low: 最低价
            - close: 收盘价
            - volume: 成交量
            - adj_close: 复权收盘价（可选）
            
        异常：
            ValueError: 如果数据缺少必需的列
            
        示例：
            data = pd.DataFrame({
                'symbol': ['AAPL'] * len(dates),
                'open': opens,
                'high': highs,
                'low': lows,
                'close': closes,
                'volume': volumes
            }, index=dates)
            engine.set_data_feed(data)
        """
        # Validate data format
        required_columns = ['symbol', 'open', 'high', 'low', 'close', 'volume']
        if not all(col in data.columns for col in required_columns):
            raise ValueError(f"Data must contain columns: {required_columns}")
        
        # Sort by timestamp
        self.market_data_feed = data.sort_index()
        self.logger.info(f"📊 数据源已设置，共 {len(data)} 条记录")
    
    def set_progress_callback(self, callback: Callable[[BacktestProgress], None]) -> None:
        """Set progress callback function."""
        self.progress_callback = callback
    
    def run_backtest(self, start_date: Optional[datetime] = None,
                    end_date: Optional[datetime] = None) -> BacktestResult:
        """
        运行回测模拟。
        
        执行完整的回测流程，包括数据加载、策略执行、订单处理、成交记录和
        结果分析。支持指定回测的时间范围。
        
        参数：
            start_date: 回测开始日期，如果为None则使用数据最早日期
            end_date: 回测结束日期，如果为None则使用数据最晚日期
            
        返回：
            BacktestResult: 包含完整回测结果的对象，包括：
                - 总收益率和绝对收益
                - 交易次数和胜率
                - 投资组合历史状态
                - 详细的交易记录
                - 风险指标和性能指标
                
        异常：
            ValueError: 如果未设置数据源或未添加策略
            ValueError: 如果指定的时间范围内没有数据
            
        示例：
            # 运行完整数据范围的回测
            result = engine.run_backtest()
            
            # 运行指定时间范围的回测
            result = engine.run_backtest(
                start_date=datetime(2023, 1, 1),
                end_date=datetime(2023, 12, 31)
            )
            
            # 查看回测结果
            logger.info(f"总收益率: {result.total_return:.2%}")
            logger.info(f"夏普比率: {result.sharpe_ratio}")
            logger.info(f"最大回撤: {result.max_drawdown:.2%}")
        """
        if self.market_data_feed is None:
            raise ValueError("Market data feed not set")
        
        if not self.strategies:
            raise ValueError("No strategies added")
        
        # Filter data by date range
        data = self.market_data_feed
        if start_date:
            data = data[data.index >= start_date]
        if end_date:
            data = data[data.index <= end_date]
        
        if data.empty:
            raise ValueError("No data available for specified date range")
        
        # Initialize progress tracking
        self.progress = BacktestProgress(len(data))
        
        # Reset state
        self._reset_state()
        
        # Set running state
        self.is_running = True
        self.should_stop = False
        
        actual_start_date = data.index[0]
        actual_end_date = data.index[-1]
        
        self.logger.info(f"🚀 开始回测: {actual_start_date} 至 {actual_end_date}")
        
        try:
            # Process each timestamp
            for i, (timestamp, row) in enumerate(data.iterrows()):
                if self.should_stop:
                    break
                
                # Update progress
                self.progress.update(i + 1)
                if self.progress_callback:
                    self.progress_callback(self.progress)
                
                # Create market data
                market_data = MarketData(
                    symbol=row['symbol'],
                    timestamp=timestamp,
                    open=row['open'],
                    high=row['high'],
                    low=row['low'],
                    close=row['close'],
                    volume=row['volume'],
                    adj_close=row.get('adj_close')
                )
                
                # Process this timestamp
                self._process_timestamp(market_data)
            
            # Finalize backtest
            self._finalize_backtest()
            
        except Exception as e:
            self.logger.error(f"回测执行失败: {e}")
            raise
        finally:
            self.is_running = False
        
        # Create and return results
        result = BacktestResult(
            id=str(uuid.uuid4()),
            strategy_id=list(self.strategies.keys())[0] if self.strategies else 'unknown',
            start_date=actual_start_date,
            end_date=actual_end_date,
            initial_capital=self.initial_capital,
            final_capital=self.portfolio.total_value,
            total_return=(self.portfolio.total_value - self.initial_capital) / self.initial_capital,
            total_trades=len(self.trades),
            status='completed',
            created_at=datetime.now()
        )
        
        # Store additional data as attributes
        result.trades = self.trades.copy()
        result.fills = self.fills.copy()
        result.portfolio_history = self.portfolio_history.copy()
        
        # Calculate basic performance metrics
        if len(self.trades) > 0:
            winning_trades = [t for t in self.trades if hasattr(t, 'pnl') and t.pnl is not None and t.pnl > 0]
            result.winning_trades = len(winning_trades)
            result.losing_trades = len([t for t in self.trades if hasattr(t, 'pnl') and t.pnl is not None and t.pnl <= 0])
            result.win_rate = len(winning_trades) / len(self.trades) if len(self.trades) > 0 else 0
        
        self.logger.info(f"🎉 回测完成，总收益率: {result.total_return:.2%}")
        return result
    
    def stop_backtest(self) -> None:
        """Stop the running backtest."""
        self.should_stop = True
        self.logger.info("⏹️ 收到回测停止请求")
    
    def _reset_state(self) -> None:
        """Reset engine state for new backtest."""
        self.portfolio = Portfolio(initial_capital=self.initial_capital, cash=self.initial_capital)
        self.current_data.clear()
        self.current_timestamp = None
        self.trades.clear()
        self.fills.clear()
        self.portfolio_history.clear()
        self.pending_orders.clear()
        
        # Clear event queue
        while not self.event_queue.empty():
            self.event_queue.get()
    
    def _process_timestamp(self, market_data: MarketData) -> None:
        """Process a single timestamp in the backtest."""
        self.current_timestamp = market_data.timestamp
        self.current_data[market_data.symbol] = market_data
        
        # Add market event to queue
        market_event = MarketEvent(market_data)
        self.event_queue.put(market_event)
        
        # Process all events in queue
        while not self.event_queue.empty():
            event = self.event_queue.get()
            self._handle_event(event)
        
        # Update portfolio with current market prices
        market_prices = {symbol: data.close for symbol, data in self.current_data.items()}
        self.portfolio.update_positions_market_value(market_prices)
        
        # Save portfolio snapshot
        self.portfolio_history.append(deepcopy(self.portfolio))
    
    def _handle_event(self, event: Event) -> None:
        """Handle different types of events."""
        if event.event_type == EventType.MARKET:
            self._handle_market_event(event)
        elif event.event_type == EventType.SIGNAL:
            self._handle_signal_event(event)
        elif event.event_type == EventType.ORDER:
            self._handle_order_event(event)
        elif event.event_type == EventType.FILL:
            self._handle_fill_event(event)
    
    def _handle_market_event(self, event: MarketEvent) -> None:
        """Handle market data events."""
        # Send market data to all active strategies
        for strategy_id, strategy in self.strategies.items():
            if not strategy.is_active:
                continue
            
            try:
                # Generate signals from strategy
                signals = strategy.on_data(event.market_data)
                
                # Create signal events
                for signal in signals:
                    if signal.validate():
                        signal_event = SignalEvent(signal, strategy_id)
                        self.event_queue.put(signal_event)
                    else:
                        self.logger.warning(f"⚠️ 策略 {strategy_id} 生成无效信号: {signal}")
                        
            except Exception as e:
                self.logger.error(f"策略 {strategy_id} 执行错误: {e}")
    
    def _handle_signal_event(self, event: SignalEvent) -> None:
        """Handle trading signal events."""
        signal = event.signal
        
        # Skip HOLD signals
        if signal.action == SignalAction.HOLD:
            return
        
        # Convert signal to order
        order = self._signal_to_order(signal, event.strategy_id)
        if order:
            order_event = OrderEvent(order)
            self.event_queue.put(order_event)
    
    def _handle_order_event(self, event: OrderEvent) -> None:
        """Handle order placement events."""
        order = event.order
        
        # Get current market data for the symbol
        market_data = self.current_data.get(order.symbol)
        if not market_data:
            self.logger.warning(f"⚠️ 缺少标的 {order.symbol} 的市场数据")
            return
        
        # Execute order
        fill = self.executor.execute_order(order, market_data, self.portfolio)
        
        if fill:
            # Store pending order
            self.pending_orders[order.id] = order
            
            # Create fill event
            fill_event = FillEvent(fill)
            self.event_queue.put(fill_event)
        else:
            self.logger.info(f"❌ 订单被拒绝: {order}")
    
    def _handle_fill_event(self, event: FillEvent) -> None:
        """Handle order fill events."""
        fill = event.fill
        
        # Create trade record
        trade = Trade(
            id=str(uuid.uuid4()),
            symbol=fill.symbol,
            side=fill.side,
            quantity=fill.quantity,
            price=fill.price,
            timestamp=fill.timestamp,
            commission=fill.commission,
            strategy_id=fill.strategy_id,
            order_id=fill.order_id
        )
        
        # Add trade to portfolio
        self.portfolio.add_trade(trade)
        
        # Store records
        self.trades.append(trade)
        self.fills.append(fill)
        
        # Remove from pending orders
        if fill.order_id in self.pending_orders:
            del self.pending_orders[fill.order_id]
        
        # Notify strategy of fill
        if fill.strategy_id in self.strategies:
            strategy = self.strategies[fill.strategy_id]
            try:
                strategy.on_order_fill({
                    'fill': fill,
                    'trade': trade
                })
            except Exception as e:
                self.logger.error(f"策略 {fill.strategy_id} 成交通知错误: {e}")
    
    def _signal_to_order(self, signal: Signal, strategy_id: str) -> Optional[Order]:
        """Convert trading signal to order."""
        try:
            order = Order(
                id=str(uuid.uuid4()),
                symbol=signal.symbol,
                side=OrderSide.BUY if signal.action == SignalAction.BUY else OrderSide.SELL,
                order_type=OrderType.LIMIT if signal.price else OrderType.MARKET,
                quantity=signal.quantity,
                price=signal.price,
                timestamp=signal.timestamp or self.current_timestamp,
                strategy_id=strategy_id,
                metadata=signal.metadata
            )
            
            if order.validate():
                return order
            else:
                self.logger.warning(f"⚠️ 信号生成的订单无效: {signal}")
                return None
                
        except Exception as e:
            self.logger.error(f"信号转换为订单时出错: {e}")
            return None
    
    def _finalize_backtest(self) -> None:
        """Finalize backtest and cleanup."""
        # Cancel any pending orders
        for order in self.pending_orders.values():
            order.status = OrderStatus.CANCELLED
        
        # Cleanup strategies
        for strategy in self.strategies.values():
            try:
                strategy.cleanup()
            except Exception as e:
                self.logger.error(f"策略清理错误: {e}")
        
        self.logger.info("✅ 回测清理完成")
    
    # Data manager interface for strategies
    def get_historical_data(self, symbol: str, lookback: int, 
                           end_date: Optional[datetime] = None) -> pd.DataFrame:
        """Get historical data for strategy context."""
        if self.market_data_feed is None:
            return pd.DataFrame()
        
        # Use current timestamp as end date if not specified
        if end_date is None:
            end_date = self.current_timestamp
        
        if end_date is None:
            return pd.DataFrame()
        
        # Filter data
        data = self.market_data_feed[
            (self.market_data_feed['symbol'] == symbol) &
            (self.market_data_feed.index <= end_date)
        ].tail(lookback)
        
        return data
    
    def get_current_data(self, symbol: str) -> Optional[MarketData]:
        """Get current market data for strategy context."""
        return self.current_data.get(symbol)