import logging
logger = logging.getLogger(__name__)
"""
量化交易系统异常类定义

本模块定义了系统中使用的所有异常类，每个异常都包含：
- 中文错误消息
- 错误码
- 详细上下文信息
- 便于日志记录和API响应的转换方法

异常分类：
- 数据异常：数据获取、验证、格式相关
- 策略异常：策略配置、执行相关
- 风险异常：资金管理、仓位限制相关
- 回测异常：回测配置、执行相关
- 订单异常：订单验证、执行相关
- 配置异常：系统配置相关
- API异常：接口调用相关
- 数据库异常：数据库操作相关
"""

from typing import Optional, Dict, Any
import pandas as pd


class TradingSystemException(Exception):
    """交易系统基础异常类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """将异常转换为字典格式，便于日志记录和API响应"""
        return {
            'error_type': self.__class__.__name__,
            'message': self.message,
            'error_code': self.error_code,
            'details': self.details
        }


class DataException(TradingSystemException):
    """数据相关异常基类"""
    pass


class DataValidationException(DataException):
    """数据验证异常
    
    当数据格式、类型或内容不符合预期时抛出
    """
    pass


class DataSourceException(DataException):
    """数据源异常
    
    当数据源连接失败或数据获取失败时抛出
    """
    pass


class DataFormatException(DataException):
    """数据格式异常
    
    当数据格式不正确或无法解析时抛出
    """
    pass


class StrategyException(TradingSystemException):
    """策略相关异常基类"""
    pass


class StrategyConfigurationException(StrategyException):
    """策略配置异常
    
    当策略参数配置错误或缺失时抛出
    """
    pass


class StrategyExecutionException(StrategyException):
    """策略执行异常
    
    当策略执行过程中出现错误时抛出
    """
    pass


class RiskException(TradingSystemException):
    """风险管理异常基类"""
    pass


class RiskLimitException(RiskException):
    """风险限制异常
    
    当超过风险限制阈值时抛出
    """
    pass


class InsufficientFundsException(RiskException):
    """资金不足异常
    
    当账户资金不足以执行交易时抛出
    """
    pass


class PositionLimitException(RiskException):
    """仓位限制异常
    
    当持仓数量超过限制时抛出
    """
    pass


class BacktestException(TradingSystemException):
    """Exception for backtest-related errors."""
    pass


class BacktestConfigurationException(BacktestException):
    """Exception for backtest configuration errors."""
    pass


class BacktestExecutionException(BacktestException):
    """Exception for backtest execution errors."""
    pass


class PortfolioException(TradingSystemException):
    """Exception for portfolio management errors."""
    pass


class OrderException(TradingSystemException):
    """Exception for order-related errors."""
    pass


class OrderValidationException(OrderException):
    """Exception for order validation errors."""
    pass


class OrderExecutionException(OrderException):
    """Exception for order execution errors."""
    pass


class ConfigurationException(TradingSystemException):
    """Exception for system configuration errors."""
    pass


class APIException(TradingSystemException):
    """Exception for API-related errors."""
    pass


class DatabaseException(TradingSystemException):
    """Exception for database-related errors."""
    pass


# Error handling utilities
class ErrorHandler:
    """集中式错误处理工具类"""
    
    # 错误码映射
    ERROR_CODES = {
        'DATA_001': '数据获取失败',
        'DATA_002': '数据验证失败',
        'DATA_003': '数据源连接失败',
        'DATA_004': '数据格式错误',
        'STRATEGY_001': '策略初始化失败',
        'STRATEGY_002': '策略参数配置错误',
        'STRATEGY_003': '策略执行错误',
        'RISK_001': '风险限制超出',
        'RISK_002': '资金不足',
        'RISK_003': '仓位限制超出',
        'BACKTEST_001': '回测配置错误',
        'BACKTEST_002': '回测执行失败',
        'ORDER_001': '订单验证失败',
        'ORDER_002': '订单执行失败',
        'CONFIG_001': '系统配置错误',
        'API_001': 'API调用失败',
        'DB_001': '数据库操作失败',
    }
    
    @staticmethod
    def handle_data_error(error: Exception, context: str = "") -> DataException:
        """将通用错误转换为数据异常，添加上下文信息"""
        if isinstance(error, DataException):
            return error
        
        details = {'原始错误': str(error), '上下文': context}
        return DataException(
            f"数据操作失败：{context} - {str(error)}",
            error_code="DATA_001",
            details=details
        )
    
    @staticmethod
    def handle_strategy_error(error: Exception, strategy_name: str = "") -> StrategyException:
        """将通用错误转换为策略异常，添加上下文信息"""
        if isinstance(error, StrategyException):
            return error
        
        details = {'原始错误': str(error), '策略名称': strategy_name}
        return StrategyException(
            f"策略执行失败：{strategy_name} - {str(error)}",
            error_code="STRATEGY_003",
            details=details
        )
    
    @staticmethod
    def handle_risk_error(error: Exception, context: str = "") -> RiskException:
        """将通用错误转换为风险异常，添加上下文信息"""
        if isinstance(error, RiskException):
            return error
        
        details = {'原始错误': str(error), '上下文': context}
        return RiskException(
            f"风险管理失败：{context} - {str(error)}",
            error_code="RISK_001",
            details=details
        )
    
    @staticmethod
    def log_error(error: TradingSystemException, logger=None) -> None:
        """使用结构化信息记录错误"""
        error_info = error.to_dict()
        error_info['时间戳'] = str(pd.Timestamp.now())
        
        if logger:
            logger.error(f"系统错误: {error_info}")
        else:
            logger.info(f"【错误】{error_info}")
    
    @staticmethod
    def create_error_response(error: TradingSystemException, include_details: bool = False) -> Dict[str, Any]:
        """创建标准化的错误响应，用于API返回"""
        response = {
            '成功': False,
            '错误': {
                '类型': error.__class__.__name__,
                '消息': error.message,
                '错误码': error.error_code,
                '时间戳': str(pd.Timestamp.now())
            }
        }
        
        if include_details and error.details:
            response['错误']['详情'] = error.details
        
        return response
    
    @staticmethod
    def get_error_message(error_code: str) -> str:
        """根据错误码获取中文错误消息"""
        return ErrorHandler.ERROR_CODES.get(error_code, "未知错误")
    
    @staticmethod
    def format_error_for_user(error: TradingSystemException) -> str:
        """格式化错误信息供用户查看"""
        if error.error_code and error.error_code in ErrorHandler.ERROR_CODES:
            base_message = ErrorHandler.ERROR_CODES[error.error_code]
            if error.details:
                details_str = ", ".join([f"{k}: {v}" for k, v in error.details.items()])
                return f"{base_message} ({details_str})"
            return base_message
        
        return error.message