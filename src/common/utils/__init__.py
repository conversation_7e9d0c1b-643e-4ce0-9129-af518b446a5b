#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一工具模块

此模块包含项目中使用的各种工具函数和辅助类。
整合了所有项目的工具需求，提供统一的工具接口。

主要模块：
- logging: 日志管理工具
- messages: 中文消息管理工具  
- tools: 通用工具类集合
- trading_tools: 量化交易专用工具
"""

__version__ = "2.0.0"
__author__ = "量化交易系统开发团队"

# 导入核心工具模块
try:
    from .logging import (
        get_logger, setup_system_logging, LogDeduplicator, 
        DeduplicatingHandler, LoggingOptimizer, LogLevelManager
    )
    from .messages import (
        get_message_manager, get_message, get_chinese_term,
        ChineseMessageManager, MessageCategory, MessageLevel, ERROR_CODES
    )
    from .tools import (
        DateTimeTools, FileTools, DataTools, ValidationTools,
        PerformanceTools, SecurityTools, SystemTools, LoggingTools,
        SystemInfo, dt, ft, dt_tools, vt, pt, st, sys_tools, log_tools
    )
    from .trading_tools import (
        TradingDataTools, TechnicalIndicatorTools, PortfolioAnalysisTools,
        DataQualityTools, MarketDataInfo
    )
    
    # 设置可导出的符号
    __all__ = [
        # 日志工具
        'get_logger', 'setup_system_logging', 'LogDeduplicator', 
        'DeduplicatingHandler', 'LoggingOptimizer', 'LogLevelManager',
        
        # 消息工具
        'get_message_manager', 'get_message', 'get_chinese_term',
        'ChineseMessageManager', 'MessageCategory', 'MessageLevel', 'ERROR_CODES',
        
        # 通用工具类
        'DateTimeTools', 'FileTools', 'DataTools', 'ValidationTools',
        'PerformanceTools', 'SecurityTools', 'SystemTools', 'LoggingTools',
        'SystemInfo',
        
        # 工具类别名
        'dt', 'ft', 'dt_tools', 'vt', 'pt', 'st', 'sys_tools', 'log_tools',
        
        # 交易工具类
        'TradingDataTools', 'TechnicalIndicatorTools', 'PortfolioAnalysisTools',
        'DataQualityTools', 'MarketDataInfo'
    ]
    
except ImportError as e:
    # 如果某些模块无法导入，记录警告但不中断
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"部分工具模块导入失败: {e}")
    
    # 提供基本的导入
    try:
        from .logging import get_logger
        __all__ = ['get_logger']
    except ImportError:
        __all__ = []


def get_all_tools():
    """
    获取所有可用工具的信息
    
    Returns:
        Dict[str, Any]: 包含所有工具类和函数的信息
    """
    tools_info = {
        "version": __version__,
        "modules": {},
        "tool_classes": {},
        "utilities": {}
    }
    
    # 通用工具类
    if 'DateTimeTools' in globals():
        tools_info["tool_classes"]["DateTimeTools"] = {
            "description": "日期时间处理工具",
            "methods": ["now_str", "get_trading_days", "add_days", "is_trading_day"]
        }
    
    if 'FileTools' in globals():
        tools_info["tool_classes"]["FileTools"] = {
            "description": "文件操作工具", 
            "methods": ["ensure_dir", "safe_read_json", "safe_write_json", "backup_file"]
        }
    
    if 'DataTools' in globals():
        tools_info["tool_classes"]["DataTools"] = {
            "description": "数据处理工具",
            "methods": ["safe_float", "safe_divide", "normalize_dict_keys", "flatten_dict"]
        }
    
    if 'ValidationTools' in globals():
        tools_info["tool_classes"]["ValidationTools"] = {
            "description": "数据验证工具",
            "methods": ["is_valid_email", "is_valid_date", "validate_required_fields"]
        }
    
    if 'PerformanceTools' in globals():
        tools_info["tool_classes"]["PerformanceTools"] = {
            "description": "性能优化工具",
            "methods": ["timer", "retry", "memoize"]
        }
    
    if 'SecurityTools' in globals():
        tools_info["tool_classes"]["SecurityTools"] = {
            "description": "安全工具",
            "methods": ["generate_token", "hash_password", "sanitize_filename"]
        }
    
    if 'SystemTools' in globals():
        tools_info["tool_classes"]["SystemTools"] = {
            "description": "系统信息工具",
            "methods": ["get_system_info", "get_memory_usage", "is_debug_mode"]
        }
    
    # 交易专用工具类
    if 'TradingDataTools' in globals():
        tools_info["tool_classes"]["TradingDataTools"] = {
            "description": "交易数据处理工具",
            "methods": ["validate_ohlcv_data", "clean_market_data", "calculate_returns"]
        }
    
    if 'TechnicalIndicatorTools' in globals():
        tools_info["tool_classes"]["TechnicalIndicatorTools"] = {
            "description": "技术指标计算工具",
            "methods": ["moving_average", "rsi", "bollinger_bands", "macd"]
        }
    
    if 'PortfolioAnalysisTools' in globals():
        tools_info["tool_classes"]["PortfolioAnalysisTools"] = {
            "description": "投资组合分析工具",
            "methods": ["calculate_portfolio_returns", "calculate_risk_metrics"]
        }
    
    # 实用函数
    if 'get_logger' in globals():
        tools_info["utilities"]["get_logger"] = "获取日志记录器"
    
    if 'get_message' in globals():
        tools_info["utilities"]["get_message"] = "获取中文消息"
    
    return tools_info


def list_available_tools():
    """
    打印所有可用工具的列表
    """
    tools_info = get_all_tools()
    
    logger.info(f"📦 统一工具库 v{tools_info['version']}")
    logger.info("=" * 50)
    
    if tools_info["tool_classes"]:
        logger.info("\n🔧 可用工具类:")
        for name, info in tools_info["tool_classes"].items():
            logger.info(f"  • {name}: {info['description']}")
            logger.info(f"    主要方法: {', '.join(info['methods'])}")
    
    if tools_info["utilities"]:
        logger.info("\n🛠️  实用函数:")
        for name, desc in tools_info["utilities"].items():
            logger.info(f"  • {name}: {desc}")
    
    logger.info(f"\n📊 总计: {len(tools_info['tool_classes'])} 个工具类, {len(tools_info['utilities'])} 个实用函数")


# 添加模块级别的便捷函数
def quick_setup():
    """
    快速设置常用工具
    
    Returns:
        Dict[str, Any]: 常用工具的引用
    """
    setup = {}
    
    # 设置日志
    if 'get_logger' in globals():
        setup['logger'] = get_logger("app")
    
    # 设置消息管理器
    if 'get_message_manager' in globals():
        setup['messages'] = get_message_manager()
    
    # 常用工具类别名
    if 'DateTimeTools' in globals():
        setup['dt'] = DateTimeTools
    if 'FileTools' in globals():
        setup['ft'] = FileTools
    if 'DataTools' in globals():
        setup['data'] = DataTools
    if 'ValidationTools' in globals():
        setup['validate'] = ValidationTools
    
    return setup