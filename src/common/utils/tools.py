#!/usr/bin/env python3
"""
统一工具库
整合所有项目中常用的工具类和辅助函数
"""

import os
import sys
import json
import time
import hashlib
import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable
from functools import wraps, lru_cache
from dataclasses import dataclass, asdict
import logging

from src.common.utils.logging import get_logger


class DateTimeTools:
    """日期时间工具类"""
    
    @staticmethod
    def now_str(fmt: str = "%Y-%m-%d %H:%M:%S") -> str:
        """获取当前时间字符串"""
        return datetime.datetime.now().strftime(fmt)
    
    @staticmethod
    def now_timestamp() -> float:
        """获取当前时间戳"""
        return time.time()
    
    @staticmethod
    def str_to_datetime(date_str: str, fmt: str = "%Y-%m-%d %H:%M:%S") -> datetime.datetime:
        """字符串转datetime"""
        return datetime.datetime.strptime(date_str, fmt)
    
    @staticmethod
    def datetime_to_str(dt: datetime.datetime, fmt: str = "%Y-%m-%d %H:%M:%S") -> str:
        """datetime转字符串"""
        return dt.strftime(fmt)
    
    @staticmethod
    def timestamp_to_datetime(timestamp: float) -> datetime.datetime:
        """时间戳转datetime"""
        return datetime.datetime.fromtimestamp(timestamp)
    
    @staticmethod
    def datetime_to_timestamp(dt: datetime.datetime) -> float:
        """datetime转时间戳"""
        return dt.timestamp()
    
    @staticmethod
    def add_days(dt: datetime.datetime, days: int) -> datetime.datetime:
        """日期加天数"""
        return dt + datetime.timedelta(days=days)
    
    @staticmethod
    def add_hours(dt: datetime.datetime, hours: int) -> datetime.datetime:
        """日期加小时数"""
        return dt + datetime.timedelta(hours=hours)
    
    @staticmethod
    def get_date_range(start_date: str, end_date: str, fmt: str = "%Y-%m-%d") -> List[str]:
        """获取日期范围内的所有日期"""
        start = datetime.datetime.strptime(start_date, fmt)
        end = datetime.datetime.strptime(end_date, fmt)
        
        dates = []
        current = start
        while current <= end:
            dates.append(current.strftime(fmt))
            current += datetime.timedelta(days=1)
        
        return dates
    
    @staticmethod
    def is_trading_day(date: datetime.datetime) -> bool:
        """判断是否为交易日（简单实现：周一到周五）"""
        return date.weekday() < 5
    
    @staticmethod
    def get_trading_days(start_date: str, end_date: str, fmt: str = "%Y-%m-%d") -> List[str]:
        """获取日期范围内的交易日"""
        all_dates = DateTimeTools.get_date_range(start_date, end_date, fmt)
        trading_days = []
        
        for date_str in all_dates:
            date = datetime.datetime.strptime(date_str, fmt)
            if DateTimeTools.is_trading_day(date):
                trading_days.append(date_str)
        
        return trading_days


class FileTools:
    """文件操作工具类"""
    
    @staticmethod
    def ensure_dir(path: Union[str, Path]) -> Path:
        """确保目录存在"""
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @staticmethod
    def safe_read_json(file_path: Union[str, Path], default: Any = None) -> Any:
        """安全读取JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return default
    
    @staticmethod
    def safe_write_json(file_path: Union[str, Path], data: Any, indent: int = 2) -> bool:
        """安全写入JSON文件"""
        try:
            FileTools.ensure_dir(Path(file_path).parent)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=indent, ensure_ascii=False)
            return True
        except Exception:
            return False
    
    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> int:
        """获取文件大小（字节）"""
        try:
            return Path(file_path).stat().st_size
        except Exception:
            return 0
    
    @staticmethod
    def get_file_hash(file_path: Union[str, Path], algorithm: str = "md5") -> str:
        """获取文件哈希值"""
        hash_algo = hashlib.new(algorithm)
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_algo.update(chunk)
            return hash_algo.hexdigest()
        except Exception:
            return ""
    
    @staticmethod
    def backup_file(file_path: Union[str, Path], backup_suffix: str = None) -> Optional[Path]:
        """备份文件"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                return None
            
            if backup_suffix is None:
                backup_suffix = DateTimeTools.now_str("%Y%m%d_%H%M%S")
            
            backup_path = file_path.with_suffix(f"{file_path.suffix}.bak_{backup_suffix}")
            
            import shutil
            shutil.copy2(file_path, backup_path)
            return backup_path
        except Exception:
            return None
    
    @staticmethod
    def cleanup_old_files(directory: Union[str, Path], 
                         pattern: str = "*", 
                         days: int = 7) -> List[Path]:
        """清理指定天数之前的旧文件"""
        directory = Path(directory)
        if not directory.exists():
            return []
        
        cutoff_time = time.time() - (days * 24 * 3600)
        removed_files = []
        
        try:
            for file_path in directory.glob(pattern):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    removed_files.append(file_path)
        except Exception:
            pass
        
        return removed_files


class DataTools:
    """数据处理工具类"""
    
    @staticmethod
    def safe_float(value: Any, default: float = 0.0) -> float:
        """安全转换为浮点数"""
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def safe_int(value: Any, default: int = 0) -> int:
        """安全转换为整数"""
        try:
            return int(value)
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def safe_divide(a: float, b: float, default: float = 0.0) -> float:
        """安全除法"""
        try:
            return a / b if b != 0 else default
        except (ValueError, TypeError, ZeroDivisionError):
            return default
    
    @staticmethod
    def round_to_precision(value: float, precision: int = 2) -> float:
        """保留指定精度的小数"""
        try:
            return round(value, precision)
        except (ValueError, TypeError):
            return 0.0
    
    @staticmethod
    def percentage(value: float, total: float) -> float:
        """计算百分比"""
        return DataTools.safe_divide(value * 100, total)
    
    @staticmethod
    def clamp(value: float, min_val: float, max_val: float) -> float:
        """将值限制在指定范围内"""
        return max(min_val, min(value, max_val))
    
    @staticmethod
    def normalize_dict_keys(data: Dict[str, Any], to_snake_case: bool = True) -> Dict[str, Any]:
        """标准化字典键名"""
        if not isinstance(data, dict):
            return data
        
        normalized = {}
        for key, value in data.items():
            if to_snake_case:
                # 转换为snake_case
                import re
                normalized_key = re.sub('([a-z0-9])([A-Z])', r'\1_\2', str(key)).lower()
            else:
                # 转换为camelCase
                components = str(key).split('_')
                normalized_key = components[0] + ''.join(word.capitalize() for word in components[1:])
            
            # 递归处理嵌套字典
            if isinstance(value, dict):
                normalized[normalized_key] = DataTools.normalize_dict_keys(value, to_snake_case)
            else:
                normalized[normalized_key] = value
        
        return normalized
    
    @staticmethod
    def flatten_dict(data: Dict[str, Any], separator: str = '.', prefix: str = '') -> Dict[str, Any]:
        """扁平化嵌套字典"""
        result = {}
        
        for key, value in data.items():
            new_key = f"{prefix}{separator}{key}" if prefix else key
            
            if isinstance(value, dict):
                result.update(DataTools.flatten_dict(value, separator, new_key))
            else:
                result[new_key] = value
        
        return result


class ValidationTools:
    """数据验证工具类"""
    
    @staticmethod
    def is_valid_email(email: str) -> bool:
        """验证邮箱格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def is_valid_phone(phone: str) -> bool:
        """验证手机号格式（简单实现）"""
        import re
        pattern = r'^\+?1?[0-9]{10,15}$'
        return bool(re.match(pattern, phone.replace('-', '').replace(' ', '')))
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """验证URL格式"""
        import re
        pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        return bool(re.match(pattern, url))
    
    @staticmethod
    def is_valid_date(date_str: str, fmt: str = "%Y-%m-%d") -> bool:
        """验证日期格式"""
        try:
            datetime.datetime.strptime(date_str, fmt)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """验证必需字段"""
        missing_fields = []
        for field in required_fields:
            if field not in data or data[field] is None or data[field] == '':
                missing_fields.append(field)
        return missing_fields
    
    @staticmethod
    def validate_field_types(data: Dict[str, Any], field_types: Dict[str, type]) -> List[str]:
        """验证字段类型"""
        type_errors = []
        for field, expected_type in field_types.items():
            if field in data and not isinstance(data[field], expected_type):
                type_errors.append(f"{field} should be {expected_type.__name__}")
        return type_errors


class PerformanceTools:
    """性能工具类"""
    
    @staticmethod
    def timer(func: Callable) -> Callable:
        """计时装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            logger = get_logger(f"timer.{func.__module__}.{func.__name__}")
            logger.debug(f"函数 {func.__name__} 执行时间: {end_time - start_time:.4f}秒")
            return result
        return wrapper
    
    @staticmethod
    def retry(max_attempts: int = 3, delay: float = 1.0, exponential_backoff: bool = True):
        """重试装饰器"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                last_exception = None
                
                for attempt in range(max_attempts):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        last_exception = e
                        if attempt < max_attempts - 1:
                            wait_time = delay * (2 ** attempt) if exponential_backoff else delay
                            time.sleep(wait_time)
                
                raise last_exception
            return wrapper
        return decorator
    
    @staticmethod  
    def memoize(maxsize: int = 128, ttl: int = None):
        """带TTL的缓存装饰器"""
        def decorator(func: Callable) -> Callable:
            if ttl is None:
                return lru_cache(maxsize=maxsize)(func)
            
            cache = {}
            cache_times = {}
            
            @wraps(func)
            def wrapper(*args, **kwargs):
                key = str(args) + str(sorted(kwargs.items()))
                current_time = time.time()
                
                # 检查缓存是否过期
                if key in cache_times and current_time - cache_times[key] > ttl:
                    cache.pop(key, None)
                    cache_times.pop(key, None)
                
                # 返回缓存结果或计算新结果
                if key in cache:
                    return cache[key]
                
                result = func(*args, **kwargs)
                cache[key] = result
                cache_times[key] = current_time
                
                # 限制缓存大小
                if len(cache) > maxsize:
                    oldest_key = min(cache_times.keys(), key=cache_times.get)
                    cache.pop(oldest_key, None)
                    cache_times.pop(oldest_key, None)
                
                return result
            
            return wrapper
        return decorator


class SecurityTools:
    """安全工具类"""
    
    @staticmethod
    def generate_token(length: int = 32) -> str:
        """生成随机令牌"""
        import secrets
        import string
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    @staticmethod
    def hash_password(password: str, salt: str = None) -> tuple[str, str]:
        """哈希密码"""
        if salt is None:
            import secrets
            salt = secrets.token_hex(16)
        
        # 使用pbkdf2
        import hashlib
        password_hash = hashlib.pbkdf2_hmac('sha256', 
                                          password.encode('utf-8'), 
                                          salt.encode('utf-8'), 
                                          100000)  # 迭代次数
        return password_hash.hex(), salt
    
    @staticmethod
    def verify_password(password: str, password_hash: str, salt: str) -> bool:
        """验证密码"""
        expected_hash, _ = SecurityTools.hash_password(password, salt)
        return expected_hash == password_hash
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名，移除不安全字符"""
        import re
        # 移除或替换不安全字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 移除控制字符
        filename = ''.join(char for char in filename if ord(char) >= 32)
        # 限制长度
        return filename[:255]
    
    @staticmethod
    def mask_sensitive_data(data: str, mask_char: str = '*', visible_chars: int = 4) -> str:
        """遮蔽敏感数据"""
        if len(data) <= visible_chars:
            return mask_char * len(data)
        
        visible_start = data[:visible_chars//2]
        visible_end = data[-(visible_chars//2):] if visible_chars > 1 else ""
        masked_middle = mask_char * (len(data) - visible_chars)
        
        return visible_start + masked_middle + visible_end


@dataclass
class SystemInfo:
    """系统信息数据类"""
    python_version: str
    platform: str
    cpu_count: int
    memory_total: int
    disk_total: int
    uptime: float


class SystemTools:
    """系统工具类"""
    
    @staticmethod
    def get_system_info() -> SystemInfo:
        """获取系统信息"""
        import platform
        import psutil
        
        try:
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return SystemInfo(
                python_version=platform.python_version(),
                platform=platform.platform(),
                cpu_count=psutil.cpu_count(),
                memory_total=memory.total,
                disk_total=disk.total,
                uptime=time.time() - psutil.boot_time()
            )
        except Exception:
            return SystemInfo(
                python_version=platform.python_version(),
                platform=platform.platform(),
                cpu_count=0,
                memory_total=0,
                disk_total=0,
                uptime=0.0
            )
    
    @staticmethod
    def get_memory_usage() -> Dict[str, float]:
        """获取内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            system_memory = psutil.virtual_memory()
            
            return {
                "process_memory_mb": process.memory_info().rss / 1024 / 1024,
                "system_memory_total_gb": system_memory.total / 1024 / 1024 / 1024,
                "system_memory_used_percent": system_memory.percent,
                "system_memory_available_gb": system_memory.available / 1024 / 1024 / 1024
            }
        except Exception:
            return {}
    
    @staticmethod
    def is_debug_mode() -> bool:
        """检查是否为调试模式"""
        return (hasattr(sys, 'gettrace') and sys.gettrace() is not None) or \
               os.environ.get('DEBUG', '').lower() in ('true', '1', 'yes')
    
    @staticmethod
    def get_environment_info() -> Dict[str, str]:
        """获取环境信息"""
        return {
            "python_path": sys.executable,
            "working_directory": os.getcwd(),
            "user": os.environ.get('USER', os.environ.get('USERNAME', 'unknown')),
            "home": os.environ.get('HOME', os.environ.get('USERPROFILE', 'unknown')),
            "path_separator": os.pathsep,
            "debug_mode": str(SystemTools.is_debug_mode())
        }


class LoggingTools:
    """日志工具类"""
    
    @staticmethod
    def setup_structured_logging(name: str, 
                                level: str = "INFO", 
                                include_system_info: bool = True) -> logging.Logger:
        """设置结构化日志"""
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level.upper()))
        
        # 创建格式化器
        if include_system_info:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - '
                '[PID:%(process)d] [Thread:%(thread)d] - '
                '%(funcName)s:%(lineno)d - %(message)s'
            )
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        
        # 控制台处理器
        if not logger.handlers:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    @staticmethod
    def log_function_call(logger: logging.Logger = None):
        """记录函数调用的装饰器"""
        def decorator(func: Callable) -> Callable:
            nonlocal logger
            if logger is None:
                logger = get_logger(f"function_call.{func.__module__}.{func.__name__}")
            
            @wraps(func)
            def wrapper(*args, **kwargs):
                logger.debug(f"调用函数 {func.__name__} with args={args}, kwargs={kwargs}")
                try:
                    result = func(*args, **kwargs)
                    logger.debug(f"函数 {func.__name__} 执行成功")
                    return result
                except Exception as e:
                    logger.error(f"函数 {func.__name__} 执行失败: {e}")
                    raise
            return wrapper
        return decorator


# 常用工具类的快捷别名
dt = DateTimeTools
ft = FileTools  
dt_tools = DataTools
vt = ValidationTools
pt = PerformanceTools
st = SecurityTools
sys_tools = SystemTools
log_tools = LoggingTools


# 导出所有工具类
__all__ = [
    'DateTimeTools', 'FileTools', 'DataTools', 'ValidationTools', 
    'PerformanceTools', 'SecurityTools', 'SystemInfo', 'SystemTools',
    'LoggingTools', 'dt', 'ft', 'dt_tools', 'vt', 'pt', 'st', 
    'sys_tools', 'log_tools'
]