"""
中文日志格式化系统

本模块提供中文日志格式化、级别映射和结构化日志输出功能。
支持中文日志级别、时间格式、上下文信息集成等。

主要功能：
- 中文日志级别显示
- 结构化日志格式
- 上下文信息集成
- 日志文件管理
- 性能监控日志

作者: 量化交易系统开发团队
版本: 1.0.0
创建日期: 2025-07-30
"""

import logging
import logging.handlers
import json
import os
import hashlib
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Set
from pathlib import Path
from collections import defaultdict
from threading import Lock

from .messages import get_message_manager, MessageLevel


class LogDeduplicator:
    """
    日志去重器
    
    通过缓存和时间窗口管理减少重复日志消息，提高日志系统性能。
    
    主要功能：
    - 基于消息内容的去重
    - 时间窗口管理
    - 重复计数统计
    - 线程安全操作
    """
    
    def __init__(self, window_seconds: int = 60, max_cache_size: int = 1000):
        """
        初始化日志去重器
        
        Args:
            window_seconds: 去重时间窗口（秒）
            max_cache_size: 最大缓存大小
        """
        self.window_seconds = window_seconds
        self.max_cache_size = max_cache_size
        
        # 消息缓存：{message_hash: {'first_seen': timestamp, 'last_seen': timestamp, 'count': int}}
        self.message_cache: Dict[str, Dict[str, Any]] = {}
        
        # 线程锁确保线程安全
        self._lock = Lock()
        
        # 清理计数器
        self._cleanup_counter = 0
        self._cleanup_interval = 10  # 每10次调用清理一次
    
    def _generate_message_hash(self, message: str, level: str, logger_name: str) -> str:
        """
        生成消息哈希值
        
        Args:
            message: 日志消息
            level: 日志级别
            logger_name: 日志器名称
            
        Returns:
            消息的哈希值
        """
        # 组合消息内容、级别和日志器名称生成唯一标识
        content = f"{logger_name}:{level}:{message}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _cleanup_expired_messages(self):
        """清理过期的消息缓存"""
        current_time = time.time()
        expired_hashes = []
        
        for msg_hash, info in self.message_cache.items():
            if current_time - info['last_seen'] > self.window_seconds:
                expired_hashes.append(msg_hash)
        
        for msg_hash in expired_hashes:
            del self.message_cache[msg_hash]
    
    def _cleanup_oversized_cache(self):
        """清理过大的缓存"""
        if len(self.message_cache) <= self.max_cache_size:
            return
            
        # 按最后见到时间排序，删除最旧的消息
        sorted_items = sorted(
            self.message_cache.items(),
            key=lambda x: x[1]['last_seen']
        )
        
        # 删除最旧的消息直到达到限制
        items_to_remove = len(sorted_items) - self.max_cache_size
        for i in range(items_to_remove):
            del self.message_cache[sorted_items[i][0]]
    
    def _cleanup_if_needed(self):
        """根据需要执行清理"""
        self._cleanup_counter += 1
        
        # 定期清理过期消息
        if self._cleanup_counter % self._cleanup_interval == 0:
            self._cleanup_expired_messages()
        
        # 如果缓存过大，立即清理最旧的消息
        if len(self.message_cache) > self.max_cache_size:
            self._cleanup_oversized_cache()
    
    def should_log(self, message: str, level: str, logger_name: str) -> tuple[bool, int]:
        """
        判断是否应该记录日志
        
        Args:
            message: 日志消息
            level: 日志级别
            logger_name: 日志器名称
            
        Returns:
            (是否应该记录, 重复次数)
        """
        with self._lock:
            current_time = time.time()
            msg_hash = self._generate_message_hash(message, level, logger_name)
            
            # 清理缓存
            self._cleanup_if_needed()
            
            if msg_hash in self.message_cache:
                # 消息已存在
                info = self.message_cache[msg_hash]
                
                # 检查是否在时间窗口内
                if current_time - info['first_seen'] <= self.window_seconds:
                    # 在时间窗口内，更新计数和最后见到时间
                    info['count'] += 1
                    info['last_seen'] = current_time
                    
                    # 对于ERROR和CRITICAL级别，总是记录
                    if level in ['ERROR', 'CRITICAL']:
                        return True, info['count']
                    
                    # 对于其他级别，只在第一次和每10次记录
                    if info['count'] % 10 == 0:
                        return True, info['count']
                    
                    return False, info['count']
                else:
                    # 超出时间窗口，重置计数
                    info['first_seen'] = current_time
                    info['last_seen'] = current_time
                    info['count'] = 1
                    return True, 1
            else:
                # 新消息
                self.message_cache[msg_hash] = {
                    'first_seen': current_time,
                    'last_seen': current_time,
                    'count': 1
                }
                
                # 检查缓存大小
                if len(self.message_cache) > self.max_cache_size:
                    self._cleanup_oversized_cache()
                
                return True, 1
    
    def add_message(self, message: str, level: str, logger_name: str):
        """
        添加消息到缓存（用于手动管理）
        
        Args:
            message: 日志消息
            level: 日志级别
            logger_name: 日志器名称
        """
        self.should_log(message, level, logger_name)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取去重统计信息
        
        Returns:
            统计信息字典
        """
        with self._lock:
            current_time = time.time()
            active_messages = 0
            total_count = 0
            
            for info in self.message_cache.values():
                if current_time - info['last_seen'] <= self.window_seconds:
                    active_messages += 1
                    total_count += info['count']
            
            return {
                'active_messages': active_messages,
                'total_cached_messages': len(self.message_cache),
                'total_message_count': total_count,
                'window_seconds': self.window_seconds,
                'max_cache_size': self.max_cache_size
            }
    
    def clear_cache(self):
        """清空缓存"""
        with self._lock:
            self.message_cache.clear()
            self._cleanup_counter = 0


class DeduplicatingHandler(logging.Handler):
    """
    去重日志处理器
    
    包装其他处理器，在记录日志前进行去重处理
    """
    
    def __init__(self, wrapped_handler: logging.Handler, deduplicator: LogDeduplicator):
        """
        初始化去重处理器
        
        Args:
            wrapped_handler: 被包装的处理器
            deduplicator: 日志去重器
        """
        super().__init__()
        self.wrapped_handler = wrapped_handler
        self.deduplicator = deduplicator
        
        # 继承被包装处理器的级别和格式器
        self.setLevel(wrapped_handler.level)
        if wrapped_handler.formatter:
            self.setFormatter(wrapped_handler.formatter)
    
    def emit(self, record: logging.LogRecord):
        """
        发送日志记录
        
        Args:
            record: 日志记录
        """
        try:
            # 检查是否应该记录此消息
            should_log, count = self.deduplicator.should_log(
                record.getMessage(),
                record.levelname,
                record.name
            )
            
            if should_log:
                # 如果重复次数大于1，在消息中添加重复计数
                if count > 1:
                    original_msg = record.getMessage()
                    record.msg = f"{original_msg} (重复 {count} 次)"
                    # 清除args以避免格式化问题
                    record.args = ()
                
                # 委托给被包装的处理器
                self.wrapped_handler.emit(record)
                
        except Exception:
            self.handleError(record)
    
    def setFormatter(self, formatter):
        """设置格式器"""
        super().setFormatter(formatter)
        self.wrapped_handler.setFormatter(formatter)
    
    def setLevel(self, level):
        """设置日志级别"""
        super().setLevel(level)
        self.wrapped_handler.setLevel(level)
    
    def close(self):
        """关闭处理器"""
        super().close()
        self.wrapped_handler.close()


class LoggingOptimizer:
    """
    日志优化管理器
    
    统一管理系统中各个组件的日志级别，特别是优化watchfiles和数据适配器的日志输出。
    
    主要功能：
    - 调整watchfiles日志级别以减少噪音
    - 设置数据适配器日志级别为DEBUG
    - 提供统一的日志优化接口
    - 支持动态调整日志级别
    """
    
    def __init__(self):
        """初始化日志优化管理器"""
        self._original_levels = {}  # 保存原始日志级别
        self._optimized_loggers = set()  # 已优化的日志器集合
    
    def optimize_watchfiles_logging(self, level: str = "WARNING"):
        """
        优化watchfiles库的日志级别
        
        Args:
            level: 要设置的日志级别，默认为WARNING
        """
        watchfiles_loggers = [
            'watchfiles',
            'watchfiles.main',
            'watchfiles.watcher',
            'watchdog',
            'watchdog.observers',
            'watchdog.observers.inotify_buffer'
        ]
        
        for logger_name in watchfiles_loggers:
            logger = logging.getLogger(logger_name)
            
            # 保存原始级别
            if logger_name not in self._original_levels:
                self._original_levels[logger_name] = logger.level
            
            # 设置新级别
            logger.setLevel(getattr(logging, level.upper()))
            self._optimized_loggers.add(logger_name)
    
    def optimize_data_adapter_logging(self, level: str = "DEBUG"):
        """
        优化数据适配器的日志级别
        
        Args:
            level: 要设置的日志级别，默认为DEBUG
        """
        data_adapter_loggers = [
            'data_adapter',
            'src.data',
            'data.adapter',
            'data.source',
            'data.feed',
            'market_data',
            'price_feed'
        ]
        
        for logger_name in data_adapter_loggers:
            logger = logging.getLogger(logger_name)
            
            # 保存原始级别
            if logger_name not in self._original_levels:
                self._original_levels[logger_name] = logger.level
            
            # 设置新级别
            logger.setLevel(getattr(logging, level.upper()))
            self._optimized_loggers.add(logger_name)
    
    def optimize_third_party_logging(self):
        """优化第三方库的日志级别"""
        third_party_configs = {
            # HTTP库
            'urllib3': 'WARNING',
            'requests': 'WARNING',
            'httpx': 'WARNING',
            'aiohttp': 'WARNING',
            
            # 数据库库
            'sqlalchemy': 'WARNING',
            'asyncpg': 'WARNING',
            'psycopg2': 'WARNING',
            
            # 异步库
            'asyncio': 'WARNING',
            'concurrent.futures': 'WARNING',
            
            # 其他常见库
            'matplotlib': 'WARNING',
            'PIL': 'WARNING',
            'pandas': 'WARNING'
        }
        
        for logger_name, level in third_party_configs.items():
            logger = logging.getLogger(logger_name)
            
            # 保存原始级别
            if logger_name not in self._original_levels:
                self._original_levels[logger_name] = logger.level
            
            # 设置新级别
            logger.setLevel(getattr(logging, level.upper()))
            self._optimized_loggers.add(logger_name)
    
    def set_logger_level(self, logger_name: str, level: str):
        """
        设置指定日志器的级别
        
        Args:
            logger_name: 日志器名称
            level: 日志级别
        """
        logger = logging.getLogger(logger_name)
        
        # 保存原始级别
        if logger_name not in self._original_levels:
            self._original_levels[logger_name] = logger.level
        
        # 设置新级别
        logger.setLevel(getattr(logging, level.upper()))
        self._optimized_loggers.add(logger_name)
    
    def apply_all_optimizations(self):
        """应用所有日志优化"""
        self.optimize_watchfiles_logging()
        self.optimize_data_adapter_logging()
        self.optimize_third_party_logging()
    
    def restore_original_levels(self):
        """恢复所有日志器的原始级别"""
        for logger_name in self._optimized_loggers:
            if logger_name in self._original_levels:
                logger = logging.getLogger(logger_name)
                logger.setLevel(self._original_levels[logger_name])
        
        self._optimized_loggers.clear()
        self._original_levels.clear()
    
    def get_optimization_status(self) -> Dict[str, Any]:
        """
        获取优化状态信息
        
        Returns:
            优化状态字典
        """
        status = {
            'optimized_loggers': list(self._optimized_loggers),
            'total_optimized': len(self._optimized_loggers),
            'original_levels': dict(self._original_levels)
        }
        
        # 获取当前级别
        current_levels = {}
        for logger_name in self._optimized_loggers:
            logger = logging.getLogger(logger_name)
            current_levels[logger_name] = logging.getLevelName(logger.level)
        
        status['current_levels'] = current_levels
        return status
    
    def is_logger_optimized(self, logger_name: str) -> bool:
        """
        检查日志器是否已被优化
        
        Args:
            logger_name: 日志器名称
            
        Returns:
            是否已优化
        """
        return logger_name in self._optimized_loggers


class LogLevelManager:
    """
    日志级别管理器
    
    提供动态日志级别调整功能，支持配置文件和运行时调整。
    
    主要功能：
    - 动态调整日志级别
    - 配置文件支持
    - 批量级别管理
    - 级别变更历史记录
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化日志级别管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self._level_history = []  # 级别变更历史
        self._managed_loggers = {}  # 管理的日志器 {name: logger}
        self._default_config = {
            'system': 'INFO',
            'watchfiles': 'WARNING',
            'data_adapter': 'DEBUG',
            'urllib3': 'WARNING',
            'requests': 'WARNING'
        }
        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        if not self.config_file or not os.path.exists(self.config_file):
            self._config = self._default_config.copy()
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                if self.config_file.endswith('.json'):
                    import json
                    loaded_config = json.load(f)
                elif self.config_file.endswith(('.yml', '.yaml')):
                    try:
                        import yaml
                        loaded_config = yaml.safe_load(f)
                    except ImportError:
                        raise ImportError("需要安装PyYAML库来支持YAML配置文件")
                else:
                    # 简单的键值对格式
                    loaded_config = {}
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            key, value = line.split('=', 1)
                            loaded_config[key.strip()] = value.strip()
            
            # 合并配置
            self._config = self._default_config.copy()
            self._config.update(loaded_config)
            
        except Exception as e:
            logger.info(f"警告: 加载配置文件失败 {self.config_file}: {e}")
            self._config = self._default_config.copy()
    
    def _save_config(self):
        """保存配置到文件"""
        if not self.config_file:
            return
        
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                if self.config_file.endswith('.json'):
                    import json
                    json.dump(self._config, f, indent=2, ensure_ascii=False)
                elif self.config_file.endswith(('.yml', '.yaml')):
                    try:
                        import yaml
                        yaml.dump(self._config, f, default_flow_style=False, allow_unicode=True)
                    except ImportError:
                        raise ImportError("需要安装PyYAML库来支持YAML配置文件")
                else:
                    # 简单的键值对格式
                    for key, value in self._config.items():
                        f.write(f"{key}={value}\n")
                        
        except Exception as e:
            logger.info(f"警告: 保存配置文件失败 {self.config_file}: {e}")
    
    def set_level(self, logger_name: str, level: str, save_to_config: bool = True):
        """
        设置日志器级别
        
        Args:
            logger_name: 日志器名称
            level: 日志级别
            save_to_config: 是否保存到配置文件
        """
        logger = logging.getLogger(logger_name)
        old_level = logging.getLevelName(logger.level)
        
        # 设置新级别
        logger.setLevel(getattr(logging, level.upper()))
        
        # 记录到管理列表
        self._managed_loggers[logger_name] = logger
        
        # 记录历史
        self._level_history.append({
            'timestamp': datetime.now().isoformat(),
            'logger_name': logger_name,
            'old_level': old_level,
            'new_level': level.upper(),
            'action': 'set_level'
        })
        
        # 更新配置
        if save_to_config:
            self._config[logger_name] = level.upper()
            self._save_config()
    
    def get_level(self, logger_name: str) -> str:
        """
        获取日志器级别
        
        Args:
            logger_name: 日志器名称
            
        Returns:
            日志级别名称
        """
        logger = logging.getLogger(logger_name)
        return logging.getLevelName(logger.level)
    
    def apply_config(self):
        """应用配置文件中的所有设置"""
        for logger_name, level in self._config.items():
            try:
                self.set_level(logger_name, level, save_to_config=False)
            except Exception as e:
                logger.info(f"警告: 设置日志器 {logger_name} 级别失败: {e}")
    
    def reset_to_defaults(self):
        """重置所有日志器到默认级别"""
        for logger_name, level in self._default_config.items():
            self.set_level(logger_name, level, save_to_config=False)
        
        self._config = self._default_config.copy()
        self._save_config()
        
        # 记录历史
        self._level_history.append({
            'timestamp': datetime.now().isoformat(),
            'action': 'reset_to_defaults',
            'affected_loggers': list(self._default_config.keys())
        })
    
    def bulk_set_levels(self, level_mapping: Dict[str, str], save_to_config: bool = True):
        """
        批量设置日志器级别
        
        Args:
            level_mapping: 日志器名称到级别的映射
            save_to_config: 是否保存到配置文件
        """
        for logger_name, level in level_mapping.items():
            try:
                self.set_level(logger_name, level, save_to_config=False)
            except Exception as e:
                logger.info(f"警告: 设置日志器 {logger_name} 级别失败: {e}")
        
        if save_to_config:
            self._config.update({k: v.upper() for k, v in level_mapping.items()})
            self._save_config()
    
    def get_all_levels(self) -> Dict[str, str]:
        """
        获取所有管理的日志器级别
        
        Returns:
            日志器名称到级别的映射
        """
        levels = {}
        for logger_name in self._managed_loggers:
            levels[logger_name] = self.get_level(logger_name)
        
        # 也包括配置中但未激活的日志器
        for logger_name in self._config:
            if logger_name not in levels:
                levels[logger_name] = self.get_level(logger_name)
        
        return levels
    
    def get_history(self, limit: Optional[int] = None) -> list:
        """
        获取级别变更历史
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            历史记录列表
        """
        if limit:
            return self._level_history[-limit:]
        return self._level_history.copy()
    
    def clear_history(self):
        """清空历史记录"""
        self._level_history.clear()
    
    def get_config(self) -> Dict[str, str]:
        """
        获取当前配置
        
        Returns:
            配置字典
        """
        return self._config.copy()
    
    def update_config(self, new_config: Dict[str, str], apply_immediately: bool = True):
        """
        更新配置
        
        Args:
            new_config: 新配置
            apply_immediately: 是否立即应用
        """
        self._config.update(new_config)
        
        if apply_immediately:
            self.apply_config()
        
        self._save_config()
    
    def reload_config(self):
        """重新加载配置文件"""
        self._load_config()
        self.apply_config()
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取管理器状态
        
        Returns:
            状态信息字典
        """
        return {
            'config_file': self.config_file,
            'managed_loggers': list(self._managed_loggers.keys()),
            'total_managed': len(self._managed_loggers),
            'current_levels': self.get_all_levels(),
            'config': self._config,
            'history_count': len(self._level_history)
        }


class ChineseFormatter(logging.Formatter):
    """
    中文日志格式化器
    
    将日志格式化为中文显示，包括：
    - 中文日志级别
    - 中文时间格式
    - 结构化上下文信息
    """
    
    # 日志级别中文映射
    LEVEL_MAPPING = {
        'DEBUG': '调试',
        'INFO': '信息',
        'WARNING': '警告',
        'ERROR': '错误',
        'CRITICAL': '严重'
    }
    
    def __init__(self, fmt: Optional[str] = None, datefmt: Optional[str] = None):
        super().__init__(fmt, datefmt)
        self.message_manager = get_message_manager()
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        # 转换日志级别为中文
        record.levelname_cn = self.LEVEL_MAPPING.get(record.levelname, record.levelname)
        
        # 格式化时间
        record.asctime_cn = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
        
        # 处理消息
        if hasattr(record, 'message_key'):
            record.msg = self.message_manager.get_message(
                record.message_key, 
                **getattr(record, 'message_params', {})
            )
        
        return super().format(record)


class StructuredFormatter(logging.Formatter):
    """
    结构化日志格式化器
    
    将日志格式化为JSON结构，便于日志分析和监控
    """
    
    def __init__(self):
        super().__init__()
        self.message_manager = get_message_manager()
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化为结构化JSON"""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'level_cn': ChineseFormatter.LEVEL_MAPPING.get(record.levelname, record.levelname),
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'process': record.process,
            'thread': record.thread
        }
        
        # 添加额外信息
        if hasattr(record, 'context'):
            log_entry['context'] = record.context
        
        if hasattr(record, 'performance'):
            log_entry['performance'] = record.performance
        
        if hasattr(record, 'error_code'):
            log_entry['error_code'] = record.error_code
        
        # 添加自定义字段
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created', 'msecs',
                          'relativeCreated', 'thread', 'threadName', 'processName',
                          'process', 'message', 'asctime'] and not key.startswith('_'):
                log_entry[key] = value
        
        return json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))


class ChineseLogger:
    """
    中文日志管理器
    
    提供统一的中文日志管理，支持：
    - 中文日志级别
    - 多输出目标（控制台、文件、网络）
    - 日志轮转
    - 性能监控
    """
    
    def __init__(self, name: str, log_dir: str = "logs", level: str = "INFO"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 设置处理器
        self._setup_handlers()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        console_formatter = ChineseFormatter(
            fmt='[%(asctime_cn)s] [%(levelname_cn)s] %(name)s: %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器 - 普通日志
        file_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / f"{self.name}.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        file_formatter = ChineseFormatter(
            fmt='[%(asctime_cn)s] [%(levelname_cn)s] [%(name)s:%(lineno)d] %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # 文件处理器 - 结构化日志
        json_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / f"{self.name}_structured.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        json_handler.setLevel(logging.DEBUG)
        json_handler.setFormatter(StructuredFormatter())
        self.logger.addHandler(json_handler)
        
        # 错误日志单独文件
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / f"{self.name}_error.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(ChineseFormatter(
            fmt='[%(asctime_cn)s] [%(levelname_cn)s] %(name)s:%(lineno)d: %(message)s'
        ))
        self.logger.addHandler(error_handler)
    
    def debug(self, msg: str, **kwargs):
        """调试日志"""
        self.logger.debug(msg, extra=kwargs)
    
    def info(self, msg: str, **kwargs):
        """信息日志"""
        self.logger.info(msg, extra=kwargs)
    
    def warning(self, msg: str, **kwargs):
        """警告日志"""
        self.logger.warning(msg, extra=kwargs)
    
    def error(self, msg: str, error_code: Optional[str] = None, **kwargs):
        """错误日志"""
        if error_code:
            kwargs['error_code'] = error_code
        self.logger.error(msg, extra=kwargs)
    
    def critical(self, msg: str, error_code: Optional[str] = None, **kwargs):
        """严重错误日志"""
        if error_code:
            kwargs['error_code'] = error_code
        self.logger.critical(msg, extra=kwargs)
    
    def performance(self, operation: str, duration: float, **kwargs):
        """性能日志"""
        kwargs.update({
            'performance': {
                'operation': operation,
                'duration': duration,
                'duration_ms': duration * 1000
            }
        })
        self.logger.info(f"性能监控: {operation} 耗时 {duration:.3f}秒", extra=kwargs)
    
    def message(self, message_key: str, level: str = "INFO", **kwargs):
        """使用消息键记录日志"""
        log_level = getattr(logging, level.upper(), logging.INFO)
        
        self.logger.log(
            log_level,
            "",  # 消息内容由formatter处理
            extra={
                'message_key': message_key,
                'message_params': kwargs
            }
        )


class PerformanceLogger:
    """
    性能日志记录器
    
    用于记录操作性能指标，支持上下文管理器用法
    """
    
    def __init__(self, logger: ChineseLogger, operation: str):
        self.logger = logger
        self.operation = operation
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.info(f"开始执行: {self.operation}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = (datetime.now() - self.start_time).total_seconds()
        
        if exc_type is None:
            self.logger.performance(self.operation, duration)
        else:
            self.logger.error(
                f"执行失败: {self.operation}",
                error_code="PERF_001",
                duration=duration,
                error=str(exc_val)
            )


class ContextLogger:
    """
    上下文日志记录器
    
    在日志中添加上下文信息，如策略ID、用户ID等
    """
    
    def __init__(self, logger: ChineseLogger, **context):
        self.logger = logger
        self.context = context
    
    def debug(self, msg: str, **kwargs):
        kwargs['context'] = self.context
        self.logger.debug(msg, **kwargs)
    
    def info(self, msg: str, **kwargs):
        kwargs['context'] = self.context
        self.logger.info(msg, **kwargs)
    
    def warning(self, msg: str, **kwargs):
        kwargs['context'] = self.context
        self.logger.warning(msg, **kwargs)
    
    def error(self, msg: str, **kwargs):
        kwargs['context'] = self.context
        self.logger.error(msg, **kwargs)
    
    def critical(self, msg: str, **kwargs):
        kwargs['context'] = self.context
        self.logger.critical(msg, **kwargs)
    
    def message(self, message_key: str, level: str = "INFO", **kwargs):
        kwargs['context'] = self.context
        self.logger.message(message_key, level, **kwargs)


# 全局日志管理器
_loggers: Dict[str, ChineseLogger] = {}


def get_logger(name: str, log_dir: str = "logs", level: str = "INFO") -> ChineseLogger:
    """获取中文日志管理器"""
    key = f"{name}_{log_dir}_{level}"
    
    if key not in _loggers:
        _loggers[key] = ChineseLogger(name, log_dir, level)
    
    return _loggers[key]


def get_performance_logger(logger: ChineseLogger, operation: str) -> PerformanceLogger:
    """获取性能日志记录器"""
    return PerformanceLogger(logger, operation)


def get_context_logger(logger: ChineseLogger, **context) -> ContextLogger:
    """获取上下文日志记录器"""
    return ContextLogger(logger, **context)


# 便捷函数
def setup_system_logging(log_dir: str = "logs", level: str = "INFO"):
    """设置系统级日志"""
    # 设置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = ChineseFormatter(
        fmt='[%(asctime_cn)s] [%(levelname_cn)s] %(name)s: %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    file_handler = logging.handlers.RotatingFileHandler(
        log_path / "system.log",
        maxBytes=50*1024*1024,  # 50MB
        backupCount=10,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_formatter = ChineseFormatter(
        fmt='[%(asctime_cn)s] [%(levelname_cn)s] [%(name)s:%(lineno)d] %(message)s'
    )
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)


def log_system_startup():
    """记录系统启动日志"""
    logger = get_logger("system")
    logger.message("system.startup")


def log_system_ready():
    """记录系统就绪日志"""
    logger = get_logger("system")
    logger.message("system.ready")


def log_system_shutdown():
    """记录系统关闭日志"""
    logger = get_logger("system")
    logger.message("system.shutdown")