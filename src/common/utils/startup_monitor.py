import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
启动状态监控界面
为系统启动提供友好的进度显示和实时状态监控
"""

import os
import sys
import time
import threading
from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

# 颜色代码
class Colors:
    RESET = '\033[0m'
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    
    # 背景色
    BG_RED = '\033[101m'
    BG_GREEN = '\033[102m'
    BG_YELLOW = '\033[103m'
    BG_BLUE = '\033[104m'


class StartupStatus(Enum):
    """启动状态枚举"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class StartupStep:
    """启动步骤"""
    name: str
    description: str
    status: StartupStatus = StartupStatus.NOT_STARTED
    progress: int = 0  # 0-100
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    details: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}


class StartupMonitor:
    """启动状态监控器"""
    
    def __init__(self):
        self.steps: List[StartupStep] = []
        self.current_step_index = 0
        self.overall_progress = 0
        self.start_time = datetime.now()
        self.is_running = False
        self.update_interval = 0.5  # 更新间隔（秒）
        
        # 控制台清屏和光标控制
        self.clear_screen = lambda: os.system('clear' if os.name == 'posix' else 'cls')
        
        # 初始化步骤
        self._initialize_default_steps()
    
    def _initialize_default_steps(self):
        """初始化默认启动步骤"""
        default_steps = [
            ("环境检查", "检查Python环境和系统要求"),
            ("虚拟环境", "创建和激活虚拟环境"),
            ("依赖安装", "安装Python和Node.js依赖"),
            ("配置生成", "生成和验证配置文件"),
            ("数据库初始化", "初始化数据库和数据表"),
            ("后端服务", "启动后端API服务"),
            ("前端服务", "启动前端开发服务器"),
            ("健康检查", "验证服务健康状态"),
            ("完成", "系统启动完成")
        ]
        
        for name, desc in default_steps:
            self.add_step(name, desc)
    
    def add_step(self, name: str, description: str):
        """添加启动步骤"""
        step = StartupStep(name=name, description=description)
        self.steps.append(step)
    
    def start_step(self, step_index: int = None):
        """开始执行步骤"""
        if step_index is None:
            step_index = self.current_step_index
        
        if 0 <= step_index < len(self.steps):
            step = self.steps[step_index]
            step.status = StartupStatus.IN_PROGRESS
            step.start_time = datetime.now()
            step.progress = 0
            self.current_step_index = step_index
    
    def update_step_progress(self, progress: int, details: Dict[str, Any] = None):
        """更新当前步骤进度"""
        if 0 <= self.current_step_index < len(self.steps):
            step = self.steps[self.current_step_index]
            step.progress = max(0, min(100, progress))
            if details:
                step.details.update(details)
    
    def complete_step(self, success: bool = True, error_message: str = None):
        """完成当前步骤"""
        if 0 <= self.current_step_index < len(self.steps):
            step = self.steps[self.current_step_index]
            step.end_time = datetime.now()
            step.progress = 100
            
            if success:
                step.status = StartupStatus.COMPLETED
            else:
                step.status = StartupStatus.FAILED
                step.error_message = error_message
            
            # 移动到下一步
            if success and self.current_step_index < len(self.steps) - 1:
                self.current_step_index += 1
    
    def skip_step(self, reason: str = ""):
        """跳过当前步骤"""
        if 0 <= self.current_step_index < len(self.steps):
            step = self.steps[self.current_step_index]
            step.status = StartupStatus.SKIPPED
            step.end_time = datetime.now()
            step.error_message = reason
            
            if self.current_step_index < len(self.steps) - 1:
                self.current_step_index += 1
    
    def get_overall_progress(self) -> int:
        """计算整体进度"""
        if not self.steps:
            return 0
        
        total_progress = 0
        for step in self.steps:
            if step.status == StartupStatus.COMPLETED:
                total_progress += 100
            elif step.status == StartupStatus.IN_PROGRESS:
                total_progress += step.progress
            # 跳过和未开始的步骤不计入进度
        
        return min(100, total_progress // len(self.steps))
    
    def display_startup_progress(self):
        """显示启动进度"""
        self.clear_screen()
        
        logger.info(f"{Colors.BOLD}{Colors.CYAN}{'='*60}")
        logger.info(f"🚀 量化交易系统启动监控")
        logger.info(f"{'='*60}{Colors.RESET}")
        
        # 整体进度
        overall_progress = self.get_overall_progress()
        elapsed_time = (datetime.now() - self.start_time).total_seconds()
        
        logger.info(f"\n{Colors.BOLD}整体进度: {self._get_progress_bar(overall_progress)}{Colors.RESET}")
        logger.info(f"启动时间: {elapsed_time:.1f}秒")
        
        # 步骤详情
        logger.info(f"\n{Colors.BOLD}启动步骤:{Colors.RESET}")
        for i, step in enumerate(self.steps):
            self._display_step(step, i == self.current_step_index)
        
        # 当前步骤详情
        if 0 <= self.current_step_index < len(self.steps):
            current_step = self.steps[self.current_step_index]
            if current_step.details:
                logger.info(f"\n{Colors.BOLD}当前步骤详情:{Colors.RESET}")
                for key, value in current_step.details.items():
                    logger.info(f"  {key}: {value}")
        
        logger.info(f"\n{Colors.CYAN}{'='*60}{Colors.RESET}")
    
    def _display_step(self, step: StartupStep, is_current: bool = False):
        """显示单个步骤状态"""
        # 状态图标和颜色
        status_info = {
            StartupStatus.NOT_STARTED: ("⭕", Colors.WHITE),
            StartupStatus.IN_PROGRESS: ("🔄", Colors.YELLOW),
            StartupStatus.COMPLETED: ("✅", Colors.GREEN),
            StartupStatus.FAILED: ("❌", Colors.RED),
            StartupStatus.SKIPPED: ("⏭️", Colors.CYAN)
        }
        
        icon, color = status_info.get(step.status, ("❓", Colors.WHITE))
        
        # 构建显示文本
        step_text = f"{icon} {step.name}: {step.description}"
        
        if is_current:
            step_text = f"{Colors.BOLD}{color}{step_text}{Colors.RESET}"
        else:
            step_text = f"{color}{step_text}{Colors.RESET}"
        
        # 显示进度条（仅对进行中的步骤）
        if step.status == StartupStatus.IN_PROGRESS and step.progress > 0:
            progress_bar = self._get_progress_bar(step.progress, width=20)
            step_text += f" {progress_bar}"
        
        # 显示错误信息
        if step.status == StartupStatus.FAILED and step.error_message:
            step_text += f"\n    {Colors.RED}错误: {step.error_message}{Colors.RESET}"
        
        # 显示执行时间
        if step.start_time:
            if step.end_time:
                duration = (step.end_time - step.start_time).total_seconds()
                step_text += f" ({duration:.1f}s)"
            elif step.status == StartupStatus.IN_PROGRESS:
                duration = (datetime.now() - step.start_time).total_seconds()
                step_text += f" ({duration:.1f}s...)"
        
        logger.info(f"  {step_text}")
    
    def _get_progress_bar(self, progress: int, width: int = 30) -> str:
        """生成进度条"""
        filled = int(width * progress / 100)
        bar = '█' * filled + '░' * (width - filled)
        return f"[{Colors.GREEN}{bar}{Colors.RESET}] {progress:3d}%"
    
    def show_current_step(self):
        """显示当前执行步骤"""
        if 0 <= self.current_step_index < len(self.steps):
            step = self.steps[self.current_step_index]
            logger.info(f"{Colors.YELLOW}➤ 正在执行: {step.name} - {step.description}{Colors.RESET}")
    
    def update_progress_indicators(self):
        """更新进度指示器"""
        self.display_startup_progress()
    
    def start_monitoring(self):
        """开始监控"""
        self.is_running = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if hasattr(self, 'monitoring_thread'):
            self.monitoring_thread.join(timeout=1)
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            self.update_progress_indicators()
            time.sleep(self.update_interval)


class SystemMetricsMonitor:
    """系统指标监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.update_interval = 2.0
        self.is_running = False
        
    def start_monitoring(self):
        """开始监控系统指标"""
        self.is_running = True
        self.metrics_thread = threading.Thread(target=self._metrics_loop, daemon=True)
        self.metrics_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if hasattr(self, 'metrics_thread'):
            self.metrics_thread.join(timeout=1)
    
    def _metrics_loop(self):
        """指标收集循环"""
        while self.is_running:
            self._collect_metrics()
            time.sleep(self.update_interval)
    
    def _collect_metrics(self):
        """收集系统指标"""
        try:
            import psutil
            
            # CPU使用率
            self.metrics['cpu_percent'] = psutil.cpu_percent(interval=None)
            
            # 内存使用
            memory = psutil.virtual_memory()
            self.metrics['memory_percent'] = memory.percent
            self.metrics['memory_used_gb'] = memory.used / (1024**3)
            self.metrics['memory_total_gb'] = memory.total / (1024**3)
            
            # 磁盘使用
            disk = psutil.disk_usage('/')
            self.metrics['disk_percent'] = (disk.used / disk.total) * 100
            self.metrics['disk_free_gb'] = disk.free / (1024**3)
            
            # 网络连接数
            self.metrics['connections'] = len(psutil.net_connections())
            
        except ImportError:
            # 如果psutil不可用，使用模拟数据
            import random
            self.metrics = {
                'cpu_percent': random.uniform(10, 30),
                'memory_percent': random.uniform(40, 60),
                'memory_used_gb': random.uniform(2, 4),
                'memory_total_gb': 8.0,
                'disk_percent': random.uniform(50, 70),
                'disk_free_gb': random.uniform(100, 200),
                'connections': random.randint(50, 150)
            }
    
    def show_system_metrics(self):
        """显示系统指标"""
        if not self.metrics:
            logger.info(f"{Colors.YELLOW}系统指标暂不可用{Colors.RESET}")
            return
        
        logger.info(f"\n{Colors.BOLD}{Colors.BLUE}系统指标:{Colors.RESET}")
        
        # CPU
        cpu = self.metrics.get('cpu_percent', 0)
        cpu_color = Colors.RED if cpu > 80 else Colors.YELLOW if cpu > 60 else Colors.GREEN
        logger.info(f"  🖥️  CPU: {cpu_color}{cpu:.1f}%{Colors.RESET}")
        
        # 内存
        mem_percent = self.metrics.get('memory_percent', 0)
        mem_used = self.metrics.get('memory_used_gb', 0)
        mem_total = self.metrics.get('memory_total_gb', 0)
        mem_color = Colors.RED if mem_percent > 85 else Colors.YELLOW if mem_percent > 70 else Colors.GREEN
        logger.info(f"  🧠 内存: {mem_color}{mem_percent:.1f}%{Colors.RESET} ({mem_used:.1f}/{mem_total:.1f}GB)")
        
        # 磁盘
        disk_percent = self.metrics.get('disk_percent', 0)
        disk_free = self.metrics.get('disk_free_gb', 0)
        disk_color = Colors.RED if disk_percent > 90 else Colors.YELLOW if disk_percent > 80 else Colors.GREEN
        logger.info(f"  💽 磁盘: {disk_color}{disk_percent:.1f}%{Colors.RESET} (剩余 {disk_free:.1f}GB)")
        
        # 网络连接
        connections = self.metrics.get('connections', 0)
        logger.info(f"  🌐 连接: {Colors.CYAN}{connections}{Colors.RESET}")


class ServiceStatusMonitor:
    """服务状态监控器"""
    
    def __init__(self):
        self.services = {}
        self.update_interval = 1.0
        self.is_running = False
    
    def register_service(self, name: str, check_function: Callable = None, port: int = None):
        """注册服务"""
        self.services[name] = {
            'check_function': check_function,
            'port': port,
            'status': 'unknown',
            'last_check': None,
            'error_message': None
        }
    
    def start_monitoring(self):
        """开始监控服务状态"""
        self.is_running = True
        self.service_thread = threading.Thread(target=self._service_loop, daemon=True)
        self.service_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if hasattr(self, 'service_thread'):
            self.service_thread.join(timeout=1)
    
    def _service_loop(self):
        """服务监控循环"""
        while self.is_running:
            self._check_services()
            time.sleep(self.update_interval)
    
    def _check_services(self):
        """检查服务状态"""
        for name, service in self.services.items():
            try:
                if service['check_function']:
                    # 使用自定义检查函数
                    service['status'] = service['check_function']()
                elif service['port']:
                    # 检查端口是否开放
                    service['status'] = self._check_port(service['port'])
                else:
                    service['status'] = 'unknown'
                
                service['last_check'] = datetime.now()
                service['error_message'] = None
                
            except Exception as e:
                service['status'] = 'error'
                service['error_message'] = str(e)
    
    def _check_port(self, port: int) -> str:
        """检查端口是否开放"""
        import socket
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            return 'running' if result == 0 else 'stopped'
        except:
            return 'error'
    
    def display_service_status(self):
        """显示服务状态"""
        if not self.services:
            return
        
        logger.info(f"\n{Colors.BOLD}{Colors.MAGENTA}服务状态:{Colors.RESET}")
        
        for name, service in self.services.items():
            status = service['status']
            
            # 状态颜色和图标
            if status == 'running':
                icon = "🟢"
                color = Colors.GREEN
                status_text = "运行中"
            elif status == 'stopped':
                icon = "🔴"
                color = Colors.RED
                status_text = "已停止"
            elif status == 'error':
                icon = "⚠️ "
                color = Colors.YELLOW
                status_text = "错误"
            else:
                icon = "⚪"
                color = Colors.WHITE
                status_text = "未知"
            
            service_text = f"  {icon} {name}: {color}{status_text}{Colors.RESET}"
            
            # 显示端口信息
            if service['port']:
                service_text += f" (端口: {service['port']})"
            
            # 显示错误信息
            if service['error_message']:
                service_text += f"\n    {Colors.RED}错误: {service['error_message']}{Colors.RESET}"
            
            logger.info(service_text)


class UnifiedStartupMonitor:
    """统一启动监控器"""
    
    def __init__(self):
        self.startup_monitor = StartupMonitor()
        self.metrics_monitor = SystemMetricsMonitor()
        self.service_monitor = ServiceStatusMonitor()
        self.is_running = False
        
        # 注册常见服务
        self._register_default_services()
    
    def _register_default_services(self):
        """注册默认服务"""
        self.service_monitor.register_service("后端API", port=8000)
        self.service_monitor.register_service("前端开发服务器", port=5173)
        self.service_monitor.register_service("数据库", port=5432)
    
    def start_monitoring(self):
        """开始全面监控"""
        self.is_running = True
        self.startup_monitor.start_monitoring()
        self.metrics_monitor.start_monitoring()
        self.service_monitor.start_monitoring()
        
        logger.info(f"{Colors.GREEN}🚀 启动监控系统已启动{Colors.RESET}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        self.startup_monitor.stop_monitoring()
        self.metrics_monitor.stop_monitoring()
        self.service_monitor.stop_monitoring()
        
        logger.info(f"{Colors.YELLOW}🛑 启动监控系统已停止{Colors.RESET}")
    
    def display_complete_status(self):
        """显示完整状态"""
        self.startup_monitor.display_startup_progress()
        self.service_monitor.display_service_status()
        self.metrics_monitor.show_system_metrics()
    
    def run_startup_sequence(self, steps_with_functions: List[tuple]):
        """运行启动序列"""
        """
        steps_with_functions: [(step_name, function, args), ...]
        """
        self.start_monitoring()
        
        try:
            for i, (step_name, func, args) in enumerate(steps_with_functions):
                if i < len(self.startup_monitor.steps):
                    self.startup_monitor.current_step_index = i
                    self.startup_monitor.start_step(i)
                
                try:
                    # 执行步骤函数
                    if args:
                        result = func(*args)
                    else:
                        result = func()
                    
                    # 模拟进度更新
                    for progress in range(0, 101, 25):
                        self.startup_monitor.update_step_progress(progress)
                        time.sleep(0.1)
                    
                    self.startup_monitor.complete_step(success=True)
                    
                except Exception as e:
                    self.startup_monitor.complete_step(success=False, error_message=str(e))
                    break
        
        finally:
            time.sleep(2)  # 显示最终状态
            self.stop_monitoring()


# 导出主要类
__all__ = [
    'StartupMonitor', 'SystemMetricsMonitor', 'ServiceStatusMonitor',
    'UnifiedStartupMonitor', 'StartupStatus', 'StartupStep', 'Colors'
]