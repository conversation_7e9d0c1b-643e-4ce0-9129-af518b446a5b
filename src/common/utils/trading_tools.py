#!/usr/bin/env python3
"""
量化交易数据处理工具
专门用于金融数据和交易相关的数据处理
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import warnings

from src.common.utils.logging import get_logger
from src.common.utils.tools import DataTools, DateTimeTools


@dataclass
class MarketDataInfo:
    """市场数据信息"""
    symbol: str
    start_date: str
    end_date: str
    total_records: int
    missing_dates: List[str]
    data_quality_score: float  # 0-1之间，1表示完美
    columns: List[str]


class TradingDataTools:
    """交易数据处理工具类"""
    
    @staticmethod
    def validate_ohlcv_data(df: pd.DataFrame) -> Dict[str, Any]:
        """验证OHLCV数据质量"""
        logger = get_logger("trading_data_tools")
        
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        issues = []
        
        # 检查必需列
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            issues.append(f"缺失列: {missing_columns}")
        
        if not missing_columns:
            # 检查数据类型
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                if not pd.api.types.is_numeric_dtype(df[col]):
                    issues.append(f"列 {col} 不是数值类型")
            
            # 检查OHLC逻辑
            invalid_ohlc = df[(df['high'] < df['low']) | 
                             (df['high'] < df['open']) |
                             (df['high'] < df['close']) |
                             (df['low'] > df['open']) |
                             (df['low'] > df['close'])]
            
            if not invalid_ohlc.empty:
                issues.append(f"发现 {len(invalid_ohlc)} 条无效的OHLC数据")
            
            # 检查负值
            negative_values = df[(df['open'] <= 0) | (df['high'] <= 0) | 
                               (df['low'] <= 0) | (df['close'] <= 0)].index
            if len(negative_values) > 0:
                issues.append(f"发现 {len(negative_values)} 条负值或零值价格数据")
            
            # 检查缺失值
            null_counts = df[numeric_columns].isnull().sum()
            if null_counts.sum() > 0:
                issues.append(f"缺失值统计: {null_counts.to_dict()}")
        
        # 计算数据质量评分
        quality_score = max(0, 1 - len(issues) * 0.2)
        
        return {
            "is_valid": len(issues) == 0,
            "issues": issues,
            "quality_score": quality_score,
            "total_records": len(df),
            "date_range": {
                "start": str(df.index.min()) if not df.empty else None,
                "end": str(df.index.max()) if not df.empty else None
            }
        }
    
    @staticmethod
    def clean_market_data(df: pd.DataFrame, 
                         fill_method: str = "forward",
                         remove_outliers: bool = True,
                         outlier_threshold: float = 3.0) -> pd.DataFrame:
        """清洗市场数据"""
        logger = get_logger("trading_data_tools")
        df_clean = df.copy()
        
        # 移除重复的索引
        if df_clean.index.duplicated().any():
            logger.warning("发现重复的时间索引，保留最后一个")
            df_clean = df_clean[~df_clean.index.duplicated(keep='last')]
        
        # 排序
        df_clean = df_clean.sort_index()
        
        # 处理缺失值
        if fill_method == "forward":
            df_clean = df_clean.fillna(method='ffill')
        elif fill_method == "backward":
            df_clean = df_clean.fillna(method='bfill')
        elif fill_method == "interpolate":
            df_clean = df_clean.interpolate(method='linear')
        elif fill_method == "drop":
            df_clean = df_clean.dropna()
        
        # 移除异常值
        if remove_outliers and not df_clean.empty:
            numeric_columns = df_clean.select_dtypes(include=[np.number]).columns
            
            for col in numeric_columns:
                if col in df_clean.columns:
                    # 使用Z-score方法检测异常值
                    z_scores = np.abs((df_clean[col] - df_clean[col].mean()) / df_clean[col].std())
                    outliers = z_scores > outlier_threshold
                    
                    if outliers.any():
                        logger.warning(f"列 {col} 发现 {outliers.sum()} 个异常值")
                        # 用前后值的平均值替换异常值
                        df_clean.loc[outliers, col] = df_clean[col].rolling(window=3, center=True).mean()
        
        return df_clean
    
    @staticmethod
    def resample_data(df: pd.DataFrame, 
                     frequency: str = "1D",
                     method: str = "ohlc") -> pd.DataFrame:
        """重采样数据到指定频率"""
        if df.empty:
            return df
        
        if method == "ohlc" and all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            # OHLC重采样
            resampled = pd.DataFrame()
            resampled['open'] = df['open'].resample(frequency).first()
            resampled['high'] = df['high'].resample(frequency).max()
            resampled['low'] = df['low'].resample(frequency).min()
            resampled['close'] = df['close'].resample(frequency).last()
            
            if 'volume' in df.columns:
                resampled['volume'] = df['volume'].resample(frequency).sum()
                
        elif method == "mean":
            resampled = df.resample(frequency).mean()
        elif method == "sum":
            resampled = df.resample(frequency).sum()
        elif method == "last":
            resampled = df.resample(frequency).last()
        else:
            resampled = df.resample(frequency).first()
        
        return resampled.dropna()
    
    @staticmethod
    def calculate_returns(df: pd.DataFrame, 
                         price_column: str = "close",
                         method: str = "simple") -> pd.Series:
        """计算收益率"""
        if price_column not in df.columns:
            raise ValueError(f"列 {price_column} 不存在")
        
        prices = df[price_column]
        
        if method == "simple":
            returns = prices.pct_change()
        elif method == "log":
            returns = np.log(prices / prices.shift(1))
        else:
            raise ValueError(f"不支持的收益率计算方法: {method}")
        
        return returns.fillna(0)
    
    @staticmethod
    def detect_missing_trading_days(df: pd.DataFrame, 
                                   start_date: str = None,
                                   end_date: str = None) -> List[str]:
        """检测缺失的交易日"""
        if df.empty:
            return []
        
        if start_date is None:
            start_date = df.index.min().strftime("%Y-%m-%d")
        if end_date is None:
            end_date = df.index.max().strftime("%Y-%m-%d")
        
        # 获取期望的交易日
        expected_dates = DateTimeTools.get_trading_days(start_date, end_date)
        
        # 转换为datetime索引
        expected_dates = pd.to_datetime(expected_dates)
        actual_dates = pd.to_datetime(df.index.date)
        
        # 找出缺失的日期
        missing_dates = expected_dates.difference(actual_dates)
        
        return [date.strftime("%Y-%m-%d") for date in missing_dates]
    
    @staticmethod
    def align_multiple_series(data_dict: Dict[str, pd.Series], 
                            method: str = "inner") -> pd.DataFrame:
        """对齐多个时间序列"""
        if not data_dict:
            return pd.DataFrame()
        
        # 创建DataFrame
        df = pd.DataFrame(data_dict)
        
        if method == "inner":
            # 只保留所有序列都有数据的日期
            df = df.dropna()
        elif method == "outer":
            # 保留所有日期
            pass  # DataFrame构造时已经是outer join
        elif method == "forward_fill":
            # 前向填充
            df = df.fillna(method='ffill')
        elif method == "backward_fill":
            # 后向填充
            df = df.fillna(method='bfill')
        
        return df


class TechnicalIndicatorTools:
    """技术指标工具类"""
    
    @staticmethod
    def moving_average(series: pd.Series, window: int, method: str = "simple") -> pd.Series:
        """移动平均线"""
        if method == "simple" or method == "sma":
            return series.rolling(window=window).mean()
        elif method == "exponential" or method == "ema":
            return series.ewm(span=window).mean()
        elif method == "weighted" or method == "wma":
            weights = np.arange(1, window + 1)
            return series.rolling(window).apply(lambda x: np.dot(x, weights) / weights.sum(), raw=True)
        else:
            raise ValueError(f"不支持的移动平均方法: {method}")
    
    @staticmethod
    def rsi(series: pd.Series, window: int = 14) -> pd.Series:
        """相对强弱指标"""
        delta = series.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
    
    @staticmethod
    def bollinger_bands(series: pd.Series, 
                       window: int = 20, 
                       num_std: float = 2) -> Dict[str, pd.Series]:
        """布林带"""
        sma = series.rolling(window=window).mean()
        std = series.rolling(window=window).std()
        
        return {
            "middle": sma,
            "upper": sma + (std * num_std),
            "lower": sma - (std * num_std),
            "bandwidth": (std * num_std * 2) / sma,
            "percent_b": (series - (sma - std * num_std)) / (std * num_std * 2)
        }
    
    @staticmethod
    def macd(series: pd.Series, 
            fast: int = 12, 
            slow: int = 26, 
            signal: int = 9) -> Dict[str, pd.Series]:
        """MACD指标"""
        ema_fast = series.ewm(span=fast).mean()
        ema_slow = series.ewm(span=slow).mean()
        
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        
        return {
            "macd": macd_line,
            "signal": signal_line,
            "histogram": histogram
        }
    
    @staticmethod
    def stochastic_oscillator(high: pd.Series, 
                             low: pd.Series, 
                             close: pd.Series,
                             window: int = 14,
                             smooth_k: int = 3,
                             smooth_d: int = 3) -> Dict[str, pd.Series]:
        """随机振荡器"""
        lowest_low = low.rolling(window=window).min()
        highest_high = high.rolling(window=window).max()
        
        k_percent = 100 * (close - lowest_low) / (highest_high - lowest_low)
        k_smooth = k_percent.rolling(window=smooth_k).mean()
        d_smooth = k_smooth.rolling(window=smooth_d).mean()
        
        return {
            "k": k_smooth,
            "d": d_smooth
        }


class PortfolioAnalysisTools:
    """投资组合分析工具"""
    
    @staticmethod
    def calculate_portfolio_returns(weights: Dict[str, float],
                                  returns: pd.DataFrame) -> pd.Series:
        """计算投资组合收益"""
        # 确保权重标准化
        total_weight = sum(weights.values())
        normalized_weights = {k: v/total_weight for k, v in weights.items()}
        
        # 只使用在returns中存在的资产
        available_assets = [asset for asset in normalized_weights.keys() if asset in returns.columns]
        
        if not available_assets:
            return pd.Series(dtype=float)
        
        # 计算加权收益
        portfolio_returns = pd.Series(0.0, index=returns.index)
        
        for asset in available_assets:
            portfolio_returns += returns[asset] * normalized_weights[asset]
        
        return portfolio_returns
    
    @staticmethod
    def calculate_risk_metrics(returns: pd.Series, 
                             risk_free_rate: float = 0.02) -> Dict[str, float]:
        """计算风险指标"""
        if returns.empty:
            return {}
        
        returns_clean = returns.dropna()
        
        metrics = {}
        
        # 基本统计
        metrics["total_return"] = (1 + returns_clean).prod() - 1
        metrics["annualized_return"] = (1 + returns_clean.mean()) ** 252 - 1
        metrics["volatility"] = returns_clean.std() * np.sqrt(252)
        
        # 风险调整收益
        excess_returns = returns_clean.mean() - risk_free_rate/252
        if returns_clean.std() > 0:
            metrics["sharpe_ratio"] = excess_returns / returns_clean.std() * np.sqrt(252)
        else:
            metrics["sharpe_ratio"] = 0.0
        
        # 最大回撤
        cumulative_returns = (1 + returns_clean).cumprod()
        running_max = cumulative_returns.cummax()
        drawdown = (cumulative_returns - running_max) / running_max
        metrics["max_drawdown"] = drawdown.min()
        
        # VaR和CVaR（5%置信水平）
        metrics["var_5"] = returns_clean.quantile(0.05)
        metrics["cvar_5"] = returns_clean[returns_clean <= metrics["var_5"]].mean()
        
        # Calmar比率
        if metrics["max_drawdown"] != 0:
            metrics["calmar_ratio"] = metrics["annualized_return"] / abs(metrics["max_drawdown"])
        else:
            metrics["calmar_ratio"] = np.inf
        
        # 其他指标
        if len(returns_clean) > 1:
            metrics["skewness"] = returns_clean.skew()
            metrics["kurtosis"] = returns_clean.kurtosis()
        
        # 正收益天数比例
        metrics["win_rate"] = (returns_clean > 0).mean()
        
        return metrics
    
    @staticmethod
    def efficient_frontier_point(returns: pd.DataFrame, 
                               target_return: float = None,
                               target_risk: float = None) -> Dict[str, Any]:
        """计算有效前沿上的点（简化实现）"""
        try:
            from scipy.optimize import minimize
        except ImportError:
            return {"error": "需要安装scipy库"}
        
        returns_clean = returns.dropna()
        if returns_clean.empty:
            return {"error": "没有有效的收益数据"}
        
        mean_returns = returns_clean.mean()
        cov_matrix = returns_clean.cov()
        num_assets = len(mean_returns)
        
        # 约束条件：权重和为1
        constraints = [{"type": "eq", "fun": lambda x: np.sum(x) - 1}]
        
        # 目标函数：最小化风险
        def objective(weights):
            return np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))
        
        # 如果指定了目标收益率
        if target_return is not None:
            constraints.append({
                "type": "eq", 
                "fun": lambda x: np.dot(x, mean_returns) - target_return
            })
        
        # 边界：权重在0-1之间
        bounds = tuple((0, 1) for _ in range(num_assets))
        
        # 初始权重：等权重
        initial_weights = np.array([1/num_assets] * num_assets)
        
        # 优化
        result = minimize(objective, initial_weights, 
                         method='SLSQP', bounds=bounds, 
                         constraints=constraints)
        
        if result.success:
            optimal_weights = dict(zip(returns_clean.columns, result.x))
            portfolio_return = np.dot(result.x, mean_returns)
            portfolio_risk = objective(result.x)
            
            return {
                "weights": optimal_weights,
                "expected_return": portfolio_return,
                "risk": portfolio_risk,
                "sharpe_ratio": portfolio_return / portfolio_risk if portfolio_risk > 0 else 0
            }
        else:
            return {"error": "优化失败"}


class DataQualityTools:
    """数据质量工具"""
    
    @staticmethod
    def generate_data_quality_report(df: pd.DataFrame, 
                                   symbol: str = "UNKNOWN") -> MarketDataInfo:
        """生成数据质量报告"""
        if df.empty:
            return MarketDataInfo(
                symbol=symbol,
                start_date="",
                end_date="", 
                total_records=0,
                missing_dates=[],
                data_quality_score=0.0,
                columns=[]
            )
        
        # 基本信息
        start_date = df.index.min().strftime("%Y-%m-%d")
        end_date = df.index.max().strftime("%Y-%m-%d")
        total_records = len(df)
        columns = df.columns.tolist()
        
        # 检测缺失的交易日
        missing_dates = TradingDataTools.detect_missing_trading_days(df, start_date, end_date)
        
        # 计算数据质量评分
        quality_issues = 0
        
        # 缺失值检查
        null_ratio = df.isnull().sum().sum() / (len(df) * len(df.columns))
        if null_ratio > 0.05:  # 超过5%缺失值
            quality_issues += 1
        
        # 重复值检查
        if df.index.duplicated().any():
            quality_issues += 1
        
        # 缺失交易日检查
        if len(missing_dates) > len(df) * 0.1:  # 缺失超过10%的交易日
            quality_issues += 1
        
        # OHLCV数据逻辑检查（如果适用）
        if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            validation_result = TradingDataTools.validate_ohlcv_data(df)
            if not validation_result["is_valid"]:
                quality_issues += len(validation_result["issues"])
        
        # 计算质量评分（0-1之间）
        quality_score = max(0, 1 - quality_issues * 0.2)
        
        return MarketDataInfo(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            total_records=total_records,
            missing_dates=missing_dates,
            data_quality_score=quality_score,
            columns=columns
        )
    
    @staticmethod
    def compare_data_sources(data1: pd.DataFrame, 
                           data2: pd.DataFrame,
                           source1_name: str = "Source1",
                           source2_name: str = "Source2") -> Dict[str, Any]:
        """比较两个数据源的质量"""
        comparison = {
            "summary": {},
            "differences": {},
            "recommendations": []
        }
        
        # 基本统计比较
        comparison["summary"][source1_name] = {
            "records": len(data1),
            "date_range": f"{data1.index.min()} to {data1.index.max()}" if not data1.empty else "Empty",
            "columns": data1.columns.tolist()
        }
        
        comparison["summary"][source2_name] = {
            "records": len(data2),
            "date_range": f"{data2.index.min()} to {data2.index.max()}" if not data2.empty else "Empty",
            "columns": data2.columns.tolist()
        }
        
        # 找出共同的时间范围和列
        if not data1.empty and not data2.empty:
            common_dates = data1.index.intersection(data2.index)
            common_columns = set(data1.columns).intersection(set(data2.columns))
            
            if len(common_dates) > 0 and len(common_columns) > 0:
                # 计算差异
                data1_common = data1.loc[common_dates, list(common_columns)]
                data2_common = data2.loc[common_dates, list(common_columns)]
                
                differences = data1_common - data2_common
                
                comparison["differences"] = {
                    "common_dates": len(common_dates),
                    "common_columns": list(common_columns),
                    "mean_absolute_difference": differences.abs().mean().to_dict(),
                    "max_difference": differences.abs().max().to_dict(),
                    "correlation": data1_common.corrwith(data2_common).to_dict()
                }
                
                # 生成建议
                for col in common_columns:
                    corr = comparison["differences"]["correlation"].get(col, 0)
                    if corr < 0.95:
                        comparison["recommendations"].append(
                            f"列 {col} 在两个数据源间相关性较低 ({corr:.3f})，建议检查数据质量"
                        )
        
        return comparison


# 导出主要类
__all__ = [
    'TradingDataTools', 'TechnicalIndicatorTools', 'PortfolioAnalysisTools',
    'DataQualityTools', 'MarketDataInfo'
]