"""
Real-time risk monitoring system for comprehensive risk oversight.

This module implements a centralized risk monitoring system that provides:
- Real-time risk indicator monitoring
- Risk threshold alert mechanisms
- Risk event logging and audit functionality
- Risk control decision recording
"""

import uuid
import json
import sqlite3
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Set
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import pandas as pd
import numpy as np
from collections import defaultdict, deque

from .models import RiskConfig, RiskViolation, RiskLevel, RiskMetrics
from .manager import RiskManager
from src.market.strategies.models.portfolio import Portfolio


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """Alert status."""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"


class MonitoringStatus(Enum):
    """Monitoring system status."""
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class RiskAlert:
    """Risk alert data structure."""
    
    id: str
    timestamp: datetime
    severity: AlertSeverity
    status: AlertStatus
    title: str
    message: str
    source: str  # Which monitor/rule generated this
    symbol: Optional[str] = None
    strategy_id: Optional[str] = None
    violation: Optional[RiskViolation] = None
    threshold_value: Optional[float] = None
    current_value: Optional[float] = None
    metadata: Dict[str, Any] = None
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['severity'] = self.severity.value
        data['status'] = self.status.value
        
        if self.acknowledged_at:
            data['acknowledged_at'] = self.acknowledged_at.isoformat()
        if self.resolved_at:
            data['resolved_at'] = self.resolved_at.isoformat()
        if self.violation:
            data['violation'] = self.violation.to_dict()
        
        return data


@dataclass
class RiskThreshold:
    """Risk threshold configuration."""
    
    name: str
    metric_name: str
    warning_threshold: float
    critical_threshold: float
    comparison_operator: str  # 'gt', 'lt', 'eq', 'gte', 'lte'
    enabled: bool = True
    cooldown_minutes: int = 5  # Minimum time between alerts
    description: str = ""
    
    def check_threshold(self, current_value: float) -> Optional[AlertSeverity]:
        """Check if current value breaches threshold."""
        if not self.enabled:
            return None
        
        def compare(val1: float, val2: float, op: str) -> bool:
            if op == 'gt':
                return val1 > val2
            elif op == 'lt':
                return val1 < val2
            elif op == 'gte':
                return val1 >= val2
            elif op == 'lte':
                return val1 <= val2
            elif op == 'eq':
                return abs(val1 - val2) < 1e-6
            return False
        
        if compare(current_value, self.critical_threshold, self.comparison_operator):
            return AlertSeverity.CRITICAL
        elif compare(current_value, self.warning_threshold, self.comparison_operator):
            return AlertSeverity.WARNING
        
        return None


@dataclass
class RiskEvent:
    """Risk event for audit logging."""
    
    id: str
    timestamp: datetime
    event_type: str
    source: str
    description: str
    severity: AlertSeverity
    symbol: Optional[str] = None
    strategy_id: Optional[str] = None
    user_id: Optional[str] = None
    portfolio_value: Optional[float] = None
    risk_metrics: Optional[Dict[str, Any]] = None
    decision_taken: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['severity'] = self.severity.value
        return data


class RiskMonitoringSystem:
    """
    Comprehensive real-time risk monitoring system.
    
    Provides centralized monitoring, alerting, and audit logging
    for all risk-related activities in the trading system.
    """
    
    def __init__(self, 
                 risk_manager: RiskManager,
                 config: Optional[RiskConfig] = None,
                 db_path: str = "data/risk_monitoring.db"):
        """
        Initialize risk monitoring system.
        
        Args:
            risk_manager: Risk manager instance
            config: Risk configuration
            db_path: Database path for persistent storage
        """
        self.risk_manager = risk_manager
        self.config = config or RiskConfig()
        self.db_path = db_path
        self.logger = logging.getLogger(f"{__name__}.RiskMonitoringSystem")
        
        # System status
        self.status = MonitoringStatus.STOPPED
        self.start_time: Optional[datetime] = None
        self.last_update: Optional[datetime] = None
        
        # Monitoring data
        self.active_alerts: Dict[str, RiskAlert] = {}
        self.alert_history: deque = deque(maxlen=10000)
        self.event_history: deque = deque(maxlen=10000)
        self.risk_metrics_history: deque = deque(maxlen=1000)
        
        # Thresholds and configuration
        self.thresholds: Dict[str, RiskThreshold] = {}
        self.alert_cooldowns: Dict[str, datetime] = {}
        self.suppressed_alerts: Set[str] = set()
        
        # Callbacks and subscribers
        self.alert_callbacks: List[Callable[[RiskAlert], None]] = []
        self.event_callbacks: List[Callable[[RiskEvent], None]] = []
        
        # Threading
        self._monitoring_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._lock = threading.RLock()
        
        # Statistics
        self.stats = {
            'alerts_generated': 0,
            'events_logged': 0,
            'violations_detected': 0,
            'decisions_recorded': 0,
            'uptime_seconds': 0
        }
        
        # Initialize components
        self._initialize_database()
        self._initialize_default_thresholds()
        self._setup_risk_manager_callbacks()
        
        self.logger.info("Risk monitoring system initialized")
    
    def _initialize_database(self) -> None:
        """Initialize SQLite database for persistent storage."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create alerts table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS risk_alerts (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    status TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    source TEXT NOT NULL,
                    symbol TEXT,
                    strategy_id TEXT,
                    threshold_value REAL,
                    current_value REAL,
                    metadata TEXT,
                    acknowledged_by TEXT,
                    acknowledged_at TEXT,
                    resolved_at TEXT
                )
            ''')
            
            # Create events table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS risk_events (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    source TEXT NOT NULL,
                    description TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    symbol TEXT,
                    strategy_id TEXT,
                    user_id TEXT,
                    portfolio_value REAL,
                    risk_metrics TEXT,
                    decision_taken TEXT,
                    metadata TEXT
                )
            ''')
            
            # Create risk metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS risk_metrics_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    portfolio_value REAL NOT NULL,
                    metrics_data TEXT NOT NULL
                )
            ''')
            
            # Create indices
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_alerts_timestamp ON risk_alerts(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_alerts_severity ON risk_alerts(severity)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_events_timestamp ON risk_events(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_events_type ON risk_events(event_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON risk_metrics_log(timestamp)')
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"Database initialized at {self.db_path}")
            
        except Exception as e:
            self.logger.error(f"Error initializing database: {e}")
            raise
    
    def _initialize_default_thresholds(self) -> None:
        """Initialize default risk thresholds."""
        default_thresholds = [
            RiskThreshold(
                name="Portfolio Drawdown",
                metric_name="current_drawdown_pct",
                warning_threshold=10.0,  # 10%
                critical_threshold=15.0,  # 15%
                comparison_operator="gt",
                description="Portfolio drawdown from peak"
            ),
            RiskThreshold(
                name="Daily Loss Limit",
                metric_name="daily_loss_pct",
                warning_threshold=3.0,  # 3%
                critical_threshold=5.0,  # 5%
                comparison_operator="gt",
                description="Daily portfolio loss percentage"
            ),
            RiskThreshold(
                name="Position Concentration",
                metric_name="largest_position_pct",
                warning_threshold=15.0,  # 15%
                critical_threshold=25.0,  # 25%
                comparison_operator="gt",
                description="Largest single position percentage"
            ),
            RiskThreshold(
                name="Leverage Ratio",
                metric_name="gross_leverage",
                warning_threshold=1.5,
                critical_threshold=2.0,
                comparison_operator="gt",
                description="Portfolio gross leverage ratio"
            ),
            RiskThreshold(
                name="VaR 1-Day",
                metric_name="portfolio_var_1d",
                warning_threshold=50000.0,  # $50k
                critical_threshold=100000.0,  # $100k
                comparison_operator="gt",
                description="1-day Value at Risk"
            ),
            RiskThreshold(
                name="Risk Budget Utilization",
                metric_name="daily_risk_used_pct",
                warning_threshold=80.0,  # 80%
                critical_threshold=95.0,  # 95%
                comparison_operator="gt",
                description="Daily risk budget utilization"
            ),
            RiskThreshold(
                name="Cash Level",
                metric_name="cash_pct",
                warning_threshold=5.0,  # 5%
                critical_threshold=2.0,  # 2%
                comparison_operator="lt",
                description="Available cash percentage"
            )
        ]
        
        for threshold in default_thresholds:
            self.thresholds[threshold.name] = threshold
        
        self.logger.info(f"Initialized {len(default_thresholds)} default thresholds")
    
    def _setup_risk_manager_callbacks(self) -> None:
        """Setup callbacks with risk manager."""
        self.risk_manager.add_alert_callback(self._handle_risk_violation)
    
    def start_monitoring(self) -> bool:
        """Start the risk monitoring system."""
        try:
            if self.status == MonitoringStatus.RUNNING:
                self.logger.warning("Monitoring system is already running")
                return True
            
            self._stop_event.clear()
            self.status = MonitoringStatus.RUNNING
            self.start_time = datetime.now()
            
            # Start monitoring thread
            self._monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                name="RiskMonitoringThread",
                daemon=True
            )
            self._monitoring_thread.start()
            
            # Log system start event
            self._log_event(
                event_type="system_start",
                source="monitoring_system",
                description="Risk monitoring system started",
                severity=AlertSeverity.INFO
            )
            
            self.logger.info("Risk monitoring system started")
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting monitoring system: {e}")
            self.status = MonitoringStatus.ERROR
            return False
    
    def stop_monitoring(self) -> bool:
        """Stop the risk monitoring system."""
        try:
            if self.status == MonitoringStatus.STOPPED:
                return True
            
            self._stop_event.set()
            self.status = MonitoringStatus.STOPPED
            
            # Wait for monitoring thread to finish
            if self._monitoring_thread and self._monitoring_thread.is_alive():
                self._monitoring_thread.join(timeout=5.0)
            
            # Log system stop event
            self._log_event(
                event_type="system_stop",
                source="monitoring_system",
                description="Risk monitoring system stopped",
                severity=AlertSeverity.INFO
            )
            
            self.logger.info("Risk monitoring system stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Error stopping monitoring system: {e}")
            return False
    
    def pause_monitoring(self) -> bool:
        """Pause the risk monitoring system."""
        if self.status == MonitoringStatus.RUNNING:
            self.status = MonitoringStatus.PAUSED
            self.logger.info("Risk monitoring system paused")
            return True
        return False
    
    def resume_monitoring(self) -> bool:
        """Resume the risk monitoring system."""
        if self.status == MonitoringStatus.PAUSED:
            self.status = MonitoringStatus.RUNNING
            self.logger.info("Risk monitoring system resumed")
            return True
        return False
    
    def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        self.logger.info("Risk monitoring loop started")
        
        while not self._stop_event.is_set():
            try:
                if self.status == MonitoringStatus.RUNNING:
                    self._perform_monitoring_cycle()
                    self.last_update = datetime.now()
                    
                    # Update uptime statistics
                    if self.start_time:
                        self.stats['uptime_seconds'] = (datetime.now() - self.start_time).total_seconds()
                
                # Sleep for monitoring interval (1 second)
                self._stop_event.wait(1.0)
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                self.status = MonitoringStatus.ERROR
                self._stop_event.wait(5.0)  # Wait longer on error
        
        self.logger.info("Risk monitoring loop stopped")
    
    def _perform_monitoring_cycle(self) -> None:
        """Perform one monitoring cycle."""
        try:
            # This would be called with actual portfolio data in real implementation
            # For now, we'll check if there are any recent risk metrics to monitor
            if hasattr(self.risk_manager, 'risk_metrics_history') and self.risk_manager.risk_metrics_history:
                latest_metrics = self.risk_manager.risk_metrics_history[-1]
                self._check_risk_thresholds(latest_metrics)
                
                # Store metrics in history
                with self._lock:
                    self.risk_metrics_history.append({
                        'timestamp': datetime.now(),
                        'metrics': latest_metrics
                    })
                
                # Persist metrics to database
                self._persist_risk_metrics(latest_metrics)
            
            # Clean up old alerts and events
            self._cleanup_old_data()
            
        except Exception as e:
            self.logger.error(f"Error in monitoring cycle: {e}")
    
    def _check_risk_thresholds(self, metrics: RiskMetrics) -> None:
        """Check risk metrics against configured thresholds."""
        try:
            metrics_dict = metrics.to_dict()
            
            # Add calculated metrics
            cash_pct = (metrics.cash / metrics.portfolio_value * 100) if metrics.portfolio_value > 0 else 0
            metrics_dict['cash_pct'] = cash_pct
            
            # Check each threshold
            for threshold_name, threshold in self.thresholds.items():
                if not threshold.enabled:
                    continue
                
                # Skip if in cooldown
                if threshold_name in self.alert_cooldowns:
                    if datetime.now() < self.alert_cooldowns[threshold_name]:
                        continue
                
                # Skip if suppressed
                if threshold_name in self.suppressed_alerts:
                    continue
                
                # Get metric value
                metric_value = metrics_dict.get(threshold.metric_name)
                if metric_value is None:
                    continue
                
                # Check threshold
                severity = threshold.check_threshold(metric_value)
                if severity:
                    self._generate_threshold_alert(threshold, metric_value, severity, metrics)
        
        except Exception as e:
            self.logger.error(f"Error checking risk thresholds: {e}")
    
    def _generate_threshold_alert(self, 
                                threshold: RiskThreshold,
                                current_value: float,
                                severity: AlertSeverity,
                                metrics: RiskMetrics) -> None:
        """Generate alert for threshold breach."""
        try:
            alert_id = str(uuid.uuid4())
            
            # Determine threshold value based on severity
            threshold_value = (threshold.critical_threshold 
                             if severity == AlertSeverity.CRITICAL 
                             else threshold.warning_threshold)
            
            alert = RiskAlert(
                id=alert_id,
                timestamp=datetime.now(),
                severity=severity,
                status=AlertStatus.ACTIVE,
                title=f"{threshold.name} Threshold Breached",
                message=f"{threshold.description}: {current_value:.2f} exceeds {threshold_value:.2f}",
                source="threshold_monitor",
                threshold_value=threshold_value,
                current_value=current_value,
                metadata={
                    'threshold_name': threshold.name,
                    'metric_name': threshold.metric_name,
                    'comparison_operator': threshold.comparison_operator,
                    'portfolio_value': metrics.portfolio_value
                }
            )
            
            self._process_alert(alert)
            
            # Set cooldown
            cooldown_time = datetime.now() + timedelta(minutes=threshold.cooldown_minutes)
            self.alert_cooldowns[threshold.name] = cooldown_time
            
        except Exception as e:
            self.logger.error(f"Error generating threshold alert: {e}")
    
    def _handle_risk_violation(self, violation: RiskViolation) -> None:
        """Handle risk violation from risk manager."""
        try:
            alert_id = str(uuid.uuid4())
            
            # Map risk level to alert severity
            severity_mapping = {
                RiskLevel.LOW: AlertSeverity.INFO,
                RiskLevel.MEDIUM: AlertSeverity.WARNING,
                RiskLevel.HIGH: AlertSeverity.ERROR,
                RiskLevel.CRITICAL: AlertSeverity.CRITICAL
            }
            
            severity = severity_mapping.get(violation.severity, AlertSeverity.WARNING)
            
            alert = RiskAlert(
                id=alert_id,
                timestamp=datetime.now(),
                severity=severity,
                status=AlertStatus.ACTIVE,
                title=f"Risk Violation: {violation.rule_name}",
                message=f"{violation.violation_type}: {violation.current_value:.4f} exceeds limit {violation.limit_value:.4f}",
                source="risk_manager",
                symbol=violation.symbol,
                strategy_id=violation.strategy_id,
                violation=violation,
                threshold_value=violation.limit_value,
                current_value=violation.current_value,
                metadata=violation.details or {}
            )
            
            self._process_alert(alert)
            self.stats['violations_detected'] += 1
            
        except Exception as e:
            self.logger.error(f"Error handling risk violation: {e}")
    
    def _process_alert(self, alert: RiskAlert) -> None:
        """Process and store alert."""
        try:
            with self._lock:
                # Store in active alerts
                self.active_alerts[alert.id] = alert
                
                # Add to history
                self.alert_history.append(alert)
                
                # Update statistics
                self.stats['alerts_generated'] += 1
            
            # Persist to database
            self._persist_alert(alert)
            
            # Log as event
            self._log_event(
                event_type="alert_generated",
                source=alert.source,
                description=f"Alert generated: {alert.title}",
                severity=alert.severity,
                symbol=alert.symbol,
                strategy_id=alert.strategy_id,
                metadata={'alert_id': alert.id}
            )
            
            # Trigger callbacks
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    self.logger.error(f"Error in alert callback: {e}")
            
            self.logger.warning(f"Risk alert generated: {alert.title} ({alert.severity.value})")
            
        except Exception as e:
            self.logger.error(f"Error processing alert: {e}")
    
    def _log_event(self, 
                  event_type: str,
                  source: str,
                  description: str,
                  severity: AlertSeverity,
                  symbol: Optional[str] = None,
                  strategy_id: Optional[str] = None,
                  user_id: Optional[str] = None,
                  portfolio_value: Optional[float] = None,
                  risk_metrics: Optional[Dict[str, Any]] = None,
                  decision_taken: Optional[str] = None,
                  metadata: Optional[Dict[str, Any]] = None) -> str:
        """Log risk event for audit trail."""
        try:
            event_id = str(uuid.uuid4())
            
            event = RiskEvent(
                id=event_id,
                timestamp=datetime.now(),
                event_type=event_type,
                source=source,
                description=description,
                severity=severity,
                symbol=symbol,
                strategy_id=strategy_id,
                user_id=user_id,
                portfolio_value=portfolio_value,
                risk_metrics=risk_metrics,
                decision_taken=decision_taken,
                metadata=metadata or {}
            )
            
            with self._lock:
                self.event_history.append(event)
                self.stats['events_logged'] += 1
            
            # Persist to database
            self._persist_event(event)
            
            # Trigger callbacks
            for callback in self.event_callbacks:
                try:
                    callback(event)
                except Exception as e:
                    self.logger.error(f"Error in event callback: {e}")
            
            return event_id
            
        except Exception as e:
            self.logger.error(f"Error logging event: {e}")
            return ""
    
    def record_decision(self, 
                       decision_type: str,
                       description: str,
                       user_id: Optional[str] = None,
                       symbol: Optional[str] = None,
                       strategy_id: Optional[str] = None,
                       metadata: Optional[Dict[str, Any]] = None) -> str:
        """Record risk control decision."""
        try:
            event_id = self._log_event(
                event_type="risk_decision",
                source="risk_control",
                description=description,
                severity=AlertSeverity.INFO,
                symbol=symbol,
                strategy_id=strategy_id,
                user_id=user_id,
                decision_taken=decision_type,
                metadata=metadata
            )
            
            self.stats['decisions_recorded'] += 1
            self.logger.info(f"Risk decision recorded: {decision_type} - {description}")
            
            return event_id
            
        except Exception as e:
            self.logger.error(f"Error recording decision: {e}")
            return ""
    
    def acknowledge_alert(self, alert_id: str, user_id: str, notes: Optional[str] = None) -> bool:
        """Acknowledge an active alert."""
        try:
            with self._lock:
                if alert_id in self.active_alerts:
                    alert = self.active_alerts[alert_id]
                    alert.status = AlertStatus.ACKNOWLEDGED
                    alert.acknowledged_by = user_id
                    alert.acknowledged_at = datetime.now()
                    
                    if notes:
                        alert.metadata['acknowledgment_notes'] = notes
                    
                    # Update in database
                    self._update_alert_status(alert)
                    
                    # Log event
                    self._log_event(
                        event_type="alert_acknowledged",
                        source="user_action",
                        description=f"Alert acknowledged by {user_id}",
                        severity=AlertSeverity.INFO,
                        user_id=user_id,
                        metadata={'alert_id': alert_id, 'notes': notes}
                    )
                    
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error acknowledging alert: {e}")
            return False
    
    def resolve_alert(self, alert_id: str, user_id: str, resolution_notes: Optional[str] = None) -> bool:
        """Resolve an active alert."""
        try:
            with self._lock:
                if alert_id in self.active_alerts:
                    alert = self.active_alerts[alert_id]
                    alert.status = AlertStatus.RESOLVED
                    alert.resolved_at = datetime.now()
                    
                    if resolution_notes:
                        alert.metadata['resolution_notes'] = resolution_notes
                    
                    # Remove from active alerts
                    del self.active_alerts[alert_id]
                    
                    # Update in database
                    self._update_alert_status(alert)
                    
                    # Log event
                    self._log_event(
                        event_type="alert_resolved",
                        source="user_action",
                        description=f"Alert resolved by {user_id}",
                        severity=AlertSeverity.INFO,
                        user_id=user_id,
                        metadata={'alert_id': alert_id, 'resolution_notes': resolution_notes}
                    )
                    
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error resolving alert: {e}")
            return False
    
    def suppress_alert_type(self, threshold_name: str, duration_minutes: int = 60) -> bool:
        """Suppress alerts for a specific threshold type."""
        try:
            self.suppressed_alerts.add(threshold_name)
            
            # Set timer to remove suppression
            def remove_suppression():
                self.suppressed_alerts.discard(threshold_name)
                self.logger.info(f"Alert suppression removed for {threshold_name}")
            
            timer = threading.Timer(duration_minutes * 60, remove_suppression)
            timer.start()
            
            self.logger.info(f"Alert suppressed for {threshold_name} for {duration_minutes} minutes")
            return True
            
        except Exception as e:
            self.logger.error(f"Error suppressing alert: {e}")
            return False
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring system status."""
        try:
            with self._lock:
                active_alerts_by_severity = defaultdict(int)
                for alert in self.active_alerts.values():
                    active_alerts_by_severity[alert.severity.value] += 1
                
                return {
                    'status': self.status.value,
                    'start_time': self.start_time.isoformat() if self.start_time else None,
                    'last_update': self.last_update.isoformat() if self.last_update else None,
                    'uptime_seconds': self.stats['uptime_seconds'],
                    'active_alerts': len(self.active_alerts),
                    'active_alerts_by_severity': dict(active_alerts_by_severity),
                    'total_alerts_generated': self.stats['alerts_generated'],
                    'total_events_logged': self.stats['events_logged'],
                    'violations_detected': self.stats['violations_detected'],
                    'decisions_recorded': self.stats['decisions_recorded'],
                    'thresholds_configured': len(self.thresholds),
                    'suppressed_alert_types': len(self.suppressed_alerts),
                    'alert_callbacks': len(self.alert_callbacks),
                    'event_callbacks': len(self.event_callbacks)
                }
        
        except Exception as e:
            self.logger.error(f"Error getting monitoring status: {e}")
            return {'error': str(e)}
    
    def get_active_alerts(self, severity: Optional[AlertSeverity] = None) -> List[RiskAlert]:
        """Get active alerts, optionally filtered by severity."""
        try:
            with self._lock:
                alerts = list(self.active_alerts.values())
                
                if severity:
                    alerts = [alert for alert in alerts if alert.severity == severity]
                
                # Sort by timestamp (newest first)
                alerts.sort(key=lambda x: x.timestamp, reverse=True)
                
                return alerts
        
        except Exception as e:
            self.logger.error(f"Error getting active alerts: {e}")
            return []
    
    def get_alert_history(self, hours: int = 24, severity: Optional[AlertSeverity] = None) -> List[RiskAlert]:
        """Get alert history for specified time period."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            with self._lock:
                alerts = [alert for alert in self.alert_history 
                         if alert.timestamp >= cutoff_time]
                
                if severity:
                    alerts = [alert for alert in alerts if alert.severity == severity]
                
                # Sort by timestamp (newest first)
                alerts.sort(key=lambda x: x.timestamp, reverse=True)
                
                return alerts
        
        except Exception as e:
            self.logger.error(f"Error getting alert history: {e}")
            return []
    
    def get_event_history(self, hours: int = 24, event_type: Optional[str] = None) -> List[RiskEvent]:
        """Get event history for specified time period."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            with self._lock:
                events = [event for event in self.event_history 
                         if event.timestamp >= cutoff_time]
                
                if event_type:
                    events = [event for event in events if event.event_type == event_type]
                
                # Sort by timestamp (newest first)
                events.sort(key=lambda x: x.timestamp, reverse=True)
                
                return events
        
        except Exception as e:
            self.logger.error(f"Error getting event history: {e}")
            return []
    
    def add_threshold(self, threshold: RiskThreshold) -> bool:
        """Add or update risk threshold."""
        try:
            self.thresholds[threshold.name] = threshold
            
            self._log_event(
                event_type="threshold_updated",
                source="configuration",
                description=f"Risk threshold updated: {threshold.name}",
                severity=AlertSeverity.INFO,
                metadata={'threshold': asdict(threshold)}
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding threshold: {e}")
            return False
    
    def remove_threshold(self, threshold_name: str) -> bool:
        """Remove risk threshold."""
        try:
            if threshold_name in self.thresholds:
                del self.thresholds[threshold_name]
                
                self._log_event(
                    event_type="threshold_removed",
                    source="configuration",
                    description=f"Risk threshold removed: {threshold_name}",
                    severity=AlertSeverity.INFO,
                    metadata={'threshold_name': threshold_name}
                )
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error removing threshold: {e}")
            return False
    
    def add_alert_callback(self, callback: Callable[[RiskAlert], None]) -> None:
        """Add alert callback function."""
        self.alert_callbacks.append(callback)
    
    def add_event_callback(self, callback: Callable[[RiskEvent], None]) -> None:
        """Add event callback function."""
        self.event_callbacks.append(callback)
    
    def _persist_alert(self, alert: RiskAlert) -> None:
        """Persist alert to database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO risk_alerts (
                    id, timestamp, severity, status, title, message, source,
                    symbol, strategy_id, threshold_value, current_value, metadata,
                    acknowledged_by, acknowledged_at, resolved_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                alert.id,
                alert.timestamp.isoformat(),
                alert.severity.value,
                alert.status.value,
                alert.title,
                alert.message,
                alert.source,
                alert.symbol,
                alert.strategy_id,
                alert.threshold_value,
                alert.current_value,
                json.dumps(alert.metadata),
                alert.acknowledged_by,
                alert.acknowledged_at.isoformat() if alert.acknowledged_at else None,
                alert.resolved_at.isoformat() if alert.resolved_at else None
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error persisting alert: {e}")
    
    def _persist_event(self, event: RiskEvent) -> None:
        """Persist event to database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO risk_events (
                    id, timestamp, event_type, source, description, severity,
                    symbol, strategy_id, user_id, portfolio_value, risk_metrics,
                    decision_taken, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                event.id,
                event.timestamp.isoformat(),
                event.event_type,
                event.source,
                event.description,
                event.severity.value,
                event.symbol,
                event.strategy_id,
                event.user_id,
                event.portfolio_value,
                json.dumps(event.risk_metrics) if event.risk_metrics else None,
                event.decision_taken,
                json.dumps(event.metadata)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error persisting event: {e}")
    
    def _persist_risk_metrics(self, metrics: RiskMetrics) -> None:
        """Persist risk metrics to database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO risk_metrics_log (timestamp, portfolio_value, metrics_data)
                VALUES (?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                metrics.portfolio_value,
                json.dumps(metrics.to_dict())
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error persisting risk metrics: {e}")
    
    def _update_alert_status(self, alert: RiskAlert) -> None:
        """Update alert status in database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE risk_alerts 
                SET status = ?, acknowledged_by = ?, acknowledged_at = ?, 
                    resolved_at = ?, metadata = ?
                WHERE id = ?
            ''', (
                alert.status.value,
                alert.acknowledged_by,
                alert.acknowledged_at.isoformat() if alert.acknowledged_at else None,
                alert.resolved_at.isoformat() if alert.resolved_at else None,
                json.dumps(alert.metadata),
                alert.id
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error updating alert status: {e}")
    
    def _cleanup_old_data(self) -> None:
        """Clean up old data from memory and database."""
        try:
            # Clean up old cooldowns
            current_time = datetime.now()
            expired_cooldowns = [
                name for name, expiry in self.alert_cooldowns.items()
                if current_time >= expiry
            ]
            
            for name in expired_cooldowns:
                del self.alert_cooldowns[name]
            
            # Clean up old database records (keep last 30 days)
            cutoff_date = (current_time - timedelta(days=30)).isoformat()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Clean old resolved alerts
            cursor.execute('''
                DELETE FROM risk_alerts 
                WHERE status = 'resolved' AND timestamp < ?
            ''', (cutoff_date,))
            
            # Clean old events (keep audit trail longer - 90 days)
            audit_cutoff = (current_time - timedelta(days=90)).isoformat()
            cursor.execute('''
                DELETE FROM risk_events 
                WHERE timestamp < ?
            ''', (audit_cutoff,))
            
            # Clean old metrics (keep 7 days)
            metrics_cutoff = (current_time - timedelta(days=7)).isoformat()
            cursor.execute('''
                DELETE FROM risk_metrics_log 
                WHERE timestamp < ?
            ''', (metrics_cutoff,))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {e}")
    
    def generate_monitoring_report(self, hours: int = 24) -> Dict[str, Any]:
        """Generate comprehensive monitoring report."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Get alerts and events for the period
            alerts = self.get_alert_history(hours)
            events = self.get_event_history(hours)
            
            # Analyze alerts by severity
            alerts_by_severity = defaultdict(int)
            for alert in alerts:
                alerts_by_severity[alert.severity.value] += 1
            
            # Analyze events by type
            events_by_type = defaultdict(int)
            for event in events:
                events_by_type[event.event_type] += 1
            
            # Get threshold breach summary
            threshold_breaches = defaultdict(int)
            for alert in alerts:
                if alert.source == "threshold_monitor":
                    threshold_name = alert.metadata.get('threshold_name', 'Unknown')
                    threshold_breaches[threshold_name] += 1
            
            # Calculate system health metrics
            total_monitoring_time = hours * 3600  # in seconds
            actual_uptime = min(self.stats['uptime_seconds'], total_monitoring_time)
            uptime_percentage = (actual_uptime / total_monitoring_time) * 100
            
            return {
                'report_period_hours': hours,
                'report_generated_at': datetime.now().isoformat(),
                'system_status': {
                    'current_status': self.status.value,
                    'uptime_percentage': uptime_percentage,
                    'uptime_seconds': actual_uptime
                },
                'alert_summary': {
                    'total_alerts': len(alerts),
                    'active_alerts': len(self.active_alerts),
                    'alerts_by_severity': dict(alerts_by_severity),
                    'threshold_breaches': dict(threshold_breaches)
                },
                'event_summary': {
                    'total_events': len(events),
                    'events_by_type': dict(events_by_type)
                },
                'statistics': self.stats.copy(),
                'configuration': {
                    'thresholds_configured': len(self.thresholds),
                    'suppressed_alert_types': list(self.suppressed_alerts),
                    'active_cooldowns': len(self.alert_cooldowns)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error generating monitoring report: {e}")
            return {'error': str(e)}
    
    def __enter__(self):
        """Context manager entry."""
        self.start_monitoring()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop_monitoring()