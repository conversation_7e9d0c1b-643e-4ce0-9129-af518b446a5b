"""
流动性风险管理模块

提供全面的流动性风险评估和管理功能，包括：
- 单个资产流动性评估
- 投资组合流动性风险分析
- 流动性冲击测试
- 流动性缺口分析
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import numpy as np
import pandas as pd
from decimal import Decimal

from src.market.strategies.models.portfolio import Portfolio
from src.market.data.manager import DataManager

logger = logging.getLogger(__name__)

@dataclass
class LiquidityMetrics:
    """流动性指标"""
    symbol: str
    avg_daily_volume: float
    bid_ask_spread: float
    price_impact: float
    liquidity_score: float
    days_to_liquidate: int
    market_depth: float
    volatility: float
    liquidity_risk_level: str  # LOW, MEDIUM, HIGH, CRITICAL

@dataclass
class LiquidityStressResult:
    """流动性压力测试结果"""
    timestamp: datetime
    total_portfolio_value: float
    liquid_value: float
    illiquid_value: float
    liquidity_ratio: float
    funding_gap: float
    liquidation_time_days: int
    high_risk_positions: Dict[str, float]
    stress_scenarios: Dict[str, Dict[str, float]]

class LiquidityRiskManager:
    """流动性风险管理器"""
    
    def __init__(self, data_manager: DataManager):
        self.data_manager = data_manager
        
        # 流动性分类阈值
        self.liquidity_thresholds = {
            'volume_low': 100000,      # 日均成交额低于10万
            'volume_medium': 1000000,  # 日均成交额100万
            'spread_high': 0.005,      # 买卖价差超过0.5%
            'spread_critical': 0.02,   # 买卖价差超过2%
            'volatility_high': 0.03,   # 日波动率超过3%
            'market_cap_small': 1e9,   # 市值小于10亿
            'market_cap_micro': 1e8    # 市值小于1亿
        }
        
        # 流动性冲击情景
        self.stress_scenarios = {
            'market_crisis': {
                'spread_multiplier': 3.0,      # 买卖价差扩大3倍
                'volume_reduction': 0.5,       # 成交量减少50%
                'price_impact_multiplier': 2.0 # 价格冲击增加1倍
            },
            'sector_crisis': {
                'spread_multiplier': 2.0,
                'volume_reduction': 0.3,
                'price_impact_multiplier': 1.5
            },
            'flash_crash': {
                'spread_multiplier': 5.0,
                'volume_reduction': 0.8,
                'price_impact_multiplier': 3.0
            }
        }
        
        # 缓存
        self._liquidity_cache = {}
        self._cache_expiry = timedelta(hours=1)
        
    async def initialize(self):
        """初始化流动性风险管理器"""
        try:
            logger.info("流动性风险管理器初始化成功")
        except Exception as e:
            logger.error(f"流动性风险管理器初始化失败: {e}")
            raise
    
    async def analyze_asset_liquidity(self, symbol: str) -> LiquidityMetrics:
        """分析单个资产的流动性"""
        try:
            # 检查缓存
            cache_key = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H')}"
            if cache_key in self._liquidity_cache:
                cache_time, metrics = self._liquidity_cache[cache_key]
                if datetime.now() - cache_time < self._cache_expiry:
                    return metrics
            
            # 获取历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=60)
            
            market_data = await self.data_manager.get_market_data(
                symbol=symbol,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d')
            )
            
            if market_data.empty:
                raise ValueError(f"无法获取 {symbol} 的市场数据")
            
            # 计算流动性指标
            avg_daily_volume = market_data['volume'].mean()
            avg_price = market_data['close'].mean()
            avg_daily_value = avg_daily_volume * avg_price
            
            # 计算波动率
            returns = market_data['close'].pct_change().dropna()
            volatility = returns.std()
            
            # 估算买卖价差（简化计算）
            high_low_spread = ((market_data['high'] - market_data['low']) / 
                              market_data['close']).mean()
            bid_ask_spread = high_low_spread * 0.3  # 简化估算
            
            # 计算价格冲击（基于成交量和波动率）
            price_impact = self._calculate_price_impact(
                avg_daily_volume, volatility, avg_price
            )
            
            # 计算市场深度
            market_depth = self._estimate_market_depth(avg_daily_value, volatility)
            
            # 计算流动性评分
            liquidity_score = self._calculate_liquidity_score(
                avg_daily_value, bid_ask_spread, price_impact, volatility
            )
            
            # 评估风险等级
            risk_level = self._assess_liquidity_risk_level(
                avg_daily_value, bid_ask_spread, price_impact, volatility
            )
            
            # 估算清算天数
            days_to_liquidate = self._estimate_liquidation_days(
                avg_daily_volume, market_depth
            )
            
            metrics = LiquidityMetrics(
                symbol=symbol,
                avg_daily_volume=avg_daily_volume,
                bid_ask_spread=bid_ask_spread,
                price_impact=price_impact,
                liquidity_score=liquidity_score,
                days_to_liquidate=days_to_liquidate,
                market_depth=market_depth,
                volatility=volatility,
                liquidity_risk_level=risk_level
            )
            
            # 缓存结果
            self._liquidity_cache[cache_key] = (datetime.now(), metrics)
            
            logger.debug(f"{symbol} 流动性分析完成: 评分 {liquidity_score:.2f}, "
                        f"风险等级 {risk_level}")
            
            return metrics
            
        except Exception as e:
            logger.error(f"分析 {symbol} 流动性失败: {e}")
            raise
    
    async def analyze_portfolio_liquidity(self, portfolio: Portfolio) -> Dict[str, LiquidityMetrics]:
        """分析投资组合流动性"""
        try:
            liquidity_analysis = {}
            
            for symbol in portfolio.positions.keys():
                try:
                    metrics = await self.analyze_asset_liquidity(symbol)
                    liquidity_analysis[symbol] = metrics
                except Exception as e:
                    logger.warning(f"分析 {symbol} 流动性失败: {e}")
            
            return liquidity_analysis
            
        except Exception as e:
            logger.error(f"分析投资组合流动性失败: {e}")
            return {}
    
    async def run_liquidity_stress_test(self, 
                                       portfolio: Portfolio,
                                       scenario_name: str = 'market_crisis',
                                       funding_need: float = 0.0) -> LiquidityStressResult:
        """运行流动性压力测试"""
        try:
            if scenario_name not in self.stress_scenarios:
                raise ValueError(f"未知的压力测试情景: {scenario_name}")
            
            scenario = self.stress_scenarios[scenario_name]
            
            # 分析各持仓的流动性
            liquidity_analysis = await self.analyze_portfolio_liquidity(portfolio)
            
            total_value = portfolio.get_total_value()
            liquid_value = 0.0
            illiquid_value = 0.0
            high_risk_positions = {}
            liquidation_timeline = {}
            
            for symbol, position in portfolio.positions.items():
                if symbol not in liquidity_analysis:
                    continue
                
                metrics = liquidity_analysis[symbol]
                position_value = position.market_value
                
                # 应用压力情景
                stressed_spread = metrics.bid_ask_spread * scenario['spread_multiplier']
                stressed_impact = metrics.price_impact * scenario['price_impact_multiplier']
                stressed_volume = metrics.avg_daily_volume * (1 - scenario['volume_reduction'])
                
                # 计算压力下的流动性成本
                liquidity_cost = (stressed_spread + stressed_impact) * position_value
                net_position_value = position_value - liquidity_cost
                
                # 重新评估清算时间
                stressed_liquidation_days = self._estimate_liquidation_days_stressed(
                    position.quantity, stressed_volume, metrics.market_depth
                )
                
                liquidation_timeline[symbol] = {
                    'days': stressed_liquidation_days,
                    'value': net_position_value,
                    'cost': liquidity_cost
                }
                
                # 分类流动性
                if (metrics.liquidity_risk_level in ['HIGH', 'CRITICAL'] or 
                    stressed_liquidation_days > 5):
                    illiquid_value += net_position_value
                    high_risk_positions[symbol] = {
                        'value': position_value,
                        'risk_level': metrics.liquidity_risk_level,
                        'liquidation_days': stressed_liquidation_days,
                        'liquidity_cost': liquidity_cost
                    }
                else:
                    liquid_value += net_position_value
            
            # 计算流动性比率
            liquidity_ratio = liquid_value / total_value if total_value > 0 else 0
            
            # 计算资金缺口
            available_liquid = liquid_value + portfolio.cash
            funding_gap = max(0, funding_need - available_liquid)
            
            # 估算总清算时间
            max_liquidation_days = max(
                (data['days'] for data in liquidation_timeline.values()), 
                default=0
            )
            
            result = LiquidityStressResult(
                timestamp=datetime.now(),
                total_portfolio_value=total_value,
                liquid_value=liquid_value,
                illiquid_value=illiquid_value,
                liquidity_ratio=liquidity_ratio,
                funding_gap=funding_gap,
                liquidation_time_days=max_liquidation_days,
                high_risk_positions=high_risk_positions,
                stress_scenarios={
                    scenario_name: {
                        'liquidation_timeline': liquidation_timeline,
                        'total_liquidity_cost': sum(
                            data['cost'] for data in liquidation_timeline.values()
                        ),
                        'scenario_parameters': scenario
                    }
                }
            )
            
            logger.info(f"流动性压力测试完成: {scenario_name}, "
                       f"流动性比率: {liquidity_ratio:.2%}, "
                       f"资金缺口: {funding_gap:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"流动性压力测试失败: {e}")
            raise
    
    async def calculate_liquidity_gap(self, 
                                    portfolio: Portfolio,
                                    time_horizons: List[int] = [1, 7, 30]) -> Dict[int, Dict[str, float]]:
        """计算不同时间期限的流动性缺口"""
        try:
            liquidity_analysis = await self.analyze_portfolio_liquidity(portfolio)
            gap_analysis = {}
            
            for horizon_days in time_horizons:
                liquidable_value = portfolio.cash  # 现金立即可用
                illiquid_value = 0.0
                
                for symbol, position in portfolio.positions.items():
                    if symbol not in liquidity_analysis:
                        continue
                    
                    metrics = liquidity_analysis[symbol]
                    
                    if metrics.days_to_liquidate <= horizon_days:
                        # 考虑流动性成本
                        liquidity_cost = (metrics.bid_ask_spread + 
                                        metrics.price_impact) * position.market_value
                        liquidable_value += position.market_value - liquidity_cost
                    else:
                        illiquid_value += position.market_value
                
                gap_analysis[horizon_days] = {
                    'liquidable_value': liquidable_value,
                    'illiquid_value': illiquid_value,
                    'liquidity_ratio': liquidable_value / portfolio.get_total_value(),
                    'coverage_ratio': liquidable_value / (liquidable_value + illiquid_value)
                }
            
            return gap_analysis
            
        except Exception as e:
            logger.error(f"计算流动性缺口失败: {e}")
            return {}
    
    async def get_liquidity_recommendations(self, 
                                          portfolio: Portfolio) -> List[Dict[str, any]]:
        """获取流动性改善建议"""
        try:
            liquidity_analysis = await self.analyze_portfolio_liquidity(portfolio)
            recommendations = []
            
            # 分析高风险持仓
            high_risk_positions = {
                symbol: metrics for symbol, metrics in liquidity_analysis.items()
                if metrics.liquidity_risk_level in ['HIGH', 'CRITICAL']
            }
            
            if high_risk_positions:
                total_high_risk_value = sum(
                    portfolio.positions[symbol].market_value 
                    for symbol in high_risk_positions.keys()
                    if symbol in portfolio.positions
                )
                
                recommendations.append({
                    'type': 'REDUCE_ILLIQUID_POSITIONS',
                    'priority': 'HIGH',
                    'description': f'减少流动性风险较高的持仓',
                    'details': {
                        'affected_symbols': list(high_risk_positions.keys()),
                        'total_value': total_high_risk_value,
                        'suggested_reduction': min(0.5, total_high_risk_value / portfolio.get_total_value())
                    }
                })
            
            # 分析现金比例
            cash_ratio = portfolio.cash / portfolio.get_total_value()
            if cash_ratio < 0.05:  # 现金比例低于5%
                recommendations.append({
                    'type': 'INCREASE_CASH_POSITION',
                    'priority': 'MEDIUM',
                    'description': '增加现金持仓以提高流动性',
                    'details': {
                        'current_cash_ratio': cash_ratio,
                        'suggested_cash_ratio': 0.10,
                        'additional_cash_needed': portfolio.get_total_value() * 0.05
                    }
                })
            
            # 分析集中度风险
            position_values = {
                symbol: position.market_value 
                for symbol, position in portfolio.positions.items()
            }
            
            if position_values:
                max_position_value = max(position_values.values())
                max_position_ratio = max_position_value / portfolio.get_total_value()
                
                if max_position_ratio > 0.3:  # 单一持仓超过30%
                    max_symbol = max(position_values, key=position_values.get)
                    recommendations.append({
                        'type': 'REDUCE_CONCENTRATION',
                        'priority': 'MEDIUM',
                        'description': f'减少 {max_symbol} 的集中度风险',
                        'details': {
                            'symbol': max_symbol,
                            'current_ratio': max_position_ratio,
                            'suggested_ratio': 0.25,
                            'reduction_amount': max_position_value * 0.2
                        }
                    })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"获取流动性建议失败: {e}")
            return []
    
    def _calculate_price_impact(self, volume: float, volatility: float, price: float) -> float:
        """计算价格冲击"""
        try:
            # 简化的价格冲击模型
            # 实际应该使用更复杂的市场微观结构模型
            
            if volume <= 0:
                return 0.1  # 默认10%的价格冲击
            
            # 基于成交量和波动率的简化模型
            volume_factor = max(0.001, 1 / np.sqrt(volume))
            volatility_factor = volatility * 2
            
            price_impact = volume_factor * volatility_factor
            return min(price_impact, 0.20)  # 最大20%的价格冲击
            
        except Exception as e:
            logger.error(f"计算价格冲击失败: {e}")
            return 0.1
    
    def _estimate_market_depth(self, daily_value: float, volatility: float) -> float:
        """估算市场深度"""
        try:
            # 简化的市场深度估算
            base_depth = daily_value * 0.1  # 假设深度为日成交额的10%
            volatility_adjustment = max(0.5, 1 - volatility * 5)  # 高波动率降低深度
            
            return base_depth * volatility_adjustment
            
        except Exception as e:
            logger.error(f"估算市场深度失败: {e}")
            return daily_value * 0.05
    
    def _calculate_liquidity_score(self, 
                                 daily_value: float, 
                                 spread: float, 
                                 price_impact: float, 
                                 volatility: float) -> float:
        """计算流动性评分"""
        try:
            # 流动性评分 (0-100)
            
            # 成交量评分 (40%权重)
            if daily_value > self.liquidity_thresholds['volume_medium']:
                volume_score = 40
            elif daily_value > self.liquidity_thresholds['volume_low']:
                volume_score = 25
            else:
                volume_score = 10
            
            # 价差评分 (30%权重)
            if spread < self.liquidity_thresholds['spread_high']:
                spread_score = 30
            elif spread < self.liquidity_thresholds['spread_critical']:
                spread_score = 15
            else:
                spread_score = 5
            
            # 价格冲击评分 (20%权重)
            if price_impact < 0.01:
                impact_score = 20
            elif price_impact < 0.05:
                impact_score = 12
            else:
                impact_score = 5
            
            # 波动率评分 (10%权重)
            if volatility < 0.02:
                volatility_score = 10
            elif volatility < self.liquidity_thresholds['volatility_high']:
                volatility_score = 6
            else:
                volatility_score = 2
            
            total_score = volume_score + spread_score + impact_score + volatility_score
            return min(100, max(0, total_score))
            
        except Exception as e:
            logger.error(f"计算流动性评分失败: {e}")
            return 50  # 默认中等评分
    
    def _assess_liquidity_risk_level(self, 
                                   daily_value: float, 
                                   spread: float, 
                                   price_impact: float, 
                                   volatility: float) -> str:
        """评估流动性风险等级"""
        try:
            risk_score = 0
            
            # 成交量风险
            if daily_value < self.liquidity_thresholds['volume_low']:
                risk_score += 3
            elif daily_value < self.liquidity_thresholds['volume_medium']:
                risk_score += 1
            
            # 价差风险
            if spread > self.liquidity_thresholds['spread_critical']:
                risk_score += 3
            elif spread > self.liquidity_thresholds['spread_high']:
                risk_score += 2
            
            # 价格冲击风险
            if price_impact > 0.1:
                risk_score += 3
            elif price_impact > 0.05:
                risk_score += 2
            elif price_impact > 0.02:
                risk_score += 1
            
            # 波动率风险
            if volatility > self.liquidity_thresholds['volatility_high']:
                risk_score += 2
            
            # 风险等级判定
            if risk_score >= 8:
                return 'CRITICAL'
            elif risk_score >= 5:
                return 'HIGH'
            elif risk_score >= 2:
                return 'MEDIUM'
            else:
                return 'LOW'
                
        except Exception as e:
            logger.error(f"评估流动性风险等级失败: {e}")
            return 'MEDIUM'
    
    def _estimate_liquidation_days(self, volume: float, market_depth: float) -> int:
        """估算清算天数"""
        try:
            if volume <= 0:
                return 30  # 默认30天
            
            # 假设每天最多交易平均成交量的10%
            daily_tradable = volume * 0.1
            
            # 基于市场深度调整
            if market_depth > 0:
                adjustment_factor = max(0.5, min(2.0, market_depth / (volume * 0.05)))
                daily_tradable *= adjustment_factor
            
            # 估算需要多少天清算（假设要清算平均持仓量）
            estimated_days = max(1, int(volume * 0.01 / daily_tradable))
            
            return min(estimated_days, 30)  # 最多30天
            
        except Exception as e:
            logger.error(f"估算清算天数失败: {e}")
            return 10
    
    def _estimate_liquidation_days_stressed(self, 
                                          position_quantity: float,
                                          stressed_volume: float, 
                                          market_depth: float) -> int:
        """估算压力情况下的清算天数"""
        try:
            if stressed_volume <= 0:
                return 60  # 极端情况下60天
            
            # 压力情况下更保守的交易比例
            daily_tradable = stressed_volume * 0.05  # 只能交易5%
            
            # 市场深度在压力下也会降低
            stressed_depth = market_depth * 0.3
            depth_adjustment = max(0.2, stressed_depth / (stressed_volume * 0.02))
            daily_tradable *= depth_adjustment
            
            estimated_days = max(1, int(position_quantity / daily_tradable))
            
            return min(estimated_days, 60)  # 最多60天
            
        except Exception as e:
            logger.error(f"估算压力清算天数失败: {e}")
            return 30