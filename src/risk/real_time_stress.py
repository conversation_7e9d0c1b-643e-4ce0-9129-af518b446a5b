"""
实时压力测试模块

提供实时的投资组合压力测试功能，包括：
- 实时市场压力测试
- 历史情景模拟
- 蒙特卡洛模拟
- 极端事件分析
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import numpy as np
import pandas as pd
from scipy import stats
from decimal import Decimal

from src.market.strategies.models.portfolio import Portfolio
from src.market.data.manager import DataManager

logger = logging.getLogger(__name__)

@dataclass
class StressTestResult:
    """压力测试结果"""
    test_name: str
    test_type: str
    timestamp: datetime
    portfolio_value: float
    stressed_value: float
    max_loss: float
    max_loss_pct: float
    var_95: float
    var_99: float
    expected_shortfall: float
    recovery_days: int
    worst_positions: Dict[str, float]
    scenario_details: Dict[str, any]

@dataclass
class MarketScenario:
    """市场情景"""
    name: str
    description: str
    shock_factors: Dict[str, float]  # symbol -> shock_factor
    duration_days: int
    probability: float = 0.01

class RealTimeStressTester:
    """实时压力测试器"""
    
    def __init__(self, data_manager: DataManager):
        self.data_manager = data_manager
        
        # 预定义市场情景
        self.market_scenarios = {
            'financial_crisis_2008': MarketScenario(
                name='2008金融危机',
                description='基于2008年金融危机的市场冲击',
                shock_factors={
                    'SPY': -0.45,  # 标普500下跌45%
                    'QQQ': -0.50,  # 纳斯达克下跌50%
                    'XLF': -0.60,  # 金融股下跌60%
                    'GLD': 0.25,   # 黄金上涨25%
                    'TLT': 0.15    # 长期国债上涨15%
                },
                duration_days=180
            ),
            'covid_crash_2020': MarketScenario(
                name='2020新冠疫情',
                description='基于2020年新冠疫情的市场冲击',
                shock_factors={
                    'SPY': -0.35,
                    'QQQ': -0.30,
                    'XLI': -0.40,  # 工业股下跌40%
                    'XLE': -0.50,  # 能源股下跌50%
                    'ZOOM': 2.00,  # 远程办公股大涨
                    'NFLX': 0.50   # 流媒体股上涨
                },
                duration_days=90
            ),
            'interest_rate_shock': MarketScenario(
                name='利率冲击',
                description='利率急剧上升的冲击情景',
                shock_factors={
                    'TLT': -0.20,  # 长期国债下跌20%
                    'XLF': 0.15,   # 银行股受益上涨15%
                    'REIT': -0.25, # 房地产信托下跌25%
                    'GROWTH': -0.15 # 成长股下跌15%
                },
                duration_days=30
            ),
            'tech_bubble_burst': MarketScenario(
                name='科技泡沫破裂',
                description='科技股泡沫破裂情景',
                shock_factors={
                    'QQQ': -0.60,  # 纳斯达克暴跌60%
                    'AAPL': -0.50,
                    'GOOGL': -0.55,
                    'TSLA': -0.70,
                    'MSFT': -0.45,
                    'SPY': -0.25   # 标普500相对抗跌
                },
                duration_days=120
            )
        }
        
        # 实时监控配置
        self.monitoring_enabled = False
        self.monitoring_interval = 300  # 5分钟
        self.alert_thresholds = {
            'max_loss_pct': 0.10,    # 最大损失超过10%告警
            'var_95': 0.05,          # VaR超过5%告警
            'position_concentration': 0.30  # 单一持仓超过30%告警
        }
        
        # 监控任务
        self._monitoring_task = None
        self._test_results_cache = {}
        
    async def initialize(self):
        """初始化压力测试器"""
        try:
            logger.info("实时压力测试器初始化成功")
        except Exception as e:
            logger.error(f"实时压力测试器初始化失败: {e}")
            raise
    
    async def start_real_time_monitoring(self, portfolio: Portfolio):
        """开始实时监控"""
        if self.monitoring_enabled:
            return
        
        self.monitoring_enabled = True
        self._monitoring_task = asyncio.create_task(
            self._monitoring_loop(portfolio)
        )
        logger.info("实时压力测试监控已启动")
    
    async def stop_real_time_monitoring(self):
        """停止实时监控"""
        self.monitoring_enabled = False
        if self._monitoring_task:
            self._monitoring_task.cancel()
        logger.info("实时压力测试监控已停止")
    
    async def run_scenario_stress_test(self, 
                                     portfolio: Portfolio, 
                                     scenario_name: str) -> StressTestResult:
        """运行情景压力测试"""
        try:
            scenario = self.market_scenarios.get(scenario_name)
            if not scenario:
                raise ValueError(f"未找到压力测试情景: {scenario_name}")
            
            # 获取当前投资组合价值
            current_value = portfolio.get_total_value()
            
            # 计算受压后的投资组合价值
            stressed_positions = {}
            total_stressed_value = portfolio.cash
            
            for symbol, position in portfolio.positions.items():
                # 获取冲击因子
                shock_factor = self._get_symbol_shock_factor(symbol, scenario.shock_factors)
                
                # 计算受压价格
                current_price = position.current_price
                stressed_price = current_price * (1 + shock_factor)
                
                # 计算受压价值
                stressed_value = position.quantity * stressed_price
                stressed_positions[symbol] = {
                    'original_value': position.market_value,
                    'stressed_value': stressed_value,
                    'shock_factor': shock_factor,
                    'loss': stressed_value - position.market_value
                }
                
                total_stressed_value += stressed_value
            
            # 计算损失
            total_loss = total_stressed_value - current_value
            loss_pct = total_loss / current_value if current_value > 0 else 0
            
            # 计算风险指标
            var_95, var_99, expected_shortfall = await self._calculate_risk_metrics(
                portfolio, scenario
            )
            
            # 找出最差的持仓
            worst_positions = {
                symbol: data['loss'] 
                for symbol, data in stressed_positions.items()
            }
            worst_positions = dict(sorted(worst_positions.items(), 
                                        key=lambda x: x[1])[:5])
            
            result = StressTestResult(
                test_name=scenario.name,
                test_type='scenario',
                timestamp=datetime.now(),
                portfolio_value=current_value,
                stressed_value=total_stressed_value,
                max_loss=abs(total_loss),
                max_loss_pct=abs(loss_pct),
                var_95=var_95,
                var_99=var_99,
                expected_shortfall=expected_shortfall,
                recovery_days=scenario.duration_days,
                worst_positions=worst_positions,
                scenario_details={
                    'scenario_name': scenario_name,
                    'shock_factors': scenario.shock_factors,
                    'stressed_positions': stressed_positions
                }
            )
            
            # 缓存结果
            self._test_results_cache[scenario_name] = result
            
            logger.info(f"情景压力测试完成: {scenario.name}, "
                       f"最大损失: {loss_pct:.2%}")
            
            return result
            
        except Exception as e:
            logger.error(f"情景压力测试失败: {e}")
            raise
    
    async def run_monte_carlo_simulation(self, 
                                       portfolio: Portfolio,
                                       num_simulations: int = 10000,
                                       time_horizon_days: int = 252) -> StressTestResult:
        """运行蒙特卡洛模拟"""
        try:
            # 获取历史收益率数据
            returns_data = await self._get_historical_returns(portfolio, days=500)
            
            if returns_data.empty:
                raise ValueError("无法获取足够的历史数据")
            
            # 计算收益率统计
            mean_returns = returns_data.mean()
            cov_matrix = returns_data.cov()
            
            # 蒙特卡洛模拟
            portfolio_values = []
            current_value = portfolio.get_total_value()
            
            for _ in range(num_simulations):
                # 生成随机收益率
                random_returns = np.random.multivariate_normal(
                    mean_returns * time_horizon_days,
                    cov_matrix * time_horizon_days
                )
                
                # 计算投资组合价值变化
                portfolio_return = 0
                for i, symbol in enumerate(returns_data.columns):
                    if symbol in portfolio.positions:
                        weight = (portfolio.positions[symbol].market_value / 
                                current_value)
                        portfolio_return += weight * random_returns[i]
                
                simulated_value = current_value * (1 + portfolio_return)
                portfolio_values.append(simulated_value)
            
            portfolio_values = np.array(portfolio_values)
            
            # 计算风险指标
            losses = current_value - portfolio_values
            losses = losses[losses > 0]  # 只考虑损失
            
            var_95 = np.percentile(losses, 95) if len(losses) > 0 else 0
            var_99 = np.percentile(losses, 99) if len(losses) > 0 else 0
            expected_shortfall = losses[losses >= var_95].mean() if len(losses) > 0 else 0
            
            max_loss = losses.max() if len(losses) > 0 else 0
            max_loss_pct = max_loss / current_value if current_value > 0 else 0
            
            result = StressTestResult(
                test_name='蒙特卡洛模拟',
                test_type='monte_carlo',
                timestamp=datetime.now(),
                portfolio_value=current_value,
                stressed_value=portfolio_values.min(),
                max_loss=max_loss,
                max_loss_pct=max_loss_pct,
                var_95=var_95,
                var_99=var_99,
                expected_shortfall=expected_shortfall,
                recovery_days=time_horizon_days,
                worst_positions={},
                scenario_details={
                    'num_simulations': num_simulations,
                    'time_horizon_days': time_horizon_days,
                    'simulation_stats': {
                        'mean_value': portfolio_values.mean(),
                        'std_value': portfolio_values.std(),
                        'min_value': portfolio_values.min(),
                        'max_value': portfolio_values.max(),
                        'loss_probability': len(losses) / num_simulations
                    }
                }
            )
            
            logger.info(f"蒙特卡洛模拟完成: {num_simulations} 次模拟, "
                       f"VaR(95%): {var_95:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"蒙特卡洛模拟失败: {e}")
            raise
    
    async def run_historical_simulation(self, 
                                      portfolio: Portfolio,
                                      start_date: str,
                                      end_date: str) -> StressTestResult:
        """运行历史模拟"""
        try:
            # 获取历史期间的收益率数据
            historical_returns = await self._get_historical_returns_period(
                portfolio, start_date, end_date
            )
            
            if historical_returns.empty:
                raise ValueError("无法获取历史期间数据")
            
            current_value = portfolio.get_total_value()
            
            # 计算历史期间投资组合的表现
            portfolio_values = []
            
            for date, returns in historical_returns.iterrows():
                portfolio_return = 0
                for symbol in returns.index:
                    if symbol in portfolio.positions:
                        weight = (portfolio.positions[symbol].market_value / 
                                current_value)
                        portfolio_return += weight * returns[symbol]
                
                simulated_value = current_value * (1 + portfolio_return)
                portfolio_values.append(simulated_value)
            
            portfolio_values = np.array(portfolio_values)
            
            # 计算风险指标
            losses = current_value - portfolio_values
            losses = losses[losses > 0]
            
            var_95 = np.percentile(losses, 95) if len(losses) > 0 else 0
            var_99 = np.percentile(losses, 99) if len(losses) > 0 else 0
            expected_shortfall = losses[losses >= var_95].mean() if len(losses) > 0 else 0
            
            max_loss = losses.max() if len(losses) > 0 else 0
            max_loss_pct = max_loss / current_value if current_value > 0 else 0
            
            result = StressTestResult(
                test_name=f'历史模拟_{start_date}_{end_date}',
                test_type='historical',
                timestamp=datetime.now(),
                portfolio_value=current_value,
                stressed_value=portfolio_values.min(),
                max_loss=max_loss,
                max_loss_pct=max_loss_pct,
                var_95=var_95,
                var_99=var_99,
                expected_shortfall=expected_shortfall,
                recovery_days=len(portfolio_values),
                worst_positions={},
                scenario_details={
                    'start_date': start_date,
                    'end_date': end_date,
                    'num_periods': len(portfolio_values),
                    'historical_stats': {
                        'mean_value': portfolio_values.mean(),
                        'std_value': portfolio_values.std(),
                        'min_value': portfolio_values.min(),
                        'max_value': portfolio_values.max()
                    }
                }
            )
            
            logger.info(f"历史模拟完成: {start_date} 到 {end_date}, "
                       f"最大损失: {max_loss_pct:.2%}")
            
            return result
            
        except Exception as e:
            logger.error(f"历史模拟失败: {e}")
            raise
    
    async def get_stress_test_summary(self) -> Dict[str, any]:
        """获取压力测试摘要"""
        try:
            summary = {
                'timestamp': datetime.now().isoformat(),
                'available_scenarios': list(self.market_scenarios.keys()),
                'monitoring_enabled': self.monitoring_enabled,
                'alert_thresholds': self.alert_thresholds,
                'cached_results': {}
            }
            
            # 汇总缓存的测试结果
            for test_name, result in self._test_results_cache.items():
                summary['cached_results'][test_name] = {
                    'test_type': result.test_type,
                    'timestamp': result.timestamp.isoformat(),
                    'max_loss_pct': result.max_loss_pct,
                    'var_95': result.var_95,
                    'var_99': result.var_99
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取压力测试摘要失败: {e}")
            return {}
    
    async def _monitoring_loop(self, portfolio: Portfolio):
        """实时监控循环"""
        while self.monitoring_enabled:
            try:
                # 运行快速压力测试
                for scenario_name in ['financial_crisis_2008', 'covid_crash_2020']:
                    result = await self.run_scenario_stress_test(portfolio, scenario_name)
                    
                    # 检查告警条件
                    if result.max_loss_pct > self.alert_thresholds['max_loss_pct']:
                        await self._trigger_stress_alert(result)
                
                # 等待下次监控
                await asyncio.sleep(self.monitoring_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"实时监控异常: {e}")
                await asyncio.sleep(60)  # 出错时等待1分钟
    
    async def _trigger_stress_alert(self, result: StressTestResult, portfolio=None):
        """触发压力测试告警"""
        try:
            alert_message = (
                f"压力测试告警: {result.test_name}\n"
                f"最大损失: {result.max_loss_pct:.2%}\n"
                f"VaR(95%): {result.var_95:.2f}\n"
                f"预期损失: {result.expected_shortfall:.2f}"
            )
            
            logger.warning(alert_message)
            
            # 计算告警相关指标
            stress_level = self._calculate_stress_level(result)
            breach_count = 1  # 当前违约数量
            stress_results = [result]  # 当前压力测试结果
            
            # 集成告警通知系统
            await self._send_stress_alert(alert_message, {
                'stress_level': stress_level.value if hasattr(stress_level, 'value') else stress_level,
                'breach_count': breach_count,
                'total_scenarios': len(stress_results),
                'timestamp': datetime.now().isoformat(),
                'portfolio_value': portfolio.total_value if portfolio else result.portfolio_value,
                'scenario_details': [
                    {
                        'name': result.scenario_name if hasattr(result, 'scenario_name') else result.test_name,
                        'loss_pct': result.max_loss_pct,
                        'var_breach': result.max_loss_pct > 0.05  # 简单的违约判断
                    } for result in stress_results if hasattr(result, 'var_breach') and result.var_breach or result.max_loss_pct > 0.05
                ][:5]  # 只包含前5个违规场景的详情
            })
            
        except Exception as e:
            logger.error(f"触发压力测试告警失败: {e}")
    
    def _calculate_stress_level(self, result: StressTestResult) -> str:
        """计算压力水平"""
        max_loss_pct = result.max_loss_pct
        
        if max_loss_pct > 0.20:  # 20%以上损失
            return "HIGH"
        elif max_loss_pct > 0.10:  # 10-20%损失
            return "MEDIUM"
        else:
            return "LOW"
    
    async def _send_stress_alert(self, message: str, details: Dict[str, Any]):
        """发送压力测试告警"""
        try:
            # 准备告警数据
            alert_data = {
                'type': 'stress_test_alert',
                'message': message,
                'severity': 'HIGH' if details['breach_count'] > 3 else 'MEDIUM',
                'timestamp': details['timestamp'],
                'details': details
            }
            
            # 可以扩展到不同的通知渠道
            await self._send_to_alert_channels(alert_data)
            
            logger.info(f"压力测试告警已发送: {details['breach_count']} 个场景违规")
            
        except Exception as e:
            logger.error(f"发送压力测试告警失败: {e}")
    
    async def _send_to_alert_channels(self, alert_data: Dict[str, Any]):
        """发送到各种告警渠道"""
        try:
            # 日志记录（始终执行）
            logger.warning(f"风险告警: {alert_data['message']}")
            
            # 可以在这里添加其他通知方式:
            # - 邮件通知
            # - 钉钉/企业微信机器人
            # - WebSocket推送到前端
            # - 数据库记录
            
            # 示例：简单的文件记录
            import json
            alert_log_path = "data/alerts/stress_alerts.jsonl"
            import os
            os.makedirs(os.path.dirname(alert_log_path), exist_ok=True)
            
            with open(alert_log_path, "a", encoding="utf-8") as f:
                f.write(json.dumps(alert_data, ensure_ascii=False) + "\n")
                
        except Exception as e:
            logger.error(f"发送到告警渠道失败: {e}")
    
    def _get_symbol_shock_factor(self, symbol: str, shock_factors: Dict[str, float]) -> float:
        """获取股票的冲击因子"""
        # 直接匹配
        if symbol in shock_factors:
            return shock_factors[symbol]
        
        # 行业匹配（简化版）
        if symbol.startswith('XL'):  # 行业ETF
            return shock_factors.get('SPY', -0.20)  # 默认市场冲击
        
        # 默认市场冲击
        return shock_factors.get('SPY', -0.20)
    
    async def _calculate_risk_metrics(self, 
                                    portfolio: Portfolio, 
                                    scenario: MarketScenario) -> Tuple[float, float, float]:
        """计算风险指标"""
        try:
            # 简化的风险指标计算
            # 实际应用中应该使用更复杂的模型
            
            total_value = portfolio.get_total_value()
            
            # 基于情景的简化VaR计算
            scenario_impact = 0
            for symbol, position in portfolio.positions.items():
                shock_factor = self._get_symbol_shock_factor(symbol, scenario.shock_factors)
                weight = position.market_value / total_value
                scenario_impact += weight * abs(shock_factor)
            
            var_95 = total_value * scenario_impact * 0.8  # 简化计算
            var_99 = total_value * scenario_impact * 1.2
            expected_shortfall = var_95 * 1.3
            
            return var_95, var_99, expected_shortfall
            
        except Exception as e:
            logger.error(f"计算风险指标失败: {e}")
            return 0.0, 0.0, 0.0
    
    async def _get_historical_returns(self, portfolio: Portfolio, days: int = 252) -> pd.DataFrame:
        """获取历史收益率数据"""
        try:
            symbols = list(portfolio.positions.keys())
            if not symbols:
                return pd.DataFrame()
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days + 30)  # 多取一些数据
            
            # 获取价格数据
            price_data = {}
            for symbol in symbols:
                try:
                    data = await self.data_manager.get_market_data(
                        symbol=symbol,
                        start_date=start_date.strftime('%Y-%m-%d'),
                        end_date=end_date.strftime('%Y-%m-%d')
                    )
                    if not data.empty:
                        price_data[symbol] = data['close']
                except Exception as e:
                    logger.warning(f"获取 {symbol} 历史数据失败: {e}")
            
            if not price_data:
                return pd.DataFrame()
            
            # 计算收益率
            price_df = pd.DataFrame(price_data)
            returns_df = price_df.pct_change().dropna()
            
            return returns_df.tail(days)  # 返回指定天数的数据
            
        except Exception as e:
            logger.error(f"获取历史收益率数据失败: {e}")
            return pd.DataFrame()
    
    async def _get_historical_returns_period(self, 
                                           portfolio: Portfolio,
                                           start_date: str,
                                           end_date: str) -> pd.DataFrame:
        """获取指定期间的历史收益率"""
        try:
            symbols = list(portfolio.positions.keys())
            if not symbols:
                return pd.DataFrame()
            
            price_data = {}
            for symbol in symbols:
                try:
                    data = await self.data_manager.get_market_data(
                        symbol=symbol,
                        start_date=start_date,
                        end_date=end_date
                    )
                    if not data.empty:
                        price_data[symbol] = data['close']
                except Exception as e:
                    logger.warning(f"获取 {symbol} 指定期间数据失败: {e}")
            
            if not price_data:
                return pd.DataFrame()
            
            price_df = pd.DataFrame(price_data)
            returns_df = price_df.pct_change().dropna()
            
            return returns_df
            
        except Exception as e:
            logger.error(f"获取指定期间历史收益率失败: {e}")
            return pd.DataFrame()