"""
监管合规检查模块

提供全面的监管合规检查功能，包括：
- 投资限制检查
- 杠杆率监控
- 集中度风险检查
- 监管报告生成
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

from src.market.strategies.models.portfolio import Portfolio
from src.market.strategies.models.trading import Order

logger = logging.getLogger(__name__)

class ComplianceStatus(Enum):
    """合规状态"""
    COMPLIANT = "compliant"      # 合规
    WARNING = "warning"          # 警告
    VIOLATION = "violation"      # 违规
    CRITICAL = "critical"        # 严重违规

class ComplianceRule(Enum):
    """合规规则类型"""
    POSITION_LIMIT = "position_limit"           # 持仓限制
    CONCENTRATION_LIMIT = "concentration_limit" # 集中度限制
    LEVERAGE_LIMIT = "leverage_limit"           # 杠杆限制
    SECTOR_LIMIT = "sector_limit"              # 行业限制
    SINGLE_STOCK_LIMIT = "single_stock_limit"   # 单股限制
    TRADING_LIMIT = "trading_limit"             # 交易限制

@dataclass
class ComplianceCheck:
    """合规检查结果"""
    rule_type: ComplianceRule
    status: ComplianceStatus
    current_value: float
    limit_value: float
    threshold_warning: float
    message: str
    affected_symbols: List[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.affected_symbols is None:
            self.affected_symbols = []

@dataclass
class ComplianceReport:
    """合规报告"""
    portfolio_id: str
    timestamp: datetime
    overall_status: ComplianceStatus
    total_checks: int
    violations: int
    warnings: int
    compliance_score: float
    checks: List[ComplianceCheck]
    recommendations: List[str]

class ComplianceChecker:
    """监管合规检查器"""
    
    def __init__(self):
        # 默认合规规则配置
        self.compliance_rules = {
            # 单一股票持仓不超过总资产的20%
            ComplianceRule.SINGLE_STOCK_LIMIT: {
                'limit': 0.20,
                'warning_threshold': 0.15,
                'enabled': True
            },
            # 单一行业集中度不超过30%
            ComplianceRule.SECTOR_LIMIT: {
                'limit': 0.30,
                'warning_threshold': 0.25,
                'enabled': True
            },
            # 杠杆率不超过2倍
            ComplianceRule.LEVERAGE_LIMIT: {
                'limit': 2.0,
                'warning_threshold': 1.8,
                'enabled': True
            },
            # 前10大持仓集中度不超过60%
            ComplianceRule.CONCENTRATION_LIMIT: {
                'limit': 0.60,
                'warning_threshold': 0.55,
                'enabled': True
            },
            # 单日交易量不超过总资产的50%
            ComplianceRule.TRADING_LIMIT: {
                'limit': 0.50,
                'warning_threshold': 0.40,
                'enabled': True
            }
        }
        
        # 行业分类映射（简化版）
        self.sector_mapping = {
            'AAPL': 'Technology',
            'GOOGL': 'Technology',
            'MSFT': 'Technology',
            'TSLA': 'Consumer Discretionary',
            'JPM': 'Financial',
            'BAC': 'Financial',
            'JNJ': 'Healthcare',
            'PFE': 'Healthcare',
            'XOM': 'Energy',
            'CVX': 'Energy'
        }
        
        # 检查历史
        self.check_history: List[ComplianceReport] = []
        
        # 日交易量跟踪
        self.daily_trading_volume = {}
        
    async def initialize(self):
        """初始化合规检查器"""
        try:
            logger.info("监管合规检查器初始化成功")
        except Exception as e:
            logger.error(f"监管合规检查器初始化失败: {e}")
            raise
    
    async def run_compliance_check(self, portfolio: Portfolio) -> ComplianceReport:
        """运行完整合规检查"""
        try:
            checks = []
            
            # 执行各项合规检查
            if self.compliance_rules[ComplianceRule.SINGLE_STOCK_LIMIT]['enabled']:
                single_stock_checks = await self._check_single_stock_limits(portfolio)
                checks.extend(single_stock_checks)
            
            if self.compliance_rules[ComplianceRule.SECTOR_LIMIT]['enabled']:
                sector_check = await self._check_sector_concentration(portfolio)
                if sector_check:
                    checks.append(sector_check)
            
            if self.compliance_rules[ComplianceRule.LEVERAGE_LIMIT]['enabled']:
                leverage_check = await self._check_leverage_ratio(portfolio)
                if leverage_check:
                    checks.append(leverage_check)
            
            if self.compliance_rules[ComplianceRule.CONCENTRATION_LIMIT]['enabled']:
                concentration_check = await self._check_concentration_risk(portfolio)
                if concentration_check:
                    checks.append(concentration_check)
            
            if self.compliance_rules[ComplianceRule.TRADING_LIMIT]['enabled']:
                trading_check = await self._check_daily_trading_limit(portfolio)
                if trading_check:
                    checks.append(trading_check)
            
            # 生成合规报告
            report = await self._generate_compliance_report(portfolio, checks)
            
            # 保存检查历史
            self.check_history.append(report)
            
            logger.info(f"合规检查完成: {report.overall_status.value}, "
                       f"{report.violations} 违规, {report.warnings} 警告")
            
            return report
            
        except Exception as e:
            logger.error(f"合规检查失败: {e}")
            raise
    
    async def check_order_compliance(self, order: Order, portfolio: Portfolio) -> List[ComplianceCheck]:
        """检查订单合规性"""
        try:
            checks = []
            
            # 模拟订单执行后的投资组合状态
            simulated_portfolio = await self._simulate_order_execution(order, portfolio)
            
            # 检查执行后是否违规
            post_trade_checks = await self.run_compliance_check(simulated_portfolio)
            
            # 筛选出新产生的违规
            violations = [
                check for check in post_trade_checks.checks
                if check.status in [ComplianceStatus.VIOLATION, ComplianceStatus.CRITICAL]
            ]
            
            if violations:
                logger.warning(f"订单 {order.id} 可能导致 {len(violations)} 项合规违规")
            
            return violations
            
        except Exception as e:
            logger.error(f"检查订单合规性失败: {e}")
            return []
    
    async def get_compliance_summary(self, days: int = 7) -> Dict[str, any]:
        """获取合规摘要"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            recent_reports = [
                report for report in self.check_history
                if report.timestamp >= cutoff_date
            ]
            
            if not recent_reports:
                return {
                    'period_days': days,
                    'total_checks': 0,
                    'compliance_trend': [],
                    'top_violations': []
                }
            
            # 计算合规趋势
            compliance_trend = []
            for report in recent_reports[-10:]:  # 最近10次检查
                compliance_trend.append({
                    'timestamp': report.timestamp.isoformat(),
                    'score': report.compliance_score,
                    'violations': report.violations,
                    'warnings': report.warnings
                })
            
            # 统计最常见的违规类型
            violation_counts = {}
            for report in recent_reports:
                for check in report.checks:
                    if check.status in [ComplianceStatus.VIOLATION, ComplianceStatus.CRITICAL]:
                        rule_type = check.rule_type.value
                        violation_counts[rule_type] = violation_counts.get(rule_type, 0) + 1
            
            top_violations = sorted(
                violation_counts.items(),
                key=lambda x: x[1],
                reverse=True
            )[:5]
            
            return {
                'period_days': days,
                'total_checks': len(recent_reports),
                'average_compliance_score': sum(r.compliance_score for r in recent_reports) / len(recent_reports),
                'total_violations': sum(r.violations for r in recent_reports),
                'total_warnings': sum(r.warnings for r in recent_reports),
                'compliance_trend': compliance_trend,
                'top_violations': [
                    {'rule_type': rule, 'count': count}
                    for rule, count in top_violations
                ],
                'latest_status': recent_reports[-1].overall_status.value if recent_reports else None
            }
            
        except Exception as e:
            logger.error(f"获取合规摘要失败: {e}")
            return {}
    
    def update_compliance_rules(self, rule_updates: Dict[ComplianceRule, Dict]):
        """更新合规规则配置"""
        try:
            for rule_type, config in rule_updates.items():
                if rule_type in self.compliance_rules:
                    self.compliance_rules[rule_type].update(config)
                    logger.info(f"更新合规规则: {rule_type.value}")
            
        except Exception as e:
            logger.error(f"更新合规规则失败: {e}")
    
    async def _check_single_stock_limits(self, portfolio: Portfolio) -> List[ComplianceCheck]:
        """检查单一股票持仓限制"""
        checks = []
        rule = self.compliance_rules[ComplianceRule.SINGLE_STOCK_LIMIT]
        total_value = portfolio.get_total_value()
        
        for symbol, position in portfolio.positions.items():
            weight = position.market_value / total_value if total_value > 0 else 0
            
            if weight > rule['limit']:
                status = ComplianceStatus.VIOLATION
            elif weight > rule['warning_threshold']:
                status = ComplianceStatus.WARNING
            else:
                status = ComplianceStatus.COMPLIANT
            
            if status != ComplianceStatus.COMPLIANT:
                checks.append(ComplianceCheck(
                    rule_type=ComplianceRule.SINGLE_STOCK_LIMIT,
                    status=status,
                    current_value=weight,
                    limit_value=rule['limit'],
                    threshold_warning=rule['warning_threshold'],
                    message=f"{symbol} 持仓占比 {weight:.2%} {'超过限制' if status == ComplianceStatus.VIOLATION else '接近限制'} {rule['limit']:.2%}",
                    affected_symbols=[symbol]
                ))
        
        return checks
    
    async def _check_sector_concentration(self, portfolio: Portfolio) -> Optional[ComplianceCheck]:
        """检查行业集中度"""
        rule = self.compliance_rules[ComplianceRule.SECTOR_LIMIT]
        total_value = portfolio.get_total_value()
        
        # 计算行业分布
        sector_exposure = {}
        for symbol, position in portfolio.positions.items():
            sector = self.sector_mapping.get(symbol, 'Unknown')
            sector_exposure[sector] = sector_exposure.get(sector, 0) + position.market_value
        
        # 检查最大行业集中度
        max_sector_exposure = max(sector_exposure.values()) if sector_exposure else 0
        max_sector_weight = max_sector_exposure / total_value if total_value > 0 else 0
        max_sector = max(sector_exposure, key=sector_exposure.get) if sector_exposure else ""
        
        if max_sector_weight > rule['limit']:
            status = ComplianceStatus.VIOLATION
        elif max_sector_weight > rule['warning_threshold']:
            status = ComplianceStatus.WARNING
        else:
            status = ComplianceStatus.COMPLIANT
        
        if status != ComplianceStatus.COMPLIANT:
            affected_symbols = [
                symbol for symbol, position in portfolio.positions.items()
                if self.sector_mapping.get(symbol, 'Unknown') == max_sector
            ]
            
            return ComplianceCheck(
                rule_type=ComplianceRule.SECTOR_LIMIT,
                status=status,
                current_value=max_sector_weight,
                limit_value=rule['limit'],
                threshold_warning=rule['warning_threshold'],
                message=f"{max_sector} 行业集中度 {max_sector_weight:.2%} {'超过限制' if status == ComplianceStatus.VIOLATION else '接近限制'} {rule['limit']:.2%}",
                affected_symbols=affected_symbols
            )
        
        return None
    
    async def _check_leverage_ratio(self, portfolio: Portfolio) -> Optional[ComplianceCheck]:
        """检查杠杆率"""
        rule = self.compliance_rules[ComplianceRule.LEVERAGE_LIMIT]
        
        # 计算杠杆率（简化计算：总市值/净资产）
        total_market_value = sum(pos.market_value for pos in portfolio.positions.values())
        net_assets = total_market_value + portfolio.cash
        leverage_ratio = total_market_value / net_assets if net_assets > 0 else 0
        
        if leverage_ratio > rule['limit']:
            status = ComplianceStatus.VIOLATION
        elif leverage_ratio > rule['warning_threshold']:
            status = ComplianceStatus.WARNING
        else:
            status = ComplianceStatus.COMPLIANT
        
        if status != ComplianceStatus.COMPLIANT:
            return ComplianceCheck(
                rule_type=ComplianceRule.LEVERAGE_LIMIT,
                status=status,
                current_value=leverage_ratio,
                limit_value=rule['limit'],
                threshold_warning=rule['warning_threshold'],
                message=f"杠杆率 {leverage_ratio:.2f} {'超过限制' if status == ComplianceStatus.VIOLATION else '接近限制'} {rule['limit']:.2f}",
                affected_symbols=list(portfolio.positions.keys())
            )
        
        return None
    
    async def _check_concentration_risk(self, portfolio: Portfolio) -> Optional[ComplianceCheck]:
        """检查前十大持仓集中度"""
        rule = self.compliance_rules[ComplianceRule.CONCENTRATION_LIMIT]
        total_value = portfolio.get_total_value()
        
        # 按持仓价值排序，取前10大
        sorted_positions = sorted(
            portfolio.positions.items(),
            key=lambda x: x[1].market_value,
            reverse=True
        )[:10]
        
        top10_value = sum(pos.market_value for _, pos in sorted_positions)
        concentration_ratio = top10_value / total_value if total_value > 0 else 0
        
        if concentration_ratio > rule['limit']:
            status = ComplianceStatus.VIOLATION
        elif concentration_ratio > rule['warning_threshold']:
            status = ComplianceStatus.WARNING
        else:
            status = ComplianceStatus.COMPLIANT
        
        if status != ComplianceStatus.COMPLIANT:
            return ComplianceCheck(
                rule_type=ComplianceRule.CONCENTRATION_LIMIT,
                status=status,
                current_value=concentration_ratio,
                limit_value=rule['limit'],
                threshold_warning=rule['warning_threshold'],
                message=f"前10大持仓集中度 {concentration_ratio:.2%} {'超过限制' if status == ComplianceStatus.VIOLATION else '接近限制'} {rule['limit']:.2%}",
                affected_symbols=[symbol for symbol, _ in sorted_positions]
            )
        
        return None
    
    async def _check_daily_trading_limit(self, portfolio: Portfolio) -> Optional[ComplianceCheck]:
        """检查日交易量限制"""
        rule = self.compliance_rules[ComplianceRule.TRADING_LIMIT]
        today = datetime.now().date()
        
        # 获取今日交易量
        daily_volume = self.daily_trading_volume.get(today, 0.0)
        total_value = portfolio.get_total_value()
        trading_ratio = daily_volume / total_value if total_value > 0 else 0
        
        if trading_ratio > rule['limit']:
            status = ComplianceStatus.VIOLATION
        elif trading_ratio > rule['warning_threshold']:
            status = ComplianceStatus.WARNING
        else:
            status = ComplianceStatus.COMPLIANT
        
        if status != ComplianceStatus.COMPLIANT:
            return ComplianceCheck(
                rule_type=ComplianceRule.TRADING_LIMIT,
                status=status,
                current_value=trading_ratio,
                limit_value=rule['limit'],
                threshold_warning=rule['warning_threshold'],
                message=f"日交易量占比 {trading_ratio:.2%} {'超过限制' if status == ComplianceStatus.VIOLATION else '接近限制'} {rule['limit']:.2%}",
                affected_symbols=[]
            )
        
        return None
    
    async def _simulate_order_execution(self, order: Order, portfolio: Portfolio) -> Portfolio:
        """模拟订单执行后的投资组合状态"""
        try:
            # 创建投资组合副本
            simulated_portfolio = Portfolio(
                portfolio.account_id,
                portfolio.cash,
                portfolio.positions.copy()
            )
            
            # 模拟订单执行
            order_value = order.quantity * order.price
            
            if order.side.value.upper() == 'BUY':
                # 买入：减少现金，增加持仓
                simulated_portfolio.cash -= order_value
                
                if order.symbol in simulated_portfolio.positions:
                    # 已有持仓，增加数量
                    pos = simulated_portfolio.positions[order.symbol]
                    new_quantity = pos.quantity + order.quantity
                    new_avg_price = ((pos.quantity * pos.average_price) + order_value) / new_quantity
                    pos.quantity = new_quantity
                    pos.average_price = new_avg_price
                    pos.current_price = order.price
                    pos.market_value = new_quantity * order.price
                else:
                    # 新建持仓
                    from src.market.strategies.models.portfolio import Position
                    simulated_portfolio.positions[order.symbol] = Position(
                        symbol=order.symbol,
                        quantity=order.quantity,
                        average_price=order.price,
                        current_price=order.price,
                        market_value=order_value
                    )
            
            elif order.side.value.upper() == 'SELL':
                # 卖出：增加现金，减少持仓
                simulated_portfolio.cash += order_value
                
                if order.symbol in simulated_portfolio.positions:
                    pos = simulated_portfolio.positions[order.symbol]
                    pos.quantity -= order.quantity
                    if pos.quantity <= 0:
                        del simulated_portfolio.positions[order.symbol]
                    else:
                        pos.market_value = pos.quantity * order.price
                        pos.current_price = order.price
            
            return simulated_portfolio
            
        except Exception as e:
            logger.error(f"模拟订单执行失败: {e}")
            return portfolio
    
    async def _generate_compliance_report(self, portfolio: Portfolio, checks: List[ComplianceCheck]) -> ComplianceReport:
        """生成合规报告"""
        try:
            violations = len([c for c in checks if c.status == ComplianceStatus.VIOLATION])
            warnings = len([c for c in checks if c.status == ComplianceStatus.WARNING])
            criticals = len([c for c in checks if c.status == ComplianceStatus.CRITICAL])
            
            # 确定整体状态
            if criticals > 0:
                overall_status = ComplianceStatus.CRITICAL
            elif violations > 0:
                overall_status = ComplianceStatus.VIOLATION
            elif warnings > 0:
                overall_status = ComplianceStatus.WARNING
            else:
                overall_status = ComplianceStatus.COMPLIANT
            
            # 计算合规分数 (0-100)
            total_checks = len(checks) + len([r for r in self.compliance_rules.values() if r['enabled']])
            compliant_checks = total_checks - violations - warnings - criticals
            compliance_score = (compliant_checks / total_checks * 100) if total_checks > 0 else 100
            
            # 生成建议
            recommendations = await self._generate_recommendations(checks)
            
            return ComplianceReport(
                portfolio_id=portfolio.account_id,
                timestamp=datetime.now(),
                overall_status=overall_status,
                total_checks=total_checks,
                violations=violations + criticals,
                warnings=warnings,
                compliance_score=compliance_score,
                checks=checks,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"生成合规报告失败: {e}")
            raise
    
    async def _generate_recommendations(self, checks: List[ComplianceCheck]) -> List[str]:
        """生成合规建议"""
        recommendations = []
        
        for check in checks:
            if check.status in [ComplianceStatus.VIOLATION, ComplianceStatus.CRITICAL]:
                if check.rule_type == ComplianceRule.SINGLE_STOCK_LIMIT:
                    recommendations.append(f"建议减持 {', '.join(check.affected_symbols)} 以降低单股集中度风险")
                elif check.rule_type == ComplianceRule.SECTOR_LIMIT:
                    recommendations.append("建议分散行业配置，减少单一行业过度集中")
                elif check.rule_type == ComplianceRule.LEVERAGE_LIMIT:
                    recommendations.append("建议降低杠杆率，增加现金头寸或减少持仓")
                elif check.rule_type == ComplianceRule.CONCENTRATION_LIMIT:
                    recommendations.append("建议分散持仓，避免前十大持仓过度集中")
                elif check.rule_type == ComplianceRule.TRADING_LIMIT:
                    recommendations.append("建议控制交易频率，避免过度交易")
        
        return recommendations
    
    def record_trading_volume(self, date: datetime, volume: float):
        """记录交易量"""
        date_key = date.date()
        if date_key not in self.daily_trading_volume:
            self.daily_trading_volume[date_key] = 0.0
        self.daily_trading_volume[date_key] += volume