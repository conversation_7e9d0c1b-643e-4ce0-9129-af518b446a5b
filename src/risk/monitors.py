"""
Risk monitoring components for real-time risk tracking.
"""

from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
import logging
import pandas as pd
import numpy as np
from collections import deque

from .models import RiskConfig, RiskViolation, RiskLevel, RiskMetrics
from src.market.strategies.models.portfolio import Portfolio


class RiskMonitor(ABC):
    """Abstract base class for risk monitors."""
    
    def __init__(self, name: str, enabled: bool = True):
        self.name = name
        self.enabled = enabled
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.last_update = None
        self.alert_callbacks: List[Callable[[RiskViolation], None]] = []
    
    @abstractmethod
    def update(self, portfolio: Portfolio, market_data: Dict[str, float]) -> None:
        """Update monitor with latest data."""
        pass
    
    @abstractmethod
    def get_status(self) -> Dict[str, Any]:
        """Get current monitor status."""
        pass
    
    def add_alert_callback(self, callback: Callable[[RiskViolation], None]) -> None:
        """Add alert callback function."""
        self.alert_callbacks.append(callback)
    
    def _trigger_alert(self, violation: RiskViolation) -> None:
        """Trigger alert callbacks."""
        for callback in self.alert_callbacks:
            try:
                callback(violation)
            except Exception as e:
                self.logger.error(f"Error in alert callback: {e}")
    
    def enable(self) -> None:
        """Enable the monitor."""
        self.enabled = True
    
    def disable(self) -> None:
        """Disable the monitor."""
        self.enabled = False


class DrawdownMonitor(RiskMonitor):
    """Monitor for tracking portfolio drawdown in real-time."""
    
    def __init__(self, config: RiskConfig, enabled: bool = True):
        super().__init__("Drawdown Monitor", enabled)
        self.config = config
        self.peak_value = 0.0
        self.current_drawdown = 0.0
        self.max_drawdown = 0.0
        self.drawdown_start_date = None
        self.drawdown_duration = 0
        self.value_history = deque(maxlen=1000)  # Keep last 1000 values
        self.daily_values = {}  # Date -> value mapping
        
    def update(self, portfolio: Portfolio, market_data: Dict[str, float]) -> None:
        """Update drawdown calculations."""
        if not self.enabled:
            return
        
        try:
            current_value = portfolio.total_value
            current_date = datetime.now().date()
            
            # Update value history
            self.value_history.append({
                'timestamp': datetime.now(),
                'value': current_value
            })
            
            # Update daily values
            self.daily_values[current_date] = current_value
            
            # Update peak value
            if current_value > self.peak_value:
                self.peak_value = current_value
                # Reset drawdown tracking if new peak
                if self.current_drawdown < 0:
                    self.drawdown_start_date = None
                    self.drawdown_duration = 0
                self.current_drawdown = 0.0
            else:
                # Calculate current drawdown
                self.current_drawdown = (current_value - self.peak_value) / self.peak_value
                
                # Track drawdown start
                if self.drawdown_start_date is None and self.current_drawdown < 0:
                    self.drawdown_start_date = current_date
                
                # Calculate drawdown duration
                if self.drawdown_start_date:
                    self.drawdown_duration = (current_date - self.drawdown_start_date).days
                
                # Update max drawdown
                if self.current_drawdown < self.max_drawdown:
                    self.max_drawdown = self.current_drawdown
            
            # Check for violations
            self._check_violations(portfolio)
            
            self.last_update = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Error updating drawdown monitor: {e}")
    
    def _check_violations(self, portfolio: Portfolio) -> None:
        """Check for drawdown violations."""
        # Check maximum drawdown limit
        if abs(self.current_drawdown) > self.config.max_drawdown_pct:
            severity = RiskLevel.CRITICAL if abs(self.current_drawdown) > self.config.emergency_stop_drawdown_pct else RiskLevel.HIGH
            
            violation = RiskViolation(
                rule_name="Max Drawdown",
                rule_type="drawdown",
                violation_type="max_drawdown_exceeded",
                current_value=abs(self.current_drawdown),
                limit_value=self.config.max_drawdown_pct,
                severity=severity,
                timestamp=datetime.now(),
                details={
                    'peak_value': self.peak_value,
                    'current_value': portfolio.total_value,
                    'drawdown_amount': self.peak_value - portfolio.total_value,
                    'drawdown_duration_days': self.drawdown_duration
                }
            )
            
            self._trigger_alert(violation)
        
        # Check daily loss limit
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        
        if yesterday in self.daily_values and today in self.daily_values:
            daily_return = (self.daily_values[today] - self.daily_values[yesterday]) / self.daily_values[yesterday]
            
            if daily_return < -self.config.daily_loss_limit_pct:
                violation = RiskViolation(
                    rule_name="Daily Loss Limit",
                    rule_type="drawdown",
                    violation_type="daily_loss_limit_exceeded",
                    current_value=abs(daily_return),
                    limit_value=self.config.daily_loss_limit_pct,
                    severity=RiskLevel.HIGH,
                    timestamp=datetime.now(),
                    details={
                        'yesterday_value': self.daily_values[yesterday],
                        'today_value': self.daily_values[today],
                        'daily_loss_amount': self.daily_values[yesterday] - self.daily_values[today]
                    }
                )
                
                self._trigger_alert(violation)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current drawdown status."""
        return {
            'peak_value': self.peak_value,
            'current_drawdown_pct': self.current_drawdown * 100,
            'max_drawdown_pct': self.max_drawdown * 100,
            'drawdown_duration_days': self.drawdown_duration,
            'drawdown_start_date': self.drawdown_start_date.isoformat() if self.drawdown_start_date else None,
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'is_in_drawdown': self.current_drawdown < 0,
            'values_tracked': len(self.value_history)
        }
    
    def get_drawdown_series(self, days: int = 30) -> pd.Series:
        """Get drawdown series for the last N days."""
        if not self.value_history:
            return pd.Series()
        
        # Convert to DataFrame
        df = pd.DataFrame(list(self.value_history))
        df.set_index('timestamp', inplace=True)
        
        # Filter last N days
        cutoff_date = datetime.now() - timedelta(days=days)
        df = df[df.index >= cutoff_date]
        
        # Calculate drawdown series
        peak = df['value'].expanding().max()
        drawdown = (df['value'] - peak) / peak
        
        return drawdown
    
    def reset_peak(self, new_peak: Optional[float] = None) -> None:
        """Reset peak value."""
        if new_peak is not None:
            self.peak_value = new_peak
        else:
            self.peak_value = 0.0
        
        self.current_drawdown = 0.0
        self.max_drawdown = 0.0
        self.drawdown_start_date = None
        self.drawdown_duration = 0
        
        self.logger.info(f"Reset drawdown monitor peak to {self.peak_value}")


class PositionMonitor(RiskMonitor):
    """Monitor for tracking position sizes and concentration risk."""
    
    def __init__(self, config: RiskConfig, enabled: bool = True):
        super().__init__("Position Monitor", enabled)
        self.config = config
        self.position_history = {}  # symbol -> list of position data
        self.concentration_alerts = set()  # Track active concentration alerts
        
    def update(self, portfolio: Portfolio, market_data: Dict[str, float]) -> None:
        """Update position monitoring."""
        if not self.enabled:
            return
        
        try:
            # Update position history
            for symbol, position in portfolio.positions.items():
                if symbol not in self.position_history:
                    self.position_history[symbol] = deque(maxlen=100)
                
                position_pct = position.market_value / portfolio.total_value if portfolio.total_value > 0 else 0
                
                self.position_history[symbol].append({
                    'timestamp': datetime.now(),
                    'value': position.market_value,
                    'percentage': position_pct,
                    'quantity': position.quantity,
                    'price': position.last_price or position.avg_price
                })
            
            # Check for violations
            self._check_violations(portfolio)
            
            self.last_update = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Error updating position monitor: {e}")
    
    def _check_violations(self, portfolio: Portfolio) -> None:
        """Check for position-related violations."""
        # Check individual position sizes
        for symbol, position in portfolio.positions.items():
            position_pct = position.market_value / portfolio.total_value if portfolio.total_value > 0 else 0
            
            if position_pct > self.config.max_position_size_pct:
                violation = RiskViolation(
                    rule_name="Position Size Limit",
                    rule_type="position_size",
                    violation_type="max_position_size_exceeded",
                    current_value=position_pct,
                    limit_value=self.config.max_position_size_pct,
                    severity=RiskLevel.HIGH if position_pct > self.config.max_position_size_pct * 1.5 else RiskLevel.MEDIUM,
                    timestamp=datetime.now(),
                    symbol=symbol,
                    details={
                        'position_value': position.market_value,
                        'portfolio_value': portfolio.total_value,
                        'position_quantity': position.quantity
                    }
                )
                
                self._trigger_alert(violation)
        
        # Check concentration risk
        self._check_concentration_risk(portfolio)
    
    def _check_concentration_risk(self, portfolio: Portfolio) -> None:
        """Check for concentration risk violations."""
        if len(portfolio.positions) < 2:
            return
        
        # Calculate top positions concentration
        position_values = [(symbol, pos.market_value) for symbol, pos in portfolio.positions.items()]
        position_values.sort(key=lambda x: x[1], reverse=True)
        
        # Top 5 positions concentration
        top_5_value = sum(value for _, value in position_values[:5])
        top_5_pct = top_5_value / portfolio.total_value if portfolio.total_value > 0 else 0
        
        if top_5_pct > 0.8:  # 80% concentration warning
            alert_key = "top_5_concentration"
            if alert_key not in self.concentration_alerts:
                violation = RiskViolation(
                    rule_name="Concentration Risk",
                    rule_type="concentration",
                    violation_type="high_concentration",
                    current_value=top_5_pct,
                    limit_value=0.8,
                    severity=RiskLevel.MEDIUM,
                    timestamp=datetime.now(),
                    details={
                        'top_5_value': top_5_value,
                        'portfolio_value': portfolio.total_value,
                        'top_positions': position_values[:5]
                    }
                )
                
                self._trigger_alert(violation)
                self.concentration_alerts.add(alert_key)
        else:
            self.concentration_alerts.discard("top_5_concentration")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current position monitoring status."""
        status = {
            'positions_tracked': len(self.position_history),
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'active_concentration_alerts': len(self.concentration_alerts)
        }
        
        # Add position summaries
        position_summaries = {}
        for symbol, history in self.position_history.items():
            if history:
                latest = history[-1]
                position_summaries[symbol] = {
                    'current_value': latest['value'],
                    'current_percentage': latest['percentage'],
                    'current_quantity': latest['quantity'],
                    'current_price': latest['price'],
                    'history_length': len(history)
                }
        
        status['position_summaries'] = position_summaries
        return status
    
    def get_position_history(self, symbol: str, days: int = 7) -> pd.DataFrame:
        """Get position history for a specific symbol."""
        if symbol not in self.position_history:
            return pd.DataFrame()
        
        # Convert to DataFrame
        history_data = list(self.position_history[symbol])
        if not history_data:
            return pd.DataFrame()
        
        df = pd.DataFrame(history_data)
        df.set_index('timestamp', inplace=True)
        
        # Filter last N days
        cutoff_date = datetime.now() - timedelta(days=days)
        df = df[df.index >= cutoff_date]
        
        return df


class RiskBudgetMonitor(RiskMonitor):
    """Monitor for tracking risk budget utilization."""
    
    def __init__(self, config: RiskConfig, enabled: bool = True):
        super().__init__("Risk Budget Monitor", enabled)
        self.config = config
        self.daily_usage = 0.0
        self.weekly_usage = 0.0
        self.monthly_usage = 0.0
        self.usage_history = deque(maxlen=1000)
        self.period_start_dates = {
            'daily': datetime.now().date(),
            'weekly': datetime.now().date() - timedelta(days=datetime.now().weekday()),
            'monthly': datetime.now().replace(day=1).date()
        }
        
    def update(self, portfolio: Portfolio, market_data: Dict[str, float]) -> None:
        """Update risk budget monitoring."""
        if not self.enabled:
            return
        
        try:
            # Reset periods if needed
            self._reset_periods_if_needed()
            
            # Check budget utilization
            self._check_budget_violations()
            
            self.last_update = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Error updating risk budget monitor: {e}")
    
    def record_risk_usage(self, amount: float, symbol: str, strategy_id: Optional[str] = None) -> None:
        """Record risk usage."""
        try:
            # Add to usage
            self.daily_usage += amount
            self.weekly_usage += amount
            self.monthly_usage += amount
            
            # Record in history
            self.usage_history.append({
                'timestamp': datetime.now(),
                'amount': amount,
                'symbol': symbol,
                'strategy_id': strategy_id,
                'daily_total': self.daily_usage,
                'weekly_total': self.weekly_usage,
                'monthly_total': self.monthly_usage
            })
            
            self.logger.debug(f"Recorded risk usage: {amount} for {symbol}")
            
        except Exception as e:
            self.logger.error(f"Error recording risk usage: {e}")
    
    def _reset_periods_if_needed(self) -> None:
        """Reset period counters if new period started."""
        now = datetime.now()
        current_date = now.date()
        
        # Reset daily if new day
        if current_date > self.period_start_dates['daily']:
            self.daily_usage = 0.0
            self.period_start_dates['daily'] = current_date
        
        # Reset weekly if new week (Monday)
        week_start = current_date - timedelta(days=current_date.weekday())
        if week_start > self.period_start_dates['weekly']:
            self.weekly_usage = 0.0
            self.period_start_dates['weekly'] = week_start
        
        # Reset monthly if new month
        month_start = current_date.replace(day=1)
        if month_start > self.period_start_dates['monthly']:
            self.monthly_usage = 0.0
            self.period_start_dates['monthly'] = month_start
    
    def _check_budget_violations(self) -> None:
        """Check for budget violations."""
        # Check daily budget
        if self.daily_usage > self.config.daily_risk_budget:
            violation = RiskViolation(
                rule_name="Daily Risk Budget",
                rule_type="risk_budget",
                violation_type="daily_budget_exceeded",
                current_value=self.daily_usage,
                limit_value=self.config.daily_risk_budget,
                severity=RiskLevel.HIGH,
                timestamp=datetime.now(),
                details={
                    'usage_pct': (self.daily_usage / self.config.daily_risk_budget) * 100,
                    'excess_amount': self.daily_usage - self.config.daily_risk_budget
                }
            )
            
            self._trigger_alert(violation)
        
        # Check weekly budget
        if self.weekly_usage > self.config.weekly_risk_budget:
            violation = RiskViolation(
                rule_name="Weekly Risk Budget",
                rule_type="risk_budget",
                violation_type="weekly_budget_exceeded",
                current_value=self.weekly_usage,
                limit_value=self.config.weekly_risk_budget,
                severity=RiskLevel.MEDIUM,
                timestamp=datetime.now(),
                details={
                    'usage_pct': (self.weekly_usage / self.config.weekly_risk_budget) * 100,
                    'excess_amount': self.weekly_usage - self.config.weekly_risk_budget
                }
            )
            
            self._trigger_alert(violation)
        
        # Check monthly budget
        if self.monthly_usage > self.config.monthly_risk_budget:
            violation = RiskViolation(
                rule_name="Monthly Risk Budget",
                rule_type="risk_budget",
                violation_type="monthly_budget_exceeded",
                current_value=self.monthly_usage,
                limit_value=self.config.monthly_risk_budget,
                severity=RiskLevel.MEDIUM,
                timestamp=datetime.now(),
                details={
                    'usage_pct': (self.monthly_usage / self.config.monthly_risk_budget) * 100,
                    'excess_amount': self.monthly_usage - self.config.monthly_risk_budget
                }
            )
            
            self._trigger_alert(violation)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current risk budget status."""
        return {
            'daily': {
                'used': self.daily_usage,
                'limit': self.config.daily_risk_budget,
                'remaining': max(0, self.config.daily_risk_budget - self.daily_usage),
                'utilization_pct': (self.daily_usage / self.config.daily_risk_budget) * 100 if self.config.daily_risk_budget > 0 else 0,
                'period_start': self.period_start_dates['daily'].isoformat()
            },
            'weekly': {
                'used': self.weekly_usage,
                'limit': self.config.weekly_risk_budget,
                'remaining': max(0, self.config.weekly_risk_budget - self.weekly_usage),
                'utilization_pct': (self.weekly_usage / self.config.weekly_risk_budget) * 100 if self.config.weekly_risk_budget > 0 else 0,
                'period_start': self.period_start_dates['weekly'].isoformat()
            },
            'monthly': {
                'used': self.monthly_usage,
                'limit': self.config.monthly_risk_budget,
                'remaining': max(0, self.config.monthly_risk_budget - self.monthly_usage),
                'utilization_pct': (self.monthly_usage / self.config.monthly_risk_budget) * 100 if self.config.monthly_risk_budget > 0 else 0,
                'period_start': self.period_start_dates['monthly'].isoformat()
            },
            'last_update': self.last_update.isoformat() if self.last_update else None,
            'usage_records': len(self.usage_history)
        }
    
    def get_usage_history(self, days: int = 7) -> pd.DataFrame:
        """Get risk usage history."""
        if not self.usage_history:
            return pd.DataFrame()
        
        # Convert to DataFrame
        df = pd.DataFrame(list(self.usage_history))
        df.set_index('timestamp', inplace=True)
        
        # Filter last N days
        cutoff_date = datetime.now() - timedelta(days=days)
        df = df[df.index >= cutoff_date]
        
        return df