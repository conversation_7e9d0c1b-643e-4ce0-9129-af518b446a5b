# 统一测试执行体系

## 🎯 测试统一化目标

将分散的测试脚本整合为统一的pytest-based测试体系，使用markers进行分类管理。

## 📊 测试类型分类

| 测试类型 | 标记 | 描述 | 执行时间 | 并行执行 |
|---------|------|------|---------|----------|
| 单元测试 | `unit` | 测试单个模块和函数 | ~5分钟 | ✅ |
| 集成测试 | `integration` | 测试模块间协作 | ~10分钟 | ✅ |
| 端到端测试 | `e2e` | 测试完整业务流程 | ~30分钟 | ❌ |
| 性能测试 | `performance` | 测试系统性能指标 | ~60分钟 | ❌ |
| 回归测试 | `regression` | 防止功能退化 | ~15分钟 | ✅ |
| 冒烟测试 | `smoke` | 快速基本功能验证 | ~3分钟 | ✅ |

## 🛠️ 使用方法

### 基本用法

```bash
# 运行所有测试
python scripts/utilities/unified_test_runner.py --all

# 运行特定类型测试
python scripts/utilities/unified_test_runner.py --type unit
python scripts/utilities/unified_test_runner.py --type integration

# 快速冒烟测试
python scripts/utilities/unified_test_runner.py --type smoke

# 带覆盖率的单元测试
python scripts/utilities/unified_test_runner.py --type unit --coverage
```

### 高级选项

```bash
# 并行执行
python scripts/utilities/unified_test_runner.py --type unit --parallel

# 详细输出
python scripts/utilities/unified_test_runner.py --type unit --verbose

# 生成XML报告
python scripts/utilities/unified_test_runner.py --type unit --xml-output

# 调试模式（禁用超时和并行）
python scripts/utilities/unified_test_runner.py --type unit --debug
```

### Pytest直接使用

```bash
# 使用标记运行测试
pytest -m unit                    # 只运行单元测试
pytest -m "integration or e2e"    # 运行集成和端到端测试
pytest -m "not slow"              # 跳过慢测试
pytest -m "smoke and not network" # 冒烟测试，跳过网络测试

# 组合条件
pytest -m "unit and not db"       # 单元测试，跳过数据库测试
pytest -m "backend and not ui"    # 后端测试，跳过UI测试
```

## 📁 测试目录结构

```
tests/
├── unit/                  # 单元测试
├── integration/           # 集成测试  
├── end_to_end/           # 端到端测试
├── performance/          # 性能测试
├── regression/           # 回归测试
└── fixtures/             # 测试数据和工具
```

## 🔧 配置文件

- `pytest.ini` - pytest配置和标记定义
- `pyproject.toml` - 项目配置和依赖
- `conftest.py` - 共享fixture和配置

## 📊 持续集成

### GitHub Actions示例
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run unit tests
        run: python scripts/utilities/unified_test_runner.py --type unit --xml-output
      - name: Run integration tests  
        run: python scripts/utilities/unified_test_runner.py --type integration
```

## 🎯 迁移指南

### 旧脚本对应关系
- `scripts/run_e2e_tests.py` → `pytest -m e2e`
- `scripts/run_performance_tests.py` → `pytest -m performance`
- `scripts/run_system_integration_tests.py` → `pytest -m integration`
- `web_ui/backend/run_tests.py` → `pytest -m backend`

### 迁移步骤
1. 为现有测试添加适当的pytest标记
2. 更新CI/CD配置使用新的统一入口
3. 逐步移除旧的测试脚本
4. 更新文档和开发指南

---

*测试统一化完成时间: 2025年1月18日*
