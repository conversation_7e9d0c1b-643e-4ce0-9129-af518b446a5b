# 统一后端架构

## 🎯 架构优化完成

量化交易系统已成功从双后端架构迁移到统一的FastAPI后端架构。

### ✅ 迁移完成的工作

1. **移除冗余基础设施API**
   - 删除了 `src/infrastructure/api_clients/api/` 目录
   - 清理了相关的导入和依赖

2. **统一后端服务**
   - 保留 `web_ui/backend/` 作为唯一后端
   - 所有API功能现在统一通过FastAPI提供

3. **更新启动方式**
   - 简化了启动脚本，只需启动一个后端服务
   - 统一了配置管理和部署流程

### 🏗️ 新的架构

```
统一后端架构:
├── 🎯 web_ui/backend/           # 唯一后端服务（FastAPI）
│   ├── app/api/endpoints/      # API端点实现
│   ├── app/services/           # 业务服务层  
│   ├── app/models/             # 数据模型
│   └── app/core/               # 核心配置和工具
│
├── 📊 src/                     # 核心业务逻辑（直接导入）
│   ├── market/                 # 市场数据和策略
│   ├── backtest/               # 回测引擎
│   ├── risk/                   # 风险管理
│   └── core/                   # 核心引擎
│
└── 🌐 web_ui/frontend/         # React前端应用
```

### 📊 架构优势

- ✅ **简化技术栈**: 只有一个FastAPI后端
- ✅ **降低维护成本**: 统一的代码库和部署流程  
- ✅ **提高性能**: 减少了HTTP通信开销
- ✅ **统一开发体验**: 一致的开发和调试环境

### 🚀 启动方式

现在只需要一个命令就能启动整个系统：

```bash
./start_system.sh
```

这会自动启动：
- FastAPI后端服务 (端口8000)
- React前端服务 (端口3000)

### 🔧 开发指南

1. **后端开发**: 在 `web_ui/backend/` 目录下开发
2. **业务逻辑**: 在 `src/` 目录下开发，通过导入在后端中使用
3. **前端开发**: 在 `web_ui/frontend/` 目录下开发

### 📋 API文档

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### 🔄 版本历史

- **v1.0**: 双后端架构（Flask基础设施API + FastAPI Web UI后端）
- **v2.0**: 统一FastAPI后端架构 ✅

---

*架构迁移完成时间: 2025年1月18日*
