import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
合约交易策略分析工具
专门针对杠杆交易的技术分析和风险管理
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.market.data.adapters.binance_adapter import BinanceAdapter
from src.market.data.adapters.base import DataSourceConfig

class ContractTradingAnalyzer:
    """合约交易分析器"""
    
    def __init__(self):
        # 适合合约交易的币种 (流动性好、波动适中)
        self.contract_suitable_coins = {
            '🥇 主力合约': ['BTCUSDT', 'ETHUSDT', 'BNBUSDT'],
            '🔥 热门合约': ['SOLUSDT', 'XRPUSDT', 'ADAUSDT', 'DOGEUSDT'],
            '⚡ 高波动合约': ['PEPEUSDT', 'SHIBUSDT', 'APTUSDT', 'INJUSDT'],
            '🏦 DeFi合约': ['UNIUSDT', 'AAVEUSDT', 'MKRUSDT'],
            '🎮 概念合约': ['MANAUSDT', 'SANDUSDT', 'AXSUSDT']
        }
        
        # 杠杆倍数建议
        self.leverage_recommendations = {
            'BTCUSDT': {'max': 10, 'safe': 3, 'aggressive': 5},
            'ETHUSDT': {'max': 10, 'safe': 3, 'aggressive': 5},
            'BNBUSDT': {'max': 10, 'safe': 3, 'aggressive': 5},
            'SOLUSDT': {'max': 20, 'safe': 5, 'aggressive': 10},
            'XRPUSDT': {'max': 20, 'safe': 5, 'aggressive': 10},
            'ADAUSDT': {'max': 20, 'safe': 5, 'aggressive': 10},
            'DOGEUSDT': {'max': 25, 'safe': 5, 'aggressive': 15},
            '默认': {'max': 20, 'safe': 3, 'aggressive': 8}
        }
    
    def analyze_contract_opportunities(self):
        """分析合约交易机会"""
        logger.info("=" * 120)
        logger.info("⚡ 合约交易策略分析 - 杠杆交易机会识别")
        logger.info("=" * 120)
        
        try:
            # 初始化适配器
            logger.info("🔌 正在连接币安API...")
            config = DataSourceConfig(
                name='contract_trading_analysis',
                adapter_class='src.data.adapters.binance_adapter.BinanceAdapter',
                enabled=True,
                priority=1,
                rate_limit={
                    'requests_per_minute': 30,
                    'requests_per_second': 5
                },
                credentials={
                    'api_key': '',
                    'api_secret': ''
                },
                default_params={}
            )
            
            adapter = BinanceAdapter(config)
            
            # 健康检查
            is_healthy, message = adapter.health_check()
            if not is_healthy:
                logger.info(f"❌ API连接失败: {message}")
                return
            
            logger.info("✅ API连接成功")
            
            # 获取市场数据
            logger.info("📊 正在获取合约交易数据...")
            all_tickers_data = adapter.get_24hr_ticker()
            
            if not all_tickers_data:
                logger.info("❌ 无法获取市场数据")
                return
            
            logger.info(f"✅ 成功获取市场数据")
            
            # 分析合约机会
            contract_analyses = []
            all_coins = []
            for category, coins in self.contract_suitable_coins.items():
                all_coins.extend(coins)
            
            for symbol in set(all_coins):
                if symbol in all_tickers_data:
                    analysis = self.analyze_contract_signal(symbol, all_tickers_data[symbol])
                    if analysis:
                        contract_analyses.append(analysis)
            
            if not contract_analyses:
                logger.info("❌ 没有获取到有效的合约分析数据")
                return
            
            # 显示分析结果
            self.display_contract_opportunities(contract_analyses)
            self.display_long_short_signals(contract_analyses)
            self.display_risk_management_advice(contract_analyses)
            self.display_position_sizing_guide(contract_analyses)
            
        except Exception as e:
            logger.info(f"❌ 合约分析出现错误: {e}")
            import traceback
            traceback.print_exc()
    
    def analyze_contract_signal(self, symbol, ticker):
        """分析合约交易信号"""
        try:
            # 基础数据
            price_change = ticker['price_change_percent']
            current_price = ticker['last_price']
            volume = ticker['quote_volume']
            high_price = ticker['high_price']
            low_price = ticker['low_price']
            
            # 计算技术指标
            volatility = (high_price - low_price) / low_price * 100 if low_price > 0 else 0
            price_position = (current_price - low_price) / (high_price - low_price) if high_price > low_price else 0.5
            
            # 合约交易评分
            long_score = 0  # 做多分数
            short_score = 0  # 做空分数
            signals = []
            
            # 价格趋势分析
            if price_change > 8:
                long_score += 25
                signals.append("强势上涨趋势 - 追涨做多")
            elif price_change > 3:
                long_score += 15
                signals.append("温和上涨 - 回调做多")
            elif price_change > 0:
                long_score += 5
                signals.append("微涨 - 观望")
            elif price_change > -3:
                short_score += 5
                signals.append("微跌 - 观望")
            elif price_change > -8:
                short_score += 15
                long_score += 10  # 超跌反弹机会
                signals.append("温和下跌 - 可做空或抄底")
            else:
                short_score += 25
                long_score += 20  # 深度超跌
                signals.append("深度下跌 - 做空或抄底")
            
            # 价格位置分析
            if price_position > 0.9:
                short_score += 20
                signals.append("价格高位 - 做空机会")
            elif price_position > 0.7:
                short_score += 10
                signals.append("价格偏高 - 谨慎做多")
            elif price_position < 0.1:
                long_score += 20
                signals.append("价格低位 - 做多机会")
            elif price_position < 0.3:
                long_score += 10
                signals.append("价格偏低 - 逢低做多")
            
            # 波动率分析
            if volatility > 15:
                long_score += 10
                short_score += 10
                signals.append(f"高波动率{volatility:.1f}% - 适合短线")
            elif volatility > 8:
                long_score += 5
                short_score += 5
                signals.append(f"中等波动率{volatility:.1f}% - 日内交易")
            elif volatility < 3:
                long_score -= 5
                short_score -= 5
                signals.append(f"低波动率{volatility:.1f}% - 不适合合约")
            
            # 成交量分析
            if volume > 500000000:  # 5亿以上
                long_score += 15
                short_score += 15
                signals.append("超高流动性 - 合约首选")
            elif volume > 100000000:  # 1亿以上
                long_score += 10
                short_score += 10
                signals.append("高流动性 - 适合合约")
            elif volume < 20000000:  # 小于2000万
                long_score -= 10
                short_score -= 10
                signals.append("流动性不足 - 谨慎操作")
            
            # 确定主要方向
            if long_score > short_score + 10:
                primary_direction = "做多"
                confidence = long_score
            elif short_score > long_score + 10:
                primary_direction = "做空"
                confidence = short_score
            else:
                primary_direction = "观望"
                confidence = max(long_score, short_score)
            
            # 杠杆建议
            leverage_info = self.leverage_recommendations.get(symbol, self.leverage_recommendations['默认'])
            
            # 根据波动率调整杠杆
            if volatility > 20:
                recommended_leverage = max(1, leverage_info['safe'] - 2)
                risk_level = "极高"
            elif volatility > 10:
                recommended_leverage = leverage_info['safe']
                risk_level = "高"
            elif volatility > 5:
                recommended_leverage = leverage_info['aggressive']
                risk_level = "中"
            else:
                recommended_leverage = leverage_info['max']
                risk_level = "低"
            
            return {
                'symbol': symbol,
                'current_price': current_price,
                'price_change_24h': price_change,
                'volatility': volatility,
                'volume_usdt': volume,
                'price_position': price_position,
                'long_score': long_score,
                'short_score': short_score,
                'primary_direction': primary_direction,
                'confidence': confidence,
                'recommended_leverage': recommended_leverage,
                'max_leverage': leverage_info['max'],
                'risk_level': risk_level,
                'signals': signals
            }
            
        except Exception as e:
            return None
    
    def display_contract_opportunities(self, analyses):
        """显示合约交易机会"""
        logger.info(f"\n" + "=" * 120)
        logger.info("🎯 合约交易机会排名")
        logger.info("=" * 120)
        
        # 按信心指数排序
        sorted_analyses = sorted(analyses, key=lambda x: x['confidence'], reverse=True)
        
        logger.info(f"{'排名':<4} {'币种':<8} {'方向':<8} {'信心':<4} {'杠杆':<6} {'24h变化':<10} {'当前价格':<12} {'风险':<6} {'主要信号'}")
        logger.info("-" * 120)
        
        for i, analysis in enumerate(sorted_analyses[:15], 1):
            symbol_name = analysis['symbol'].replace('USDT', '')
            
            # 方向颜色
            if analysis['primary_direction'] == "做多":
                direction_emoji = "🟢"
            elif analysis['primary_direction'] == "做空":
                direction_emoji = "🔴"
            else:
                direction_emoji = "⚪"
            
            # 信心等级
            if analysis['confidence'] >= 40:
                confidence_level = "很高"
            elif analysis['confidence'] >= 25:
                confidence_level = "高"
            elif analysis['confidence'] >= 15:
                confidence_level = "中"
            else:
                confidence_level = "低"
            
            logger.info(f"{i:2d}   {symbol_name:<8} {direction_emoji}{analysis['primary_direction']:<7} "
                  f"{confidence_level:<4} {analysis['recommended_leverage']:2d}x    "
                  f"{analysis['price_change_24h']:+6.2f}%   ${analysis['current_price']:>10.4f} "
                  f"{analysis['risk_level']:<6} {analysis['signals'][0] if analysis['signals'] else ''}")
    
    def display_long_short_signals(self, analyses):
        """显示做多做空信号"""
        logger.info(f"\n" + "=" * 120)
        logger.info("📈📉 做多做空信号分析")
        logger.info("=" * 120)
        
        # 分离做多做空机会
        long_opportunities = [a for a in analyses if a['primary_direction'] == "做多" and a['confidence'] >= 20]
        short_opportunities = [a for a in analyses if a['primary_direction'] == "做空" and a['confidence'] >= 20]
        
        logger.info("🟢 做多机会:")
        if long_opportunities:
            long_opportunities.sort(key=lambda x: x['long_score'], reverse=True)
            for i, analysis in enumerate(long_opportunities[:5], 1):
                symbol_name = analysis['symbol'].replace('USDT', '')
                logger.info(f"   {i}. {symbol_name:<8}: 做多分数{analysis['long_score']:2d} | "
                      f"建议杠杆{analysis['recommended_leverage']:2d}x | "
                      f"入场价位${analysis['current_price']:.4f} | "
                      f"止损位置{analysis['price_position']*0.95:.1%}")
        else:
            logger.info("   当前没有强势做多机会")
        
        logger.info(f"\n🔴 做空机会:")
        if short_opportunities:
            short_opportunities.sort(key=lambda x: x['short_score'], reverse=True)
            for i, analysis in enumerate(short_opportunities[:5], 1):
                symbol_name = analysis['symbol'].replace('USDT', '')
                logger.info(f"   {i}. {symbol_name:<8}: 做空分数{analysis['short_score']:2d} | "
                      f"建议杠杆{analysis['recommended_leverage']:2d}x | "
                      f"入场价位${analysis['current_price']:.4f} | "
                      f"止损位置{analysis['price_position']*1.05:.1%}")
        else:
            logger.info("   当前没有强势做空机会")
        
        # 统计信息
        logger.info(f"\n📊 信号统计:")
        total_long = len([a for a in analyses if a['long_score'] > a['short_score']])
        total_short = len([a for a in analyses if a['short_score'] > a['long_score']])
        total_neutral = len(analyses) - total_long - total_short
        
        logger.info(f"   做多信号: {total_long} 个 ({total_long/len(analyses)*100:.1f}%)")
        logger.info(f"   做空信号: {total_short} 个 ({total_short/len(analyses)*100:.1f}%)")
        logger.info(f"   观望信号: {total_neutral} 个 ({total_neutral/len(analyses)*100:.1f}%)")
        
        # 市场情绪
        if total_long > total_short * 1.5:
            market_sentiment = "偏多头 🐂"
        elif total_short > total_long * 1.5:
            market_sentiment = "偏空头 🐻"
        else:
            market_sentiment = "中性震荡 ⚖️"
        
        logger.info(f"   市场情绪: {market_sentiment}")
    
    def display_risk_management_advice(self, analyses):
        """显示风险管理建议"""
        logger.info(f"\n" + "=" * 120)
        logger.info("⚠️ 合约交易风险管理")
        logger.info("=" * 120)
        
        # 风险等级分类
        high_risk_coins = [a for a in analyses if a['risk_level'] == "极高"]
        medium_risk_coins = [a for a in analyses if a['risk_level'] in ["高", "中"]]
        low_risk_coins = [a for a in analyses if a['risk_level'] == "低"]
        
        logger.info("🔴 极高风险币种 (波动率>20%):")
        if high_risk_coins:
            for coin in high_risk_coins:
                symbol_name = coin['symbol'].replace('USDT', '')
                logger.info(f"   • {symbol_name}: 波动率{coin['volatility']:.1f}%, 最大建议杠杆{coin['recommended_leverage']}x")
                logger.info(f"     ⚠️ 建议: 小仓位试探, 严格止损5%, 快进快出")
        else:
            logger.info("   无")
        
        logger.info(f"\n🟡 中等风险币种:")
        for coin in medium_risk_coins[:3]:
            symbol_name = coin['symbol'].replace('USDT', '')
            logger.info(f"   • {symbol_name}: 波动率{coin['volatility']:.1f}%, 建议杠杆{coin['recommended_leverage']}x")
        
        logger.info(f"\n🟢 相对安全币种:")
        for coin in low_risk_coins[:3]:
            symbol_name = coin['symbol'].replace('USDT', '')
            logger.info(f"   • {symbol_name}: 波动率{coin['volatility']:.1f}%, 可用杠杆{coin['recommended_leverage']}x")
        
        logger.info(f"\n💡 核心风险管理原则:")
        logger.info("   1. 🛡️  止损设置: 永远设置止损，建议3-8%")
        logger.info("   2. 💰 仓位管理: 单笔合约资金不超过总资金的10%")
        logger.info("   3. ⚡ 杠杆控制: 新手建议3-5倍，老手不超过20倍")
        logger.info("   4. ⏰ 时间管理: 避免隔夜持仓，控制持仓时间")
        logger.info("   5. 🎯 止盈策略: 达到目标盈利的50%时减仓一半")
        logger.info("   6. 📊 资金管理: 连续亏损3次后暂停交易")
        logger.info("   7. 💭 心态管理: 不要报复性交易，控制贪婪恐惧")
    
    def display_position_sizing_guide(self, analyses):
        """显示仓位管理指南"""
        logger.info(f"\n" + "=" * 120)
        logger.info("📊 仓位管理与开仓指南")
        logger.info("=" * 120)
        
        logger.info("💰 资金分配建议 (以10,000 USDT为例):")
        logger.info("   总合约资金: 3,000 USDT (30%)")
        logger.info("   单笔最大: 500 USDT (5%)")
        logger.info("   同时持仓: 最多3个品种")
        logger.info("   紧急资金: 2,000 USDT (应对爆仓)")
        
        logger.info(f"\n⚡ 具体开仓建议:")
        
        # 推荐开仓的币种
        recommended_positions = sorted(analyses, key=lambda x: x['confidence'], reverse=True)[:5]
        
        for i, pos in enumerate(recommended_positions, 1):
            symbol_name = pos['symbol'].replace('USDT', '')
            direction = pos['primary_direction']
            
            if direction == "观望":
                continue
            
            # 计算具体参数
            entry_price = pos['current_price']
            leverage = pos['recommended_leverage']
            
            if direction == "做多":
                stop_loss_price = entry_price * 0.92  # 8% 止损
                take_profit_price = entry_price * 1.15  # 15% 止盈
            else:
                stop_loss_price = entry_price * 1.08  # 8% 止损
                take_profit_price = entry_price * 0.85  # 15% 止盈
            
            logger.info(f"\n   {i}. {symbol_name} - {direction}策略:")
            logger.info(f"      💸 建议本金: 300-500 USDT")
            logger.info(f"      ⚡ 杠杆倍数: {leverage}x")
            logger.info(f"      📍 开仓价位: ${entry_price:.4f}")
            logger.info(f"      🛑 止损价位: ${stop_loss_price:.4f} (-8%)")
            logger.info(f"      🎯 止盈价位: ${take_profit_price:.4f} (+15%)")
            logger.info(f"      ⏱️  建议时长: 1-4小时 (日内交易)")
            logger.info(f"      🎪 风险等级: {pos['risk_level']}")
        
        logger.info(f"\n📋 实战操作流程:")
        logger.info("   1. 📱 选择交易所: 币安、OKX或Bybit")
        logger.info("   2. ⚙️  设置杠杆: 根据上述建议调整")
        logger.info("   3. 💰 投入资金: 不超过总资金5%")
        logger.info("   4. 📍 设置止损: 开仓同时设置止损单")
        logger.info("   5. 🎯 设置止盈: 分批止盈，保护利润")
        logger.info("   6. 📊 监控仓位: 实时关注价格变化")
        logger.info("   7. ⏰ 及时平仓: 不要贪婪，见好就收")
        
        logger.info(f"\n🚨 紧急情况处理:")
        logger.info("   • 爆仓预警: 保证金率低于20%立即减仓")
        logger.info("   • 连续亏损: 亏损3次后休息1天")
        logger.info("   • 市场异常: 重大消息时立即平仓观望")
        logger.info("   • 技术故障: 网络问题时优先平仓")
        
        logger.info(f"\n📅 交易时间安排:")
        logger.info("   🌅 亚洲时段 (9:00-17:00): 相对平稳，适合趋势跟随")
        logger.info("   🌍 欧洲时段 (15:00-23:00): 波动加大，注意风险")
        logger.info("   🌎 美洲时段 (21:00-5:00): 高波动时期，短线机会多")
        logger.info("   🌙 深夜时段: 建议避免开新仓，流动性差")

def main():
    """主函数"""
    analyzer = ContractTradingAnalyzer()
    analyzer.analyze_contract_opportunities()
    
    logger.info(f"\n" + "=" * 120)
    logger.info("⚠️  最终风险提醒")
    logger.info("=" * 120)
    logger.info("🚨 合约交易属于高风险投资，可能导致本金全部损失")
    logger.info("💡 建议新手先用小资金练习，熟悉后再加大投入")
    logger.info("📚 持续学习技术分析和风险管理知识")
    logger.info("🧘 保持冷静心态，避免情绪化交易")
    logger.info("💰 永远不要借钱或用生活必需资金进行合约交易")
    logger.info(f"\n📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("🔄 建议每30分钟更新一次分析数据")

if __name__ == '__main__':
    main()