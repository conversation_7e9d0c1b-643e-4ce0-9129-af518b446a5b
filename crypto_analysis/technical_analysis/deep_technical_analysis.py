import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
深度技术分析和基本面研究
对推荐币种进行全面的量化分析
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.market.data.adapters.binance_adapter import BinanceAdapter
from src.market.data.adapters.base import DataSourceConfig

class TechnicalAnalysis:
    """技术分析工具类"""
    
    @staticmethod
    def calculate_rsi(prices, window=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.fillna(50)
    
    @staticmethod
    def calculate_macd(prices, fast=12, slow=26, signal=9):
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line
        return macd, signal_line, histogram
    
    @staticmethod
    def calculate_bollinger_bands(prices, window=20, num_std=2):
        """计算布林带"""
        sma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper_band = sma + (std * num_std)
        lower_band = sma - (std * num_std)
        return upper_band, sma, lower_band
    
    @staticmethod
    def calculate_moving_averages(prices):
        """计算移动平均线"""
        return {
            'sma_10': prices.rolling(window=10).mean(),
            'sma_20': prices.rolling(window=20).mean(),
            'sma_50': prices.rolling(window=50).mean(),
            'sma_200': prices.rolling(window=200).mean(),
            'ema_12': prices.ewm(span=12).mean(),
            'ema_26': prices.ewm(span=26).mean()
        }
    
    @staticmethod
    def find_support_resistance(prices, window=20):
        """寻找支撑和阻力位"""
        highs = prices.rolling(window=window, center=True).max()
        lows = prices.rolling(window=window, center=True).min()
        
        resistance_levels = []
        support_levels = []
        
        for i in range(window, len(prices) - window):
            if prices.iloc[i] == highs.iloc[i]:
                resistance_levels.append(prices.iloc[i])
            if prices.iloc[i] == lows.iloc[i]:
                support_levels.append(prices.iloc[i])
        
        # 去重并排序
        resistance_levels = sorted(list(set(resistance_levels)), reverse=True)[:5]
        support_levels = sorted(list(set(support_levels)))[:5]
        
        return support_levels, resistance_levels

def comprehensive_analysis():
    """综合分析函数"""
    logger.info("=" * 100)
    logger.info("🔬 深度技术分析和基本面研究")
    logger.info("=" * 100)
    
    # 创建配置
    config = DataSourceConfig(
        name='comprehensive_analysis',
        adapter_class='src.data.adapters.binance_adapter.BinanceAdapter',
        enabled=True,
        priority=1,
        rate_limit={
            'requests_per_minute': 20,
            'requests_per_second': 3
        },
        credentials={
            'api_key': '',
            'api_secret': ''
        },
        default_params={}
    )
    
    try:
        # 初始化适配器
        logger.info("🔌 正在连接币安API...")
        adapter = BinanceAdapter(config)
        
        # 健康检查
        is_healthy, message = adapter.health_check()
        if not is_healthy:
            logger.info(f"❌ API连接失败: {message}")
            return
        
        logger.info("✅ API连接成功\n")
        
        # 重点分析币种 (基于之前的推荐)
        focus_coins = ['BTCUSDT', 'ETHUSDT', 'ATOMUSDT']
        
        # 分析时间框架
        analysis_periods = {
            '短期(7天)': 7,
            '中期(30天)': 30,
            '长期(90天)': 90,
            '超长期(180天)': 180
        }
        
        ta = TechnicalAnalysis()
        
        for symbol in focus_coins:
            logger.info(f"📊 正在分析 {symbol.replace('USDT', '')} ({symbol})")
            logger.info("=" * 80)
            
            try:
                # 获取不同周期的数据
                end_date = datetime.now()
                
                # 获取180天数据用于长期分析
                start_date = end_date - timedelta(days=180)
                data = adapter.fetch_historical_data(symbol, start_date, end_date, '1d')
                
                if not data or len(data) < 50:
                    logger.info(f"⚠️ {symbol} 数据不足，跳过分析\n")
                    continue
                
                # 转换为DataFrame
                df = pd.DataFrame([{
                    'timestamp': d.timestamp,
                    'open': d.open,
                    'high': d.high,
                    'low': d.low,
                    'close': d.close,
                    'volume': d.volume
                } for d in data])
                
                df.set_index('timestamp', inplace=True)
                df.sort_index(inplace=True)
                
                # 1. 技术指标计算
                logger.info("🔍 技术指标分析:")
                
                # RSI
                df['rsi'] = ta.calculate_rsi(df['close'])
                current_rsi = df['rsi'].iloc[-1]
                rsi_trend = "超买" if current_rsi > 70 else "超卖" if current_rsi < 30 else "中性"
                logger.info(f"   RSI(14): {current_rsi:.1f} - {rsi_trend}")
                
                # MACD
                macd, signal, histogram = ta.calculate_macd(df['close'])
                df['macd'] = macd
                df['macd_signal'] = signal
                df['macd_histogram'] = histogram
                
                current_macd = df['macd'].iloc[-1]
                current_signal = df['macd_signal'].iloc[-1]
                macd_trend = "看涨" if current_macd > current_signal else "看跌"
                macd_momentum = "增强" if df['macd_histogram'].iloc[-1] > df['macd_histogram'].iloc[-2] else "减弱"
                logger.info(f"   MACD: {current_macd:.4f} vs 信号线 {current_signal:.4f} - {macd_trend} ({macd_momentum})")
                
                # 布林带
                bb_upper, bb_middle, bb_lower = ta.calculate_bollinger_bands(df['close'])
                df['bb_upper'] = bb_upper
                df['bb_middle'] = bb_middle
                df['bb_lower'] = bb_lower
                
                current_price = df['close'].iloc[-1]
                bb_position = (current_price - df['bb_lower'].iloc[-1]) / (df['bb_upper'].iloc[-1] - df['bb_lower'].iloc[-1]) * 100
                bb_signal = "接近上轨" if bb_position > 80 else "接近下轨" if bb_position < 20 else "中性区域"
                logger.info(f"   布林带位置: {bb_position:.1f}% - {bb_signal}")
                
                # 移动平均线
                mas = ta.calculate_moving_averages(df['close'])
                for name, ma in mas.items():
                    df[name] = ma
                
                # 均线排列分析
                sma_alignment = []
                if df['close'].iloc[-1] > df['sma_20'].iloc[-1]:
                    sma_alignment.append("价格 > SMA20")
                if df['sma_20'].iloc[-1] > df['sma_50'].iloc[-1]:
                    sma_alignment.append("SMA20 > SMA50")
                if df['sma_50'].iloc[-1] > df['sma_200'].iloc[-1]:
                    sma_alignment.append("SMA50 > SMA200")
                
                alignment_strength = len(sma_alignment)
                trend_strength = "强势多头" if alignment_strength == 3 else "温和多头" if alignment_strength == 2 else "震荡或空头" if alignment_strength <= 1 else "混合"
                logger.info(f"   均线排列: {' | '.join(sma_alignment)} - {trend_strength}")
                
                # 2. 支撑阻力位分析
                logger.info(f"\n📈 支撑阻力位分析:")
                support_levels, resistance_levels = ta.find_support_resistance(df['close'])
                
                if support_levels:
                    nearest_support = max([s for s in support_levels if s < current_price], default=min(support_levels))
                    support_distance = (current_price - nearest_support) / current_price * 100
                    logger.info(f"   最近支撑位: ${nearest_support:.4f} (距离: -{support_distance:.1f}%)")
                
                if resistance_levels:
                    nearest_resistance = min([r for r in resistance_levels if r > current_price], default=max(resistance_levels))
                    resistance_distance = (nearest_resistance - current_price) / current_price * 100
                    logger.info(f"   最近阻力位: ${nearest_resistance:.4f} (距离: +{resistance_distance:.1f}%)")
                
                # 3. 多时间框架分析
                logger.info(f"\n📅 多时间框架表现:")
                for period_name, days in analysis_periods.items():
                    if len(df) >= days:
                        period_start = df.iloc[-days]['close']
                        period_change = (current_price - period_start) / period_start * 100
                        trend = "上涨" if period_change > 0 else "下跌"
                        logger.info(f"   {period_name}: {period_change:+.2f}% ({trend})")
                
                # 4. 波动率和风险分析
                logger.info(f"\n⚡ 波动率分析:")
                volatilities = {}
                for period_name, days in analysis_periods.items():
                    if len(df) >= days:
                        period_returns = df['close'][-days:].pct_change().dropna()
                        volatility = period_returns.std() * np.sqrt(365) * 100  # 年化波动率
                        volatilities[period_name] = volatility
                        risk_level = "高" if volatility > 100 else "中" if volatility > 50 else "低"
                        logger.info(f"   {period_name}年化波动率: {volatility:.1f}% (风险: {risk_level})")
                
                # 5. 成交量分析
                logger.info(f"\n📊 成交量分析:")
                current_volume = df['volume'].iloc[-1]
                avg_volume_30d = df['volume'][-30:].mean()
                volume_ratio = current_volume / avg_volume_30d
                volume_trend = "放量" if volume_ratio > 1.5 else "缩量" if volume_ratio < 0.5 else "正常"
                logger.info(f"   当前成交量: {current_volume:,.0f}")
                logger.info(f"   30日平均成交量: {avg_volume_30d:,.0f}")
                logger.info(f"   成交量比率: {volume_ratio:.2f}x ({volume_trend})")
                
                # 6. 综合评分
                logger.info(f"\n🎯 综合技术评分:")
                score = 0
                signals = []
                
                # RSI评分
                if 30 <= current_rsi <= 70:
                    score += 15
                    signals.append("RSI中性区间(+15)")
                elif current_rsi < 30:
                    score += 25
                    signals.append("RSI超卖买入信号(+25)")
                else:
                    score -= 10
                    signals.append("RSI超买警告(-10)")
                
                # MACD评分
                if current_macd > current_signal and df['macd_histogram'].iloc[-1] > 0:
                    score += 20
                    signals.append("MACD多头信号(+20)")
                elif current_macd < current_signal and df['macd_histogram'].iloc[-1] < 0:
                    score -= 15
                    signals.append("MACD空头信号(-15)")
                
                # 均线评分
                score += alignment_strength * 10
                signals.append(f"均线排列({alignment_strength}/3)({alignment_strength*10:+d})")
                
                # 布林带评分
                if 20 <= bb_position <= 80:
                    score += 10
                    signals.append("布林带中性(+10)")
                elif bb_position < 20:
                    score += 15
                    signals.append("布林带下轨支撑(+15)")
                else:
                    score -= 5
                    signals.append("布林带上轨压力(-5)")
                
                # 成交量评分
                if volume_ratio > 1.2:
                    score += 10
                    signals.append("成交量配合(+10)")
                elif volume_ratio < 0.8:
                    score -= 5
                    signals.append("成交量不配合(-5)")
                
                logger.info(f"   总得分: {score}/100")
                logger.info(f"   评级: {'A级(推荐买入)' if score >= 70 else 'B级(谨慎买入)' if score >= 40 else 'C级(观望)' if score >= 20 else 'D级(避险)'}")
                
                logger.info(f"   信号详情:")
                for signal in signals:
                    logger.info(f"     • {signal}")
                
                # 7. 投资建议
                logger.info(f"\n💡 投资建议:")
                
                if score >= 70:
                    logger.info("   🟢 强烈推荐: 技术面显示强势，适合积极买入")
                    if nearest_support:
                        logger.info(f"   📍 建议买入价: ${nearest_support:.4f} 附近 (支撑位)")
                    if nearest_resistance:
                        logger.info(f"   🎯 目标价位: ${nearest_resistance:.4f} (阻力位)")
                elif score >= 40:
                    logger.info("   🟡 谨慎推荐: 技术面中性偏多，可以小仓位试探")
                    logger.info("   📍 建议分批买入，严格止损")
                elif score >= 20:
                    logger.info("   ⚪ 观望为主: 技术面信号混合，等待更明确信号")
                else:
                    logger.info("   🔴 建议避险: 技术面偏空，暂不建议买入")
                
                # 风险提示
                if volatilities.get('短期(7天)', 0) > 80:
                    logger.info("   ⚠️ 风险提醒: 短期波动率较高，注意仓位控制")
                
                logger.info("\n" + "="*80 + "\n")
                
            except Exception as e:
                logger.info(f"❌ 分析 {symbol} 时出错: {e}\n")
                continue
        
        # 基本面分析
        logger.info("📰 基本面研究:")
        logger.info("=" * 80)
        
        fundamental_analysis = {
            'BTC': {
                '技术创新': '比特币作为数字黄金地位稳固，机构采用持续增加',
                '监管环境': '美国ETF通过，监管环境逐步明朗',
                '市场地位': '加密货币龙头，市值占比约40%',
                '采用情况': '萨尔瓦多等国家法定货币，企业储备资产',
                '风险因素': '能耗争议，监管不确定性'
            },
            'ETH': {
                '技术创新': 'POS共识机制，Layer2生态蓬勃发展',
                '监管环境': 'ETF即将通过，机构认可度提升',
                '市场地位': 'DeFi和NFT生态核心，智能合约平台领导者',
                '采用情况': '大量DApp和协议基于以太坊构建',
                '风险因素': '网络拥堵，高Gas费用'
            },
            'ATOM': {
                '技术创新': 'Cosmos生态核心，跨链技术领先',
                '监管环境': '监管风险相对较低',
                '市场地位': '跨链赛道重要项目，IBC协议创新',
                '采用情况': '多个项目使用Cosmos SDK构建',
                '风险因素': '市场认知度有待提升，竞争激烈'
            }
        }
        
        for coin, analysis in fundamental_analysis.items():
            logger.info(f"\n🏢 {coin} 基本面分析:")
            for aspect, content in analysis.items():
                logger.info(f"   {aspect}: {content}")
        
        # 市场环境分析
        logger.info(f"\n🌍 当前市场环境:")
        logger.info("   📈 美联储政策: 降息预期支撑风险资产")
        logger.info("   💼 机构态度: 传统金融机构加速入场")
        logger.info("   🏛️ 监管动态: 主要国家监管框架逐步完善")
        logger.info("   📊 技术面: 整体技术形态偏多头")
        logger.info("   💰 资金流向: 资金从传统资产流入加密市场")
        
        # 最终投资建议
        logger.info(f"\n" + "="*100)
        logger.info("🎯 最终投资建议")
        logger.info("="*100)
        
        logger.info("📋 推荐投资组合配置:")
        logger.info("   🥇 BTC (40%): 核心配置，长期持有")
        logger.info("     - 优势: 流动性好，机构认可度高，抗通胀属性")
        logger.info("     - 建议: 分批买入，长期持有，可作为组合压舱石")
        
        logger.info("   🥈 ETH (35%): 成长配置，受益生态发展")
        logger.info("     - 优势: 生态丰富，技术先进，ETF催化")
        logger.info("     - 建议: 关注Layer2发展，适合中长期持有")
        
        logger.info("   🥉 ATOM (15%): 潜力配置，跨链概念")
        logger.info("     - 优势: 技术创新，跨链赛道领先")
        logger.info("     - 建议: 小仓位配置，关注生态发展")
        
        logger.info("   💰 现金储备 (10%): 机动资金，等待机会")
        logger.info("     - 用途: 把握突然的买入机会，应对急跌风险")
        
        logger.info(f"\n⏰ 操作时机建议:")
        logger.info("   🟢 立即买入: BTC在11.4万美元以下")
        logger.info("   🟡 分批买入: ETH在3600-3700美元区间")
        logger.info("   ⚪ 等待时机: ATOM等待跌破4美元")
        
        logger.info(f"\n⚠️ 风险管理:")
        logger.info("   📉 止损设置: 单笔投资止损不超过15%")
        logger.info("   🔄 仓位管理: 任何单币种不超过总资金50%")
        logger.info("   📊 定期调仓: 每月评估一次，及时调整")
        logger.info("   📈 利润了结: 单币种盈利50%以上考虑部分获利")
        
        logger.info(f"\n📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("🔄 建议每日跟踪，每周深度分析")
        
    except Exception as e:
        logger.info(f"❌ 综合分析出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    comprehensive_analysis()