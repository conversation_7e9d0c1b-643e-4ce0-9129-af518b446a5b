import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
超级深度市场分析工具
整合多维度数据源，构建全方位市场洞察系统
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import warnings
import json
import time
from collections import defaultdict
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.market.data.adapters.binance_adapter import BinanceAdapter
from src.market.data.adapters.base import DataSourceConfig

class SuperDeepMarketAnalyzer:
    """超级深度市场分析器"""
    
    def __init__(self):
        # 扩展分析币种列表
        self.tier1_coins = ['BTCUSDT', 'ETHUSDT']  # 顶级币种
        self.tier2_coins = ['BNBUSDT', 'XRPUSDT', 'ADAUSDT', 'SOLUSDT']  # 主流币种
        self.tier3_coins = ['DOGEUSDT', 'LTCUSDT', 'LINKUSDT', 'UNIUSDT', 'AVAXUSDT', 'ATOMUSDT']  # 优质币种
        self.tier4_coins = ['INJUSDT', 'APTUSDT', 'SUIUSDT', 'ARBUSDT', 'OPUSDT', 'MATICUSDT']  # 新兴币种
        self.tier5_coins = ['PEPEUSDT', 'SHIBUSDT', 'FLOKIUSDT', 'BONKUSDT']  # Meme币种
        
        self.all_coins = self.tier1_coins + self.tier2_coins + self.tier3_coins + self.tier4_coins + self.tier5_coins
        
        # 数据收集间隔
        self.intervals = ['1m', '5m', '15m', '1h', '4h', '1d']
        
        # 市场分析结果存储
        self.market_data = {}
        self.depth_data = {}
        self.volume_analysis = {}
        self.sentiment_data = {}
        self.correlation_matrix = {}
        
    def run_super_deep_analysis(self):
        """运行超级深度分析"""
        logger.info("=" * 160)
        logger.info("🔬 超级深度市场分析 - 多维度数据整合系统")
        logger.info("=" * 160)
        
        try:
            # 初始化适配器
            adapter = self.initialize_adapter()
            
            # 1. 全市场扫描分析
            logger.info("\n🌍 第一阶段：全市场扫描分析...")
            self.analyze_full_market_scan(adapter)
            
            # 2. 深度流动性分析
            logger.info("\n📖 第二阶段：深度流动性分析...")
            self.analyze_deep_liquidity(adapter)
            
            # 3. 高频数据分析
            logger.info("\n⚡ 第三阶段：高频数据模式分析...")
            self.analyze_high_frequency_patterns(adapter)
            
            # 4. 大户资金追踪
            logger.info("\n🐋 第四阶段：大户资金流向追踪...")
            self.analyze_whale_activities(adapter)
            
            # 5. 市场情绪量化
            logger.info("\n😊 第五阶段：市场情绪量化分析...")
            self.analyze_market_sentiment(adapter)
            
            # 6. 相关性网络分析
            logger.info("\n🕸️  第六阶段：币种相关性网络分析...")
            self.analyze_correlation_network(adapter)
            
            # 7. 时间序列预测
            logger.info("\n🔮 第七阶段：时间序列趋势预测...")
            self.build_prediction_models(adapter)
            
            # 8. 综合结果展示
            logger.info("\n📊 第八阶段：生成超级深度分析报告...")
            self.generate_super_report()
            
        except Exception as e:
            logger.info(f"❌ 超级深度分析出现错误: {e}")
            import traceback
            traceback.print_exc()
    
    def initialize_adapter(self):
        """初始化适配器"""
        logger.info("🔌 正在连接币安API...")
        config = DataSourceConfig(
            name='super_deep_analysis',
            adapter_class='src.data.adapters.binance_adapter.BinanceAdapter',
            enabled=True,
            priority=1,
            rate_limit={
                'requests_per_minute': 15,
                'requests_per_second': 1
            },
            credentials={
                'api_key': '',
                'api_secret': ''
            },
            default_params={}
        )
        adapter = BinanceAdapter(config)
        
        is_healthy, message = adapter.health_check()
        if not is_healthy:
            raise Exception(f"API连接失败: {message}")
        
        logger.info("✅ API连接成功")
        return adapter
    
    def analyze_full_market_scan(self, adapter):
        """全市场扫描分析"""
        try:
            # 获取全市场数据
            all_tickers = adapter.get_24hr_ticker()
            if not all_tickers:
                return
            
            # 筛选USDT交易对
            usdt_pairs = {k: v for k, v in all_tickers.items() if k.endswith('USDT') and v['quote_volume'] > 1000000}
            
            logger.info(f"   📊 分析 {len(usdt_pairs)} 个活跃USDT交易对")
            
            # 分类统计
            self.market_data['total_active_pairs'] = len(usdt_pairs)
            self.market_data['categories'] = self.categorize_market_performance(usdt_pairs)
            self.market_data['volume_tiers'] = self.analyze_volume_tiers(usdt_pairs)
            self.market_data['volatility_analysis'] = self.analyze_market_volatility(usdt_pairs)
            self.market_data['momentum_signals'] = self.detect_momentum_signals(usdt_pairs)
            
            logger.info(f"   ✅ 完成全市场扫描，发现 {len(self.market_data['momentum_signals']['strong_momentum'])} 个强势动量信号")
            
        except Exception as e:
            logger.info(f"   ❌ 全市场扫描失败: {e}")
    
    def analyze_deep_liquidity(self, adapter):
        """深度流动性分析"""
        liquidity_data = {}
        
        logger.info("   📖 分析重点币种流动性深度...")
        
        for i, symbol in enumerate(self.all_coins[:10], 1):  # 限制数量避免超时
            try:
                logger.info(f"   [{i}/10] 分析 {symbol.replace('USDT', '')} 流动性...", end='')
                
                # 获取订单簿深度
                order_book = adapter.get_order_book(symbol, limit=200)
                if not order_book:
                    logger.info(" ❌")
                    continue
                
                # 分析流动性指标
                liquidity_metrics = self.calculate_liquidity_metrics(order_book, symbol)
                liquidity_data[symbol] = liquidity_metrics
                
                # 获取最近交易记录
                recent_trades = adapter.get_recent_trades(symbol, limit=100)
                if recent_trades:
                    trade_analysis = self.analyze_recent_trades(recent_trades)
                    liquidity_data[symbol].update(trade_analysis)
                
                logger.info(" ✅")
                time.sleep(0.2)  # 避免请求过快
                
            except Exception as e:
                logger.info(f" ❌ {str(e)[:30]}...")
                continue
        
        self.depth_data = liquidity_data
        logger.info(f"   ✅ 完成 {len(liquidity_data)} 个币种的深度流动性分析")
    
    def analyze_high_frequency_patterns(self, adapter):
        """高频数据模式分析"""
        logger.info("   ⚡ 获取高频K线数据进行模式识别...")
        
        hf_patterns = {}
        
        # 分析重点币种的高频模式
        for symbol in self.tier1_coins + self.tier2_coins:
            try:
                logger.info(f"   📈 分析 {symbol.replace('USDT', '')} 高频模式...", end='')
                
                patterns = {}
                
                # 获取多个时间框架数据
                for interval in ['1m', '5m', '15m']:
                    end_date = datetime.now()
                    start_date = end_date - timedelta(hours=12 if interval == '1m' else 24)
                    
                    try:
                        data = adapter.fetch_historical_data(symbol, start_date, end_date, interval)
                        if data and len(data) > 20:
                            df = self.prepare_dataframe(data)
                            pattern_data = self.detect_patterns(df, interval)
                            patterns[interval] = pattern_data
                    except:
                        continue
                
                if patterns:
                    hf_patterns[symbol] = patterns
                    logger.info(" ✅")
                else:
                    logger.info(" ❌")
                
                time.sleep(0.3)
                
            except Exception as e:
                logger.info(f" ❌")
                continue
        
        self.market_data['hf_patterns'] = hf_patterns
        logger.info(f"   ✅ 完成 {len(hf_patterns)} 个币种的高频模式分析")
    
    def analyze_whale_activities(self, adapter):
        """大户资金追踪分析"""
        logger.info("   🐋 追踪大户资金活动模式...")
        
        whale_data = {}
        
        for symbol in self.all_coins[:8]:  # 限制数量
            try:
                logger.info(f"   🔍 追踪 {symbol.replace('USDT', '')} 大户活动...", end='')
                
                # 获取最近交易数据
                recent_trades = adapter.get_recent_trades(symbol, limit=200)
                if recent_trades:
                    whale_analysis = self.detect_whale_activities(recent_trades, symbol)
                    whale_data[symbol] = whale_analysis
                    logger.info(" ✅")
                else:
                    logger.info(" ❌")
                
                time.sleep(0.2)
                
            except Exception as e:
                logger.info(f" ❌")
                continue
        
        self.market_data['whale_activities'] = whale_data
        logger.info(f"   ✅ 完成 {len(whale_data)} 个币种的大户追踪分析")
    
    def analyze_market_sentiment(self, adapter):
        """市场情绪量化分析"""
        logger.info("   😊 量化市场情绪指标...")
        
        try:
            # 获取全市场数据计算恐贪指数
            all_tickers = adapter.get_24hr_ticker()
            if all_tickers:
                sentiment_metrics = self.calculate_sentiment_index(all_tickers)
                self.sentiment_data = sentiment_metrics
                logger.info(f"   ✅ 完成市场情绪量化，当前恐贪指数: {sentiment_metrics.get('fear_greed_index', 'N/A')}")
            
        except Exception as e:
            logger.info(f"   ❌ 市场情绪分析失败: {e}")
    
    def analyze_correlation_network(self, adapter):
        """相关性网络分析"""
        logger.info("   🕸️  构建币种相关性网络...")
        
        try:
            # 获取主要币种的24小时价格变化
            price_changes = {}
            all_tickers = adapter.get_24hr_ticker()
            
            if all_tickers:
                for symbol in self.all_coins:
                    if symbol in all_tickers:
                        price_changes[symbol] = all_tickers[symbol]['price_change_percent']
                
                # 计算相关性矩阵
                correlation_data = self.calculate_correlations(price_changes)
                self.correlation_matrix = correlation_data
                
                logger.info(f"   ✅ 完成 {len(price_changes)} 个币种的相关性网络分析")
            
        except Exception as e:
            logger.info(f"   ❌ 相关性分析失败: {e}")
    
    def build_prediction_models(self, adapter):
        """构建预测模型"""
        logger.info("   🔮 构建时间序列预测模型...")
        
        predictions = {}
        
        for symbol in self.tier1_coins:  # 只对顶级币种做预测
            try:
                logger.info(f"   📊 为 {symbol.replace('USDT', '')} 构建预测模型...", end='')
                
                # 获取历史数据
                end_date = datetime.now()
                start_date = end_date - timedelta(days=7)
                
                data = adapter.fetch_historical_data(symbol, start_date, end_date, '1h')
                if data and len(data) > 50:
                    df = self.prepare_dataframe(data)
                    prediction = self.simple_trend_prediction(df)
                    predictions[symbol] = prediction
                    logger.info(" ✅")
                else:
                    logger.info(" ❌")
                
                time.sleep(0.2)
                
            except Exception as e:
                logger.info(f" ❌")
                continue
        
        self.market_data['predictions'] = predictions
        logger.info(f"   ✅ 完成 {len(predictions)} 个币种的预测模型构建")
    
    def categorize_market_performance(self, usdt_pairs):
        """分类市场表现"""
        categories = {
            'strong_gainers': [],    # >15%
            'moderate_gainers': [],  # 5-15%
            'slight_gainers': [],    # 0-5%
            'slight_losers': [],     # 0 to -5%
            'moderate_losers': [],   # -5 to -15%
            'strong_losers': []      # <-15%
        }
        
        for symbol, data in usdt_pairs.items():
            change = data['price_change_percent']
            if change > 15:
                categories['strong_gainers'].append((symbol, change))
            elif change > 5:
                categories['moderate_gainers'].append((symbol, change))
            elif change > 0:
                categories['slight_gainers'].append((symbol, change))
            elif change > -5:
                categories['slight_losers'].append((symbol, change))
            elif change > -15:
                categories['moderate_losers'].append((symbol, change))
            else:
                categories['strong_losers'].append((symbol, change))
        
        # 排序
        for category in categories:
            categories[category].sort(key=lambda x: x[1], reverse=True)
        
        return categories
    
    def analyze_volume_tiers(self, usdt_pairs):
        """分析成交量层级"""
        volumes = [(symbol, data['quote_volume']) for symbol, data in usdt_pairs.items()]
        volumes.sort(key=lambda x: x[1], reverse=True)
        
        total_volume = sum([v[1] for v in volumes])
        
        tiers = {
            'mega_cap': [],      # >1B
            'large_cap': [],     # 100M-1B
            'mid_cap': [],       # 10M-100M
            'small_cap': [],     # 1M-10M
            'micro_cap': []      # <1M
        }
        
        for symbol, volume in volumes:
            if volume > 1000000000:
                tiers['mega_cap'].append((symbol, volume))
            elif volume > 100000000:
                tiers['large_cap'].append((symbol, volume))
            elif volume > 10000000:
                tiers['mid_cap'].append((symbol, volume))
            elif volume > 1000000:
                tiers['small_cap'].append((symbol, volume))
            else:
                tiers['micro_cap'].append((symbol, volume))
        
        return {
            'tiers': tiers,
            'total_volume': total_volume,
            'dominance': {tier: sum([v[1] for v in coins])/total_volume*100 for tier, coins in tiers.items()}
        }
    
    def analyze_market_volatility(self, usdt_pairs):
        """分析市场波动率"""
        volatility_data = []
        
        for symbol, data in usdt_pairs.items():
            high = data['high_price']
            low = data['low_price']
            if low > 0:
                volatility = (high - low) / low * 100
                volatility_data.append((symbol, volatility, data['quote_volume']))
        
        # 按波动率排序
        volatility_data.sort(key=lambda x: x[1], reverse=True)
        
        return {
            'highest_volatility': volatility_data[:20],
            'lowest_volatility': volatility_data[-20:],
            'avg_volatility': np.mean([v[1] for v in volatility_data]),
            'volatility_distribution': {
                'extreme': len([v for v in volatility_data if v[1] > 30]),
                'high': len([v for v in volatility_data if 15 < v[1] <= 30]),
                'moderate': len([v for v in volatility_data if 5 < v[1] <= 15]),
                'low': len([v for v in volatility_data if v[1] <= 5])
            }
        }
    
    def detect_momentum_signals(self, usdt_pairs):
        """检测动量信号"""
        signals = {
            'strong_momentum': [],
            'weak_momentum': [],
            'reversal_candidates': []
        }
        
        for symbol, data in usdt_pairs.items():
            change = data['price_change_percent']
            volume = data['quote_volume']
            
            # 强动量：大涨幅 + 高成交量
            if change > 8 and volume > 50000000:
                signals['strong_momentum'].append((symbol, change, volume))
            # 弱动量：小涨幅 + 低成交量
            elif 0 < change < 3 and volume < 10000000:
                signals['weak_momentum'].append((symbol, change, volume))
            # 反转候选：大跌 + 高成交量
            elif change < -8 and volume > 30000000:
                signals['reversal_candidates'].append((symbol, change, volume))
        
        return signals
    
    def calculate_liquidity_metrics(self, order_book, symbol):
        """计算流动性指标"""
        bids = order_book['bids']
        asks = order_book['asks']
        
        if not bids or not asks:
            return {}
        
        # 计算各层级流动性
        bid_liquidity = {}
        ask_liquidity = {}
        
        depths = [0.1, 0.5, 1.0, 2.0, 5.0]  # 百分比深度
        best_bid = float(bids[0][0])
        best_ask = float(asks[0][0])
        
        for depth in depths:
            bid_threshold = best_bid * (1 - depth/100)
            ask_threshold = best_ask * (1 + depth/100)
            
            bid_volume = sum([float(qty) for price, qty in bids if float(price) >= bid_threshold])
            ask_volume = sum([float(qty) for price, qty in asks if float(price) <= ask_threshold])
            
            bid_liquidity[f'{depth}%'] = bid_volume
            ask_liquidity[f'{depth}%'] = ask_volume
        
        # 计算流动性不平衡
        total_bid_value = sum([float(price) * float(qty) for price, qty in bids[:50]])
        total_ask_value = sum([float(price) * float(qty) for price, qty in asks[:50]])
        
        return {
            'spread_bps': (best_ask - best_bid) / best_bid * 10000,
            'bid_liquidity': bid_liquidity,
            'ask_liquidity': ask_liquidity,
            'liquidity_ratio': total_bid_value / total_ask_value if total_ask_value > 0 else 0,
            'market_impact': self.calculate_market_impact(bids, asks, [1000, 5000, 10000, 50000])  # USDT金额
        }
    
    def calculate_market_impact(self, bids, asks, amounts):
        """计算市场冲击成本"""
        impacts = {}
        
        for amount in amounts:
            # 买入冲击
            remaining = amount
            weighted_price = 0
            total_qty = 0
            
            for price, qty in asks:
                price_f, qty_f = float(price), float(qty)
                value = price_f * qty_f
                
                if remaining <= value:
                    needed_qty = remaining / price_f
                    weighted_price += price_f * needed_qty
                    total_qty += needed_qty
                    break
                else:
                    weighted_price += price_f * qty_f
                    total_qty += qty_f
                    remaining -= value
            
            if total_qty > 0:
                avg_price = weighted_price / total_qty
                best_ask = float(asks[0][0])
                buy_impact = (avg_price - best_ask) / best_ask * 100
            else:
                buy_impact = float('inf')
            
            impacts[f'buy_${amount}'] = buy_impact
        
        return impacts
    
    def analyze_recent_trades(self, trades):
        """分析最近交易"""
        if not trades:
            return {}
        
        buy_volume = sum([t['qty'] for t in trades if not t['is_buyer_maker']])
        sell_volume = sum([t['qty'] for t in trades if t['is_buyer_maker']])
        total_volume = buy_volume + sell_volume
        
        large_trades = [t for t in trades if t['qty'] * t['price'] > 10000]  # >10k USDT
        
        return {
            'buy_sell_ratio': buy_volume / sell_volume if sell_volume > 0 else 0,
            'large_trade_count': len(large_trades),
            'avg_trade_size': total_volume / len(trades) if trades else 0,
            'trade_frequency': len(trades)  # 在固定时间窗口内
        }
    
    def prepare_dataframe(self, data):
        """准备DataFrame"""
        df = pd.DataFrame([{
            'timestamp': d.timestamp,
            'open': d.open,
            'high': d.high,
            'low': d.low,
            'close': d.close,
            'volume': d.volume
        } for d in data])
        
        df.set_index('timestamp', inplace=True)
        df.sort_index(inplace=True)
        
        # 添加技术指标
        df['returns'] = df['close'].pct_change()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['volatility'] = df['returns'].rolling(20).std()
        
        return df
    
    def detect_patterns(self, df, interval):
        """检测价格模式"""
        if len(df) < 20:
            return {}
        
        patterns = {}
        
        # 趋势检测
        recent_closes = df['close'].tail(10)
        if len(recent_closes) >= 2:
            trend_slope = (recent_closes.iloc[-1] - recent_closes.iloc[0]) / len(recent_closes)
            patterns['trend'] = 'up' if trend_slope > 0 else 'down'
            patterns['trend_strength'] = abs(trend_slope) / recent_closes.iloc[0] * 100
        
        # 支撑阻力检测
        highs = df['high'].tail(50)
        lows = df['low'].tail(50)
        
        resistance_levels = []
        support_levels = []
        
        for i in range(2, len(highs)-2):
            if highs.iloc[i] > highs.iloc[i-1] and highs.iloc[i] > highs.iloc[i+1]:
                resistance_levels.append(highs.iloc[i])
            if lows.iloc[i] < lows.iloc[i-1] and lows.iloc[i] < lows.iloc[i+1]:
                support_levels.append(lows.iloc[i])
        
        patterns['resistance_levels'] = sorted(set(resistance_levels), reverse=True)[:3]
        patterns['support_levels'] = sorted(set(support_levels))[:3]
        
        # 波动率分析
        if 'volatility' in df.columns:
            current_vol = df['volatility'].iloc[-1]
            avg_vol = df['volatility'].mean()
            patterns['volatility_regime'] = 'high' if current_vol > avg_vol * 1.5 else 'low'
        
        return patterns
    
    def detect_whale_activities(self, trades, symbol):
        """检测大户活动"""
        if not trades:
            return {}
        
        # 定义大户交易阈值（根据币种调整）
        if 'BTC' in symbol:
            whale_threshold = 50000  # $50k
        elif 'ETH' in symbol:
            whale_threshold = 30000  # $30k
        else:
            whale_threshold = 10000  # $10k
        
        whale_trades = []
        for trade in trades:
            trade_value = trade['price'] * trade['qty']
            if trade_value >= whale_threshold:
                whale_trades.append({
                    'value': trade_value,
                    'side': 'buy' if not trade['is_buyer_maker'] else 'sell',
                    'time': trade['time']
                })
        
        if not whale_trades:
            return {'whale_activity': 'none'}
        
        # 分析大户行为
        buy_trades = [t for t in whale_trades if t['side'] == 'buy']
        sell_trades = [t for t in whale_trades if t['side'] == 'sell']
        
        buy_value = sum([t['value'] for t in buy_trades])
        sell_value = sum([t['value'] for t in sell_trades])
        
        return {
            'whale_trade_count': len(whale_trades),
            'whale_buy_value': buy_value,
            'whale_sell_value': sell_value,
            'whale_net_flow': buy_value - sell_value,
            'whale_activity': 'accumulating' if buy_value > sell_value * 1.5 else 'distributing' if sell_value > buy_value * 1.5 else 'balanced'
        }
    
    def calculate_sentiment_index(self, all_tickers):
        """计算市场情绪指数"""
        usdt_pairs = {k: v for k, v in all_tickers.items() if k.endswith('USDT') and v['quote_volume'] > 1000000}
        
        if not usdt_pairs:
            return {}
        
        changes = [v['price_change_percent'] for v in usdt_pairs.values()]
        volumes = [v['quote_volume'] for v in usdt_pairs.values()]
        
        # 计算各种情绪指标
        positive_coins = len([c for c in changes if c > 0])
        total_coins = len(changes)
        
        # 恐贪指数 (0-100, 100为极度贪婪)
        avg_change = np.mean(changes)
        volatility = np.std(changes)
        positive_ratio = positive_coins / total_coins
        
        # 综合计算恐贪指数
        change_component = max(0, min(50, (avg_change + 10) * 2.5))  # -10% to 10% -> 0 to 50
        ratio_component = positive_ratio * 30  # 0 to 30
        volatility_component = max(0, 20 - volatility)  # 低波动率加分
        
        fear_greed_index = int(change_component + ratio_component + volatility_component)
        
        return {
            'fear_greed_index': fear_greed_index,
            'market_sentiment': 'Extreme Greed' if fear_greed_index > 80 else 'Greed' if fear_greed_index > 60 else 'Neutral' if fear_greed_index > 40 else 'Fear' if fear_greed_index > 20 else 'Extreme Fear',
            'positive_ratio': positive_ratio,
            'avg_change': avg_change,
            'market_volatility': volatility,
            'active_pairs': total_coins
        }
    
    def calculate_correlations(self, price_changes):
        """计算相关性"""
        if len(price_changes) < 2:
            return {}
        
        symbols = list(price_changes.keys())
        changes = list(price_changes.values())
        
        correlations = {}
        strong_correlations = []
        
        for i, sym1 in enumerate(symbols):
            for j, sym2 in enumerate(symbols[i+1:], i+1):
                # 简单相关性计算（需要历史数据才能更准确）
                change1, change2 = changes[i], changes[j]
                
                # 基于当前价格变化的简单相关性估算
                if abs(change1) > 1 and abs(change2) > 1:
                    same_direction = (change1 > 0) == (change2 > 0)
                    correlation_strength = 1 - abs(abs(change1) - abs(change2)) / (abs(change1) + abs(change2))
                    
                    if same_direction and correlation_strength > 0.7:
                        strong_correlations.append((sym1, sym2, correlation_strength))
        
        return {
            'strong_correlations': sorted(strong_correlations, key=lambda x: x[2], reverse=True)[:10]
        }
    
    def simple_trend_prediction(self, df):
        """简单趋势预测"""
        if len(df) < 24:
            return {}
        
        # 基于移动平均和趋势的简单预测
        recent_prices = df['close'].tail(24)
        sma_short = recent_prices.tail(6).mean()
        sma_long = recent_prices.tail(24).mean()
        
        trend_direction = 'up' if sma_short > sma_long else 'down'
        trend_strength = abs(sma_short - sma_long) / sma_long * 100
        
        current_price = recent_prices.iloc[-1]
        
        # 简单价格预测（仅作参考）
        if trend_direction == 'up':
            next_4h = current_price * (1 + trend_strength/100 * 0.1)
            next_24h = current_price * (1 + trend_strength/100 * 0.3)
        else:
            next_4h = current_price * (1 - trend_strength/100 * 0.1)
            next_24h = current_price * (1 - trend_strength/100 * 0.3)
        
        return {
            'trend_direction': trend_direction,
            'trend_strength': trend_strength,
            'current_price': current_price,
            'prediction_4h': next_4h,
            'prediction_24h': next_24h,
            'confidence': min(100, trend_strength * 10)  # 简单信心度
        }
    
    def generate_super_report(self):
        """生成超级深度分析报告"""
        logger.info("=" * 160)
        logger.info("📊 超级深度市场分析报告")
        logger.info("=" * 160)
        
        # 报告第一部分：市场全景
        self.report_market_overview()
        
        # 报告第二部分：流动性深度分析
        self.report_liquidity_analysis()
        
        # 报告第三部分：高频模式识别
        self.report_pattern_analysis()
        
        # 报告第四部分：大户资金追踪
        self.report_whale_analysis()
        
        # 报告第五部分：市场情绪量化
        self.report_sentiment_analysis()
        
        # 报告第六部分：相关性网络
        self.report_correlation_analysis()
        
        # 报告第七部分：预测模型结果
        self.report_prediction_analysis()
        
        # 报告第八部分：超级投资建议
        self.report_super_recommendations()
    
    def report_market_overview(self):
        """报告市场全景"""
        if not self.market_data:
            return
        
        logger.info(f"\n" + "=" * 160)
        logger.info("🌍 市场全景扫描")
        logger.info("=" * 160)
        
        logger.info(f"📊 活跃交易对总数: {self.market_data.get('total_active_pairs', 'N/A')} 个")
        
        # 表现分类
        if 'categories' in self.market_data:
            cats = self.market_data['categories']
            logger.info(f"\n📈 市场表现分布:")
            logger.info(f"   🚀 强势上涨(>15%): {len(cats.get('strong_gainers', []))} 个")
            logger.info(f"   🟢 温和上涨(5-15%): {len(cats.get('moderate_gainers', []))} 个")
            logger.info(f"   🟡 微涨(0-5%): {len(cats.get('slight_gainers', []))} 个")
            logger.info(f"   🟡 微跌(0-5%): {len(cats.get('slight_losers', []))} 个")
            logger.info(f"   🔴 温和下跌(-5 to -15%): {len(cats.get('moderate_losers', []))} 个")
            logger.info(f"   💥 强势下跌(<-15%): {len(cats.get('strong_losers', []))} 个")
            
            # 展示顶级表现者
            if cats.get('strong_gainers'):
                logger.info(f"\n🏆 强势领涨币种:")
                for symbol, change in cats['strong_gainers'][:5]:
                    name = symbol.replace('USDT', '')
                    logger.info(f"     • {name}: {change:+.2f}%")
        
        # 成交量分析
        if 'volume_tiers' in self.market_data:
            vol_data = self.market_data['volume_tiers']
            logger.info(f"\n💰 成交量分析 (总计: ${vol_data.get('total_volume', 0):,.0f}):")
            dominance = vol_data.get('dominance', {})
            logger.info(f"   🦣 超大盘(>$1B): {dominance.get('mega_cap', 0):.1f}% 市场占比")
            logger.info(f"   🐋 大盘($100M-$1B): {dominance.get('large_cap', 0):.1f}% 市场占比")
            logger.info(f"   🐟 中盘($10M-$100M): {dominance.get('mid_cap', 0):.1f}% 市场占比")
            logger.info(f"   🦐 小盘($1M-$10M): {dominance.get('small_cap', 0):.1f}% 市场占比")
        
        # 波动率分析
        if 'volatility_analysis' in self.market_data:
            vol_analysis = self.market_data['volatility_analysis']
            logger.info(f"\n⚡ 市场波动率分析:")
            logger.info(f"   平均波动率: {vol_analysis.get('avg_volatility', 0):.2f}%")
            
            vol_dist = vol_analysis.get('volatility_distribution', {})
            logger.info(f"   极高波动(>30%): {vol_dist.get('extreme', 0)} 个币种")
            logger.info(f"   高波动(15-30%): {vol_dist.get('high', 0)} 个币种")
            logger.info(f"   中等波动(5-15%): {vol_dist.get('moderate', 0)} 个币种")
            logger.info(f"   低波动(<5%): {vol_dist.get('low', 0)} 个币种")
    
    def report_liquidity_analysis(self):
        """报告流动性分析"""
        if not self.depth_data:
            return
        
        logger.info(f"\n" + "=" * 160)
        logger.info("📖 深度流动性分析")
        logger.info("=" * 160)
        
        logger.info(f"{'币种':<8} {'点差(bp)':<10} {'1%深度买':<12} {'1%深度卖':<12} {'流动性比':<10} {'$10k冲击':<10}")
        logger.info("-" * 80)
        
        for symbol, data in list(self.depth_data.items())[:10]:
            name = symbol.replace('USDT', '')
            spread = data.get('spread_bps', 0)
            bid_1pct = data.get('bid_liquidity', {}).get('1.0%', 0)
            ask_1pct = data.get('ask_liquidity', {}).get('1.0%', 0)
            liq_ratio = data.get('liquidity_ratio', 0)
            impact_10k = data.get('market_impact', {}).get('buy_$10000', 0)
            
            logger.info(f"{name:<8} {spread:<10.1f} {bid_1pct:<12.1f} {ask_1pct:<12.1f} {liq_ratio:<10.2f} {impact_10k:<10.3f}%")
    
    def report_pattern_analysis(self):
        """报告模式分析"""
        if 'hf_patterns' not in self.market_data:
            return
        
        logger.info(f"\n" + "=" * 160)
        logger.info("⚡ 高频模式识别")
        logger.info("=" * 160)
        
        patterns = self.market_data['hf_patterns']
        
        for symbol, timeframes in patterns.items():
            name = symbol.replace('USDT', '')
            logger.info(f"\n📊 {name} 模式分析:")
            
            for tf, pattern_data in timeframes.items():
                if pattern_data:
                    trend = pattern_data.get('trend', 'unknown')
                    strength = pattern_data.get('trend_strength', 0)
                    vol_regime = pattern_data.get('volatility_regime', 'unknown')
                    
                    trend_emoji = "📈" if trend == 'up' else "📉" if trend == 'down' else "➡️"
                    logger.info(f"   {tf}: {trend_emoji} {trend} | 强度:{strength:.3f}% | 波动率:{vol_regime}")
                    
                    # 显示关键支撑阻力位
                    resistance = pattern_data.get('resistance_levels', [])
                    support = pattern_data.get('support_levels', [])
                    
                    if resistance:
                        logger.info(f"        阻力位: {', '.join([f'${r:.4f}' for r in resistance[:2]])}")
                    if support:
                        logger.info(f"        支撑位: {', '.join([f'${s:.4f}' for s in support[:2]])}")
    
    def report_whale_analysis(self):
        """报告大户分析"""
        if 'whale_activities' not in self.market_data:
            return
        
        logger.info(f"\n" + "=" * 160)
        logger.info("🐋 大户资金活动追踪")
        logger.info("=" * 160)
        
        whale_data = self.market_data['whale_activities']
        
        logger.info(f"{'币种':<8} {'大户交易':<10} {'买入金额':<15} {'卖出金额':<15} {'净流向':<15} {'行为判断'}")
        logger.info("-" * 90)
        
        for symbol, data in whale_data.items():
            name = symbol.replace('USDT', '')
            trade_count = data.get('whale_trade_count', 0)
            buy_value = data.get('whale_buy_value', 0)
            sell_value = data.get('whale_sell_value', 0)
            net_flow = data.get('whale_net_flow', 0)
            activity = data.get('whale_activity', 'none')
            
            activity_emoji = "🟢" if activity == 'accumulating' else "🔴" if activity == 'distributing' else "⚪"
            
            logger.info(f"{name:<8} {trade_count:<10d} ${buy_value:<14,.0f} ${sell_value:<14,.0f} ${net_flow:<14,.0f} {activity_emoji}{activity}")
    
    def report_sentiment_analysis(self):
        """报告情绪分析"""
        if not self.sentiment_data:
            return
        
        logger.info(f"\n" + "=" * 160)
        logger.info("😊 市场情绪量化分析")
        logger.info("=" * 160)
        
        fgi = self.sentiment_data.get('fear_greed_index', 50)
        sentiment = self.sentiment_data.get('market_sentiment', 'Neutral')
        positive_ratio = self.sentiment_data.get('positive_ratio', 0.5)
        avg_change = self.sentiment_data.get('avg_change', 0)
        volatility = self.sentiment_data.get('market_volatility', 0)
        
        # 情绪颜色
        if fgi > 80:
            sentiment_color = "🔥"
        elif fgi > 60:
            sentiment_color = "🟢"
        elif fgi > 40:
            sentiment_color = "🟡"
        elif fgi > 20:
            sentiment_color = "🔴"
        else:
            sentiment_color = "💀"
        
        logger.info(f"🎭 恐贪指数: {sentiment_color} {fgi}/100 ({sentiment})")
        logger.info(f"📊 上涨币种比例: {positive_ratio*100:.1f}%")
        logger.info(f"📈 市场平均变化: {avg_change:+.2f}%")
        logger.info(f"⚡ 市场波动率: {volatility:.2f}%")
        
        # 情绪解读
        logger.info(f"\n💭 情绪解读:")
        if fgi > 75:
            logger.info("   • 市场极度贪婪，注意泡沫风险，考虑获利了结")
        elif fgi > 55:
            logger.info("   • 市场情绪乐观，可适度参与，但需控制风险")
        elif fgi > 25:
            logger.info("   • 市场情绪中性，适合震荡操作和定投策略")
        else:
            logger.info("   • 市场恐慌情绪浓厚，可能是长期投资的好时机")
    
    def report_correlation_analysis(self):
        """报告相关性分析"""
        if not self.correlation_matrix:
            return
        
        logger.info(f"\n" + "=" * 160)
        logger.info("🕸️  币种相关性网络分析")
        logger.info("=" * 160)
        
        correlations = self.correlation_matrix.get('strong_correlations', [])
        
        if correlations:
            logger.info("🔗 强相关性币种对:")
            for sym1, sym2, strength in correlations:
                name1 = sym1.replace('USDT', '')
                name2 = sym2.replace('USDT', '')
                logger.info(f"   {name1} ↔ {name2}: 相关度 {strength:.3f}")
                
                # 给出交易建议
                if strength > 0.8:
                    logger.info(f"      💡 高度相关，可考虑配对交易或同向操作")
        else:
            logger.info("   当前市场相关性较低，币种表现分化明显")
    
    def report_prediction_analysis(self):
        """报告预测分析"""
        if 'predictions' not in self.market_data:
            return
        
        logger.info(f"\n" + "=" * 160)
        logger.info("🔮 时间序列预测分析")
        logger.info("=" * 160)
        
        predictions = self.market_data['predictions']
        
        logger.info(f"{'币种':<8} {'当前价格':<12} {'4小时预测':<12} {'24小时预测':<13} {'趋势':<6} {'信心度'}")
        logger.info("-" * 80)
        
        for symbol, pred_data in predictions.items():
            name = symbol.replace('USDT', '')
            current = pred_data.get('current_price', 0)
            pred_4h = pred_data.get('prediction_4h', 0)
            pred_24h = pred_data.get('prediction_24h', 0)
            trend = pred_data.get('trend_direction', 'unknown')
            confidence = pred_data.get('confidence', 0)
            
            trend_emoji = "📈" if trend == 'up' else "📉"
            
            logger.info(f"{name:<8} ${current:<11.4f} ${pred_4h:<11.4f} ${pred_24h:<12.4f} {trend_emoji}{trend:<5} {confidence:.0f}%")
        
        logger.info(f"\n⚠️  预测说明: 基于历史趋势的简单模型，仅供参考，不构成投资建议")
    
    def report_super_recommendations(self):
        """报告超级投资建议"""
        logger.info(f"\n" + "=" * 160)
        logger.info("💡 超级深度投资建议")
        logger.info("=" * 160)
        
        # 综合所有数据生成建议
        recommendations = self.generate_integrated_recommendations()
        
        logger.info("🏆 多维度综合评分:")
        
        sorted_recs = sorted(recommendations.items(), key=lambda x: x[1]['total_score'], reverse=True)
        
        logger.info(f"{'排名':<4} {'币种':<8} {'总分':<6} {'流动性':<8} {'动量':<6} {'情绪':<6} {'预测':<6} {'建议'}")
        logger.info("-" * 70)
        
        for i, (symbol, scores) in enumerate(sorted_recs[:15], 1):
            name = symbol.replace('USDT', '')
            total = scores['total_score']
            liq = scores.get('liquidity_score', 0)
            mom = scores.get('momentum_score', 0)
            sent = scores.get('sentiment_score', 0)
            pred = scores.get('prediction_score', 0)
            
            if total >= 80:
                recommendation = "🔥强烈推荐"
            elif total >= 60:
                recommendation = "🟢积极买入"
            elif total >= 40:
                recommendation = "🟡谨慎关注"
            elif total >= 20:
                recommendation = "⚪中性观望"
            else:
                recommendation = "🔴建议回避"
            
            logger.info(f"{i:2d}   {name:<8} {total:<6.0f} {liq:<8.0f} {mom:<6.0f} {sent:<6.0f} {pred:<6.0f} {recommendation}")
        
        # 具体操作策略
        self.provide_actionable_strategies(sorted_recs)
        
        # 风险警示
        self.provide_risk_warnings()
    
    def generate_integrated_recommendations(self):
        """生成整合推荐"""
        recommendations = {}
        
        for symbol in self.all_coins:
            scores = {
                'liquidity_score': 0,
                'momentum_score': 0,
                'sentiment_score': 0,
                'prediction_score': 0,
                'total_score': 0
            }
            
            # 流动性评分
            if symbol in self.depth_data:
                depth = self.depth_data[symbol]
                spread = depth.get('spread_bps', 1000)
                liq_ratio = depth.get('liquidity_ratio', 1)
                
                if spread < 10:  # 低点差
                    scores['liquidity_score'] += 25
                elif spread < 50:
                    scores['liquidity_score'] += 15
                elif spread < 100:
                    scores['liquidity_score'] += 5
                
                if liq_ratio > 1.2:  # 买盘优势
                    scores['liquidity_score'] += 20
                elif liq_ratio > 0.8:
                    scores['liquidity_score'] += 10
            
            # 动量评分
            if 'momentum_signals' in self.market_data:
                momentum = self.market_data['momentum_signals']
                if any(symbol in entry[0] for entry in momentum.get('strong_momentum', [])):
                    scores['momentum_score'] = 30
                elif any(symbol in entry[0] for entry in momentum.get('reversal_candidates', [])):
                    scores['momentum_score'] = 25
            
            # 情绪评分
            if self.sentiment_data:
                fgi = self.sentiment_data.get('fear_greed_index', 50)
                if 20 <= fgi <= 40:  # 恐慌时买入机会
                    scores['sentiment_score'] = 25
                elif 60 <= fgi <= 80:  # 乐观时谨慎
                    scores['sentiment_score'] = 15
                else:
                    scores['sentiment_score'] = 10
            
            # 预测评分
            if 'predictions' in self.market_data and symbol in self.market_data['predictions']:
                pred = self.market_data['predictions'][symbol]
                trend = pred.get('trend_direction', 'unknown')
                confidence = pred.get('confidence', 0)
                
                if trend == 'up' and confidence > 60:
                    scores['prediction_score'] = 25
                elif trend == 'up' and confidence > 40:
                    scores['prediction_score'] = 15
                elif trend == 'down' and confidence > 60:
                    scores['prediction_score'] = -15
            
            # 计算总分
            scores['total_score'] = sum([
                scores['liquidity_score'],
                scores['momentum_score'],
                scores['sentiment_score'],
                scores['prediction_score']
            ])
            
            recommendations[symbol] = scores
        
        return recommendations
    
    def provide_actionable_strategies(self, sorted_recommendations):
        """提供可行的策略"""
        logger.info(f"\n📋 具体操作策略:")
        
        top_picks = [item for item in sorted_recommendations if item[1]['total_score'] >= 60][:3]
        
        if top_picks:
            logger.info(f"🎯 核心推荐组合:")
            total_allocation = 0
            for symbol, scores in top_picks:
                name = symbol.replace('USDT', '')
                score = scores['total_score']
                
                # 根据得分分配权重
                if score >= 80:
                    allocation = 30
                elif score >= 70:
                    allocation = 25
                else:
                    allocation = 20
                
                total_allocation += allocation
                
                logger.info(f"   • {name}: {allocation}% 配置 (得分{score:.0f})")
                
                # 给出具体操作建议
                if scores['momentum_score'] > 20:
                    logger.info(f"     ⚡ 动量强劲，建议分2-3批买入")
                if scores['liquidity_score'] > 30:
                    logger.info(f"     💧 流动性优秀，适合大资金操作")
                if scores['prediction_score'] > 15:
                    logger.info(f"     🔮 趋势向上，中期持有")
            
            logger.info(f"   💰 现金保留: {100-total_allocation}% (应对突发机会)")
        
        # 风险管理策略
        logger.info(f"\n🛡️  风险管理策略:")
        logger.info(f"   • 单币种最大仓位: 不超过总资金的30%")
        logger.info(f"   • 分批建仓: 每次买入不超过目标仓位的1/3")
        logger.info(f"   • 动态调整: 每周根据新数据调整一次配置")
        logger.info(f"   • 止损设置: 任何仓位下跌15%必须减仓")
        
        # 时机把握
        logger.info(f"\n⏰ 最佳操作时机:")
        if self.sentiment_data and self.sentiment_data.get('fear_greed_index', 50) < 40:
            logger.info(f"   📉 当前市场恐慌，是逢低买入的好时机")
        elif self.sentiment_data and self.sentiment_data.get('fear_greed_index', 50) > 70:
            logger.info(f"   📈 市场过热，建议等待回调再入场")
        else:
            logger.info(f"   ⚖️  市场情绪中性，可按计划正常操作")
    
    def provide_risk_warnings(self):
        """提供风险警示"""
        logger.info(f"\n" + "=" * 160)
        logger.info("⚠️  重要风险提示")
        logger.info("=" * 160)
        
        logger.info("🚨 本分析基于历史数据和技术指标，存在以下风险:")
        logger.info("   • 加密货币市场极度波动，可能出现极端价格变动")
        logger.info("   • 预测模型仅供参考，实际表现可能与预期差异巨大") 
        logger.info("   • 市场情绪和外部事件可能瞬间改变市场方向")
        logger.info("   • 流动性数据基于当前快照，可能快速变化")
        logger.info("   • 大户行为分析具有滞后性，不能预测未来动作")
        
        logger.info(f"\n💡 投资原则:")
        logger.info("   • 只投入你能承受损失的资金")
        logger.info("   • 保持理性，不要被情绪主导决策")
        logger.info("   • 定期学习和更新投资知识")
        logger.info("   • 建立并严格执行交易纪律")
        logger.info("   • 保持多元化，不要把鸡蛋放在一个篮子里")
        
        logger.info(f"\n📅 本次分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("🔄 建议每6-12小时更新一次深度分析数据")

def main():
    """主函数"""
    analyzer = SuperDeepMarketAnalyzer()
    analyzer.run_super_deep_analysis()

if __name__ == '__main__':
    main()