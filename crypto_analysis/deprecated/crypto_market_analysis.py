import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
简化版加密货币市场分析
直接使用币安API获取24小时行情数据进行分析
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.market.data.adapters.binance_adapter import BinanceAdapter
from src.market.data.adapters.base import DataSourceConfig

def analyze_market_data():
    """分析市场数据"""
    logger.info("=== 加密货币市场分析工具 ===\n")
    
    # 创建配置
    config = DataSourceConfig(
        name='binance_market_analysis',
        adapter_class='src.data.adapters.binance_adapter.BinanceAdapter',
        enabled=True,
        priority=1,
        rate_limit={
            'requests_per_minute': 30,
            'requests_per_second': 5
        },
        credentials={
            'api_key': '',
            'api_secret': ''
        },
        default_params={}
    )
    
    try:
        # 初始化适配器
        logger.info("正在连接币安API...")
        adapter = BinanceAdapter(config)
        
        # 健康检查
        is_healthy, message = adapter.health_check()
        if not is_healthy:
            logger.info(f"❌ API连接失败: {message}")
            return
        
        logger.info("✅ API连接成功\n")
        
        # 主流币种列表
        top_coins = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'XRPUSDT', 'ADAUSDT',
            'SOLUSDT', 'DOTUSDT', 'LINKUSDT', 'LTCUSDT', 'AVAXUSDT',
            'ATOMUSDT', 'ALGOUSDT', 'VETUSDT', 'SHIBUSDT', 'DOGEUSDT'
        ]
        
        logger.info(f"正在获取 {len(top_coins)} 个主流币种的24小时行情数据...\n")
        
        # 获取所有币种的24小时行情
        all_tickers = adapter.get_24hr_ticker()
        
        if not all_tickers:
            logger.info("❌ 无法获取市场数据")
            return
        
        # 过滤我们关注的币种
        analyzed_coins = []
        
        for symbol in top_coins:
            if symbol in all_tickers:
                ticker = all_tickers[symbol]
                
                # 计算分析得分
                score = 0
                signals = []
                
                # 价格变化分析
                price_change_pct = ticker['price_change_percent']
                if price_change_pct > 5:
                    signals.append("24h涨幅>5% - 强势表现")
                    score += 15
                elif price_change_pct > 2:
                    signals.append("24h涨幅>2% - 温和上涨")
                    score += 8
                elif price_change_pct < -5:
                    signals.append("24h跌幅>5% - 可能超跌")
                    score += 10
                elif price_change_pct < -2:
                    signals.append("24h跌幅>2% - 调整中")
                    score -= 5
                
                # 成交量分析 (相对于价格的成交量)
                volume_value = ticker['quote_volume']  # USDT成交量
                if volume_value > 1000000000:  # 10亿USDT以上
                    signals.append("成交量超10亿USDT - 高流动性")
                    score += 10
                elif volume_value > 500000000:  # 5亿USDT以上
                    signals.append("成交量超5亿USDT - 良好流动性")
                    score += 5
                
                # 价格波动分析
                high_price = ticker['high_price']
                low_price = ticker['low_price']
                current_price = ticker['last_price']
                
                if high_price > low_price:
                    daily_volatility = (high_price - low_price) / low_price * 100
                    
                    if daily_volatility > 10:
                        signals.append(f"24h波动率{daily_volatility:.1f}% - 高波动")
                        score -= 3
                    elif daily_volatility < 3:
                        signals.append(f"24h波动率{daily_volatility:.1f}% - 低波动")
                        score += 5
                    
                    # 当前价格位置
                    price_position = (current_price - low_price) / (high_price - low_price)
                    if price_position > 0.8:
                        signals.append("价格接近24h高点")
                        score -= 5
                    elif price_position < 0.2:
                        signals.append("价格接近24h低点 - 可能反弹")
                        score += 8
                else:
                    daily_volatility = 0
                    price_position = 0.5
                
                # 市值排名加权 (BTC和ETH权重更高)
                if symbol in ['BTCUSDT', 'ETHUSDT']:
                    score += 5  # 大盘币加分
                elif symbol in ['BNBUSDT', 'XRPUSDT', 'ADAUSDT']:
                    score += 3  # 主流币加分
                
                analyzed_coins.append({
                    'symbol': symbol,
                    'current_price': current_price,
                    'price_change_24h': price_change_pct,
                    'volume_usdt': volume_value,
                    'high_24h': high_price,
                    'low_24h': low_price,
                    'volatility_24h': daily_volatility,
                    'price_position': price_position,
                    'signals': signals,
                    'score': score
                })
        
        # 按得分排序
        analyzed_coins.sort(key=lambda x: x['score'], reverse=True)
        
        # 显示分析结果
        logger.info("=" * 80)
        logger.info("🏆 币种分析排名 (按量化得分排序)")
        logger.info("=" * 80)
        
        for i, coin in enumerate(analyzed_coins, 1):
            symbol_name = coin['symbol'].replace('USDT', '')
            
            logger.info(f"\n{i:2d}. {symbol_name:<8} (得分: {coin['score']:3d}) - {coin['symbol']}")
            logger.info(f"    💰 当前价格: ${coin['current_price']:>12.4f}")
            logger.info(f"    📈 24h变化: {coin['price_change_24h']:>+6.2f}%")
            logger.info(f"    📊 24h成交量: ${coin['volume_usdt']:>12,.0f}")
            logger.info(f"    📉 24h波动率: {coin['volatility_24h']:>6.2f}%")
            logger.info(f"    📍 价格位置: {coin['price_position']*100:>5.1f}% (24h区间)")
            
            if coin['signals']:
                logger.info("    🔍 技术信号:")
                for signal in coin['signals']:
                    logger.info(f"       • {signal}")
        
        # 市场统计
        logger.info(f"\n{'='*80}")
        logger.info("📊 市场统计")
        logger.info(f"{'='*80}")
        
        # 涨跌统计
        rising_coins = [c for c in analyzed_coins if c['price_change_24h'] > 0]
        falling_coins = [c for c in analyzed_coins if c['price_change_24h'] < 0]
        
        logger.info(f"📈 上涨币种: {len(rising_coins)} 个 ({len(rising_coins)/len(analyzed_coins)*100:.1f}%)")
        logger.info(f"📉 下跌币种: {len(falling_coins)} 个 ({len(falling_coins)/len(analyzed_coins)*100:.1f}%)")
        
        # 平均涨跌幅
        avg_change = np.mean([c['price_change_24h'] for c in analyzed_coins])
        logger.info(f"📊 平均涨跌幅: {avg_change:+.2f}%")
        
        # 成交量统计
        total_volume = sum([c['volume_usdt'] for c in analyzed_coins])
        logger.info(f"💰 总成交量: ${total_volume:,.0f}")
        
        # 波动率统计
        avg_volatility = np.mean([c['volatility_24h'] for c in analyzed_coins])
        logger.info(f"📈 平均波动率: {avg_volatility:.2f}%")
        
        # 投资建议
        logger.info(f"\n{'='*80}")
        logger.info("💡 投资建议")
        logger.info(f"{'='*80}")
        
        # 推荐买入
        buy_candidates = [c for c in analyzed_coins if c['score'] > 15][:3]
        if buy_candidates:
            logger.info("🟢 推荐关注 (得分>15):")
            for coin in buy_candidates:
                symbol_name = coin['symbol'].replace('USDT', '')
                logger.info(f"   {symbol_name}: 得分{coin['score']}, 24h{coin['price_change_24h']:+.1f}%")
        
        # 超跌候选
        oversold_candidates = [c for c in analyzed_coins 
                             if c['price_change_24h'] < -3 and c['price_position'] < 0.3][:3]
        if oversold_candidates:
            logger.info("\n🔵 超跌反弹候选:")
            for coin in oversold_candidates:
                symbol_name = coin['symbol'].replace('USDT', '')
                logger.info(f"   {symbol_name}: 24h{coin['price_change_24h']:+.1f}%, 价格位置{coin['price_position']*100:.0f}%")
        
        # 高波动警告
        high_vol_coins = [c for c in analyzed_coins if c['volatility_24h'] > 8]
        if high_vol_coins:
            logger.info("\n🟡 高波动率警告 (>8%):")
            for coin in high_vol_coins[:3]:
                symbol_name = coin['symbol'].replace('USDT', '')
                logger.info(f"   {symbol_name}: 波动率{coin['volatility_24h']:.1f}%")
        
        logger.info(f"\n{'='*80}")
        logger.info("⚠️  风险提示")
        logger.info(f"{'='*80}")
        logger.info("• 以上分析基于24小时行情数据，仅供参考")
        logger.info("• 数字货币市场波动巨大，投资需谨慎")
        logger.info("• 建议结合更长周期的技术分析和基本面研究")
        logger.info("• 分散投资，合理控制仓位")
        logger.info("• 设置止损点，严格风险管理")
        
        logger.info(f"\n📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("🔄 建议每小时更新一次数据以获取最新市场动态")
        
    except Exception as e:
        logger.info(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    analyze_market_data()