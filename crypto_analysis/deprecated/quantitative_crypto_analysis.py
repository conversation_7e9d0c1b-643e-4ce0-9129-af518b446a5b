import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
加密货币量化分析工具
使用币安API获取数据并进行技术分析，找出有潜力的币种
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.market.data.adapters.binance_adapter import BinanceAdapter
from src.market.data.adapters.base import DataSourceConfig

def calculate_technical_indicators(df):
    """计算技术指标"""
    # RSI指标
    def calculate_rsi(prices, window=14):
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    # MACD指标
    def calculate_macd(prices, fast=12, slow=26, signal=9):
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line
        return macd, signal_line, histogram
    
    # 布林带
    def calculate_bollinger_bands(prices, window=20, num_std=2):
        sma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper_band = sma + (std * num_std)
        lower_band = sma - (std * num_std)
        return upper_band, sma, lower_band
    
    # 计算各项指标
    df['sma_20'] = df['close'].rolling(window=20).mean()
    df['sma_50'] = df['close'].rolling(window=50).mean()
    df['ema_12'] = df['close'].ewm(span=12).mean()
    df['ema_26'] = df['close'].ewm(span=26).mean()
    
    df['rsi'] = calculate_rsi(df['close'])
    
    macd, signal, histogram = calculate_macd(df['close'])
    df['macd'] = macd
    df['macd_signal'] = signal
    df['macd_histogram'] = histogram
    
    upper_bb, middle_bb, lower_bb = calculate_bollinger_bands(df['close'])
    df['bb_upper'] = upper_bb
    df['bb_middle'] = middle_bb
    df['bb_lower'] = lower_bb
    
    # 价格变化率
    df['price_change_1d'] = df['close'].pct_change(1) * 100
    df['price_change_7d'] = df['close'].pct_change(7) * 100
    df['price_change_30d'] = df['close'].pct_change(30) * 100
    
    # 波动率
    df['volatility_20d'] = df['close'].rolling(window=20).std() / df['close'].rolling(window=20).mean() * 100
    df['volatility_20d'] = df['volatility_20d'].fillna(0)
    
    # 成交量指标
    df['volume_sma_20'] = df['volume'].rolling(window=20).mean()
    df['volume_ratio'] = df['volume'] / df['volume_sma_20']
    df['volume_ratio'] = df['volume_ratio'].fillna(1)
    
    return df

def analyze_coin_potential(df, symbol):
    """分析币种潜力"""
    if len(df) < 50:
        return None
    
    latest = df.iloc[-1]
    prev_day = df.iloc[-2] if len(df) > 1 else latest
    
    analysis = {
        'symbol': symbol,
        'current_price': latest['close'],
        'price_change_24h': latest.get('price_change_1d', 0),
        'price_change_7d': latest.get('price_change_7d', 0),
        'volume_24h': latest['volume'],
        'volume_ratio': latest.get('volume_ratio', 1),
        'rsi': latest.get('rsi', 50),
        'macd': latest.get('macd', 0),
        'macd_signal': latest.get('macd_signal', 0),
        'volatility': latest.get('volatility_20d', 0),
        
        # 技术分析信号
        'signals': [],
        'score': 0
    }
    
    # RSI分析
    rsi_val = latest.get('rsi', 50)
    if pd.notna(rsi_val):
        if rsi_val < 30:
            analysis['signals'].append('RSI超卖 - 买入信号')
            analysis['score'] += 15
        elif rsi_val > 70:
            analysis['signals'].append('RSI超买 - 卖出信号')
            analysis['score'] -= 10
        elif 40 <= rsi_val <= 60:
            analysis['signals'].append('RSI中性区间')
            analysis['score'] += 5
    
    # MACD分析
    macd_val = latest.get('macd', 0)
    macd_signal_val = latest.get('macd_signal', 0)
    prev_macd = prev_day.get('macd', 0)
    prev_macd_signal = prev_day.get('macd_signal', 0)
    
    if pd.notna(macd_val) and pd.notna(macd_signal_val):
        if macd_val > macd_signal_val and prev_macd <= prev_macd_signal:
            analysis['signals'].append('MACD金叉 - 买入信号')
            analysis['score'] += 20
        elif macd_val < macd_signal_val and prev_macd >= prev_macd_signal:
            analysis['signals'].append('MACD死叉 - 卖出信号')
            analysis['score'] -= 15
    
    # 移动平均线分析
    sma_20 = latest.get('sma_20', latest['close'])
    sma_50 = latest.get('sma_50', latest['close'])
    
    if pd.notna(sma_20) and pd.notna(sma_50):
        if latest['close'] > sma_20 > sma_50:
            analysis['signals'].append('价格在均线之上 - 上升趋势')
            analysis['score'] += 10
        elif latest['close'] < sma_20 < sma_50:
            analysis['signals'].append('价格在均线之下 - 下降趋势')
            analysis['score'] -= 10
    
    # 布林带分析
    bb_upper = latest.get('bb_upper', latest['close'])
    bb_lower = latest.get('bb_lower', latest['close'])
    
    if pd.notna(bb_upper) and pd.notna(bb_lower) and bb_upper != bb_lower:
        bb_position = (latest['close'] - bb_lower) / (bb_upper - bb_lower)
        if bb_position < 0.2:
            analysis['signals'].append('接近布林带下轨 - 可能反弹')
            analysis['score'] += 12
        elif bb_position > 0.8:
            analysis['signals'].append('接近布林带上轨 - 可能回调')
            analysis['score'] -= 8
    
    # 成交量分析
    volume_ratio = latest.get('volume_ratio', 1)
    if pd.notna(volume_ratio):
        if volume_ratio > 1.5:
            analysis['signals'].append('成交量放大 - 趋势确认')
            analysis['score'] += 8
        elif volume_ratio < 0.5:
            analysis['signals'].append('成交量萎缩 - 趋势可能反转')
            analysis['score'] -= 5
    
    # 价格变化分析
    if latest['price_change_7d'] > 15:
        analysis['signals'].append('7日涨幅超过15% - 强势币种')
        analysis['score'] += 10
    elif latest['price_change_7d'] < -15:
        analysis['signals'].append('7日跌幅超过15% - 可能超跌')
        analysis['score'] += 8
    
    # 波动率分析
    if latest['volatility'] < 5:
        analysis['signals'].append('低波动率 - 可能突破')
        analysis['score'] += 5
    elif latest['volatility'] > 20:
        analysis['signals'].append('高波动率 - 风险较高')
        analysis['score'] -= 5
    
    return analysis

def main():
    """主分析函数"""
    logger.info("=== 加密货币量化分析工具 ===\n")
    
    # 创建配置
    config = DataSourceConfig(
        name='binance_analysis',
        adapter_class='src.data.adapters.binance_adapter.BinanceAdapter',
        enabled=True,
        priority=1,
        rate_limit={
            'requests_per_minute': 30,
            'requests_per_second': 5
        },
        credentials={
            'api_key': '',
            'api_secret': ''
        },
        default_params={}
    )
    
    try:
        # 初始化适配器
        logger.info("正在初始化币安API...")
        adapter = BinanceAdapter(config)
        
        # 健康检查
        is_healthy, message = adapter.health_check()
        if not is_healthy:
            logger.info(f"API连接失败: {message}")
            return
        
        logger.info("✓ API连接成功")
        
        # 分析的币种列表（主流币种，更新后的交易对）
        coins_to_analyze = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'XRPUSDT', 'ADAUSDT',
            'SOLUSDT', 'DOTUSDT', 'LINKUSDT', 'UNIUSDT', 'LTCUSDT',
            'AVAXUSDT', 'ATOMUSDT', 'ALGOUSDT', 'VETUSDT', 'SHIBUSDT',
            'DOGEUSDT', 'TRXUSDT', 'XLMUSDT', 'BCHUSDT', 'ETCUSDT'
        ]
        
        logger.info(f"\n正在分析 {len(coins_to_analyze)} 个币种...")
        logger.info("获取60天历史数据进行技术分析...\n")
        
        # 设置日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=60)
        
        analyses = []
        
        for i, symbol in enumerate(coins_to_analyze, 1):
            try:
                logger.info(f"[{i}/{len(coins_to_analyze)}] 分析 {symbol}...")
                
                # 获取历史数据
                data = adapter.fetch_historical_data(symbol, start_date, end_date, '1d')
                
                if not data:
                    logger.info(f"  ⚠️ 无法获取 {symbol} 的数据")
                    continue
                
                # 转换为DataFrame
                df = pd.DataFrame([{
                    'timestamp': d.timestamp,
                    'open': d.open,
                    'high': d.high,
                    'low': d.low,
                    'close': d.close,
                    'volume': d.volume
                } for d in data])
                
                df.set_index('timestamp', inplace=True)
                df.sort_index(inplace=True)
                
                # 计算技术指标
                df = calculate_technical_indicators(df)
                
                # 分析潜力
                analysis = analyze_coin_potential(df, symbol)
                if analysis:
                    analyses.append(analysis)
                    logger.info(f"  ✓ 完成分析，得分: {analysis['score']}")
                
            except Exception as e:
                logger.info(f"  ❌ 分析 {symbol} 时出错: {e}")
                continue
        
        # 按得分排序
        analyses.sort(key=lambda x: x['score'], reverse=True)
        
        logger.info(f"\n=== 量化分析结果 ===")
        logger.info(f"共分析了 {len(analyses)} 个币种\n")
        
        # 显示前10名
        logger.info("🏆 TOP 10 推荐币种：\n")
        
        for i, analysis in enumerate(analyses[:10], 1):
            logger.info(f"{i:2d}. {analysis['symbol']:<10} (得分: {analysis['score']:3d})")
            logger.info(f"    当前价格: ${analysis['current_price']:>10.4f}")
            logger.info(f"    24h变化: {analysis['price_change_24h']:>6.2f}%")
            logger.info(f"    7日变化: {analysis['price_change_7d']:>6.2f}%")
            logger.info(f"    RSI指标: {analysis['rsi']:>6.1f}")
            logger.info(f"    波动率:  {analysis['volatility']:>6.2f}%")
            logger.info(f"    成交量比: {analysis['volume_ratio']:>5.2f}")
            
            if analysis['signals']:
                logger.info("    技术信号:")
                for signal in analysis['signals'][:3]:  # 只显示前3个信号
                    logger.info(f"      • {signal}")
            logger.info()
        
        # 市场概况
        logger.info("=== 市场概况 ===")
        
        # 统计分析
        scores = [a['score'] for a in analyses]
        positive_scores = [s for s in scores if s > 0]
        negative_scores = [s for s in scores if s < 0]
        
        if len(analyses) > 0:
            logger.info(f"平均得分: {np.mean(scores):.1f}")
            logger.info(f"看涨币种: {len(positive_scores)} 个 ({len(positive_scores)/len(analyses)*100:.1f}%)")
            logger.info(f"看跌币种: {len(negative_scores)} 个 ({len(negative_scores)/len(analyses)*100:.1f}%)")
        else:
            logger.info("没有足够的数据进行分析")
            return
        
        # 价格变化统计
        price_changes_24h = [a['price_change_24h'] for a in analyses]
        price_changes_7d = [a['price_change_7d'] for a in analyses]
        
        logger.info(f"\n24小时价格变化:")
        logger.info(f"  平均: {np.mean(price_changes_24h):+.2f}%")
        logger.info(f"  上涨币种: {len([x for x in price_changes_24h if x > 0])} 个")
        logger.info(f"  下跌币种: {len([x for x in price_changes_24h if x < 0])} 个")
        
        logger.info(f"\n7天价格变化:")
        logger.info(f"  平均: {np.mean(price_changes_7d):+.2f}%")
        logger.info(f"  涨幅>10%: {len([x for x in price_changes_7d if x > 10])} 个")
        logger.info(f"  跌幅>10%: {len([x for x in price_changes_7d if x < -10])} 个")
        
        # 特别推荐
        logger.info(f"\n=== 特别推荐 ===")
        
        # 超跌反弹候选
        oversold_candidates = [a for a in analyses if a['rsi'] < 35 and a['price_change_7d'] < -10]
        if oversold_candidates:
            logger.info("💡 超跌反弹候选 (RSI<35 且7日跌幅>10%):")
            for candidate in oversold_candidates[:3]:
                logger.info(f"   {candidate['symbol']}: RSI={candidate['rsi']:.1f}, 7日跌幅={candidate['price_change_7d']:.1f}%")
        
        # 强势币种
        strong_coins = [a for a in analyses if a['score'] > 20 and a['price_change_7d'] > 5]
        if strong_coins:
            logger.info("\n🚀 强势币种 (得分>20 且7日涨幅>5%):")
            for coin in strong_coins[:3]:
                logger.info(f"   {coin['symbol']}: 得分={coin['score']}, 7日涨幅={coin['price_change_7d']:.1f}%")
        
        # 成交量异常
        volume_anomaly = [a for a in analyses if a['volume_ratio'] > 2.0]
        if volume_anomaly:
            logger.info(f"\n📈 成交量异常放大 (成交量比>2.0):")
            for coin in volume_anomaly[:3]:
                logger.info(f"   {coin['symbol']}: 成交量比={coin['volume_ratio']:.1f}")
        
        logger.info(f"\n⚠️  投资提醒:")
        logger.info("• 以上分析仅供参考，不构成投资建议")
        logger.info("• 数字货币投资风险较高，请理性投资")
        logger.info("• 建议结合基本面分析和风险管理")
        logger.info("• 分散投资，控制仓位")
        
        logger.info(f"\n分析完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        logger.info(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()