import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
扩展币种量化分析工具
分析更多热门币种，包括主流币、DeFi代币、Layer1/Layer2项目等
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.market.data.adapters.binance_adapter import BinanceAdapter
from src.market.data.adapters.base import DataSourceConfig

class ExtendedCryptoAnalyzer:
    """扩展加密货币分析器"""
    
    def __init__(self):
        # 币种分类
        self.coin_categories = {
            '主流币种': ['BTCUSDT', 'ETHUSDT', 'BNBUSDT'],
            'Layer1公链': ['SOLUSDT', 'ADAUSDT', 'DOTUSDT', 'AVAXUSDT', 'NEARUSDT', 'FTMUSDT', 'ATOMUSDT'],
            'Layer2解决方案': ['MATICUSDT', 'OPUSDT', 'ARBUSDT'],
            'DeFi代币': ['UNIUSDT', 'AAVEUSDT', 'SUSHIUSDT', 'COMPUSDT', 'MKRUSDT', 'CRVUSDT'],
            '智能合约平台': ['TRXUSDT', 'EOSUSDT', 'NEOUSDT'],
            '支付币种': ['XRPUSDT', 'XLMUSDT', 'LTCUSDT', 'BCHUSDT'],
            '存储概念': ['FILUSDT', 'ARUSDT', 'STORJUSDT'],
            '预言机': ['LINKUSDT', 'BANDUSDT'],
            '交换代币': ['CAKEUSDT', '1INCHUSDT', 'DYDXUSDT'],
            'NFT/元宇宙': ['MANAUSDT', 'SANDUSDT', 'AXSUSDT', 'ENJUSDT'],
            'Meme币': ['DOGEUSDT', 'SHIBUSDT', 'PEPEUSDT'],
            '稳定币相关': ['LUNAUSDT', 'USTCUSDT'],
            '新兴概念': ['APTUSDT', 'SUIUSDT', 'INJUSDT', 'TIAUSDT', 'RNDRUSDT']
        }
        
        # 所有币种列表
        self.all_coins = []
        for category, coins in self.coin_categories.items():
            self.all_coins.extend(coins)
        
        # 去重
        self.all_coins = list(set(self.all_coins))
        
    def initialize_adapter(self):
        """初始化币安适配器"""
        config = DataSourceConfig(
            name='extended_crypto_analysis',
            adapter_class='src.data.adapters.binance_adapter.BinanceAdapter',
            enabled=True,
            priority=1,
            rate_limit={
                'requests_per_minute': 25,
                'requests_per_second': 3
            },
            credentials={
                'api_key': '',
                'api_secret': ''
            },
            default_params={}
        )
        
        return BinanceAdapter(config)
    
    def analyze_single_coin(self, adapter, symbol):
        """分析单个币种"""
        try:
            # 获取24小时行情
            ticker = adapter.get_24hr_ticker(symbol)
            if not ticker:
                return None
            
            # 计算技术指标得分
            score = 0
            signals = []
            
            # 价格变化分析
            price_change_pct = ticker['price_change_percent']
            if price_change_pct > 10:
                signals.append("24h涨幅>10% - 强势突破")
                score += 25
            elif price_change_pct > 5:
                signals.append("24h涨幅>5% - 强势表现")
                score += 15
            elif price_change_pct > 2:
                signals.append("24h涨幅>2% - 温和上涨")
                score += 8
            elif price_change_pct < -10:
                signals.append("24h跌幅>10% - 深度超跌")
                score += 20  # 反弹机会
            elif price_change_pct < -5:
                signals.append("24h跌幅>5% - 可能超跌")
                score += 12
            elif price_change_pct < -2:
                signals.append("24h跌幅>2% - 调整中")
                score -= 3
            
            # 成交量分析
            volume_value = ticker['quote_volume']
            if volume_value > 500000000:  # 5亿USDT以上
                signals.append("成交量>5亿USDT - 高流动性")
                score += 10
            elif volume_value > 100000000:  # 1亿USDT以上
                signals.append("成交量>1亿USDT - 良好流动性")
                score += 5
            elif volume_value < 10000000:  # 小于1000万USDT
                signals.append("成交量<1000万USDT - 流动性不足")
                score -= 5
            
            # 波动率分析
            high_price = ticker['high_price']
            low_price = ticker['low_price']
            current_price = ticker['last_price']
            
            if high_price > low_price:
                daily_volatility = (high_price - low_price) / low_price * 100
                
                if daily_volatility > 20:
                    signals.append(f"24h波动率{daily_volatility:.1f}% - 极高风险")
                    score -= 10
                elif daily_volatility > 15:
                    signals.append(f"24h波动率{daily_volatility:.1f}% - 高风险")
                    score -= 5
                elif daily_volatility > 10:
                    signals.append(f"24h波动率{daily_volatility:.1f}% - 中等风险")
                    score -= 2
                elif daily_volatility < 3:
                    signals.append(f"24h波动率{daily_volatility:.1f}% - 低风险")
                    score += 5
                
                # 价格位置分析
                price_position = (current_price - low_price) / (high_price - low_price)
                if price_position > 0.9:
                    signals.append("价格接近24h高点 - 谨慎追高")
                    score -= 8
                elif price_position > 0.8:
                    signals.append("价格位于24h高位")
                    score -= 3
                elif price_position < 0.1:
                    signals.append("价格接近24h低点 - 反弹机会")
                    score += 15
                elif price_position < 0.2:
                    signals.append("价格位于24h低位 - 可能反弹")
                    score += 10
            else:
                daily_volatility = 0
                price_position = 0.5
            
            return {
                'symbol': symbol,
                'current_price': current_price,
                'price_change_24h': price_change_pct,
                'volume_usdt': volume_value,
                'high_24h': high_price,
                'low_24h': low_price,
                'volatility_24h': daily_volatility,
                'price_position': price_position,
                'signals': signals,
                'score': score
            }
            
        except Exception as e:
            logger.info(f"   ⚠️ 分析 {symbol} 失败: {str(e)[:50]}...")
            return None
    
    def run_extended_analysis(self):
        """运行扩展分析"""
        logger.info("=" * 120)
        logger.info("🔍 扩展币种量化分析 - 深度市场扫描")
        logger.info("=" * 120)
        
        try:
            # 初始化适配器
            logger.info("🔌 正在连接币安API...")
            adapter = self.initialize_adapter()
            
            # 健康检查
            is_healthy, message = adapter.health_check()
            if not is_healthy:
                logger.info(f"❌ API连接失败: {message}")
                return
            
            logger.info("✅ API连接成功\n")
            
            logger.info(f"📊 正在分析 {len(self.all_coins)} 个币种...")
            logger.info(f"📂 涵盖 {len(self.coin_categories)} 个类别\n")
            
            # 分析所有币种
            all_analyses = []
            successful_count = 0
            
            for i, symbol in enumerate(self.all_coins, 1):
                logger.info(f"[{i:2d}/{len(self.all_coins)}] 分析 {symbol.replace('USDT', ''):<8}", end=' ')
                
                analysis = self.analyze_single_coin(adapter, symbol)
                if analysis:
                    all_analyses.append(analysis)
                    successful_count += 1
                    logger.info(f"✅ 得分: {analysis['score']:3d}")
                else:
                    logger.info("❌ 失败")
            
            logger.info(f"\n✅ 成功分析 {successful_count} 个币种")
            
            if not all_analyses:
                logger.info("❌ 没有获取到有效数据")
                return
            
            # 按得分排序
            all_analyses.sort(key=lambda x: x['score'], reverse=True)
            
            # 分类显示结果
            self.display_category_results(all_analyses)
            
            # 显示总体排名
            self.display_overall_ranking(all_analyses)
            
            # 市场统计
            self.display_market_statistics(all_analyses)
            
            # 投资建议
            self.display_investment_recommendations(all_analyses)
            
        except Exception as e:
            logger.info(f"❌ 扩展分析出现错误: {e}")
            import traceback
            traceback.print_exc()
    
    def display_category_results(self, all_analyses):
        """按类别显示结果"""
        logger.info("\n" + "=" * 120)
        logger.info("📋 分类分析结果")
        logger.info("=" * 120)
        
        for category, coins in self.coin_categories.items():
            category_analyses = [a for a in all_analyses if a['symbol'] in coins]
            if not category_analyses:
                continue
            
            # 按得分排序
            category_analyses.sort(key=lambda x: x['score'], reverse=True)
            
            logger.info(f"\n🏷️  {category} ({len(category_analyses)} 个):")
            
            for i, analysis in enumerate(category_analyses[:5], 1):  # 只显示前5名
                symbol_name = analysis['symbol'].replace('USDT', '')
                score_level = "🟢" if analysis['score'] >= 20 else "🟡" if analysis['score'] >= 10 else "🔴"
                
                logger.info(f"   {i}. {symbol_name:<8} {score_level} 得分:{analysis['score']:3d} | "
                      f"24h:{analysis['price_change_24h']:+6.2f}% | "
                      f"价格:${analysis['current_price']:>10.4f} | "
                      f"成交量:${analysis['volume_usdt']:>12,.0f}")
                
                # 显示主要信号
                if analysis['signals']:
                    top_signals = analysis['signals'][:2]  # 只显示前2个信号
                    for signal in top_signals:
                        logger.info(f"      • {signal}")
    
    def display_overall_ranking(self, all_analyses):
        """显示总体排名"""
        logger.info(f"\n" + "=" * 120)
        logger.info("🏆 总体排名 TOP 20")
        logger.info("=" * 120)
        
        for i, analysis in enumerate(all_analyses[:20], 1):
            symbol_name = analysis['symbol'].replace('USDT', '')
            
            # 判断币种类别
            category = "未分类"
            for cat_name, coins in self.coin_categories.items():
                if analysis['symbol'] in coins:
                    category = cat_name
                    break
            
            score_level = "🥇" if i <= 3 else "🥈" if i <= 10 else "🥉"
            
            logger.info(f"{score_level} {i:2d}. {symbol_name:<8} | 得分:{analysis['score']:3d} | "
                  f"24h:{analysis['price_change_24h']:+6.2f}% | "
                  f"价格:${analysis['current_price']:>10.4f} | "
                  f"类别:{category}")
    
    def display_market_statistics(self, all_analyses):
        """显示市场统计"""
        logger.info(f"\n" + "=" * 120)
        logger.info("📊 市场统计分析")
        logger.info("=" * 120)
        
        # 基础统计
        total_coins = len(all_analyses)
        rising_coins = len([a for a in all_analyses if a['price_change_24h'] > 0])
        falling_coins = total_coins - rising_coins
        
        avg_change = np.mean([a['price_change_24h'] for a in all_analyses])
        avg_volume = np.mean([a['volume_usdt'] for a in all_analyses])
        avg_volatility = np.mean([a['volatility_24h'] for a in all_analyses])
        
        logger.info(f"📈 上涨币种: {rising_coins} 个 ({rising_coins/total_coins*100:.1f}%)")
        logger.info(f"📉 下跌币种: {falling_coins} 个 ({falling_coins/total_coins*100:.1f}%)")
        logger.info(f"📊 平均涨跌幅: {avg_change:+.2f}%")
        logger.info(f"💰 平均成交量: ${avg_volume:,.0f}")
        logger.info(f"📈 平均波动率: {avg_volatility:.2f}%")
        
        # 各类别表现
        logger.info(f"\n📂 分类表现:")
        for category, coins in self.coin_categories.items():
            category_analyses = [a for a in all_analyses if a['symbol'] in coins]
            if category_analyses:
                cat_avg_change = np.mean([a['price_change_24h'] for a in category_analyses])
                cat_rising = len([a for a in category_analyses if a['price_change_24h'] > 0])
                
                performance = "🟢" if cat_avg_change > 2 else "🟡" if cat_avg_change > -2 else "🔴"
                logger.info(f"   {performance} {category:<15}: 平均{cat_avg_change:+5.2f}% | "
                      f"上涨比例: {cat_rising}/{len(category_analyses)} ({cat_rising/len(category_analyses)*100:.0f}%)")
        
        # 极端表现
        best_performer = max(all_analyses, key=lambda x: x['price_change_24h'])
        worst_performer = min(all_analyses, key=lambda x: x['price_change_24h'])
        highest_volume = max(all_analyses, key=lambda x: x['volume_usdt'])
        
        logger.info(f"\n🎯 极端表现:")
        logger.info(f"   🚀 最佳表现: {best_performer['symbol'].replace('USDT', '')} "
              f"({best_performer['price_change_24h']:+.2f}%)")
        logger.info(f"   💥 最差表现: {worst_performer['symbol'].replace('USDT', '')} "
              f"({worst_performer['price_change_24h']:+.2f}%)")
        logger.info(f"   💰 最高成交量: {highest_volume['symbol'].replace('USDT', '')} "
              f"(${highest_volume['volume_usdt']:,.0f})")
    
    def display_investment_recommendations(self, all_analyses):
        """显示投资建议"""
        logger.info(f"\n" + "=" * 120)
        logger.info("💡 投资建议")
        logger.info("=" * 120)
        
        # 强势推荐 (得分>25)
        strong_buy = [a for a in all_analyses if a['score'] > 25]
        if strong_buy:
            logger.info("🟢 强势推荐 (得分>25):")
            for coin in strong_buy[:5]:
                logger.info(f"   • {coin['symbol'].replace('USDT', '')}: 得分{coin['score']}, "
                      f"24h{coin['price_change_24h']:+.2f}%")
        
        # 谨慎推荐 (得分15-25)
        moderate_buy = [a for a in all_analyses if 15 <= a['score'] <= 25]
        if moderate_buy:
            logger.info(f"\n🟡 谨慎推荐 (得分15-25):")
            for coin in moderate_buy[:5]:
                logger.info(f"   • {coin['symbol'].replace('USDT', '')}: 得分{coin['score']}, "
                      f"24h{coin['price_change_24h']:+.2f}%")
        
        # 超跌反弹候选
        oversold_candidates = [a for a in all_analyses 
                             if a['price_change_24h'] < -8 and a['price_position'] < 0.3]
        if oversold_candidates:
            logger.info(f"\n🔵 超跌反弹候选:")
            for coin in sorted(oversold_candidates, key=lambda x: x['price_change_24h'])[:5]:
                logger.info(f"   • {coin['symbol'].replace('USDT', '')}: "
                      f"24h{coin['price_change_24h']:+.2f}%, 价格位置{coin['price_position']*100:.0f}%")
        
        # 高风险警告
        high_risk = [a for a in all_analyses if a['volatility_24h'] > 20]
        if high_risk:
            logger.info(f"\n🔴 高风险警告 (波动率>20%):")
            for coin in sorted(high_risk, key=lambda x: x['volatility_24h'], reverse=True)[:5]:
                logger.info(f"   • {coin['symbol'].replace('USDT', '')}: 波动率{coin['volatility_24h']:.1f}%")
        
        # 类别建议
        logger.info(f"\n📂 分类投资建议:")
        category_scores = {}
        for category, coins in self.coin_categories.items():
            category_analyses = [a for a in all_analyses if a['symbol'] in coins]
            if category_analyses:
                avg_score = np.mean([a['score'] for a in category_analyses])
                category_scores[category] = avg_score
        
        # 按平均得分排序
        sorted_categories = sorted(category_scores.items(), key=lambda x: x[1], reverse=True)
        
        for i, (category, avg_score) in enumerate(sorted_categories[:5], 1):
            recommendation = "推荐配置" if avg_score > 15 else "谨慎配置" if avg_score > 5 else "暂不推荐"
            logger.info(f"   {i}. {category:<15}: 平均得分{avg_score:.1f} - {recommendation}")
        
        logger.info(f"\n⚠️  风险提醒:")
        logger.info("• 以上分析基于24小时数据，短期波动性较大")
        logger.info("• 建议结合更长期的技术分析和基本面研究")
        logger.info("• 数字货币投资风险极高，请严格控制仓位")
        logger.info("• 分散投资，单一币种仓位不宜过重")
        logger.info("• 设置止损，及时止盈")
        
        logger.info(f"\n📅 分析完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def main():
    """主函数"""
    analyzer = ExtendedCryptoAnalyzer()
    analyzer.run_extended_analysis()

if __name__ == '__main__':
    main()