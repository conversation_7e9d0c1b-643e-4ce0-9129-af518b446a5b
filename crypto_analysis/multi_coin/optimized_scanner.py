import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
优化版扩展币种分析工具
专注于更多币种的快速分析，使用批量API调用提高效率
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.market.data.adapters.binance_adapter import BinanceAdapter
from src.market.data.adapters.base import DataSourceConfig

def optimized_multi_coin_analysis():
    """优化版多币种分析"""
    logger.info("=" * 100)
    logger.info("🚀 优化版扩展币种分析 - 高效市场扫描")
    logger.info("=" * 100)
    
    # 币种分类 (优选有代表性的币种)
    coin_categories = {
        '🥇 主流币种': ['BTCUSDT', 'ETHUSDT', 'BNBUSDT'],
        '⛓️ Layer1公链': ['SOLUSDT', 'ADAUSDT', 'DOTUSDT', 'AVAXUSDT', 'ATOMUSDT', 'NEARUSDT'],
        '🔗 Layer2项目': ['MATICUSDT', 'OPUSDT', 'ARBUSDT'],
        '🏦 DeFi协议': ['UNIUSDT', 'AAVEUSDT', 'MKRUSDT', 'CRVUSDT', 'SUSHIUSDT'],
        '💸 支付货币': ['XRPUSDT', 'XLMUSDT', 'LTCUSDT', 'BCHUSDT'],
        '🔮 预言机': ['LINKUSDT', 'BANDUSDT'],
        '🎮 NFT/元宇宙': ['MANAUSDT', 'SANDUSDT', 'AXSUSDT', 'ENJUSDT'],
        '🐕 Meme币': ['DOGEUSDT', 'SHIBUSDT', 'PEPEUSDT'],
        '🆕 新兴项目': ['APTUSDT', 'SUIUSDT', 'INJUSDT', 'TIAUSDT'],
        '🔄 交易所币': ['CAKEUSDT', '1INCHUSDT', 'GMXUSDT']
    }
    
    try:
        # 初始化适配器
        logger.info("🔌 正在连接币安API...")
        config = DataSourceConfig(
            name='multi_coin_analysis',
            adapter_class='src.data.adapters.binance_adapter.BinanceAdapter',
            enabled=True,
            priority=1,
            rate_limit={
                'requests_per_minute': 30,
                'requests_per_second': 5
            },
            credentials={
                'api_key': '',
                'api_secret': ''
            },
            default_params={}
        )
        
        adapter = BinanceAdapter(config)
        
        # 健康检查
        is_healthy, message = adapter.health_check()
        if not is_healthy:
            logger.info(f"❌ API连接失败: {message}")
            return
        
        logger.info("✅ API连接成功")
        
        # 获取所有币种的24小时行情 (批量获取，效率更高)
        logger.info("📊 正在批量获取市场数据...")
        all_tickers_data = adapter.get_24hr_ticker()
        
        if not all_tickers_data:
            logger.info("❌ 无法获取市场数据")
            return
        
        logger.info(f"✅ 成功获取 {len(all_tickers_data)} 个交易对的数据")
        
        # 分析每个类别
        all_analyses = []
        category_results = {}
        
        total_analyzed = 0
        
        for category, symbols in coin_categories.items():
            logger.info(f"\n{category}:")
            category_analyses = []
            
            for symbol in symbols:
                if symbol in all_tickers_data:
                    ticker = all_tickers_data[symbol]
                    analysis = analyze_coin_data(symbol, ticker)
                    if analysis:
                        category_analyses.append(analysis)
                        all_analyses.append(analysis)
                        total_analyzed += 1
                        
                        symbol_name = symbol.replace('USDT', '')
                        score_emoji = get_score_emoji(analysis['score'])
                        logger.info(f"   {score_emoji} {symbol_name:<8}: 得分{analysis['score']:3d} | "
                              f"24h{analysis['price_change_24h']:+6.2f}% | "
                              f"${analysis['current_price']:>10.4f}")
                else:
                    logger.info(f"   ❌ {symbol.replace('USDT', ''):<8}: 数据不可用")
            
            # 保存类别结果
            category_analyses.sort(key=lambda x: x['score'], reverse=True)
            category_results[category] = category_analyses
        
        logger.info(f"\n✅ 总共分析了 {total_analyzed} 个币种")
        
        # 显示总体排名
        display_top_performers(all_analyses)
        
        # 显示类别统计
        display_category_statistics(category_results)
        
        # 市场概况
        display_market_overview(all_analyses)
        
        # 投资建议
        display_investment_advice(all_analyses, category_results)
        
    except Exception as e:
        logger.info(f"❌ 分析过程出现错误: {e}")
        import traceback
        traceback.print_exc()

def analyze_coin_data(symbol, ticker):
    """分析单个币种数据"""
    try:
        score = 0
        signals = []
        
        # 价格变化分析
        price_change_pct = ticker['price_change_percent']
        if price_change_pct > 15:
            signals.append("强势暴涨")
            score += 30
        elif price_change_pct > 8:
            signals.append("强势上涨")
            score += 20
        elif price_change_pct > 3:
            signals.append("温和上涨")
            score += 10
        elif price_change_pct < -15:
            signals.append("深度超跌")
            score += 25  # 反弹机会
        elif price_change_pct < -8:
            signals.append("显著下跌")
            score += 15
        elif price_change_pct < -3:
            signals.append("温和下跌")
            score += 5
        
        # 成交量分析
        volume_value = ticker['quote_volume']
        if volume_value > 1000000000:  # 10亿+
            signals.append("超高流动性")
            score += 15
        elif volume_value > 300000000:  # 3亿+
            signals.append("高流动性")
            score += 10
        elif volume_value > 50000000:  # 5000万+
            signals.append("中等流动性")
            score += 5
        elif volume_value < 5000000:  # 小于500万
            signals.append("低流动性")
            score -= 10
        
        # 价格位置分析
        high_price = ticker['high_price']
        low_price = ticker['low_price']
        current_price = ticker['last_price']
        
        if high_price > low_price:
            price_position = (current_price - low_price) / (high_price - low_price)
            
            if price_position > 0.9:
                signals.append("接近高点")
                score -= 10
            elif price_position < 0.1:
                signals.append("接近低点")
                score += 20
            elif price_position < 0.3:
                signals.append("偏低位置")
                score += 10
            
            # 波动率分析
            volatility = (high_price - low_price) / low_price * 100
            if volatility > 25:
                signals.append("极高波动")
                score -= 15
            elif volatility > 15:
                signals.append("高波动")
                score -= 8
            elif volatility < 5:
                signals.append("低波动")
                score += 8
        else:
            price_position = 0.5
            volatility = 0
        
        return {
            'symbol': symbol,
            'current_price': current_price,
            'price_change_24h': price_change_pct,
            'volume_usdt': volume_value,
            'high_24h': high_price,
            'low_24h': low_price,
            'price_position': price_position,
            'volatility': volatility,
            'signals': signals,
            'score': score
        }
        
    except Exception as e:
        return None

def get_score_emoji(score):
    """根据得分返回表情符号"""
    if score >= 30:
        return "🔥"
    elif score >= 20:
        return "🟢"
    elif score >= 10:
        return "🟡"
    elif score >= 0:
        return "⚪"
    else:
        return "🔴"

def display_top_performers(all_analyses):
    """显示顶级表现者"""
    logger.info(f"\n" + "=" * 100)
    logger.info("🏆 TOP 15 表现最佳币种")
    logger.info("=" * 100)
    
    # 按得分排序
    top_performers = sorted(all_analyses, key=lambda x: x['score'], reverse=True)[:15]
    
    for i, analysis in enumerate(top_performers, 1):
        symbol_name = analysis['symbol'].replace('USDT', '')
        score_emoji = get_score_emoji(analysis['score'])
        
        medal = "🥇" if i <= 3 else "🥈" if i <= 8 else "🥉"
        
        logger.info(f"{medal} {i:2d}. {symbol_name:<8} {score_emoji} | "
              f"得分:{analysis['score']:3d} | "
              f"24h:{analysis['price_change_24h']:+6.2f}% | "
              f"价格:${analysis['current_price']:>10.4f} | "
              f"成交量:${analysis['volume_usdt']:>10.0f}")
        
        # 显示关键信号
        if analysis['signals']:
            key_signals = analysis['signals'][:2]
            logger.info(f"      💡 {' | '.join(key_signals)}")

def display_category_statistics(category_results):
    """显示类别统计"""
    logger.info(f"\n" + "=" * 100)
    logger.info("📊 各类别表现统计")
    logger.info("=" * 100)
    
    category_stats = []
    
    for category, analyses in category_results.items():
        if analyses:
            avg_score = np.mean([a['score'] for a in analyses])
            avg_change = np.mean([a['price_change_24h'] for a in analyses])
            best_coin = max(analyses, key=lambda x: x['score'])
            rising_count = len([a for a in analyses if a['price_change_24h'] > 0])
            
            category_stats.append({
                'category': category,
                'avg_score': avg_score,
                'avg_change': avg_change,
                'best_coin': best_coin,
                'rising_ratio': rising_count / len(analyses),
                'total_coins': len(analyses)
            })
    
    # 按平均得分排序
    category_stats.sort(key=lambda x: x['avg_score'], reverse=True)
    
    for i, stat in enumerate(category_stats, 1):
        performance_emoji = "🔥" if stat['avg_score'] > 15 else "🟢" if stat['avg_score'] > 8 else "🟡" if stat['avg_score'] > 0 else "🔴"
        
        logger.info(f"{performance_emoji} {i}. {stat['category']}")
        logger.info(f"     平均得分: {stat['avg_score']:5.1f} | "
              f"平均涨跌: {stat['avg_change']:+5.2f}% | "
              f"上涨比例: {stat['rising_ratio']*100:.0f}% ({stat['rising_ratio']*stat['total_coins']:.0f}/{stat['total_coins']})")
        logger.info(f"     最佳币种: {stat['best_coin']['symbol'].replace('USDT', '')} "
              f"(得分{stat['best_coin']['score']}, {stat['best_coin']['price_change_24h']:+.2f}%)")
        logger.info()

def display_market_overview(all_analyses):
    """显示市场概况"""
    logger.info("=" * 100)
    logger.info("🌍 市场概况")
    logger.info("=" * 100)
    
    total_coins = len(all_analyses)
    rising_coins = len([a for a in all_analyses if a['price_change_24h'] > 0])
    
    # 价格变化分布
    big_gainers = len([a for a in all_analyses if a['price_change_24h'] > 10])
    moderate_gainers = len([a for a in all_analyses if 3 < a['price_change_24h'] <= 10])
    small_gainers = len([a for a in all_analyses if 0 < a['price_change_24h'] <= 3])
    
    small_losers = len([a for a in all_analyses if -3 <= a['price_change_24h'] < 0])
    moderate_losers = len([a for a in all_analyses if -10 <= a['price_change_24h'] < -3])
    big_losers = len([a for a in all_analyses if a['price_change_24h'] < -10])
    
    logger.info(f"📈 总体表现:")
    logger.info(f"   上涨币种: {rising_coins}/{total_coins} ({rising_coins/total_coins*100:.1f}%)")
    logger.info(f"   下跌币种: {total_coins-rising_coins}/{total_coins} ({(total_coins-rising_coins)/total_coins*100:.1f}%)")
    
    logger.info(f"\n📊 涨跌幅分布:")
    logger.info(f"   🚀 大幅上涨(>10%):  {big_gainers:2d} 个")
    logger.info(f"   🟢 温和上涨(3-10%): {moderate_gainers:2d} 个")
    logger.info(f"   🟡 小幅上涨(0-3%):  {small_gainers:2d} 个")
    logger.info(f"   🟡 小幅下跌(0-3%):  {small_losers:2d} 个")
    logger.info(f"   🔴 温和下跌(3-10%): {moderate_losers:2d} 个")
    logger.info(f"   💥 大幅下跌(>10%):  {big_losers:2d} 个")
    
    # 统计数据
    avg_change = np.mean([a['price_change_24h'] for a in all_analyses])
    avg_volume = np.mean([a['volume_usdt'] for a in all_analyses])
    total_volume = sum([a['volume_usdt'] for a in all_analyses])
    
    logger.info(f"\n💰 市场数据:")
    logger.info(f"   平均涨跌幅: {avg_change:+.2f}%")
    logger.info(f"   平均成交量: ${avg_volume:,.0f}")
    logger.info(f"   总成交量:   ${total_volume:,.0f}")

def display_investment_advice(all_analyses, category_results):
    """显示投资建议"""
    logger.info(f"\n" + "=" * 100)
    logger.info("💡 投资建议与策略")
    logger.info("=" * 100)
    
    # 强势推荐
    strong_buy = [a for a in all_analyses if a['score'] >= 25]
    if strong_buy:
        logger.info("🔥 强势推荐 (得分≥25):")
        for coin in sorted(strong_buy, key=lambda x: x['score'], reverse=True)[:5]:
            logger.info(f"   • {coin['symbol'].replace('USDT', '')}: 得分{coin['score']}, "
                  f"24h{coin['price_change_24h']:+.2f}%, ${coin['current_price']:.4f}")
    
    # 潜力币种
    potential = [a for a in all_analyses if 15 <= a['score'] < 25]
    if potential:
        logger.info(f"\n🟢 潜力币种 (得分15-24):")
        for coin in sorted(potential, key=lambda x: x['score'], reverse=True)[:5]:
            logger.info(f"   • {coin['symbol'].replace('USDT', '')}: 得分{coin['score']}, "
                  f"24h{coin['price_change_24h']:+.2f}%, ${coin['current_price']:.4f}")
    
    # 超跌反弹
    oversold = [a for a in all_analyses if a['price_change_24h'] < -10 and a['price_position'] < 0.3]
    if oversold:
        logger.info(f"\n🔵 超跌反弹候选:")
        for coin in sorted(oversold, key=lambda x: x['price_change_24h'])[:3]:
            logger.info(f"   • {coin['symbol'].replace('USDT', '')}: "
                  f"24h{coin['price_change_24h']:+.2f}%, 价格位置{coin['price_position']*100:.0f}%")
    
    # 最佳类别
    logger.info(f"\n📂 推荐投资类别:")
    best_categories = []
    for category, analyses in category_results.items():
        if analyses:
            avg_score = np.mean([a['score'] for a in analyses])
            best_categories.append((category, avg_score))
    
    best_categories.sort(key=lambda x: x[1], reverse=True)
    
    for i, (category, score) in enumerate(best_categories[:5], 1):
        status = "🔥推荐" if score > 15 else "🟢关注" if score > 8 else "🟡观望"
        logger.info(f"   {i}. {category}: 平均得分{score:.1f} - {status}")
    
    logger.info(f"\n⚠️  投资提醒:")
    logger.info("• 当前分析基于24小时数据，适用于短期交易参考")
    logger.info("• 高得分币种适合短期关注，低位币种适合中长期布局")  
    logger.info("• 建议组合投资: 30%主流币 + 40%优质山寨币 + 20%潜力新币 + 10%现金")
    logger.info("• 严格风险控制: 单币种仓位≤15%, 设置止损≤10%")
    logger.info("• 定期调仓: 每周评估一次，及时止盈止损")
    
    logger.info(f"\n📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("🔄 建议每2-4小时更新一次以获取最新市场动态")

if __name__ == '__main__':
    optimized_multi_coin_analysis()