import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
多维度市场信息综合分析工具
整合多个时间框架、深度数据和市场指标进行全面分析
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.market.data.adapters.binance_adapter import BinanceAdapter
from src.market.data.adapters.base import DataSourceConfig

class ComprehensiveMarketAnalyzer:
    """综合市场分析器"""
    
    def __init__(self):
        self.focus_pairs = [
            'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'XRPUSDT', 'ADAUSDT',
            'SOLUSDT', 'DOGEUSDT', 'LTCUSDT', 'LINKUSDT', 'UNIUSDT',
            'AVAXUSDT', 'ATOMUSDT', 'INJUSDT', 'APTUSDT', 'SUIUSDT'
        ]
        
        self.timeframes = {
            '1h': {'days': 2, 'periods': 48},
            '4h': {'days': 7, 'periods': 42}, 
            '1d': {'days': 60, 'periods': 60},
            '1w': {'days': 365, 'periods': 52}
        }
    
    def run_comprehensive_analysis(self):
        """运行综合市场分析"""
        logger.info("=" * 140)
        logger.info("🔬 多维度市场信息综合分析")
        logger.info("=" * 140)
        
        try:
            # 初始化适配器
            logger.info("🔌 正在连接币安API...")
            adapter = self.initialize_adapter()
            
            # 健康检查
            is_healthy, message = adapter.health_check()
            if not is_healthy:
                logger.info(f"❌ API连接失败: {message}")
                return
            
            logger.info("✅ API连接成功")
            
            # 1. 获取全市场概况
            logger.info("\n📊 正在获取全市场数据...")
            market_overview = self.get_market_overview(adapter)
            
            # 2. 获取深度数据
            logger.info("📖 正在获取订单簿深度数据...")
            depth_analysis = self.analyze_market_depth(adapter)
            
            # 3. 获取多时间框架数据
            logger.info("⏰ 正在获取多时间框架技术数据...")
            timeframe_analysis = self.analyze_multiple_timeframes(adapter)
            
            # 4. 分析资金流向
            logger.info("💰 正在分析资金流向...")
            flow_analysis = self.analyze_capital_flow(adapter)
            
            # 5. 综合展示分析结果
            self.display_comprehensive_results(
                market_overview, 
                depth_analysis, 
                timeframe_analysis, 
                flow_analysis
            )
            
        except Exception as e:
            logger.info(f"❌ 综合分析出现错误: {e}")
            import traceback
            traceback.print_exc()
    
    def initialize_adapter(self):
        """初始化币安适配器"""
        config = DataSourceConfig(
            name='comprehensive_market_analysis',
            adapter_class='src.data.adapters.binance_adapter.BinanceAdapter',
            enabled=True,
            priority=1,
            rate_limit={
                'requests_per_minute': 20,
                'requests_per_second': 2
            },
            credentials={
                'api_key': '',
                'api_secret': ''
            },
            default_params={}
        )
        return BinanceAdapter(config)
    
    def get_market_overview(self, adapter):
        """获取市场概况"""
        try:
            # 获取24小时行情数据
            all_tickers = adapter.get_24hr_ticker()
            if not all_tickers:
                return None
            
            # 筛选USDT交易对
            usdt_pairs = {k: v for k, v in all_tickers.items() if k.endswith('USDT')}
            
            # 统计分析
            total_pairs = len(usdt_pairs)
            rising_pairs = len([v for v in usdt_pairs.values() if v['price_change_percent'] > 0])
            falling_pairs = total_pairs - rising_pairs
            
            # 成交量统计
            volumes = [v['quote_volume'] for v in usdt_pairs.values()]
            total_volume = sum(volumes)
            
            # 涨跌幅统计
            changes = [v['price_change_percent'] for v in usdt_pairs.values()]
            
            # 分类统计
            big_gainers = len([c for c in changes if c > 10])
            moderate_gainers = len([c for c in changes if 5 < c <= 10])
            small_gainers = len([c for c in changes if 0 < c <= 5])
            small_losers = len([c for c in changes if -5 <= c < 0])
            moderate_losers = len([c for c in changes if -10 <= c < -5])
            big_losers = len([c for c in changes if c < -10])
            
            return {
                'total_pairs': total_pairs,
                'rising_pairs': rising_pairs,
                'falling_pairs': falling_pairs,
                'total_volume': total_volume,
                'avg_change': np.mean(changes),
                'median_change': np.median(changes),
                'volatility': np.std(changes),
                'big_gainers': big_gainers,
                'moderate_gainers': moderate_gainers,
                'small_gainers': small_gainers,
                'small_losers': small_losers,
                'moderate_losers': moderate_losers,
                'big_losers': big_losers,
                'top_gainers': sorted(usdt_pairs.items(), key=lambda x: x[1]['price_change_percent'], reverse=True)[:10],
                'top_losers': sorted(usdt_pairs.items(), key=lambda x: x[1]['price_change_percent'])[:10],
                'top_volume': sorted(usdt_pairs.items(), key=lambda x: x[1]['quote_volume'], reverse=True)[:10]
            }
            
        except Exception as e:
            logger.info(f"   ⚠️ 获取市场概况失败: {e}")
            return None
    
    def analyze_market_depth(self, adapter):
        """分析市场深度"""
        depth_data = {}
        
        for symbol in self.focus_pairs:
            try:
                logger.info(f"   📖 获取 {symbol.replace('USDT', '')} 深度数据...", end='')
                
                # 获取订单簿
                order_book = adapter.get_order_book(symbol, limit=100)
                if not order_book:
                    logger.info(" ❌")
                    continue
                
                bids = order_book['bids']
                asks = order_book['asks']
                
                if not bids or not asks:
                    logger.info(" ❌")
                    continue
                
                # 计算买卖压力
                bid_total = sum([float(price) * float(qty) for price, qty in bids])
                ask_total = sum([float(price) * float(qty) for price, qty in asks])
                
                # 买卖比
                buy_sell_ratio = bid_total / ask_total if ask_total > 0 else 0
                
                # 价格中心
                best_bid = float(bids[0][0])
                best_ask = float(asks[0][0])
                spread = (best_ask - best_bid) / best_bid * 100
                
                # 深度支撑阻力
                bid_support = sum([float(qty) for price, qty in bids[:20]])
                ask_resistance = sum([float(qty) for price, qty in asks[:20]])
                
                depth_data[symbol] = {
                    'buy_sell_ratio': buy_sell_ratio,
                    'spread_percent': spread,
                    'bid_support': bid_support,
                    'ask_resistance': ask_resistance,
                    'best_bid': best_bid,
                    'best_ask': best_ask,
                    'depth_imbalance': (bid_support - ask_resistance) / (bid_support + ask_resistance) if (bid_support + ask_resistance) > 0 else 0
                }
                
                logger.info(" ✅")
                
            except Exception as e:
                logger.info(f" ❌ {str(e)[:30]}...")
                continue
        
        return depth_data
    
    def analyze_multiple_timeframes(self, adapter):
        """分析多时间框架"""
        timeframe_data = {}
        
        for symbol in self.focus_pairs[:5]:  # 只分析前5个币种以节省时间
            try:
                logger.info(f"   ⏰ 分析 {symbol.replace('USDT', '')} 多时间框架...", end='')
                
                symbol_data = {}
                
                # 获取不同时间框架数据
                for timeframe, config in self.timeframes.items():
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=config['days'])
                    
                    try:
                        data = adapter.fetch_historical_data(symbol, start_date, end_date, timeframe)
                        if data and len(data) >= 10:
                            df = pd.DataFrame([{
                                'timestamp': d.timestamp,
                                'close': d.close,
                                'volume': d.volume,
                                'high': d.high,
                                'low': d.low
                            } for d in data])
                            
                            df.set_index('timestamp', inplace=True)
                            df.sort_index(inplace=True)
                            
                            # 计算技术指标
                            df['sma_20'] = df['close'].rolling(20).mean()
                            df['rsi'] = self.calculate_rsi(df['close'])
                            
                            # 趋势判断
                            recent_close = df['close'].iloc[-1]
                            sma_20 = df['sma_20'].iloc[-1]
                            rsi = df['rsi'].iloc[-1]
                            
                            # 价格变化
                            if len(df) >= 5:
                                price_change = (recent_close - df['close'].iloc[-5]) / df['close'].iloc[-5] * 100
                            else:
                                price_change = 0
                            
                            trend = "上涨" if recent_close > sma_20 else "下跌"
                            
                            symbol_data[timeframe] = {
                                'trend': trend,
                                'price_change': price_change,
                                'rsi': rsi,
                                'close': recent_close,
                                'volume_avg': df['volume'].mean(),
                                'volatility': df['close'].pct_change().std() * 100
                            }
                    
                    except Exception as e:
                        symbol_data[timeframe] = None
                        continue
                
                timeframe_data[symbol] = symbol_data
                logger.info(" ✅")
                
            except Exception as e:
                logger.info(f" ❌ {str(e)[:30]}...")
                continue
        
        return timeframe_data
    
    def analyze_capital_flow(self, adapter):
        """分析资金流向"""
        flow_data = {}
        
        # 获取24小时行情数据用于分析
        all_tickers = adapter.get_24hr_ticker()
        if not all_tickers:
            return flow_data
        
        # 分析重点币种的资金流向
        for symbol in self.focus_pairs:
            if symbol in all_tickers:
                ticker = all_tickers[symbol]
                
                # 资金流向指标
                volume = ticker['quote_volume']
                price_change = ticker['price_change_percent']
                
                # 成交量价格分析
                if price_change > 0 and volume > 50000000:  # 上涨且高成交量
                    flow_signal = "强势流入"
                    flow_strength = 3
                elif price_change > 0:
                    flow_signal = "温和流入"
                    flow_strength = 1
                elif price_change < -5 and volume > 50000000:  # 大跌高量
                    flow_signal = "恐慌流出"
                    flow_strength = -3
                elif price_change < 0:
                    flow_signal = "温和流出"
                    flow_strength = -1
                else:
                    flow_signal = "平衡"
                    flow_strength = 0
                
                flow_data[symbol] = {
                    'flow_signal': flow_signal,
                    'flow_strength': flow_strength,
                    'volume': volume,
                    'price_change': price_change
                }
        
        return flow_data
    
    def calculate_rsi(self, prices, window=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.fillna(50)
    
    def display_comprehensive_results(self, market_overview, depth_analysis, timeframe_analysis, flow_analysis):
        """显示综合分析结果"""
        
        # 1. 市场概况
        if market_overview:
            logger.info(f"\n" + "=" * 140)
            logger.info("🌍 全市场概况分析")
            logger.info("=" * 140)
            
            total = market_overview['total_pairs']
            rising = market_overview['rising_pairs']
            falling = market_overview['falling_pairs']
            
            logger.info(f"📊 市场统计:")
            logger.info(f"   交易对总数: {total:,} 个")
            logger.info(f"   上涨品种: {rising:,} 个 ({rising/total*100:.1f}%)")
            logger.info(f"   下跌品种: {falling:,} 个 ({falling/total*100:.1f}%)")
            logger.info(f"   总成交量: ${market_overview['total_volume']:,.0f}")
            logger.info(f"   平均涨跌: {market_overview['avg_change']:+.2f}%")
            logger.info(f"   市场波动率: {market_overview['volatility']:.2f}%")
            
            logger.info(f"\n📈 涨跌分布:")
            logger.info(f"   🚀 大幅上涨(>10%): {market_overview['big_gainers']:3d} 个")
            logger.info(f"   🟢 温和上涨(5-10%): {market_overview['moderate_gainers']:3d} 个") 
            logger.info(f"   🟡 小幅上涨(0-5%):  {market_overview['small_gainers']:3d} 个")
            logger.info(f"   🟡 小幅下跌(0-5%):  {market_overview['small_losers']:3d} 个")
            logger.info(f"   🔴 温和下跌(5-10%): {market_overview['moderate_losers']:3d} 个")
            logger.info(f"   💥 大幅下跌(>10%):  {market_overview['big_losers']:3d} 个")
            
            # 市场情绪判断
            if rising > falling * 1.5:
                sentiment = "强烈乐观 🐂"
            elif rising > falling:
                sentiment = "温和乐观 🐂"
            elif falling > rising * 1.5:
                sentiment = "强烈悲观 🐻"
            else:
                sentiment = "震荡整理 ⚖️"
            
            logger.info(f"\n🎭 市场情绪: {sentiment}")
            
            # 顶级表现者
            logger.info(f"\n🏆 今日最佳表现:")
            for i, (symbol, data) in enumerate(market_overview['top_gainers'][:5], 1):
                name = symbol.replace('USDT', '')
                logger.info(f"   {i}. {name:<8}: {data['price_change_percent']:+6.2f}% | ${data['quote_volume']:>12,.0f}")
            
            logger.info(f"\n💔 今日最差表现:")
            for i, (symbol, data) in enumerate(market_overview['top_losers'][:5], 1):
                name = symbol.replace('USDT', '')
                logger.info(f"   {i}. {name:<8}: {data['price_change_percent']:+6.2f}% | ${data['quote_volume']:>12,.0f}")
        
        # 2. 深度分析
        if depth_analysis:
            logger.info(f"\n" + "=" * 140)
            logger.info("📖 市场深度分析")
            logger.info("=" * 140)
            
            logger.info(f"{'币种':<8} {'买卖比':<8} {'价差%':<8} {'深度倾向':<12} {'流动性':<8} {'信号'}")
            logger.info("-" * 70)
            
            for symbol, data in depth_analysis.items():
                name = symbol.replace('USDT', '')
                buy_sell_ratio = data['buy_sell_ratio']
                spread = data['spread_percent']
                imbalance = data['depth_imbalance']
                
                # 流动性判断
                if spread < 0.01:
                    liquidity = "极高"
                elif spread < 0.05:
                    liquidity = "高"
                elif spread < 0.1:
                    liquidity = "中等"
                else:
                    liquidity = "低"
                
                # 深度倾向
                if imbalance > 0.2:
                    depth_bias = "强势买盘"
                elif imbalance > 0.05:
                    depth_bias = "偏向买盘"
                elif imbalance < -0.2:
                    depth_bias = "强势卖盘"
                elif imbalance < -0.05:
                    depth_bias = "偏向卖盘"
                else:
                    depth_bias = "相对平衡"
                
                # 交易信号
                if buy_sell_ratio > 1.2 and imbalance > 0.1:
                    signal = "🟢 做多"
                elif buy_sell_ratio < 0.8 and imbalance < -0.1:
                    signal = "🔴 做空"
                else:
                    signal = "⚪ 观望"
                
                logger.info(f"{name:<8} {buy_sell_ratio:<8.2f} {spread:<8.3f} {depth_bias:<12} {liquidity:<8} {signal}")
        
        # 3. 多时间框架分析
        if timeframe_analysis:
            logger.info(f"\n" + "=" * 140)
            logger.info("⏰ 多时间框架趋势分析")
            logger.info("=" * 140)
            
            for symbol, data in timeframe_analysis.items():
                name = symbol.replace('USDT', '')
                logger.info(f"\n📊 {name} 趋势分析:")
                
                trend_agreement = 0
                total_trends = 0
                
                for timeframe in ['1h', '4h', '1d', '1w']:
                    if timeframe in data and data[timeframe]:
                        tf_data = data[timeframe]
                        trend = tf_data['trend']
                        change = tf_data['price_change']
                        rsi = tf_data.get('rsi', 50)
                        
                        trend_emoji = "📈" if trend == "上涨" else "📉"
                        rsi_status = "超买" if rsi > 70 else "超卖" if rsi < 30 else "中性"
                        
                        logger.info(f"   {timeframe:>3}: {trend_emoji} {trend:<4} | 变化:{change:+6.2f}% | RSI:{rsi:5.1f}({rsi_status})")
                        
                        if trend == "上涨":
                            trend_agreement += 1
                        total_trends += 1
                
                # 趋势一致性判断
                if total_trends > 0:
                    agreement_rate = trend_agreement / total_trends
                    if agreement_rate >= 0.75:
                        trend_signal = "🟢 强势一致(做多)"
                    elif agreement_rate <= 0.25:
                        trend_signal = "🔴 弱势一致(做空)"
                    else:
                        trend_signal = "🟡 趋势分歧(观望)"
                    
                    logger.info(f"   📊 趋势一致性: {agreement_rate*100:.0f}% - {trend_signal}")
        
        # 4. 资金流向分析
        if flow_analysis:
            logger.info(f"\n" + "=" * 140)
            logger.info("💰 资金流向分析")
            logger.info("=" * 140)
            
            # 按资金流强度排序
            sorted_flows = sorted(flow_analysis.items(), key=lambda x: x[1]['flow_strength'], reverse=True)
            
            logger.info(f"{'币种':<8} {'资金流向':<12} {'强度':<4} {'成交量(USDT)':<15} {'24h变化':<10}")
            logger.info("-" * 65)
            
            for symbol, data in sorted_flows:
                name = symbol.replace('USDT', '')
                flow_signal = data['flow_signal']
                strength = data['flow_strength']
                volume = data['volume']
                change = data['price_change']
                
                strength_emoji = "🔥" if abs(strength) >= 3 else "🟢" if strength > 0 else "🔴" if strength < 0 else "⚪"
                
                logger.info(f"{name:<8} {flow_signal:<12} {strength_emoji}{strength:>2d}   ${volume:>12,.0f}   {change:+6.2f}%")
            
            # 整体资金流向判断
            total_inflow = len([d for d in flow_analysis.values() if d['flow_strength'] > 0])
            total_outflow = len([d for d in flow_analysis.values() if d['flow_strength'] < 0])
            
            if total_inflow > total_outflow:
                overall_flow = "整体资金净流入 💰"
            elif total_outflow > total_inflow:
                overall_flow = "整体资金净流出 💸"
            else:
                overall_flow = "资金流向平衡 ⚖️"
            
            logger.info(f"\n📈 {overall_flow}")
        
        # 5. 综合投资建议
        self.generate_comprehensive_recommendations(market_overview, depth_analysis, timeframe_analysis, flow_analysis)
    
    def generate_comprehensive_recommendations(self, market_overview, depth_analysis, timeframe_analysis, flow_analysis):
        """生成综合投资建议"""
        logger.info(f"\n" + "=" * 140)
        logger.info("💡 综合投资建议")
        logger.info("=" * 140)
        
        recommendations = []
        
        # 基于多维度分析生成建议
        for symbol in self.focus_pairs:
            score = 0
            reasons = []
            
            # 市场深度评分
            if symbol in depth_analysis:
                depth = depth_analysis[symbol]
                if depth['buy_sell_ratio'] > 1.2:
                    score += 15
                    reasons.append("买盘深度优势")
                elif depth['buy_sell_ratio'] < 0.8:
                    score -= 10
                    reasons.append("卖盘压力较大")
                
                if depth['spread_percent'] < 0.05:
                    score += 10
                    reasons.append("流动性良好")
            
            # 多时间框架评分
            if symbol in timeframe_analysis:
                tf_data = timeframe_analysis[symbol]
                uptrend_count = 0
                total_tf = 0
                
                for tf, data in tf_data.items():
                    if data and data['trend'] == "上涨":
                        uptrend_count += 1
                    if data:
                        total_tf += 1
                
                if total_tf > 0:
                    trend_ratio = uptrend_count / total_tf
                    if trend_ratio >= 0.75:
                        score += 20
                        reasons.append("多周期趋势一致向上")
                    elif trend_ratio <= 0.25:
                        score -= 15
                        reasons.append("多周期趋势向下")
            
            # 资金流向评分
            if symbol in flow_analysis:
                flow = flow_analysis[symbol]
                score += flow['flow_strength'] * 5
                if flow['flow_strength'] > 0:
                    reasons.append(f"资金{flow['flow_signal']}")
                elif flow['flow_strength'] < 0:
                    reasons.append(f"资金{flow['flow_signal']}")
            
            if score != 0:  # 只包含有评分的币种
                recommendations.append({
                    'symbol': symbol,
                    'score': score,
                    'reasons': reasons
                })
        
        # 按得分排序
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        
        logger.info("🏆 多维度综合评分排名:")
        logger.info(f"{'排名':<4} {'币种':<8} {'得分':<6} {'评级':<8} {'主要原因'}")
        logger.info("-" * 80)
        
        for i, rec in enumerate(recommendations, 1):
            name = rec['symbol'].replace('USDT', '')
            score = rec['score']
            
            if score >= 30:
                rating = "🔥强烈推荐"
            elif score >= 20:
                rating = "🟢推荐买入"
            elif score >= 10:
                rating = "🟡谨慎关注"
            elif score >= 0:
                rating = "⚪中性观望"
            else:
                rating = "🔴建议回避"
            
            reasons_str = " | ".join(rec['reasons'][:3])  # 只显示前3个原因
            
            logger.info(f"{i:2d}   {name:<8} {score:+4d}   {rating:<8} {reasons_str}")
        
        # 具体操作建议
        logger.info(f"\n📋 具体操作建议:")
        
        top_picks = [r for r in recommendations if r['score'] >= 20]
        if top_picks:
            logger.info("🟢 重点买入标的:")
            for pick in top_picks[:3]:
                name = pick['symbol'].replace('USDT', '')
                logger.info(f"   • {name}: 综合得分{pick['score']:+d}, 建议现货配置20-30%")
        
        moderate_picks = [r for r in recommendations if 10 <= r['score'] < 20]
        if moderate_picks:
            logger.info(f"\n🟡 适量关注标的:")
            for pick in moderate_picks[:3]:
                name = pick['symbol'].replace('USDT', '')
                logger.info(f"   • {name}: 综合得分{pick['score']:+d}, 建议小仓位试探10-15%")
        
        avoid_picks = [r for r in recommendations if r['score'] < 0]
        if avoid_picks:
            logger.info(f"\n🔴 建议回避标的:")
            for pick in sorted(avoid_picks, key=lambda x: x['score'])[:3]:
                name = pick['symbol'].replace('USDT', '')
                logger.info(f"   • {name}: 综合得分{pick['score']:+d}, 多重负面因素")
        
        # 市场环境判断
        logger.info(f"\n🌍 当前市场环境判断:")
        if market_overview:
            rising_ratio = market_overview['rising_pairs'] / market_overview['total_pairs']
            avg_change = market_overview['avg_change']
            
            if rising_ratio > 0.6 and avg_change > 2:
                env_assessment = "牛市行情，适合积极买入"
                strategy = "主动出击，重仓优质标的"
            elif rising_ratio > 0.4 and avg_change > 0:
                env_assessment = "震荡向上，适合分批建仓"
                strategy = "稳健投资，逢低买入"
            elif rising_ratio < 0.4 and avg_change < -2:
                env_assessment = "熊市调整，适合防御策略"
                strategy = "控制仓位，等待机会"
            else:
                env_assessment = "震荡整理，适合高抛低吸"
                strategy = "短线操作，严格止损"
            
            logger.info(f"   📊 {env_assessment}")
            logger.info(f"   💡 策略建议: {strategy}")
        
        logger.info(f"\n⚠️  风险提醒:")
        logger.info("• 多维度分析提供更全面视角，但市场变化快速")
        logger.info("• 建议结合实时新闻和基本面变化调整策略")
        logger.info("• 严格执行资金管理，分散投资降低风险")
        logger.info("• 定期回顾和调整投资组合配置")
        
        logger.info(f"\n📅 分析完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("🔄 建议每1-2小时更新一次多维度分析")

def main():
    """主函数"""
    analyzer = ComprehensiveMarketAnalyzer()
    analyzer.run_comprehensive_analysis()

if __name__ == '__main__':
    main()