#!/bin/bash

# 量化交易系统统一启动脚本
# 版本: 4.0.0 - 简化统一版
# 描述: 一键启动整个量化交易系统，自动处理所有复杂性

set -euo pipefail

# 获取脚本目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  ${NC}$1"
}

log_success() {
    echo -e "${GREEN}✅ ${NC}$1"
}

log_warning() {
    echo -e "${YELLOW}⚠️  ${NC}$1"
}

log_error() {
    echo -e "${RED}❌ ${NC}$1"
}

log_step() {
    echo -e "${CYAN}🔄 ${NC}$1"
}

log_header() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# 显示启动信息
show_startup_info() {
    clear
    echo -e "${BOLD}${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                   量化交易系统 v4.0                           ║"
    echo "║                  统一启动管理器                               ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
    log_info "正在启动量化交易系统..."
    echo
}

# 检查系统要求
check_requirements() {
    log_header "检查系统环境"
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装 Python 3.8+"
        exit 1
    fi
    
    local python_version=$(python3 --version | cut -d' ' -f2)
    log_success "Python版本: $python_version"
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_warning "Node.js 未安装，将尝试使用系统包管理器安装..."
        if command -v brew &> /dev/null; then
            brew install node
        elif command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y nodejs npm
        else
            log_error "请手动安装 Node.js 16+"
            exit 1
        fi
    fi
    
    local node_version=$(node --version)
    log_success "Node.js版本: $node_version"
    
    # 检查必要目录
    if [ ! -d "src" ] || [ ! -d "web_ui" ]; then
        log_error "项目结构不完整，请确保在项目根目录运行"
        exit 1
    fi
    
    log_success "系统环境检查完成"
    echo
}

# 创建必要目录
create_directories() {
    log_step "创建必要目录..."
    
    mkdir -p logs/{startup,backend,frontend,errors}
    mkdir -p data/{cache,db,exports,temp}
    mkdir -p web_ui/backend/logs
    mkdir -p web_ui/frontend/dist
    
    log_success "目录结构创建完成"
}

# 设置虚拟环境
setup_virtual_environment() {
    log_step "设置Python虚拟环境..."
    
    local venv_path="web_ui/backend/venv"
    
    if [ ! -d "$venv_path" ]; then
        log_info "创建Python虚拟环境..."
        cd web_ui/backend
        python3 -m venv venv
        cd ../..
        log_success "虚拟环境创建完成"
    else
        log_info "虚拟环境已存在，跳过创建"
    fi
    
    # 验证虚拟环境
    if [ -f "$venv_path/bin/activate" ] || [ -f "$venv_path/Scripts/activate" ]; then
        log_success "虚拟环境验证通过"
    else
        log_error "虚拟环境创建失败"
        exit 1
    fi
}

# 安装后端依赖
install_backend_dependencies() {
    log_step "安装后端依赖..."
    
    cd web_ui/backend
    
    # 激活虚拟环境
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
    elif [ -f "venv/Scripts/activate" ]; then
        source venv/Scripts/activate
    fi
    
    # 安装依赖
    if [ -f "requirements.txt" ]; then
        log_info "正在安装Python依赖包..."
        pip install --upgrade pip
        pip install -r requirements.txt
        log_success "后端依赖安装完成"
    else
        log_warning "requirements.txt 不存在，跳过依赖安装"
    fi
    
    cd ../..
}

# 安装前端依赖
install_frontend_dependencies() {
    log_step "安装前端依赖..."
    
    cd web_ui/frontend
    
    if [ -f "package.json" ]; then
        log_info "正在安装Node.js依赖包..."
        
        # 检查是否有package-lock.json，优先使用npm ci
        if [ -f "package-lock.json" ]; then
            npm ci
        else
            npm install
        fi
        
        log_success "前端依赖安装完成"
    else
        log_warning "package.json 不存在，跳过依赖安装"
    fi
    
    cd ../..
}

# 生成配置文件
generate_configurations() {
    log_step "生成配置文件..."
    
    # 生成后端配置
    local backend_env="web_ui/backend/.env"
    if [ ! -f "$backend_env" ]; then
        log_info "生成后端环境配置..."
        cat > "$backend_env" << EOF
# 后端服务配置
HOST=127.0.0.1
PORT=8000
DEBUG=true
RELOAD=true

# 数据库配置
DATABASE_URL=sqlite:///../../data/db/trading_system.db

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/backend.log

# CORS配置
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# API配置
API_PREFIX=/api/v1
API_TITLE=量化交易系统API
API_VERSION=2.0.0
EOF
        log_success "后端配置文件生成完成"
    fi
    
    # 生成前端配置
    local frontend_env="web_ui/frontend/.env"
    if [ ! -f "$frontend_env" ]; then
        log_info "生成前端环境配置..."
        cat > "$frontend_env" << EOF
# 前端配置
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000
VITE_APP_TITLE=量化交易系统
VITE_APP_VERSION=2.0.0
NODE_ENV=development
EOF
        log_success "前端配置文件生成完成"
    fi
}

# 启动后端服务
start_backend_service() {
    log_step "启动后端服务..."
    
    cd web_ui/backend
    
    # 激活虚拟环境
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
    elif [ -f "venv/Scripts/activate" ]; then
        source venv/Scripts/activate
    fi
    
    # 检查端口占用
    if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口8000已被占用，正在清理..."
        pkill -f "uvicorn.*main:app" || true
        sleep 2
    fi
    
    # 启动服务
    log_info "正在启动FastAPI服务器..."
    nohup python -m uvicorn main:app --host 127.0.0.1 --port 8000 --reload > logs/backend.log 2>&1 &
    echo $! > backend.pid
    
    cd ../..
    
    # 等待服务启动
    log_info "等待后端服务启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8000/health > /dev/null 2>&1; then
            log_success "后端服务启动成功 (端口: 8000)"
            return 0
        fi
        sleep 1
    done
    
    log_error "后端服务启动失败"
    return 1
}

# 启动前端服务
start_frontend_service() {
    log_step "启动前端服务..."
    
    cd web_ui/frontend
    
    # 检查端口占用
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口3000已被占用，正在清理..."
        pkill -f "vite.*--port 3000" || true
        sleep 2
    fi
    
    # 启动开发服务器
    log_info "正在启动前端开发服务器..."
    nohup npm run dev > ../backend/logs/frontend.log 2>&1 &
    echo $! > frontend.pid
    
    cd ../..
    
    # 等待服务启动
    log_info "等待前端服务启动..."
    for i in {1..30}; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            log_success "前端服务启动成功 (端口: 3000)"
            return 0
        fi
        sleep 1
    done
    
    log_warning "前端服务可能需要更长时间启动，请稍后检查"
    return 0
}

# 执行健康检查
perform_health_checks() {
    log_header "系统健康检查"
    
    # 检查后端API
    log_info "检查后端API健康状态..."
    if curl -s http://localhost:8000/health | grep -q "ok\|healthy\|status.*ok" 2>/dev/null; then
        log_success "后端API健康检查通过"
    else
        log_warning "后端API健康检查失败，但服务可能仍在启动中"
    fi
    
    # 检查前端
    log_info "检查前端服务状态..."
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        log_success "前端服务健康检查通过"
    else
        log_warning "前端服务可能仍在启动中"
    fi
    
    # 检查数据库连接
    log_info "检查数据库连接..."
    if [ -f "data/db/trading_system.db" ] || mkdir -p data/db; then
        log_success "数据库连接检查通过"
    fi
    
    echo
}

# 显示启动完成信息
show_completion_info() {
    echo
    log_header "🎉 系统启动完成"
    echo
    echo -e "${BOLD}${GREEN}✅ 量化交易系统已成功启动！${NC}"
    echo
    echo -e "${BOLD}访问地址：${NC}"
    echo -e "  🌐 前端界面: ${CYAN}http://localhost:3000${NC}"
    echo -e "  🔧 后端API:  ${CYAN}http://localhost:8000${NC}"
    echo -e "  📚 API文档:  ${CYAN}http://localhost:8000/docs${NC}"
    echo
    echo -e "${BOLD}管理命令：${NC}"
    echo -e "  停止系统: ${YELLOW}./start_system.sh stop${NC}"
    echo -e "  查看状态: ${YELLOW}./start_system.sh status${NC}"
    echo -e "  查看日志: ${YELLOW}./start_system.sh logs${NC}"
    echo
    echo -e "${BOLD}日志文件：${NC}"
    echo -e "  后端日志: ${BLUE}web_ui/backend/logs/backend.log${NC}"
    echo -e "  前端日志: ${BLUE}web_ui/backend/logs/frontend.log${NC}"
    echo
}

# 停止系统
stop_system() {
    log_header "停止系统服务"
    
    # 停止后端
    if [ -f "web_ui/backend/backend.pid" ]; then
        local backend_pid=$(cat web_ui/backend/backend.pid)
        if kill "$backend_pid" 2>/dev/null; then
            log_success "后端服务已停止"
        fi
        rm -f web_ui/backend/backend.pid
    fi
    
    # 停止前端
    if [ -f "web_ui/frontend/frontend.pid" ]; then
        local frontend_pid=$(cat web_ui/frontend/frontend.pid)
        if kill "$frontend_pid" 2>/dev/null; then
            log_success "前端服务已停止"
        fi
        rm -f web_ui/frontend/frontend.pid
    fi
    
    # 清理端口
    pkill -f "uvicorn.*main:app" || true
    pkill -f "vite.*--port 3000" || true
    
    log_success "系统已完全停止"
}

# 查看系统状态
check_status() {
    log_header "系统状态检查"
    
    # 检查后端状态
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        log_success "后端服务: 正在运行 (http://localhost:8000)"
    else
        log_error "后端服务: 未运行"
    fi
    
    # 检查前端状态
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        log_success "前端服务: 正在运行 (http://localhost:3000)"
    else
        log_error "前端服务: 未运行"
    fi
    
    # 检查进程
    echo
    log_info "相关进程："
    ps aux | grep -E "(uvicorn|vite)" | grep -v grep || log_warning "未找到相关进程"
}

# 查看日志
show_logs() {
    log_header "系统日志"
    
    echo -e "${BOLD}选择要查看的日志：${NC}"
    echo "1) 后端日志"
    echo "2) 前端日志"
    echo "3) 同时查看所有日志"
    read -p "请选择 (1-3): " choice
    
    case $choice in
        1)
            if [ -f "web_ui/backend/logs/backend.log" ]; then
                tail -f web_ui/backend/logs/backend.log
            else
                log_error "后端日志文件不存在"
            fi
            ;;
        2)
            if [ -f "web_ui/backend/logs/frontend.log" ]; then
                tail -f web_ui/backend/logs/frontend.log
            else
                log_error "前端日志文件不存在"
            fi
            ;;
        3)
            if [ -f "web_ui/backend/logs/backend.log" ] && [ -f "web_ui/backend/logs/frontend.log" ]; then
                tail -f web_ui/backend/logs/backend.log web_ui/backend/logs/frontend.log
            else
                log_error "日志文件不存在"
            fi
            ;;
        *)
            log_error "无效选择"
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "量化交易系统启动脚本 v4.0.0"
    echo
    echo "使用方法:"
    echo "  $0 [命令]"
    echo
    echo "命令:"
    echo "  start    启动系统 (默认)"
    echo "  stop     停止系统"
    echo "  restart  重启系统"
    echo "  status   查看状态"
    echo "  logs     查看日志"
    echo "  help     显示帮助"
    echo
    echo "示例:"
    echo "  $0         # 启动系统"
    echo "  $0 start   # 启动系统"
    echo "  $0 stop    # 停止系统"
    echo "  $0 status  # 查看状态"
}

# 主启动流程
main_startup_flow() {
    show_startup_info
    check_requirements
    create_directories
    setup_virtual_environment
    install_backend_dependencies
    install_frontend_dependencies
    generate_configurations
    
    if start_backend_service && start_frontend_service; then
        perform_health_checks
        show_completion_info
        
        # 保持运行状态，等待用户中断
        echo -e "${BOLD}${YELLOW}按 Ctrl+C 停止系统${NC}"
        trap stop_system SIGINT SIGTERM
        
        # 等待信号
        while true; do
            sleep 1
        done
    else
        log_error "系统启动失败"
        exit 1
    fi
}

# 主函数
main() {
    local command=${1:-start}
    
    case $command in
        start)
            main_startup_flow
            ;;
        stop)
            stop_system
            ;;
        restart)
            stop_system
            sleep 2
            main_startup_flow
            ;;
        status)
            check_status
            ;;
        logs)
            show_logs
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 检查是否在正确的目录
if [ ! -f "src/application/main.py" ] || [ ! -d "web_ui" ]; then
    log_error "请在项目根目录运行此脚本"
    exit 1
fi

# 运行主函数
main "$@"