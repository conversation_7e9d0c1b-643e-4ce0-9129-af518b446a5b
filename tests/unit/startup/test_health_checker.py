import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
健康检查模块单元测试
测试健康检查系统的各个功能组件
"""

import unittest
import subprocess
import tempfile
import os
import json
import time
from unittest.mock import patch, MagicMock
import requests


class TestHealthChecker(unittest.TestCase):
    """健康检查模块测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../"))
        self.health_checker_script = os.path.join(
            self.project_root, "scripts/startup/modules/health_checker.sh"
        )
        
        # 确保健康检查脚本存在
        self.assertTrue(
            os.path.exists(self.health_checker_script),
            f"健康检查脚本不存在: {self.health_checker_script}"
        )
    
    def test_health_checker_script_exists(self):
        """测试健康检查脚本文件存在"""
        self.assertTrue(os.path.exists(self.health_checker_script))
        
        # 检查脚本是否可执行
        self.assertTrue(os.access(self.health_checker_script, os.R_OK))
    
    def test_health_checker_functions_defined(self):
        """测试健康检查函数是否正确定义"""
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 检查必需的函数是否定义
        required_functions = [
            'health_check_backend',
            'health_check_frontend', 
            'test_api_connectivity',
            'test_database_connection',
            'test_websocket_connection',
            'validate_service_communication',
            'generate_health_status_report'
        ]
        
        for function_name in required_functions:
            self.assertIn(
                f"{function_name}()",
                script_content,
                f"函数 {function_name} 未在脚本中定义"
            )
    
    def test_health_check_configuration(self):
        """测试健康检查配置"""
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 检查配置项是否存在
        config_items = [
            'backend_url',
            'frontend_url',
            'backend_health_endpoint',
            'websocket_endpoint',
            'database_file',
            'max_attempts',
            'timeout'
        ]
        
        for config_item in config_items:
            self.assertIn(
                config_item,
                script_content,
                f"配置项 {config_item} 未在脚本中定义"
            )
    
    def test_backend_health_check_simulation(self):
        """模拟测试后端健康检查"""
        # 这里我们测试脚本的基本结构，实际的bash函数测试需要在bash环境中进行
        result = subprocess.run(
            ['bash', '-c', f'source {self.health_checker_script} && echo "脚本加载成功"'],
            capture_output=True,
            text=True,
            cwd=self.project_root
        )
        
        self.assertEqual(result.returncode, 0, f"健康检查脚本加载失败: {result.stderr}")
        self.assertIn("脚本加载成功", result.stdout)
    
    def test_health_report_generation_structure(self):
        """测试健康检查报告生成结构"""
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 检查报告生成相关的关键字
        report_keywords = [
            'generate_health_status_report',
            'health_report',
            'JSON报告',
            '文本报告',
            '系统健康状态'
        ]
        
        for keyword in report_keywords:
            self.assertIn(
                keyword,
                script_content,
                f"报告生成关键字 {keyword} 未找到"
            )
    
    def test_connectivity_test_functions(self):
        """测试连通性测试函数"""
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 检查连通性测试相关函数
        connectivity_functions = [
            'test_api_connectivity',
            'test_database_connection', 
            'test_websocket_connection',
            'validate_service_communication'
        ]
        
        for function_name in connectivity_functions:
            self.assertIn(
                f"{function_name}()",
                script_content,
                f"连通性测试函数 {function_name} 未定义"
            )
    
    def test_error_handling_and_diagnostics(self):
        """测试错误处理和诊断功能"""
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 检查错误处理和诊断相关功能
        diagnostic_keywords = [
            'diagnose_backend_issues',
            'diagnose_frontend_issues',
            'provide_communication_troubleshooting',
            '故障排除建议',
            '诊断'
        ]
        
        for keyword in diagnostic_keywords:
            self.assertIn(
                keyword,
                script_content,
                f"诊断功能关键字 {keyword} 未找到"
            )
    
    def test_websocket_test_implementation(self):
        """测试WebSocket测试实现"""
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 检查WebSocket测试相关实现
        websocket_keywords = [
            'test_websocket_connection',
            'websocket_url',
            'WebSocket连接',
            'websockets',
            'Sec-WebSocket-Key'
        ]
        
        for keyword in websocket_keywords:
            self.assertIn(
                keyword,
                script_content,
                f"WebSocket测试关键字 {keyword} 未找到"
            )
    
    def test_database_connection_test(self):
        """测试数据库连接测试"""
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 检查数据库连接测试相关实现
        database_keywords = [
            'test_database_connection',
            'database_file',
            'SQLite',
            '数据库连接',
            'provide_database_troubleshooting'
        ]
        
        for keyword in database_keywords:
            self.assertIn(
                keyword,
                script_content,
                f"数据库测试关键字 {keyword} 未找到"
            )
    
    def test_health_status_display(self):
        """测试健康状态显示功能"""
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        # 检查状态显示相关功能
        display_keywords = [
            'display_health_status',
            '系统健康状态面板',
            '🟢',  # 绿色状态指示器
            '🔴',  # 红色状态指示器
            'PID'
        ]
        
        for keyword in display_keywords:
            self.assertIn(
                keyword,
                script_content,
                f"状态显示关键字 {keyword} 未找到"
            )


class TestHealthCheckIntegration(unittest.TestCase):
    """健康检查集成测试"""
    
    def setUp(self):
        """集成测试前置设置"""
        self.project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../"))
        self.test_log_dir = os.path.join(self.project_root, "logs")
        
        # 确保日志目录存在
        os.makedirs(self.test_log_dir, exist_ok=True)
    
    def test_health_check_script_execution(self):
        """测试健康检查脚本执行"""
        health_checker_script = os.path.join(
            self.project_root, "scripts/startup/modules/health_checker.sh"
        )
        
        # 测试脚本是否可以正常加载
        result = subprocess.run(
            ['bash', '-c', f'source {health_checker_script} && echo "健康检查模块加载成功"'],
            capture_output=True,
            text=True,
            cwd=self.project_root
        )
        
        self.assertEqual(result.returncode, 0, f"健康检查脚本执行失败: {result.stderr}")
        self.assertIn("健康检查模块加载成功", result.stdout)
    
    def test_health_report_file_generation(self):
        """测试健康检查报告文件生成"""
        # 创建临时测试环境
        with tempfile.TemporaryDirectory() as temp_dir:
            test_report_file = os.path.join(temp_dir, "test_health_report.txt")
            
            # 模拟生成健康检查报告
            report_content = """==========================================
量化交易系统健康检查报告
==========================================
检查时间: 2024-01-01 12:00:00
总体状态: success

服务详细状态:
  后端API服务:
    - 端口状态: 运行中 (端口 8000)
    - 进程ID: 12345
    - 健康状态: 健康

  前端开发服务:
    - 端口状态: 运行中 (端口 3000)
    - 进程ID: 12346
    - 可访问性: 可访问

连通性测试结果:
  ✓ 后端健康检查: 可访问 (http://localhost:8000/health)
  ✓ API文档: 可访问 (http://localhost:8000/docs)
  ✓ 前端界面: 可访问 (http://localhost:3000)

建议操作:
  - 系统运行正常，可以开始使用
  - 定期检查日志文件
  - 监控系统资源使用情况
"""
            
            with open(test_report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            # 验证报告文件生成
            self.assertTrue(os.path.exists(test_report_file))
            
            with open(test_report_file, 'r', encoding='utf-8') as f:
                content = f.read()
                self.assertIn("量化交易系统健康检查报告", content)
                self.assertIn("服务详细状态", content)
                self.assertIn("连通性测试结果", content)
    
    def test_json_health_report_structure(self):
        """测试JSON健康检查报告结构"""
        # 模拟JSON报告结构
        expected_json_structure = {
            "timestamp": "2024-01-01T12:00:00+08:00",
            "overall_status": "success",
            "services": {
                "backend": {
                    "port_status": "运行中",
                    "pid": "12345",
                    "health_status": "健康",
                    "port": 8000
                },
                "frontend": {
                    "port_status": "运行中", 
                    "pid": "12346",
                    "accessibility": "可访问",
                    "port": 3000
                }
            },
            "connectivity": {
                "后端健康检查": {
                    "url": "http://localhost:8000/health",
                    "accessible": True
                },
                "API文档": {
                    "url": "http://localhost:8000/docs", 
                    "accessible": True
                },
                "前端界面": {
                    "url": "http://localhost:3000",
                    "accessible": True
                }
            }
        }
        
        # 验证JSON结构的完整性
        self.assertIn("timestamp", expected_json_structure)
        self.assertIn("overall_status", expected_json_structure)
        self.assertIn("services", expected_json_structure)
        self.assertIn("connectivity", expected_json_structure)
        
        # 验证服务信息结构
        services = expected_json_structure["services"]
        self.assertIn("backend", services)
        self.assertIn("frontend", services)
        
        # 验证后端服务信息
        backend = services["backend"]
        required_backend_fields = ["port_status", "pid", "health_status", "port"]
        for field in required_backend_fields:
            self.assertIn(field, backend)
        
        # 验证前端服务信息
        frontend = services["frontend"]
        required_frontend_fields = ["port_status", "pid", "accessibility", "port"]
        for field in required_frontend_fields:
            self.assertIn(field, frontend)


def run_health_checker_tests():
    """运行健康检查测试套件"""
    logger.info("🧪 开始运行健康检查模块测试...")
    
    # 创建测试套件
    loader = unittest.TestLoader()
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTests(loader.loadTestsFromTestCase(TestHealthChecker))
    test_suite.addTests(loader.loadTestsFromTestCase(TestHealthCheckIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        logger.info("✅ 所有健康检查测试通过！")
        return True
    else:
        logger.info("❌ 部分健康检查测试失败")
        logger.info(f"失败数量: {len(result.failures)}")
        logger.info(f"错误数量: {len(result.errors)}")
        return False


if __name__ == "__main__":
    success = run_health_checker_tests()
    exit(0 if success else 1)