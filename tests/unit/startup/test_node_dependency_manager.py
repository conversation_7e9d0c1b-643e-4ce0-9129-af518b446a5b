import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Node.js依赖管理模块单元测试
专门测试Node.js依赖管理功能
"""

import os
import sys
import unittest
import tempfile
import shutil
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


class TestNodeDependencyManager(unittest.TestCase):
    """Node.js依赖管理器测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.test_dir = tempfile.mkdtemp()
        self.frontend_dir = os.path.join(self.test_dir, "web_ui", "frontend")
        self.cache_dir = os.path.join(self.test_dir, "data", "cache", "dependencies", "node")
        
        # 创建测试目录结构
        os.makedirs(self.frontend_dir, exist_ok=True)
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # 创建测试package.json
        self.package_json_data = {
            "name": "quantitative-trading-frontend",
            "version": "2.0.0",
            "description": "量化交易系统现代Web前端",
            "dependencies": {
                "react": "^18.3.1",
                "antd": "^5.26.6",
                "axios": "^1.6.0",
                "echarts": "^6.0.0",
                "dayjs": "^1.11.10"
            },
            "devDependencies": {
                "@types/react": "^18.3.23",
                "typescript": "^5.8.3",
                "vite": "^4.5.14",
                "@vitejs/plugin-react": "^4.7.0",
                "eslint": "^8.53.0"
            },
            "scripts": {
                "dev": "vite",
                "build": "tsc && vite build",
                "preview": "vite preview",
                "lint": "eslint . --ext ts,tsx",
                "test": "jest"
            }
        }
        
        self.package_json_file = os.path.join(self.frontend_dir, "package.json")
        with open(self.package_json_file, 'w', encoding='utf-8') as f:
            json.dump(self.package_json_data, f, indent=2, ensure_ascii=False)
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_install_node_dependencies_package_json_exists(self):
        """测试package.json存在时的Node.js依赖安装"""
        logger.info("测试package.json存在时的Node.js依赖安装...")
        
        # 验证package.json存在
        self.assertTrue(os.path.exists(self.package_json_file))
        
        # 验证package.json内容
        with open(self.package_json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            self.assertEqual(data['name'], 'quantitative-trading-frontend')
            self.assertIn('react', data['dependencies'])
            self.assertIn('antd', data['dependencies'])
            self.assertIn('typescript', data['devDependencies'])
        
        logger.info("✅ package.json存在时的依赖安装测试通过")
    
    def test_install_node_dependencies_package_json_missing(self):
        """测试package.json不存在时的处理"""
        logger.info("测试package.json不存在时的处理...")
        
        # 删除package.json文件
        os.remove(self.package_json_file)
        
        # 验证文件不存在
        self.assertFalse(os.path.exists(self.package_json_file))
        
        # 模拟跳过安装的逻辑
        should_skip_install = not os.path.exists(self.package_json_file)
        self.assertTrue(should_skip_install)
        
        logger.info("✅ package.json不存在时的处理测试通过")
    
    def test_check_node_modules_not_exists(self):
        """测试node_modules不存在时的检查"""
        logger.info("测试node_modules不存在时的检查...")
        
        node_modules_dir = os.path.join(self.frontend_dir, "node_modules")
        
        # 验证node_modules不存在
        self.assertFalse(os.path.exists(node_modules_dir))
        
        # 模拟需要安装依赖的逻辑
        needs_install = not os.path.exists(node_modules_dir)
        self.assertTrue(needs_install)
        
        logger.info("✅ node_modules不存在时的检查测试通过")
    
    def test_check_node_modules_exists_but_incomplete(self):
        """测试node_modules存在但不完整的情况"""
        logger.info("测试node_modules存在但不完整的情况...")
        
        node_modules_dir = os.path.join(self.frontend_dir, "node_modules")
        os.makedirs(node_modules_dir, exist_ok=True)
        
        # 只创建部分依赖包目录
        react_dir = os.path.join(node_modules_dir, "react")
        os.makedirs(react_dir, exist_ok=True)
        
        # 检查关键包是否完整
        required_packages = ["react", "antd", "axios", "echarts", "dayjs"]
        missing_packages = []
        
        for package in required_packages:
            package_dir = os.path.join(node_modules_dir, package)
            if not os.path.exists(package_dir):
                missing_packages.append(package)
        
        # 应该有缺失的包
        self.assertGreater(len(missing_packages), 0)
        self.assertIn("antd", missing_packages)
        self.assertIn("axios", missing_packages)
        
        logger.info(f"  缺失的包: {missing_packages}")
        logger.info("✅ node_modules不完整情况的检查测试通过")
    
    def test_check_node_modules_complete(self):
        """测试node_modules完整的情况"""
        logger.info("测试node_modules完整的情况...")
        
        node_modules_dir = os.path.join(self.frontend_dir, "node_modules")
        os.makedirs(node_modules_dir, exist_ok=True)
        
        # 创建所有必需的包目录
        required_packages = ["react", "antd", "axios", "echarts", "dayjs", "typescript", "vite"]
        for package in required_packages:
            package_dir = os.path.join(node_modules_dir, package)
            os.makedirs(package_dir, exist_ok=True)
            
            # 创建package.json文件模拟真实包
            package_json = os.path.join(package_dir, "package.json")
            with open(package_json, 'w') as f:
                json.dump({"name": package, "version": "1.0.0"}, f)
        
        # 检查所有包是否存在
        missing_packages = []
        for package in required_packages:
            package_dir = os.path.join(node_modules_dir, package)
            if not os.path.exists(package_dir):
                missing_packages.append(package)
        
        # 应该没有缺失的包
        self.assertEqual(len(missing_packages), 0)
        
        logger.info("✅ node_modules完整情况的检查测试通过")
    
    def test_update_frontend_dependencies_check_outdated(self):
        """测试检查过期依赖"""
        logger.info("测试检查过期依赖...")
        
        # 模拟npm outdated命令的输出
        outdated_output = """Package    Current  Wanted   Latest   Location                          Depended by
react      18.2.0   18.3.1   18.3.1   node_modules/react                quantitative-trading-frontend
antd       5.25.0   5.26.6   5.26.6   node_modules/antd                 quantitative-trading-frontend
axios      1.5.0    1.6.0    1.6.2    node_modules/axios                quantitative-trading-frontend"""
        
        # 解析过期包信息
        lines = outdated_output.strip().split('\n')[1:]  # 跳过标题行
        outdated_packages = []
        
        for line in lines:
            parts = line.split()
            if len(parts) >= 4:
                package_name = parts[0]
                current_version = parts[1]
                wanted_version = parts[2]
                latest_version = parts[3]
                outdated_packages.append({
                    'name': package_name,
                    'current': current_version,
                    'wanted': wanted_version,
                    'latest': latest_version
                })
        
        # 验证解析结果
        self.assertEqual(len(outdated_packages), 3)
        
        package_names = [pkg['name'] for pkg in outdated_packages]
        self.assertIn('react', package_names)
        self.assertIn('antd', package_names)
        self.assertIn('axios', package_names)
        
        logger.info(f"  发现 {len(outdated_packages)} 个过期包")
        for pkg in outdated_packages:
            logger.info(f"    {pkg['name']}: {pkg['current']} -> {pkg['wanted']}")
        
        logger.info("✅ 检查过期依赖测试通过")
    
    def test_update_frontend_dependencies_no_outdated(self):
        """测试没有过期依赖的情况"""
        logger.info("测试没有过期依赖的情况...")
        
        # 模拟没有过期包的输出
        outdated_output = ""
        
        # 检查是否有过期包
        has_outdated = len(outdated_output.strip()) > 0
        self.assertFalse(has_outdated)
        
        logger.info("✅ 没有过期依赖的情况测试通过")
    
    def test_optimize_frontend_dependencies_security_audit(self):
        """测试安全审计功能"""
        logger.info("测试安全审计功能...")
        
        # 模拟npm audit命令的输出 - 无漏洞
        audit_output_clean = """
audited 1500 packages in 2.5s

found 0 vulnerabilities
"""
        
        # 检查是否有安全漏洞
        has_vulnerabilities = "found 0 vulnerabilities" not in audit_output_clean
        self.assertFalse(has_vulnerabilities)
        
        # 模拟有漏洞的情况
        audit_output_with_issues = """
audited 1500 packages in 2.5s

found 3 vulnerabilities (1 moderate, 2 high)

To address all issues, run:
  npm audit fix
"""
        
        has_vulnerabilities_2 = "found 0 vulnerabilities" not in audit_output_with_issues
        self.assertTrue(has_vulnerabilities_2)
        
        # 提取漏洞数量
        import re
        vulnerability_match = re.search(r'found (\d+) vulnerabilities', audit_output_with_issues)
        if vulnerability_match:
            vulnerability_count = int(vulnerability_match.group(1))
            self.assertEqual(vulnerability_count, 3)
        
        logger.info("✅ 安全审计功能测试通过")
    
    def test_optimize_frontend_dependencies_package_analysis(self):
        """测试包分析和优化"""
        logger.info("测试包分析和优化...")
        
        # 模拟包大小分析
        package_sizes = {
            "react": "42.2 kB",
            "antd": "2.1 MB", 
            "echarts": "3.5 MB",
            "axios": "15.3 kB",
            "dayjs": "2.8 kB"
        }
        
        # 找出最大的包
        largest_packages = sorted(package_sizes.items(), 
                                key=lambda x: float(x[1].split()[0]) if 'MB' in x[1] else float(x[1].split()[0])/1000,
                                reverse=True)[:3]
        
        self.assertEqual(largest_packages[0][0], "echarts")
        self.assertEqual(largest_packages[1][0], "antd")
        
        logger.info("  最大的包:")
        for pkg, size in largest_packages:
            logger.info(f"    {pkg}: {size}")
        
        logger.info("✅ 包分析和优化测试通过")
    
    def test_frontend_dependency_cache_management(self):
        """测试前端依赖缓存管理"""
        logger.info("测试前端依赖缓存管理...")
        
        # 创建npm缓存目录
        npm_cache_dir = os.path.join(self.cache_dir, "npm")
        os.makedirs(npm_cache_dir, exist_ok=True)
        
        # 模拟缓存文件
        cache_files = [
            "registry.npmjs.org/react/-/react-18.3.1.tgz",
            "registry.npmjs.org/antd/-/antd-5.26.6.tgz",
            "registry.npmjs.org/axios/-/axios-1.6.0.tgz"
        ]
        
        for cache_file in cache_files:
            cache_file_path = os.path.join(npm_cache_dir, cache_file.replace('/', '_'))
            os.makedirs(os.path.dirname(cache_file_path), exist_ok=True)
            with open(cache_file_path, 'w') as f:
                f.write("cached package content")
        
        # 验证缓存文件存在
        cached_files = []
        for root, dirs, files in os.walk(npm_cache_dir):
            cached_files.extend(files)
        
        self.assertGreater(len(cached_files), 0)
        
        # 计算缓存大小
        total_size = 0
        for root, dirs, files in os.walk(npm_cache_dir):
            for file in files:
                file_path = os.path.join(root, file)
                total_size += os.path.getsize(file_path)
        
        self.assertGreater(total_size, 0)
        
        logger.info(f"  缓存文件数量: {len(cached_files)}")
        logger.info(f"  缓存总大小: {total_size} bytes")
        
        logger.info("✅ 前端依赖缓存管理测试通过")
    
    def test_critical_frontend_packages_verification(self):
        """测试关键前端包验证"""
        logger.info("测试关键前端包验证...")
        
        node_modules_dir = os.path.join(self.frontend_dir, "node_modules")
        os.makedirs(node_modules_dir, exist_ok=True)
        
        # 定义关键包列表
        critical_packages = [
            "react",
            "antd", 
            "@vitejs/plugin-react",
            "typescript",
            "vite",
            "axios",
            "echarts"
        ]
        
        # 创建关键包目录
        verified_packages = []
        for package in critical_packages:
            if package.startswith('@'):
                # 处理scoped包
                scope, name = package.split('/')
                package_dir = os.path.join(node_modules_dir, scope, name)
            else:
                package_dir = os.path.join(node_modules_dir, package)
            
            os.makedirs(package_dir, exist_ok=True)
            
            # 创建package.json验证包的完整性
            package_json = os.path.join(package_dir, "package.json")
            with open(package_json, 'w') as f:
                json.dump({
                    "name": package,
                    "version": "1.0.0",
                    "main": "index.js"
                }, f)
            
            if os.path.exists(package_dir) and os.path.exists(package_json):
                verified_packages.append(package)
        
        # 验证所有关键包都已验证
        self.assertEqual(len(verified_packages), len(critical_packages))
        
        logger.info(f"  验证通过的关键包数量: {len(verified_packages)}")
        for package in verified_packages:
            logger.info(f"    ✓ {package}")
        
        logger.info("✅ 关键前端包验证测试通过")
    
    def test_node_dependency_installation_workflow(self):
        """测试Node.js依赖安装完整工作流"""
        logger.info("测试Node.js依赖安装完整工作流...")
        
        # 1. 检查package.json存在
        package_json_exists = os.path.exists(self.package_json_file)
        self.assertTrue(package_json_exists)
        
        # 2. 初始化缓存
        npm_cache_dir = os.path.join(self.cache_dir, "npm")
        os.makedirs(npm_cache_dir, exist_ok=True)
        cache_initialized = os.path.exists(npm_cache_dir)
        self.assertTrue(cache_initialized)
        
        # 3. 检查node_modules完整性
        node_modules_dir = os.path.join(self.frontend_dir, "node_modules")
        needs_install = not os.path.exists(node_modules_dir)
        self.assertTrue(needs_install)  # 初始状态应该需要安装
        
        # 4. 模拟清理现有node_modules（如果存在且损坏）
        if os.path.exists(node_modules_dir):
            shutil.rmtree(node_modules_dir)
        
        # 5. 模拟npm install过程
        os.makedirs(node_modules_dir, exist_ok=True)
        
        # 创建一些关键包
        key_packages = ["react", "antd", "typescript", "vite"]
        for package in key_packages:
            package_dir = os.path.join(node_modules_dir, package)
            os.makedirs(package_dir, exist_ok=True)
        
        # 6. 验证安装结果
        install_success = os.path.exists(node_modules_dir)
        self.assertTrue(install_success)
        
        # 7. 验证关键包
        verified_count = 0
        for package in key_packages:
            package_dir = os.path.join(node_modules_dir, package)
            if os.path.exists(package_dir):
                verified_count += 1
        
        self.assertEqual(verified_count, len(key_packages))
        
        logger.info(f"  工作流步骤:")
        logger.info(f"    1. ✓ package.json检查通过")
        logger.info(f"    2. ✓ 缓存初始化完成")
        logger.info(f"    3. ✓ 依赖安装完成")
        logger.info(f"    4. ✓ 关键包验证通过 ({verified_count}/{len(key_packages)})")
        
        logger.info("✅ Node.js依赖安装完整工作流测试通过")


def run_node_dependency_tests():
    """运行Node.js依赖管理测试套件"""
    logger.info("=" * 60)
    logger.info("🧪 Node.js依赖管理单元测试套件")
    logger.info("=" * 60)
    logger.info("测试开始时间:", __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    logger.info()
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestNodeDependencyManager)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    logger.info()
    logger.info("=" * 60)
    logger.info("📊 测试结果统计")
    logger.info("=" * 60)
    logger.info(f"总测试数: {result.testsRun}")
    logger.info(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    logger.info(f"失败: {len(result.failures)}")
    logger.info(f"错误: {len(result.errors)}")
    
    if result.failures:
        logger.info("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            logger.info(f"  - {test}: {traceback}")
    
    if result.errors:
        logger.info("\n💥 错误的测试:")
        for test, traceback in result.errors:
            logger.info(f"  - {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    logger.info(f"\n✅ 测试成功率: {success_rate:.1f}%")
    
    if result.wasSuccessful():
        logger.info("\n🎉 所有Node.js依赖管理测试通过！")
        return True
    else:
        logger.info("\n⚠️ 部分测试失败，请检查上述错误信息")
        return False


if __name__ == "__main__":
    success = run_node_dependency_tests()
    sys.exit(0 if success else 1)