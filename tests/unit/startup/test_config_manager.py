import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置管理模块单元测试
测试配置文件生成、验证和管理功能
"""

import os
import sys
import unittest
import tempfile
import shutil
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


class TestConfigManager(unittest.TestCase):
    """配置管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时测试目录
        self.test_dir = tempfile.mkdtemp()
        self.config_manager_script = project_root / "scripts" / "startup" / "modules" / "config_manager.sh"
        
        # 设置测试环境变量
        self.test_env = os.environ.copy()
        self.test_env['PROJECT_ROOT'] = self.test_dir
        
        logger.info(f"测试目录: {self.test_dir}")
        
    def tearDown(self):
        """测试后清理"""
        # 清理临时目录
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
            
    def create_test_structure(self):
        """创建测试目录结构"""
        # 创建必要的目录
        directories = [
            "web_ui/backend",
            "web_ui/frontend", 
            "config/templates",
            "data/db",
            "logs",
            "backup"
        ]
        
        for directory in directories:
            os.makedirs(os.path.join(self.test_dir, directory), exist_ok=True)
            
    def run_bash_function(self, function_name, *args):
        """运行bash函数"""
        # 创建临时脚本来测试函数
        script_content = f"""
#!/bin/bash
source {self.config_manager_script}

# 模拟日志函数
log_info() {{ echo "[INFO] $1"; }}
log_success() {{ echo "[SUCCESS] $1"; }}
log_warning() {{ echo "[WARNING] $1"; }}
log_error() {{ echo "[ERROR] $1"; }}
log_debug() {{ echo "[DEBUG] $1"; }}

# 模拟计时器函数
start_timer() {{ echo "Timer started: $1"; }}
end_timer() {{ echo "Timer ended: $1 - $2"; }}

# 设置项目根目录
PROJECT_ROOT="{self.test_dir}"

# 运行指定函数
{function_name} {' '.join(args)}
"""
        
        # 写入临时脚本文件
        script_file = os.path.join(self.test_dir, "test_script.sh")
        with open(script_file, 'w') as f:
            f.write(script_content)
            
        # 设置执行权限
        os.chmod(script_file, 0o755)
        
        # 执行脚本
        try:
            result = subprocess.run(
                ['/bin/bash', script_file],
                capture_output=True,
                text=True,
                env=self.test_env,
                timeout=30
            )
            return result
        except subprocess.TimeoutExpired:
            self.fail("脚本执行超时")
            
    def test_init_config_templates(self):
        """测试配置模板初始化"""
        logger.info("测试配置模板初始化...")
        
        self.create_test_structure()
        
        # 运行初始化函数
        result = self.run_bash_function("init_config_templates")
        
        # 检查执行结果
        self.assertEqual(result.returncode, 0, f"函数执行失败: {result.stderr}")
        self.assertIn("配置模板和默认值初始化完成", result.stdout)
        
        logger.info("✅ 配置模板初始化测试通过")
        
    def test_generate_backend_config(self):
        """测试后端配置文件生成"""
        logger.info("测试后端配置文件生成...")
        
        self.create_test_structure()
        
        # 运行配置生成函数
        result = self.run_bash_function("init_config_templates")
        self.assertEqual(result.returncode, 0)
        
        result = self.run_bash_function("generate_backend_config")
        
        # 检查执行结果
        self.assertEqual(result.returncode, 0, f"函数执行失败: {result.stderr}")
        
        # 检查配置文件是否生成
        backend_config = os.path.join(self.test_dir, "web_ui", "backend", ".env")
        self.assertTrue(os.path.exists(backend_config), "后端配置文件未生成")
        
        # 检查配置文件内容
        with open(backend_config, 'r') as f:
            content = f.read()
            
        # 验证必需的配置项
        required_configs = [
            "HOST=",
            "PORT=",
            "DEBUG=",
            "DATABASE_URL=",
            "LOG_LEVEL=",
            "SECRET_KEY=",
            "CORS_ORIGINS="
        ]
        
        for config in required_configs:
            self.assertIn(config, content, f"配置项缺失: {config}")
            
        logger.info("✅ 后端配置文件生成测试通过")
        
    def test_generate_frontend_config(self):
        """测试前端配置文件生成"""
        logger.info("测试前端配置文件生成...")
        
        self.create_test_structure()
        
        # 运行配置生成函数
        result = self.run_bash_function("init_config_templates")
        self.assertEqual(result.returncode, 0)
        
        result = self.run_bash_function("generate_frontend_config")
        
        # 检查执行结果
        self.assertEqual(result.returncode, 0, f"函数执行失败: {result.stderr}")
        
        # 检查配置文件是否生成
        frontend_config = os.path.join(self.test_dir, "web_ui", "frontend", ".env")
        self.assertTrue(os.path.exists(frontend_config), "前端配置文件未生成")
        
        # 检查配置文件内容
        with open(frontend_config, 'r') as f:
            content = f.read()
            
        # 验证必需的配置项
        required_configs = [
            "VITE_API_BASE_URL=",
            "VITE_WS_URL=",
            "VITE_APP_TITLE=",
            "VITE_APP_VERSION=",
            "NODE_ENV="
        ]
        
        for config in required_configs:
            self.assertIn(config, content, f"配置项缺失: {config}")
            
        logger.info("✅ 前端配置文件生成测试通过")
        
    def test_validate_backend_config(self):
        """测试后端配置验证"""
        logger.info("测试后端配置验证...")
        
        self.create_test_structure()
        
        # 创建测试配置文件
        backend_config = os.path.join(self.test_dir, "web_ui", "backend", ".env")
        with open(backend_config, 'w') as f:
            f.write("""
HOST=127.0.0.1
PORT=8000
DEBUG=true
DATABASE_URL=sqlite:///test.db
LOG_LEVEL=INFO
SECRET_KEY=test-secret-key
CORS_ORIGINS=["http://localhost:3000"]
""")
        
        # 运行验证函数
        result = self.run_bash_function("validate_backend_config", backend_config)
        
        # 检查执行结果
        self.assertEqual(result.returncode, 0, f"配置验证失败: {result.stderr}")
        self.assertIn("后端配置文件验证通过", result.stdout)
        
        logger.info("✅ 后端配置验证测试通过")
        
    def test_validate_frontend_config(self):
        """测试前端配置验证"""
        logger.info("测试前端配置验证...")
        
        self.create_test_structure()
        
        # 创建测试配置文件
        frontend_config = os.path.join(self.test_dir, "web_ui", "frontend", ".env")
        with open(frontend_config, 'w') as f:
            f.write("""
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000
VITE_APP_TITLE=测试应用
VITE_APP_VERSION=1.0.0
NODE_ENV=development
""")
        
        # 运行验证函数
        result = self.run_bash_function("validate_frontend_config", frontend_config)
        
        # 检查执行结果
        self.assertEqual(result.returncode, 0, f"配置验证失败: {result.stderr}")
        self.assertIn("前端配置文件验证通过", result.stdout)
        
        logger.info("✅ 前端配置验证测试通过")
        
    def test_backup_config_file(self):
        """测试配置文件备份"""
        logger.info("测试配置文件备份...")
        
        self.create_test_structure()
        
        # 创建测试配置文件
        test_config = os.path.join(self.test_dir, "web_ui", ".env")
        with open(test_config, 'w') as f:
            f.write("TEST_CONFIG=value\n")
            
        # 运行备份函数
        result = self.run_bash_function("backup_config_file", test_config)
        
        # 检查执行结果
        self.assertEqual(result.returncode, 0, f"配置备份失败: {result.stderr}")
        
        # 检查备份目录是否创建
        backup_dir = os.path.join(self.test_dir, "backup", "config")
        self.assertTrue(os.path.exists(backup_dir), "备份目录未创建")
        
        # 检查备份文件是否存在
        backup_files = os.listdir(backup_dir)
        self.assertTrue(len(backup_files) > 0, "备份文件未创建")
        self.assertTrue(any(".env.backup_" in f for f in backup_files), "备份文件命名不正确")
        
        logger.info("✅ 配置文件备份测试通过")
        
    def test_complete_config_setup(self):
        """测试完整配置设置流程"""
        logger.info("测试完整配置设置流程...")
        
        self.create_test_structure()
        
        # 运行完整配置设置
        result = self.run_bash_function("setup_all_configurations")
        
        # 检查执行结果
        self.assertEqual(result.returncode, 0, f"配置设置失败: {result.stderr}")
        
        # 检查所有配置文件是否生成
        config_files = [
            "web_ui/.env",
            "web_ui/backend/.env",
            "web_ui/frontend/.env"
        ]
        
        for config_file in config_files:
            full_path = os.path.join(self.test_dir, config_file)
            self.assertTrue(os.path.exists(full_path), f"配置文件未生成: {config_file}")
            
        logger.info("✅ 完整配置设置流程测试通过")
        
    def test_check_config_integrity(self):
        """测试配置完整性检查"""
        logger.info("测试配置完整性检查...")
        
        self.create_test_structure()
        
        # 创建完整的测试配置文件
        backend_config = os.path.join(self.test_dir, "web_ui", "backend", ".env")
        with open(backend_config, 'w') as f:
            f.write("""
HOST=127.0.0.1
PORT=8000
DEBUG=true
DATABASE_URL=sqlite:///test.db
LOG_LEVEL=INFO
SECRET_KEY=test-secret-key-with-sufficient-length-for-security
CORS_ORIGINS=["http://localhost:3000"]
ACCESS_TOKEN_EXPIRE_MINUTES=480
""")
        
        # 运行完整性检查
        result = self.run_bash_function("check_config_integrity", backend_config, "backend")
        
        # 检查执行结果
        self.assertEqual(result.returncode, 0, f"配置完整性检查失败: {result.stderr}")
        self.assertIn("后端配置完整性检查通过", result.stdout)
        
        logger.info("✅ 配置完整性检查测试通过")
        
    def test_fix_config_errors(self):
        """测试配置错误修复"""
        logger.info("测试配置错误修复...")
        
        self.create_test_structure()
        
        # 创建有问题的配置文件
        backend_config = os.path.join(self.test_dir, "web_ui", "backend", ".env")
        with open(backend_config, 'w') as f:
            f.write("""HOST=127.0.0.1
PORT=invalid_port
DEBUG=true
DATABASE_URL=sqlite:///test.db
LOG_LEVEL=INVALID_LEVEL
SECRET_KEY=weak
CORS_ORIGINS=["http://localhost:3000"]
""")
        
        # 初始化配置模板
        result = self.run_bash_function("init_config_templates")
        self.assertEqual(result.returncode, 0)
        
        # 运行错误修复
        result = self.run_bash_function("fix_config_errors", backend_config, "backend")
        
        # 检查执行结果
        self.assertEqual(result.returncode, 0, f"配置错误修复失败: {result.stderr}")
        
        # 检查修复后的配置文件
        with open(backend_config, 'r') as f:
            content = f.read()
            
        logger.info(f"修复后的配置内容:\n{content}")
        
        # 验证修复结果 - 检查端口号是否为数字
        port_lines = [line.strip() for line in content.split('\n') if line.strip().startswith('PORT=')]
        self.assertTrue(len(port_lines) > 0, "PORT配置项不存在")
        port_value = port_lines[0].split('=')[1]
        self.assertTrue(port_value.isdigit(), f"端口号不是数字: {port_value}")
        
        # 检查日志级别是否有效
        log_lines = [line.strip() for line in content.split('\n') if line.strip().startswith('LOG_LEVEL=')]
        self.assertTrue(len(log_lines) > 0, "LOG_LEVEL配置项不存在")
        log_value = log_lines[0].split('=')[1]
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        self.assertIn(log_value, valid_levels, f"日志级别无效: {log_value}")
        
        # 检查是否添加了缺失的配置项
        self.assertIn("ACCESS_TOKEN_EXPIRE_MINUTES=", content, "缺失配置项未添加")
        
        logger.info("✅ 配置错误修复测试通过")
        
    def test_merge_config_updates(self):
        """测试配置更新合并"""
        logger.info("测试配置更新合并...")
        
        self.create_test_structure()
        
        # 创建原配置文件
        config_file = os.path.join(self.test_dir, "web_ui", ".env")
        with open(config_file, 'w') as f:
            f.write("""
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=原标题
NODE_ENV=development
""")
        
        # 创建更新文件
        updates_file = os.path.join(self.test_dir, "updates.env")
        with open(updates_file, 'w') as f:
            f.write("""
VITE_APP_TITLE=新标题
VITE_APP_VERSION=2.0.0
""")
        
        # 运行配置合并
        result = self.run_bash_function("merge_config_updates", config_file, updates_file)
        
        # 检查执行结果
        self.assertEqual(result.returncode, 0, f"配置合并失败: {result.stderr}")
        
        # 检查合并结果
        with open(config_file, 'r') as f:
            content = f.read()
            
        self.assertIn("VITE_APP_TITLE=新标题", content, "配置项未更新")
        self.assertIn("VITE_APP_VERSION=2.0.0", content, "新配置项未添加")
        self.assertIn("VITE_API_BASE_URL=http://localhost:8000", content, "原配置项丢失")
        
        logger.info("✅ 配置更新合并测试通过")
        
    def test_config_backup_restore(self):
        """测试配置备份和恢复"""
        logger.info("测试配置备份和恢复...")
        
        self.create_test_structure()
        
        # 创建测试配置文件
        config_file = os.path.join(self.test_dir, "web_ui", ".env")
        original_content = "VITE_APP_TITLE=原始配置\nNODE_ENV=development\n"
        with open(config_file, 'w') as f:
            f.write(original_content)
            
        # 创建配置备份
        backup_name = "test_backup"
        result = self.run_bash_function("create_config_backup", backup_name)
        
        # 检查备份创建
        self.assertEqual(result.returncode, 0, f"配置备份创建失败: {result.stderr}")
        
        backup_dir = os.path.join(self.test_dir, "backup", "config", backup_name)
        self.assertTrue(os.path.exists(backup_dir), "备份目录未创建")
        
        backup_file = os.path.join(backup_dir, ".env")
        self.assertTrue(os.path.exists(backup_file), "备份文件未创建")
        
        # 修改原配置文件
        with open(config_file, 'w') as f:
            f.write("VITE_APP_TITLE=修改后的配置\n")
            
        # 恢复配置备份
        result = self.run_bash_function("restore_config_backup", backup_name)
        
        # 检查恢复结果
        self.assertEqual(result.returncode, 0, f"配置恢复失败: {result.stderr}")
        
        with open(config_file, 'r') as f:
            restored_content = f.read()
            
        self.assertEqual(restored_content, original_content, "配置恢复不正确")
        
        logger.info("✅ 配置备份和恢复测试通过")


def run_config_manager_tests():
    """运行配置管理器测试"""
    logger.info("=" * 50)
    logger.info("🧪 配置管理器单元测试")
    logger.info("=" * 50)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestConfigManager)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    logger.info("\n" + "=" * 50)
    if result.wasSuccessful():
        logger.info("✅ 所有配置管理器测试通过！")
    else:
        logger.info("❌ 部分测试失败")
        logger.info(f"失败数量: {len(result.failures)}")
        logger.info(f"错误数量: {len(result.errors)}")
    logger.info("=" * 50)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_config_manager_tests()
    sys.exit(0 if success else 1)