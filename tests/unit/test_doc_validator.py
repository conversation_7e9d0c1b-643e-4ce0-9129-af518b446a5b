import logging
logger = logging.getLogger(__name__)
"""
Unit tests for DocValidator.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, Mock

from src.documentation.doc_validator import DocValidator, ValidationRule
from src.documentation.models import (
    DocumentationConfig,
    DocumentInfo,
    DocumentType,
    DocumentStatus,
    ValidationSeverity
)


class TestDocValidator:
    """Test cases for DocValidator."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests."""
        temp_dir = Path(tempfile.mkdtemp())
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def config(self, temp_dir):
        """Create test configuration."""
        return DocumentationConfig(
            base_path=temp_dir / "docs",
            output_path=temp_dir / "output",
            template_path=temp_dir / "templates",
            max_line_length=80,
            require_headers=True,
            check_links=True,
            spell_check_enabled=True
        )
    
    @pytest.fixture
    def validator(self, config):
        """Create DocValidator instance."""
        return DocValidator(config)
    
    @pytest.fixture
    def sample_good_doc(self, temp_dir):
        """Create a well-formatted sample document."""
        doc_file = temp_dir / "good_doc.md"
        doc_file.write_text("""# Sample Documentation

## Introduction

This is a well-formatted document that follows best practices.
It has proper headers, reasonable line lengths, and good structure.

## Features

- Clear headings
- Proper formatting
- Good content length
- Examples included

## Example Usage

```python
def hello_world():
    logger.info("Hello, World!")
```

## Conclusion

This document demonstrates good documentation practices.
""")
        return doc_file
    
    @pytest.fixture
    def sample_bad_doc(self, temp_dir):
        """Create a poorly formatted sample document."""
        doc_file = temp_dir / "bad_doc.md"
        doc_file.write_text("""No title here

This document has many issues including very long lines that exceed the maximum line length limit and should be flagged by the validator as problematic formatting.

### Skipped Header Level

Empty section below:

### Empty Section

[Broken Link](http://this-domain-does-not-exist-12345.com)

TODO: Fix this later
FIXME: This needs attention

Some misspelled words: thiss, documentt, examplee
""")
        return doc_file
    
    @pytest.fixture
    def sample_doc_info(self, sample_good_doc):
        """Create DocumentInfo for testing."""
        return DocumentInfo(
            id="test_doc_001",
            title="Sample Documentation",
            type=DocumentType.USER_GUIDE,
            status=DocumentStatus.DRAFT,
            file_path=sample_good_doc,
            description="A sample document for testing"
        )
    
    def test_initialization(self, validator, config):
        """Test validator initialization."""
        assert validator.config == config
        assert isinstance(validator.rules, dict)
        assert len(validator.rules) > 0
        assert isinstance(validator._common_words, set)
        assert len(validator._common_words) > 0
        
        # Check specific rules exist
        assert 'missing_title' in validator.rules
        assert 'broken_links' in validator.rules
        assert 'long_lines' in validator.rules
    
    def test_validation_rule_class(self):
        """Test ValidationRule class."""
        rule = ValidationRule(
            name="test_rule",
            description="Test rule description",
            severity=ValidationSeverity.WARNING
        )
        
        assert rule.name == "test_rule"
        assert rule.description == "Test rule description"
        assert rule.severity == ValidationSeverity.WARNING
    
    def test_validate_good_document(self, validator, sample_doc_info):
        """Test validation of a well-formatted document."""
        result = validator.validate_document(sample_doc_info)
        
        assert result.document_id == sample_doc_info.id
        assert result.is_valid is True
        assert result.score > 50.0  # Should have reasonable score
        
        # Should have minimal issues
        error_issues = [i for i in result.issues if i.severity == ValidationSeverity.ERROR]
        assert len(error_issues) == 0
    
    def test_validate_bad_document(self, validator, sample_bad_doc, temp_dir):
        """Test validation of a poorly formatted document."""
        bad_doc_info = DocumentInfo(
            id="bad_doc_001",
            title="Bad Document",
            type=DocumentType.USER_GUIDE,
            status=DocumentStatus.DRAFT,
            file_path=sample_bad_doc,
            description="A poorly formatted document"
        )
        
        result = validator.validate_document(bad_doc_info)
        
        assert result.document_id == bad_doc_info.id
        assert result.is_valid is False  # Should have errors
        assert result.score < 80.0  # Should have low score
        
        # Should have multiple issues
        assert len(result.issues) > 5
        
        # Check for specific issues
        issue_rules = [issue.rule for issue in result.issues]
        assert 'missing_title' in issue_rules
        assert 'long_lines' in issue_rules
        # Note: inconsistent_style may not always be detected depending on content
    
    def test_check_completeness(self, validator):
        """Test completeness checking."""
        # Test missing title
        content_no_title = "This document has no title\n\nJust some content."
        issues = validator._check_completeness(content_no_title)
        
        title_issues = [i for i in issues if i.rule == 'missing_title']
        assert len(title_issues) > 0
        
        # Test empty sections
        content_empty_section = """# Title

## Section 1

Some content here.

## Empty Section

## Another Section

More content.
"""
        issues = validator._check_completeness(content_empty_section)
        empty_section_issues = [i for i in issues if i.rule == 'empty_sections']
        assert len(empty_section_issues) > 0
    
    def test_check_style_and_format(self, validator):
        """Test style and format checking."""
        # Test long lines
        long_line = "This is a very long line that exceeds the maximum line length limit and should be flagged by the validator"
        content_long_lines = f"# Title\n\n{long_line}"
        
        issues = validator._check_style_and_format(content_long_lines)
        long_line_issues = [i for i in issues if i.rule == 'long_lines']
        assert len(long_line_issues) > 0
        
        # Test header level skipping
        content_bad_headers = """# Title

### Skipped H2

Content here.
"""
        issues = validator._check_style_and_format(content_bad_headers)
        style_issues = [i for i in issues if i.rule == 'inconsistent_style']
        assert len(style_issues) > 0
    
    @patch('requests.head')
    def test_check_links(self, mock_head, validator, temp_dir):
        """Test link validation."""
        # Create a test file for local link testing
        local_file = temp_dir / "existing_file.md"
        local_file.write_text("# Existing File")
        
        # Mock HTTP response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_head.return_value = mock_response
        
        content = f"""# Test Document

[Good HTTP Link](https://example.com)
[Local Link](existing_file.md)
[Broken Local Link](non_existent.md)
[Broken HTTP Link](http://broken-link-test.com)
"""
        
        # Mock broken HTTP link
        def mock_head_side_effect(url, **kwargs):
            if 'broken-link-test' in url:
                mock_response.status_code = 404
                return mock_response
            else:
                mock_response.status_code = 200
                return mock_response
        
        mock_head.side_effect = mock_head_side_effect
        
        base_path = temp_dir / "test_doc.md"
        issues = validator._check_links(content, base_path)
        
        # Should find broken links
        broken_link_issues = [i for i in issues if i.rule == 'broken_links']
        assert len(broken_link_issues) >= 1  # At least the broken local link
    
    def test_check_spelling(self, validator):
        """Test spell checking."""
        content = """# Title

This document contains some misspelled words like thiss, documentt, and examplee.
It also has correct words like document, example, and this.
"""
        
        issues = validator._check_spelling(content)
        spelling_issues = [i for i in issues if i.rule == 'spelling_errors']
        
        # Should find some spelling issues
        assert len(spelling_issues) > 0
    
    def test_calculate_quality_score(self, validator):
        """Test quality score calculation."""
        from src.documentation.models import ValidationIssue
        
        # Test with no issues
        issues = []
        content = "# Title\n\nGood content with proper length and structure."
        score = validator._calculate_quality_score(issues, content)
        assert score == 100.0
        
        # Test with errors
        issues = [
            ValidationIssue(
                severity=ValidationSeverity.ERROR,
                message="Test error",
                rule="test_rule"
            )
        ]
        score = validator._calculate_quality_score(issues, content)
        assert score < 100.0
        
        # Test with code examples bonus
        content_with_code = "# Title\n\n```python\nprint('hello')\n```\n\nSome content here."
        score_with_code = validator._calculate_quality_score([], content_with_code)
        score_without_code = validator._calculate_quality_score([], "# Title\n\nContent")
        # Both should be high scores, but code version might have bonus
        assert score_with_code >= score_without_code
    
    def test_validate_multiple_documents(self, validator, sample_good_doc, sample_bad_doc):
        """Test validation of multiple documents."""
        good_doc_info = DocumentInfo(
            id="good_doc",
            title="Good Document",
            type=DocumentType.USER_GUIDE,
            status=DocumentStatus.DRAFT,
            file_path=sample_good_doc
        )
        
        bad_doc_info = DocumentInfo(
            id="bad_doc",
            title="Bad Document",
            type=DocumentType.USER_GUIDE,
            status=DocumentStatus.DRAFT,
            file_path=sample_bad_doc
        )
        
        documents = [good_doc_info, bad_doc_info]
        results = validator.validate_multiple_documents(documents)
        
        assert len(results) == 2
        assert "good_doc" in results
        assert "bad_doc" in results
        
        # Good document should be valid
        assert results["good_doc"].is_valid is True
        
        # Bad document should be invalid
        assert results["bad_doc"].is_valid is False
    
    def test_generate_validation_report(self, validator, temp_dir):
        """Test validation report generation."""
        from src.documentation.models import ValidationResult, ValidationIssue
        
        # Create mock results
        results = {
            "doc1": ValidationResult(
                document_id="doc1",
                is_valid=True,
                issues=[],
                score=95.0
            ),
            "doc2": ValidationResult(
                document_id="doc2",
                is_valid=False,
                issues=[
                    ValidationIssue(
                        severity=ValidationSeverity.ERROR,
                        message="Test error",
                        rule="test_rule",
                        suggestion="Fix the test error"
                    )
                ],
                score=60.0
            )
        }
        
        output_path = temp_dir / "validation_report.md"
        validator.generate_validation_report(results, output_path)
        
        assert output_path.exists()
        
        content = output_path.read_text()
        assert "Documentation Validation Report" in content
        assert "**Total Documents**: 2" in content
        assert "**Valid Documents**: 1" in content
        assert "doc1" in content
        assert "doc2" in content
        assert "✅ VALID" in content
        assert "❌ INVALID" in content
    
    def test_get_improvement_suggestions(self, validator):
        """Test improvement suggestions generation."""
        from src.documentation.models import ValidationResult, ValidationIssue
        
        result = ValidationResult(
            document_id="test_doc",
            is_valid=False,
            issues=[
                ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    message="Critical error",
                    rule="test_rule",
                    suggestion="Fix this critical issue"
                ),
                ValidationIssue(
                    severity=ValidationSeverity.WARNING,
                    message="Warning issue",
                    rule="test_rule",
                    suggestion="Address this warning"
                ),
                ValidationIssue(
                    severity=ValidationSeverity.INFO,
                    message="Info issue",
                    rule="test_rule",
                    suggestion="Consider this improvement"
                )
            ],
            score=45.0
        )
        
        suggestions = validator.get_improvement_suggestions(result)
        
        assert len(suggestions) > 0
        assert any("Critical Issues" in s for s in suggestions)
        assert any("Fix this critical issue" in s for s in suggestions)
        assert any("General Recommendations" in s for s in suggestions)
    
    def test_validate_nonexistent_file(self, validator, temp_dir):
        """Test validation of non-existent file."""
        nonexistent_file = temp_dir / "nonexistent.md"
        
        doc_info = DocumentInfo(
            id="nonexistent_doc",
            title="Non-existent Document",
            type=DocumentType.USER_GUIDE,
            status=DocumentStatus.DRAFT,
            file_path=nonexistent_file
        )
        
        result = validator.validate_document(doc_info)
        
        assert result.is_valid is False
        assert result.score == 0.0
        assert len(result.issues) > 0
        assert any("file not found" in issue.message.lower() for issue in result.issues)
    
    def test_link_validation_caching(self, validator, temp_dir):
        """Test that link validation results are cached."""
        content = """# Test

[Test Link](https://example.com)
[Same Link Again](https://example.com)
"""
        
        base_path = temp_dir / "test.md"
        
        with patch('requests.head') as mock_head:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_head.return_value = mock_response
            
            # First validation should make HTTP request
            validator._check_links(content, base_path)
            first_call_count = mock_head.call_count
            assert first_call_count >= 1
            
            # Second validation should use cache
            validator._check_links(content, base_path)
            assert mock_head.call_count == first_call_count  # No additional calls