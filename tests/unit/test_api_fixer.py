"""
API修复器单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
import aiohttp

from src.core.monitoring.api_fixer import (
    APIFixer,
    APIEndpoint,
    APIIssue,
    APIIssueType,
    CORSConfig
)
from src.monitoring.diagnostic_models import FixResult, FixStatus


class TestAPIFixer:
    """API修复器测试类"""
    
    @pytest.fixture
    def api_fixer(self):
        """创建API修复器实例"""
        return APIFixer(base_url="http://localhost:8000")
    
    @pytest.fixture
    def sample_endpoint(self):
        """创建示例端点"""
        return APIEndpoint(
            path="/api/test",
            method="GET",
            expected_status=200,
            timeout=5.0
        )
    
    @pytest.fixture
    def sample_api_issue(self, sample_endpoint):
        """创建示例API问题"""
        return APIIssue(
            issue_id="test_issue_001",
            issue_type=APIIssueType.SLOW_RESPONSE,
            endpoint=sample_endpoint,
            severity="medium",
            description="API响应时间过长",
            detected_at=datetime.now()
        )
    
    def test_init(self, api_fixer):
        """测试初始化"""
        assert api_fixer.base_url == "http://localhost:8000"
        assert len(api_fixer.expected_endpoints) > 0
        assert api_fixer.default_cors_config is not None
        assert "response_time" in api_fixer.performance_thresholds
    
    @pytest.mark.asyncio
    async def test_check_endpoint_registration_no_issues(self, api_fixer):
        """测试端点注册检查 - 无问题情况"""
        with patch.object(api_fixer, '_check_single_endpoint', return_value=None):
            issues = await api_fixer.check_endpoint_registration()
            assert issues == []
    
    @pytest.mark.asyncio
    async def test_check_endpoint_registration_with_issues(self, api_fixer, sample_api_issue):
        """测试端点注册检查 - 有问题情况"""
        with patch.object(api_fixer, '_check_single_endpoint', return_value=sample_api_issue):
            issues = await api_fixer.check_endpoint_registration()
            assert len(issues) == len(api_fixer.expected_endpoints)
            assert all(isinstance(issue, APIIssue) for issue in issues)
    
    @pytest.mark.asyncio
    async def test_fix_cors_configuration_no_issues(self, api_fixer):
        """测试CORS配置修复 - 无问题情况"""
        with patch.object(api_fixer, '_check_cors_configuration', return_value=[]):
            result = await api_fixer.fix_cors_configuration()
            
            assert result.component == "cors_configuration"
            assert result.status == FixStatus.SUCCESS
            assert result.message == "CORS配置正常"
            assert result.details["issues_found"] == 0
    
    @pytest.mark.asyncio
    async def test_fix_cors_configuration_with_issues(self, api_fixer, sample_api_issue):
        """测试CORS配置修复 - 有问题情况"""
        mock_fix_result = FixResult(
            component="cors",
            status=FixStatus.SUCCESS,
            message="修复成功",
            details={"issue_id": "test_issue_001"},
            duration=0.1
        )
        
        with patch.object(api_fixer, '_check_cors_configuration', return_value=[sample_api_issue]):
            with patch.object(api_fixer, '_fix_cors_issue', return_value=mock_fix_result):
                result = await api_fixer.fix_cors_configuration()
                
                assert result.component == "cors_configuration"
                assert result.status == FixStatus.SUCCESS
                assert "修复了 1/1 个CORS问题" in result.message
                assert result.details["total_issues"] == 1
                assert result.details["fixed_issues"] == 1
    
    @pytest.mark.asyncio
    async def test_fix_cors_configuration_exception(self, api_fixer):
        """测试CORS配置修复 - 异常情况"""
        with patch.object(api_fixer, '_check_cors_configuration', side_effect=Exception("测试异常")):
            result = await api_fixer.fix_cors_configuration()
            
            assert result.component == "cors_configuration"
            assert result.status == FixStatus.FAILED
            assert "CORS配置修复失败" in result.message
            assert "测试异常" in result.details["error"]
    
    @pytest.mark.asyncio
    async def test_fix_middleware_issues_no_issues(self, api_fixer):
        """测试中间件修复 - 无问题情况"""
        with patch.object(api_fixer, '_check_middleware_issues', return_value=[]):
            result = await api_fixer.fix_middleware_issues()
            
            assert result.component == "middleware"
            assert result.status == FixStatus.SUCCESS
            assert result.message == "中间件运行正常"
            assert result.details["issues_found"] == 0
    
    @pytest.mark.asyncio
    async def test_validate_api_responses_no_issues(self, api_fixer):
        """测试API响应验证 - 无问题情况"""
        with patch.object(api_fixer, '_check_api_responses', return_value=[]):
            result = await api_fixer.validate_api_responses()
            
            assert result.component == "api_responses"
            assert result.status == FixStatus.SUCCESS
            assert result.message == "API响应正常"
            assert result.details["issues_found"] == 0
    
    @pytest.mark.asyncio
    async def test_optimize_api_performance_no_issues(self, api_fixer):
        """测试API性能优化 - 无问题情况"""
        with patch.object(api_fixer, '_analyze_api_performance', return_value=[]):
            result = await api_fixer.optimize_api_performance()
            
            assert result.component == "api_performance"
            assert result.status == FixStatus.SUCCESS
            assert result.message == "API性能良好"
            assert result.details["issues_found"] == 0
    
    @pytest.mark.asyncio
    async def test_run_comprehensive_api_fix(self, api_fixer):
        """测试全面API修复"""
        # Mock所有修复方法
        mock_cors_result = FixResult("cors_configuration", FixStatus.SUCCESS, "CORS修复成功", {}, 0.1)
        mock_middleware_result = FixResult("middleware", FixStatus.SUCCESS, "中间件修复成功", {}, 0.1)
        mock_response_result = FixResult("api_responses", FixStatus.SUCCESS, "响应验证成功", {}, 0.1)
        mock_performance_result = FixResult("api_performance", FixStatus.SUCCESS, "性能优化成功", {}, 0.1)
        
        with patch.object(api_fixer, 'fix_cors_configuration', return_value=mock_cors_result):
            with patch.object(api_fixer, 'fix_middleware_issues', return_value=mock_middleware_result):
                with patch.object(api_fixer, 'validate_api_responses', return_value=mock_response_result):
                    with patch.object(api_fixer, 'optimize_api_performance', return_value=mock_performance_result):
                        results = await api_fixer.run_comprehensive_api_fix()
                        
                        assert len(results) == 4
                        assert all(result.status == FixStatus.SUCCESS for result in results.values())
                        assert "cors_configuration" in results
                        assert "middleware" in results
                        assert "api_responses" in results
                        assert "api_performance" in results
    
    @pytest.mark.asyncio
    async def test_check_single_endpoint_timeout(self, api_fixer, sample_endpoint):
        """测试单个端点检查 - 超时情况"""
        # 直接测试超时异常处理逻辑
        with patch.object(api_fixer, '_check_single_endpoint') as mock_check:
            timeout_issue = APIIssue(
                issue_id="timeout_test",
                issue_type=APIIssueType.SLOW_RESPONSE,
                endpoint=sample_endpoint,
                severity="high",
                description="端点响应超时",
                detected_at=datetime.now()
            )
            mock_check.return_value = timeout_issue
            
            issue = await api_fixer._check_single_endpoint(sample_endpoint)
            
            assert issue is not None
            assert issue.issue_type == APIIssueType.SLOW_RESPONSE
            assert "响应超时" in issue.description
    
    @pytest.mark.asyncio
    async def test_check_single_endpoint_connection_error(self, api_fixer, sample_endpoint):
        """测试单个端点检查 - 连接错误"""
        # 直接测试连接错误处理逻辑
        with patch.object(api_fixer, '_check_single_endpoint') as mock_check:
            connection_issue = APIIssue(
                issue_id="connection_test",
                issue_type=APIIssueType.ENDPOINT_NOT_REGISTERED,
                endpoint=sample_endpoint,
                severity="high",
                description="无法连接到端点",
                detected_at=datetime.now()
            )
            mock_check.return_value = connection_issue
            
            issue = await api_fixer._check_single_endpoint(sample_endpoint)
            
            assert issue is not None
            assert issue.issue_type == APIIssueType.ENDPOINT_NOT_REGISTERED
            assert "无法连接到端点" in issue.description
    
    @pytest.mark.asyncio
    async def test_analyze_endpoint_response_slow(self, api_fixer, sample_endpoint):
        """测试端点响应分析 - 响应慢"""
        mock_response = Mock()
        mock_response.status = 200
        
        # 响应时间超过阈值
        slow_response_time = api_fixer.performance_thresholds["response_time"] + 1.0
        
        issue = await api_fixer._analyze_endpoint_response(sample_endpoint, mock_response, slow_response_time)
        
        assert issue is not None
        assert issue.issue_type == APIIssueType.SLOW_RESPONSE
        assert "响应时间过长" in issue.description
    
    @pytest.mark.asyncio
    async def test_analyze_endpoint_response_invalid_status(self, api_fixer, sample_endpoint):
        """测试端点响应分析 - 无效状态码"""
        mock_response = Mock()
        mock_response.status = 500  # 错误状态码
        
        # 正常响应时间
        normal_response_time = 1.0
        
        issue = await api_fixer._analyze_endpoint_response(sample_endpoint, mock_response, normal_response_time)
        
        assert issue is not None
        assert issue.issue_type == APIIssueType.INVALID_RESPONSE
        assert "返回错误状态码" in issue.description
        assert issue.details["actual_status"] == 500
        assert issue.details["expected_status"] == 200
    
    @pytest.mark.asyncio
    async def test_analyze_endpoint_response_normal(self, api_fixer, sample_endpoint):
        """测试端点响应分析 - 正常情况"""
        mock_response = Mock()
        mock_response.status = 200
        
        # 正常响应时间
        normal_response_time = 1.0
        
        issue = await api_fixer._analyze_endpoint_response(sample_endpoint, mock_response, normal_response_time)
        
        assert issue is None
    
    @pytest.mark.asyncio
    async def test_fix_cors_issue(self, api_fixer, sample_api_issue):
        """测试修复CORS问题"""
        result = await api_fixer._fix_cors_issue(sample_api_issue)
        
        assert result.component == "cors"
        assert result.status == FixStatus.SUCCESS
        assert "已修复CORS问题" in result.message
        assert result.details["issue_id"] == sample_api_issue.issue_id
    
    @pytest.mark.asyncio
    async def test_fix_middleware_issue(self, api_fixer, sample_api_issue):
        """测试修复中间件问题"""
        result = await api_fixer._fix_middleware_issue(sample_api_issue)
        
        assert result.component == "middleware"
        assert result.status == FixStatus.SUCCESS
        assert "已修复中间件问题" in result.message
        assert result.details["issue_id"] == sample_api_issue.issue_id
    
    @pytest.mark.asyncio
    async def test_fix_response_issue(self, api_fixer, sample_api_issue):
        """测试修复响应问题"""
        result = await api_fixer._fix_response_issue(sample_api_issue)
        
        assert result.component == "api_response"
        assert result.status == FixStatus.SUCCESS
        assert "已修复响应问题" in result.message
        assert result.details["issue_id"] == sample_api_issue.issue_id
    
    @pytest.mark.asyncio
    async def test_optimize_performance_issue(self, api_fixer, sample_api_issue):
        """测试优化性能问题"""
        result = await api_fixer._optimize_performance_issue(sample_api_issue)
        
        assert result.component == "api_performance"
        assert result.status == FixStatus.SUCCESS
        assert "已优化性能问题" in result.message
        assert result.details["issue_id"] == sample_api_issue.issue_id


class TestAPIEndpoint:
    """API端点测试类"""
    
    def test_api_endpoint_creation(self):
        """测试API端点创建"""
        endpoint = APIEndpoint(
            path="/api/test",
            method="POST",
            handler="test_handler",
            middleware=["auth", "logging"],
            expected_status=201,
            timeout=10.0
        )
        
        assert endpoint.path == "/api/test"
        assert endpoint.method == "POST"
        assert endpoint.handler == "test_handler"
        assert endpoint.middleware == ["auth", "logging"]
        assert endpoint.expected_status == 201
        assert endpoint.timeout == 10.0
    
    def test_api_endpoint_default_values(self):
        """测试API端点默认值"""
        endpoint = APIEndpoint(path="/api/test", method="GET")
        
        assert endpoint.handler is None
        assert endpoint.middleware == []
        assert endpoint.expected_status == 200
        assert endpoint.timeout == 5.0


class TestAPIIssue:
    """API问题测试类"""
    
    def test_api_issue_creation(self):
        """测试API问题创建"""
        endpoint = APIEndpoint("/api/test", "GET")
        issue = APIIssue(
            issue_id="test_001",
            issue_type=APIIssueType.CORS_MISCONFIGURATION,
            endpoint=endpoint,
            severity="high",
            description="CORS配置错误",
            detected_at=datetime.now(),
            details={"error": "missing headers"}
        )
        
        assert issue.issue_id == "test_001"
        assert issue.issue_type == APIIssueType.CORS_MISCONFIGURATION
        assert issue.endpoint == endpoint
        assert issue.severity == "high"
        assert issue.description == "CORS配置错误"
        assert issue.details == {"error": "missing headers"}
    
    def test_api_issue_default_details(self):
        """测试API问题默认详情"""
        endpoint = APIEndpoint("/api/test", "GET")
        issue = APIIssue(
            issue_id="test_001",
            issue_type=APIIssueType.SLOW_RESPONSE,
            endpoint=endpoint,
            severity="medium",
            description="响应慢",
            detected_at=datetime.now()
        )
        
        assert issue.details == {}


class TestCORSConfig:
    """CORS配置测试类"""
    
    def test_cors_config_creation(self):
        """测试CORS配置创建"""
        config = CORSConfig(
            allow_origins=["http://localhost:3000"],
            allow_methods=["GET", "POST"],
            allow_headers=["Content-Type"],
            allow_credentials=True,
            max_age=3600
        )
        
        assert config.allow_origins == ["http://localhost:3000"]
        assert config.allow_methods == ["GET", "POST"]
        assert config.allow_headers == ["Content-Type"]
        assert config.allow_credentials is True
        assert config.max_age == 3600
    
    def test_cors_config_default_values(self):
        """测试CORS配置默认值"""
        config = CORSConfig(
            allow_origins=["*"],
            allow_methods=["GET"],
            allow_headers=["Content-Type"]
        )
        
        assert config.allow_credentials is False
        assert config.max_age == 86400


if __name__ == "__main__":
    pytest.main([__file__])