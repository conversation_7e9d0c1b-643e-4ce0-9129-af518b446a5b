import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理功能测试模块

此模块测试配置文件的加载、验证和管理机制，确保配置系统正常工作。
"""

import os
import unittest
import tempfile
import shutil
import yaml
import json
from pathlib import Path
from unittest.mock import patch, mock_open

from tools.config.config_loader import ConfigLoader
from tools.file_organization.config_validator import ConfigValidator


class ConfigurationLoadingTest(unittest.TestCase):
    """配置加载测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.project_root = Path.cwd()
        self.config_loader = ConfigLoader()
        self.temp_dir = None
        
    def tearDown(self):
        """测试后清理"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            
    def create_temp_config_structure(self):
        """创建临时配置目录结构"""
        self.temp_dir = tempfile.mkdtemp()
        config_dir = Path(self.temp_dir) / 'config'
        config_dir.mkdir()
        
        # 创建环境目录
        env_dir = config_dir / 'environments'
        env_dir.mkdir()
        
        # 创建模板目录
        template_dir = config_dir / 'templates'
        template_dir.mkdir()
        
        return config_dir
        
    def test_main_config_loading(self):
        """测试主配置文件加载"""
        logger.info("开始测试主配置文件加载...")
        
        config_dir = self.create_temp_config_structure()
        
        # 创建测试配置文件
        main_config = {
            'system': {
                'name': '量化交易系统',
                'version': '1.0.0'
            },
            'database': {
                'type': 'sqlite',
                'path': 'data/db/trading_system.db'
            }
        }
        
        config_file = config_dir / 'config.yaml'
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(main_config, f, allow_unicode=True)
            
        # 测试加载配置
        loaded_config = self.config_loader.load_config(str(config_file))
        
        self.assertIsNotNone(loaded_config, "配置加载失败")
        self.assertEqual(loaded_config['system']['name'], '量化交易系统')
        self.assertEqual(loaded_config['database']['type'], 'sqlite')
        
        logger.info("✅ 主配置文件加载测试通过")
        
    def test_environment_specific_config_loading(self):
        """测试环境特定配置加载"""
        logger.info("开始测试环境特定配置加载...")
        
        config_dir = self.create_temp_config_structure()
        env_dir = config_dir / 'environments'
        
        # 创建开发环境配置
        dev_config = {
            'debug': True,
            'database': {
                'path': 'data/db/dev_trading_system.db'
            }
        }
        
        dev_config_file = env_dir / 'development.yaml'
        with open(dev_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(dev_config, f, allow_unicode=True)
            
        # 创建生产环境配置
        prod_config = {
            'debug': False,
            'database': {
                'path': 'data/db/prod_trading_system.db'
            }
        }
        
        prod_config_file = env_dir / 'production.yaml'
        with open(prod_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(prod_config, f, allow_unicode=True)
            
        # 测试加载不同环境配置
        dev_loaded = self.config_loader.load_environment_config('development', str(env_dir))
        prod_loaded = self.config_loader.load_environment_config('production', str(env_dir))
        
        self.assertTrue(dev_loaded['debug'], "开发环境配置加载错误")
        self.assertFalse(prod_loaded['debug'], "生产环境配置加载错误")
        
        logger.info("✅ 环境特定配置加载测试通过")
        
    def test_config_template_loading(self):
        """测试配置模板加载"""
        logger.info("开始测试配置模板加载...")
        
        config_dir = self.create_temp_config_structure()
        template_dir = config_dir / 'templates'
        
        # 创建API配置模板
        api_template = """
# API配置模板
api:
  host: ${API_HOST:localhost}
  port: ${API_PORT:8000}
  debug: ${DEBUG:false}
  
database:
  url: ${DATABASE_URL:sqlite:///data/db/trading_system.db}
"""
        
        template_file = template_dir / 'api.yaml.template'
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(api_template)
            
        # 测试模板加载
        template_content = self.config_loader.load_template(str(template_file))
        
        self.assertIsNotNone(template_content, "配置模板加载失败")
        self.assertIn('${API_HOST:localhost}', template_content)
        
        logger.info("✅ 配置模板加载测试通过")
        
    def test_config_merging(self):
        """测试配置合并功能"""
        logger.info("开始测试配置合并功能...")
        
        base_config = {
            'system': {
                'name': '量化交易系统',
                'version': '1.0.0'
            },
            'database': {
                'type': 'sqlite',
                'timeout': 30
            }
        }
        
        override_config = {
            'system': {
                'version': '1.1.0'
            },
            'database': {
                'timeout': 60
            },
            'api': {
                'port': 8080
            }
        }
        
        merged_config = self.config_loader.merge_configs(base_config, override_config)
        
        # 验证合并结果
        self.assertEqual(merged_config['system']['name'], '量化交易系统')
        self.assertEqual(merged_config['system']['version'], '1.1.0')  # 被覆盖
        self.assertEqual(merged_config['database']['type'], 'sqlite')
        self.assertEqual(merged_config['database']['timeout'], 60)  # 被覆盖
        self.assertEqual(merged_config['api']['port'], 8080)  # 新增
        
        logger.info("✅ 配置合并功能测试通过")


class ConfigurationValidationTest(unittest.TestCase):
    """配置验证测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.validator = ConfigValidator()
        
    def test_yaml_format_validation(self):
        """测试YAML格式验证"""
        logger.info("开始测试YAML格式验证...")
        
        # 有效的YAML
        valid_yaml = """
system:
  name: 量化交易系统
  version: 1.0.0
database:
  type: sqlite
"""
        
        # 无效的YAML
        invalid_yaml = """
system:
  name: 量化交易系统
  version: 1.0.0
database:
  type: sqlite
    invalid_indent: value
"""
        
        # 测试有效YAML
        is_valid, error = self.validator.validate_yaml_format(valid_yaml)
        self.assertTrue(is_valid, f"有效YAML验证失败: {error}")
        
        # 测试无效YAML
        is_valid, error = self.validator.validate_yaml_format(invalid_yaml)
        self.assertFalse(is_valid, "无效YAML应该验证失败")
        
        logger.info("✅ YAML格式验证测试通过")
        
    def test_json_format_validation(self):
        """测试JSON格式验证"""
        logger.info("开始测试JSON格式验证...")
        
        # 有效的JSON
        valid_json = """
{
  "system": {
    "name": "量化交易系统",
    "version": "1.0.0"
  },
  "database": {
    "type": "sqlite"
  }
}
"""
        
        # 无效的JSON
        invalid_json = """
{
  "system": {
    "name": "量化交易系统",
    "version": "1.0.0"
  },
  "database": {
    "type": "sqlite",
  }
}
"""
        
        # 测试有效JSON
        is_valid, error = self.validator.validate_json_format(valid_json)
        self.assertTrue(is_valid, f"有效JSON验证失败: {error}")
        
        # 测试无效JSON
        is_valid, error = self.validator.validate_json_format(invalid_json)
        self.assertFalse(is_valid, "无效JSON应该验证失败")
        
        logger.info("✅ JSON格式验证测试通过")
        
    def test_config_schema_validation(self):
        """测试配置架构验证"""
        logger.info("开始测试配置架构验证...")
        
        # 定义配置架构
        schema = {
            'type': 'object',
            'properties': {
                'system': {
                    'type': 'object',
                    'properties': {
                        'name': {'type': 'string'},
                        'version': {'type': 'string'}
                    },
                    'required': ['name', 'version']
                },
                'database': {
                    'type': 'object',
                    'properties': {
                        'type': {'type': 'string', 'enum': ['sqlite', 'postgresql', 'mysql']},
                        'path': {'type': 'string'}
                    },
                    'required': ['type']
                }
            },
            'required': ['system', 'database']
        }
        
        # 有效配置
        valid_config = {
            'system': {
                'name': '量化交易系统',
                'version': '1.0.0'
            },
            'database': {
                'type': 'sqlite',
                'path': 'data/db/trading_system.db'
            }
        }
        
        # 无效配置（缺少必需字段）
        invalid_config = {
            'system': {
                'name': '量化交易系统'
                # 缺少version字段
            },
            'database': {
                'type': 'sqlite'
            }
        }
        
        # 测试有效配置
        is_valid, errors = self.validator.validate_schema(valid_config, schema)
        self.assertTrue(is_valid, f"有效配置架构验证失败: {errors}")
        
        # 测试无效配置
        is_valid, errors = self.validator.validate_schema(invalid_config, schema)
        self.assertFalse(is_valid, "无效配置应该架构验证失败")
        
        logger.info("✅ 配置架构验证测试通过")
        
    def test_config_value_validation(self):
        """测试配置值验证"""
        logger.info("开始测试配置值验证...")
        
        # 测试端口号验证
        self.assertTrue(self.validator.validate_port(8080), "有效端口号验证失败")
        self.assertFalse(self.validator.validate_port(70000), "无效端口号应该验证失败")
        self.assertFalse(self.validator.validate_port(-1), "负数端口号应该验证失败")
        
        # 测试文件路径验证
        self.assertTrue(self.validator.validate_file_path("data/db/trading_system.db"), 
                       "有效文件路径验证失败")
        self.assertFalse(self.validator.validate_file_path(""), "空文件路径应该验证失败")
        
        # 测试URL验证
        self.assertTrue(self.validator.validate_url("http://localhost:8080"), 
                       "有效URL验证失败")
        self.assertFalse(self.validator.validate_url("invalid-url"), "无效URL应该验证失败")
        
        logger.info("✅ 配置值验证测试通过")


class ConfigurationManagementIntegrationTest(unittest.TestCase):
    """配置管理集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.project_root = Path.cwd()
        
    def test_real_config_files_loading(self):
        """测试真实配置文件加载"""
        logger.info("开始测试真实配置文件加载...")
        
        config_dir = self.project_root / 'config'
        if not config_dir.exists():
            self.skipTest("配置目录不存在，跳过真实配置文件测试")
            
        # 测试主配置文件
        main_config_file = config_dir / 'config.yaml'
        if main_config_file.exists():
            try:
                with open(main_config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                self.assertIsNotNone(config, "主配置文件加载失败")
                logger.info("✅ 主配置文件加载成功")
            except Exception as e:
                self.fail(f"主配置文件加载异常: {e}")
                
        # 测试数据源配置文件
        data_sources_file = config_dir / 'data_sources.yaml'
        if data_sources_file.exists():
            try:
                with open(data_sources_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                self.assertIsNotNone(config, "数据源配置文件加载失败")
                logger.info("✅ 数据源配置文件加载成功")
            except Exception as e:
                self.fail(f"数据源配置文件加载异常: {e}")
                
    def test_environment_config_switching(self):
        """测试环境配置切换"""
        logger.info("开始测试环境配置切换...")
        
        env_dir = self.project_root / 'config' / 'environments'
        if not env_dir.exists():
            self.skipTest("环境配置目录不存在，跳过环境配置测试")
            
        # 检查环境配置文件
        env_files = list(env_dir.glob('*.yaml'))
        if not env_files:
            self.skipTest("没有找到环境配置文件，跳过环境配置测试")
            
        for env_file in env_files:
            try:
                with open(env_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                self.assertIsNotNone(config, f"环境配置文件 {env_file.name} 加载失败")
                logger.info(f"✅ 环境配置文件 {env_file.name} 加载成功")
            except Exception as e:
                self.fail(f"环境配置文件 {env_file.name} 加载异常: {e}")


if __name__ == '__main__':
    logger.info("开始执行配置管理功能测试...")
    unittest.main(verbosity=2)