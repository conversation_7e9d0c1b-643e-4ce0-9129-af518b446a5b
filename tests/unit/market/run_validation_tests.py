#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目结构验证测试运行器

此脚本运行所有项目文件组织规范的验证测试，生成详细的测试报告。
"""

import sys
import unittest
import time
import json
from pathlib import Path
from io import StringIO
from typing import Dict, List, Any
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入测试模块
from tests.unit.file_organization.test_project_structure_validation import (
    ProjectStructureIntegrityTest, FileplacementRulesTest
)
from tests.unit.file_organization.test_configuration_management import (
    ConfigurationLoadingTest, ConfigurationValidationTest, ConfigurationManagementIntegrationTest
)
from tests.unit.file_organization.test_file_access_performance import FileAccessPerformanceTest


class ValidationTestRunner:
    """验证测试运行器"""
    
    def __init__(self):
        """初始化测试运行器"""
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
    def run_structure_integrity_tests(self) -> Dict[str, Any]:
        """运行结构完整性测试"""
        logger.info("\n" + "="*60)
        logger.info("运行项目结构完整性测试")
        logger.info("="*60)
        
        # 创建测试套件
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        # 添加结构完整性测试
        suite.addTests(loader.loadTestsFromTestCase(ProjectStructureIntegrityTest))
        suite.addTests(loader.loadTestsFromTestCase(FileplacementRulesTest))
        
        # 运行测试
        stream = StringIO()
        runner = unittest.TextTestRunner(stream=stream, verbosity=2)
        result = runner.run(suite)
        
        # 收集结果
        test_output = stream.getvalue()
        
        results = {
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'skipped': len(result.skipped),
            'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100 if result.testsRun > 0 else 0,
            'output': test_output,
            'failure_details': [{'test': str(test), 'error': error} for test, error in result.failures],
            'error_details': [{'test': str(test), 'error': error} for test, error in result.errors]
        }
        
        logger.info(f"结构完整性测试完成:")
        logger.info(f"  运行测试: {results['tests_run']}")
        logger.info(f"  失败: {results['failures']}")
        logger.info(f"  错误: {results['errors']}")
        logger.info(f"  跳过: {results['skipped']}")
        logger.info(f"  成功率: {results['success_rate']:.1f}%")
        
        return results
        
    def run_configuration_tests(self) -> Dict[str, Any]:
        """运行配置管理测试"""
        logger.info("\n" + "="*60)
        logger.info("运行配置管理功能测试")
        logger.info("="*60)
        
        # 创建测试套件
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        # 添加配置管理测试
        suite.addTests(loader.loadTestsFromTestCase(ConfigurationLoadingTest))
        suite.addTests(loader.loadTestsFromTestCase(ConfigurationValidationTest))
        suite.addTests(loader.loadTestsFromTestCase(ConfigurationManagementIntegrationTest))
        
        # 运行测试
        stream = StringIO()
        runner = unittest.TextTestRunner(stream=stream, verbosity=2)
        result = runner.run(suite)
        
        # 收集结果
        test_output = stream.getvalue()
        
        results = {
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'skipped': len(result.skipped),
            'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100 if result.testsRun > 0 else 0,
            'output': test_output,
            'failure_details': [{'test': str(test), 'error': error} for test, error in result.failures],
            'error_details': [{'test': str(test), 'error': error} for test, error in result.errors]
        }
        
        logger.info(f"配置管理测试完成:")
        logger.info(f"  运行测试: {results['tests_run']}")
        logger.info(f"  失败: {results['failures']}")
        logger.info(f"  错误: {results['errors']}")
        logger.info(f"  跳过: {results['skipped']}")
        logger.info(f"  成功率: {results['success_rate']:.1f}%")
        
        return results
        
    def run_performance_tests(self) -> Dict[str, Any]:
        """运行性能测试"""
        logger.info("\n" + "="*60)
        logger.info("运行文件访问性能测试")
        logger.info("="*60)
        
        # 创建测试套件
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        # 添加性能测试
        suite.addTests(loader.loadTestsFromTestCase(FileAccessPerformanceTest))
        
        # 运行测试
        stream = StringIO()
        runner = unittest.TextTestRunner(stream=stream, verbosity=2)
        result = runner.run(suite)
        
        # 收集结果
        test_output = stream.getvalue()
        
        results = {
            'tests_run': result.testsRun,
            'failures': len(result.failures),
            'errors': len(result.errors),
            'skipped': len(result.skipped),
            'success_rate': (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100 if result.testsRun > 0 else 0,
            'output': test_output,
            'failure_details': [{'test': str(test), 'error': error} for test, error in result.failures],
            'error_details': [{'test': str(test), 'error': error} for test, error in result.errors]
        }
        
        logger.info(f"性能测试完成:")
        logger.info(f"  运行测试: {results['tests_run']}")
        logger.info(f"  失败: {results['failures']}")
        logger.info(f"  错误: {results['errors']}")
        logger.info(f"  跳过: {results['skipped']}")
        logger.info(f"  成功率: {results['success_rate']:.1f}%")
        
        return results
        
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有验证测试"""
        logger.info("开始运行项目结构验证和测试系统...")
        logger.info(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.start_time = time.time()
        
        # 运行各类测试
        structure_results = self.run_structure_integrity_tests()
        config_results = self.run_configuration_tests()
        performance_results = self.run_performance_tests()
        
        self.end_time = time.time()
        total_time = self.end_time - self.start_time
        
        # 汇总结果
        total_tests = (structure_results['tests_run'] + 
                      config_results['tests_run'] + 
                      performance_results['tests_run'])
        
        total_failures = (structure_results['failures'] + 
                         config_results['failures'] + 
                         performance_results['failures'])
        
        total_errors = (structure_results['errors'] + 
                       config_results['errors'] + 
                       performance_results['errors'])
        
        total_skipped = (structure_results['skipped'] + 
                        config_results['skipped'] + 
                        performance_results['skipped'])
        
        overall_success_rate = (total_tests - total_failures - total_errors) / total_tests * 100 if total_tests > 0 else 0
        
        summary = {
            'total_time': total_time,
            'total_tests': total_tests,
            'total_failures': total_failures,
            'total_errors': total_errors,
            'total_skipped': total_skipped,
            'overall_success_rate': overall_success_rate,
            'structure_tests': structure_results,
            'configuration_tests': config_results,
            'performance_tests': performance_results
        }
        
        self.test_results = summary
        
        # 打印总结
        self.print_test_summary()
        
        return summary
        
    def print_test_summary(self):
        """打印测试总结"""
        logger.info("\n" + "="*80)
        logger.info("项目结构验证测试总结")
        logger.info("="*80)
        
        results = self.test_results
        
        logger.info(f"总执行时间: {results['total_time']:.2f} 秒")
        logger.info(f"总测试数量: {results['total_tests']}")
        logger.info(f"总失败数量: {results['total_failures']}")
        logger.info(f"总错误数量: {results['total_errors']}")
        logger.info(f"总跳过数量: {results['total_skipped']}")
        logger.info(f"总体成功率: {results['overall_success_rate']:.1f}%")
        
        logger.info("\n详细结果:")
        logger.info(f"  结构完整性测试: {results['structure_tests']['success_rate']:.1f}% "
              f"({results['structure_tests']['tests_run']} 个测试)")
        logger.info(f"  配置管理测试: {results['configuration_tests']['success_rate']:.1f}% "
              f"({results['configuration_tests']['tests_run']} 个测试)")
        logger.info(f"  性能测试: {results['performance_tests']['success_rate']:.1f}% "
              f"({results['performance_tests']['tests_run']} 个测试)")
        
        # 显示失败和错误详情
        if results['total_failures'] > 0 or results['total_errors'] > 0:
            logger.info("\n问题详情:")
            
            for test_type, test_results in [
                ('结构完整性', results['structure_tests']),
                ('配置管理', results['configuration_tests']),
                ('性能测试', results['performance_tests'])
            ]:
                if test_results['failures'] or test_results['errors']:
                    logger.info(f"\n{test_type}测试问题:")
                    
                    for failure in test_results['failure_details']:
                        logger.info(f"  失败: {failure['test']}")
                        logger.info(f"    {failure['error']}")
                        
                    for error in test_results['error_details']:
                        logger.info(f"  错误: {error['test']}")
                        logger.info(f"    {error['error']}")
        
        # 给出建议
        if results['overall_success_rate'] < 100:
            logger.info("\n建议:")
            if results['structure_tests']['success_rate'] < 100:
                logger.info("  - 检查项目目录结构是否完整")
                logger.info("  - 确认文件放置是否符合规范")
            if results['configuration_tests']['success_rate'] < 100:
                logger.info("  - 检查配置文件格式和内容")
                logger.info("  - 确认配置加载和验证机制")
            if results['performance_tests']['success_rate'] < 100:
                logger.info("  - 检查文件系统性能")
                logger.info("  - 考虑优化文件访问策略")
        else:
            logger.info("\n✅ 所有测试通过！项目文件组织规范实施良好。")
            
        logger.info("="*80)
        
    def save_test_report(self, report_path: str = None):
        """保存测试报告"""
        if not report_path:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            report_path = f"test_results/project_structure_validation_report_{timestamp}.json"
            
        report_file = Path(report_path)
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 准备报告数据
        report_data = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'test_results': self.test_results,
            'system_info': {
                'python_version': sys.version,
                'platform': sys.platform,
                'project_root': str(project_root)
            }
        }
        
        # 保存报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"\n测试报告已保存到: {report_path}")


def main():
    """主函数"""
    runner = ValidationTestRunner()
    
    try:
        # 运行所有测试
        results = runner.run_all_tests()
        
        # 保存测试报告
        runner.save_test_report()
        
        # 根据测试结果设置退出码
        if results['total_failures'] > 0 or results['total_errors'] > 0:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except KeyboardInterrupt:
        logger.info("\n测试被用户中断")
        sys.exit(130)
    except Exception as e:
        logger.info(f"\n测试运行过程中发生异常: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()