import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目结构验证测试模块

此模块包含项目文件组织规范的完整性验证测试，确保项目结构符合规范要求。
"""

import os
import stat
import unittest
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Set
import time
import json

from tools.file_organization.structure_validator import StructureValidator as ProjectStructureValidator
from tools.file_organization.rules_engine import RulesEngine as FileOrganizationRulesEngine


class ProjectStructureIntegrityTest(unittest.TestCase):
    """项目结构完整性测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.project_root = Path.cwd()
        self.validator = ProjectStructureValidator()
        self.required_directories = [
            '.kiro', '.kiro/settings', '.kiro/specs', '.kiro/steering',
            'config', 'config/environments', 'config/templates',
            'src', 'src/analysis', 'src/backtest', 'src/core', 'src/data',
            'src/indicators', 'src/live_trading', 'src/models', 'src/monitoring',
            'src/risk', 'src/strategies', 'src/utils',
            'web_ui', 'web_ui/frontend', 'web_ui/backend',
            'tests', 'tests/unit', 'tests/integration', 'tests/end_to_end',
            'docs', 'docs/getting-started', 'docs/api-reference', 'docs/developer-guide',
            'docs/localization', 'docs/web_ui',
            'data', 'data/cache', 'data/csv', 'data/db', 'data/exports',
            'examples', 'examples/getting_started', 'examples/api_usage',
            'examples/data_analysis', 'examples/trading_strategies',
            'examples/system_integration',
            'scripts', 'scripts/database', 'scripts/monitoring', 'scripts/web_ui',
            'tools', 'tools/development', 'tools/docs', 'tools/setup',
            'logs'
        ]
        
    def test_required_directories_exist(self):
        """测试必需目录是否存在"""
        logger.info("开始测试必需目录存在性...")
        
        missing_directories = []
        for directory in self.required_directories:
            dir_path = self.project_root / directory
            if not dir_path.exists():
                missing_directories.append(directory)
                
        if missing_directories:
            self.fail(f"缺少必需目录: {', '.join(missing_directories)}")
            
        logger.info(f"✅ 所有 {len(self.required_directories)} 个必需目录都存在")
        
    def test_directory_permissions(self):
        """测试目录权限设置"""
        logger.info("开始测试目录权限...")
        
        permission_errors = []
        for directory in self.required_directories:
            dir_path = self.project_root / directory
            if dir_path.exists():
                # 检查目录是否可读写
                if not os.access(dir_path, os.R_OK):
                    permission_errors.append(f"{directory}: 不可读")
                if not os.access(dir_path, os.W_OK):
                    permission_errors.append(f"{directory}: 不可写")
                    
        if permission_errors:
            self.fail(f"目录权限错误: {'; '.join(permission_errors)}")
            
        logger.info("✅ 所有目录权限设置正确")
        
    def test_directory_structure_integrity(self):
        """测试目录结构完整性"""
        logger.info("开始测试目录结构完整性...")
        
        # 验证核心模块目录结构
        src_modules = ['analysis', 'backtest', 'core', 'data', 'indicators',
                      'live_trading', 'models', 'monitoring', 'risk', 'strategies', 'utils']
        
        for module in src_modules:
            module_path = self.project_root / 'src' / module
            if module_path.exists():
                # 检查是否有__init__.py文件
                init_file = module_path / '__init__.py'
                self.assertTrue(init_file.exists(), 
                              f"模块 {module} 缺少 __init__.py 文件")
                              
        # 验证配置目录结构
        config_path = self.project_root / 'config'
        if config_path.exists():
            # 检查主配置文件
            main_config = config_path / 'config.yaml'
            self.assertTrue(main_config.exists(), "缺少主配置文件 config.yaml")
            
        logger.info("✅ 目录结构完整性验证通过")
        
    def test_gitkeep_files_in_empty_directories(self):
        """测试空目录中的.gitkeep文件"""
        logger.info("开始测试空目录的.gitkeep文件...")
        
        empty_dirs_needing_gitkeep = [
            'data/cache', 'data/exports', 'logs'
        ]
        
        missing_gitkeep = []
        for directory in empty_dirs_needing_gitkeep:
            dir_path = self.project_root / directory
            if dir_path.exists():
                gitkeep_file = dir_path / '.gitkeep'
                if not gitkeep_file.exists():
                    # 检查目录是否真的为空（除了可能的其他文件）
                    files = list(dir_path.iterdir())
                    if not files:  # 目录为空
                        missing_gitkeep.append(directory)
                        
        if missing_gitkeep:
            logger.info(f"⚠️  以下空目录缺少.gitkeep文件: {', '.join(missing_gitkeep)}")
        else:
            logger.info("✅ 空目录的.gitkeep文件检查通过")


class FileplacementRulesTest(unittest.TestCase):
    """文件放置规则测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.project_root = Path.cwd()
        self.rules_engine = FileOrganizationRulesEngine()
        
    def test_source_code_placement_rules(self):
        """测试源代码文件放置规则"""
        logger.info("开始测试源代码文件放置规则...")
        
        # 检查src目录下的Python文件
        src_path = self.project_root / 'src'
        if src_path.exists():
            misplaced_files = []
            
            for py_file in src_path.rglob('*.py'):
                relative_path = py_file.relative_to(src_path)
                
                # 检查文件是否在正确的模块目录中
                if len(relative_path.parts) > 1:
                    module_name = relative_path.parts[0]
                    expected_modules = ['analysis', 'backtest', 'core', 'data',
                                      'indicators', 'live_trading', 'models',
                                      'monitoring', 'risk', 'strategies', 'utils']
                    
                    if module_name not in expected_modules and module_name != '__pycache__':
                        misplaced_files.append(str(relative_path))
                        
            if misplaced_files:
                logger.info(f"⚠️  发现可能放置不当的源代码文件: {', '.join(misplaced_files)}")
            else:
                logger.info("✅ 源代码文件放置规则检查通过")
                
    def test_test_file_placement_rules(self):
        """测试测试文件放置规则"""
        logger.info("开始测试测试文件放置规则...")
        
        tests_path = self.project_root / 'tests'
        if tests_path.exists():
            misplaced_tests = []
            
            for test_file in tests_path.rglob('test_*.py'):
                relative_path = test_file.relative_to(tests_path)
                
                # 检查测试文件是否在正确的测试类型目录中
                if len(relative_path.parts) > 1:
                    test_type = relative_path.parts[0]
                    expected_types = ['unit', 'integration', 'end_to_end']
                    
                    if test_type not in expected_types:
                        misplaced_tests.append(str(relative_path))
                        
            if misplaced_tests:
                logger.info(f"⚠️  发现可能放置不当的测试文件: {', '.join(misplaced_tests)}")
            else:
                logger.info("✅ 测试文件放置规则检查通过")
                
    def test_config_file_placement_rules(self):
        """测试配置文件放置规则"""
        logger.info("开始测试配置文件放置规则...")
        
        config_extensions = ['.yaml', '.yml', '.json', '.toml', '.ini', '.env']
        misplaced_configs = []
        
        # 检查根目录下的配置文件
        for file_path in self.project_root.iterdir():
            if file_path.is_file() and file_path.suffix in config_extensions:
                # 某些配置文件允许在根目录
                allowed_root_configs = ['.env', 'requirements.txt', 'pyproject.toml']
                if file_path.name not in allowed_root_configs:
                    misplaced_configs.append(file_path.name)
                    
        if misplaced_configs:
            logger.info(f"⚠️  发现可能应该移到config/目录的配置文件: {', '.join(misplaced_configs)}")
        else:
            logger.info("✅ 配置文件放置规则检查通过")
            
    def test_documentation_placement_rules(self):
        """测试文档文件放置规则"""
        logger.info("开始测试文档文件放置规则...")
        
        doc_extensions = ['.md', '.rst', '.txt']
        misplaced_docs = []
        
        # 检查根目录下的文档文件
        for file_path in self.project_root.iterdir():
            if file_path.is_file() and file_path.suffix in doc_extensions:
                # 某些文档文件允许在根目录
                allowed_root_docs = ['README.md', 'USAGE.md', 'CHANGELOG.md', 
                                   'LICENSE.txt', 'CONTRIBUTING.md']
                if file_path.name not in allowed_root_docs:
                    misplaced_docs.append(file_path.name)
                    
        if misplaced_docs:
            logger.info(f"⚠️  发现可能应该移到docs/目录的文档文件: {', '.join(misplaced_docs)}")
        else:
            logger.info("✅ 文档文件放置规则检查通过")


if __name__ == '__main__':
    logger.info("开始执行项目结构验证测试...")
    unittest.main(verbosity=2)