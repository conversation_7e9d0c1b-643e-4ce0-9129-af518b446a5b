import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件访问性能测试模块

此模块测试大量文件访问和目录遍历的性能，确保文件组织系统在大规模项目中的性能表现。
"""

import os
import time
import unittest
import tempfile
import shutil
import statistics
from pathlib import Path
from typing import List, Dict
import concurrent.futures
import threading
import psutil
import gc


class FileAccessPerformanceTest(unittest.TestCase):
    """文件访问性能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.project_root = Path.cwd()
        self.temp_dir = None
        self.performance_results = {}
        
    def tearDown(self):
        """测试后清理"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            
    def create_test_file_structure(self, num_dirs: int = 100, files_per_dir: int = 50):
        """创建测试文件结构"""
        self.temp_dir = tempfile.mkdtemp()
        base_path = Path(self.temp_dir)
        
        logger.info(f"创建测试文件结构: {num_dirs} 个目录，每个目录 {files_per_dir} 个文件...")
        
        start_time = time.time()
        
        for i in range(num_dirs):
            dir_path = base_path / f"test_dir_{i:03d}"
            dir_path.mkdir()
            
            for j in range(files_per_dir):
                file_path = dir_path / f"test_file_{j:03d}.py"
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
测试文件 {i}_{j}
\"\"\"

def test_function_{i}_{j}():
    \"\"\"测试函数 {i}_{j}\"\"\"
    return "测试数据 {i}_{j}"

class TestClass_{i}_{j}:
    \"\"\"测试类 {i}_{j}\"\"\"
    
    def __init__(self):
        self.data = "测试数据 {i}_{j}"
        
    def get_data(self):
        return self.data
""")
        
        creation_time = time.time() - start_time
        logger.info(f"✅ 测试文件结构创建完成，耗时: {creation_time:.2f} 秒")
        
        return base_path
        
    def measure_execution_time(self, func, *args, **kwargs):
        """测量函数执行时间"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        return result, execution_time
        
    def test_directory_traversal_performance(self):
        """测试目录遍历性能"""
        logger.info("开始测试目录遍历性能...")
        
        # 创建测试文件结构
        test_path = self.create_test_file_structure(50, 30)
        
        # 测试不同的遍历方法
        traversal_methods = {
            'os.walk': self._traverse_with_os_walk,
            'pathlib.rglob': self._traverse_with_pathlib_rglob,
            'pathlib.iterdir_recursive': self._traverse_with_pathlib_iterdir
        }
        
        results = {}
        
        for method_name, method_func in traversal_methods.items():
            logger.info(f"测试遍历方法: {method_name}")
            
            # 多次测试取平均值
            times = []
            for i in range(3):
                _, exec_time = self.measure_execution_time(method_func, test_path)
                times.append(exec_time)
                
            avg_time = statistics.mean(times)
            results[method_name] = {
                'average_time': avg_time,
                'times': times,
                'std_dev': statistics.stdev(times) if len(times) > 1 else 0
            }
            
            logger.info(f"  平均耗时: {avg_time:.3f} 秒 (标准差: {results[method_name]['std_dev']:.3f})")
            
        # 找出最快的方法
        fastest_method = min(results.keys(), key=lambda k: results[k]['average_time'])
        logger.info(f"✅ 最快的遍历方法: {fastest_method} ({results[fastest_method]['average_time']:.3f} 秒)")
        
        # 性能要求：遍历1500个文件应该在1秒内完成
        for method_name, result in results.items():
            self.assertLess(result['average_time'], 2.0, 
                          f"遍历方法 {method_name} 性能不达标: {result['average_time']:.3f} 秒")
                          
        self.performance_results['directory_traversal'] = results
        
    def _traverse_with_os_walk(self, path):
        """使用os.walk遍历目录"""
        files = []
        for root, dirs, filenames in os.walk(path):
            for filename in filenames:
                files.append(os.path.join(root, filename))
        return files
        
    def _traverse_with_pathlib_rglob(self, path):
        """使用pathlib.rglob遍历目录"""
        return list(path.rglob('*'))
        
    def _traverse_with_pathlib_iterdir(self, path):
        """使用pathlib递归遍历目录"""
        def recursive_iterdir(p):
            for item in p.iterdir():
                yield item
                if item.is_dir():
                    yield from recursive_iterdir(item)
                    
        return list(recursive_iterdir(path))
        
    def test_file_reading_performance(self):
        """测试文件读取性能"""
        logger.info("开始测试文件读取性能...")
        
        # 创建测试文件结构
        test_path = self.create_test_file_structure(20, 25)
        
        # 获取所有Python文件
        py_files = list(test_path.rglob('*.py'))
        logger.info(f"找到 {len(py_files)} 个Python文件")
        
        # 测试单线程文件读取
        logger.info("测试单线程文件读取...")
        single_thread_files, single_thread_time = self.measure_execution_time(
            self._read_files_single_thread, py_files
        )
        
        logger.info(f"单线程读取耗时: {single_thread_time:.3f} 秒")
        
        # 测试多线程文件读取
        logger.info("测试多线程文件读取...")
        multi_thread_files, multi_thread_time = self.measure_execution_time(
            self._read_files_multi_thread, py_files, max_workers=4
        )
        
        logger.info(f"多线程读取耗时: {multi_thread_time:.3f} 秒")
        
        # 验证读取结果一致性
        self.assertEqual(len(single_thread_files), len(multi_thread_files), 
                        "单线程和多线程读取的文件数量不一致")
        
        # 计算性能提升
        if single_thread_time > 0:
            speedup = single_thread_time / multi_thread_time
            logger.info(f"多线程性能提升: {speedup:.2f}x")
            
        # 性能要求：读取500个文件应该在3秒内完成
        self.assertLess(single_thread_time, 5.0, 
                       f"单线程文件读取性能不达标: {single_thread_time:.3f} 秒")
        self.assertLess(multi_thread_time, 3.0, 
                       f"多线程文件读取性能不达标: {multi_thread_time:.3f} 秒")
                       
        self.performance_results['file_reading'] = {
            'single_thread_time': single_thread_time,
            'multi_thread_time': multi_thread_time,
            'speedup': speedup if single_thread_time > 0 else 0,
            'files_count': len(py_files)
        }
        
    def _read_files_single_thread(self, files):
        """单线程读取文件"""
        contents = []
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    contents.append((str(file_path), len(content)))
            except Exception as e:
                logger.info(f"读取文件失败 {file_path}: {e}")
        return contents
        
    def _read_files_multi_thread(self, files, max_workers=4):
        """多线程读取文件"""
        contents = []
        
        def read_single_file(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    return (str(file_path), len(content))
            except Exception as e:
                logger.info(f"读取文件失败 {file_path}: {e}")
                return None
                
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {executor.submit(read_single_file, file_path): file_path 
                             for file_path in files}
            
            for future in concurrent.futures.as_completed(future_to_file):
                result = future.result()
                if result:
                    contents.append(result)
                    
        return contents
        
    def test_memory_usage_during_traversal(self):
        """测试遍历过程中的内存使用"""
        logger.info("开始测试遍历过程中的内存使用...")
        
        # 创建较大的测试文件结构
        test_path = self.create_test_file_structure(100, 50)
        
        # 获取初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        logger.info(f"初始内存使用: {initial_memory:.2f} MB")
        
        # 执行遍历并监控内存
        memory_samples = []
        
        def monitor_memory():
            while not stop_monitoring:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_samples.append(current_memory)
                time.sleep(0.1)
                
        stop_monitoring = False
        monitor_thread = threading.Thread(target=monitor_memory)
        monitor_thread.start()
        
        try:
            # 执行遍历
            files = list(test_path.rglob('*'))
            logger.info(f"遍历完成，找到 {len(files)} 个文件/目录")
            
        finally:
            stop_monitoring = True
            monitor_thread.join()
            
        # 分析内存使用
        if memory_samples:
            max_memory = max(memory_samples)
            avg_memory = statistics.mean(memory_samples)
            memory_increase = max_memory - initial_memory
            
            logger.info(f"最大内存使用: {max_memory:.2f} MB")
            logger.info(f"平均内存使用: {avg_memory:.2f} MB")
            logger.info(f"内存增长: {memory_increase:.2f} MB")
            
            # 内存使用要求：遍历5000个文件内存增长不应超过100MB
            self.assertLess(memory_increase, 200.0, 
                          f"内存使用过多: {memory_increase:.2f} MB")
                          
            self.performance_results['memory_usage'] = {
                'initial_memory': initial_memory,
                'max_memory': max_memory,
                'avg_memory': avg_memory,
                'memory_increase': memory_increase,
                'files_count': len(files)
            }
            
        # 强制垃圾回收
        gc.collect()
        
    def test_concurrent_file_access(self):
        """测试并发文件访问性能"""
        logger.info("开始测试并发文件访问性能...")
        
        # 创建测试文件结构
        test_path = self.create_test_file_structure(30, 20)
        py_files = list(test_path.rglob('*.py'))
        
        # 测试不同并发级别
        concurrency_levels = [1, 2, 4, 8]
        results = {}
        
        for workers in concurrency_levels:
            logger.info(f"测试并发级别: {workers} 个工作线程")
            
            # 多次测试取平均值
            times = []
            for i in range(3):
                _, exec_time = self.measure_execution_time(
                    self._concurrent_file_access, py_files, workers
                )
                times.append(exec_time)
                
            avg_time = statistics.mean(times)
            results[workers] = {
                'average_time': avg_time,
                'times': times,
                'throughput': len(py_files) / avg_time  # 文件/秒
            }
            
            logger.info(f"  平均耗时: {avg_time:.3f} 秒, 吞吐量: {results[workers]['throughput']:.1f} 文件/秒")
            
        # 找出最佳并发级别
        best_workers = max(results.keys(), key=lambda k: results[k]['throughput'])
        logger.info(f"✅ 最佳并发级别: {best_workers} 个工作线程 "
              f"(吞吐量: {results[best_workers]['throughput']:.1f} 文件/秒)")
              
        self.performance_results['concurrent_access'] = results
        
    def _concurrent_file_access(self, files, max_workers):
        """并发文件访问"""
        results = []
        
        def process_file(file_path):
            try:
                # 模拟文件处理：读取、分析、统计
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 简单的内容分析
                lines = content.count('\n')
                chars = len(content)
                words = len(content.split())
                
                return {
                    'file': str(file_path),
                    'lines': lines,
                    'chars': chars,
                    'words': words
                }
            except Exception as e:
                return {'file': str(file_path), 'error': str(e)}
                
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {executor.submit(process_file, file_path): file_path 
                             for file_path in files}
            
            for future in concurrent.futures.as_completed(future_to_file):
                result = future.result()
                results.append(result)
                
        return results
        
    def test_large_directory_handling(self):
        """测试大目录处理性能"""
        logger.info("开始测试大目录处理性能...")
        
        # 创建包含大量文件的单个目录
        self.temp_dir = tempfile.mkdtemp()
        large_dir = Path(self.temp_dir) / "large_directory"
        large_dir.mkdir()
        
        num_files = 1000
        logger.info(f"创建包含 {num_files} 个文件的大目录...")
        
        # 创建大量文件
        start_time = time.time()
        for i in range(num_files):
            file_path = large_dir / f"file_{i:04d}.txt"
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"这是文件 {i} 的内容\n" * 10)
                
        creation_time = time.time() - start_time
        logger.info(f"文件创建耗时: {creation_time:.2f} 秒")
        
        # 测试目录列表性能
        list_times = []
        for i in range(5):
            start_time = time.time()
            files = list(large_dir.iterdir())
            list_time = time.time() - start_time
            list_times.append(list_time)
            
        avg_list_time = statistics.mean(list_times)
        logger.info(f"目录列表平均耗时: {avg_list_time:.3f} 秒")
        
        # 测试文件统计性能
        start_time = time.time()
        file_count = len(list(large_dir.iterdir()))
        total_size = sum(f.stat().st_size for f in large_dir.iterdir() if f.is_file())
        stat_time = time.time() - start_time
        
        logger.info(f"文件统计耗时: {stat_time:.3f} 秒")
        logger.info(f"统计结果: {file_count} 个文件, 总大小: {total_size} 字节")
        
        # 性能要求
        self.assertLess(avg_list_time, 0.5, f"大目录列表性能不达标: {avg_list_time:.3f} 秒")
        self.assertLess(stat_time, 1.0, f"文件统计性能不达标: {stat_time:.3f} 秒")
        
        self.performance_results['large_directory'] = {
            'files_count': num_files,
            'creation_time': creation_time,
            'avg_list_time': avg_list_time,
            'stat_time': stat_time,
            'total_size': total_size
        }
        
    def test_performance_summary(self):
        """输出性能测试总结"""
        if not self.performance_results:
            self.skipTest("没有性能测试结果可供总结")
            
        logger.info("\n" + "="*60)
        logger.info("性能测试总结")
        logger.info("="*60)
        
        for test_name, results in self.performance_results.items():
            logger.info(f"\n{test_name.upper()}:")
            if isinstance(results, dict):
                for key, value in results.items():
                    if isinstance(value, (int, float)):
                        if 'time' in key.lower():
                            logger.info(f"  {key}: {value:.3f} 秒")
                        elif 'memory' in key.lower():
                            logger.info(f"  {key}: {value:.2f} MB")
                        else:
                            logger.info(f"  {key}: {value}")
                    else:
                        logger.info(f"  {key}: {value}")
                        
        logger.info("\n" + "="*60)


if __name__ == '__main__':
    logger.info("开始执行文件访问性能测试...")
    unittest.main(verbosity=2)