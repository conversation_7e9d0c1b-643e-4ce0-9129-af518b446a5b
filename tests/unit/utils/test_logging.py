"""
logging模块的单元测试

测试logging模块的核心功能和边界条件，特别是LogDeduplicator类的功能。
"""

import pytest
import unittest
import time
import logging
import os
from unittest.mock import Mock, patch, MagicMock
from io import StringIO

try:
    from src.common.utils.logging import LogDeduplicator, DeduplicatingHandler, ChineseLogger, LoggingOptimizer, ChineseFormatter, LogLevelManager
except ImportError as e:
    pytest.skip(f"无法导入模块 src.utils.logging: {e}", allow_module_level=True)


class TestLogDeduplicator(unittest.TestCase):
    """
    LogDeduplicator类测试
    """
    
    def setUp(self):
        """测试前置设置"""
        self.deduplicator = LogDeduplicator(window_seconds=5, max_cache_size=10)
    
    def tearDown(self):
        """测试后置清理"""
        self.deduplicator.clear_cache()
    
    def test_init(self):
        """测试初始化"""
        dedup = LogDeduplicator(window_seconds=30, max_cache_size=500)
        self.assertEqual(dedup.window_seconds, 30)
        self.assertEqual(dedup.max_cache_size, 500)
        self.assertEqual(len(dedup.message_cache), 0)
    
    def test_generate_message_hash(self):
        """测试消息哈希生成"""
        hash1 = self.deduplicator._generate_message_hash("test message", "INFO", "test_logger")
        hash2 = self.deduplicator._generate_message_hash("test message", "INFO", "test_logger")
        hash3 = self.deduplicator._generate_message_hash("different message", "INFO", "test_logger")
        hash4 = self.deduplicator._generate_message_hash("test message", "ERROR", "test_logger")
        
        # 相同消息应该生成相同哈希
        self.assertEqual(hash1, hash2)
        
        # 不同消息应该生成不同哈希
        self.assertNotEqual(hash1, hash3)
        self.assertNotEqual(hash1, hash4)
    
    def test_should_log_new_message(self):
        """测试新消息应该被记录"""
        should_log, count = self.deduplicator.should_log("new message", "INFO", "test_logger")
        
        self.assertTrue(should_log)
        self.assertEqual(count, 1)
        self.assertEqual(len(self.deduplicator.message_cache), 1)
    
    def test_should_log_duplicate_message(self):
        """测试重复消息的处理"""
        # 第一次记录
        should_log1, count1 = self.deduplicator.should_log("duplicate message", "INFO", "test_logger")
        self.assertTrue(should_log1)
        self.assertEqual(count1, 1)
        
        # 第2-9次记录（应该被跳过）
        for i in range(8):  # 2-9次
            should_log, count = self.deduplicator.should_log("duplicate message", "INFO", "test_logger")
            self.assertFalse(should_log)
            self.assertEqual(count, i + 2)
        
        # 第10次记录（应该被记录）
        should_log10, count10 = self.deduplicator.should_log("duplicate message", "INFO", "test_logger")
        self.assertTrue(should_log10)
        self.assertEqual(count10, 10)
    
    def test_should_log_error_messages_always(self):
        """测试ERROR和CRITICAL级别消息总是被记录"""
        # ERROR级别
        for i in range(5):
            should_log, count = self.deduplicator.should_log("error message", "ERROR", "test_logger")
            self.assertTrue(should_log)
            self.assertEqual(count, i + 1)
        
        # CRITICAL级别
        for i in range(5):
            should_log, count = self.deduplicator.should_log("critical message", "CRITICAL", "test_logger")
            self.assertTrue(should_log)
            self.assertEqual(count, i + 1)
    
    def test_time_window_expiry(self):
        """测试时间窗口过期"""
        # 记录消息
        should_log1, count1 = self.deduplicator.should_log("time test", "INFO", "test_logger")
        self.assertTrue(should_log1)
        self.assertEqual(count1, 1)
        
        # 直接修改缓存中的时间戳来模拟时间过去
        msg_hash = self.deduplicator._generate_message_hash("time test", "INFO", "test_logger")
        old_time = self.deduplicator.message_cache[msg_hash]['first_seen']
        self.deduplicator.message_cache[msg_hash]['first_seen'] = old_time - 10  # 10秒前
        self.deduplicator.message_cache[msg_hash]['last_seen'] = old_time - 10
        
        # 再次记录相同消息，应该被视为新消息
        should_log2, count2 = self.deduplicator.should_log("time test", "INFO", "test_logger")
        self.assertTrue(should_log2)
        self.assertEqual(count2, 1)  # 重置为1
    
    def test_cleanup_expired_messages(self):
        """测试过期消息清理"""
        # 添加一些消息
        self.deduplicator.should_log("message1", "INFO", "test_logger")
        self.deduplicator.should_log("message2", "INFO", "test_logger")
        self.assertEqual(len(self.deduplicator.message_cache), 2)
        
        # 直接修改缓存中的时间戳来模拟时间过去
        current_time = time.time()
        for info in self.deduplicator.message_cache.values():
            info['last_seen'] = current_time - 10  # 10秒前
        
        # 触发清理
        self.deduplicator._cleanup_expired_messages()
        
        # 过期消息应该被清理
        self.assertEqual(len(self.deduplicator.message_cache), 0)
    
    def test_max_cache_size_limit(self):
        """测试最大缓存大小限制"""
        # 添加超过最大缓存大小的消息
        for i in range(15):  # 超过max_cache_size=10
            self.deduplicator.should_log(f"message{i}", "INFO", "test_logger")
        
        # 缓存大小应该被限制
        self.assertLessEqual(len(self.deduplicator.message_cache), self.deduplicator.max_cache_size)
    
    def test_get_stats(self):
        """测试统计信息获取"""
        # 添加一些消息
        self.deduplicator.should_log("message1", "INFO", "test_logger")
        self.deduplicator.should_log("message1", "INFO", "test_logger")  # 重复
        self.deduplicator.should_log("message2", "INFO", "test_logger")
        
        stats = self.deduplicator.get_stats()
        
        self.assertIn('active_messages', stats)
        self.assertIn('total_cached_messages', stats)
        self.assertIn('total_message_count', stats)
        self.assertIn('window_seconds', stats)
        self.assertIn('max_cache_size', stats)
        
        self.assertEqual(stats['active_messages'], 2)  # 2个不同的消息
        self.assertEqual(stats['total_cached_messages'], 2)
        self.assertEqual(stats['total_message_count'], 3)  # 总共3次记录
    
    def test_clear_cache(self):
        """测试缓存清理"""
        # 添加一些消息
        self.deduplicator.should_log("message1", "INFO", "test_logger")
        self.deduplicator.should_log("message2", "INFO", "test_logger")
        self.assertEqual(len(self.deduplicator.message_cache), 2)
        
        # 清理缓存
        self.deduplicator.clear_cache()
        self.assertEqual(len(self.deduplicator.message_cache), 0)
        self.assertEqual(self.deduplicator._cleanup_counter, 0)
    
    def test_thread_safety(self):
        """测试线程安全性"""
        import threading
        import concurrent.futures
        
        results = []
        
        def log_messages():
            for i in range(10):
                should_log, count = self.deduplicator.should_log("thread test", "INFO", "test_logger")
                results.append((should_log, count))
        
        # 创建多个线程同时访问
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(log_messages) for _ in range(5)]
            concurrent.futures.wait(futures)
        
        # 验证结果
        self.assertEqual(len(results), 50)  # 5个线程 * 10次调用
        
        # 第一次调用应该返回True
        first_calls = [r for r in results if r[1] == 1]
        self.assertEqual(len(first_calls), 1)  # 只有一个第一次调用


class TestDeduplicatingHandler(unittest.TestCase):
    """
    DeduplicatingHandler类测试
    """
    
    def setUp(self):
        """测试前置设置"""
        self.deduplicator = LogDeduplicator(window_seconds=5)
        self.stream = StringIO()
        self.base_handler = logging.StreamHandler(self.stream)
        self.base_handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))
        self.handler = DeduplicatingHandler(self.base_handler, self.deduplicator)
    
    def tearDown(self):
        """测试后置清理"""
        self.handler.close()
        self.deduplicator.clear_cache()
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.handler.wrapped_handler, self.base_handler)
        self.assertEqual(self.handler.deduplicator, self.deduplicator)
        self.assertEqual(self.handler.level, self.base_handler.level)
    
    def test_emit_new_message(self):
        """测试发送新消息"""
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg="test message",
            args=(),
            exc_info=None
        )
        
        self.handler.emit(record)
        
        output = self.stream.getvalue()
        self.assertIn("test message", output)
    
    def test_emit_duplicate_message(self):
        """测试发送重复消息"""
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg="duplicate message",
            args=(),
            exc_info=None
        )
        
        # 第一次发送
        self.handler.emit(record)
        first_output = self.stream.getvalue()
        
        # 第二次发送（应该被跳过）
        self.handler.emit(record)
        second_output = self.stream.getvalue()
        
        # 输出应该相同（第二次没有新输出）
        self.assertEqual(first_output, second_output)
    
    def test_emit_with_repeat_count(self):
        """测试带重复计数的消息发送"""
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="",
            lineno=0,
            msg="repeat test",
            args=(),
            exc_info=None
        )
        
        # 发送10次以触发重复计数显示
        for i in range(10):
            self.handler.emit(record)
        
        output = self.stream.getvalue()
        self.assertIn("重复 10 次", output)


class TestLogging(unittest.TestCase):
    """
    logging模块其他功能测试类
    """
    
    def setUp(self):
        """测试前置设置"""
        pass
    
    def tearDown(self):
        """测试后置清理"""
        pass
    
    def test_chinese_formatter_level_mapping(self):
        """测试中文格式化器的级别映射"""
        formatter = ChineseFormatter()
        
        # 测试级别映射
        self.assertEqual(formatter.LEVEL_MAPPING['DEBUG'], '调试')
        self.assertEqual(formatter.LEVEL_MAPPING['INFO'], '信息')
        self.assertEqual(formatter.LEVEL_MAPPING['WARNING'], '警告')
        self.assertEqual(formatter.LEVEL_MAPPING['ERROR'], '错误')
        self.assertEqual(formatter.LEVEL_MAPPING['CRITICAL'], '严重')


class TestLoggingOptimizer(unittest.TestCase):
    """
    LoggingOptimizer类测试
    """
    
    def setUp(self):
        """测试前置设置"""
        self.optimizer = LoggingOptimizer()
    
    def tearDown(self):
        """测试后置清理"""
        self.optimizer.restore_original_levels()
    
    def test_init(self):
        """测试初始化"""
        optimizer = LoggingOptimizer()
        self.assertEqual(len(optimizer._original_levels), 0)
        self.assertEqual(len(optimizer._optimized_loggers), 0)
    
    def test_optimize_watchfiles_logging(self):
        """测试watchfiles日志优化"""
        # 优化前获取一个watchfiles日志器的级别
        test_logger = logging.getLogger('watchfiles')
        original_level = test_logger.level
        
        # 执行优化
        self.optimizer.optimize_watchfiles_logging("ERROR")
        
        # 验证级别已更改
        self.assertEqual(test_logger.level, logging.ERROR)
        
        # 验证原始级别被保存
        self.assertIn('watchfiles', self.optimizer._original_levels)
        self.assertEqual(self.optimizer._original_levels['watchfiles'], original_level)
        
        # 验证日志器被标记为已优化
        self.assertTrue(self.optimizer.is_logger_optimized('watchfiles'))
    
    def test_optimize_data_adapter_logging(self):
        """测试数据适配器日志优化"""
        # 优化前获取一个数据适配器日志器的级别
        test_logger = logging.getLogger('data_adapter')
        original_level = test_logger.level
        
        # 执行优化
        self.optimizer.optimize_data_adapter_logging("INFO")
        
        # 验证级别已更改
        self.assertEqual(test_logger.level, logging.INFO)
        
        # 验证原始级别被保存
        self.assertIn('data_adapter', self.optimizer._original_levels)
        self.assertEqual(self.optimizer._original_levels['data_adapter'], original_level)
    
    def test_optimize_third_party_logging(self):
        """测试第三方库日志优化"""
        # 优化前获取一个第三方库日志器的级别
        test_logger = logging.getLogger('urllib3')
        original_level = test_logger.level
        
        # 执行优化
        self.optimizer.optimize_third_party_logging()
        
        # 验证级别已更改为WARNING
        self.assertEqual(test_logger.level, logging.WARNING)
        
        # 验证原始级别被保存
        self.assertIn('urllib3', self.optimizer._original_levels)
        self.assertEqual(self.optimizer._original_levels['urllib3'], original_level)
    
    def test_set_logger_level(self):
        """测试设置指定日志器级别"""
        test_logger = logging.getLogger('test_custom_logger')
        original_level = test_logger.level
        
        # 设置级别
        self.optimizer.set_logger_level('test_custom_logger', 'CRITICAL')
        
        # 验证级别已更改
        self.assertEqual(test_logger.level, logging.CRITICAL)
        
        # 验证原始级别被保存
        self.assertIn('test_custom_logger', self.optimizer._original_levels)
        self.assertEqual(self.optimizer._original_levels['test_custom_logger'], original_level)
    
    def test_apply_all_optimizations(self):
        """测试应用所有优化"""
        # 执行所有优化
        self.optimizer.apply_all_optimizations()
        
        # 验证各类日志器都被优化
        self.assertTrue(self.optimizer.is_logger_optimized('watchfiles'))
        self.assertTrue(self.optimizer.is_logger_optimized('data_adapter'))
        self.assertTrue(self.optimizer.is_logger_optimized('urllib3'))
        
        # 验证优化数量大于0
        self.assertGreater(len(self.optimizer._optimized_loggers), 0)
    
    def test_restore_original_levels(self):
        """测试恢复原始级别"""
        # 先进行一些优化
        test_logger = logging.getLogger('test_restore_logger')
        original_level = test_logger.level
        
        self.optimizer.set_logger_level('test_restore_logger', 'ERROR')
        self.assertEqual(test_logger.level, logging.ERROR)
        
        # 恢复原始级别
        self.optimizer.restore_original_levels()
        
        # 验证级别已恢复
        self.assertEqual(test_logger.level, original_level)
        
        # 验证内部状态已清理
        self.assertEqual(len(self.optimizer._optimized_loggers), 0)
        self.assertEqual(len(self.optimizer._original_levels), 0)
    
    def test_get_optimization_status(self):
        """测试获取优化状态"""
        # 进行一些优化
        self.optimizer.set_logger_level('test_status_logger', 'WARNING')
        
        # 获取状态
        status = self.optimizer.get_optimization_status()
        
        # 验证状态信息
        self.assertIn('optimized_loggers', status)
        self.assertIn('total_optimized', status)
        self.assertIn('original_levels', status)
        self.assertIn('current_levels', status)
        
        self.assertIn('test_status_logger', status['optimized_loggers'])
        self.assertEqual(status['total_optimized'], 1)
        self.assertIn('test_status_logger', status['current_levels'])
        self.assertEqual(status['current_levels']['test_status_logger'], 'WARNING')
    
    def test_is_logger_optimized(self):
        """测试检查日志器是否已优化"""
        # 未优化时应返回False
        self.assertFalse(self.optimizer.is_logger_optimized('test_check_logger'))
        
        # 优化后应返回True
        self.optimizer.set_logger_level('test_check_logger', 'INFO')
        self.assertTrue(self.optimizer.is_logger_optimized('test_check_logger'))


class TestLogLevelManager(unittest.TestCase):
    """
    LogLevelManager类测试
    """
    
    def setUp(self):
        """测试前置设置"""
        self.test_config_file = "tests/fixtures/test_logging_config.json"
        self.manager = LogLevelManager()
    
    def tearDown(self):
        """测试后置清理"""
        # 清理测试配置文件
        if os.path.exists(self.test_config_file):
            os.remove(self.test_config_file)
    
    def test_init_without_config(self):
        """测试无配置文件初始化"""
        manager = LogLevelManager()
        self.assertIsNone(manager.config_file)
        self.assertEqual(manager._config, manager._default_config)
    
    def test_init_with_config(self):
        """测试带配置文件初始化"""
        config_file = "tests/fixtures/logging_config.json"
        manager = LogLevelManager(config_file)
        self.assertEqual(manager.config_file, config_file)
        
        # 验证配置被加载
        self.assertIn('test_logger', manager._config)
        self.assertEqual(manager._config['test_logger'], 'WARNING')
    
    def test_set_level(self):
        """测试设置日志级别"""
        test_logger = logging.getLogger('test_set_level_logger')
        original_level = test_logger.level
        
        # 设置级别
        self.manager.set_level('test_set_level_logger', 'ERROR', save_to_config=False)
        
        # 验证级别已更改
        self.assertEqual(test_logger.level, logging.ERROR)
        
        # 验证历史记录
        history = self.manager.get_history()
        self.assertGreater(len(history), 0)
        self.assertEqual(history[-1]['logger_name'], 'test_set_level_logger')
        self.assertEqual(history[-1]['new_level'], 'ERROR')
    
    def test_get_level(self):
        """测试获取日志级别"""
        test_logger = logging.getLogger('test_get_level_logger')
        test_logger.setLevel(logging.WARNING)
        
        level = self.manager.get_level('test_get_level_logger')
        self.assertEqual(level, 'WARNING')
    
    def test_apply_config(self):
        """测试应用配置"""
        # 设置一些配置
        self.manager._config['test_apply_logger'] = 'CRITICAL'
        
        # 应用配置
        self.manager.apply_config()
        
        # 验证级别已设置
        test_logger = logging.getLogger('test_apply_logger')
        self.assertEqual(test_logger.level, logging.CRITICAL)
    
    def test_reset_to_defaults(self):
        """测试重置到默认值"""
        # 先设置一些非默认值
        self.manager.set_level('system', 'ERROR', save_to_config=False)
        
        # 重置到默认值
        self.manager.reset_to_defaults()
        
        # 验证已重置
        system_logger = logging.getLogger('system')
        self.assertEqual(system_logger.level, logging.INFO)  # 默认值
        
        # 验证历史记录
        history = self.manager.get_history()
        self.assertTrue(any(h.get('action') == 'reset_to_defaults' for h in history))
    
    def test_bulk_set_levels(self):
        """测试批量设置级别"""
        level_mapping = {
            'bulk_test_1': 'DEBUG',
            'bulk_test_2': 'WARNING',
            'bulk_test_3': 'ERROR'
        }
        
        # 批量设置
        self.manager.bulk_set_levels(level_mapping, save_to_config=False)
        
        # 验证所有级别都已设置
        for logger_name, expected_level in level_mapping.items():
            logger = logging.getLogger(logger_name)
            self.assertEqual(logger.level, getattr(logging, expected_level))
    
    def test_get_all_levels(self):
        """测试获取所有级别"""
        # 设置一些级别
        self.manager.set_level('all_levels_test_1', 'DEBUG', save_to_config=False)
        self.manager.set_level('all_levels_test_2', 'WARNING', save_to_config=False)
        
        # 获取所有级别
        all_levels = self.manager.get_all_levels()
        
        # 验证包含设置的级别
        self.assertIn('all_levels_test_1', all_levels)
        self.assertIn('all_levels_test_2', all_levels)
        self.assertEqual(all_levels['all_levels_test_1'], 'DEBUG')
        self.assertEqual(all_levels['all_levels_test_2'], 'WARNING')
    
    def test_get_history(self):
        """测试获取历史记录"""
        # 执行一些操作
        self.manager.set_level('history_test', 'DEBUG', save_to_config=False)
        self.manager.set_level('history_test', 'WARNING', save_to_config=False)
        
        # 获取历史
        history = self.manager.get_history()
        self.assertGreaterEqual(len(history), 2)
        
        # 测试限制数量
        limited_history = self.manager.get_history(limit=1)
        self.assertEqual(len(limited_history), 1)
    
    def test_clear_history(self):
        """测试清空历史"""
        # 执行一些操作
        self.manager.set_level('clear_history_test', 'DEBUG', save_to_config=False)
        self.assertGreater(len(self.manager.get_history()), 0)
        
        # 清空历史
        self.manager.clear_history()
        self.assertEqual(len(self.manager.get_history()), 0)
    
    def test_get_config(self):
        """测试获取配置"""
        config = self.manager.get_config()
        self.assertIsInstance(config, dict)
        self.assertIn('system', config)
    
    def test_update_config(self):
        """测试更新配置"""
        new_config = {'update_test_logger': 'CRITICAL'}
        
        # 更新配置
        self.manager.update_config(new_config, apply_immediately=True)
        
        # 验证配置已更新
        self.assertIn('update_test_logger', self.manager._config)
        self.assertEqual(self.manager._config['update_test_logger'], 'CRITICAL')
        
        # 验证已应用
        test_logger = logging.getLogger('update_test_logger')
        self.assertEqual(test_logger.level, logging.CRITICAL)
    
    def test_get_status(self):
        """测试获取状态"""
        # 设置一些级别
        self.manager.set_level('status_test', 'DEBUG', save_to_config=False)
        
        # 获取状态
        status = self.manager.get_status()
        
        # 验证状态信息
        self.assertIn('config_file', status)
        self.assertIn('managed_loggers', status)
        self.assertIn('total_managed', status)
        self.assertIn('current_levels', status)
        self.assertIn('config', status)
        self.assertIn('history_count', status)
        
        self.assertIn('status_test', status['managed_loggers'])
        self.assertGreater(status['total_managed'], 0)


if __name__ == '__main__':
    unittest.main()
