import logging
logger = logging.getLogger(__name__)
"""
系统优化综合测试套件

整合所有优化组件的测试，验证系统优化效果
"""

import pytest
import asyncio
import time
import os
import tempfile
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from dataclasses import dataclass
from typing import Dict, List, Any

# 导入所有测试模块
from test_logging_optimization import TestLoggingManager, TestErrorTracker
from test_monitoring_optimization import TestMonitoringOptimizer, TestPollingManager, TestCacheManager, TestThrottleManager
from test_error_handling import TestErrorClassifier, TestDiagnosticEngine, TestRecommendationEngine, TestEnhancedErrorHandler
from test_typescript_optimization import TestTypeScriptOptimization, TestTypeScriptConfigOptimization


@dataclass
class OptimizationTestResult:
    """优化测试结果"""
    component: str
    test_name: str
    passed: bool
    execution_time: float
    error_message: str = ""
    performance_metrics: Dict[str, Any] = None


class SystemOptimizationTestSuite:
    """系统优化测试套件"""
    
    def __init__(self):
        self.test_results: List[OptimizationTestResult] = []
        self.start_time = None
        self.end_time = None
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有优化测试"""
        self.start_time = time.time()
        
        # 运行各组件测试
        self._run_logging_tests()
        self._run_monitoring_tests()
        self._run_error_handling_tests()
        self._run_typescript_tests()
        
        self.end_time = time.time()
        
        return self._generate_test_report()
    
    def _run_logging_tests(self):
        """运行日志优化测试"""
        logger.info("运行日志优化测试...")
        
        # 测试日志管理器
        try:
            start_time = time.time()
            test_manager = TestLoggingManager()
            test_manager.setup_method()
            test_manager.test_setup_logging()
            execution_time = time.time() - start_time
            
            self.test_results.append(OptimizationTestResult(
                component="logging",
                test_name="test_setup_logging",
                passed=True,
                execution_time=execution_time
            ))
        except Exception as e:
            self.test_results.append(OptimizationTestResult(
                component="logging",
                test_name="test_setup_logging",
                passed=False,
                execution_time=0,
                error_message=str(e)
            ))
        
        # 测试错误追踪器
        try:
            start_time = time.time()
            test_tracker = TestErrorTracker()
            test_tracker.setup_method()
            test_tracker.test_track_error_basic()
            test_tracker.test_track_error_frequency()
            execution_time = time.time() - start_time
            
            self.test_results.append(OptimizationTestResult(
                component="logging",
                test_name="test_error_tracking",
                passed=True,
                execution_time=execution_time
            ))
        except Exception as e:
            self.test_results.append(OptimizationTestResult(
                component="logging",
                test_name="test_error_tracking",
                passed=False,
                execution_time=0,
                error_message=str(e)
            ))
    
    def _run_monitoring_tests(self):
        """运行监控优化测试"""
        logger.info("运行监控优化测试...")
        
        # 测试监控优化器
        try:
            start_time = time.time()
            test_optimizer = TestMonitoringOptimizer()
            test_optimizer.setup_method()
            test_optimizer.test_initialization()
            test_optimizer.test_register_monitoring_task()
            execution_time = time.time() - start_time
            
            self.test_results.append(OptimizationTestResult(
                component="monitoring",
                test_name="test_monitoring_optimizer",
                passed=True,
                execution_time=execution_time
            ))
        except Exception as e:
            self.test_results.append(OptimizationTestResult(
                component="monitoring",
                test_name="test_monitoring_optimizer",
                passed=False,
                execution_time=0,
                error_message=str(e)
            ))
        
        # 测试轮询管理器
        try:
            start_time = time.time()
            test_polling = TestPollingManager()
            test_polling.setup_method()
            test_polling.test_initialization()
            test_polling.test_register_polling_task()
            test_polling.test_get_current_interval()
            execution_time = time.time() - start_time
            
            self.test_results.append(OptimizationTestResult(
                component="monitoring",
                test_name="test_polling_manager",
                passed=True,
                execution_time=execution_time
            ))
        except Exception as e:
            self.test_results.append(OptimizationTestResult(
                component="monitoring",
                test_name="test_polling_manager",
                passed=False,
                execution_time=0,
                error_message=str(e)
            ))
        
        # 测试缓存管理器
        try:
            start_time = time.time()
            test_cache = TestCacheManager()
            test_cache.setup_method()
            test_cache.test_initialization()
            test_cache.test_set_and_get()
            test_cache.test_cache_expiration()
            test_cache.test_cache_eviction()
            execution_time = time.time() - start_time
            
            self.test_results.append(OptimizationTestResult(
                component="monitoring",
                test_name="test_cache_manager",
                passed=True,
                execution_time=execution_time,
                performance_metrics={
                    'cache_operations': 4,
                    'eviction_test_passed': True
                }
            ))
        except Exception as e:
            self.test_results.append(OptimizationTestResult(
                component="monitoring",
                test_name="test_cache_manager",
                passed=False,
                execution_time=0,
                error_message=str(e)
            ))
        
        # 测试节流管理器
        try:
            start_time = time.time()
            test_throttle = TestThrottleManager()
            test_throttle.setup_method()
            test_throttle.test_initialization()
            test_throttle.test_try_acquire_allowed()
            test_throttle.test_record_success()
            test_throttle.test_record_failure()
            execution_time = time.time() - start_time
            
            self.test_results.append(OptimizationTestResult(
                component="monitoring",
                test_name="test_throttle_manager",
                passed=True,
                execution_time=execution_time,
                performance_metrics={
                    'throttle_operations': 4,
                    'backoff_test_passed': True
                }
            ))
        except Exception as e:
            self.test_results.append(OptimizationTestResult(
                component="monitoring",
                test_name="test_throttle_manager",
                passed=False,
                execution_time=0,
                error_message=str(e)
            ))
    
    def _run_error_handling_tests(self):
        """运行错误处理测试"""
        logger.info("运行错误处理测试...")
        
        # 测试错误分类器
        try:
            start_time = time.time()
            test_classifier = TestErrorClassifier()
            test_classifier.setup_method()
            test_classifier.test_initialization()
            test_classifier.test_classify_import_error()
            test_classifier.test_classify_network_error()
            test_classifier.test_pattern_identification_new()
            execution_time = time.time() - start_time
            
            self.test_results.append(OptimizationTestResult(
                component="error_handling",
                test_name="test_error_classifier",
                passed=True,
                execution_time=execution_time
            ))
            test_classifier.teardown_method()
        except Exception as e:
            self.test_results.append(OptimizationTestResult(
                component="error_handling",
                test_name="test_error_classifier",
                passed=False,
                execution_time=0,
                error_message=str(e)
            ))
        
        # 测试诊断引擎
        try:
            start_time = time.time()
            test_diagnostic = TestDiagnosticEngine()
            test_diagnostic.setup_method()
            test_diagnostic.test_initialization()
            test_diagnostic.test_check_configuration()
            test_diagnostic.test_check_dependencies()
            execution_time = time.time() - start_time
            
            self.test_results.append(OptimizationTestResult(
                component="error_handling",
                test_name="test_diagnostic_engine",
                passed=True,
                execution_time=execution_time
            ))
        except Exception as e:
            self.test_results.append(OptimizationTestResult(
                component="error_handling",
                test_name="test_diagnostic_engine",
                passed=False,
                execution_time=0,
                error_message=str(e)
            ))
        
        # 测试建议引擎
        try:
            start_time = time.time()
            test_recommendation = TestRecommendationEngine()
            test_recommendation.setup_method()
            test_recommendation.test_initialization()
            test_recommendation.test_generate_recommendations_for_issues()
            test_recommendation.test_generate_best_practice_recommendations()
            execution_time = time.time() - start_time
            
            self.test_results.append(OptimizationTestResult(
                component="error_handling",
                test_name="test_recommendation_engine",
                passed=True,
                execution_time=execution_time
            ))
        except Exception as e:
            self.test_results.append(OptimizationTestResult(
                component="error_handling",
                test_name="test_recommendation_engine",
                passed=False,
                execution_time=0,
                error_message=str(e)
            ))
        
        # 测试增强错误处理器
        try:
            start_time = time.time()
            test_handler = TestEnhancedErrorHandler()
            test_handler.setup_method()
            test_handler.test_initialization()
            test_handler.test_handle_error_basic()
            test_handler.test_get_error_statistics()
            execution_time = time.time() - start_time
            
            self.test_results.append(OptimizationTestResult(
                component="error_handling",
                test_name="test_enhanced_error_handler",
                passed=True,
                execution_time=execution_time
            ))
            test_handler.teardown_method()
        except Exception as e:
            self.test_results.append(OptimizationTestResult(
                component="error_handling",
                test_name="test_enhanced_error_handler",
                passed=False,
                execution_time=0,
                error_message=str(e)
            ))
    
    def _run_typescript_tests(self):
        """运行TypeScript优化测试"""
        logger.info("运行TypeScript优化测试...")
        
        # 测试TypeScript优化
        try:
            start_time = time.time()
            test_ts = TestTypeScriptOptimization()
            test_ts.setup_method()
            test_ts.test_analyze_errors()
            test_ts.test_fix_optional_chaining_issues()
            test_ts.test_fix_type_assertion_issues()
            test_ts.test_optimize_tsconfig()
            execution_time = time.time() - start_time
            
            self.test_results.append(OptimizationTestResult(
                component="typescript",
                test_name="test_typescript_optimization",
                passed=True,
                execution_time=execution_time,
                performance_metrics={
                    'errors_analyzed': 335,
                    'fixes_applied': 4
                }
            ))
            test_ts.teardown_method()
        except Exception as e:
            self.test_results.append(OptimizationTestResult(
                component="typescript",
                test_name="test_typescript_optimization",
                passed=False,
                execution_time=0,
                error_message=str(e)
            ))
        
        # 测试TypeScript配置优化
        try:
            start_time = time.time()
            test_config = TestTypeScriptConfigOptimization()
            test_config.setup_method()
            test_config.test_tsconfig_strict_mode_adjustment()
            test_config.test_tsconfig_lib_skip_optimization()
            test_config.test_tsconfig_module_resolution_optimization()
            execution_time = time.time() - start_time
            
            self.test_results.append(OptimizationTestResult(
                component="typescript",
                test_name="test_typescript_config_optimization",
                passed=True,
                execution_time=execution_time
            ))
            test_config.teardown_method()
        except Exception as e:
            self.test_results.append(OptimizationTestResult(
                component="typescript",
                test_name="test_typescript_config_optimization",
                passed=False,
                execution_time=0,
                error_message=str(e)
            ))
    
    def _generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r.passed])
        failed_tests = total_tests - passed_tests
        
        total_execution_time = self.end_time - self.start_time
        
        # 按组件分组统计
        component_stats = {}
        for result in self.test_results:
            if result.component not in component_stats:
                component_stats[result.component] = {
                    'total': 0,
                    'passed': 0,
                    'failed': 0,
                    'execution_time': 0
                }
            
            component_stats[result.component]['total'] += 1
            component_stats[result.component]['execution_time'] += result.execution_time
            
            if result.passed:
                component_stats[result.component]['passed'] += 1
            else:
                component_stats[result.component]['failed'] += 1
        
        # 收集性能指标
        performance_metrics = {}
        for result in self.test_results:
            if result.performance_metrics:
                performance_metrics[result.test_name] = result.performance_metrics
        
        # 收集失败的测试
        failed_test_details = [
            {
                'component': r.component,
                'test_name': r.test_name,
                'error_message': r.error_message
            }
            for r in self.test_results if not r.passed
        ]
        
        return {
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'total_execution_time': total_execution_time
            },
            'component_stats': component_stats,
            'performance_metrics': performance_metrics,
            'failed_tests': failed_test_details,
            'test_results': [
                {
                    'component': r.component,
                    'test_name': r.test_name,
                    'passed': r.passed,
                    'execution_time': r.execution_time,
                    'error_message': r.error_message
                }
                for r in self.test_results
            ],
            'timestamp': datetime.now().isoformat()
        }


class TestSystemOptimizationIntegration:
    """系统优化集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.test_suite = SystemOptimizationTestSuite()
    
    def test_run_complete_optimization_test_suite(self):
        """测试运行完整的优化测试套件"""
        logger.info("\n开始运行系统优化综合测试套件...")
        
        # 运行所有测试
        report = self.test_suite.run_all_tests()
        
        # 验证测试报告
        assert 'summary' in report
        assert 'component_stats' in report
        assert 'test_results' in report
        
        summary = report['summary']
        assert summary['total_tests'] > 0
        assert summary['success_rate'] >= 0
        
        # 打印测试结果摘要
        logger.info(f"\n测试摘要:")
        logger.info(f"总测试数: {summary['total_tests']}")
        logger.info(f"通过测试: {summary['passed_tests']}")
        logger.info(f"失败测试: {summary['failed_tests']}")
        logger.info(f"成功率: {summary['success_rate']:.2f}%")
        logger.info(f"总执行时间: {summary['total_execution_time']:.2f}秒")
        
        # 打印组件统计
        logger.info(f"\n组件测试统计:")
        for component, stats in report['component_stats'].items():
            logger.info(f"  {component}: {stats['passed']}/{stats['total']} 通过 "
                  f"({stats['execution_time']:.2f}秒)")
        
        # 打印失败的测试
        if report['failed_tests']:
            logger.info(f"\n失败的测试:")
            for failed in report['failed_tests']:
                logger.info(f"  {failed['component']}.{failed['test_name']}: {failed['error_message']}")
        
        # 验证关键组件测试通过
        component_stats = report['component_stats']
        
        # 至少应该有这些组件的测试
        expected_components = ['logging', 'monitoring', 'error_handling', 'typescript']
        for component in expected_components:
            assert component in component_stats, f"缺少{component}组件的测试"
            assert component_stats[component]['total'] > 0, f"{component}组件没有测试"
        
        # 整体成功率应该合理
        assert summary['success_rate'] >= 70, f"测试成功率过低: {summary['success_rate']:.2f}%"
    
    def test_performance_metrics_collection(self):
        """测试性能指标收集"""
        # 运行测试套件
        report = self.test_suite.run_all_tests()
        
        # 验证性能指标
        performance_metrics = report.get('performance_metrics', {})
        
        # 应该有一些性能指标
        assert len(performance_metrics) >= 0  # 可能为空，但结构应该存在
        
        # 验证执行时间记录
        for result in report['test_results']:
            assert 'execution_time' in result
            assert result['execution_time'] >= 0
    
    def test_error_reporting_accuracy(self):
        """测试错误报告准确性"""
        # 运行测试套件
        report = self.test_suite.run_all_tests()
        
        # 验证错误报告
        failed_tests = report.get('failed_tests', [])
        
        for failed_test in failed_tests:
            assert 'component' in failed_test
            assert 'test_name' in failed_test
            assert 'error_message' in failed_test
            assert failed_test['error_message']  # 错误消息不应为空
    
    def test_component_coverage(self):
        """测试组件覆盖率"""
        # 运行测试套件
        report = self.test_suite.run_all_tests()
        
        component_stats = report['component_stats']
        
        # 验证所有关键组件都有测试覆盖
        required_components = {
            'logging': ['日志管理', '错误追踪'],
            'monitoring': ['监控优化', '轮询管理', '缓存管理', '节流管理'],
            'error_handling': ['错误分类', '诊断引擎', '建议引擎', '错误处理器'],
            'typescript': ['TypeScript优化', '配置优化']
        }
        
        for component, features in required_components.items():
            assert component in component_stats, f"缺少{component}组件测试"
            
            # 每个组件至少应该有一定数量的测试
            min_tests = len(features)  # 至少每个功能一个测试
            actual_tests = component_stats[component]['total']
            assert actual_tests >= min_tests, \
                f"{component}组件测试数量不足: {actual_tests} < {min_tests}"


def run_optimization_test_suite():
    """运行优化测试套件的主函数"""
    logger.info("=" * 60)
    logger.info("系统优化组件单元测试套件")
    logger.info("=" * 60)
    
    # 创建并运行测试套件
    suite = SystemOptimizationTestSuite()
    report = suite.run_all_tests()
    
    # 保存测试报告
    import json
    report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        logger.info(f"\n测试报告已保存到: {report_file}")
    except Exception as e:
        logger.info(f"保存测试报告失败: {e}")
    
    return report


if __name__ == '__main__':
    # 运行综合测试套件
    report = run_optimization_test_suite()
    
    # 根据测试结果设置退出码
    success_rate = report['summary']['success_rate']
    if success_rate >= 90:
        logger.info("\n✅ 所有测试基本通过，系统优化组件运行良好！")
        exit(0)
    elif success_rate >= 70:
        logger.info("\n⚠️  大部分测试通过，但有一些问题需要关注。")
        exit(1)
    else:
        logger.info("\n❌ 测试失败率较高，需要修复问题。")
        exit(2)