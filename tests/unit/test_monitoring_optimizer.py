import logging
logger = logging.getLogger(__name__)
"""
监控优化器单元测试
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.monitoring.monitoring_optimizer import (
    MonitoringOptimizer,
    MonitoringOptimizationConfig,
    OptimizationLevel,
    PerformanceMetrics
)
from src.monitoring.polling_manager import PollingConfig
from src.monitoring.cache_manager import CacheConfig
from src.monitoring.throttle_manager import ThrottleConfig


class TestMonitoringOptimizer:
    """监控优化器测试"""
    
    @pytest.fixture
    def config(self):
        """测试配置"""
        return MonitoringOptimizationConfig(
            optimization_level=OptimizationLevel.BALANCED,
            optimization_check_interval_seconds=10,
            auto_adjustment_enabled=True
        )
    
    @pytest.fixture
    def optimizer(self, config):
        """监控优化器实例"""
        return MonitoringOptimizer(config)
    
    def test_initialization(self, optimizer):
        """测试初始化"""
        assert optimizer.config.optimization_level == OptimizationLevel.BALANCED
        assert optimizer.polling_manager is not None
        assert optimizer.cache_manager is not None
        assert optimizer.throttle_registry is not None
        assert optimizer.health_monitor is not None
        assert not optimizer.running
    
    @pytest.mark.asyncio
    async def test_start_stop(self, optimizer):
        """测试启动和停止"""
        # 启动
        await optimizer.start()
        assert optimizer.running
        
        # 停止
        await optimizer.stop()
        assert not optimizer.running
    
    def test_register_monitoring_task(self, optimizer):
        """测试注册监控任务"""
        callback = Mock()
        
        optimizer.register_monitoring_task("test_task", callback, 30)
        
        # 验证任务已注册
        assert "test_task" in optimizer.polling_manager.polling_callbacks
        assert optimizer.throttle_registry.get("test_task") is not None
    
    def test_unregister_monitoring_task(self, optimizer):
        """测试注销监控任务"""
        callback = Mock()
        
        # 先注册
        optimizer.register_monitoring_task("test_task", callback)
        
        # 然后注销
        optimizer.unregister_monitoring_task("test_task")
        
        # 验证任务已注销
        assert "test_task" not in optimizer.polling_manager.polling_callbacks
        assert optimizer.throttle_registry.get("test_task") is None
    
    @pytest.mark.asyncio
    async def test_optimize_monitoring_intervals(self, optimizer):
        """测试监控间隔优化"""
        # 模拟高负载情况
        with patch.object(optimizer, '_collect_performance_metrics') as mock_collect:
            mock_collect.return_value = PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_usage=85.0,  # 高CPU使用率
                memory_usage=90.0,  # 高内存使用率
                cache_hit_rate=0.8,
                average_response_time_ms=500.0,
                active_polling_tasks=5,
                throttled_requests_rate=0.1,
                system_load=2.0
            )
            
            await optimizer.optimize_monitoring_intervals()
            
            # 验证统计信息更新
            assert optimizer.stats['total_optimizations'] > 0
            assert optimizer.stats['last_optimization_time'] is not None
    
    @pytest.mark.asyncio
    async def test_implement_intelligent_caching(self, optimizer):
        """测试智能缓存实现"""
        # 模拟低命中率情况
        with patch.object(optimizer.cache_manager, 'get_cache_info') as mock_info:
            mock_info.return_value = {
                'statistics': {'hit_rate': 0.6}  # 低命中率
            }
            
            original_strategy = optimizer.cache_manager.config.strategy
            await optimizer.implement_intelligent_caching()
            
            # 验证缓存策略可能被调整
            # 注意：这里的测试可能需要根据实际实现调整
    
    def test_get_optimization_report(self, optimizer):
        """测试获取优化报告"""
        report = optimizer.get_optimization_report()
        
        assert report.timestamp is not None
        assert report.optimization_level == optimizer.config.optimization_level
        assert isinstance(report.recommendations, list)
        assert isinstance(report.applied_optimizations, list)
        assert report.next_check_time > datetime.now()
    
    def test_get_monitoring_status(self, optimizer):
        """测试获取监控状态"""
        status = optimizer.get_monitoring_status()
        
        assert 'running' in status
        assert 'optimization_level' in status
        assert 'components' in status
        assert 'statistics' in status
        
        # 验证组件状态
        components = status['components']
        assert 'polling_manager' in components
        assert 'cache_manager' in components
        assert 'throttle_managers' in components
    
    def test_generate_recommendations(self, optimizer):
        """测试生成建议"""
        # 添加一些性能历史数据
        optimizer.performance_history = [
            PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_usage=85.0,  # 高CPU
                memory_usage=90.0,  # 高内存
                cache_hit_rate=0.6,  # 低命中率
                average_response_time_ms=1500.0,  # 高响应时间
                active_polling_tasks=5,
                throttled_requests_rate=0.15,  # 高节流率
                system_load=2.0
            )
        ]
        
        recommendations = optimizer._generate_recommendations()
        
        assert len(recommendations) > 0
        # 验证包含相关建议
        assert any("CPU" in rec for rec in recommendations)
        assert any("内存" in rec for rec in recommendations)
        assert any("缓存" in rec for rec in recommendations)
    
    def test_get_applied_optimizations(self, optimizer):
        """测试获取已应用的优化"""
        # 启用一些优化
        optimizer.polling_manager.config.adaptive_enabled = True
        
        optimizations = optimizer._get_applied_optimizations()
        
        assert isinstance(optimizations, list)
        # 验证包含自适应轮询优化
        assert any("自适应轮询" in opt for opt in optimizations)
    
    @pytest.mark.asyncio
    async def test_performance_metrics_collection(self, optimizer):
        """测试性能指标收集"""
        with patch.object(optimizer.health_monitor, 'get_system_metrics') as mock_system:
            from src.core.monitoring.health_monitor import SystemMetrics
            mock_system.return_value = SystemMetrics(
                timestamp=datetime.now(),
                cpu_usage=50.0,
                memory_usage=60.0,
                disk_usage=70.0,
                network_io={'bytes_sent': 1000, 'bytes_recv': 2000},
                process_count=100,
                load_average=1.5,
                uptime_seconds=3600
            )
            
            metrics = await optimizer._collect_performance_metrics()
            
            assert metrics.cpu_usage == 50.0
            assert metrics.memory_usage == 60.0
            assert metrics.timestamp is not None
    
    @pytest.mark.asyncio
    async def test_optimization_levels(self, optimizer):
        """测试不同优化级别"""
        # 测试激进优化
        await optimizer._apply_aggressive_optimization()
        assert optimizer.polling_manager.config.active_interval == 3
        
        # 测试平衡优化
        await optimizer._apply_balanced_optimization()
        assert optimizer.polling_manager.config.active_interval == 5
        
        # 测试保守优化
        await optimizer._apply_conservative_optimization()
        assert optimizer.polling_manager.config.active_interval == 10


@pytest.mark.asyncio
async def test_monitoring_optimizer_integration():
    """测试监控优化器集成"""
    config = MonitoringOptimizationConfig(
        optimization_level=OptimizationLevel.BALANCED,
        optimization_check_interval_seconds=1,  # 快速测试
        auto_adjustment_enabled=False  # 禁用自动调整以便测试
    )
    
    optimizer = MonitoringOptimizer(config)
    
    try:
        # 启动优化器
        await optimizer.start()
        
        # 注册一个测试任务
        test_callback = Mock()
        optimizer.register_monitoring_task("integration_test", test_callback, 5)
        
        # 等待一小段时间让系统稳定
        await asyncio.sleep(0.1)
        
        # 验证组件状态
        status = optimizer.get_monitoring_status()
        assert status['running']
        
        # 获取优化报告
        report = optimizer.get_optimization_report()
        assert report is not None
        
        # 手动触发优化
        await optimizer.optimize_monitoring_intervals()
        
        # 验证统计信息
        assert optimizer.stats['total_optimizations'] > 0
        
    finally:
        # 清理
        await optimizer.stop()


if __name__ == "__main__":
    # 运行简单的集成测试
    asyncio.run(test_monitoring_optimizer_integration())
    logger.info("监控优化器集成测试通过！")