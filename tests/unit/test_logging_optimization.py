"""
日志优化组件单元测试

测试日志去重和级别管理功能
"""

import pytest
import logging
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os

from src.core.monitoring.logging_manager import LoggingManager, ErrorTracker, LogEntry, DatabaseLogHandler, JSONFormatter


class TestLoggingManager:
    """日志管理器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.logging_manager = LoggingManager()
    
    def test_setup_logging(self):
        """测试日志系统设置"""
        # 测试日志系统初始化
        self.logging_manager.setup_logging()
        
        # 验证配置状态
        assert self.logging_manager.configured is True
        
        # 验证根日志器配置
        root_logger = logging.getLogger()
        assert len(root_logger.handlers) > 0
        
        # 测试重复设置不会重复配置
        initial_handler_count = len(root_logger.handlers)
        self.logging_manager.setup_logging()
        assert len(root_logger.handlers) == initial_handler_count
    
    def test_error_tracker_initialization(self):
        """测试错误追踪器初始化"""
        assert self.logging_manager.error_tracker is not None
        assert isinstance(self.logging_manager.error_tracker, ErrorTracker)
    
    @patch('src.monitoring.logging_manager.config')
    def test_logging_configuration_with_mock_config(self, mock_config):
        """测试使用模拟配置的日志设置"""
        # 设置模拟配置
        mock_config.logging.level = 'INFO'
        mock_config.logging.file_path = '/tmp/test.log'
        mock_config.logging.format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        mock_config.logging.max_file_size = 10485760
        mock_config.logging.backup_count = 5
        mock_config.is_production.return_value = False
        
        # 重置配置状态
        self.logging_manager.configured = False
        
        # 测试配置
        self.logging_manager.setup_logging()
        
        # 验证配置应用
        assert self.logging_manager.configured is True


class TestErrorTracker:
    """错误追踪器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.error_tracker = ErrorTracker()
    
    def test_track_error_basic(self):
        """测试基本错误追踪"""
        error = ValueError("Test error")
        context = {"component": "test", "operation": "unit_test"}
        
        # 追踪错误
        self.error_tracker.track_error(error, context)
        
        # 验证错误被记录
        error_key = "ValueError:Test error"
        assert error_key in self.error_tracker.error_counts
        assert self.error_tracker.error_counts[error_key] == 1
        assert error_key in self.error_tracker.error_details
    
    def test_track_error_frequency(self):
        """测试错误频率统计"""
        error = RuntimeError("Frequent error")
        
        # 多次追踪同一错误
        for i in range(5):
            self.error_tracker.track_error(error)
        
        error_key = "RuntimeError:Frequent error"
        assert self.error_tracker.error_counts[error_key] == 5
    
    def test_track_error_with_context(self):
        """测试带上下文的错误追踪"""
        error = ConnectionError("Network timeout")
        context = {
            "url": "https://api.example.com",
            "timeout": 30,
            "retry_count": 3
        }
        
        self.error_tracker.track_error(error, context)
        
        error_key = "ConnectionError:Network timeout"
        error_detail = self.error_tracker.error_details[error_key]
        
        assert error_detail['context'] == context
        assert error_detail['type'] == 'ConnectionError'
        assert error_detail['message'] == 'Network timeout'
    
    @patch.object(ErrorTracker, '_send_alert')
    def test_high_frequency_alert(self, mock_send_alert):
        """测试高频错误告警"""
        error = Exception("High frequency error")
        
        # 触发告警阈值（10次）
        for i in range(10):
            self.error_tracker.track_error(error)
        
        # 验证告警被触发
        mock_send_alert.assert_called_once()
    
    def test_get_error_summary(self):
        """测试错误摘要获取"""
        # 添加一些测试错误
        errors = [
            ValueError("Error 1"),
            RuntimeError("Error 2"),
            ValueError("Error 1"),  # 重复错误
            TypeError("Error 3")
        ]
        
        for error in errors:
            self.error_tracker.track_error(error)
        
        summary = self.error_tracker.get_error_summary()
        
        # 验证摘要内容
        assert summary['total_errors'] == 3  # 3种不同的错误
        assert summary['total_occurrences'] == 4  # 总共4次发生
        assert len(summary['top_errors']) <= 10
        
        # 验证最频繁的错误
        top_error = summary['top_errors'][0]
        assert top_error[0] == "ValueError:Error 1"
        assert top_error[1] == 2  # 出现2次


class TestDatabaseLogHandler:
    """数据库日志处理器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.handler = DatabaseLogHandler(batch_size=5, flush_interval=1)
    
    def test_handler_initialization(self):
        """测试处理器初始化"""
        assert self.handler.batch_size == 5
        assert self.handler.flush_interval == 1
        assert self.handler.worker_thread.is_alive()
        assert self.handler.flush_thread.is_alive()
    
    def test_create_log_entry(self):
        """测试日志条目创建"""
        # 创建测试日志记录
        logger = logging.getLogger('test_logger')
        record = logger.makeRecord(
            name='test_logger',
            level=logging.INFO,
            fn='test.py',
            lno=10,
            msg='Test message',
            args=(),
            exc_info=None
        )
        
        # 创建日志条目
        log_entry = self.handler._create_log_entry(record)
        
        # 验证日志条目
        assert isinstance(log_entry, LogEntry)
        assert log_entry.level == 'INFO'
        assert log_entry.logger == 'test_logger'
        assert log_entry.message == 'Test message'
        assert log_entry.module == 'test'
        assert log_entry.line_number == 10
    
    def test_create_log_entry_with_exception(self):
        """测试带异常信息的日志条目创建"""
        logger = logging.getLogger('test_logger')
        
        try:
            raise ValueError("Test exception")
        except ValueError:
            record = logger.makeRecord(
                name='test_logger',
                level=logging.ERROR,
                fn='test.py',
                lno=20,
                msg='Error occurred',
                args=(),
                exc_info=True
            )
        
        log_entry = self.handler._create_log_entry(record)
        
        # 验证异常信息被包含
        assert log_entry.exception_info is not None
        assert 'ValueError: Test exception' in log_entry.exception_info
    
    @patch('src.monitoring.logging_manager.create_engine')
    def test_flush_to_database(self, mock_create_engine):
        """测试刷新到数据库"""
        # 设置模拟数据库连接
        mock_engine = Mock()
        mock_conn = Mock()
        mock_create_engine.return_value = mock_engine
        mock_engine.connect.return_value.__enter__.return_value = mock_conn
        
        # 添加测试日志条目
        test_entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level='INFO',
            logger='test',
            message='Test message'
        )
        self.handler.batch_logs = [test_entry]
        
        # 执行刷新
        self.handler._flush_to_database()
        
        # 验证数据库操作
        mock_conn.execute.assert_called()
        mock_conn.commit.assert_called()
        assert len(self.handler.batch_logs) == 0


class TestJSONFormatter:
    """JSON格式化器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.formatter = JSONFormatter()
    
    def test_format_basic_record(self):
        """测试基本记录格式化"""
        logger = logging.getLogger('test_logger')
        record = logger.makeRecord(
            name='test_logger',
            level=logging.INFO,
            fn='test.py',
            lno=15,
            msg='Test message',
            args=(),
            exc_info=None
        )
        
        # 格式化记录
        formatted = self.formatter.format(record)
        
        # 验证JSON格式
        import json
        log_data = json.loads(formatted)
        
        assert log_data['level'] == 'INFO'
        assert log_data['logger'] == 'test_logger'
        assert log_data['message'] == 'Test message'
        assert log_data['module'] == 'test'
        assert log_data['line'] == 15
        assert 'timestamp' in log_data
    
    def test_format_record_with_exception(self):
        """测试带异常的记录格式化"""
        logger = logging.getLogger('test_logger')
        
        try:
            raise RuntimeError("Test runtime error")
        except RuntimeError:
            record = logger.makeRecord(
                name='test_logger',
                level=logging.ERROR,
                fn='test.py',
                lno=25,
                msg='Runtime error occurred',
                args=(),
                exc_info=True
            )
        
        formatted = self.formatter.format(record)
        
        import json
        log_data = json.loads(formatted)
        
        assert 'exception' in log_data
        assert 'RuntimeError: Test runtime error' in log_data['exception']
    
    def test_format_record_with_extra_fields(self):
        """测试带额外字段的记录格式化"""
        logger = logging.getLogger('test_logger')
        record = logger.makeRecord(
            name='test_logger',
            level=logging.WARNING,
            fn='test.py',
            lno=30,
            msg='Warning message',
            args=(),
            exc_info=None
        )
        
        # 添加额外字段
        record.user_id = 'user123'
        record.request_id = 'req456'
        record.custom_data = {'key': 'value'}
        
        formatted = self.formatter.format(record)
        
        import json
        log_data = json.loads(formatted)
        
        assert log_data['user_id'] == 'user123'
        assert log_data['request_id'] == 'req456'
        assert log_data['custom_data'] == {'key': 'value'}


class TestLogDeduplication:
    """日志去重测试"""
    
    def setup_method(self):
        """测试前设置"""
        # 这里可以添加日志去重相关的测试
        # 由于LogDeduplicator类在当前代码中没有直接实现
        # 我们可以测试相关的去重逻辑
        pass
    
    def test_duplicate_message_detection(self):
        """测试重复消息检测"""
        # 模拟日志去重逻辑测试
        messages = {}
        window_seconds = 60
        
        def should_log(message, level):
            """简化的去重逻辑"""
            key = f"{level}:{message}"
            now = time.time()
            
            if key in messages:
                last_time = messages[key]
                if now - last_time < window_seconds:
                    return False
            
            messages[key] = now
            return True
        
        # 测试去重逻辑
        assert should_log("Test message", "INFO") is True
        assert should_log("Test message", "INFO") is False  # 重复消息
        assert should_log("Different message", "INFO") is True
        assert should_log("Test message", "ERROR") is True  # 不同级别
        
        # 模拟时间过去
        time.sleep(0.1)
        messages["INFO:Test message"] = time.time() - 70  # 超过窗口时间
        assert should_log("Test message", "INFO") is True


class TestLogLevelManagement:
    """日志级别管理测试"""
    
    def test_third_party_logger_levels(self):
        """测试第三方库日志级别设置"""
        # 测试常见第三方库的日志级别
        third_party_loggers = {
            'urllib3': logging.WARNING,
            'requests': logging.WARNING,
            'sqlalchemy.engine': logging.WARNING,
        }
        
        for logger_name, expected_level in third_party_loggers.items():
            logger = logging.getLogger(logger_name)
            logger.setLevel(expected_level)
            assert logger.level == expected_level
    
    def test_dynamic_log_level_adjustment(self):
        """测试动态日志级别调整"""
        test_logger = logging.getLogger('test_dynamic')
        
        # 测试级别调整
        levels = [logging.DEBUG, logging.INFO, logging.WARNING, logging.ERROR, logging.CRITICAL]
        
        for level in levels:
            test_logger.setLevel(level)
            assert test_logger.level == level
            assert test_logger.isEnabledFor(level)
    
    def test_component_specific_log_levels(self):
        """测试组件特定的日志级别"""
        components = {
            'data_adapter': logging.DEBUG,
            'monitoring': logging.INFO,
            'frontend': logging.WARNING,
            'strategy': logging.INFO
        }
        
        for component, level in components.items():
            logger = logging.getLogger(component)
            logger.setLevel(level)
            assert logger.level == level


if __name__ == '__main__':
    pytest.main([__file__])