"""
Tests for enhanced order execution simulation.
"""

import pytest
import uuid
from datetime import datetime
from unittest.mock import Mock, patch

from src.backtest.executor import OrderExecutor, ExecutionConfig, RejectionReason
from src.market.strategies.models.trading import Order, OrderSide, OrderType, OrderStatus
from src.market.strategies.models.market_data import MarketData
from src.market.strategies.models.portfolio import Port<PERSON>lio, Position


class TestExecutionConfig:
    """Test execution configuration."""
    
    def test_default_config_validation(self):
        """Test default configuration is valid."""
        config = ExecutionConfig()
        assert config.validate()
    
    def test_invalid_config_validation(self):
        """Test invalid configuration detection."""
        # Negative commission rate
        config = ExecutionConfig(commission_rate=-0.1)
        assert not config.validate()
        
        # Invalid partial fill ratio
        config = ExecutionConfig(min_fill_ratio=0.8, max_fill_ratio=0.3)
        assert not config.validate()
        
        # Invalid probability
        config = ExecutionConfig(rejection_probability=1.5)
        assert not config.validate()


class TestOrderExecutor:
    """Test order executor functionality."""
    
    @pytest.fixture
    def executor(self):
        """Create order executor with test configuration."""
        config = ExecutionConfig(
            commission_rate=0.001,
            slippage_rate=0.0005,
            partial_fill_probability=0.0,  # Disable for predictable tests
            rejection_probability=0.0      # Disable for predictable tests
        )
        executor = OrderExecutor(config)
        executor.set_random_seed(42)  # For consistent testing
        return executor
    
    @pytest.fixture
    def market_data(self):
        """Create sample market data."""
        return MarketData(
            symbol="AAPL",
            timestamp=datetime(2024, 1, 15, 10, 0),
            open=150.0,
            high=152.0,
            low=149.0,
            close=151.0,
            volume=1000000
        )
    
    @pytest.fixture
    def portfolio(self):
        """Create sample portfolio."""
        portfolio = Portfolio(initial_capital=2000000, cash=1500000)  # More cash for large orders
        # Add some existing position
        position = Position(
            symbol="AAPL",
            quantity=100,
            avg_price=145.0,
            market_value=15100.0,
            unrealized_pnl=600.0
        )
        portfolio.positions["AAPL"] = position
        return portfolio
    
    def test_market_order_execution(self, executor, market_data, portfolio):
        """Test market order execution."""
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=10
        )
        
        fill = executor.execute_order(order, market_data, portfolio)
        
        assert fill is not None
        assert fill.symbol == "AAPL"
        assert fill.side == OrderSide.BUY
        assert fill.quantity == 10
        assert fill.price > market_data.close  # Should include slippage
        assert fill.commission > 0
        assert order.status == OrderStatus.FILLED
    
    def test_limit_order_execution(self, executor, market_data, portfolio):
        """Test limit order execution."""
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=10,
            price=151.0
        )
        
        fill = executor.execute_order(order, market_data, portfolio)
        
        assert fill is not None
        assert fill.price == 151.0  # Should execute at limit price
        assert order.status == OrderStatus.FILLED
    
    def test_stop_order_execution(self, executor, market_data, portfolio):
        """Test stop order execution."""
        # Buy stop order (trigger when price goes above stop price)
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.STOP,
            quantity=10,
            stop_price=150.0  # Should trigger since high=152.0
        )
        
        fill = executor.execute_order(order, market_data, portfolio)
        
        assert fill is not None
        assert fill.quantity == 10
        assert order.status == OrderStatus.FILLED
    
    def test_stop_order_not_triggered(self, executor, market_data, portfolio):
        """Test stop order not triggered."""
        # Buy stop order with high trigger price
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.STOP,
            quantity=10,
            stop_price=155.0  # Should not trigger since high=152.0
        )
        
        fill = executor.execute_order(order, market_data, portfolio)
        
        assert fill is None  # Order not triggered
        assert order.status == OrderStatus.PENDING
    
    def test_stop_limit_order_execution(self, executor, market_data, portfolio):
        """Test stop-limit order execution."""
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.SELL,
            order_type=OrderType.STOP_LIMIT,
            quantity=50,
            stop_price=150.0,  # Should trigger since low=149.0
            price=149.5
        )
        
        fill = executor.execute_order(order, market_data, portfolio)
        
        assert fill is not None
        assert fill.price == 149.5  # Should execute at limit price
        assert fill.quantity == 50
        assert order.status == OrderStatus.FILLED
    
    def test_insufficient_funds_rejection(self, executor, market_data):
        """Test order rejection due to insufficient funds."""
        # Portfolio with very little cash
        poor_portfolio = Portfolio(initial_capital=1000, cash=100)
        
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100  # Would cost ~15,100 + commission
        )
        
        fill = executor.execute_order(order, market_data, poor_portfolio)
        
        assert fill is None
        assert order.status == OrderStatus.REJECTED
    
    def test_insufficient_shares_rejection(self, executor, market_data):
        """Test order rejection due to insufficient shares."""
        # Portfolio with no positions
        empty_portfolio = Portfolio(initial_capital=100000, cash=50000)
        
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.SELL,
            order_type=OrderType.MARKET,
            quantity=100  # Don't have any shares
        )
        
        fill = executor.execute_order(order, market_data, empty_portfolio)
        
        assert fill is None
        assert order.status == OrderStatus.REJECTED
    
    def test_partial_fill_execution(self, executor, market_data, portfolio):
        """Test partial fill execution."""
        # Enable partial fills
        executor.config.partial_fill_probability = 1.0  # Always partial fill
        executor.config.min_fill_ratio = 0.5
        executor.config.max_fill_ratio = 0.5
        
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100
        )
        
        fill = executor.execute_order(order, market_data, portfolio)
        
        assert fill is not None
        assert fill.quantity == 50  # 50% of 100
        assert order.status == OrderStatus.PARTIALLY_FILLED
        assert order.quantity == 50  # Remaining quantity
    
    def test_commission_calculation(self, executor, market_data, portfolio):
        """Test commission calculation."""
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=1000
        )
        
        fill = executor.execute_order(order, market_data, portfolio)
        
        assert fill is not None
        expected_commission = max(
            fill.quantity * fill.price * executor.config.commission_rate,
            executor.config.min_commission
        )
        assert abs(fill.commission - expected_commission) < 0.01
    
    def test_slippage_application(self, executor, market_data, portfolio):
        """Test slippage application."""
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=10
        )
        
        fill = executor.execute_order(order, market_data, portfolio)
        
        assert fill is not None
        # Buy order should have higher price due to slippage
        assert fill.price > market_data.close
    
    def test_market_impact_calculation(self, executor, market_data, portfolio):
        """Test market impact on large orders."""
        # Large order should have more market impact
        large_order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=5000  # Reduced size to fit within portfolio cash
        )
        
        small_order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=10
        )
        
        large_fill = executor.execute_order(large_order, market_data, portfolio)
        small_fill = executor.execute_order(small_order, market_data, portfolio)
        
        assert large_fill is not None
        assert small_fill is not None
        # Large order should have higher execution price due to market impact
        assert large_fill.price > small_fill.price
    
    def test_random_rejection(self, executor, market_data, portfolio):
        """Test random order rejection."""
        # Set high rejection probability
        executor.config.rejection_probability = 1.0  # Always reject
        
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=10
        )
        
        fill = executor.execute_order(order, market_data, portfolio)
        
        assert fill is None
        assert order.status == OrderStatus.REJECTED
    
    def test_price_tolerance_rejection(self, executor, market_data, portfolio):
        """Test limit order rejection due to price tolerance."""
        # Set very strict price tolerance
        executor.config.price_tolerance = 0.01  # 1%
        
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=10,
            price=140.0  # Too far from market price (151.0)
        )
        
        fill = executor.execute_order(order, market_data, portfolio)
        
        assert fill is None
        assert order.status == OrderStatus.REJECTED
    
    def test_execution_stats(self, executor, market_data, portfolio):
        """Test execution statistics tracking."""
        # Execute a few orders
        for i in range(3):
            order = Order(
                id=str(uuid.uuid4()),
                symbol="AAPL",
                side=OrderSide.BUY,
                order_type=OrderType.MARKET,
                quantity=10
            )
            executor.execute_order(order, market_data, portfolio)
        
        stats = executor.get_execution_stats()
        
        assert stats['total_recent_orders'] == 3
        assert 'AAPL' in stats['symbols_traded']
        assert 'config' in stats
    
    def test_order_history_reset(self, executor, market_data, portfolio):
        """Test order history reset."""
        # Execute an order
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=10
        )
        executor.execute_order(order, market_data, portfolio)
        
        # Check history exists
        assert executor.get_execution_stats()['total_recent_orders'] > 0
        
        # Reset and check
        executor.reset_order_history()
        assert executor.get_execution_stats()['total_recent_orders'] == 0


if __name__ == "__main__":
    pytest.main([__file__])