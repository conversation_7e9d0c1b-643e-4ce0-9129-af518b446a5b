import logging
logger = logging.getLogger(__name__)
"""
多策略管理系统测试

测试策略组合管理功能：
- 策略权重分配和管理
- 动态权重调整机制  
- 策略冲突检测和解决
- 组合绩效跟踪和分析
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.market.strategies.multi_strategy_manager import (
    StrategyPortfolioManager, WeightingMethod, RebalanceFrequency, ConflictType,
    StrategyAllocation, StrategyConflict, ConflictResolver
)
from src.market.strategies.base import BaseStrategy
from src.market.strategies.signals import Signal, SignalType
from src.market.strategies.models.trading import Trade
from src.analysis.metrics import PerformanceMetrics


class MockStrategy(BaseStrategy):
    """模拟策略类用于测试"""
    
    def __init__(self, name: str, expected_return: float = 0.1, volatility: float = 0.2):
        super().__init__(name)
        self.expected_return = expected_return
        self.volatility = volatility
        self._signals = []
    
    def get_parameter_definitions(self):
        """获取策略参数定义"""
        return {}
    
    def initialize(self, context):
        """策略初始化"""
        self.context = context
        self.is_initialized = True
    
    def on_data(self, data):
        """数据更新时的处理逻辑"""
        return self._signals.copy()
    
    def add_signal(self, signal: Signal):
        self._signals.append(signal)


class TestStrategyPortfolioManager:
    """测试策略组合管理器"""
    
    @pytest.fixture
    def portfolio_manager(self):
        """创建测试用的组合管理器"""
        return StrategyPortfolioManager(
            initial_capital=1000000.0,
            weighting_method=WeightingMethod.EQUAL_WEIGHT,
            rebalance_frequency=RebalanceFrequency.MONTHLY
        )
    
    @pytest.fixture
    def sample_strategies(self):
        """创建测试用的策略"""
        strategies = [
            MockStrategy("MA_Strategy", 0.12, 0.15),
            MockStrategy("RSI_Strategy", 0.08, 0.25),
            MockStrategy("MACD_Strategy", 0.15, 0.30)
        ]
        return strategies
    
    def test_portfolio_manager_initialization(self, portfolio_manager):
        """测试组合管理器初始化"""
        assert portfolio_manager.initial_capital == 1000000.0
        assert portfolio_manager.weighting_method == WeightingMethod.EQUAL_WEIGHT
        assert portfolio_manager.rebalance_frequency == RebalanceFrequency.MONTHLY
        assert len(portfolio_manager.strategies) == 0
        assert len(portfolio_manager.strategy_allocations) == 0
        assert not portfolio_manager.is_running
    
    def test_add_strategy(self, portfolio_manager, sample_strategies):
        """测试添加策略"""
        strategy = sample_strategies[0]
        strategy_id = portfolio_manager.add_strategy(strategy, weight=0.5)
        
        assert strategy_id in portfolio_manager.strategies
        assert strategy_id in portfolio_manager.strategy_allocations
        
        allocation = portfolio_manager.strategy_allocations[strategy_id]
        assert allocation.strategy_name == "MA_Strategy"
        assert allocation.weight == 1.0  # 自动标准化为1.0（只有一个策略）
        assert allocation.allocated_capital == 1000000.0
    
    def test_add_multiple_strategies(self, portfolio_manager, sample_strategies):
        """测试添加多个策略"""
        weights = [0.4, 0.3, 0.3]
        strategy_ids = []
        
        for i, strategy in enumerate(sample_strategies):
            strategy_id = portfolio_manager.add_strategy(strategy, weight=weights[i])
            strategy_ids.append(strategy_id)
        
        # 验证策略数量
        assert len(portfolio_manager.strategies) == 3
        assert len(portfolio_manager.strategy_allocations) == 3
        
        # 验证权重标准化
        total_weight = sum(alloc.weight for alloc in portfolio_manager.strategy_allocations.values())
        assert abs(total_weight - 1.0) < 1e-6
        
        # 验证资金分配
        total_allocated = sum(alloc.allocated_capital for alloc in portfolio_manager.strategy_allocations.values())
        assert abs(total_allocated - 1000000.0) < 1e-2
    
    def test_remove_strategy(self, portfolio_manager, sample_strategies):
        """测试移除策略"""
        strategy = sample_strategies[0]
        strategy_id = portfolio_manager.add_strategy(strategy, weight=1.0)
        
        # 验证策略已添加
        assert strategy_id in portfolio_manager.strategies
        
        # 移除策略
        portfolio_manager.remove_strategy(strategy_id)
        
        # 验证策略已移除
        assert strategy_id not in portfolio_manager.strategies
        assert strategy_id not in portfolio_manager.strategy_allocations
    
    def test_update_weights(self, portfolio_manager, sample_strategies):
        """测试更新权重"""
        # 添加策略
        strategy_ids = []
        for i, strategy in enumerate(sample_strategies):
            strategy_id = portfolio_manager.add_strategy(strategy, weight=1.0/len(sample_strategies))
            strategy_ids.append(strategy_id)
        
        # 更新权重
        new_weights = {
            strategy_ids[0]: 0.5,
            strategy_ids[1]: 0.3,
            strategy_ids[2]: 0.2
        }
        portfolio_manager.update_weights(new_weights, "test_update")
        
        # 验证权重更新
        for strategy_id, expected_weight in new_weights.items():
            actual_weight = portfolio_manager.strategy_allocations[strategy_id].weight
            assert abs(actual_weight - expected_weight) < 1e-6
        
        # 验证重平衡历史
        assert len(portfolio_manager.rebalance_history) == 1
        assert portfolio_manager.rebalance_history[0].trigger_reason == "test_update"
    
    def test_calculate_equal_weights(self, portfolio_manager, sample_strategies):
        """测试等权重计算"""
        # 添加策略
        for strategy in sample_strategies:
            portfolio_manager.add_strategy(strategy, weight=0.5)
        
        weights = portfolio_manager.calculate_optimal_weights()
        
        # 验证等权重
        expected_weight = 1.0 / len(sample_strategies)
        for weight in weights.values():
            assert abs(weight - expected_weight) < 1e-6
    
    def test_weight_constraints(self, portfolio_manager, sample_strategies):
        """测试权重约束"""
        strategy = sample_strategies[0]
        strategy_id = portfolio_manager.add_strategy(
            strategy, 
            weight=0.5, 
            min_weight=0.2, 
            max_weight=0.8
        )
        
        allocation = portfolio_manager.strategy_allocations[strategy_id]
        assert allocation.min_weight == 0.2
        assert allocation.max_weight == 0.8
        
        # 测试约束应用
        weights = {strategy_id: 0.1}  # 低于最小权重
        constrained = portfolio_manager._apply_weight_constraints(weights)
        assert constrained[strategy_id] >= 0.2
    
    def test_rebalance_conditions(self, portfolio_manager, sample_strategies):
        """测试重平衡条件"""
        # 添加策略
        for strategy in sample_strategies:
            portfolio_manager.add_strategy(strategy, weight=1.0/len(sample_strategies))
        
        # 测试初始状态（应该需要重平衡，因为没有历史记录）
        should_rebalance, reason = portfolio_manager.should_rebalance()
        assert should_rebalance
        assert reason == "scheduled_rebalance"
        
        # 设置最后重平衡时间
        portfolio_manager.last_rebalance = datetime.now()
        
        # 测试不需要重平衡的情况
        should_rebalance, reason = portfolio_manager.should_rebalance()
        assert not should_rebalance


class TestConflictDetection:
    """测试冲突检测"""
    
    @pytest.fixture
    def portfolio_manager(self):
        """创建测试用的组合管理器"""
        return StrategyPortfolioManager()
    
    def test_signal_conflict_detection(self, portfolio_manager):
        """测试信号冲突检测"""
        # 创建冲突信号
        signals = [
            Signal(
                symbol="AAPL",
                signal_type=SignalType.BUY,
                timestamp=datetime.now(),
                strategy_name="strategy_1",
                confidence=0.8
            ),
            Signal(
                symbol="AAPL",
                signal_type=SignalType.SELL,
                timestamp=datetime.now(),
                strategy_name="strategy_2",
                confidence=0.7
            )
        ]
        
        conflicts = portfolio_manager.detect_conflicts(signals)
        
        assert len(conflicts) == 1
        assert conflicts[0].conflict_type == ConflictType.SIGNAL_CONFLICT
        assert len(conflicts[0].strategies_involved) == 2
        assert "AAPL" in conflicts[0].description
    
    def test_no_conflict_detection(self, portfolio_manager):
        """测试无冲突情况"""
        # 创建无冲突信号
        signals = [
            Signal(
                symbol="AAPL",
                signal_type=SignalType.BUY,
                timestamp=datetime.now(),
                strategy_name="strategy_1",
                confidence=0.8
            ),
            Signal(
                symbol="MSFT",
                signal_type=SignalType.BUY,
                timestamp=datetime.now(),
                strategy_name="strategy_2",
                confidence=0.7
            )
        ]
        
        conflicts = portfolio_manager.detect_conflicts(signals)
        
        assert len(conflicts) == 0


class TestConflictResolver:
    """测试冲突解决器"""
    
    @pytest.fixture
    def resolver(self):
        """创建冲突解决器"""
        return ConflictResolver()
    
    def test_signal_conflict_resolution(self, resolver):
        """测试信号冲突解决"""
        conflict = StrategyConflict(
            conflict_id="test_conflict",
            conflict_type=ConflictType.SIGNAL_CONFLICT,
            strategies_involved=["strategy_1", "strategy_2"],
            description="测试冲突",
            timestamp=datetime.now()
        )
        
        resolution = resolver.resolve_conflict(conflict)
        
        assert "resolution" in resolution
        assert "action" in resolution
        assert resolution["resolution"] == "priority_based"
    
    def test_position_conflict_resolution(self, resolver):
        """测试仓位冲突解决"""
        conflict = StrategyConflict(
            conflict_id="test_conflict",
            conflict_type=ConflictType.POSITION_CONFLICT,
            strategies_involved=["strategy_1"],
            description="仓位超限",
            timestamp=datetime.now()
        )
        
        resolution = resolver.resolve_conflict(conflict)
        
        assert resolution["resolution"] == "position_scaling"
        assert resolution["action"] == "scale_down_positions"
    
    def test_unknown_conflict_type(self, resolver):
        """测试未知冲突类型"""
        # 创建一个不存在的冲突类型（通过直接设置属性）
        conflict = StrategyConflict(
            conflict_id="test_conflict",
            conflict_type=ConflictType.SIGNAL_CONFLICT,
            strategies_involved=["strategy_1"],
            description="未知冲突",
            timestamp=datetime.now()
        )
        # 手动修改冲突类型为不存在的值
        conflict.conflict_type = "unknown_type"
        
        resolution = resolver.resolve_conflict(conflict)
        
        assert resolution["resolution"] == "no_resolution_method"
        assert resolution["action"] == "manual_review"


class TestIntegrationWorkflow:
    """测试集成工作流"""
    
    def test_full_workflow(self):
        """测试完整工作流"""
        # 1. 创建组合管理器
        manager = StrategyPortfolioManager(
            initial_capital=1000000.0,
            weighting_method=WeightingMethod.EQUAL_WEIGHT
        )
        
        # 2. 添加策略
        strategies = [
            MockStrategy("Strategy_A", 0.12, 0.15),
            MockStrategy("Strategy_B", 0.08, 0.25),
            MockStrategy("Strategy_C", 0.15, 0.30)
        ]
        
        strategy_ids = []
        for strategy in strategies:
            strategy_id = manager.add_strategy(strategy, weight=1.0/len(strategies))
            strategy_ids.append(strategy_id)
        
        # 3. 验证初始设置
        assert len(manager.strategies) == 3
        total_weight = sum(alloc.weight for alloc in manager.strategy_allocations.values())
        assert abs(total_weight - 1.0) < 1e-6
        
        # 4. 创建一些信号（包括冲突）
        signals = [
            Signal(
                symbol="AAPL",
                signal_type=SignalType.BUY,
                timestamp=datetime.now(),
                strategy_name="Strategy_A",
                confidence=0.8
            ),
            Signal(
                symbol="AAPL",
                signal_type=SignalType.SELL,
                timestamp=datetime.now(),
                strategy_name="Strategy_B",
                confidence=0.7
            ),
            Signal(
                symbol="MSFT",
                signal_type=SignalType.BUY,
                timestamp=datetime.now(),
                strategy_name="Strategy_C",
                confidence=0.9
            )
        ]
        
        # 5. 检测冲突
        conflicts = manager.detect_conflicts(signals)
        assert len(conflicts) == 1
        
        # 6. 解决冲突
        resolver = ConflictResolver()
        resolution = resolver.resolve_conflict(conflicts[0])
        assert "resolution" in resolution
        
        # 7. 更新权重
        new_weights = {
            strategy_ids[0]: 0.5,
            strategy_ids[1]: 0.3,
            strategy_ids[2]: 0.2
        }
        manager.update_weights(new_weights, "optimization")
        
        # 8. 验证权重更新和历史记录
        assert len(manager.rebalance_history) == 1
        for sid, expected_weight in new_weights.items():
            actual_weight = manager.strategy_allocations[sid].weight
            assert abs(actual_weight - expected_weight) < 1e-6
        
        # 9. 生成报告
        report = manager.generate_rebalance_report()
        assert "total_rebalances" in report
        assert "current_weights" in report
        assert report["total_rebalances"] == 1
        
        logger.info("✅ Task 8.1 完整工作流测试通过！")


if __name__ == "__main__":
    # 运行集成测试
    test = TestIntegrationWorkflow()
    test.test_full_workflow()
    
    logger.info("🎉 Task 8.1 策略组合管理功能测试完成！")