"""
策略框架单元测试
"""

import unittest
from datetime import datetime
from unittest.mock import Mock, MagicMock
import pandas as pd

from src.market.strategies.base import BaseStrategy, StrategyContext
from src.market.strategies.signals import Signal, SignalType, SignalManager
from src.market.strategies.parameters import StrategyParameter, ParameterType, ParameterValidator
from src.market.strategies.lifecycle import StrategyLifecycleManager, StrategyState


class TestStrategy(BaseStrategy):
    """测试用策略类"""
    
    def get_parameter_definitions(self):
        return {
            'period': StrategyParameter(
                name='period',
                param_type=ParameterType.INTEGER,
                default_value=20,
                required=True,
                min_value=1,
                max_value=100,
                description='移动平均周期'
            ),
            'threshold': StrategyParameter(
                name='threshold',
                param_type=ParameterType.FLOAT,
                default_value=0.02,
                required=False,
                min_value=0.0,
                max_value=1.0,
                description='信号阈值'
            )
        }
    
    def initialize(self, context):
        self.is_initialized = True
    
    def on_data(self, data):
        # 简单的测试信号生成
        return [Signal(
            symbol='TEST',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            strategy_name=self.name,
            confidence=0.8
        )]


class TestStrategyFramework(unittest.TestCase):
    """策略框架测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.mock_data_manager = Mock()
        self.mock_indicator_engine = Mock()
        self.mock_portfolio = Mock()
        
        self.context = StrategyContext(
            data_manager=self.mock_data_manager,
            indicator_engine=self.mock_indicator_engine,
            portfolio=self.mock_portfolio
        )
    
    def test_strategy_parameter_validation(self):
        """测试策略参数验证"""
        param = StrategyParameter(
            name='test_param',
            param_type=ParameterType.INTEGER,
            min_value=1,
            max_value=100,
            required=True
        )
        
        # 正常值
        self.assertTrue(param.validate(50))
        
        # 超出范围
        with self.assertRaises(ValueError):
            param.validate(0)
        
        with self.assertRaises(ValueError):
            param.validate(101)
        
        # 类型错误
        with self.assertRaises(ValueError):
            param.validate("invalid")
    
    def test_parameter_validator(self):
        """测试参数验证器"""
        # 正数验证
        self.assertTrue(ParameterValidator.positive_number(5.5))
        self.assertFalse(ParameterValidator.positive_number(-1))
        
        # 百分比验证
        self.assertTrue(ParameterValidator.percentage(50))
        self.assertFalse(ParameterValidator.percentage(150))
        
        # 概率验证
        self.assertTrue(ParameterValidator.probability(0.5))
        self.assertFalse(ParameterValidator.probability(1.5))
        
        # 符号验证
        self.assertTrue(ParameterValidator.valid_symbol('AAPL'))
        self.assertTrue(ParameterValidator.valid_symbol('000001.SZ'))
        self.assertFalse(ParameterValidator.valid_symbol('INVALID@SYMBOL'))
    
    def test_signal_creation_and_validation(self):
        """测试信号创建和验证"""
        signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            strategy_name='test_strategy',
            confidence=0.8,
            quantity=100,
            price=150.0
        )
        
        self.assertEqual(signal.symbol, 'AAPL')
        self.assertEqual(signal.signal_type, SignalType.BUY)
        self.assertTrue(signal.is_entry_signal())
        self.assertFalse(signal.is_exit_signal())
        
        # 测试无效置信度
        with self.assertRaises(ValueError):
            Signal(
                symbol='AAPL',
                signal_type=SignalType.BUY,
                timestamp=datetime.now(),
                strategy_name='test_strategy',
                confidence=1.5  # 无效值
            )
    
    def test_signal_manager(self):
        """测试信号管理器"""
        manager = SignalManager()
        
        signal1 = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            strategy_name='strategy1',
            confidence=0.8
        )
        
        signal2 = Signal(
            symbol='AAPL',
            signal_type=SignalType.SELL,
            timestamp=datetime.now(),
            strategy_name='strategy2',
            confidence=0.9
        )
        
        # 添加信号
        self.assertTrue(manager.add_signal(signal1))
        self.assertTrue(manager.add_signal(signal2))
        
        # 获取最新信号
        latest = manager.get_latest_signal('AAPL')
        self.assertEqual(latest.signal_type, SignalType.SELL)  # 最后添加的信号
        
        # 按策略获取信号
        strategy1_signals = manager.get_signals_by_strategy('strategy1')
        self.assertEqual(len(strategy1_signals), 1)
        self.assertEqual(strategy1_signals[0].signal_type, SignalType.BUY)
        
        # 统计信息
        stats = manager.get_signal_statistics()
        self.assertEqual(stats['total_signals'], 2)
        self.assertEqual(stats['signal_types']['BUY'], 1)
        self.assertEqual(stats['signal_types']['SELL'], 1)
    
    def test_strategy_context(self):
        """测试策略上下文"""
        # 模拟历史数据
        mock_data = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [105, 106, 107],
            'low': [99, 100, 101],
            'close': [104, 105, 106],
            'volume': [1000, 1100, 1200]
        })
        self.mock_data_manager.get_historical_data.return_value = mock_data
        
        # 测试获取历史数据
        data = self.context.get_historical_data('AAPL', 30)
        self.assertIsInstance(data, pd.DataFrame)
        self.assertEqual(len(data), 3)
        
        # 测试获取指标
        mock_indicator = pd.Series([1, 2, 3])
        self.mock_indicator_engine.calculate.return_value = mock_indicator
        
        indicator = self.context.get_indicator('SMA', mock_data, period=20)
        self.assertIsInstance(indicator, pd.Series)
        self.assertEqual(len(indicator), 3)
    
    def test_base_strategy(self):
        """测试基础策略类"""
        strategy = TestStrategy('test_strategy', {'period': 30, 'threshold': 0.05})
        
        # 测试参数获取
        self.assertEqual(strategy.get_parameter('period'), 30)
        self.assertEqual(strategy.get_parameter('threshold'), 0.05)
        
        # 测试参数设置
        strategy.set_parameter('period', 50)
        self.assertEqual(strategy.get_parameter('period'), 50)
        
        # 测试无效参数
        with self.assertRaises(ValueError):
            strategy.set_parameter('period', 0)  # 小于最小值
        
        # 测试策略状态
        status = strategy.get_status()
        self.assertEqual(status['name'], 'test_strategy')
        self.assertFalse(status['is_initialized'])
        
        # 测试初始化
        strategy.initialize(self.context)
        self.assertTrue(strategy.is_initialized)
    
    def test_strategy_lifecycle_manager(self):
        """测试策略生命周期管理器"""
        manager = StrategyLifecycleManager()
        strategy = TestStrategy('test_strategy')
        
        # 注册策略
        self.assertTrue(manager.register_strategy(strategy, self.context))
        self.assertEqual(manager.get_strategy_state('test_strategy'), StrategyState.CREATED)
        
        # 初始化策略
        self.assertTrue(manager.initialize_strategy('test_strategy'))
        self.assertEqual(manager.get_strategy_state('test_strategy'), StrategyState.INITIALIZED)
        
        # 启动策略
        self.assertTrue(manager.start_strategy('test_strategy'))
        self.assertEqual(manager.get_strategy_state('test_strategy'), StrategyState.RUNNING)
        
        # 处理数据
        mock_data = Mock()
        signals = manager.process_data('test_strategy', mock_data)
        self.assertEqual(len(signals), 1)
        self.assertEqual(signals[0].signal_type, SignalType.BUY)
        
        # 暂停策略
        self.assertTrue(manager.pause_strategy('test_strategy'))
        self.assertEqual(manager.get_strategy_state('test_strategy'), StrategyState.PAUSED)
        
        # 停止策略
        self.assertTrue(manager.stop_strategy('test_strategy'))
        self.assertEqual(manager.get_strategy_state('test_strategy'), StrategyState.STOPPED)
        
        # 注销策略
        self.assertTrue(manager.unregister_strategy('test_strategy'))
        self.assertIsNone(manager.get_strategy_state('test_strategy'))
    
    def test_signal_filtering(self):
        """测试信号过滤"""
        manager = SignalManager()
        
        # 添加过滤器：只允许置信度大于0.5的信号
        def confidence_filter(signal):
            return signal.confidence > 0.5
        
        manager.add_filter(confidence_filter)
        
        # 高置信度信号应该通过
        high_confidence_signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            strategy_name='test',
            confidence=0.8
        )
        self.assertTrue(manager.add_signal(high_confidence_signal))
        
        # 低置信度信号应该被过滤
        low_confidence_signal = Signal(
            symbol='AAPL',
            signal_type=SignalType.SELL,
            timestamp=datetime.now(),
            strategy_name='test',
            confidence=0.3
        )
        self.assertFalse(manager.add_signal(low_confidence_signal))
        
        # 验证只有高置信度信号被记录
        stats = manager.get_signal_statistics()
        self.assertEqual(stats['total_signals'], 1)


if __name__ == '__main__':
    unittest.main()