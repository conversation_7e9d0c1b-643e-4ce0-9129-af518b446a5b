import logging
logger = logging.getLogger(__name__)
"""
策略协调机制测试

测试策略协调功能：
- 多策略信号聚合和优先级管理
- 资金分配和仓位协调逻辑
- 策略间通信和状态同步
- 策略组合风险控制
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.market.strategies.strategy_coordinator import (
    StrategyCoordinator, SignalAggregator, CapitalAllocator,
    SignalPriority, AggregationMethod, StrategyState,
    PrioritySignal, AggregatedSignal, PositionAllocation
)
from src.market.strategies.multi_strategy_manager import StrategyPortfolioManager, WeightingMethod
from src.market.strategies.signals import Signal, SignalType
from src.market.strategies.base import BaseStrategy


class MockStrategy(BaseStrategy):
    """模拟策略类用于测试"""
    
    def __init__(self, name: str):
        super().__init__(name)
        self._test_signals = []
    
    def get_parameter_definitions(self):
        return {}
    
    def initialize(self, context):
        self.context = context
        self.is_initialized = True
    
    def on_data(self, data):
        return self._test_signals.copy()
    
    def set_test_signals(self, signals: List[Signal]):
        self._test_signals = signals


class TestSignalAggregator:
    """测试信号聚合器"""
    
    @pytest.fixture
    def aggregator(self):
        return SignalAggregator(AggregationMethod.CONFIDENCE_BASED)
    
    @pytest.fixture
    def sample_signals(self):
        """创建测试用的信号"""
        signals = [
            PrioritySignal(
                signal=Signal(
                    symbol="AAPL",
                    signal_type=SignalType.BUY,
                    timestamp=datetime.now(),
                    strategy_name="strategy_1",
                    confidence=0.8
                ),
                priority=SignalPriority.HIGH,
                weight=0.5,
                strategy_id="strategy_1"
            ),
            PrioritySignal(
                signal=Signal(
                    symbol="AAPL",
                    signal_type=SignalType.BUY,
                    timestamp=datetime.now(),
                    strategy_name="strategy_2", 
                    confidence=0.6
                ),
                priority=SignalPriority.MEDIUM,
                weight=0.3,
                strategy_id="strategy_2"
            ),
            PrioritySignal(
                signal=Signal(
                    symbol="MSFT",
                    signal_type=SignalType.SELL,
                    timestamp=datetime.now(),
                    strategy_name="strategy_3",
                    confidence=0.9
                ),
                priority=SignalPriority.HIGH,
                weight=0.2,
                strategy_id="strategy_3"
            )
        ]
        return signals
    
    def test_aggregator_initialization(self, aggregator):
        """测试聚合器初始化"""
        assert aggregator.method == AggregationMethod.CONFIDENCE_BASED
    
    def test_signal_aggregation_by_confidence(self, aggregator, sample_signals):
        """测试基于置信度的信号聚合"""
        aggregated = aggregator.aggregate_signals(sample_signals)
        
        # 验证结果
        assert len(aggregated) == 2  # AAPL 和 MSFT
        assert "AAPL" in aggregated
        assert "MSFT" in aggregated
        
        # AAPL 应该选择置信度最高的信号（0.8）
        aapl_signal = aggregated["AAPL"]
        assert aapl_signal.signal_type == SignalType.BUY
        assert aapl_signal.aggregate_confidence == 0.8
        assert len(aapl_signal.component_signals) == 2
        
        # MSFT 只有一个信号
        msft_signal = aggregated["MSFT"]
        assert msft_signal.signal_type == SignalType.SELL
        assert msft_signal.aggregate_confidence == 0.9
        assert len(msft_signal.component_signals) == 1
    
    def test_signal_aggregation_by_priority(self, sample_signals):
        """测试基于优先级的信号聚合"""
        aggregator = SignalAggregator(AggregationMethod.PRIORITY_BASED)
        aggregated = aggregator.aggregate_signals(sample_signals)
        
        # AAPL 应该选择高优先级的信号
        aapl_signal = aggregated["AAPL"]
        assert aapl_signal.signal_type == SignalType.BUY
        # 应该选择第一个信号（优先级HIGH）
        assert aapl_signal.aggregate_confidence == 0.8
    
    def test_weighted_average_aggregation(self, sample_signals):
        """测试加权平均聚合"""
        aggregator = SignalAggregator(AggregationMethod.WEIGHTED_AVERAGE)
        
        # 创建冲突信号用于测试
        conflicting_signals = [
            PrioritySignal(
                signal=Signal(
                    symbol="AAPL",
                    signal_type=SignalType.BUY,
                    timestamp=datetime.now(),
                    strategy_name="strategy_1",
                    confidence=0.8
                ),
                priority=SignalPriority.HIGH,
                weight=0.6,
                strategy_id="strategy_1"
            ),
            PrioritySignal(
                signal=Signal(
                    symbol="AAPL",
                    signal_type=SignalType.SELL,
                    timestamp=datetime.now(),
                    strategy_name="strategy_2",
                    confidence=0.7
                ),
                priority=SignalPriority.MEDIUM,
                weight=0.4,
                strategy_id="strategy_2"
            )
        ]
        
        aggregated = aggregator.aggregate_signals(conflicting_signals)
        
        # 验证加权平均结果
        assert "AAPL" in aggregated
        aapl_signal = aggregated["AAPL"]
        
        # 买入权重信号: 0.8 * 0.6 = 0.48
        # 卖出权重信号: 0.7 * 0.4 = 0.28
        # 买入权重更高，应该选择买入
        assert aapl_signal.signal_type == SignalType.BUY


class TestCapitalAllocator:
    """测试资金分配器"""
    
    @pytest.fixture
    def allocator(self):
        return CapitalAllocator(total_capital=1000000.0)
    
    @pytest.fixture
    def sample_aggregated_signals(self):
        """创建聚合信号用于测试"""
        signals = {
            "AAPL": AggregatedSignal(
                symbol="AAPL",
                signal_type=SignalType.BUY,
                aggregate_confidence=0.8,
                component_signals=[
                    PrioritySignal(
                        signal=Signal(
                            symbol="AAPL",
                            signal_type=SignalType.BUY,
                            timestamp=datetime.now(),
                            strategy_name="strategy_1",
                            confidence=0.8
                        ),
                        priority=SignalPriority.HIGH,
                        strategy_id="strategy_1"
                    )
                ],
                aggregation_method=AggregationMethod.CONFIDENCE_BASED,
                timestamp=datetime.now()
            )
        }
        return signals
    
    def test_allocator_initialization(self, allocator):
        """测试分配器初始化"""
        assert allocator.total_capital == 1000000.0
        assert allocator.allocated_capital == 0.0
        assert len(allocator.allocations) == 0
    
    def test_capital_allocation(self, allocator, sample_aggregated_signals):
        """测试资金分配"""
        strategy_weights = {"strategy_1": 0.5, "strategy_2": 0.3}
        
        allocations = allocator.allocate_capital(sample_aggregated_signals, strategy_weights)
        
        # 验证分配结果
        assert "strategy_1" in allocations
        strategy_1_allocations = allocations["strategy_1"]
        assert len(strategy_1_allocations) == 1
        
        allocation = strategy_1_allocations[0]
        assert allocation.symbol == "AAPL"
        assert allocation.strategy_id == "strategy_1"
        assert allocation.allocated_capital > 0
        assert allocation.target_quantity > 0
    
    def test_available_capital(self, allocator):
        """测试可用资金计算"""
        initial_available = allocator.get_available_capital()
        assert initial_available == 1000000.0
        
        # 模拟分配一些资金
        allocator.allocated_capital = 100000.0
        remaining_available = allocator.get_available_capital()
        assert remaining_available == 900000.0


class TestStrategyCoordinator:
    """测试策略协调器"""
    
    @pytest.fixture
    def portfolio_manager(self):
        return StrategyPortfolioManager(
            initial_capital=1000000.0,
            weighting_method=WeightingMethod.EQUAL_WEIGHT
        )
    
    @pytest.fixture
    def coordinator(self, portfolio_manager):
        return StrategyCoordinator(
            portfolio_manager=portfolio_manager,
            aggregation_method=AggregationMethod.CONFIDENCE_BASED
        )
    
    @pytest.fixture
    def strategies_with_coordinator(self, portfolio_manager, coordinator):
        """创建带有策略的协调器"""
        # 添加策略
        strategies = [
            MockStrategy("Strategy_A"),
            MockStrategy("Strategy_B"), 
            MockStrategy("Strategy_C")
        ]
        
        strategy_ids = []
        for strategy in strategies:
            strategy_id = portfolio_manager.add_strategy(strategy, weight=1.0/len(strategies))
            strategy_ids.append(strategy_id)
        
        return coordinator, strategies, strategy_ids
    
    def test_coordinator_initialization(self, coordinator):
        """测试协调器初始化"""
        assert coordinator.portfolio_manager is not None
        assert coordinator.signal_aggregator is not None
        assert coordinator.capital_allocator is not None
        assert len(coordinator.strategy_states) == 0
    
    def test_strategy_state_management(self, coordinator):
        """测试策略状态管理"""
        strategy_id = "test_strategy"
        
        # 更新策略状态
        coordinator.update_strategy_state(strategy_id, StrategyState.ACTIVE, "策略启动")
        
        # 验证状态更新
        assert strategy_id in coordinator.strategy_states
        assert coordinator.strategy_states[strategy_id] == StrategyState.ACTIVE
    
    def test_coordination_status(self, coordinator):
        """测试协调状态获取"""
        status = coordinator.get_coordination_status()
        
        assert "total_strategies" in status
        assert "active_strategies" in status
        assert "strategy_states" in status
        assert "available_capital" in status
        assert "allocated_capital" in status
        assert "last_coordination" in status
    
    def test_strategy_synchronization(self, strategies_with_coordinator):
        """测试策略同步"""
        coordinator, strategies, strategy_ids = strategies_with_coordinator
        
        # 同步策略
        sync_results = coordinator.synchronize_strategies()
        
        # 验证同步结果
        assert len(sync_results) == len(strategy_ids)
        for strategy_id in strategy_ids:
            assert strategy_id in sync_results
            # 在测试中，所有策略应该同步成功
            assert sync_results[strategy_id] is True
    
    def test_signal_callbacks(self, coordinator):
        """测试信号回调"""
        callback_called = []
        
        def test_callback(signals):
            callback_called.append(signals)
        
        coordinator.add_signal_callback(test_callback)
        
        # 模拟信号聚合通知
        test_signals = {
            "AAPL": AggregatedSignal(
                symbol="AAPL",
                signal_type=SignalType.BUY,
                aggregate_confidence=0.8,
                component_signals=[],
                aggregation_method=AggregationMethod.CONFIDENCE_BASED,
                timestamp=datetime.now()
            )
        }
        
        coordinator._notify_signal_callbacks(test_signals)
        
        # 验证回调被调用
        assert len(callback_called) == 1
        assert "AAPL" in callback_called[0]
    
    def test_state_callbacks(self, coordinator):
        """测试状态回调"""
        callback_called = []
        
        def test_callback(update):
            callback_called.append(update)
        
        coordinator.add_state_callback(test_callback)
        
        # 更新策略状态应该触发回调
        coordinator.update_strategy_state("test_strategy", StrategyState.ACTIVE, "测试")
        
        # 验证回调被调用
        assert len(callback_called) == 1
        assert callback_called[0].strategy_id == "test_strategy"
        assert callback_called[0].state == StrategyState.ACTIVE


class TestIntegrationWorkflow:
    """测试集成工作流"""
    
    def test_full_coordination_workflow(self):
        """测试完整协调工作流"""
        logger.info("🔄 开始策略协调集成测试...")
        
        # 1. 创建组合管理器
        portfolio_manager = StrategyPortfolioManager(
            initial_capital=1000000.0,
            weighting_method=WeightingMethod.EQUAL_WEIGHT
        )
        
        # 2. 创建协调器
        coordinator = StrategyCoordinator(
            portfolio_manager=portfolio_manager,
            aggregation_method=AggregationMethod.CONFIDENCE_BASED
        )
        
        # 3. 添加策略
        strategies = [
            MockStrategy("Strategy_A"),
            MockStrategy("Strategy_B"),
            MockStrategy("Strategy_C")
        ]
        
        strategy_ids = []
        for strategy in strategies:
            strategy_id = portfolio_manager.add_strategy(strategy, weight=1.0/len(strategies))
            strategy_ids.append(strategy_id)
        
        # 4. 设置策略信号
        test_signals = [
            Signal(
                symbol="AAPL",
                signal_type=SignalType.BUY,
                timestamp=datetime.now(),
                strategy_name="Strategy_A",
                confidence=0.8
            ),
            Signal(
                symbol="MSFT",
                signal_type=SignalType.SELL,
                timestamp=datetime.now(),
                strategy_name="Strategy_B",
                confidence=0.7
            )
        ]
        
        # 为策略设置信号（在实际中会通过on_data方法返回）
        for i, strategy in enumerate(strategies[:2]):  # 只为前两个策略设置信号
            strategy.set_test_signals([test_signals[i]])
        
        # 5. 测试信号聚合
        priority_signals = [
            PrioritySignal(
                signal=test_signals[0],
                priority=SignalPriority.HIGH,
                weight=0.33,
                strategy_id=strategy_ids[0]
            ),
            PrioritySignal(
                signal=test_signals[1],
                priority=SignalPriority.MEDIUM,
                weight=0.33,
                strategy_id=strategy_ids[1]
            )
        ]
        
        aggregated_signals = coordinator.signal_aggregator.aggregate_signals(priority_signals)
        assert len(aggregated_signals) == 2
        assert "AAPL" in aggregated_signals
        assert "MSFT" in aggregated_signals
        logger.info("✅ 信号聚合测试通过")
        
        # 6. 测试资金分配
        strategy_weights = {sid: 1.0/len(strategy_ids) for sid in strategy_ids}
        allocations = coordinator.capital_allocator.allocate_capital(
            aggregated_signals, strategy_weights
        )
        assert len(allocations) >= 1  # 至少有一个策略有分配
        logger.info("✅ 资金分配测试通过")
        
        # 7. 测试策略状态管理
        for strategy_id in strategy_ids:
            coordinator.update_strategy_state(strategy_id, StrategyState.ACTIVE)
        
        status = coordinator.get_coordination_status()
        assert status["total_strategies"] == 3
        assert status["active_strategies"] == 3
        logger.info("✅ 策略状态管理测试通过")
        
        # 8. 测试同步
        sync_results = coordinator.synchronize_strategies()
        assert len(sync_results) == 3
        assert all(sync_results.values())  # 所有策略都应该同步成功
        logger.info("✅ 策略同步测试通过")
        
        # 9. 测试回调机制
        callback_triggered = {"signal": False, "state": False}
        
        def signal_callback(signals):
            callback_triggered["signal"] = True
        
        def state_callback(update):
            callback_triggered["state"] = True
        
        coordinator.add_signal_callback(signal_callback)
        coordinator.add_state_callback(state_callback)
        
        # 触发回调
        coordinator._notify_signal_callbacks(aggregated_signals)
        coordinator.update_strategy_state("test", StrategyState.SUSPENDED)
        
        assert callback_triggered["signal"]
        assert callback_triggered["state"]
        logger.info("✅ 回调机制测试通过")
        
        logger.info("🎉 Task 8.2 策略协调机制完整工作流测试通过！")


if __name__ == "__main__":
    # 运行集成测试
    test = TestIntegrationWorkflow()
    test.test_full_coordination_workflow()
    
    logger.info("🎉 Task 8.2 策略协调机制功能测试完成！")