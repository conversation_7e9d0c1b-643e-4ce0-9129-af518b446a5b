"""
测试宏观经济策略模块
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.market.strategies.economic_base import EconomicStrategy, EconomicContext
from src.market.strategies.examples.asset_allocation_strategy import AssetAllocationStrategy
from src.market.strategies.examples.economic_cycle_strategy import EconomicCycleStrategy
from src.market.strategies.examples.inflation_hedge_strategy import InflationHedgeStrategy
from src.market.strategies.economic_correlation import EconomicCorrelationAnalyzer, EconomicEventAnalyzer
from src.market.strategies.models.market_data import MarketData, EconomicData


class TestEconomicContext(unittest.TestCase):
    """测试经济上下文"""
    
    def setUp(self):
        self.mock_data_manager = Mock()
        self.mock_indicator_engine = Mock()
        self.mock_portfolio = Mock()
        self.mock_economic_data_manager = Mock()
        
        self.context = EconomicContext(
            data_manager=self.mock_data_manager,
            indicator_engine=self.mock_indicator_engine,
            portfolio=self.mock_portfolio,
            economic_data_manager=self.mock_economic_data_manager
        )
    
    def test_get_economic_data(self):
        """测试获取经济数据"""
        # 模拟经济数据
        mock_economic_data = [
            EconomicData(
                series_id='GDP',
                timestamp=datetime(2023, 1, 1),
                value=100.0,
                market='ECONOMIC',
                source='FRED'
            ),
            EconomicData(
                series_id='GDP',
                timestamp=datetime(2023, 2, 1),
                value=101.0,
                market='ECONOMIC',
                source='FRED'
            )
        ]
        
        self.mock_economic_data_manager.get_economic_data.return_value = mock_economic_data
        
        # 测试获取数据
        result = self.context.get_economic_data('GDP', 365)
        
        self.assertFalse(result.empty)
        self.assertEqual(len(result), 2)
        self.assertIn('value', result.columns)
    
    def test_get_economic_trend(self):
        """测试经济趋势分析"""
        # 模拟上升趋势数据
        mock_data = pd.DataFrame({
            'value': [100, 101, 102, 103, 104],
            'series_id': ['GDP'] * 5,
            'unit': ['Billions'] * 5,
            'frequency': ['Quarterly'] * 5
        }, index=pd.date_range('2023-01-01', periods=5, freq='M'))
        
        with patch.object(self.context, 'get_economic_data', return_value=mock_data):
            trend = self.context.get_economic_trend('GDP', 90)
            self.assertEqual(trend, 'RISING')
    
    def test_get_economic_cycle_phase(self):
        """测试经济周期识别"""
        # 模拟经济数据
        gdp_data = pd.DataFrame({
            'value': [100, 101, 102, 103],
            'series_id': ['GDPC1'] * 4
        }, index=pd.date_range('2023-01-01', periods=4, freq='Q'))
        
        unemployment_data = pd.DataFrame({
            'value': [5.0, 4.8, 4.5, 4.2],
            'series_id': ['UNRATE'] * 4
        }, index=pd.date_range('2023-01-01', periods=4, freq='M'))
        
        inflation_data = pd.DataFrame({
            'value': [250, 252, 254, 256],
            'series_id': ['CPIAUCSL'] * 4
        }, index=pd.date_range('2023-01-01', periods=4, freq='M'))
        
        with patch.object(self.context, 'get_economic_data') as mock_get_data:
            def side_effect(series_id, *args, **kwargs):
                if series_id == 'GDPC1':
                    return gdp_data
                elif series_id == 'UNRATE':
                    return unemployment_data
                elif series_id == 'CPIAUCSL':
                    return inflation_data
                return pd.DataFrame()
            
            mock_get_data.side_effect = side_effect
            
            with patch.object(self.context, 'get_economic_trend') as mock_trend:
                mock_trend.side_effect = lambda series_id, days: {
                    'GDPC1': 'RISING',
                    'UNRATE': 'FALLING'
                }.get(series_id, 'STABLE')
                
                cycle_phase = self.context.get_economic_cycle_phase()
                self.assertEqual(cycle_phase, 'EXPANSION')


class TestAssetAllocationStrategy(unittest.TestCase):
    """测试资产配置策略"""
    
    def setUp(self):
        self.strategy = AssetAllocationStrategy()
        self.mock_context = Mock(spec=EconomicContext)
        self.strategy.set_context(self.mock_context)
    
    def test_get_economic_indicators(self):
        """测试获取经济指标列表"""
        indicators = self.strategy.get_economic_indicators()
        
        self.assertIsInstance(indicators, list)
        self.assertIn('GDPC1', indicators)
        self.assertIn('UNRATE', indicators)
        self.assertIn('CPIAUCSL', indicators)
    
    def test_analyze_economic_conditions(self):
        """测试经济状况分析"""
        # 模拟经济数据
        mock_economic_data = {
            'GDPC1': pd.DataFrame({'value': [100, 101, 102]}, 
                                index=pd.date_range('2023-01-01', periods=3, freq='Q')),
            'UNRATE': pd.DataFrame({'value': [5.0, 4.8, 4.5]}, 
                                 index=pd.date_range('2023-01-01', periods=3, freq='M')),
            'CPIAUCSL': pd.DataFrame({'value': [250, 252, 254]}, 
                                   index=pd.date_range('2023-01-01', periods=3, freq='M'))
        }
        
        # 模拟上下文方法
        self.mock_context.get_economic_cycle_phase.return_value = 'EXPANSION'
        self.mock_context.get_economic_trend.return_value = 'RISING'
        
        analysis = self.strategy.analyze_economic_conditions(mock_economic_data)
        
        self.assertIn('cycle_phase', analysis)
        self.assertIn('recommended_weights', analysis)
        self.assertEqual(analysis['cycle_phase'], 'EXPANSION')
    
    def test_generate_allocation_signals(self):
        """测试资产配置信号生成"""
        # 模拟市场数据
        market_data = MarketData(
            symbol='SPY',
            timestamp=datetime.now(),
            open=400.0,
            high=405.0,
            low=395.0,
            close=402.0,
            volume=1000000
        )
        
        # 模拟经济分析结果
        economic_analysis = {
            'cycle_phase': 'EXPANSION',
            'recommended_weights': {
                'stocks': 0.7,
                'bonds': 0.2,
                'commodities': 0.1
            },
            'confidence_score': 0.8
        }
        
        # 模拟当前权重
        self.strategy.current_weights = {'SPY': 0.6}
        
        signals = self.strategy._generate_allocation_signals(market_data, economic_analysis)
        
        self.assertIsInstance(signals, list)
        if signals:
            signal = signals[0]
            self.assertEqual(signal.symbol, 'SPY')
            self.assertIn(signal.signal_type.value, ['BUY', 'SELL'])


class TestEconomicCycleStrategy(unittest.TestCase):
    """测试经济周期策略"""
    
    def setUp(self):
        self.strategy = EconomicCycleStrategy()
        self.mock_context = Mock(spec=EconomicContext)
        self.strategy.set_context(self.mock_context)
    
    def test_analyze_leading_indicators(self):
        """测试先行指标分析"""
        # 模拟经济数据
        mock_economic_data = {
            'HOUST': pd.DataFrame({'value': [1500, 1520, 1540]}, 
                                index=pd.date_range('2023-01-01', periods=3, freq='M')),
            'SP500': pd.DataFrame({'value': [4000, 4100, 4200]}, 
                                index=pd.date_range('2023-01-01', periods=3, freq='D')),
            'DGS10': pd.DataFrame({'value': [3.5, 3.6, 3.7]}, 
                                index=pd.date_range('2023-01-01', periods=3, freq='D')),
            'DGS2': pd.DataFrame({'value': [3.0, 3.1, 3.2]}, 
                               index=pd.date_range('2023-01-01', periods=3, freq='D'))
        }
        
        self.mock_context.get_economic_trend.return_value = 'RISING'
        
        leading_analysis = self.strategy._analyze_leading_indicators(mock_economic_data)
        
        self.assertIsInstance(leading_analysis, dict)
        if 'housing_starts' in leading_analysis:
            self.assertIn('trend', leading_analysis['housing_starts'])
            self.assertIn('signal', leading_analysis['housing_starts'])
    
    def test_calculate_transition_probabilities(self):
        """测试周期转换概率计算"""
        leading_indicators = {
            'housing_starts': {'signal': 'POSITIVE'},
            'stock_market': {'signal': 'POSITIVE'},
            'consumer_confidence': {'signal': 'NEGATIVE'}
        }
        
        probabilities = self.strategy._calculate_transition_probabilities(
            'EXPANSION', leading_indicators
        )
        
        self.assertIsInstance(probabilities, dict)
        if probabilities:
            # 概率和应该接近1
            total_prob = sum(probabilities.values())
            self.assertAlmostEqual(total_prob, 1.0, places=2)


class TestInflationHedgeStrategy(unittest.TestCase):
    """测试通胀对冲策略"""
    
    def setUp(self):
        self.strategy = InflationHedgeStrategy()
        self.mock_context = Mock(spec=EconomicContext)
        self.strategy.set_context(self.mock_context)
    
    def test_calculate_current_inflation(self):
        """测试当前通胀率计算"""
        # 模拟CPI数据
        cpi_data = {
            'CPIAUCSL': pd.DataFrame({
                'value': [250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262]
            }, index=pd.date_range('2023-01-01', periods=13, freq='M'))
        }
        
        inflation_rate = self.strategy._calculate_current_inflation(cpi_data)
        
        # 预期通胀率约为 (262-261)/261 * 100 = 0.38% (年化约4.38%)
        # 实际计算是最后12个月的变化：(262-250)/250 * 100 = 4.8%
        # 但由于索引从0开始，实际是(262-251)/251 * 100 ≈ 4.38%
        self.assertAlmostEqual(inflation_rate, 4.38, places=1)
    
    def test_classify_inflation_regime(self):
        """测试通胀环境分类"""
        # 测试不同通胀率的分类
        self.assertEqual(self.strategy._classify_inflation_regime(-1.0), 'DEFLATION')
        self.assertEqual(self.strategy._classify_inflation_regime(1.5), 'LOW_INFLATION')
        self.assertEqual(self.strategy._classify_inflation_regime(3.0), 'MODERATE_INFLATION')
        self.assertEqual(self.strategy._classify_inflation_regime(5.0), 'HIGH_INFLATION')
        self.assertEqual(self.strategy._classify_inflation_regime(12.0), 'HYPERINFLATION')
    
    def test_generate_hedge_recommendations(self):
        """测试对冲建议生成"""
        analysis = {
            'inflation_regime': 'HIGH_INFLATION',
            'inflation_trend': 'ACCELERATING',
            'commodity_pressure': 'HIGH',
            'wage_pressure': 'HIGH'
        }
        
        recommendations = self.strategy._generate_hedge_recommendations(analysis)
        
        self.assertIsInstance(recommendations, dict)
        self.assertIn('commodities', recommendations)
        
        if 'commodities' in recommendations:
            commodity_rec = recommendations['commodities']
            self.assertIn('recommended_weight', commodity_rec)
            self.assertIn('symbols', commodity_rec)
            # 高通胀环境下商品权重应该较高
            self.assertGreater(commodity_rec['recommended_weight'], 0.1)


class TestEconomicCorrelationAnalyzer(unittest.TestCase):
    """测试经济相关性分析器"""
    
    def setUp(self):
        self.mock_context = Mock(spec=EconomicContext)
        self.analyzer = EconomicCorrelationAnalyzer(self.mock_context)
    
    def test_calculate_correlation_matrix(self):
        """测试相关性矩阵计算"""
        # 模拟数据
        market_data = {
            'SPY': pd.DataFrame({
                'close': [400, 401, 402, 403, 404]
            }, index=pd.date_range('2023-01-01', periods=5, freq='D'))
        }
        
        economic_data = {
            'GDP': pd.DataFrame({
                'value': [100, 101, 102, 103, 104]
            }, index=pd.date_range('2023-01-01', periods=5, freq='D'))
        }
        
        self.mock_context.get_multiple_economic_data.return_value = economic_data
        self.mock_context.get_historical_data.return_value = market_data['SPY']
        
        correlation_matrix = self.analyzer.calculate_correlation_matrix(
            ['SPY'], ['GDP'], 365
        )
        
        self.assertIsInstance(correlation_matrix, pd.DataFrame)
        self.assertEqual(correlation_matrix.shape, (1, 1))
    
    def test_classify_correlation_strength(self):
        """测试相关性强度分类"""
        self.assertEqual(self.analyzer._classify_correlation_strength(0.9), 'VERY_STRONG')
        self.assertEqual(self.analyzer._classify_correlation_strength(0.7), 'STRONG')
        self.assertEqual(self.analyzer._classify_correlation_strength(0.5), 'MODERATE')
        self.assertEqual(self.analyzer._classify_correlation_strength(0.3), 'WEAK')
        self.assertEqual(self.analyzer._classify_correlation_strength(0.1), 'VERY_WEAK')


class TestEconomicEventAnalyzer(unittest.TestCase):
    """测试经济事件分析器"""
    
    def setUp(self):
        self.mock_context = Mock(spec=EconomicContext)
        self.analyzer = EconomicEventAnalyzer(self.mock_context)
    
    def test_detect_significant_changes(self):
        """测试显著变化检测"""
        # 模拟数据，包含显著变化
        data = pd.DataFrame({
            'value': [2.0, 2.1, 2.0, 2.5, 2.4]  # 2.0到2.5是25%的变化
        }, index=pd.date_range('2023-01-01', periods=5, freq='D'))
        
        changes = self.analyzer._detect_significant_changes(data, 0.2)  # 20%阈值
        
        self.assertIsInstance(changes, list)
        self.assertGreater(len(changes), 0)
        
        if changes:
            change = changes[0]
            self.assertIn('date', change)
            self.assertIn('change', change)
            self.assertIn('significance', change)
    
    def test_calculate_market_impact(self):
        """测试市场影响计算"""
        pre_data = pd.DataFrame({
            'close': [100, 101, 102]
        }, index=pd.date_range('2023-01-01', periods=3, freq='D'))
        
        post_data = pd.DataFrame({
            'close': [102, 105, 108]
        }, index=pd.date_range('2023-01-04', periods=3, freq='D'))
        
        impact = self.analyzer._calculate_market_impact(pre_data, post_data)
        
        self.assertIsInstance(impact, dict)
        self.assertIn('pre_event_return', impact)
        self.assertIn('post_event_return', impact)
        self.assertIn('impact_magnitude', impact)
        self.assertIn('direction', impact)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_strategy_workflow(self):
        """测试策略完整工作流程"""
        # 创建策略
        strategy = AssetAllocationStrategy()
        
        # 创建模拟上下文
        mock_context = Mock(spec=EconomicContext)
        mock_context.get_multiple_economic_data.return_value = {
            'GDPC1': pd.DataFrame({'value': [100, 101, 102]}, 
                                index=pd.date_range('2023-01-01', periods=3, freq='Q')),
            'UNRATE': pd.DataFrame({'value': [5.0, 4.8, 4.5]}, 
                                 index=pd.date_range('2023-01-01', periods=3, freq='M'))
        }
        mock_context.get_economic_cycle_phase.return_value = 'EXPANSION'
        mock_context.get_economic_trend.return_value = 'RISING'
        mock_context.get_position.return_value = 0.0
        
        strategy.set_context(mock_context)
        
        # 创建市场数据
        market_data = MarketData(
            symbol='SPY',
            timestamp=datetime.now(),
            open=400.0,
            high=405.0,
            low=395.0,
            close=402.0,
            volume=1000000
        )
        
        # 执行策略
        signals = strategy.on_data(market_data)
        
        # 验证结果
        self.assertIsInstance(signals, list)
        # 在扩张期应该有买入信号
        if signals:
            self.assertIn(signals[0].signal_type.value, ['BUY', 'SELL'])


if __name__ == '__main__':
    unittest.main()