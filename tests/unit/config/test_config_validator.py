import logging
logger = logging.getLogger(__name__)
"""
Tests for Configuration Validator
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock

from config.config_validator import (
    ConfigValidator, ValidationRule, DependencyRule, FieldType
)
from config.configuration_manager import ValidationResult, ValidationSeverity


class TestConfigValidator:
    """Test cases for ConfigValidator."""
    
    @pytest.fixture
    def validator(self):
        """Create ConfigValidator instance."""
        return ConfigValidator()
    
    def test_initialization(self, validator):
        """Test validator initialization."""
        assert validator is not None
        assert len(validator._validation_rules) > 0
        assert len(validator._dependency_rules) > 0
    
    def test_add_validation_rule(self, validator):
        """Test adding custom validation rule."""
        rule = ValidationRule(
            field_path="test.field",
            field_type=FieldType.STRING,
            required=True,
            description="Test field"
        )
        
        validator.add_validation_rule("test_section", rule)
        
        assert "test_section" in validator._validation_rules
        assert rule in validator._validation_rules["test_section"]
    
    def test_add_dependency_rule(self, validator):
        """Test adding custom dependency rule."""
        rule = DependencyRule(
            source_field="source",
            target_field="target",
            condition="required_if",
            condition_value=True,
            description="Test dependency"
        )
        
        validator.add_dependency_rule("test_section", rule)
        
        assert "test_section" in validator._dependency_rules
        assert rule in validator._dependency_rules["test_section"]
    
    def test_validate_config_empty(self, validator):
        """Test validating empty configuration."""
        result = validator.validate_config("system", {})
        
        assert isinstance(result, ValidationResult)
        assert not result.is_valid  # Should have errors for missing required fields
        assert len(result.errors) > 0
    
    def test_validate_config_valid_system(self, validator):
        """Test validating valid system configuration."""
        config_data = {
            "database": {
                "host": "localhost",
                "port": 5432,
                "database": "test_db",
                "username": "test_user",
                "password": "test_pass",
                "pool_size": 10,
                "echo": False
            },
            "api": {
                "host": "0.0.0.0",
                "port": 8000,
                "debug": False,
                "cors_origins": ["*"],
                "rate_limit": "100/minute",
                "auth_enabled": True,
                "jwt_secret": "a" * 32,  # 32 character secret
                "request_timeout": 30
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file_path": "logs/test.log",
                "max_file_size": 10485760,
                "backup_count": 5
            },
            "security": {
                "secret_key": "b" * 32,  # 32 character secret
                "algorithm": "HS256",
                "access_token_expire_minutes": 30,
                "password_min_length": 8,
                "max_login_attempts": 5
            }
        }
        
        result = validator.validate_config("system", config_data)
        
        # Should be valid or have only warnings/info
        if not result.is_valid:
            logger.info("Validation errors:", result.errors)
        
        # Check that there are no critical errors
        critical_errors = [e for e in result.errors if "Required field" in e]
        assert len(critical_errors) == 0
    
    def test_validate_config_invalid_types(self, validator):
        """Test validating configuration with invalid types."""
        config_data = {
            "database": {
                "host": "localhost",
                "port": "invalid_port",  # Should be integer
                "database": 123,  # Should be string
                "username": "test_user"
            }
        }
        
        result = validator.validate_config("system", config_data)
        
        assert not result.is_valid
        assert any("invalid type" in error.lower() for error in result.errors)
    
    def test_validate_config_missing_required(self, validator):
        """Test validating configuration with missing required fields."""
        config_data = {
            "database": {
                "host": "localhost"
                # Missing required fields: port, database, username
            }
        }
        
        result = validator.validate_config("system", config_data)
        
        assert not result.is_valid
        assert any("Required field" in error for error in result.errors)
    
    def test_validate_config_invalid_values(self, validator):
        """Test validating configuration with invalid values."""
        config_data = {
            "database": {
                "host": "localhost",
                "port": 99999,  # Invalid port (too high)
                "database": "test_db",
                "username": "test_user"
            },
            "logging": {
                "level": "INVALID_LEVEL"  # Invalid log level
            }
        }
        
        result = validator.validate_config("system", config_data)
        
        assert not result.is_valid
        assert len(result.errors) > 0
    
    def test_validate_dependencies(self, validator):
        """Test validating configuration dependencies."""
        all_configs = {
            "system": {
                "database": {
                    "host": "localhost",
                    "port": 5432,
                    "database": "test_db",
                    "username": "test_user"
                },
                "api": {
                    "host": "0.0.0.0",
                    "port": 8000,
                    "auth_enabled": True
                    # Missing jwt_secret when auth is enabled
                }
            }
        }
        
        result = validator.validate_dependencies(all_configs)
        
        assert not result.is_valid
        assert any("JWT secret" in error for error in result.errors)
    
    def test_validate_production_security(self, validator):
        """Test validating security configuration for production."""
        all_configs = {
            "environment": {
                "environment": "production"
            },
            "system": {
                "security": {
                    "secret_key": "short"  # Too short for production
                },
                "api": {
                    "debug": True  # Should be False in production
                }
            }
        }
        
        result = validator.validate_dependencies(all_configs)
        
        assert not result.is_valid
        assert any("production" in error.lower() for error in result.errors)
    
    def test_generate_fix_suggestions(self, validator):
        """Test generating fix suggestions."""
        result = ValidationResult(is_valid=False)
        result.errors = [
            "Required field 'database.host' is missing",
            "Field 'api.port' has invalid type. Expected integer, got string",
            "Field 'logging.level' is not in allowed values: ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']"
        ]
        
        suggestions = validator.generate_fix_suggestions(result)
        
        assert len(suggestions) == 3
        assert any("Add the required field" in suggestion for suggestion in suggestions)
        assert any("Convert the field value to integer" in suggestion for suggestion in suggestions)
        assert any("Use one of the allowed values" in suggestion for suggestion in suggestions)
    
    def test_validate_file_format_yaml(self, validator):
        """Test validating YAML file format."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("""
database:
  host: localhost
  port: 5432
""")
            f.flush()
            
            result = validator.validate_file_format(Path(f.name))
            
            assert result.is_valid
            assert any("YAML format is valid" in info for info in result.info)
        
        Path(f.name).unlink()  # Clean up
    
    def test_validate_file_format_invalid_yaml(self, validator):
        """Test validating invalid YAML file format."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("""
database:
  host: localhost
  port: 5432
    invalid_indentation: true
""")
            f.flush()
            
            result = validator.validate_file_format(Path(f.name))
            
            assert not result.is_valid
            assert any("Invalid YAML format" in error for error in result.errors)
        
        Path(f.name).unlink()  # Clean up
    
    def test_validate_file_format_json(self, validator):
        """Test validating JSON file format."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write('{"database": {"host": "localhost", "port": 5432}}')
            f.flush()
            
            result = validator.validate_file_format(Path(f.name))
            
            assert result.is_valid
            assert any("JSON format is valid" in info for info in result.info)
        
        Path(f.name).unlink()  # Clean up
    
    def test_validate_file_format_invalid_json(self, validator):
        """Test validating invalid JSON file format."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write('{"database": {"host": "localhost", "port": 5432}')  # Missing closing brace
            f.flush()
            
            result = validator.validate_file_format(Path(f.name))
            
            assert not result.is_valid
            assert any("Invalid JSON format" in error for error in result.errors)
        
        Path(f.name).unlink()  # Clean up
    
    def test_validate_file_format_env(self, validator):
        """Test validating .env file format."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
            f.write("""
DATABASE_HOST=localhost
DATABASE_PORT=5432
# This is a comment
API_KEY=secret_key
""")
            f.flush()
            
            result = validator.validate_file_format(Path(f.name))
            
            assert result.is_valid
            assert any(".env format is valid" in info for info in result.info)
        
        Path(f.name).unlink()  # Clean up
    
    def test_validate_file_format_invalid_env(self, validator):
        """Test validating invalid .env file format."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
            f.write("""
DATABASE_HOST=localhost
INVALID_LINE_WITHOUT_EQUALS
API_KEY=secret_key
""")
            f.flush()
            
            result = validator.validate_file_format(Path(f.name))
            
            assert not result.is_valid
            assert any("Invalid .env format" in error for error in result.errors)
        
        Path(f.name).unlink()  # Clean up
    
    def test_validate_file_not_exists(self, validator):
        """Test validating non-existent file."""
        result = validator.validate_file_format(Path("/nonexistent/file.yaml"))
        
        assert not result.is_valid
        assert any("does not exist" in error for error in result.errors)


class TestValidationRule:
    """Test cases for ValidationRule."""
    
    def test_initialization(self):
        """Test ValidationRule initialization."""
        rule = ValidationRule(
            field_path="test.field",
            field_type=FieldType.STRING,
            required=True,
            description="Test field"
        )
        
        assert rule.field_path == "test.field"
        assert rule.field_type == FieldType.STRING
        assert rule.required is True
        assert rule.description == "Test field"
        assert rule.dependencies == []


class TestDependencyRule:
    """Test cases for DependencyRule."""
    
    def test_initialization(self):
        """Test DependencyRule initialization."""
        rule = DependencyRule(
            source_field="source",
            target_field="target",
            condition="required_if",
            condition_value=True,
            description="Test dependency"
        )
        
        assert rule.source_field == "source"
        assert rule.target_field == "target"
        assert rule.condition == "required_if"
        assert rule.condition_value is True
        assert rule.description == "Test dependency"


class TestFieldType:
    """Test cases for FieldType enum."""
    
    def test_field_types(self):
        """Test FieldType enum values."""
        assert FieldType.STRING.value == "string"
        assert FieldType.INTEGER.value == "integer"
        assert FieldType.FLOAT.value == "float"
        assert FieldType.BOOLEAN.value == "boolean"
        assert FieldType.LIST.value == "list"
        assert FieldType.DICT.value == "dict"
        assert FieldType.PATH.value == "path"
        assert FieldType.URL.value == "url"
        assert FieldType.EMAIL.value == "email"
        assert FieldType.PORT.value == "port"
        assert FieldType.IP_ADDRESS.value == "ip_address"