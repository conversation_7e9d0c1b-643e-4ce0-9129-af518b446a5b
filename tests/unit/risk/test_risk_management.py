"""
Unit tests for risk management system.
"""

import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import pandas as pd
import numpy as np

from src.risk.manager import RiskManager
from src.risk.models import RiskConfig, RiskViolation, RiskLevel, StopLossOrder, TakeProfitOrder
from src.risk.rules import PositionSizeRule, StopLossRule, DrawdownRule, RiskBudgetRule
from src.risk.monitors import DrawdownMonitor, PositionMonitor, RiskBudgetMonitor
from src.market.strategies.models.portfolio import Portfolio, Position
from src.market.strategies.models.trading import Order, OrderSide, OrderType, Signal, SignalAction


class TestRiskConfig(unittest.TestCase):
    """Test risk configuration."""
    
    def test_default_config(self):
        """Test default risk configuration."""
        config = RiskConfig()
        
        self.assertEqual(config.max_position_size_pct, 0.1)
        self.assertEqual(config.max_portfolio_risk_pct, 0.02)
        self.assertEqual(config.default_stop_loss_pct, 0.05)
        self.assertEqual(config.max_drawdown_pct, 0.15)
        self.assertTrue(config.enable_drawdown_protection)
    
    def test_config_validation(self):
        """Test configuration validation."""
        # Valid config
        config = RiskConfig()
        self.assertTrue(config.validate())
        
        # Invalid position size
        config.max_position_size_pct = -0.1
        self.assertFalse(config.validate())
        
        # Invalid drawdown
        config.max_position_size_pct = 0.1
        config.max_drawdown_pct = -0.1
        self.assertFalse(config.validate())
        
        # Invalid leverage
        config.max_drawdown_pct = 0.15
        config.max_leverage = -1.0
        self.assertFalse(config.validate())


class TestPositionSizeRule(unittest.TestCase):
    """Test position size rule."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.rule = PositionSizeRule()
        self.config = RiskConfig()
        self.portfolio = Portfolio(initial_capital=100000, cash=50000)
        
        # Add some positions
        self.portfolio.positions['AAPL'] = Position(
            symbol='AAPL',
            quantity=100,
            avg_price=150.0,
            market_value=15000
        )
    
    def test_check_order_insufficient_cash(self):
        """Test insufficient cash check."""
        order = Order(
            id='test_order',
            symbol='MSFT',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=1000,
            price=200.0  # Requires $200,000 but only have $50,000 cash
        )
        
        violation = self.rule.check_order(order, self.portfolio, self.config)
        
        self.assertIsNotNone(violation)
        self.assertEqual(violation.violation_type, 'insufficient_cash')
        self.assertEqual(violation.severity, RiskLevel.CRITICAL)
    
    def test_check_order_position_size_limit(self):
        """Test position size limit check."""
        # Portfolio value = 50000 cash + 15000 position = 65000
        # Max position size = 10% = 6500
        # Order for $20,000 would exceed limit
        
        order = Order(
            id='test_order',
            symbol='MSFT',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100,
            price=200.0  # $20,000 order
        )
        
        violation = self.rule.check_order(order, self.portfolio, self.config)
        
        self.assertIsNotNone(violation)
        self.assertEqual(violation.violation_type, 'max_position_size')
        self.assertEqual(violation.severity, RiskLevel.HIGH)
    
    def test_check_order_short_selling(self):
        """Test short selling prevention."""
        order = Order(
            id='test_order',
            symbol='GOOGL',  # Not in portfolio
            side=OrderSide.SELL,
            order_type=OrderType.MARKET,
            quantity=10,
            price=2000.0
        )
        
        violation = self.rule.check_order(order, self.portfolio, self.config)
        
        self.assertIsNotNone(violation)
        self.assertEqual(violation.violation_type, 'short_selling_not_allowed')
        self.assertEqual(violation.severity, RiskLevel.CRITICAL)
    
    def test_check_portfolio_leverage(self):
        """Test portfolio leverage check."""
        # Add large position to exceed leverage
        self.portfolio.positions['TSLA'] = Position(
            symbol='TSLA',
            quantity=1000,
            avg_price=800.0,
            market_value=800000  # Very large position
        )
        
        violations = self.rule.check_portfolio(self.portfolio, self.config)
        
        # Should have violations for both position size and leverage
        self.assertGreater(len(violations), 0)
        
        violation_types = [v.violation_type for v in violations]
        self.assertIn('max_leverage', violation_types)


class TestStopLossRule(unittest.TestCase):
    """Test stop loss rule."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.rule = StopLossRule()
        self.config = RiskConfig()
        self.portfolio = Portfolio(initial_capital=100000, cash=50000)
        
        # Add position
        self.portfolio.positions['AAPL'] = Position(
            symbol='AAPL',
            quantity=100,
            avg_price=150.0,
            market_value=15000
        )
    
    def test_add_stop_loss(self):
        """Test adding stop loss order."""
        result = self.rule.add_stop_loss(
            symbol='AAPL',
            stop_price=140.0,
            quantity=100,
            stop_type='fixed'
        )
        
        self.assertEqual(result, 'AAPL')
        self.assertIn('AAPL', self.rule.stop_orders)
        
        stop_order = self.rule.stop_orders['AAPL']
        self.assertEqual(stop_order.stop_price, 140.0)
        self.assertEqual(stop_order.quantity, 100)
        self.assertEqual(stop_order.stop_type, 'fixed')
    
    def test_add_take_profit(self):
        """Test adding take profit order."""
        result = self.rule.add_take_profit(
            symbol='AAPL',
            target_price=180.0,
            quantity=100
        )
        
        self.assertEqual(result, 'AAPL')
        self.assertIn('AAPL', self.rule.take_profit_orders)
        
        tp_order = self.rule.take_profit_orders['AAPL']
        self.assertEqual(tp_order.target_price, 180.0)
        self.assertEqual(tp_order.quantity, 100)
    
    def test_stop_loss_trigger(self):
        """Test stop loss trigger detection."""
        # Add stop loss
        self.rule.add_stop_loss('AAPL', 140.0, 100)
        
        # Test trigger
        market_data = {'AAPL': 135.0}  # Below stop price
        triggers = self.rule.check_triggers(market_data)
        
        self.assertEqual(len(triggers), 1)
        self.assertEqual(triggers[0]['type'], 'stop_loss')
        self.assertEqual(triggers[0]['symbol'], 'AAPL')
        self.assertEqual(triggers[0]['current_price'], 135.0)
    
    def test_take_profit_trigger(self):
        """Test take profit trigger detection."""
        # Add take profit
        self.rule.add_take_profit('AAPL', 180.0, 100)
        
        # Test trigger
        market_data = {'AAPL': 185.0}  # Above target price
        triggers = self.rule.check_triggers(market_data)
        
        self.assertEqual(len(triggers), 1)
        self.assertEqual(triggers[0]['type'], 'take_profit')
        self.assertEqual(triggers[0]['symbol'], 'AAPL')
        self.assertEqual(triggers[0]['current_price'], 185.0)
    
    def test_trailing_stop(self):
        """Test trailing stop functionality."""
        # Add trailing stop
        self.rule.add_stop_loss('AAPL', 140.0, 100, 'trailing', trail_amount=10.0)
        
        stop_order = self.rule.stop_orders['AAPL']
        
        # Price goes up - should update stop
        updated = stop_order.update_trailing_stop(160.0)
        self.assertTrue(updated)
        self.assertEqual(stop_order.stop_price, 150.0)  # 160 - 10
        
        # Price goes down - should not update stop
        updated = stop_order.update_trailing_stop(155.0)
        self.assertFalse(updated)
        self.assertEqual(stop_order.stop_price, 150.0)  # Unchanged


class TestDrawdownRule(unittest.TestCase):
    """Test drawdown rule."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.rule = DrawdownRule()
        self.config = RiskConfig()
        self.portfolio = Portfolio(initial_capital=100000, cash=50000)
        
        # Set peak value
        self.rule.peak_value = 100000
    
    def test_drawdown_violation(self):
        """Test drawdown violation detection."""
        # Simulate portfolio loss
        self.portfolio.total_value = 80000  # 20% drawdown
        
        order = Order(
            id='test_order',
            symbol='AAPL',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=10,
            price=100.0
        )
        
        violation = self.rule.check_order(order, self.portfolio, self.config)
        
        self.assertIsNotNone(violation)
        self.assertEqual(violation.violation_type, 'max_drawdown_exceeded')
        self.assertEqual(violation.severity, RiskLevel.HIGH)
    
    def test_daily_loss_limit(self):
        """Test daily loss limit."""
        # Set daily start value
        self.rule.daily_start_value = 100000
        self.portfolio.total_value = 90000  # 10% daily loss (exceeds 5% limit)
        
        violations = self.rule.check_portfolio(self.portfolio, self.config)
        
        self.assertGreater(len(violations), 0)
        violation_types = [v.violation_type for v in violations]
        self.assertIn('daily_loss_limit', violation_types)
    
    def test_reset_peak(self):
        """Test peak value reset."""
        self.rule.reset_peak(120000)
        self.assertEqual(self.rule.peak_value, 120000)
        
        self.rule.reset_peak()
        self.assertEqual(self.rule.peak_value, 0.0)


class TestRiskBudgetRule(unittest.TestCase):
    """Test risk budget rule."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.rule = RiskBudgetRule()
        self.config = RiskConfig()
        self.portfolio = Portfolio(initial_capital=100000, cash=50000)
    
    def test_budget_initialization(self):
        """Test budget period initialization."""
        # Initialize budgets
        self.rule._initialize_budgets(self.config, 100000)
        
        self.assertIsNotNone(self.rule.daily_budget)
        self.assertIsNotNone(self.rule.weekly_budget)
        self.assertIsNotNone(self.rule.monthly_budget)
        
        self.assertEqual(self.rule.daily_budget.budget_amount, self.config.daily_risk_budget)
        self.assertEqual(self.rule.weekly_budget.budget_amount, self.config.weekly_risk_budget)
        self.assertEqual(self.rule.monthly_budget.budget_amount, self.config.monthly_risk_budget)
    
    def test_risk_usage_recording(self):
        """Test risk usage recording."""
        self.rule._initialize_budgets(self.config, 100000)
        
        # Record risk usage
        self.rule.record_risk_usage(0.01, 'AAPL', 'strategy_1')
        
        self.assertEqual(self.rule.daily_budget.used_amount, 0.01)
        self.assertEqual(self.rule.daily_budget.trades_count, 1)
    
    def test_budget_exceeded_violation(self):
        """Test budget exceeded violation."""
        self.rule._initialize_budgets(self.config, 100000)
        
        # Use up daily budget
        self.rule.daily_budget.used_amount = self.config.daily_risk_budget
        
        # Try to place order that would exceed budget
        order = Order(
            id='test_order',
            symbol='AAPL',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=100,
            price=100.0  # 1% of portfolio
        )
        
        violation = self.rule.check_order(order, self.portfolio, self.config)
        
        self.assertIsNotNone(violation)
        self.assertEqual(violation.violation_type, 'daily_risk_budget_exceeded')


class TestDrawdownMonitor(unittest.TestCase):
    """Test drawdown monitor."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = RiskConfig()
        self.monitor = DrawdownMonitor(self.config)
        self.portfolio = Portfolio(initial_capital=100000, cash=50000)
        self.portfolio.total_value = 100000
    
    def test_peak_tracking(self):
        """Test peak value tracking."""
        market_data = {}
        
        # Initial update
        self.monitor.update(self.portfolio, market_data)
        self.assertEqual(self.monitor.peak_value, 100000)
        
        # Portfolio increases
        self.portfolio.total_value = 110000
        self.monitor.update(self.portfolio, market_data)
        self.assertEqual(self.monitor.peak_value, 110000)
        
        # Portfolio decreases
        self.portfolio.total_value = 105000
        self.monitor.update(self.portfolio, market_data)
        self.assertEqual(self.monitor.peak_value, 110000)  # Peak unchanged
    
    def test_drawdown_calculation(self):
        """Test drawdown calculation."""
        market_data = {}
        
        # Set peak
        self.monitor.peak_value = 100000
        
        # Portfolio drops
        self.portfolio.total_value = 90000
        self.monitor.update(self.portfolio, market_data)
        
        self.assertEqual(self.monitor.current_drawdown, -0.1)  # 10% drawdown
    
    def test_get_status(self):
        """Test status reporting."""
        market_data = {}
        self.monitor.update(self.portfolio, market_data)
        
        status = self.monitor.get_status()
        
        self.assertIn('peak_value', status)
        self.assertIn('current_drawdown_pct', status)
        self.assertIn('max_drawdown_pct', status)
        self.assertIn('is_in_drawdown', status)


class TestRiskManager(unittest.TestCase):
    """Test risk manager."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = RiskConfig()
        self.risk_manager = RiskManager(self.config)
        self.portfolio = Portfolio(initial_capital=100000, cash=50000)
        
        # Add position
        self.portfolio.positions['AAPL'] = Position(
            symbol='AAPL',
            quantity=100,
            avg_price=150.0,
            market_value=15000
        )
        self.portfolio.total_value = 65000  # 50000 cash + 15000 position
    
    def test_initialization(self):
        """Test risk manager initialization."""
        self.assertIsNotNone(self.risk_manager.config)
        self.assertGreater(len(self.risk_manager.rules), 0)
        self.assertGreater(len(self.risk_manager.monitors), 0)
        self.assertFalse(self.risk_manager.emergency_stop_active)
    
    def test_check_order_allowed(self):
        """Test order checking - allowed order."""
        order = Order(
            id='test_order',
            symbol='MSFT',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=10,
            price=100.0  # Small order, should be allowed
        )
        
        allowed, violations = self.risk_manager.check_order(order, self.portfolio)
        
        self.assertTrue(allowed)
        self.assertEqual(len(violations), 0)
    
    def test_check_order_rejected(self):
        """Test order checking - rejected order."""
        order = Order(
            id='test_order',
            symbol='MSFT',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=1000,
            price=200.0  # Large order, should be rejected
        )
        
        allowed, violations = self.risk_manager.check_order(order, self.portfolio)
        
        self.assertFalse(allowed)
        self.assertGreater(len(violations), 0)
    
    def test_emergency_stop(self):
        """Test emergency stop activation."""
        # Simulate critical violations
        self.risk_manager.emergency_stop_active = True
        
        order = Order(
            id='test_order',
            symbol='AAPL',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=1,
            price=100.0
        )
        
        allowed, violations = self.risk_manager.check_order(order, self.portfolio)
        
        self.assertFalse(allowed)
        self.assertEqual(violations[0].violation_type, 'emergency_stop_active')
    
    def test_add_stop_loss(self):
        """Test adding stop loss."""
        result = self.risk_manager.add_stop_loss('AAPL', 140.0, 100)
        self.assertTrue(result)
        
        # Check if stop loss was added to rule
        stop_loss_rule = self.risk_manager.rules['stop_loss']
        self.assertIn('AAPL', stop_loss_rule.stop_orders)
    
    def test_add_take_profit(self):
        """Test adding take profit."""
        result = self.risk_manager.add_take_profit('AAPL', 180.0, 100)
        self.assertTrue(result)
        
        # Check if take profit was added to rule
        stop_loss_rule = self.risk_manager.rules['stop_loss']
        self.assertIn('AAPL', stop_loss_rule.take_profit_orders)
    
    def test_calculate_risk_metrics(self):
        """Test risk metrics calculation."""
        metrics = self.risk_manager.calculate_risk_metrics(self.portfolio)
        
        self.assertIsNotNone(metrics)
        self.assertEqual(metrics.portfolio_value, self.portfolio.total_value)
        self.assertEqual(metrics.total_positions, len(self.portfolio.positions))
        self.assertGreater(metrics.largest_position_pct, 0)
    
    def test_update_monitors(self):
        """Test monitor updates."""
        market_data = {'AAPL': 155.0}
        
        # Should not raise exception
        self.risk_manager.update_monitors(self.portfolio, market_data)
        
        # Check that monitors were updated
        for monitor in self.risk_manager.monitors.values():
            self.assertIsNotNone(monitor.last_update)
    
    def test_get_risk_summary(self):
        """Test risk summary generation."""
        summary = self.risk_manager.get_risk_summary()
        
        self.assertIn('timestamp', summary)
        self.assertIn('emergency_stop_active', summary)
        self.assertIn('rules', summary)
        self.assertIn('monitors', summary)
        self.assertIn('config', summary)
    
    def test_enable_disable_rules(self):
        """Test enabling/disabling rules."""
        # Disable rule
        result = self.risk_manager.disable_rule('position_size')
        self.assertTrue(result)
        self.assertFalse(self.risk_manager.rules['position_size'].is_enabled())
        
        # Enable rule
        result = self.risk_manager.enable_rule('position_size')
        self.assertTrue(result)
        self.assertTrue(self.risk_manager.rules['position_size'].is_enabled())
    
    def test_enable_disable_monitors(self):
        """Test enabling/disabling monitors."""
        # Disable monitor
        result = self.risk_manager.disable_monitor('drawdown')
        self.assertTrue(result)
        self.assertFalse(self.risk_manager.monitors['drawdown'].enabled)
        
        # Enable monitor
        result = self.risk_manager.enable_monitor('drawdown')
        self.assertTrue(result)
        self.assertTrue(self.risk_manager.monitors['drawdown'].enabled)
    
    def test_alert_callbacks(self):
        """Test alert callback functionality."""
        alerts_received = []
        
        def alert_callback(violation):
            alerts_received.append(violation)
        
        self.risk_manager.add_alert_callback(alert_callback)
        
        # Create order that will trigger violation
        order = Order(
            id='test_order',
            symbol='MSFT',
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=1000,
            price=200.0
        )
        
        self.risk_manager.check_order(order, self.portfolio)
        
        # Should have received alerts
        self.assertGreater(len(alerts_received), 0)


if __name__ == '__main__':
    unittest.main()