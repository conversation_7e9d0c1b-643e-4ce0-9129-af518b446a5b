"""
后端修复管理器单元测试
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from src.core.monitoring.backend_fix_manager import (
    BackendFixManager, 
    BackendIssue, 
    FixType, 
    FixOperation
)
from src.monitoring.diagnostic_models import FixResult, FixStatus


class TestBackendFixManager:
    """后端修复管理器测试类"""
    
    @pytest.fixture
    def fix_manager(self):
        """创建后端修复管理器实例"""
        return BackendFixManager()
    
    @pytest.fixture
    def sample_backend_issue(self):
        """创建示例后端问题"""
        return BackendIssue(
            issue_id="test_issue_001",
            fix_type=FixType.API_ENDPOINT,
            severity="medium",
            description="API端点响应超时",
            component="api_endpoints",
            detected_at=datetime.now(),
            auto_fixable=True
        )
    
    def test_init(self, fix_manager):
        """测试初始化"""
        assert fix_manager.api_fixer is not None
        assert fix_manager.database_fixer is not None
        assert fix_manager.service_manager is not None
        assert fix_manager.fix_history == []
        assert fix_manager.active_fixes == {}
        assert fix_manager.performance_metrics['total_fixes'] == 0
    
    @pytest.mark.asyncio
    async def test_fix_api_endpoints_no_issues(self, fix_manager):
        """测试修复API端点 - 无问题情况"""
        with patch.object(fix_manager, '_detect_api_issues', return_value=[]):
            result = await fix_manager.fix_api_endpoints()
            
            assert result.component == "api_endpoints"
            assert result.status == FixStatus.SUCCESS
            assert result.message == "API端点运行正常"
            assert result.details["issues_found"] == 0
    
    @pytest.mark.asyncio
    async def test_fix_api_endpoints_with_issues(self, fix_manager, sample_backend_issue):
        """测试修复API端点 - 有问题情况"""
        mock_fix_result = FixResult(
            component="api_endpoints",
            status=FixStatus.SUCCESS,
            message="修复成功",
            details={"issue_id": "test_issue_001"},
            duration=0.1
        )
        
        with patch.object(fix_manager, '_detect_api_issues', return_value=[sample_backend_issue]):
            with patch.object(fix_manager, '_fix_api_issue', return_value=mock_fix_result):
                result = await fix_manager.fix_api_endpoints()
                
                assert result.component == "api_endpoints"
                assert result.status == FixStatus.SUCCESS
                assert "修复了 1/1 个API问题" in result.message
                assert result.details["total_issues"] == 1
                assert result.details["fixed_issues"] == 1
    
    @pytest.mark.asyncio
    async def test_fix_api_endpoints_exception(self, fix_manager):
        """测试修复API端点 - 异常情况"""
        with patch.object(fix_manager, '_detect_api_issues', side_effect=Exception("测试异常")):
            result = await fix_manager.fix_api_endpoints()
            
            assert result.component == "api_endpoints"
            assert result.status == FixStatus.FAILED
            assert "API端点修复失败" in result.message
            assert "测试异常" in result.details["error"]
    
    @pytest.mark.asyncio
    async def test_fix_database_connections_no_issues(self, fix_manager):
        """测试修复数据库连接 - 无问题情况"""
        with patch.object(fix_manager, '_detect_database_issues', return_value=[]):
            result = await fix_manager.fix_database_connections()
            
            assert result.component == "database_connections"
            assert result.status == FixStatus.SUCCESS
            assert result.message == "数据库连接正常"
            assert result.details["issues_found"] == 0
    
    @pytest.mark.asyncio
    async def test_fix_service_dependencies_no_issues(self, fix_manager):
        """测试修复服务依赖 - 无问题情况"""
        with patch.object(fix_manager, '_detect_service_dependency_issues', return_value=[]):
            result = await fix_manager.fix_service_dependencies()
            
            assert result.component == "service_dependencies"
            assert result.status == FixStatus.SUCCESS
            assert result.message == "服务依赖正常"
            assert result.details["issues_found"] == 0
    
    @pytest.mark.asyncio
    async def test_optimize_performance_no_issues(self, fix_manager):
        """测试性能优化 - 无问题情况"""
        with patch.object(fix_manager, '_analyze_performance_issues', return_value=[]):
            result = await fix_manager.optimize_performance()
            
            assert result.component == "performance_optimization"
            assert result.status == FixStatus.SUCCESS
            assert result.message == "性能状态良好"
            assert result.details["issues_found"] == 0
    
    @pytest.mark.asyncio
    async def test_run_comprehensive_fix(self, fix_manager):
        """测试全面修复"""
        # Mock所有修复方法
        mock_api_result = FixResult("api_endpoints", FixStatus.SUCCESS, "API修复成功", {}, 0.1)
        mock_db_result = FixResult("database_connections", FixStatus.SUCCESS, "数据库修复成功", {}, 0.1)
        mock_service_result = FixResult("service_dependencies", FixStatus.SUCCESS, "服务修复成功", {}, 0.1)
        mock_perf_result = FixResult("performance_optimization", FixStatus.SUCCESS, "性能优化成功", {}, 0.1)
        
        with patch.object(fix_manager, 'fix_api_endpoints', return_value=mock_api_result):
            with patch.object(fix_manager, 'fix_database_connections', return_value=mock_db_result):
                with patch.object(fix_manager, 'fix_service_dependencies', return_value=mock_service_result):
                    with patch.object(fix_manager, 'optimize_performance', return_value=mock_perf_result):
                        results = await fix_manager.run_comprehensive_fix()
                        
                        assert len(results) == 4
                        assert all(result.status == FixStatus.SUCCESS for result in results.values())
                        assert "api_endpoints" in results
                        assert "database_connections" in results
                        assert "service_dependencies" in results
                        assert "performance_optimization" in results
    
    def test_get_fix_history(self, fix_manager):
        """测试获取修复历史"""
        # 添加一些历史记录
        result1 = FixResult("test1", FixStatus.SUCCESS, "测试1", {}, 0.1)
        result2 = FixResult("test2", FixStatus.FAILED, "测试2", {}, 0.2)
        
        fix_manager.fix_history = [result1, result2]
        
        history = fix_manager.get_fix_history()
        assert len(history) == 2
        assert history[0].component == "test1"
        assert history[1].component == "test2"
        
        # 确保返回的是副本
        history.append(FixResult("test3", FixStatus.SUCCESS, "测试3", {}, 0.3))
        assert len(fix_manager.fix_history) == 2
    
    def test_get_performance_metrics(self, fix_manager):
        """测试获取性能指标"""
        # 设置一些指标
        fix_manager.performance_metrics = {
            'total_fixes': 10,
            'successful_fixes': 8,
            'failed_fixes': 2,
            'average_fix_time': 1.5
        }
        
        metrics = fix_manager.get_performance_metrics()
        assert metrics['total_fixes'] == 10
        assert metrics['successful_fixes'] == 8
        assert metrics['failed_fixes'] == 2
        assert metrics['average_fix_time'] == 1.5
        
        # 确保返回的是副本
        metrics['total_fixes'] = 20
        assert fix_manager.performance_metrics['total_fixes'] == 10
    
    def test_clear_fix_history(self, fix_manager):
        """测试清空修复历史"""
        # 添加一些历史记录
        result1 = FixResult("test1", FixStatus.SUCCESS, "测试1", {}, 0.1)
        fix_manager.fix_history = [result1]
        
        assert len(fix_manager.fix_history) == 1
        
        fix_manager.clear_fix_history()
        assert len(fix_manager.fix_history) == 0
    
    def test_update_metrics_success(self, fix_manager):
        """测试更新指标 - 成功情况"""
        result = FixResult("test", FixStatus.SUCCESS, "测试", {}, 2.0)
        
        fix_manager._update_metrics(result)
        
        assert fix_manager.performance_metrics['total_fixes'] == 1
        assert fix_manager.performance_metrics['successful_fixes'] == 1
        assert fix_manager.performance_metrics['failed_fixes'] == 0
        assert fix_manager.performance_metrics['average_fix_time'] == 2.0
    
    def test_update_metrics_failed(self, fix_manager):
        """测试更新指标 - 失败情况"""
        result = FixResult("test", FixStatus.FAILED, "测试", {}, 1.0)
        
        fix_manager._update_metrics(result)
        
        assert fix_manager.performance_metrics['total_fixes'] == 1
        assert fix_manager.performance_metrics['successful_fixes'] == 0
        assert fix_manager.performance_metrics['failed_fixes'] == 1
        assert fix_manager.performance_metrics['average_fix_time'] == 1.0
    
    def test_update_metrics_average_calculation(self, fix_manager):
        """测试更新指标 - 平均时间计算"""
        # 第一次修复
        result1 = FixResult("test1", FixStatus.SUCCESS, "测试1", {}, 2.0)
        fix_manager._update_metrics(result1)
        assert fix_manager.performance_metrics['average_fix_time'] == 2.0
        
        # 第二次修复
        result2 = FixResult("test2", FixStatus.SUCCESS, "测试2", {}, 4.0)
        fix_manager._update_metrics(result2)
        assert fix_manager.performance_metrics['average_fix_time'] == 3.0  # (2.0 + 4.0) / 2
        
        # 第三次修复
        result3 = FixResult("test3", FixStatus.SUCCESS, "测试3", {}, 0.0)
        fix_manager._update_metrics(result3)
        assert fix_manager.performance_metrics['average_fix_time'] == 2.0  # (2.0 + 4.0 + 0.0) / 3


class TestBackendIssue:
    """后端问题测试类"""
    
    def test_backend_issue_creation(self):
        """测试后端问题创建"""
        issue = BackendIssue(
            issue_id="test_001",
            fix_type=FixType.API_ENDPOINT,
            severity="high",
            description="测试问题",
            component="test_component",
            detected_at=datetime.now(),
            auto_fixable=False
        )
        
        assert issue.issue_id == "test_001"
        assert issue.fix_type == FixType.API_ENDPOINT
        assert issue.severity == "high"
        assert issue.description == "测试问题"
        assert issue.component == "test_component"
        assert issue.auto_fixable is False
    
    def test_backend_issue_default_auto_fixable(self):
        """测试后端问题默认可自动修复"""
        issue = BackendIssue(
            issue_id="test_001",
            fix_type=FixType.DATABASE_CONNECTION,
            severity="medium",
            description="测试问题",
            component="test_component",
            detected_at=datetime.now()
        )
        
        assert issue.auto_fixable is True


class TestFixOperation:
    """修复操作测试类"""
    
    def test_fix_operation_creation(self):
        """测试修复操作创建"""
        operation = FixOperation(
            operation_id="op_001",
            issue_id="issue_001",
            fix_type=FixType.PERFORMANCE_OPTIMIZATION,
            description="性能优化操作",
            steps=["步骤1", "步骤2", "步骤3"],
            estimated_duration=300,
            risk_level="low"
        )
        
        assert operation.operation_id == "op_001"
        assert operation.issue_id == "issue_001"
        assert operation.fix_type == FixType.PERFORMANCE_OPTIMIZATION
        assert operation.description == "性能优化操作"
        assert len(operation.steps) == 3
        assert operation.estimated_duration == 300
        assert operation.risk_level == "low"


if __name__ == "__main__":
    pytest.main([__file__])