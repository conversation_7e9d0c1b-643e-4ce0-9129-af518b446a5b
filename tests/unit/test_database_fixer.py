"""
数据库修复器单元测试
"""

import pytest
import asyncio
import tempfile
import os
import shutil
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
from pathlib import Path

from src.core.monitoring.database_fixer import (
    DatabaseFixer,
    DatabaseConfig,
    DatabaseIssue,
    DatabaseIssueType,
    QueryPerformanceMetrics,
    BackupInfo
)
from src.monitoring.diagnostic_models import FixResult, FixStatus


class TestDatabaseFixer:
    """数据库修复器测试类"""
    
    @pytest.fixture
    def temp_db_config(self):
        """创建临时数据库配置"""
        temp_dir = tempfile.mkdtemp()
        db_path = os.path.join(temp_dir, "test.db")
        
        # 创建一个空的数据库文件
        with open(db_path, 'w') as f:
            f.write("")
        
        config = DatabaseConfig(
            db_path=db_path,
            max_connections=5,
            connection_timeout=10.0,
            query_timeout=5.0
        )
        
        yield config
        
        # 清理临时文件
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def database_fixer(self, temp_db_config):
        """创建数据库修复器实例"""
        return DatabaseFixer(temp_db_config)
    
    @pytest.fixture
    def sample_database_issue(self):
        """创建示例数据库问题"""
        return DatabaseIssue(
            issue_id="test_issue_001",
            issue_type=DatabaseIssueType.SLOW_QUERY,
            severity="medium",
            description="查询执行时间过长",
            table_name="test_table",
            query="SELECT * FROM test_table",
            detected_at=datetime.now()
        )
    
    @pytest.fixture
    def sample_query_metric(self):
        """创建示例查询性能指标"""
        return QueryPerformanceMetrics(
            query="SELECT * FROM positions",
            execution_time=6.0,  # 超过阈值
            rows_affected=1000,
            table_name="positions"
        )
    
    def test_init_with_config(self, temp_db_config):
        """测试使用配置初始化"""
        fixer = DatabaseFixer(temp_db_config)
        
        assert fixer.config == temp_db_config
        assert fixer.connection_pool == []
        assert fixer.active_connections == 0
        assert len(fixer.query_metrics) == 0
        assert "slow_query_time" in fixer.performance_thresholds
    
    def test_init_without_config(self):
        """测试不使用配置初始化"""
        fixer = DatabaseFixer()
        
        assert fixer.config is not None
        assert fixer.config.db_path == "data/config/trading_system.db"
        assert fixer.config.max_connections == 10
    
    @pytest.mark.asyncio
    async def test_check_connection_pool_no_issues(self, database_fixer):
        """测试连接池检查 - 无问题情况"""
        with patch.object(database_fixer, '_check_connection_pool_issues', return_value=[]):
            result = await database_fixer.check_connection_pool()
            
            assert result.component == "database_connection_pool"
            assert result.status == FixStatus.SUCCESS
            assert result.message == "连接池状态正常"
            assert result.details["active_connections"] == 0
    
    @pytest.mark.asyncio
    async def test_check_connection_pool_with_issues(self, database_fixer, sample_database_issue):
        """测试连接池检查 - 有问题情况"""
        mock_fix_result = FixResult(
            component="connection_pool",
            status=FixStatus.SUCCESS,
            message="修复成功",
            details={"issue_id": "test_issue_001"},
            duration=0.1
        )
        
        with patch.object(database_fixer, '_check_connection_pool_issues', return_value=[sample_database_issue]):
            with patch.object(database_fixer, '_fix_connection_pool_issue', return_value=mock_fix_result):
                result = await database_fixer.check_connection_pool()
                
                assert result.component == "database_connection_pool"
                assert result.status == FixStatus.SUCCESS
                assert "修复了 1/1 个连接池问题" in result.message
                assert result.details["total_issues"] == 1
                assert result.details["fixed_issues"] == 1
    
    @pytest.mark.asyncio
    async def test_optimize_query_performance_no_issues(self, database_fixer):
        """测试查询性能优化 - 无问题情况"""
        with patch.object(database_fixer, '_analyze_query_performance', return_value=[]):
            result = await database_fixer.optimize_query_performance()
            
            assert result.component == "query_performance"
            assert result.status == FixStatus.SUCCESS
            assert result.message == "查询性能良好"
            assert result.details["slow_queries"] == 0
    
    @pytest.mark.asyncio
    async def test_check_data_integrity_no_issues(self, database_fixer):
        """测试数据完整性检查 - 无问题情况"""
        with patch.object(database_fixer, '_check_data_integrity_issues', return_value=[]):
            result = await database_fixer.check_data_integrity()
            
            assert result.component == "data_integrity"
            assert result.status == FixStatus.SUCCESS
            assert result.message == "数据完整性正常"
            assert result.details["integrity_violations"] == 0
    
    @pytest.mark.asyncio
    async def test_create_backup_success(self, database_fixer):
        """测试创建备份 - 成功情况"""
        with patch.object(database_fixer, '_verify_backup', return_value=True):
            with patch.object(database_fixer, '_cleanup_old_backups'):
                result = await database_fixer.create_backup()
                
                assert result.component == "database_backup"
                assert result.status == FixStatus.SUCCESS
                assert "数据库备份创建成功" in result.message
                assert "backup_path" in result.details
                assert "backup_size" in result.details
    
    @pytest.mark.asyncio
    async def test_create_backup_source_not_exists(self, database_fixer):
        """测试创建备份 - 源文件不存在"""
        # 删除源数据库文件
        os.remove(database_fixer.config.db_path)
        
        result = await database_fixer.create_backup()
        
        assert result.component == "database_backup"
        assert result.status == FixStatus.FAILED
        assert result.message == "源数据库文件不存在"
    
    @pytest.mark.asyncio
    async def test_create_backup_verification_failed(self, database_fixer):
        """测试创建备份 - 验证失败"""
        with patch.object(database_fixer, '_verify_backup', return_value=False):
            result = await database_fixer.create_backup()
            
            assert result.component == "database_backup"
            assert result.status == FixStatus.FAILED
            assert result.message == "备份验证失败"
    
    @pytest.mark.asyncio
    async def test_restore_from_backup_success(self, database_fixer, temp_db_config):
        """测试从备份恢复 - 成功情况"""
        # 创建一个临时备份文件
        backup_path = temp_db_config.db_path + ".backup"
        shutil.copy2(temp_db_config.db_path, backup_path)
        
        with patch.object(database_fixer, '_verify_backup', return_value=True):
            with patch.object(database_fixer, '_verify_database', return_value=True):
                with patch.object(database_fixer, 'create_backup') as mock_backup:
                    mock_backup.return_value = FixResult("backup", FixStatus.SUCCESS, "备份成功", {}, 0.1)
                    
                    result = await database_fixer.restore_from_backup(backup_path)
                    
                    assert result.component == "database_restore"
                    assert result.status == FixStatus.SUCCESS
                    assert "数据库恢复成功" in result.message
        
        # 清理备份文件
        if os.path.exists(backup_path):
            os.remove(backup_path)
    
    @pytest.mark.asyncio
    async def test_restore_from_backup_file_not_exists(self, database_fixer):
        """测试从备份恢复 - 备份文件不存在"""
        non_existent_path = "/path/to/non/existent/backup.db"
        
        result = await database_fixer.restore_from_backup(non_existent_path)
        
        assert result.component == "database_restore"
        assert result.status == FixStatus.FAILED
        assert result.message == "备份文件不存在"
    
    @pytest.mark.asyncio
    async def test_run_comprehensive_database_fix(self, database_fixer):
        """测试全面数据库修复"""
        # Mock所有修复方法
        mock_pool_result = FixResult("database_connection_pool", FixStatus.SUCCESS, "连接池修复成功", {}, 0.1)
        mock_query_result = FixResult("query_performance", FixStatus.SUCCESS, "查询优化成功", {}, 0.1)
        mock_integrity_result = FixResult("data_integrity", FixStatus.SUCCESS, "完整性检查成功", {}, 0.1)
        mock_backup_result = FixResult("database_backup", FixStatus.SUCCESS, "备份创建成功", {}, 0.1)
        
        with patch.object(database_fixer, 'check_connection_pool', return_value=mock_pool_result):
            with patch.object(database_fixer, 'optimize_query_performance', return_value=mock_query_result):
                with patch.object(database_fixer, 'check_data_integrity', return_value=mock_integrity_result):
                    with patch.object(database_fixer, 'create_backup', return_value=mock_backup_result):
                        results = await database_fixer.run_comprehensive_database_fix()
                        
                        assert len(results) == 4
                        assert all(result.status == FixStatus.SUCCESS for result in results.values())
                        assert "connection_pool" in results
                        assert "query_performance" in results
                        assert "data_integrity" in results
                        assert "backup" in results
    
    def test_add_query_metric(self, database_fixer):
        """测试添加查询性能指标"""
        query = "SELECT * FROM test_table"
        execution_time = 2.5
        rows_affected = 100
        table_name = "test_table"
        
        database_fixer.add_query_metric(query, execution_time, rows_affected, table_name)
        
        assert len(database_fixer.query_metrics) == 1
        metric = database_fixer.query_metrics[0]
        assert metric.query == query
        assert metric.execution_time == execution_time
        assert metric.rows_affected == rows_affected
        assert metric.table_name == table_name
    
    def test_add_query_metric_limit(self, database_fixer):
        """测试查询指标数量限制"""
        # 添加超过1000条记录
        for i in range(1100):
            database_fixer.add_query_metric(f"SELECT {i}", 1.0, 10, "test_table")
        
        # 应该只保留最近的1000条
        assert len(database_fixer.query_metrics) == 1000
        # 最后一条应该是最新的
        assert "SELECT 1099" in database_fixer.query_metrics[-1].query
    
    def test_cleanup_idle_connections(self, database_fixer):
        """测试清理空闲连接"""
        database_fixer.active_connections = 3
        
        database_fixer._cleanup_idle_connections()
        
        assert database_fixer.active_connections == 2
    
    def test_cleanup_idle_connections_zero(self, database_fixer):
        """测试清理空闲连接 - 零连接情况"""
        database_fixer.active_connections = 0
        
        database_fixer._cleanup_idle_connections()
        
        assert database_fixer.active_connections == 0
    
    @pytest.mark.asyncio
    async def test_check_connection_pool_issues_high_usage(self, database_fixer):
        """测试连接池问题检查 - 高使用率"""
        # 设置高连接数
        database_fixer.active_connections = 5  # 超过阈值 (5 * 0.8 = 4)
        
        issues = await database_fixer._check_connection_pool_issues()
        
        assert len(issues) == 1
        assert issues[0].issue_type == DatabaseIssueType.CONNECTION_POOL_EXHAUSTED
        assert "连接池使用率过高" in issues[0].description
    
    @pytest.mark.asyncio
    async def test_fix_connection_pool_issue_exhausted(self, database_fixer, sample_database_issue):
        """测试修复连接池问题 - 连接池耗尽"""
        sample_database_issue.issue_type = DatabaseIssueType.CONNECTION_POOL_EXHAUSTED
        database_fixer.active_connections = 3
        
        result = await database_fixer._fix_connection_pool_issue(sample_database_issue)
        
        assert result.component == "connection_pool"
        assert result.status == FixStatus.SUCCESS
        assert "已清理空闲连接" in result.message
        assert database_fixer.active_connections == 2
    
    @pytest.mark.asyncio
    async def test_analyze_query_performance_slow_queries(self, database_fixer, sample_query_metric):
        """测试查询性能分析 - 慢查询"""
        database_fixer.query_metrics = [sample_query_metric]
        
        with patch.object(database_fixer, '_check_missing_indexes', return_value={}):
            issues = await database_fixer._analyze_query_performance()
            
            assert len(issues) == 1
            assert issues[0].issue_type == DatabaseIssueType.SLOW_QUERY
            assert "慢查询检测" in issues[0].description
    
    @pytest.mark.asyncio
    async def test_check_missing_indexes(self, database_fixer):
        """测试检查缺失索引"""
        missing_indexes = await database_fixer._check_missing_indexes()
        
        assert isinstance(missing_indexes, dict)
        assert "positions" in missing_indexes
        assert "orders" in missing_indexes
        assert "market_data" in missing_indexes
        assert "signals" in missing_indexes
    
    @pytest.mark.asyncio
    async def test_optimize_slow_query(self, database_fixer):
        """测试优化慢查询"""
        query = "SELECT * FROM positions WHERE symbol = 'BTCUSDT'"
        table_name = "positions"
        
        result = await database_fixer._optimize_slow_query(query, table_name)
        
        assert result["original_query"] == query
        assert "optimization_type" in result
        assert "estimated_improvement" in result
    
    @pytest.mark.asyncio
    async def test_create_missing_index(self, database_fixer):
        """测试创建缺失索引"""
        table_name = "positions"
        indexes = ["symbol", "timestamp"]
        
        created_indexes = await database_fixer._create_missing_index(table_name, indexes)
        
        assert len(created_indexes) == 2
        assert "idx_positions_symbol" in created_indexes
        assert "idx_positions_timestamp" in created_indexes
    
    @pytest.mark.asyncio
    async def test_check_required_tables(self, database_fixer):
        """测试检查必需表"""
        missing_tables = await database_fixer._check_required_tables()
        
        # 基于模拟逻辑，signals表应该缺失
        assert "signals" in missing_tables
    
    @pytest.mark.asyncio
    async def test_check_foreign_key_violations(self, database_fixer):
        """测试检查外键约束违反"""
        violations = await database_fixer._check_foreign_key_violations()
        
        # 模拟逻辑返回空列表
        assert isinstance(violations, list)
    
    @pytest.mark.asyncio
    async def test_repair_data_corruption(self, database_fixer):
        """测试修复数据损坏"""
        table_name = "test_table"
        
        result = await database_fixer._repair_data_corruption(table_name)
        
        assert result["table_name"] == table_name
        assert "corruption_type" in result
        assert "repair_action" in result
    
    @pytest.mark.asyncio
    async def test_fix_foreign_key_violation(self, database_fixer):
        """测试修复外键约束违反"""
        violation_details = {"type": "orphaned_record", "table": "orders"}
        
        result = await database_fixer._fix_foreign_key_violation(violation_details)
        
        assert result["violation_type"] == "orphaned_record"
        assert "fix_action" in result
        assert "records_affected" in result
    
    @pytest.mark.asyncio
    async def test_verify_backup_valid(self, database_fixer, temp_db_config):
        """测试验证备份 - 有效备份"""
        # 创建一个有效的SQLite数据库文件
        import sqlite3
        conn = sqlite3.connect(temp_db_config.db_path)
        conn.execute("CREATE TABLE test (id INTEGER)")
        conn.commit()
        conn.close()
        
        result = await database_fixer._verify_backup(temp_db_config.db_path)
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_verify_backup_invalid(self, database_fixer):
        """测试验证备份 - 无效备份"""
        non_existent_path = "/path/to/non/existent/backup.db"
        
        result = await database_fixer._verify_backup(non_existent_path)
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_cleanup_old_backups(self, database_fixer):
        """测试清理旧备份"""
        # 创建一些测试备份文件
        backup_dir = database_fixer.backup_directory
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建旧备份文件
        old_backup = backup_dir / "old_backup.db"
        old_backup.touch()
        
        # 设置文件修改时间为8天前
        old_time = (datetime.now() - timedelta(days=8)).timestamp()
        os.utime(old_backup, (old_time, old_time))
        
        # 创建新备份文件
        new_backup = backup_dir / "new_backup.db"
        new_backup.touch()
        
        await database_fixer._cleanup_old_backups()
        
        # 旧备份应该被删除，新备份应该保留
        assert not old_backup.exists()
        assert new_backup.exists()
        
        # 清理测试文件
        if new_backup.exists():
            new_backup.unlink()


class TestDatabaseConfig:
    """数据库配置测试类"""
    
    def test_database_config_creation(self):
        """测试数据库配置创建"""
        config = DatabaseConfig(
            db_path="/path/to/db.sqlite",
            max_connections=20,
            connection_timeout=60.0,
            query_timeout=30.0,
            backup_interval=7200,
            backup_retention=14
        )
        
        assert config.db_path == "/path/to/db.sqlite"
        assert config.max_connections == 20
        assert config.connection_timeout == 60.0
        assert config.query_timeout == 30.0
        assert config.backup_interval == 7200
        assert config.backup_retention == 14
    
    def test_database_config_default_values(self):
        """测试数据库配置默认值"""
        config = DatabaseConfig(db_path="/path/to/db.sqlite")
        
        assert config.max_connections == 10
        assert config.connection_timeout == 30.0
        assert config.query_timeout == 10.0
        assert config.backup_interval == 3600
        assert config.backup_retention == 7


class TestDatabaseIssue:
    """数据库问题测试类"""
    
    def test_database_issue_creation(self):
        """测试数据库问题创建"""
        issue = DatabaseIssue(
            issue_id="test_001",
            issue_type=DatabaseIssueType.SLOW_QUERY,
            severity="high",
            description="查询执行时间过长",
            table_name="test_table",
            query="SELECT * FROM test_table",
            detected_at=datetime.now(),
            details={"execution_time": 10.5}
        )
        
        assert issue.issue_id == "test_001"
        assert issue.issue_type == DatabaseIssueType.SLOW_QUERY
        assert issue.severity == "high"
        assert issue.description == "查询执行时间过长"
        assert issue.table_name == "test_table"
        assert issue.query == "SELECT * FROM test_table"
        assert issue.details == {"execution_time": 10.5}
    
    def test_database_issue_default_values(self):
        """测试数据库问题默认值"""
        issue = DatabaseIssue(
            issue_id="test_001",
            issue_type=DatabaseIssueType.DATA_CORRUPTION,
            severity="critical",
            description="数据损坏"
        )
        
        assert issue.table_name is None
        assert issue.query is None
        assert issue.detected_at is not None
        assert issue.details == {}


class TestQueryPerformanceMetrics:
    """查询性能指标测试类"""
    
    def test_query_performance_metrics_creation(self):
        """测试查询性能指标创建"""
        metrics = QueryPerformanceMetrics(
            query="SELECT * FROM positions",
            execution_time=2.5,
            rows_affected=100,
            table_name="positions",
            timestamp=datetime.now()
        )
        
        assert metrics.query == "SELECT * FROM positions"
        assert metrics.execution_time == 2.5
        assert metrics.rows_affected == 100
        assert metrics.table_name == "positions"
        assert metrics.timestamp is not None
    
    def test_query_performance_metrics_default_timestamp(self):
        """测试查询性能指标默认时间戳"""
        metrics = QueryPerformanceMetrics(
            query="SELECT * FROM orders",
            execution_time=1.0,
            rows_affected=50,
            table_name="orders"
        )
        
        assert metrics.timestamp is not None
        assert isinstance(metrics.timestamp, datetime)


class TestBackupInfo:
    """备份信息测试类"""
    
    def test_backup_info_creation(self):
        """测试备份信息创建"""
        backup_info = BackupInfo(
            backup_id="backup_001",
            backup_path="/path/to/backup.db",
            backup_size=1024000,
            created_at=datetime.now(),
            is_valid=True,
            checksum="abc123"
        )
        
        assert backup_info.backup_id == "backup_001"
        assert backup_info.backup_path == "/path/to/backup.db"
        assert backup_info.backup_size == 1024000
        assert backup_info.is_valid is True
        assert backup_info.checksum == "abc123"
    
    def test_backup_info_default_values(self):
        """测试备份信息默认值"""
        backup_info = BackupInfo(
            backup_id="backup_002",
            backup_path="/path/to/backup2.db",
            backup_size=512000,
            created_at=datetime.now()
        )
        
        assert backup_info.is_valid is True
        assert backup_info.checksum is None


if __name__ == "__main__":
    pytest.main([__file__])