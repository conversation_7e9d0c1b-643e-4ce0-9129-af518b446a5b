"""
策略框架集成测试

验证策略基础类、上下文管理器、生命周期管理和参数验证系统的集成工作
"""

import unittest
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock
import pandas as pd

from src.market.strategies.base import BaseStrategy, StrategyContext
from src.market.strategies.signals import Signal, SignalType
from src.market.strategies.parameters import StrategyParameter, ParameterType
from src.market.strategies.lifecycle import StrategyLifecycleManager, StrategyState
from src.market.strategies.models.market_data import MarketData
from src.market.strategies.models.portfolio import Portfolio


class SimpleMovingAverageStrategy(BaseStrategy):
    """简单移动平均策略示例"""
    
    def get_parameter_definitions(self):
        return {
            'short_period': StrategyParameter(
                name='short_period',
                param_type=ParameterType.INTEGER,
                default_value=10,
                required=True,
                min_value=1,
                max_value=50,
                description='短期移动平均周期'
            ),
            'long_period': StrategyParameter(
                name='long_period',
                param_type=ParameterType.INTEGER,
                default_value=20,
                required=True,
                min_value=10,
                max_value=100,
                description='长期移动平均周期'
            ),
            'signal_threshold': StrategyParameter(
                name='signal_threshold',
                param_type=ParameterType.FLOAT,
                default_value=0.02,
                required=False,
                min_value=0.0,
                max_value=0.1,
                description='信号触发阈值'
            )
        }
    
    def initialize(self, context):
        """策略初始化"""
        self.context = context
        self.is_initialized = True
        self.last_signal_time = None
        
        # 验证参数
        short_period = self.get_parameter('short_period')
        long_period = self.get_parameter('long_period')
        
        if short_period >= long_period:
            raise ValueError("短期周期必须小于长期周期")
    
    def on_data(self, data):
        """处理市场数据并生成信号"""
        if not self.is_initialized or not self.context:
            return []
        
        signals = []
        
        # 处理单个标的数据
        if isinstance(data, MarketData):
            signal = self._process_single_data(data)
            if signal:
                signals.append(signal)
        
        # 处理多个标的数据
        elif isinstance(data, dict):
            for symbol, market_data in data.items():
                signal = self._process_single_data(market_data)
                if signal:
                    signals.append(signal)
        
        return signals
    
    def _process_single_data(self, data):
        """处理单个标的的数据"""
        try:
            # 获取历史数据
            historical_data = self.context.get_historical_data(
                data.symbol, 
                self.get_parameter('long_period') + 5
            )
            
            if len(historical_data) < self.get_parameter('long_period'):
                return None
            
            # 计算移动平均线
            short_ma = self.context.get_indicator(
                'SMA', 
                historical_data, 
                period=self.get_parameter('short_period')
            )
            
            long_ma = self.context.get_indicator(
                'SMA', 
                historical_data, 
                period=self.get_parameter('long_period')
            )
            
            if len(short_ma) < 2 or len(long_ma) < 2:
                return None
            
            # 计算信号
            current_short = short_ma.iloc[-1]
            current_long = long_ma.iloc[-1]
            prev_short = short_ma.iloc[-2]
            prev_long = long_ma.iloc[-2]
            
            threshold = self.get_parameter('signal_threshold')
            
            # 金叉信号（短期均线上穿长期均线）
            if (prev_short <= prev_long and 
                current_short > current_long and
                (current_short - current_long) / current_long > threshold):
                
                return Signal(
                    symbol=data.symbol,
                    signal_type=SignalType.BUY,
                    timestamp=data.timestamp,
                    strategy_name=self.name,
                    confidence=min(0.9, (current_short - current_long) / current_long * 10),
                    price=data.close,
                    metadata={
                        'short_ma': current_short,
                        'long_ma': current_long,
                        'crossover_strength': (current_short - current_long) / current_long
                    }
                )
            
            # 死叉信号（短期均线下穿长期均线）
            elif (prev_short >= prev_long and 
                  current_short < current_long and
                  (current_long - current_short) / current_long > threshold):
                
                return Signal(
                    symbol=data.symbol,
                    signal_type=SignalType.SELL,
                    timestamp=data.timestamp,
                    strategy_name=self.name,
                    confidence=min(0.9, (current_long - current_short) / current_long * 10),
                    price=data.close,
                    metadata={
                        'short_ma': current_short,
                        'long_ma': current_long,
                        'crossover_strength': (current_long - current_short) / current_long
                    }
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"处理数据时发生错误: {e}")
            return None


class TestStrategyIntegration(unittest.TestCase):
    """策略框架集成测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建模拟的数据管理器
        self.mock_data_manager = Mock()
        
        # 创建模拟的指标引擎
        self.mock_indicator_engine = Mock()
        
        # 创建模拟的投资组合
        self.mock_portfolio = Portfolio(initial_capital=100000, cash=100000)
        
        # 创建策略上下文
        self.context = StrategyContext(
            data_manager=self.mock_data_manager,
            indicator_engine=self.mock_indicator_engine,
            portfolio=self.mock_portfolio
        )
        
        # 创建生命周期管理器
        self.lifecycle_manager = StrategyLifecycleManager()
    
    def test_complete_strategy_workflow(self):
        """测试完整的策略工作流程"""
        # 1. 创建策略实例
        strategy = SimpleMovingAverageStrategy(
            name='SMA_Strategy',
            parameters={
                'short_period': 5,
                'long_period': 10,
                'signal_threshold': 0.01
            }
        )
        
        # 2. 注册策略到生命周期管理器
        self.assertTrue(self.lifecycle_manager.register_strategy(strategy, self.context))
        self.assertEqual(
            self.lifecycle_manager.get_strategy_state('SMA_Strategy'), 
            StrategyState.CREATED
        )
        
        # 3. 初始化策略
        self.assertTrue(self.lifecycle_manager.initialize_strategy('SMA_Strategy'))
        self.assertEqual(
            self.lifecycle_manager.get_strategy_state('SMA_Strategy'), 
            StrategyState.INITIALIZED
        )
        
        # 4. 启动策略
        self.assertTrue(self.lifecycle_manager.start_strategy('SMA_Strategy'))
        self.assertEqual(
            self.lifecycle_manager.get_strategy_state('SMA_Strategy'), 
            StrategyState.RUNNING
        )
        
        # 5. 模拟历史数据和指标计算
        self._setup_mock_data()
        
        # 6. 创建市场数据
        market_data = MarketData(
            symbol='AAPL',
            timestamp=datetime.now(),
            open=150.0,
            high=152.0,
            low=149.0,
            close=151.0,
            volume=1000000
        )
        
        # 7. 处理数据并生成信号
        signals = self.lifecycle_manager.process_data('SMA_Strategy', market_data)
        
        # 8. 验证信号生成
        self.assertIsInstance(signals, list)
        if signals:  # 如果生成了信号
            signal = signals[0]
            self.assertEqual(signal.symbol, 'AAPL')
            self.assertIn(signal.signal_type, [SignalType.BUY, SignalType.SELL])
            self.assertEqual(signal.strategy_name, 'SMA_Strategy')
            self.assertTrue(0 <= signal.confidence <= 1)
            self.assertIsNotNone(signal.metadata)
        
        # 9. 停止策略
        self.assertTrue(self.lifecycle_manager.stop_strategy('SMA_Strategy'))
        self.assertEqual(
            self.lifecycle_manager.get_strategy_state('SMA_Strategy'), 
            StrategyState.STOPPED
        )
    
    def test_parameter_validation_integration(self):
        """测试参数验证集成"""
        # 测试有效参数
        strategy = SimpleMovingAverageStrategy(
            name='Test_Strategy',
            parameters={
                'short_period': 5,
                'long_period': 20,
                'signal_threshold': 0.02
            }
        )
        
        self.assertEqual(strategy.get_parameter('short_period'), 5)
        self.assertEqual(strategy.get_parameter('long_period'), 20)
        self.assertEqual(strategy.get_parameter('signal_threshold'), 0.02)
        
        # 测试无效参数：短期周期大于长期周期
        with self.assertRaises(ValueError):
            invalid_strategy = SimpleMovingAverageStrategy(
                name='Invalid_Strategy',
                parameters={
                    'short_period': 25,  # 大于长期周期
                    'long_period': 20
                }
            )
            invalid_strategy.initialize(self.context)
        
        # 测试参数范围验证
        with self.assertRaises(ValueError):
            strategy.set_parameter('short_period', 0)  # 小于最小值
        
        with self.assertRaises(ValueError):
            strategy.set_parameter('signal_threshold', 0.2)  # 大于最大值
    
    def test_context_integration(self):
        """测试上下文集成"""
        strategy = SimpleMovingAverageStrategy('Context_Test_Strategy')
        
        # 设置模拟数据
        self._setup_mock_data()
        
        # 初始化策略
        strategy.initialize(self.context)
        
        # 测试上下文功能
        self.assertEqual(strategy.context, self.context)
        
        # 测试历史数据获取
        historical_data = strategy.context.get_historical_data('AAPL', 30)
        self.assertIsInstance(historical_data, pd.DataFrame)
        
        # 测试指标计算
        indicator_data = strategy.context.get_indicator('SMA', historical_data, period=10)
        self.assertIsInstance(indicator_data, pd.Series)
        
        # 测试投资组合访问
        portfolio = strategy.context.get_portfolio()
        self.assertEqual(portfolio.initial_capital, 100000)
    
    def test_signal_generation_scenarios(self):
        """测试不同场景下的信号生成"""
        strategy = SimpleMovingAverageStrategy(
            name='Signal_Test_Strategy',
            parameters={'short_period': 5, 'long_period': 15}
        )
        
        # 注册并启动策略
        self.lifecycle_manager.register_strategy(strategy, self.context)
        self.lifecycle_manager.initialize_strategy('Signal_Test_Strategy')
        self.lifecycle_manager.start_strategy('Signal_Test_Strategy')
        
        # 场景1：金叉信号（短期均线上穿长期均线）
        self._setup_golden_cross_scenario()
        
        market_data = MarketData(
            symbol='TEST',
            timestamp=datetime.now(),
            open=105.0,
            high=106.0,
            low=104.0,
            close=105.5,
            volume=1000000
        )
        
        signals = self.lifecycle_manager.process_data('Signal_Test_Strategy', market_data)
        
        if signals:
            self.assertEqual(signals[0].signal_type, SignalType.BUY)
            self.assertGreater(signals[0].confidence, 0)
        
        # 场景2：死叉信号（短期均线下穿长期均线）
        self._setup_death_cross_scenario()
        
        market_data.close = 95.0
        signals = self.lifecycle_manager.process_data('Signal_Test_Strategy', market_data)
        
        if signals:
            self.assertEqual(signals[0].signal_type, SignalType.SELL)
            self.assertGreater(signals[0].confidence, 0)
    
    def test_error_handling(self):
        """测试错误处理"""
        strategy = SimpleMovingAverageStrategy('Error_Test_Strategy')
        
        # 测试未初始化的策略
        market_data = MarketData(
            symbol='TEST',
            timestamp=datetime.now(),
            open=100.0,
            high=101.0,
            low=99.0,
            close=100.5,
            volume=1000000
        )
        
        signals = strategy.on_data(market_data)
        self.assertEqual(len(signals), 0)  # 未初始化应该返回空列表
        
        # 测试数据获取失败的情况
        self.mock_data_manager.get_historical_data.side_effect = Exception("数据获取失败")
        
        strategy.initialize(self.context)
        signals = strategy.on_data(market_data)
        self.assertEqual(len(signals), 0)  # 异常情况应该返回空列表
    
    def _setup_mock_data(self):
        """设置模拟数据"""
        # 创建模拟历史数据
        dates = pd.date_range(start='2023-01-01', periods=30, freq='D')
        mock_historical_data = pd.DataFrame({
            'timestamp': dates,
            'open': range(100, 130),
            'high': range(102, 132),
            'low': range(98, 128),
            'close': range(101, 131),
            'volume': [1000000] * 30
        })
        
        self.mock_data_manager.get_historical_data.return_value = mock_historical_data
        
        # 创建模拟指标数据
        def mock_calculate_indicator(name, data, **kwargs):
            if name == 'SMA':
                period = kwargs.get('period', 10)
                return data['close'].rolling(window=period).mean()
            return pd.Series([0] * len(data))
        
        self.mock_indicator_engine.calculate.side_effect = mock_calculate_indicator
    
    def _setup_golden_cross_scenario(self):
        """设置金叉场景的模拟数据"""
        # 短期均线上穿长期均线的数据
        dates = pd.date_range(start='2023-01-01', periods=10, freq='D')
        prices = [95, 96, 97, 98, 99, 100, 102, 104, 106, 108]  # 上升趋势
        
        mock_data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p + 1 for p in prices],
            'low': [p - 1 for p in prices],
            'close': prices,
            'volume': [1000000] * 10
        })
        
        self.mock_data_manager.get_historical_data.return_value = mock_data
        
        # 模拟短期均线高于长期均线
        def mock_golden_cross_indicator(name, data, **kwargs):
            period = kwargs.get('period', 10)
            if period == 5:  # 短期
                return pd.Series([98, 99, 100, 101, 102, 103, 104, 105, 106, 107])
            else:  # 长期
                return pd.Series([96, 97, 98, 99, 100, 101, 102, 103, 104, 105])
        
        self.mock_indicator_engine.calculate.side_effect = mock_golden_cross_indicator
    
    def _setup_death_cross_scenario(self):
        """设置死叉场景的模拟数据"""
        # 短期均线下穿长期均线的数据
        dates = pd.date_range(start='2023-01-01', periods=10, freq='D')
        prices = [105, 104, 103, 102, 101, 100, 98, 96, 94, 92]  # 下降趋势
        
        mock_data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p + 1 for p in prices],
            'low': [p - 1 for p in prices],
            'close': prices,
            'volume': [1000000] * 10
        })
        
        self.mock_data_manager.get_historical_data.return_value = mock_data
        
        # 模拟短期均线低于长期均线
        def mock_death_cross_indicator(name, data, **kwargs):
            period = kwargs.get('period', 10)
            if period == 5:  # 短期
                return pd.Series([102, 101, 100, 99, 98, 97, 96, 95, 94, 93])
            else:  # 长期
                return pd.Series([104, 103, 102, 101, 100, 99, 98, 97, 96, 95])
        
        self.mock_indicator_engine.calculate.side_effect = mock_death_cross_indicator


if __name__ == '__main__':
    unittest.main()