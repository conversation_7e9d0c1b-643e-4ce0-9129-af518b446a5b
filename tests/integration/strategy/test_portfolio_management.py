"""
Tests for portfolio management functionality.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock

from src.market.strategies.models.portfolio import Portfolio, Position, PortfolioSnapshot, CashFlow
from src.market.strategies.models.portfolio_manager import PortfolioManager, PortfolioAllocation, RebalanceRecommendation
from src.market.strategies.models.trading import Trade, OrderSide


class TestPortfolio:
    """Test enhanced Portfolio class functionality."""
    
    def test_portfolio_initialization(self):
        """Test portfolio initialization."""
        portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        
        assert portfolio.initial_capital == 100000.0
        assert portfolio.cash == 100000.0
        assert portfolio.total_value == 100000.0
        assert portfolio.peak_value == 100000.0
        assert len(portfolio.history) == 1  # Initial snapshot
        assert portfolio.validate()
    
    def test_add_trade_buy(self):
        """Test adding buy trade to portfolio."""
        portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        
        trade = Mock(spec=Trade)
        trade.id = "trade_1"
        trade.symbol = "AAPL"
        trade.side = Mock()
        trade.side.value = "BUY"
        trade.quantity = 100
        trade.price = 150.0
        trade.commission = 5.0
        
        portfolio.add_trade(trade)
        
        # Check cash reduction
        expected_cash = 100000.0 - (100 * 150.0 + 5.0)
        assert portfolio.cash == expected_cash
        
        # Check position creation
        assert "AAPL" in portfolio.positions
        position = portfolio.positions["AAPL"]
        assert position.quantity == 100
        assert position.avg_price == 150.0
        
        # Check trade and cash flow records
        assert len(portfolio.trades) == 1
        assert len(portfolio.cash_flows) == 2  # Trade + commission
        
        # Check history snapshot
        assert len(portfolio.history) == 2  # Initial + after trade
    
    def test_add_trade_sell(self):
        """Test adding sell trade to portfolio."""
        portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        
        # First buy
        buy_trade = Mock(spec=Trade)
        buy_trade.id = "trade_1"
        buy_trade.symbol = "AAPL"
        buy_trade.side = Mock()
        buy_trade.side.value = "BUY"
        buy_trade.quantity = 100
        buy_trade.price = 150.0
        buy_trade.commission = 5.0
        
        portfolio.add_trade(buy_trade)
        
        # Then sell
        sell_trade = Mock(spec=Trade)
        sell_trade.id = "trade_2"
        sell_trade.symbol = "AAPL"
        sell_trade.side = Mock()
        sell_trade.side.value = "SELL"
        sell_trade.quantity = 50
        sell_trade.price = 160.0
        sell_trade.commission = 5.0
        
        portfolio.add_trade(sell_trade)
        
        # Check position update
        position = portfolio.positions["AAPL"]
        assert position.quantity == 50
        assert position.avg_price == 150.0  # Avg price should remain same
        assert position.realized_pnl == (160.0 - 150.0) * 50  # Profit from sale
        
        # Check cash increase
        expected_cash = 100000.0 - (100 * 150.0 + 5.0) + (50 * 160.0 - 5.0)
        assert portfolio.cash == expected_cash
    
    def test_update_positions_market_value(self):
        """Test updating positions with market prices."""
        portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        
        # Add position
        trade = Mock(spec=Trade)
        trade.id = "trade_1"
        trade.symbol = "AAPL"
        trade.side = Mock()
        trade.side.value = "BUY"
        trade.quantity = 100
        trade.price = 150.0
        trade.commission = 5.0
        
        portfolio.add_trade(trade)
        
        # Update market prices
        market_prices = {"AAPL": 160.0}
        portfolio.update_positions_market_value(market_prices)
        
        position = portfolio.positions["AAPL"]
        assert position.last_price == 160.0
        assert position.market_value == 100 * 160.0
        assert position.unrealized_pnl == (160.0 - 150.0) * 100
        
        # Check total value update
        expected_total = portfolio.cash + 100 * 160.0
        assert portfolio.total_value == expected_total
    
    def test_drawdown_tracking(self):
        """Test drawdown tracking functionality."""
        portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        
        # Simulate portfolio value changes by directly setting cash (which affects total_value)
        portfolio.cash = 110000.0  # +10%
        portfolio.update_total_value()
        assert portfolio.peak_value == 110000.0
        assert portfolio.current_drawdown == 0.0
        
        portfolio.cash = 105000.0  # -4.5% from peak
        portfolio.update_total_value()
        expected_drawdown = (105000.0 - 110000.0) / 110000.0
        assert abs(portfolio.current_drawdown - expected_drawdown) < 1e-6
        assert portfolio.max_drawdown == expected_drawdown
        
        portfolio.cash = 95000.0  # -13.6% from peak
        portfolio.update_total_value()
        expected_drawdown = (95000.0 - 110000.0) / 110000.0
        assert abs(portfolio.current_drawdown - expected_drawdown) < 1e-6
        assert portfolio.max_drawdown == expected_drawdown
    
    def test_performance_metrics(self):
        """Test performance metrics calculation."""
        portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        
        # Add some daily returns
        portfolio.daily_returns = [0.01, -0.005, 0.02, -0.01, 0.015]
        portfolio.total_value = 105000.0
        portfolio.max_drawdown = -0.05
        
        metrics = portfolio.get_performance_metrics()
        
        assert 'total_return' in metrics
        assert 'annualized_return' in metrics
        assert 'volatility' in metrics
        assert 'sharpe_ratio' in metrics
        assert 'sortino_ratio' in metrics
        assert 'max_drawdown' in metrics
        
        assert metrics['total_return'] == 0.05  # 5% return
        assert metrics['max_drawdown'] == -0.05
    
    def test_cash_flow_tracking(self):
        """Test cash flow tracking."""
        portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        
        # Add dividend
        portfolio.add_dividend("AAPL", 100.0)
        assert portfolio.cash == 100100.0
        
        # Add deposit
        portfolio.add_deposit(5000.0, "Additional investment")
        assert portfolio.cash == 105100.0
        assert portfolio.initial_capital == 105000.0  # Adjusted for deposit
        
        # Add withdrawal
        portfolio.add_withdrawal(2000.0, "Partial withdrawal")
        assert portfolio.cash == 103100.0
        assert portfolio.initial_capital == 103000.0  # Adjusted for withdrawal
        
        # Check cash flow summary
        summary = portfolio.get_cash_flow_summary()
        assert summary['dividend_received'] == 100.0
        assert summary['total_inflow'] > 0
        assert summary['total_outflow'] < 0
    
    def test_history_dataframe(self):
        """Test portfolio history DataFrame export."""
        portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        
        # Add some trades to create history
        trade = Mock(spec=Trade)
        trade.id = "trade_1"
        trade.symbol = "AAPL"
        trade.side = Mock()
        trade.side.value = "BUY"
        trade.quantity = 100
        trade.price = 150.0
        trade.commission = 5.0
        
        portfolio.add_trade(trade)
        
        df = portfolio.get_history_dataframe()
        
        assert isinstance(df, pd.DataFrame)
        assert len(df) >= 2  # Initial + after trade
        assert 'total_value' in df.columns
        assert 'cash' in df.columns
        assert 'positions_value' in df.columns
        assert df.index.name == 'timestamp'


class TestPortfolioManager:
    """Test PortfolioManager functionality."""
    
    def setup_method(self):
        """Set up test portfolio manager."""
        self.portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        self.manager = PortfolioManager(self.portfolio)
        
        # Add some positions
        trade1 = Mock(spec=Trade)
        trade1.id = "trade_1"
        trade1.symbol = "AAPL"
        trade1.side = Mock()
        trade1.side.value = "BUY"
        trade1.quantity = 100
        trade1.price = 150.0
        trade1.commission = 5.0
        trade1.timestamp = datetime.now()
        trade1.strategy_id = "test_strategy"
        trade1.pnl = None
        
        trade2 = Mock(spec=Trade)
        trade2.id = "trade_2"
        trade2.symbol = "GOOGL"
        trade2.side = Mock()
        trade2.side.value = "BUY"
        trade2.quantity = 50
        trade2.price = 2000.0
        trade2.commission = 5.0
        trade2.timestamp = datetime.now()
        trade2.strategy_id = "test_strategy"
        trade2.pnl = None
        
        self.portfolio.add_trade(trade1)
        self.portfolio.add_trade(trade2)
        
        # Update market values
        market_prices = {"AAPL": 160.0, "GOOGL": 2100.0}
        self.portfolio.update_positions_market_value(market_prices)
    
    def test_current_allocation(self):
        """Test current allocation calculation."""
        allocation = self.manager.get_current_allocation()
        
        assert "AAPL" in allocation
        assert "GOOGL" in allocation
        
        # Check that all weights are positive
        for weight in allocation.values():
            assert weight > 0
        
        # Check allocation weights sum to approximately 1 (allowing for small cash remainder)
        total_weight = sum(allocation.values())
        assert total_weight > 0.9  # Should be close to 1, but cash might be very small
    
    def test_target_allocation(self):
        """Test setting target allocation."""
        allocations = [
            PortfolioAllocation("AAPL", 0.4),
            PortfolioAllocation("GOOGL", 0.4),
            PortfolioAllocation("CASH", 0.2)
        ]
        
        self.manager.set_target_allocation(allocations)
        
        assert len(self.manager.allocations) == 3
        assert self.manager.allocations["AAPL"].target_weight == 0.4
    
    def test_allocation_drift(self):
        """Test allocation drift analysis."""
        allocations = [
            PortfolioAllocation("AAPL", 0.4),
            PortfolioAllocation("GOOGL", 0.4),
            PortfolioAllocation("CASH", 0.2)
        ]
        
        self.manager.set_target_allocation(allocations)
        drift = self.manager.analyze_allocation_drift()
        
        assert "AAPL" in drift
        assert "GOOGL" in drift
        assert "CASH" in drift
        
        # Drift should be difference between current and target
        current_allocation = self.manager.get_current_allocation()
        for symbol in drift:
            expected_drift = current_allocation.get(symbol, 0.0) - allocations[0].target_weight
            # Note: This is simplified - actual test would need proper symbol matching
    
    def test_rebalance_recommendations(self):
        """Test rebalancing recommendations."""
        allocations = [
            PortfolioAllocation("AAPL", 0.5, rebalance_threshold=0.05),
            PortfolioAllocation("GOOGL", 0.3, rebalance_threshold=0.05),
            PortfolioAllocation("CASH", 0.2, rebalance_threshold=0.05)
        ]
        
        self.manager.set_target_allocation(allocations)
        
        market_prices = {"AAPL": 160.0, "GOOGL": 2100.0}
        recommendations = self.manager.get_rebalance_recommendations(market_prices)
        
        assert isinstance(recommendations, list)
        for rec in recommendations:
            assert isinstance(rec, RebalanceRecommendation)
            assert rec.recommended_action in ['BUY', 'SELL']
            assert rec.recommended_quantity >= 0
    
    def test_concentration_analysis(self):
        """Test portfolio concentration analysis."""
        analysis = self.manager.analyze_position_concentration()
        
        assert 'concentration_ratio' in analysis
        assert 'herfindahl_index' in analysis
        assert 'effective_positions' in analysis
        assert 'number_of_positions' in analysis
        
        assert analysis['number_of_positions'] == 2  # AAPL and GOOGL
        assert analysis['concentration_ratio'] > 0  # Should be positive
        assert analysis['herfindahl_index'] > 0
    
    def test_var_calculation(self):
        """Test Value at Risk calculation."""
        # Add some daily returns
        self.portfolio.daily_returns = [0.01, -0.02, 0.015, -0.01, 0.005, -0.03, 0.02]
        
        var_95 = self.manager.calculate_var(confidence_level=0.05)
        var_99 = self.manager.calculate_var(confidence_level=0.01)
        
        assert var_95 >= 0
        assert var_99 >= var_95  # 99% VaR should be higher than 95% VaR
    
    def test_expected_shortfall(self):
        """Test Expected Shortfall calculation."""
        # Add some daily returns
        self.portfolio.daily_returns = [0.01, -0.02, 0.015, -0.01, 0.005, -0.03, 0.02]
        
        es_95 = self.manager.calculate_expected_shortfall(confidence_level=0.05)
        es_99 = self.manager.calculate_expected_shortfall(confidence_level=0.01)
        
        assert es_95 >= 0
        assert es_99 >= es_95  # 99% ES should be higher than 95% ES
    
    def test_performance_report(self):
        """Test comprehensive performance report generation."""
        # Add some daily returns for metrics calculation
        self.portfolio.daily_returns = [0.01, -0.005, 0.02, -0.01, 0.015]
        
        report = self.manager.generate_performance_report()
        
        # Check report structure
        assert 'portfolio_summary' in report
        assert 'performance_metrics' in report
        assert 'allocation_analysis' in report
        assert 'risk_metrics' in report
        assert 'cash_flow_summary' in report
        assert 'positions_summary' in report
        
        # Check portfolio summary
        summary = report['portfolio_summary']
        assert 'initial_capital' in summary
        assert 'current_value' in summary
        assert 'number_of_positions' in summary
        
        # Check risk metrics
        risk_metrics = report['risk_metrics']
        assert 'var_95' in risk_metrics
        assert 'var_99' in risk_metrics
        assert 'expected_shortfall_95' in risk_metrics
    
    def test_export_dataframes(self):
        """Test exporting portfolio data to DataFrames."""
        dataframes = self.manager.export_to_dataframe()
        
        assert 'portfolio_history' in dataframes
        assert 'cash_flows' in dataframes
        assert 'trades' in dataframes
        assert 'positions' in dataframes
        
        # Check DataFrame types
        for df_name, df in dataframes.items():
            assert isinstance(df, pd.DataFrame)
            if not df.empty:
                assert df.index.name in ['timestamp', None]
    
    def test_simulate_rebalancing(self):
        """Test rebalancing simulation."""
        allocations = [
            PortfolioAllocation("AAPL", 0.5, rebalance_threshold=0.05),
            PortfolioAllocation("GOOGL", 0.3, rebalance_threshold=0.05),
            PortfolioAllocation("CASH", 0.2, rebalance_threshold=0.05)
        ]
        
        self.manager.set_target_allocation(allocations)
        
        market_prices = {"AAPL": 160.0, "GOOGL": 2100.0}
        simulated_trades = self.manager.simulate_rebalancing(market_prices, dry_run=True)
        
        assert isinstance(simulated_trades, list)
        for trade in simulated_trades:
            assert 'symbol' in trade
            assert 'action' in trade
            assert 'quantity' in trade
            assert 'estimated_value' in trade
            assert trade['action'] in ['BUY', 'SELL']
        
        # Check that dry run doesn't affect rebalance history
        assert len(self.manager.rebalance_history) == 0
        
        # Test actual rebalancing (not dry run)
        self.manager.simulate_rebalancing(market_prices, dry_run=False)
        assert len(self.manager.rebalance_history) == 1


if __name__ == "__main__":
    pytest.main([__file__])