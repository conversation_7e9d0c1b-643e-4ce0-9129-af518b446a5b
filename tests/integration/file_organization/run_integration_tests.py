logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
项目文件组织系统集成测试运行器

执行所有集成测试并生成综合报告。
"""

import os
import sys
import time
import unittest
import json
from pathlib import Path
from typing import Dict, List, Any
import logging
from io import StringIO

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入所有测试模块
from test_complete_system_integration import ProjectFileOrganizationIntegrationTest
from test_tools_collaboration import FileOrganizationToolsCollaborationTest
from test_performance_scaling import PerformanceScalingTest
from test_user_acceptance import UserAcceptanceTest


class IntegrationTestRunner:
    """集成测试运行器"""
    
    def __init__(self):
        """初始化测试运行器"""
        self.logger = self._setup_logging()
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('integration_test_results.log', encoding='utf-8')
            ]
        )
        return logging.getLogger(__name__)
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有集成测试"""
        self.logger.info("开始执行项目文件组织系统集成测试套件...")
        self.start_time = time.time()
        
        # 定义测试套件
        test_suites = [
            {
                "name": "完整系统集成测试",
                "class": ProjectFileOrganizationIntegrationTest,
                "description": "测试整个文件组织系统的集成功能"
            },
            {
                "name": "工具协同工作测试",
                "class": FileOrganizationToolsCollaborationTest,
                "description": "测试各个工具组件之间的协同工作"
            },
            {
                "name": "性能扩展测试",
                "class": PerformanceScalingTest,
                "description": "测试系统在不同规模下的性能表现"
            },
            {
                "name": "用户接受度测试",
                "class": UserAcceptanceTest,
                "description": "测试系统的易用性和实用性"
            }
        ]
        
        # 执行每个测试套件
        for suite_info in test_suites:
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"执行测试套件: {suite_info['name']}")
            self.logger.info(f"描述: {suite_info['description']}")
            self.logger.info(f"{'='*60}")
            
            suite_result = self._run_test_suite(suite_info)
            self.test_results[suite_info['name']] = suite_result
        
        self.end_time = time.time()
        
        # 生成综合报告
        comprehensive_report = self._generate_comprehensive_report()
        
        self.logger.info(f"\n{'='*60}")
        self.logger.info("集成测试套件执行完成")
        self.logger.info(f"总耗时: {self.end_time - self.start_time:.2f}秒")
        self.logger.info(f"{'='*60}")
        
        return comprehensive_report
    
    def _run_test_suite(self, suite_info: Dict[str, Any]) -> Dict[str, Any]:
        """运行单个测试套件"""
        suite_start_time = time.time()
        
        # 创建测试套件
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromTestCase(suite_info['class'])
        
        # 创建自定义测试结果收集器
        test_result = DetailedTestResult()
        
        # 运行测试
        suite.run(test_result)
        
        suite_end_time = time.time()
        
        # 收集结果
        suite_result = {
            "name": suite_info['name'],
            "description": suite_info['description'],
            "start_time": suite_start_time,
            "end_time": suite_end_time,
            "duration": suite_end_time - suite_start_time,
            "tests_run": test_result.testsRun,
            "failures": len(test_result.failures),
            "errors": len(test_result.errors),
            "skipped": len(test_result.skipped),
            "success_rate": (test_result.testsRun - len(test_result.failures) - len(test_result.errors)) / test_result.testsRun if test_result.testsRun > 0 else 0,
            "detailed_results": test_result.get_detailed_results()
        }
        
        # 输出套件结果摘要
        self._print_suite_summary(suite_result)
        
        return suite_result
    
    def _print_suite_summary(self, suite_result: Dict[str, Any]):
        """打印测试套件摘要"""
        self.logger.info(f"\n测试套件结果摘要:")
        self.logger.info(f"  测试总数: {suite_result['tests_run']}")
        self.logger.info(f"  成功: {suite_result['tests_run'] - suite_result['failures'] - suite_result['errors']}")
        self.logger.info(f"  失败: {suite_result['failures']}")
        self.logger.info(f"  错误: {suite_result['errors']}")
        self.logger.info(f"  跳过: {suite_result['skipped']}")
        self.logger.info(f"  成功率: {suite_result['success_rate']:.1%}")
        self.logger.info(f"  耗时: {suite_result['duration']:.2f}秒")
        
        # 如果有失败或错误，显示详情
        if suite_result['failures'] > 0 or suite_result['errors'] > 0:
            self.logger.warning("存在测试失败或错误，请查看详细日志")
    
    def _generate_comprehensive_report(self) -> Dict[str, Any]:
        """生成综合测试报告"""
        total_tests = sum(result['tests_run'] for result in self.test_results.values())
        total_failures = sum(result['failures'] for result in self.test_results.values())
        total_errors = sum(result['errors'] for result in self.test_results.values())
        total_skipped = sum(result['skipped'] for result in self.test_results.values())
        
        overall_success_rate = (total_tests - total_failures - total_errors) / total_tests if total_tests > 0 else 0
        
        # 计算各个维度的评分
        performance_score = self._calculate_performance_score()
        usability_score = self._calculate_usability_score()
        reliability_score = self._calculate_reliability_score()
        
        comprehensive_report = {
            "test_execution_info": {
                "start_time": self.start_time,
                "end_time": self.end_time,
                "total_duration": self.end_time - self.start_time,
                "test_suites_count": len(self.test_results)
            },
            "overall_statistics": {
                "total_tests": total_tests,
                "successful_tests": total_tests - total_failures - total_errors,
                "failed_tests": total_failures,
                "error_tests": total_errors,
                "skipped_tests": total_skipped,
                "overall_success_rate": overall_success_rate
            },
            "quality_scores": {
                "performance_score": performance_score,
                "usability_score": usability_score,
                "reliability_score": reliability_score,
                "overall_quality_score": (performance_score + usability_score + reliability_score) / 3
            },
            "suite_results": self.test_results,
            "recommendations": self._generate_recommendations()
        }
        
        # 保存报告到文件
        report_filename = f"comprehensive_integration_test_report_{int(time.time())}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"综合测试报告已保存: {report_filename}")
        
        # 打印综合摘要
        self._print_comprehensive_summary(comprehensive_report)
        
        return comprehensive_report
    
    def _calculate_performance_score(self) -> float:
        """计算性能评分"""
        performance_suite = self.test_results.get("性能扩展测试")
        if not performance_suite:
            return 0.0
        
        # 基于成功率和性能指标计算评分
        base_score = performance_suite['success_rate'] * 100
        
        # 如果有性能相关的失败，降低评分
        if performance_suite['failures'] > 0 or performance_suite['errors'] > 0:
            base_score *= 0.8
        
        return min(base_score, 100.0)
    
    def _calculate_usability_score(self) -> float:
        """计算易用性评分"""
        usability_suite = self.test_results.get("用户接受度测试")
        if not usability_suite:
            return 0.0
        
        # 基于用户接受度测试结果计算评分
        base_score = usability_suite['success_rate'] * 100
        
        # 考虑用户满意度（如果有的话）
        if usability_suite['failures'] == 0 and usability_suite['errors'] == 0:
            base_score = min(base_score * 1.1, 100.0)  # 奖励完全成功的情况
        
        return base_score
    
    def _calculate_reliability_score(self) -> float:
        """计算可靠性评分"""
        # 基于所有测试套件的整体表现计算可靠性
        total_success_rates = [result['success_rate'] for result in self.test_results.values()]
        
        if not total_success_rates:
            return 0.0
        
        # 计算平均成功率
        avg_success_rate = sum(total_success_rates) / len(total_success_rates)
        
        # 计算一致性（成功率的标准差，越小越好）
        variance = sum((rate - avg_success_rate) ** 2 for rate in total_success_rates) / len(total_success_rates)
        consistency_factor = max(0, 1 - variance)  # 方差越小，一致性越好
        
        reliability_score = avg_success_rate * 100 * consistency_factor
        
        return min(reliability_score, 100.0)
    
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于测试结果生成建议
        for suite_name, suite_result in self.test_results.items():
            if suite_result['success_rate'] < 0.9:
                recommendations.append(f"改进{suite_name}的稳定性，当前成功率为{suite_result['success_rate']:.1%}")
            
            if suite_result['duration'] > 60:  # 超过1分钟
                recommendations.append(f"优化{suite_name}的执行性能，当前耗时{suite_result['duration']:.1f}秒")
        
        # 通用建议
        overall_success_rate = sum(result['success_rate'] for result in self.test_results.values()) / len(self.test_results)
        
        if overall_success_rate < 0.95:
            recommendations.append("提高整体测试通过率，加强错误处理和边界情况处理")
        
        if not recommendations:
            recommendations.append("系统表现良好，继续保持当前的开发和测试标准")
        
        return recommendations
    
    def _print_comprehensive_summary(self, report: Dict[str, Any]):
        """打印综合测试摘要"""
        self.logger.info("\n" + "="*80)
        self.logger.info("项目文件组织系统集成测试综合报告")
        self.logger.info("="*80)
        
        # 基本统计
        stats = report['overall_statistics']
        self.logger.info(f"\n📊 测试统计:")
        self.logger.info(f"  总测试数: {stats['total_tests']}")
        self.logger.info(f"  成功: {stats['successful_tests']} ({stats['overall_success_rate']:.1%})")
        self.logger.info(f"  失败: {stats['failed_tests']}")
        self.logger.info(f"  错误: {stats['error_tests']}")
        self.logger.info(f"  跳过: {stats['skipped_tests']}")
        
        # 质量评分
        scores = report['quality_scores']
        self.logger.info(f"\n🎯 质量评分:")
        self.logger.info(f"  性能评分: {scores['performance_score']:.1f}/100")
        self.logger.info(f"  易用性评分: {scores['usability_score']:.1f}/100")
        self.logger.info(f"  可靠性评分: {scores['reliability_score']:.1f}/100")
        self.logger.info(f"  综合质量评分: {scores['overall_quality_score']:.1f}/100")
        
        # 执行信息
        exec_info = report['test_execution_info']
        self.logger.info(f"\n⏱️ 执行信息:")
        self.logger.info(f"  总耗时: {exec_info['total_duration']:.2f}秒")
        self.logger.info(f"  测试套件数: {exec_info['test_suites_count']}")
        
        # 改进建议
        recommendations = report['recommendations']
        self.logger.info(f"\n💡 改进建议:")
        for i, recommendation in enumerate(recommendations, 1):
            self.logger.info(f"  {i}. {recommendation}")
        
        # 整体评价
        overall_score = scores['overall_quality_score']
        if overall_score >= 90:
            grade = "优秀 🌟"
        elif overall_score >= 80:
            grade = "良好 👍"
        elif overall_score >= 70:
            grade = "合格 ✅"
        elif overall_score >= 60:
            grade = "需要改进 ⚠️"
        else:
            grade = "不合格 ❌"
        
        self.logger.info(f"\n🏆 整体评价: {grade}")
        self.logger.info("="*80)


class DetailedTestResult(unittest.TestResult):
    """详细的测试结果收集器"""
    
    def __init__(self):
        super().__init__()
        self.test_details = []
    
    def startTest(self, test):
        super().startTest(test)
        self.current_test_start = time.time()
    
    def stopTest(self, test):
        super().stopTest(test)
        duration = time.time() - self.current_test_start
        
        # 确定测试状态
        status = "SUCCESS"
        error_info = None
        
        # 检查是否有失败或错误
        for failure in self.failures:
            if failure[0] == test:
                status = "FAILURE"
                error_info = failure[1]
                break
        
        for error in self.errors:
            if error[0] == test:
                status = "ERROR"
                error_info = error[1]
                break
        
        # 检查是否跳过
        for skipped in self.skipped:
            if skipped[0] == test:
                status = "SKIPPED"
                error_info = skipped[1]
                break
        
        self.test_details.append({
            "test_name": str(test),
            "test_method": test._testMethodName,
            "test_class": test.__class__.__name__,
            "status": status,
            "duration": duration,
            "error_info": error_info
        })
    
    def get_detailed_results(self) -> List[Dict[str, Any]]:
        """获取详细的测试结果"""
        return self.test_details


def main():
    """主函数"""
    logger.info("项目文件组织系统集成测试套件")
    logger.info("="*50)
    
    # 创建测试运行器
    runner = IntegrationTestRunner()
    
    try:
        # 运行所有测试
        comprehensive_report = runner.run_all_tests()
        
        # 根据测试结果设置退出码
        overall_success_rate = comprehensive_report['overall_statistics']['overall_success_rate']
        
        if overall_success_rate >= 0.95:
            logger.info("\n🎉 所有测试基本通过！")
            sys.exit(0)
        elif overall_success_rate >= 0.8:
            logger.info("\n⚠️ 大部分测试通过，但有一些问题需要关注。")
            sys.exit(1)
        else:
            logger.info("\n❌ 测试失败率较高，需要立即修复问题。")
            sys.exit(2)
            
    except Exception as e:
        logger.info(f"\n💥 测试执行过程中发生严重错误: {e}")
        sys.exit(3)


if __name__ == "__main__":
    main()