"""
Tests for data validation and cleaning functionality.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.market.data.validation import (
    OHLCVValidator, TimeSeriesValidator, AnomalyDetector, DataCleaner,
    DataQualityReporter, DataValidator, ValidationSeverity, ValidationIssue,
    ValidationResult
)
from src.market.strategies.models.market_data import UnifiedMarketData
from src.common.exceptions import DataValidationException


class TestOHLCVValidator:
    """Test OHLCV data validation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = OHLCVValidator()
    
    def test_validate_valid_record(self):
        """Test validation of valid market data record."""
        data = UnifiedMarketData(
            symbol="AAPL",
            timestamp=datetime(2023, 1, 1),
            open=150.0,
            high=155.0,
            low=149.0,
            close=154.0,
            volume=1000000,
            market="US",
            exchange="NASDAQ",
            currency="USD"
        )
        
        result = self.validator.validate_single_record(data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_validate_invalid_prices(self):
        """Test validation of invalid price data."""
        data = UnifiedMarketData(
            symbol="AAPL",
            timestamp=datetime(2023, 1, 1),
            open=-150.0,  # Negative price
            high=155.0,
            low=149.0,
            close=154.0,
            volume=1000000,
            market="US",
            exchange="NASDAQ",
            currency="USD"
        )
        
        result = self.validator.validate_single_record(data)
        assert not result.is_valid
        assert any(issue.issue_type == "negative_price" for issue in result.issues)
    
    def test_validate_ohlc_inconsistency(self):
        """Test validation of OHLC inconsistencies."""
        data = UnifiedMarketData(
            symbol="AAPL",
            timestamp=datetime(2023, 1, 1),
            open=150.0,
            high=148.0,  # High less than open
            low=149.0,
            close=154.0,
            volume=1000000,
            market="US",
            exchange="NASDAQ",
            currency="USD"
        )
        
        result = self.validator.validate_single_record(data)
        assert not result.is_valid
        assert any(issue.issue_type == "high_price_inconsistent" for issue in result.issues)
    
    def test_validate_dataframe(self):
        """Test validation of DataFrame."""
        df = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [105, 106, 107],
            'low': [99, 100, 101],
            'close': [104, 105, 106],
            'volume': [1000, 1100, 1200]
        }, index=pd.date_range('2023-01-01', periods=3))
        
        result = self.validator.validate_dataframe(df, "TEST")
        assert result.is_valid
    
    def test_validate_missing_columns(self):
        """Test validation with missing required columns."""
        df = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [105, 106, 107],
            # Missing 'low', 'close', 'volume'
        })
        
        result = self.validator.validate_dataframe(df, "TEST")
        assert not result.is_valid
        assert any(issue.issue_type == "missing_columns" for issue in result.issues)


class TestTimeSeriesValidator:
    """Test time series validation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = TimeSeriesValidator()
    
    def test_validate_complete_timeseries(self):
        """Test validation of complete time series."""
        df = pd.DataFrame({
            'close': [100, 101, 102, 103, 104]
        }, index=pd.date_range('2023-01-01', periods=5))
        
        result = self.validator.validate_completeness(df, "TEST")
        assert result.is_valid
    
    def test_validate_duplicate_timestamps(self):
        """Test validation with duplicate timestamps."""
        dates = [datetime(2023, 1, 1), datetime(2023, 1, 1), datetime(2023, 1, 2)]
        df = pd.DataFrame({
            'close': [100, 101, 102]
        }, index=dates)
        
        result = self.validator.validate_completeness(df, "TEST")
        assert not result.is_valid
        assert any(issue.issue_type == "duplicate_timestamps" for issue in result.issues)
    
    def test_validate_unordered_timestamps(self):
        """Test validation with unordered timestamps."""
        dates = [datetime(2023, 1, 3), datetime(2023, 1, 1), datetime(2023, 1, 2)]
        df = pd.DataFrame({
            'close': [100, 101, 102]
        }, index=dates)
        
        result = self.validator.validate_completeness(df, "TEST")
        assert not result.is_valid
        assert any(issue.issue_type == "unordered_timestamps" for issue in result.issues)
    
    def test_validate_insufficient_data(self):
        """Test validation with insufficient data points."""
        df = pd.DataFrame({
            'close': [100, 101]
        }, index=pd.date_range('2023-01-01', periods=2))
        
        result = self.validator.validate_completeness(df, "TEST")
        # Should be valid but with warnings
        assert any(issue.issue_type == "insufficient_data" for issue in result.issues)


class TestAnomalyDetector:
    """Test anomaly detection."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.detector = AnomalyDetector()
    
    def test_detect_price_jump(self):
        """Test detection of price jumps."""
        # Create data with a large price jump
        prices = [100] * 10 + [150] + [100] * 10  # 50% jump
        df = pd.DataFrame({
            'close': prices,
            'volume': [1000] * len(prices)
        }, index=pd.date_range('2023-01-01', periods=len(prices)))
        
        issues = self.detector.detect_price_anomalies(df)
        assert len(issues) > 0
        assert any(issue.issue_type == "price_jump" for issue in issues)
    
    def test_detect_volume_spike(self):
        """Test detection of volume spikes."""
        # Create data with volume spike
        volumes = [1000] * 25 + [10000] + [1000] * 5  # 10x spike
        df = pd.DataFrame({
            'close': [100] * len(volumes),
            'volume': volumes
        }, index=pd.date_range('2023-01-01', periods=len(volumes)))
        
        issues = self.detector.detect_volume_anomalies(df)
        assert len(issues) > 0
        assert any(issue.issue_type == "volume_spike" for issue in issues)
    
    def test_detect_zero_volume(self):
        """Test detection of zero volume."""
        df = pd.DataFrame({
            'close': [100, 101, 102],
            'volume': [1000, 0, 1100]  # Zero volume in middle
        }, index=pd.date_range('2023-01-01', periods=3))
        
        issues = self.detector.detect_volume_anomalies(df)
        assert any(issue.issue_type == "zero_volume" for issue in issues)


class TestDataCleaner:
    """Test data cleaning functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.cleaner = DataCleaner()
    
    def test_handle_missing_values_forward_fill(self):
        """Test forward fill of missing values."""
        df = pd.DataFrame({
            'close': [100, np.nan, np.nan, 103],
            'volume': [1000, 1100, np.nan, 1300]
        }, index=pd.date_range('2023-01-01', periods=4))
        
        cleaned_df, operations = self.cleaner.handle_missing_values(df)
        
        # Check that missing values are filled
        assert not cleaned_df['close'].isnull().any()
        assert len(operations) > 0
    
    def test_handle_outliers_clip(self):
        """Test outlier clipping."""
        # Create data with outliers
        data = [100] * 20 + [1000, 10]  # Extreme outliers
        df = pd.DataFrame({
            'close': data
        }, index=pd.date_range('2023-01-01', periods=len(data)))
        
        cleaned_df, operations = self.cleaner.handle_outliers(df, ['close'])
        
        # Check that outliers are clipped
        assert cleaned_df['close'].max() < 1000
        assert cleaned_df['close'].min() > 10
        assert len(operations) > 0
    
    def test_fix_ohlc_inconsistencies(self):
        """Test fixing OHLC inconsistencies."""
        df = pd.DataFrame({
            'open': [100, 101],
            'high': [99, 106],  # First high is less than open
            'low': [98, 107],   # Second low is greater than high
            'close': [104, 105]
        }, index=pd.date_range('2023-01-01', periods=2))
        
        cleaned_df, operations = self.cleaner.fix_ohlc_inconsistencies(df)
        
        # Check that inconsistencies are fixed
        assert all(cleaned_df['high'] >= cleaned_df[['open', 'close']].max(axis=1))
        assert all(cleaned_df['low'] <= cleaned_df[['open', 'close']].min(axis=1))
        assert len(operations) > 0
    
    def test_clean_data_comprehensive(self):
        """Test comprehensive data cleaning."""
        df = pd.DataFrame({
            'open': [100, np.nan, 102],
            'high': [99, 106, 107],  # First high less than open
            'low': [98, 100, 101],
            'close': [104, 105, 106],
            'volume': [1000, 1100, 10000]  # Last volume is outlier
        }, index=pd.date_range('2023-01-01', periods=3))
        
        cleaned_df, report = self.cleaner.clean_data(df)
        
        assert 'original_rows' in report
        assert 'cleaned_rows' in report
        assert 'operations' in report
        assert len(report['operations']) > 0


class TestDataQualityReporter:
    """Test data quality reporting."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.reporter = DataQualityReporter()
    
    def test_generate_quality_report(self):
        """Test generation of quality report."""
        df = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [105, 106, 107],
            'low': [99, 100, 101],
            'close': [104, 105, 106],
            'volume': [1000, 1100, 1200]
        }, index=pd.date_range('2023-01-01', periods=3))
        
        report = self.reporter.generate_quality_report(df, "TEST", "US")
        
        assert 'symbol' in report
        assert 'data_summary' in report
        assert 'validation_results' in report
        assert 'anomaly_detection' in report
        assert 'recommendations' in report
        
        # Check data summary
        assert report['data_summary']['total_records'] == 3
        assert 'date_range' in report['data_summary']
    
    def test_generate_recommendations(self):
        """Test recommendation generation."""
        # Create a mock report with issues
        report = {
            'validation_results': {
                'ohlcv': {'summary': {'critical': 1, 'errors': 0}},
                'timeseries': {'summary': {'errors': 1}}
            },
            'data_summary': {'missing_values': {'close': 5}},
            'anomaly_detection': {'total_anomalies': 10}
        }
        
        recommendations = self.reporter._generate_recommendations(report)
        
        assert len(recommendations) > 0
        assert any("Critical" in rec for rec in recommendations)


class TestDataValidator:
    """Test main data validator interface."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = DataValidator()
    
    def test_validate_market_data_single_record(self):
        """Test validation of single market data record."""
        data = UnifiedMarketData(
            symbol="AAPL",
            timestamp=datetime(2023, 1, 1),
            open=150.0,
            high=155.0,
            low=149.0,
            close=154.0,
            volume=1000000,
            market="US",
            exchange="NASDAQ",
            currency="USD"
        )
        
        result = self.validator.validate_market_data(data)
        assert isinstance(result, ValidationResult)
    
    def test_validate_market_data_dataframe(self):
        """Test validation of DataFrame."""
        df = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [105, 106, 107],
            'low': [99, 100, 101],
            'close': [104, 105, 106],
            'volume': [1000, 1100, 1200]
        }, index=pd.date_range('2023-01-01', periods=3))
        
        result = self.validator.validate_market_data(df, "TEST")
        assert isinstance(result, ValidationResult)
    
    def test_validate_unsupported_data_type(self):
        """Test validation with unsupported data type."""
        with pytest.raises(DataValidationException):
            self.validator.validate_market_data("invalid_data")
    
    def test_clean_market_data(self):
        """Test market data cleaning."""
        df = pd.DataFrame({
            'open': [100, np.nan, 102],
            'high': [105, 106, 107],
            'low': [99, 100, 101],
            'close': [104, 105, 106],
            'volume': [1000, 1100, 1200]
        }, index=pd.date_range('2023-01-01', periods=3))
        
        cleaned_df, report = self.validator.clean_market_data(df)
        
        assert isinstance(cleaned_df, pd.DataFrame)
        assert isinstance(report, dict)
        assert 'operations' in report
    
    def test_generate_quality_report(self):
        """Test quality report generation."""
        df = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [105, 106, 107],
            'low': [99, 100, 101],
            'close': [104, 105, 106],
            'volume': [1000, 1100, 1200]
        }, index=pd.date_range('2023-01-01', periods=3))
        
        report = self.validator.generate_quality_report(df, "TEST", "US")
        
        assert isinstance(report, dict)
        assert 'symbol' in report
        assert 'validation_results' in report
    
    def test_validate_and_clean(self):
        """Test combined validation and cleaning."""
        df = pd.DataFrame({
            'open': [100, np.nan, 102],
            'high': [105, 106, 107],
            'low': [99, 100, 101],
            'close': [104, 105, 106],
            'volume': [1000, 1100, 1200]
        }, index=pd.date_range('2023-01-01', periods=3))
        
        cleaned_df, combined_report = self.validator.validate_and_clean(df, "TEST")
        
        assert isinstance(cleaned_df, pd.DataFrame)
        assert isinstance(combined_report, dict)
        assert 'validation' in combined_report
        assert 'cleaning' in combined_report
        assert 'final_quality' in combined_report


class TestValidationModels:
    """Test validation model classes."""
    
    def test_validation_issue_creation(self):
        """Test ValidationIssue creation."""
        issue = ValidationIssue(
            severity=ValidationSeverity.ERROR,
            issue_type="test_issue",
            message="Test message",
            timestamp=datetime(2023, 1, 1),
            value=100.0
        )
        
        assert issue.severity == ValidationSeverity.ERROR
        assert issue.issue_type == "test_issue"
        assert issue.message == "Test message"
        assert issue.timestamp == datetime(2023, 1, 1)
        assert issue.value == 100.0
    
    def test_validation_result_creation(self):
        """Test ValidationResult creation and summary calculation."""
        issues = [
            ValidationIssue(ValidationSeverity.CRITICAL, "critical_issue", "Critical"),
            ValidationIssue(ValidationSeverity.ERROR, "error_issue", "Error"),
            ValidationIssue(ValidationSeverity.WARNING, "warning_issue", "Warning"),
            ValidationIssue(ValidationSeverity.INFO, "info_issue", "Info")
        ]
        
        result = ValidationResult(True, issues, {})
        
        assert result.summary['total_issues'] == 4
        assert result.summary['critical'] == 1
        assert result.summary['errors'] == 1
        assert result.summary['warnings'] == 1
        assert result.summary['info'] == 1
        assert not result.is_valid  # Should be invalid due to critical and error issues
    
    def test_validation_result_valid_data(self):
        """Test ValidationResult with only warnings and info."""
        issues = [
            ValidationIssue(ValidationSeverity.WARNING, "warning_issue", "Warning"),
            ValidationIssue(ValidationSeverity.INFO, "info_issue", "Info")
        ]
        
        result = ValidationResult(True, issues, {})
        
        assert result.is_valid  # Should be valid with only warnings and info


if __name__ == "__main__":
    pytest.main([__file__])