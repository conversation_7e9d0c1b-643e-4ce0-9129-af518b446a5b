"""
Tests for data import and management functionality.
"""

import unittest
import tempfile
import os
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.market.data.management_interface import get_data_management_interface
from src.market.data.importers import get_import_manager


class TestDataImportManagement(unittest.TestCase):
    """Test cases for data import and management."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.dm = get_data_management_interface()
        self.import_manager = get_import_manager()
        
        # Create temporary directory for test files
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """Clean up test fixtures."""
        # Clean up temporary files
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_csv_file(self, filename: str = 'test_data.csv') -> str:
        """Create a test CSV file."""
        # Generate test data
        dates = pd.date_range(start='2023-01-01', end='2023-01-10', freq='D')
        data = []
        
        for i, date in enumerate(dates):
            data.append({
                'symbol': 'TEST',
                'timestamp': date.strftime('%Y-%m-%d'),
                'open': 100.0 + i,
                'high': 102.0 + i,
                'low': 98.0 + i,
                'close': 101.0 + i,
                'volume': 1000000 + (i * 1000)
            })
        
        df = pd.DataFrame(data)
        file_path = os.path.join(self.temp_dir, filename)
        df.to_csv(file_path, index=False)
        
        return file_path
    
    def test_file_validation(self):
        """Test file validation functionality."""
        # Create test file
        test_file = self.create_test_csv_file()
        
        # Validate file
        result = self.dm.validate_import_file(test_file)
        
        self.assertIsInstance(result, dict)
        self.assertIn('valid', result)
        self.assertTrue(result['valid'])
        self.assertIn('file_info', result)
        self.assertIn('columns', result)
    
    def test_file_preview(self):
        """Test file preview functionality."""
        # Create test file
        test_file = self.create_test_csv_file()
        
        # Get preview
        success, preview = self.dm.preview_import_file(test_file, rows=3)
        
        self.assertTrue(success)
        self.assertIsInstance(preview, pd.DataFrame)
        self.assertEqual(len(preview), 3)
        self.assertIn('symbol', preview.columns)
    
    def test_import_task_creation(self):
        """Test import task creation."""
        # Create test file
        test_file = self.create_test_csv_file()
        
        # Create import task
        task_id = self.dm.create_import_task(
            file_path=test_file,
            market='US',
            exchange='TEST',
            currency='USD'
        )
        
        self.assertIsInstance(task_id, str)
        self.assertTrue(len(task_id) > 0)
        
        # Check task status
        status = self.dm.get_import_task_status(task_id)
        self.assertIsNotNone(status)
        self.assertEqual(status['status'], 'pending')
        self.assertEqual(status['market'], 'US')
    
    def test_import_task_listing(self):
        """Test import task listing."""
        # Create test file
        test_file = self.create_test_csv_file()
        
        # Create multiple tasks
        task_id1 = self.dm.create_import_task(test_file, market='US')
        task_id2 = self.dm.create_import_task(test_file, market='CN')
        
        # List all tasks
        all_tasks = self.dm.list_import_tasks()
        self.assertGreaterEqual(len(all_tasks), 2)
        
        # List pending tasks
        pending_tasks = self.dm.list_import_tasks(status_filter='pending')
        self.assertGreaterEqual(len(pending_tasks), 2)
        
        # Check task IDs are in the list
        task_ids = [task['id'] for task in all_tasks]
        self.assertIn(task_id1, task_ids)
        self.assertIn(task_id2, task_ids)
    
    def test_sync_task_creation(self):
        """Test sync task creation."""
        symbols = ['AAPL', 'GOOGL']
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        # Create sync task
        task_id = self.dm.create_sync_task(
            symbols=symbols,
            start_date=start_date,
            end_date=end_date,
            interval='1d'
        )
        
        self.assertIsInstance(task_id, str)
        self.assertTrue(len(task_id) > 0)
        
        # Check task status
        status = self.dm.get_sync_task_status(task_id)
        self.assertIsNotNone(status)
        self.assertEqual(status['status'], 'pending')
        self.assertEqual(status['symbols'], symbols)
    
    def test_cache_operations(self):
        """Test cache management operations."""
        # Get cache statistics
        stats = self.dm.get_cache_statistics()
        self.assertIsInstance(stats, dict)
        self.assertIn('memory_cache', stats)
        self.assertIn('disk_cache', stats)
        
        # Cleanup cache
        cleanup_result = self.dm.cleanup_cache()
        self.assertIsInstance(cleanup_result, dict)
        
        # Clear cache
        clear_result = self.dm.clear_cache()
        self.assertIsInstance(clear_result, dict)
    
    def test_system_health(self):
        """Test system health monitoring."""
        health = self.dm.get_system_health()
        
        self.assertIsInstance(health, dict)
        self.assertIn('database', health)
        self.assertIn('cache', health)
        self.assertIn('data_sources', health)
        self.assertIn('tasks', health)
        self.assertIn('timestamp', health)
    
    def test_data_sources_status(self):
        """Test data sources status."""
        status = self.dm.get_data_sources_status()
        
        self.assertIsInstance(status, dict)
        # Should have at least the mock adapter
        self.assertGreater(len(status), 0)
    
    def test_coverage_operations(self):
        """Test data coverage operations."""
        # Get coverage summary
        summary = self.dm.get_data_coverage_summary()
        self.assertIsInstance(summary, dict)
        
        # Test symbol coverage (may return None if no data)
        symbol_coverage = self.dm.get_symbol_coverage('AAPL', 'US')
        # This may be None if no data exists, which is fine for testing
        
        # Test market coverage (may return None if no data)
        market_coverage = self.dm.get_market_coverage('US')
        # This may be None if no data exists, which is fine for testing
    
    def test_import_manager_functionality(self):
        """Test import manager specific functionality."""
        # Create test file
        test_file = self.create_test_csv_file()
        
        # Test file validation
        is_valid, message = self.import_manager.validate_file(test_file)
        self.assertTrue(is_valid)
        
        # Test preview
        success, preview = self.import_manager.get_import_preview(test_file, 3)
        self.assertTrue(success)
        self.assertIsInstance(preview, pd.DataFrame)
        
        # Test supported formats
        formats = self.import_manager.get_supported_formats()
        self.assertIn('.csv', formats)
        
        # Test file structure validation
        structure = self.import_manager.validate_file_structure(test_file)
        self.assertIsInstance(structure, dict)
        self.assertIn('valid', structure)
    
    def test_auto_column_detection(self):
        """Test automatic column detection."""
        # Create test file with different column names
        data = [{
            'code': 'TEST',  # Different name for symbol
            'date': '2023-01-01',  # Different name for timestamp
            'opening': 100.0,  # Different name for open
            'highest': 102.0,  # Different name for high
            'lowest': 98.0,  # Different name for low
            'closing': 101.0,  # Different name for close
            'vol': 1000000  # Different name for volume
        }]
        
        df = pd.DataFrame(data)
        file_path = os.path.join(self.temp_dir, 'test_auto_detect.csv')
        df.to_csv(file_path, index=False)
        
        # Test auto detection
        mappings = self.import_manager.auto_detect_columns(df)
        
        # Should detect some mappings
        self.assertIsInstance(mappings, dict)
        # May not detect all columns due to variations, but should detect some
    
    def test_task_cleanup(self):
        """Test task cleanup functionality."""
        # Create some tasks
        test_file = self.create_test_csv_file()
        task_id = self.dm.create_import_task(test_file)
        
        # Test cleanup (should not remove recent tasks)
        cleanup_result = self.dm.cleanup_old_tasks(days=1)
        self.assertIsInstance(cleanup_result, dict)
        self.assertIn('import_tasks_removed', cleanup_result)
        self.assertIn('sync_tasks_removed', cleanup_result)


class TestDataImportManagerSpecific(unittest.TestCase):
    """Test cases specific to DataImportManager."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.import_manager = get_import_manager()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_encoding_detection(self):
        """Test file encoding detection."""
        # Create a simple CSV file
        data = "symbol,timestamp,open,high,low,close,volume\nTEST,2023-01-01,100,102,98,101,1000000"
        file_path = os.path.join(self.temp_dir, 'test_encoding.csv')
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(data)
        
        # Test encoding detection
        encoding = self.import_manager.detect_file_encoding(file_path)
        self.assertIsInstance(encoding, str)
        self.assertTrue(len(encoding) > 0)
    
    def test_file_type_detection(self):
        """Test file type detection."""
        # Test CSV
        csv_file = os.path.join(self.temp_dir, 'test.csv')
        Path(csv_file).touch()
        
        file_type = self.import_manager.detect_file_type(csv_file)
        self.assertEqual(file_type, 'csv')
        
        # Test Excel
        excel_file = os.path.join(self.temp_dir, 'test.xlsx')
        Path(excel_file).touch()
        
        file_type = self.import_manager.detect_file_type(excel_file)
        self.assertEqual(file_type, 'excel')
        
        # Test unsupported
        txt_file = os.path.join(self.temp_dir, 'test.txt')
        Path(txt_file).touch()
        
        file_type = self.import_manager.detect_file_type(txt_file)
        self.assertIsNone(file_type)
    
    def test_import_statistics(self):
        """Test import statistics."""
        stats = self.import_manager.get_import_statistics()
        
        self.assertIsInstance(stats, dict)
        self.assertIn('total_imports', stats)
        self.assertIn('successful_imports', stats)
        self.assertIn('failed_imports', stats)
        self.assertIn('supported_formats', stats)


if __name__ == '__main__':
    unittest.main()