import logging
logger = logging.getLogger(__name__)
"""
Integration tests for Configuration Management System
"""

import pytest
import tempfile
import shutil
from pathlib import Path

from config.configuration_manager import ConfigurationManager


class TestConfigurationSystemIntegration:
    """Integration tests for the complete configuration management system."""
    
    @pytest.fixture
    def temp_config_dir(self):
        """Create temporary configuration directory."""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def config_manager(self, temp_config_dir):
        """Create ConfigurationManager instance with temporary directory."""
        return ConfigurationManager(str(temp_config_dir), environment='testing')
    
    def test_complete_configuration_workflow(self, config_manager):
        """Test complete configuration management workflow."""
        # Step 1: Fix missing configurations
        fixed_configs = config_manager.fix_missing_configs()
        assert len(fixed_configs) > 0
        
        # Step 2: Validate all configurations
        validation_result = config_manager.validate_all_configs()
        
        # Should be valid or have only warnings (not errors)
        if not validation_result.is_valid:
            logger.info("Validation errors:", validation_result.errors)
            logger.info("Validation warnings:", validation_result.warnings)
            
            # Check that there are no critical validation errors
            critical_errors = [e for e in validation_result.errors 
                             if "Required field" in e and "missing" in e]
            assert len(critical_errors) == 0, f"Critical validation errors: {critical_errors}"
        
        # Step 3: Create backup
        backup_info = config_manager.backup_configurations("integration test backup")
        assert backup_info is not None
        assert backup_info.backup_path.exists()
        
        # Step 4: Modify configuration
        success = config_manager.set_config("test.integration", "test_value")
        assert success
        
        value = config_manager.get_config("test.integration")
        assert value == "test_value"
        
        # Step 5: Update environment configuration
        success = config_manager.update_environment_configs("development")
        assert success
        
        # Step 6: Validate again after changes
        validation_result = config_manager.validate_all_configs()
        # Should still be valid or have only warnings
        
        # Step 7: Restore from backup
        success = config_manager.restore_configurations(backup_info.backup_path)
        assert success
        
        # Step 8: Verify restoration
        value = config_manager.get_config("test.integration")
        assert value is None  # Should be gone after restore
    
    def test_configuration_generation_and_validation(self, config_manager):
        """Test configuration generation followed by validation."""
        # Generate configurations with specific user inputs
        user_inputs = {
            'database_host': 'localhost',
            'database_port': 5432,
            'database_name': 'test_db',
            'database_username': 'test_user',
            'database_password': 'test_pass',
            'api_host': '0.0.0.0',
            'api_port': 8000,
            'generate_secrets': True,
            'enable_demo_data': True
        }
        
        # Generate all configurations
        all_configs = config_manager.generator.generate_all_configs('testing', user_inputs)
        assert len(all_configs) > 0
        
        # Save generated configurations
        for config_type, config_data in all_configs.items():
            if config_type == 'system':
                file_path = config_manager.config_root / "core" / "system.yaml"
            elif config_type == 'environment':
                file_path = config_manager.config_root / "environments" / "testing.yaml"
            elif config_type == 'datasources':
                file_path = config_manager.config_root / "datasources" / "sources.yaml"
            elif config_type == 'markets':
                file_path = config_manager.config_root / "core" / "markets.yaml"
            elif config_type == 'logging':
                file_path = config_manager.config_root / "logging" / "logging_optimization.yaml"
            elif config_type == 'security':
                file_path = config_manager.config_root / "security" / "security.yaml"
            else:
                continue
            
            success = config_manager.loader.save_config(config_data, file_path)
            assert success
        
        # Reload configurations
        success = config_manager.reload_configurations()
        assert success
        
        # Validate the generated configurations
        validation_result = config_manager.validate_all_configs()
        
        # Generated configurations should be valid
        if not validation_result.is_valid:
            logger.info("Generated config validation errors:", validation_result.errors)
            logger.info("Generated config validation warnings:", validation_result.warnings)
            
            # Should not have critical errors for properly generated configs
            critical_errors = [e for e in validation_result.errors 
                             if any(keyword in e.lower() for keyword in ['required', 'missing', 'invalid type'])]
            assert len(critical_errors) == 0, f"Critical errors in generated configs: {critical_errors}"
    
    def test_configuration_file_formats(self, config_manager):
        """Test loading and saving different configuration file formats."""
        test_config = {
            'database': {
                'host': 'localhost',
                'port': 5432
            },
            'api': {
                'host': '0.0.0.0',
                'port': 8000
            }
        }
        
        # Test YAML format
        yaml_file = config_manager.config_root / "test.yaml"
        success = config_manager.loader.save_config(test_config, yaml_file)
        assert success
        
        loaded_yaml = config_manager.loader.load_file(yaml_file)
        assert loaded_yaml == test_config
        
        # Validate YAML format
        format_result = config_manager.validator.validate_file_format(yaml_file)
        assert format_result.is_valid
        
        # Test JSON format
        json_file = config_manager.config_root / "test.json"
        success = config_manager.loader.save_config(test_config, json_file)
        assert success
        
        loaded_json = config_manager.loader.load_file(json_file)
        assert loaded_json == test_config
        
        # Validate JSON format
        format_result = config_manager.validator.validate_file_format(json_file)
        assert format_result.is_valid
        
        # Test .env format
        env_config = {
            'DATABASE_HOST': 'localhost',
            'DATABASE_PORT': '5432',
            'API_HOST': '0.0.0.0',
            'API_PORT': '8000'
        }
        
        env_file = config_manager.config_root / "test.env"
        success = config_manager.loader.save_config(env_config, env_file)
        assert success
        
        loaded_env = config_manager.loader.load_file(env_file)
        assert loaded_env == env_config
        
        # Validate .env format
        format_result = config_manager.validator.validate_file_format(env_file)
        assert format_result.is_valid
    
    def test_configuration_merging_and_inheritance(self, config_manager):
        """Test configuration merging and environment inheritance."""
        # Create base configuration
        base_config = {
            'database': {
                'host': 'localhost',
                'port': 5432,
                'pool_size': 10
            },
            'api': {
                'host': '0.0.0.0',
                'port': 8000,
                'debug': False
            }
        }
        
        base_file = config_manager.config_root / "core" / "system.yaml"
        base_file.parent.mkdir(parents=True, exist_ok=True)
        success = config_manager.loader.save_config(base_config, base_file)
        assert success
        
        # Create environment-specific override
        env_config = {
            'database': {
                'host': 'test-db-host',  # Override
                'echo': True  # Add new field
            },
            'api': {
                'debug': True  # Override for testing
            },
            'logging': {  # Add new section
                'level': 'DEBUG'
            }
        }
        
        env_file = config_manager.config_root / "environments" / "testing.yaml"
        env_file.parent.mkdir(parents=True, exist_ok=True)
        success = config_manager.loader.save_config(env_config, env_file)
        assert success
        
        # Load environment configuration with inheritance
        merged_config = config_manager.loader.load_environment_config('testing')
        
        # Check merging worked correctly
        assert merged_config['database']['host'] == 'test-db-host'  # Overridden
        assert merged_config['database']['port'] == 5432  # Inherited
        assert merged_config['database']['pool_size'] == 10  # Inherited
        assert merged_config['database']['echo'] is True  # Added
        
        assert merged_config['api']['host'] == '0.0.0.0'  # Inherited
        assert merged_config['api']['port'] == 8000  # Inherited
        assert merged_config['api']['debug'] is True  # Overridden
        
        assert merged_config['logging']['level'] == 'DEBUG'  # Added
        
        # Check metadata
        assert merged_config['_environment'] == 'testing'
        assert '_loaded_at' in merged_config
    
    def test_configuration_backup_and_restore_cycle(self, config_manager):
        """Test complete backup and restore cycle."""
        # Generate initial configurations
        config_manager.fix_missing_configs()
        
        # Set some custom values
        config_manager.set_config("custom.value1", "original_value1")
        config_manager.set_config("custom.value2", "original_value2")
        
        # Create first backup
        backup1 = config_manager.backup_configurations("backup1")
        
        # Modify configurations
        config_manager.set_config("custom.value1", "modified_value1")
        config_manager.set_config("custom.value3", "new_value3")
        
        # Create second backup
        backup2 = config_manager.backup_configurations("backup2")
        
        # Verify current state
        assert config_manager.get_config("custom.value1") == "modified_value1"
        assert config_manager.get_config("custom.value2") == "original_value2"
        assert config_manager.get_config("custom.value3") == "new_value3"
        
        # Restore from first backup
        success = config_manager.restore_configurations(backup1.backup_path)
        assert success
        
        # Verify restoration to first backup state
        assert config_manager.get_config("custom.value1") == "original_value1"
        assert config_manager.get_config("custom.value2") == "original_value2"
        assert config_manager.get_config("custom.value3") is None  # Should be gone
        
        # Restore from second backup
        success = config_manager.restore_configurations(backup2.backup_path)
        assert success
        
        # Verify restoration to second backup state
        assert config_manager.get_config("custom.value1") == "modified_value1"
        assert config_manager.get_config("custom.value2") == "original_value2"
        assert config_manager.get_config("custom.value3") == "new_value3"
        
        # Check backup history
        history = config_manager.get_backup_history()
        assert len(history) >= 4  # 2 explicit + 2 pre-restore backups
        
        # Cleanup old backups
        removed_count = config_manager.cleanup_old_backups(keep_count=2)
        assert removed_count >= 0
    
    def test_environment_switching(self, config_manager):
        """Test switching between different environments."""
        # Start with testing environment
        assert config_manager.get_environment() == 'testing'
        
        # Generate configurations for testing
        config_manager.fix_missing_configs()
        config_manager.set_config("env_test.current", "testing_value")
        
        # Switch to development environment
        success = config_manager.set_environment('development')
        assert success
        assert config_manager.get_environment() == 'development'
        
        # Value should not be available in new environment
        value = config_manager.get_config("env_test.current")
        assert value is None
        
        # Set value in development environment
        config_manager.set_config("env_test.current", "development_value")
        assert config_manager.get_config("env_test.current") == "development_value"
        
        # Switch back to testing environment
        success = config_manager.set_environment('testing')
        assert success
        assert config_manager.get_environment() == 'testing'
        
        # Original testing value should be back
        value = config_manager.get_config("env_test.current")
        # Note: This might be None if configurations weren't properly saved/loaded
        # In a real implementation, you'd want to persist environment-specific configs
    
    def test_configuration_validation_with_dependencies(self, config_manager):
        """Test configuration validation including dependency checks."""
        # Create configuration with dependency issues
        config_data = {
            'system': {
                'api': {
                    'auth_enabled': True,
                    # Missing jwt_secret - should cause dependency error
                    'host': '0.0.0.0',
                    'port': 8000
                },
                'database': {
                    'host': 'localhost',
                    'port': 5432,
                    'database': 'test_db',
                    'username': 'test_user'
                    # Missing password when username is provided
                }
            },
            'environment': {
                'environment': 'production'
            }
        }
        
        # Set the configurations
        for section, section_data in config_data.items():
            for key, value in section_data.items():
                config_manager.set_config(key, value, section)
        
        # Validate configurations
        validation_result = config_manager.validate_all_configs()
        
        # Should have dependency errors
        assert not validation_result.is_valid
        assert len(validation_result.errors) > 0
        
        # Check for specific dependency errors
        error_messages = ' '.join(validation_result.errors).lower()
        assert 'jwt' in error_messages or 'secret' in error_messages
        
        # Fix the dependency issues
        config_manager.set_config('api.jwt_secret', 'a' * 64, 'system')  # Long enough secret
        config_manager.set_config('security.secret_key', 'b' * 64, 'system')  # Production needs strong secret
        
        # Validate again
        validation_result = config_manager.validate_all_configs()
        
        # Should have fewer errors now
        remaining_errors = [e for e in validation_result.errors 
                          if 'jwt' in e.lower() or 'secret' in e.lower()]
        assert len(remaining_errors) == 0