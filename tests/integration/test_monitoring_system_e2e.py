import logging
logger = logging.getLogger(__name__)
"""
监控系统端到端集成测试

测试监控系统的端到端功能
"""

import pytest
import asyncio
import time
import json
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from dataclasses import asdict

from src.monitoring.monitoring_optimizer import MonitoringOptimizer, MonitoringOptimizationConfig, OptimizationLevel
from src.monitoring.polling_manager import PollingManager, PollingConfig, SystemActivity
from src.monitoring.cache_manager import CacheManager, CacheConfig
from src.monitoring.throttle_manager import ThrottleManagerRegistry, ThrottleConfig
from src.error_handling.enhanced_error_handler import EnhancedErrorHandler, ErrorHandlingConfig


class MockDataSource:
    """模拟数据源"""
    
    def __init__(self, name, latency=0.1, error_rate=0.0):
        self.name = name
        self.latency = latency
        self.error_rate = error_rate
        self.request_count = 0
        self.last_request_time = None
    
    async def fetch_data(self):
        """获取数据"""
        self.request_count += 1
        self.last_request_time = datetime.now()
        
        # 模拟网络延迟
        await asyncio.sleep(self.latency)
        
        # 模拟错误
        import random
        if random.random() < self.error_rate:
            raise Exception(f"Data source {self.name} error")
        
        # 返回模拟数据
        return {
            'source': self.name,
            'timestamp': datetime.now().isoformat(),
            'data': f"sample_data_{self.request_count}",
            'request_id': self.request_count
        }
    
    def get_stats(self):
        """获取统计信息"""
        return {
            'name': self.name,
            'request_count': self.request_count,
            'last_request_time': self.last_request_time.isoformat() if self.last_request_time else None,
            'latency': self.latency,
            'error_rate': self.error_rate
        }


class MockSystemMonitor:
    """模拟系统监控器"""
    
    def __init__(self):
        self.metrics_history = []
        self.alerts = []
        self.running = False
    
    async def start(self):
        """启动监控"""
        self.running = True
    
    async def stop(self):
        """停止监控"""
        self.running = False
    
    async def collect_metrics(self):
        """收集系统指标"""
        if not self.running:
            return None
        
        # 模拟系统指标收集
        import random
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'cpu_usage': random.uniform(10, 90),
            'memory_usage': random.uniform(30, 80),
            'disk_usage': random.uniform(20, 70),
            'network_io': random.uniform(100, 1000),
            'active_connections': random.randint(5, 50)
        }
        
        self.metrics_history.append(metrics)
        
        # 限制历史大小
        if len(self.metrics_history) > 100:
            self.metrics_history = self.metrics_history[-100:]
        
        return metrics
    
    def check_thresholds(self, metrics):
        """检查阈值并生成告警"""
        alerts = []
        
        if metrics['cpu_usage'] > 80:
            alerts.append({
                'type': 'cpu_high',
                'message': f"CPU usage is {metrics['cpu_usage']:.1f}%",
                'severity': 'warning',
                'timestamp': metrics['timestamp']
            })
        
        if metrics['memory_usage'] > 85:
            alerts.append({
                'type': 'memory_high',
                'message': f"Memory usage is {metrics['memory_usage']:.1f}%",
                'severity': 'critical',
                'timestamp': metrics['timestamp']
            })
        
        self.alerts.extend(alerts)
        return alerts
    
    def get_metrics_summary(self):
        """获取指标摘要"""
        if not self.metrics_history:
            return None
        
        recent_metrics = self.metrics_history[-10:]  # 最近10个指标
        
        return {
            'total_metrics': len(self.metrics_history),
            'recent_avg_cpu': sum(m['cpu_usage'] for m in recent_metrics) / len(recent_metrics),
            'recent_avg_memory': sum(m['memory_usage'] for m in recent_metrics) / len(recent_metrics),
            'alert_count': len(self.alerts),
            'last_update': self.metrics_history[-1]['timestamp']
        }


class TestMonitoringSystemE2E:
    """监控系统端到端测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建监控优化器配置
        self.monitoring_config = MonitoringOptimizationConfig(
            optimization_level=OptimizationLevel.BALANCED,
            enable_adaptive_optimization=True,
            performance_monitoring_enabled=True,
            optimization_check_interval_seconds=5
        )
        
        # 创建组件
        self.monitoring_optimizer = MonitoringOptimizer(self.monitoring_config)
        self.data_sources = {}
        self.system_monitor = MockSystemMonitor()
        
        # 创建错误处理器
        error_config = ErrorHandlingConfig(
            report_directory=self.temp_dir,
            batch_processing=False
        )
        self.error_handler = EnhancedErrorHandler(error_config)
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_complete_monitoring_system_startup(self):
        """测试完整监控系统启动"""
        logger.info("测试监控系统完整启动...")
        
        # 启动监控优化器
        await self.monitoring_optimizer.start()
        assert self.monitoring_optimizer.running is True
        
        # 启动系统监控器
        await self.system_monitor.start()
        assert self.system_monitor.running is True
        
        # 创建数据源
        self.data_sources['market_data'] = MockDataSource('market_data', latency=0.05)
        self.data_sources['news_feed'] = MockDataSource('news_feed', latency=0.1)
        self.data_sources['economic_data'] = MockDataSource('economic_data', latency=0.2)
        
        # 注册监控任务
        for name, source in self.data_sources.items():
            self.monitoring_optimizer.register_monitoring_task(
                name, source.fetch_data, custom_interval=10
            )
        
        # 注册系统监控任务
        self.monitoring_optimizer.register_monitoring_task(
            'system_metrics', self.system_monitor.collect_metrics, custom_interval=5
        )
        
        # 验证任务注册
        polling_stats = self.monitoring_optimizer.polling_manager.get_polling_stats()
        assert polling_stats['total_tasks'] == 4  # 3个数据源 + 1个系统监控
        
        # 运行一段时间
        await asyncio.sleep(0.5)
        
        # 验证监控运行状态
        status = self.monitoring_optimizer.get_monitoring_status()
        assert status['running'] is True
        assert status['components']['polling_manager']['active_tasks'] > 0
        
        # 停止监控系统
        await self.monitoring_optimizer.stop()
        await self.system_monitor.stop()
        
        assert self.monitoring_optimizer.running is False
        assert self.system_monitor.running is False
    
    @pytest.mark.asyncio
    async def test_adaptive_monitoring_optimization(self):
        """测试自适应监控优化"""
        logger.info("测试自适应监控优化...")
        
        # 启动监控系统
        await self.monitoring_optimizer.start()
        await self.system_monitor.start()
        
        try:
            # 创建高负载数据源
            high_load_source = MockDataSource('high_load', latency=0.01, error_rate=0.1)
            self.monitoring_optimizer.register_monitoring_task(
                'high_load_task', high_load_source.fetch_data, custom_interval=1
            )
            
            # 模拟高负载系统活动
            high_load_activity = SystemActivity(
                timestamp=datetime.now(),
                cpu_usage=85.0,  # 高CPU使用率
                memory_usage=80.0,  # 高内存使用率
                api_requests_per_minute=100,
                active_connections=30
            )
            
            # 调整轮询频率
            self.monitoring_optimizer.polling_manager.adjust_polling_frequency(high_load_activity)
            
            # 运行优化
            await self.monitoring_optimizer.optimize_monitoring_intervals()
            
            # 验证优化效果
            stats = self.monitoring_optimizer.get_monitoring_status()
            assert stats['statistics']['total_optimizations'] > 0
            
            # 模拟低负载活动
            low_load_activity = SystemActivity(
                timestamp=datetime.now(),
                cpu_usage=15.0,  # 低CPU使用率
                memory_usage=30.0,  # 低内存使用率
                api_requests_per_minute=5,
                active_connections=3
            )
            
            # 再次调整
            self.monitoring_optimizer.polling_manager.adjust_polling_frequency(low_load_activity)
            await self.monitoring_optimizer.optimize_monitoring_intervals()
            
            # 验证自适应调整
            final_stats = self.monitoring_optimizer.get_monitoring_status()
            assert final_stats['statistics']['total_optimizations'] > stats['statistics']['total_optimizations']
        
        finally:
            await self.monitoring_optimizer.stop()
            await self.system_monitor.stop()
    
    @pytest.mark.asyncio
    async def test_caching_system_integration(self):
        """测试缓存系统集成"""
        logger.info("测试缓存系统集成...")
        
        # 启动监控系统
        await self.monitoring_optimizer.start()
        
        try:
            # 获取缓存管理器
            cache_manager = self.monitoring_optimizer.cache_manager
            
            # 测试缓存操作
            test_data = {
                'market_data': {'BTC/USD': 45000, 'ETH/USD': 3200},
                'timestamp': datetime.now().isoformat()
            }
            
            # 设置缓存
            cache_manager.set('market_snapshot', test_data, ttl_seconds=30)
            
            # 获取缓存
            cached_data = cache_manager.get('market_snapshot')
            assert cached_data == test_data
            
            # 测试缓存命中率
            for i in range(10):
                cache_manager.get('market_snapshot')  # 多次获取以提高命中率
            
            # 获取缓存统计
            cache_stats = cache_manager.get_statistics()
            assert cache_stats.cache_hits > 0
            assert cache_stats.hit_rate > 0
            
            # 测试智能缓存优化
            await self.monitoring_optimizer.implement_intelligent_caching()
            
            # 验证缓存优化
            cache_info = cache_manager.get_cache_info()
            assert 'current_state' in cache_info
            assert cache_info['current_state']['size'] > 0
        
        finally:
            await self.monitoring_optimizer.stop()
    
    @pytest.mark.asyncio
    async def test_throttling_system_integration(self):
        """测试节流系统集成"""
        logger.info("测试节流系统集成...")
        
        # 启动监控系统
        await self.monitoring_optimizer.start()
        
        try:
            # 创建高频数据源
            high_freq_source = MockDataSource('high_freq', latency=0.01)
            
            # 注册带节流的监控任务
            throttle_config = ThrottleConfig(
                requests_per_second=5.0,  # 限制每秒5个请求
                burst_size=10
            )
            
            self.monitoring_optimizer.register_monitoring_task(
                'throttled_task', high_freq_source.fetch_data, custom_interval=1
            )
            
            # 获取节流管理器
            throttle_manager = self.monitoring_optimizer.get_throttle_manager('throttled_task')
            assert throttle_manager is not None
            
            # 测试节流功能
            allowed_count = 0
            throttled_count = 0
            
            for i in range(20):
                if throttle_manager.try_acquire():
                    allowed_count += 1
                    throttle_manager.record_success(50.0)  # 50ms响应时间
                else:
                    throttled_count += 1
                
                await asyncio.sleep(0.1)
            
            # 验证节流效果
            assert throttled_count > 0  # 应该有一些请求被节流
            
            # 获取节流统计
            throttle_info = throttle_manager.get_throttle_info()
            assert throttle_info['statistics']['total_requests'] == 20
            assert throttle_info['statistics']['throttled_requests'] == throttled_count
        
        finally:
            await self.monitoring_optimizer.stop()
    
    @pytest.mark.asyncio
    async def test_error_handling_in_monitoring(self):
        """测试监控中的错误处理"""
        logger.info("测试监控系统错误处理...")
        
        # 启动监控系统
        await self.monitoring_optimizer.start()
        
        try:
            # 创建会出错的数据源
            error_source = MockDataSource('error_source', latency=0.05, error_rate=0.5)
            
            # 注册监控任务
            self.monitoring_optimizer.register_monitoring_task(
                'error_task', error_source.fetch_data, custom_interval=2
            )
            
            # 运行一段时间以触发错误
            error_count = 0
            success_count = 0
            
            for i in range(10):
                try:
                    await error_source.fetch_data()
                    success_count += 1
                except Exception as e:
                    error_count += 1
                    # 使用错误处理器处理错误
                    self.error_handler.handle_error(e, {
                        'component': 'monitoring',
                        'task': 'error_task',
                        'iteration': i
                    })
            
            # 验证错误处理
            assert error_count > 0  # 应该有错误发生
            
            # 获取错误统计
            error_stats = self.error_handler.get_error_statistics()
            assert error_stats['total_errors'] > 0
            
            # 验证错误分类
            assert 'by_category' in error_stats
            assert 'by_severity' in error_stats
        
        finally:
            await self.monitoring_optimizer.stop()
    
    @pytest.mark.asyncio
    async def test_performance_monitoring_integration(self):
        """测试性能监控集成"""
        logger.info("测试性能监控集成...")
        
        # 启动监控系统
        await self.monitoring_optimizer.start()
        await self.system_monitor.start()
        
        try:
            # 注册性能监控任务
            self.monitoring_optimizer.register_monitoring_task(
                'performance_monitor', self.system_monitor.collect_metrics, custom_interval=1
            )
            
            # 运行监控收集性能数据
            for i in range(5):
                metrics = await self.system_monitor.collect_metrics()
                
                # 检查阈值
                alerts = self.system_monitor.check_thresholds(metrics)
                
                # 如果有告警，记录到错误处理器
                for alert in alerts:
                    if alert['severity'] == 'critical':
                        error = Exception(f"Performance alert: {alert['message']}")
                        self.error_handler.handle_error(error, {
                            'component': 'performance_monitor',
                            'alert_type': alert['type'],
                            'metrics': metrics
                        })
                
                await asyncio.sleep(0.2)
            
            # 获取性能摘要
            summary = self.system_monitor.get_metrics_summary()
            assert summary is not None
            assert summary['total_metrics'] >= 5
            
            # 验证监控优化器的性能历史
            optimizer_status = self.monitoring_optimizer.get_monitoring_status()
            assert optimizer_status['performance_history_size'] >= 0
        
        finally:
            await self.monitoring_optimizer.stop()
            await self.system_monitor.stop()
    
    @pytest.mark.asyncio
    async def test_monitoring_system_resilience(self):
        """测试监控系统弹性"""
        logger.info("测试监控系统弹性...")
        
        # 启动监控系统
        await self.monitoring_optimizer.start()
        
        try:
            # 创建多个不同特性的数据源
            sources = {
                'stable_source': MockDataSource('stable', latency=0.05, error_rate=0.0),
                'slow_source': MockDataSource('slow', latency=0.5, error_rate=0.0),
                'unreliable_source': MockDataSource('unreliable', latency=0.1, error_rate=0.3),
                'fast_source': MockDataSource('fast', latency=0.01, error_rate=0.1)
            }
            
            # 注册所有数据源
            for name, source in sources.items():
                self.monitoring_optimizer.register_monitoring_task(
                    name, source.fetch_data, custom_interval=3
                )
            
            # 运行监控系统
            start_time = time.time()
            
            # 模拟系统运行
            while time.time() - start_time < 2.0:  # 运行2秒
                # 获取监控状态
                status = self.monitoring_optimizer.get_monitoring_status()
                
                # 验证系统仍在运行
                assert status['running'] is True
                
                # 检查组件健康状态
                components = status['components']
                assert 'polling_manager' in components
                assert 'cache_manager' in components
                
                await asyncio.sleep(0.1)
            
            # 验证系统弹性
            final_status = self.monitoring_optimizer.get_monitoring_status()
            assert final_status['running'] is True
            
            # 获取各数据源的统计
            source_stats = {}
            for name, source in sources.items():
                source_stats[name] = source.get_stats()
            
            # 验证不同数据源都有请求
            for name, stats in source_stats.items():
                logger.info(f"{name}: {stats['request_count']} requests")
                assert stats['request_count'] > 0
        
        finally:
            await self.monitoring_optimizer.stop()
    
    @pytest.mark.asyncio
    async def test_monitoring_configuration_changes(self):
        """测试监控配置变更"""
        logger.info("测试监控配置动态变更...")
        
        # 启动监控系统
        await self.monitoring_optimizer.start()
        
        try:
            # 初始配置
            initial_config = self.monitoring_optimizer.config
            assert initial_config.optimization_level == OptimizationLevel.BALANCED
            
            # 创建测试数据源
            test_source = MockDataSource('config_test', latency=0.1)
            self.monitoring_optimizer.register_monitoring_task(
                'config_test_task', test_source.fetch_data, custom_interval=5
            )
            
            # 运行一段时间
            await asyncio.sleep(0.3)
            
            # 获取初始统计
            initial_stats = self.monitoring_optimizer.get_monitoring_status()
            
            # 模拟配置变更 - 切换到激进优化
            await self.monitoring_optimizer._apply_aggressive_optimization()
            
            # 运行一段时间以应用新配置
            await asyncio.sleep(0.3)
            
            # 获取变更后统计
            updated_stats = self.monitoring_optimizer.get_monitoring_status()
            
            # 验证配置变更效果
            # 激进优化应该有更短的轮询间隔
            polling_config = self.monitoring_optimizer.polling_manager.config
            assert polling_config.active_interval <= 5  # 应该是较短的间隔
            
            # 缓存配置应该更大
            cache_config = self.monitoring_optimizer.cache_manager.config
            assert cache_config.max_size >= 1000  # 应该有更大的缓存
            
            # 模拟配置变更 - 切换到保守优化
            await self.monitoring_optimizer._apply_conservative_optimization()
            
            await asyncio.sleep(0.3)
            
            # 验证保守优化配置
            conservative_polling_config = self.monitoring_optimizer.polling_manager.config
            assert conservative_polling_config.active_interval >= 5  # 应该是较长的间隔
            
            conservative_cache_config = self.monitoring_optimizer.cache_manager.config
            assert conservative_cache_config.max_size <= 1000  # 应该有较小的缓存
        
        finally:
            await self.monitoring_optimizer.stop()
    
    @pytest.mark.asyncio
    async def test_monitoring_system_metrics_collection(self):
        """测试监控系统指标收集"""
        logger.info("测试监控系统指标收集...")
        
        # 启动监控系统
        await self.monitoring_optimizer.start()
        
        try:
            # 创建多个数据源进行指标收集
            data_sources = []
            for i in range(3):
                source = MockDataSource(f'metrics_source_{i}', latency=0.05 + i*0.02)
                data_sources.append(source)
                
                self.monitoring_optimizer.register_monitoring_task(
                    f'metrics_task_{i}', source.fetch_data, custom_interval=2
                )
            
            # 运行监控收集指标
            metrics_collection = []
            
            for iteration in range(5):
                # 收集当前监控状态
                status = self.monitoring_optimizer.get_monitoring_status()
                
                # 收集缓存指标
                cache_info = self.monitoring_optimizer.cache_manager.get_cache_info()
                
                # 收集节流指标
                throttle_stats = {}
                for i in range(3):
                    throttle_manager = self.monitoring_optimizer.get_throttle_manager(f'metrics_task_{i}')
                    if throttle_manager:
                        throttle_stats[f'task_{i}'] = throttle_manager.get_statistics()
                
                # 组合指标
                combined_metrics = {
                    'timestamp': datetime.now().isoformat(),
                    'iteration': iteration,
                    'monitoring_status': status,
                    'cache_info': cache_info,
                    'throttle_stats': throttle_stats,
                    'data_source_stats': [source.get_stats() for source in data_sources]
                }
                
                metrics_collection.append(combined_metrics)
                
                await asyncio.sleep(0.4)
            
            # 验证指标收集
            assert len(metrics_collection) == 5
            
            # 验证指标完整性
            for metrics in metrics_collection:
                assert 'timestamp' in metrics
                assert 'monitoring_status' in metrics
                assert 'cache_info' in metrics
                assert 'data_source_stats' in metrics
                
                # 验证监控状态
                assert metrics['monitoring_status']['running'] is True
                
                # 验证缓存信息
                assert 'current_state' in metrics['cache_info']
                
                # 验证数据源统计
                assert len(metrics['data_source_stats']) == 3
            
            # 分析指标趋势
            request_counts = []
            for metrics in metrics_collection:
                total_requests = sum(
                    stats['request_count'] for stats in metrics['data_source_stats']
                )
                request_counts.append(total_requests)
            
            # 请求数应该随时间增长
            assert request_counts[-1] > request_counts[0]
            
            logger.info(f"指标收集完成: {len(metrics_collection)} 个时间点")
            logger.info(f"请求数趋势: {request_counts[0]} -> {request_counts[-1]}")
        
        finally:
            await self.monitoring_optimizer.stop()


if __name__ == '__main__':
    pytest.main([__file__, '-v'])