import logging
logger = logging.getLogger(__name__)
"""
前后端集成测试

验证前后端集成和错误处理
"""

import pytest
import asyncio
import json
import tempfile
import os
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock
import aiohttp
from aiohttp import web
import threading
import time

from src.api.app import create_app
from src.error_handling.enhanced_error_handler import EnhancedErrorHandler, ErrorHandlingConfig


class MockFrontendServer:
    """模拟前端服务器"""
    
    def __init__(self, port=3000):
        self.port = port
        self.app = None
        self.runner = None
        self.site = None
        self.running = False
    
    async def start(self):
        """启动前端服务器"""
        self.app = web.Application()
        
        # 添加前端路由
        self.app.router.add_get('/', self.index_handler)
        self.app.router.add_get('/api/health', self.health_handler)
        self.app.router.add_post('/api/data', self.data_handler)
        self.app.router.add_get('/api/status', self.status_handler)
        
        # 添加错误处理中间件
        self.app.middlewares.append(self.error_middleware)
        
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()
        
        self.site = web.TCPSite(self.runner, 'localhost', self.port)
        await self.site.start()
        
        self.running = True
        logger.info(f"Mock frontend server started on port {self.port}")
    
    async def stop(self):
        """停止前端服务器"""
        if self.site:
            await self.site.stop()
        if self.runner:
            await self.runner.cleanup()
        self.running = False
        logger.info("Mock frontend server stopped")
    
    async def index_handler(self, request):
        """首页处理器"""
        return web.Response(
            text="""
            <!DOCTYPE html>
            <html>
            <head><title>Trading System</title></head>
            <body>
                <h1>Trading System Frontend</h1>
                <div id="app">Loading...</div>
                <script>
                    // Mock frontend JavaScript
                    document.getElementById('app').innerHTML = 'Frontend Loaded';
                </script>
            </body>
            </html>
            """,
            content_type='text/html'
        )
    
    async def health_handler(self, request):
        """健康检查处理器"""
        return web.json_response({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'frontend_version': '1.0.0'
        })
    
    async def data_handler(self, request):
        """数据处理器"""
        try:
            data = await request.json()
            
            # 模拟数据处理
            processed_data = {
                'received': data,
                'processed_at': datetime.now().isoformat(),
                'status': 'success'
            }
            
            return web.json_response(processed_data)
            
        except Exception as e:
            return web.json_response({
                'error': str(e),
                'status': 'error'
            }, status=400)
    
    async def status_handler(self, request):
        """状态处理器"""
        return web.json_response({
            'frontend_status': 'running',
            'uptime': time.time(),
            'requests_handled': getattr(self, 'request_count', 0)
        })
    
    @web.middleware
    async def error_middleware(self, request, handler):
        """错误处理中间件"""
        try:
            # 增加请求计数
            self.request_count = getattr(self, 'request_count', 0) + 1
            
            response = await handler(request)
            return response
            
        except Exception as e:
            # 记录错误
            error_response = {
                'error': str(e),
                'path': request.path,
                'method': request.method,
                'timestamp': datetime.now().isoformat()
            }
            
            return web.json_response(error_response, status=500)


class MockBackendServer:
    """模拟后端服务器"""
    
    def __init__(self, port=8000):
        self.port = port
        self.app = None
        self.runner = None
        self.site = None
        self.running = False
        self.error_handler = None
    
    async def start(self):
        """启动后端服务器"""
        # 创建错误处理器
        temp_dir = tempfile.mkdtemp()
        config = ErrorHandlingConfig(
            report_directory=temp_dir,
            batch_processing=False
        )
        self.error_handler = EnhancedErrorHandler(config)
        
        self.app = web.Application()
        
        # 添加后端API路由
        self.app.router.add_get('/api/health', self.health_handler)
        self.app.router.add_get('/api/market-data', self.market_data_handler)
        self.app.router.add_post('/api/orders', self.orders_handler)
        self.app.router.add_get('/api/portfolio', self.portfolio_handler)
        self.app.router.add_post('/api/strategies', self.strategies_handler)
        
        # 添加CORS中间件
        self.app.middlewares.append(self.cors_middleware)
        self.app.middlewares.append(self.error_middleware)
        
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()
        
        self.site = web.TCPSite(self.runner, 'localhost', self.port)
        await self.site.start()
        
        self.running = True
        logger.info(f"Mock backend server started on port {self.port}")
    
    async def stop(self):
        """停止后端服务器"""
        if self.site:
            await self.site.stop()
        if self.runner:
            await self.runner.cleanup()
        self.running = False
        logger.info("Mock backend server stopped")
    
    async def health_handler(self, request):
        """健康检查处理器"""
        return web.json_response({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'backend_version': '1.0.0',
            'database_status': 'connected',
            'services': {
                'trading_engine': 'running',
                'data_feed': 'connected',
                'risk_manager': 'active'
            }
        })
    
    async def market_data_handler(self, request):
        """市场数据处理器"""
        # 模拟市场数据
        market_data = {
            'timestamp': datetime.now().isoformat(),
            'symbols': [
                {
                    'symbol': 'BTC/USD',
                    'price': 45000.50,
                    'change': 2.5,
                    'volume': 1234567
                },
                {
                    'symbol': 'ETH/USD',
                    'price': 3200.75,
                    'change': -1.2,
                    'volume': 987654
                }
            ]
        }
        
        return web.json_response(market_data)
    
    async def orders_handler(self, request):
        """订单处理器"""
        try:
            order_data = await request.json()
            
            # 验证订单数据
            required_fields = ['symbol', 'side', 'quantity', 'price']
            for field in required_fields:
                if field not in order_data:
                    raise ValueError(f"Missing required field: {field}")
            
            # 模拟订单处理
            order_response = {
                'order_id': f"ORD_{int(time.time())}",
                'status': 'filled',
                'symbol': order_data['symbol'],
                'side': order_data['side'],
                'quantity': order_data['quantity'],
                'price': order_data['price'],
                'timestamp': datetime.now().isoformat()
            }
            
            return web.json_response(order_response)
            
        except Exception as e:
            # 使用错误处理器处理错误
            if self.error_handler:
                error_report = self.error_handler.handle_error(e, {
                    'component': 'orders_api',
                    'request_data': await request.text()
                })
            
            return web.json_response({
                'error': str(e),
                'status': 'error'
            }, status=400)
    
    async def portfolio_handler(self, request):
        """投资组合处理器"""
        portfolio_data = {
            'total_value': 125000.75,
            'cash_balance': 25000.00,
            'positions': [
                {
                    'symbol': 'BTC/USD',
                    'quantity': 2.5,
                    'avg_price': 42000.00,
                    'current_price': 45000.50,
                    'pnl': 7501.25
                },
                {
                    'symbol': 'ETH/USD',
                    'quantity': 10.0,
                    'avg_price': 3100.00,
                    'current_price': 3200.75,
                    'pnl': 1007.50
                }
            ],
            'timestamp': datetime.now().isoformat()
        }
        
        return web.json_response(portfolio_data)
    
    async def strategies_handler(self, request):
        """策略处理器"""
        try:
            strategy_data = await request.json()
            
            # 模拟策略创建/更新
            strategy_response = {
                'strategy_id': f"STRAT_{int(time.time())}",
                'name': strategy_data.get('name', 'Unnamed Strategy'),
                'type': strategy_data.get('type', 'custom'),
                'status': 'active',
                'parameters': strategy_data.get('parameters', {}),
                'created_at': datetime.now().isoformat()
            }
            
            return web.json_response(strategy_response)
            
        except Exception as e:
            return web.json_response({
                'error': str(e),
                'status': 'error'
            }, status=400)
    
    @web.middleware
    async def cors_middleware(self, request, handler):
        """CORS中间件"""
        response = await handler(request)
        
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        
        return response
    
    @web.middleware
    async def error_middleware(self, request, handler):
        """错误处理中间件"""
        try:
            response = await handler(request)
            return response
            
        except Exception as e:
            # 使用错误处理器处理错误
            if self.error_handler:
                error_report = self.error_handler.handle_error(e, {
                    'component': 'backend_api',
                    'path': request.path,
                    'method': request.method
                })
            
            error_response = {
                'error': str(e),
                'path': request.path,
                'method': request.method,
                'timestamp': datetime.now().isoformat()
            }
            
            return web.json_response(error_response, status=500)


class TestFrontendBackendIntegration:
    """前后端集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.frontend_server = MockFrontendServer(port=3001)
        self.backend_server = MockBackendServer(port=8001)
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_frontend_backend_startup(self):
        """测试前后端启动"""
        logger.info("测试前后端服务器启动...")
        
        # 启动后端服务器
        await self.backend_server.start()
        assert self.backend_server.running is True
        
        # 启动前端服务器
        await self.frontend_server.start()
        assert self.frontend_server.running is True
        
        # 等待服务器完全启动
        await asyncio.sleep(0.1)
        
        # 测试后端健康检查
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8001/api/health') as resp:
                assert resp.status == 200
                data = await resp.json()
                assert data['status'] == 'healthy'
        
        # 测试前端健康检查
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:3001/api/health') as resp:
                assert resp.status == 200
                data = await resp.json()
                assert data['status'] == 'healthy'
        
        # 停止服务器
        await self.frontend_server.stop()
        await self.backend_server.stop()
    
    @pytest.mark.asyncio
    async def test_api_communication(self):
        """测试API通信"""
        logger.info("测试前后端API通信...")
        
        # 启动服务器
        await self.backend_server.start()
        await self.frontend_server.start()
        await asyncio.sleep(0.1)
        
        try:
            # 测试市场数据API
            async with aiohttp.ClientSession() as session:
                async with session.get('http://localhost:8001/api/market-data') as resp:
                    assert resp.status == 200
                    data = await resp.json()
                    assert 'symbols' in data
                    assert len(data['symbols']) > 0
            
            # 测试订单API
            order_data = {
                'symbol': 'BTC/USD',
                'side': 'buy',
                'quantity': 1.0,
                'price': 45000.0
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'http://localhost:8001/api/orders',
                    json=order_data
                ) as resp:
                    assert resp.status == 200
                    data = await resp.json()
                    assert 'order_id' in data
                    assert data['status'] == 'filled'
            
            # 测试投资组合API
            async with aiohttp.ClientSession() as session:
                async with session.get('http://localhost:8001/api/portfolio') as resp:
                    assert resp.status == 200
                    data = await resp.json()
                    assert 'total_value' in data
                    assert 'positions' in data
        
        finally:
            await self.frontend_server.stop()
            await self.backend_server.stop()
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self):
        """测试错误处理集成"""
        logger.info("测试前后端错误处理集成...")
        
        # 启动服务器
        await self.backend_server.start()
        await self.frontend_server.start()
        await asyncio.sleep(0.1)
        
        try:
            # 测试无效订单数据（缺少必需字段）
            invalid_order = {
                'symbol': 'BTC/USD',
                # 缺少 side, quantity, price
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'http://localhost:8001/api/orders',
                    json=invalid_order
                ) as resp:
                    assert resp.status == 400
                    data = await resp.json()
                    assert 'error' in data
                    assert 'Missing required field' in data['error']
            
            # 测试前端数据处理错误
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'http://localhost:3001/api/data',
                    data='invalid json'  # 发送无效JSON
                ) as resp:
                    assert resp.status == 400
                    data = await resp.json()
                    assert 'error' in data
            
            # 验证错误被记录到错误处理器
            if self.backend_server.error_handler:
                error_stats = self.backend_server.error_handler.get_error_statistics()
                assert error_stats['total_errors'] > 0
        
        finally:
            await self.frontend_server.stop()
            await self.backend_server.stop()
    
    @pytest.mark.asyncio
    async def test_cors_handling(self):
        """测试CORS处理"""
        logger.info("测试CORS处理...")
        
        # 启动后端服务器
        await self.backend_server.start()
        await asyncio.sleep(0.1)
        
        try:
            # 测试CORS预检请求
            async with aiohttp.ClientSession() as session:
                async with session.options(
                    'http://localhost:8001/api/health',
                    headers={
                        'Origin': 'http://localhost:3000',
                        'Access-Control-Request-Method': 'GET'
                    }
                ) as resp:
                    # 验证CORS头
                    assert 'Access-Control-Allow-Origin' in resp.headers
                    assert resp.headers['Access-Control-Allow-Origin'] == '*'
            
            # 测试实际请求的CORS头
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    'http://localhost:8001/api/health',
                    headers={'Origin': 'http://localhost:3000'}
                ) as resp:
                    assert resp.status == 200
                    assert 'Access-Control-Allow-Origin' in resp.headers
        
        finally:
            await self.backend_server.stop()
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """测试并发请求处理"""
        logger.info("测试并发请求处理...")
        
        # 启动服务器
        await self.backend_server.start()
        await asyncio.sleep(0.1)
        
        try:
            # 创建多个并发请求
            async def make_request(session, endpoint):
                async with session.get(f'http://localhost:8001{endpoint}') as resp:
                    return await resp.json()
            
            async with aiohttp.ClientSession() as session:
                # 并发发送多个请求
                tasks = [
                    make_request(session, '/api/health'),
                    make_request(session, '/api/market-data'),
                    make_request(session, '/api/portfolio'),
                    make_request(session, '/api/health'),
                    make_request(session, '/api/market-data')
                ]
                
                start_time = time.time()
                results = await asyncio.gather(*tasks)
                end_time = time.time()
                
                # 验证所有请求都成功
                assert len(results) == 5
                for result in results:
                    assert result is not None
                
                # 并发请求应该比串行请求快
                logger.info(f"并发请求完成时间: {end_time - start_time:.3f}秒")
                assert (end_time - start_time) < 1.0
        
        finally:
            await self.backend_server.stop()
    
    @pytest.mark.asyncio
    async def test_data_flow_integration(self):
        """测试数据流集成"""
        logger.info("测试前后端数据流集成...")
        
        # 启动服务器
        await self.backend_server.start()
        await self.frontend_server.start()
        await asyncio.sleep(0.1)
        
        try:
            # 模拟完整的数据流：前端 -> 后端 -> 前端
            
            # 1. 前端获取市场数据
            async with aiohttp.ClientSession() as session:
                async with session.get('http://localhost:8001/api/market-data') as resp:
                    market_data = await resp.json()
                    assert 'symbols' in market_data
            
            # 2. 前端发送订单到后端
            order_data = {
                'symbol': market_data['symbols'][0]['symbol'],
                'side': 'buy',
                'quantity': 1.0,
                'price': market_data['symbols'][0]['price']
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'http://localhost:8001/api/orders',
                    json=order_data
                ) as resp:
                    order_result = await resp.json()
                    assert order_result['status'] == 'filled'
            
            # 3. 前端获取更新后的投资组合
            async with aiohttp.ClientSession() as session:
                async with session.get('http://localhost:8001/api/portfolio') as resp:
                    portfolio_data = await resp.json()
                    assert 'positions' in portfolio_data
            
            # 4. 前端处理接收到的数据
            processed_data = {
                'market_data': market_data,
                'order_result': order_result,
                'portfolio': portfolio_data
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'http://localhost:3001/api/data',
                    json=processed_data
                ) as resp:
                    frontend_result = await resp.json()
                    assert frontend_result['status'] == 'success'
                    assert 'received' in frontend_result
        
        finally:
            await self.frontend_server.stop()
            await self.backend_server.stop()
    
    @pytest.mark.asyncio
    async def test_websocket_communication(self):
        """测试WebSocket通信（模拟）"""
        logger.info("测试WebSocket通信...")
        
        # 由于这是模拟测试，我们创建一个简单的WebSocket模拟
        websocket_messages = []
        
        def mock_websocket_handler(message):
            """模拟WebSocket消息处理"""
            websocket_messages.append({
                'timestamp': datetime.now().isoformat(),
                'message': message,
                'type': 'websocket'
            })
        
        # 模拟实时数据推送
        real_time_data = [
            {'type': 'price_update', 'symbol': 'BTC/USD', 'price': 45100.0},
            {'type': 'order_update', 'order_id': 'ORD_123', 'status': 'filled'},
            {'type': 'portfolio_update', 'total_value': 125500.0}
        ]
        
        # 处理模拟WebSocket消息
        for data in real_time_data:
            mock_websocket_handler(data)
        
        # 验证WebSocket消息处理
        assert len(websocket_messages) == 3
        assert websocket_messages[0]['message']['type'] == 'price_update'
        assert websocket_messages[1]['message']['type'] == 'order_update'
        assert websocket_messages[2]['message']['type'] == 'portfolio_update'
    
    @pytest.mark.asyncio
    async def test_authentication_integration(self):
        """测试认证集成（模拟）"""
        logger.info("测试认证集成...")
        
        # 模拟认证令牌
        mock_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.test_token"
        
        # 启动后端服务器
        await self.backend_server.start()
        await asyncio.sleep(0.1)
        
        try:
            # 测试带认证头的请求
            headers = {
                'Authorization': f'Bearer {mock_token}',
                'Content-Type': 'application/json'
            }
            
            async with aiohttp.ClientSession() as session:
                # 测试认证的API调用
                async with session.get(
                    'http://localhost:8001/api/portfolio',
                    headers=headers
                ) as resp:
                    assert resp.status == 200
                    data = await resp.json()
                    assert 'total_value' in data
                
                # 测试策略API（需要认证）
                strategy_data = {
                    'name': 'Test Strategy',
                    'type': 'momentum',
                    'parameters': {'period': 14, 'threshold': 0.02}
                }
                
                async with session.post(
                    'http://localhost:8001/api/strategies',
                    json=strategy_data,
                    headers=headers
                ) as resp:
                    assert resp.status == 200
                    data = await resp.json()
                    assert 'strategy_id' in data
                    assert data['status'] == 'active'
        
        finally:
            await self.backend_server.stop()


if __name__ == '__main__':
    pytest.main([__file__, '-v'])