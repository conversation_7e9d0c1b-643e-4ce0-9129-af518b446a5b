import logging
logger = logging.getLogger(__name__)
"""
启动和依赖管理集成测试

测试依赖解析、健康检查和启动管理的集成
"""

import pytest
import asyncio
import tempfile
import os
import json
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock

from src.core.startup_manager import StartupManager
from src.core.dependency_resolver import DependencyResolver
from src.core.enhanced_health_checker import HealthChecker
from src.core.shutdown_handler import ShutdownHandler
from src.error_handling.enhanced_error_handler import EnhancedErrorHandler, ErrorHandlingConfig
from src.monitoring.monitoring_optimizer import MonitoringOptimizer, MonitoringOptimizationConfig, OptimizationLevel


class MockSystemComponent:
    """模拟系统组件"""
    
    def __init__(self, name, startup_time=0.1, failure_rate=0.0, dependencies=None):
        self.name = name
        self.startup_time = startup_time
        self.failure_rate = failure_rate
        self.dependencies = dependencies or []
        self.status = 'stopped'
        self.startup_count = 0
        self.shutdown_count = 0
        self.health_status = 'unknown'
    
    async def start(self):
        """启动组件"""
        self.startup_count += 1
        
        # 模拟启动时间
        await asyncio.sleep(self.startup_time)
        
        # 模拟启动失败
        import random
        if random.random() < self.failure_rate:
            self.status = 'failed'
            raise Exception(f"Component {self.name} failed to start")
        
        self.status = 'running'
        self.health_status = 'healthy'
        return True
    
    async def stop(self):
        """停止组件"""
        self.shutdown_count += 1
        
        # 模拟停止时间
        await asyncio.sleep(0.05)
        
        self.status = 'stopped'
        self.health_status = 'unknown'
        return True
    
    async def health_check(self):
        """健康检查"""
        if self.status == 'running':
            return {
                'status': self.health_status,
                'component': self.name,
                'uptime': time.time(),
                'startup_count': self.startup_count
            }
        else:
            return {
                'status': 'unhealthy',
                'component': self.name,
                'reason': f'Component is {self.status}'
            }
    
    def get_info(self):
        """获取组件信息"""
        return {
            'name': self.name,
            'status': self.status,
            'health_status': self.health_status,
            'startup_count': self.startup_count,
            'shutdown_count': self.shutdown_count,
            'dependencies': self.dependencies
        }


class TestStartupDependencyIntegration:
    """启动依赖管理集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建错误处理器
        error_config = ErrorHandlingConfig(
            report_directory=self.temp_dir,
            batch_processing=False
        )
        self.error_handler = EnhancedErrorHandler(error_config)
        
        # 创建系统组件
        self.components = {}
        self.startup_manager = None
        self.dependency_resolver = None
        self.health_checker = None
        self.shutdown_handler = None
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_components(self):
        """创建测试组件"""
        # 创建具有依赖关系的组件
        self.components = {
            'database': MockSystemComponent('database', startup_time=0.2),
            'cache': MockSystemComponent('cache', startup_time=0.1, dependencies=['database']),
            'api_server': MockSystemComponent('api_server', startup_time=0.15, dependencies=['database', 'cache']),
            'monitoring': MockSystemComponent('monitoring', startup_time=0.1, dependencies=['database']),
            'web_ui': MockSystemComponent('web_ui', startup_time=0.1, dependencies=['api_server']),
            'data_processor': MockSystemComponent('data_processor', startup_time=0.2, dependencies=['database', 'cache'])
        }
    
    @pytest.mark.asyncio
    async def test_dependency_resolution_integration(self):
        """测试依赖解析集成"""
        logger.info("测试依赖解析集成...")
        
        # 创建测试组件
        self.create_test_components()
        
        # 创建依赖解析器
        self.dependency_resolver = DependencyResolver()
        
        # 添加组件依赖关系
        for name, component in self.components.items():
            self.dependency_resolver.add_dependency(name, component.dependencies)
        
        # 解析启动顺序
        startup_order = self.dependency_resolver.resolve_startup_order()
        
        # 验证启动顺序
        assert len(startup_order) == len(self.components)
        
        # 验证依赖顺序正确
        def get_index(component_name):
            return startup_order.index(component_name)
        
        # database应该最先启动（没有依赖）
        assert get_index('database') == 0
        
        # cache依赖database，应该在database之后
        assert get_index('cache') > get_index('database')
        
        # api_server依赖database和cache，应该在它们之后
        assert get_index('api_server') > get_index('database')
        assert get_index('api_server') > get_index('cache')
        
        # web_ui依赖api_server，应该最后启动
        assert get_index('web_ui') > get_index('api_server')
        
        logger.info(f"启动顺序: {startup_order}")
        
        # 验证循环依赖检测
        # 添加循环依赖
        self.dependency_resolver.add_dependency('database', ['web_ui'])  # 创建循环
        
        with pytest.raises(Exception) as exc_info:
            self.dependency_resolver.resolve_startup_order()
        
        assert 'circular dependency' in str(exc_info.value).lower()
    
    @pytest.mark.asyncio
    async def test_startup_manager_integration(self):
        """测试启动管理器集成"""
        logger.info("测试启动管理器集成...")
        
        # 创建测试组件
        self.create_test_components()
        
        # 创建启动管理器
        self.startup_manager = StartupManager()
        
        # 注册组件
        for name, component in self.components.items():
            self.startup_manager.register_component(name, component, component.dependencies)
        
        # 执行启动
        start_time = time.time()
        startup_result = await self.startup_manager.startup_with_dependencies()
        end_time = time.time()
        
        # 验证启动结果
        assert startup_result is True
        
        # 验证所有组件都已启动
        for component in self.components.values():
            assert component.status == 'running'
            assert component.startup_count == 1
        
        # 验证启动时间合理（并行启动应该比串行快）
        total_startup_time = end_time - start_time
        max_sequential_time = sum(c.startup_time for c in self.components.values())
        assert total_startup_time < max_sequential_time
        
        logger.info(f"启动时间: {total_startup_time:.3f}秒 (最大串行时间: {max_sequential_time:.3f}秒)")
        
        # 获取启动报告
        startup_report = self.startup_manager.get_startup_report()
        assert startup_report is not None
        assert startup_report['success'] is True
        assert len(startup_report['components']) == len(self.components)
    
    @pytest.mark.asyncio
    async def test_startup_failure_handling(self):
        """测试启动失败处理"""
        logger.info("测试启动失败处理...")
        
        # 创建测试组件，其中一个会失败
        self.components = {
            'database': MockSystemComponent('database', startup_time=0.1),
            'failing_component': MockSystemComponent('failing_component', startup_time=0.1, failure_rate=1.0),
            'dependent_component': MockSystemComponent('dependent_component', startup_time=0.1, dependencies=['failing_component'])
        }
        
        # 创建启动管理器
        self.startup_manager = StartupManager()
        
        # 注册组件
        for name, component in self.components.items():
            self.startup_manager.register_component(name, component, component.dependencies)
        
        # 执行启动（应该失败）
        startup_result = await self.startup_manager.startup_with_dependencies()
        
        # 验证启动失败
        assert startup_result is False
        
        # 验证失败组件状态
        assert self.components['failing_component'].status == 'failed'
        
        # 验证依赖组件没有启动
        assert self.components['dependent_component'].status == 'stopped'
        
        # 验证独立组件仍然启动成功
        assert self.components['database'].status == 'running'
        
        # 获取启动报告
        startup_report = self.startup_manager.get_startup_report()
        assert startup_report['success'] is False
        assert len(startup_report['failed_components']) > 0
        assert 'failing_component' in startup_report['failed_components']
    
    @pytest.mark.asyncio
    async def test_health_check_integration(self):
        """测试健康检查集成"""
        logger.info("测试健康检查集成...")
        
        # 创建测试组件
        self.create_test_components()
        
        # 创建健康检查器
        self.health_checker = HealthChecker()
        
        # 启动部分组件
        await self.components['database'].start()
        await self.components['cache'].start()
        
        # 模拟一个组件不健康
        self.components['cache'].health_status = 'degraded'
        
        # 注册健康检查
        for name, component in self.components.items():
            self.health_checker.register_component(name, component.health_check)
        
        # 执行健康检查
        health_report = await self.health_checker.perform_comprehensive_health_check()
        
        # 验证健康检查结果
        assert health_report is not None
        assert 'overall_status' in health_report
        assert 'component_status' in health_report
        
        # 验证组件状态
        component_status = health_report['component_status']
        assert component_status['database']['status'] == 'healthy'
        assert component_status['cache']['status'] == 'degraded'
        assert component_status['api_server']['status'] == 'unhealthy'  # 未启动
        
        # 验证整体状态
        assert health_report['overall_status'] in ['degraded', 'unhealthy']
        
        # 测试健康检查错误处理
        def failing_health_check():
            raise Exception("Health check failed")
        
        self.health_checker.register_component('failing_health', failing_health_check)
        
        # 执行健康检查（应该处理错误）
        health_report_with_error = await self.health_checker.perform_comprehensive_health_check()
        
        # 验证错误被正确处理
        assert 'failing_health' in health_report_with_error['component_status']
        assert health_report_with_error['component_status']['failing_health']['status'] == 'error'
    
    @pytest.mark.asyncio
    async def test_shutdown_integration(self):
        """测试关闭集成"""
        logger.info("测试关闭集成...")
        
        # 创建测试组件
        self.create_test_components()
        
        # 启动所有组件
        for component in self.components.values():
            await component.start()
        
        # 创建关闭处理器
        self.shutdown_handler = ShutdownHandler()
        
        # 注册组件
        for name, component in self.components.items():
            self.shutdown_handler.register_component(name, component)
        
        # 执行优雅关闭
        shutdown_result = await self.shutdown_handler.graceful_shutdown()
        
        # 验证关闭结果
        assert shutdown_result is True
        
        # 验证所有组件都已停止
        for component in self.components.values():
            assert component.status == 'stopped'
            assert component.shutdown_count == 1
        
        # 获取关闭报告
        shutdown_report = self.shutdown_handler.get_shutdown_report()
        assert shutdown_report is not None
        assert shutdown_report['success'] is True
        assert len(shutdown_report['components']) == len(self.components)
    
    @pytest.mark.asyncio
    async def test_startup_monitoring_integration(self):
        """测试启动监控集成"""
        logger.info("测试启动监控集成...")
        
        # 创建测试组件
        self.create_test_components()
        
        # 创建监控优化器
        monitoring_config = MonitoringOptimizationConfig(
            optimization_level=OptimizationLevel.BALANCED,
            enable_adaptive_optimization=True
        )
        monitoring_optimizer = MonitoringOptimizer(monitoring_config)
        
        # 创建启动管理器
        self.startup_manager = StartupManager()
        
        # 注册组件
        for name, component in self.components.items():
            self.startup_manager.register_component(name, component, component.dependencies)
        
        # 启动监控
        await monitoring_optimizer.start()
        
        try:
            # 注册启动监控任务
            startup_metrics = []
            
            async def collect_startup_metrics():
                metrics = {
                    'timestamp': datetime.now().isoformat(),
                    'components_status': {
                        name: component.get_info() 
                        for name, component in self.components.items()
                    }
                }
                startup_metrics.append(metrics)
                return metrics
            
            monitoring_optimizer.register_monitoring_task(
                'startup_monitoring', collect_startup_metrics, custom_interval=1
            )
            
            # 执行启动
            startup_result = await self.startup_manager.startup_with_dependencies()
            
            # 等待监控收集数据
            await asyncio.sleep(0.5)
            
            # 验证启动成功
            assert startup_result is True
            
            # 验证监控数据收集
            assert len(startup_metrics) > 0
            
            # 分析启动过程
            final_metrics = startup_metrics[-1]
            for name, info in final_metrics['components_status'].items():
                assert info['status'] == 'running'
                assert info['startup_count'] == 1
        
        finally:
            await monitoring_optimizer.stop()
    
    @pytest.mark.asyncio
    async def test_error_handling_during_startup(self):
        """测试启动过程中的错误处理"""
        logger.info("测试启动错误处理...")
        
        # 创建会出错的组件
        self.components = {
            'normal_component': MockSystemComponent('normal_component', startup_time=0.1),
            'slow_component': MockSystemComponent('slow_component', startup_time=0.5),
            'error_component': MockSystemComponent('error_component', startup_time=0.1, failure_rate=1.0),
            'dependent_component': MockSystemComponent('dependent_component', startup_time=0.1, dependencies=['error_component'])
        }
        
        # 创建启动管理器
        self.startup_manager = StartupManager()
        
        # 注册组件
        for name, component in self.components.items():
            self.startup_manager.register_component(name, component, component.dependencies)
        
        # 执行启动并捕获错误
        startup_errors = []
        
        # 模拟启动过程中的错误处理
        original_start = self.components['error_component'].start
        
        async def start_with_error_handling():
            try:
                return await original_start()
            except Exception as e:
                # 使用错误处理器处理启动错误
                error_report = self.error_handler.handle_error(e, {
                    'component': 'error_component',
                    'phase': 'startup',
                    'operation': 'component_start'
                })
                startup_errors.append(error_report)
                raise
        
        self.components['error_component'].start = start_with_error_handling
        
        # 执行启动
        startup_result = await self.startup_manager.startup_with_dependencies()
        
        # 验证启动失败
        assert startup_result is False
        
        # 验证错误被正确处理
        assert len(startup_errors) > 0
        
        error_report = startup_errors[0]
        assert error_report.classified_error.context['phase'] == 'startup'
        assert error_report.classified_error.component == 'error_component'
        
        # 验证错误统计
        error_stats = self.error_handler.get_error_statistics()
        assert error_stats['total_errors'] > 0
        
        startup_errors_count = sum(
            count for category, count in error_stats['by_component'].items()
            if 'error_component' in category
        )
        assert startup_errors_count > 0
    
    @pytest.mark.asyncio
    async def test_complete_system_lifecycle_integration(self):
        """测试完整系统生命周期集成"""
        logger.info("测试完整系统生命周期集成...")
        
        # 创建测试组件
        self.create_test_components()
        
        # 创建所有管理器
        self.startup_manager = StartupManager()
        self.health_checker = HealthChecker()
        self.shutdown_handler = ShutdownHandler()
        
        # 创建监控优化器
        monitoring_config = MonitoringOptimizationConfig(
            optimization_level=OptimizationLevel.BALANCED
        )
        monitoring_optimizer = MonitoringOptimizer(monitoring_config)
        
        lifecycle_metrics = []
        
        try:
            # 阶段1: 系统启动
            logger.info("阶段1: 系统启动")
            
            # 注册组件到启动管理器
            for name, component in self.components.items():
                self.startup_manager.register_component(name, component, component.dependencies)
            
            # 执行启动
            startup_result = await self.startup_manager.startup_with_dependencies()
            assert startup_result is True
            
            lifecycle_metrics.append({
                'phase': 'startup_complete',
                'timestamp': datetime.now().isoformat(),
                'components_running': sum(1 for c in self.components.values() if c.status == 'running')
            })
            
            # 阶段2: 健康检查
            logger.info("阶段2: 健康检查")
            
            # 注册健康检查
            for name, component in self.components.items():
                self.health_checker.register_component(name, component.health_check)
            
            # 执行健康检查
            health_report = await self.health_checker.perform_comprehensive_health_check()
            assert health_report['overall_status'] == 'healthy'
            
            lifecycle_metrics.append({
                'phase': 'health_check_complete',
                'timestamp': datetime.now().isoformat(),
                'overall_health': health_report['overall_status'],
                'healthy_components': sum(1 for status in health_report['component_status'].values() if status['status'] == 'healthy')
            })
            
            # 阶段3: 监控启动
            logger.info("阶段3: 监控启动")
            
            await monitoring_optimizer.start()
            
            # 注册系统监控任务
            async def system_health_monitor():
                return await self.health_checker.perform_comprehensive_health_check()
            
            monitoring_optimizer.register_monitoring_task(
                'system_health', system_health_monitor, custom_interval=2
            )
            
            lifecycle_metrics.append({
                'phase': 'monitoring_started',
                'timestamp': datetime.now().isoformat(),
                'monitoring_active': monitoring_optimizer.running
            })
            
            # 阶段4: 系统运行
            logger.info("阶段4: 系统运行")
            
            # 模拟系统运行一段时间
            await asyncio.sleep(0.5)
            
            # 验证系统状态
            final_health = await self.health_checker.perform_comprehensive_health_check()
            
            lifecycle_metrics.append({
                'phase': 'system_running',
                'timestamp': datetime.now().isoformat(),
                'overall_health': final_health['overall_status'],
                'uptime': 0.5
            })
            
            # 阶段5: 系统关闭
            logger.info("阶段5: 系统关闭")
            
            # 注册组件到关闭处理器
            for name, component in self.components.items():
                self.shutdown_handler.register_component(name, component)
            
            # 停止监控
            await monitoring_optimizer.stop()
            
            # 执行优雅关闭
            shutdown_result = await self.shutdown_handler.graceful_shutdown()
            assert shutdown_result is True
            
            lifecycle_metrics.append({
                'phase': 'shutdown_complete',
                'timestamp': datetime.now().isoformat(),
                'components_stopped': sum(1 for c in self.components.values() if c.status == 'stopped')
            })
            
            # 验证完整生命周期
            assert len(lifecycle_metrics) == 5
            
            phases = [m['phase'] for m in lifecycle_metrics]
            expected_phases = [
                'startup_complete', 
                'health_check_complete', 
                'monitoring_started', 
                'system_running', 
                'shutdown_complete'
            ]
            assert phases == expected_phases
            
            # 验证最终状态
            final_metrics = lifecycle_metrics[-1]
            assert final_metrics['components_stopped'] == len(self.components)
            
            logger.info("完整系统生命周期测试成功完成")
            
        except Exception as e:
            # 确保监控被停止
            if monitoring_optimizer.running:
                await monitoring_optimizer.stop()
            raise
    
    @pytest.mark.asyncio
    async def test_concurrent_startup_scenarios(self):
        """测试并发启动场景"""
        logger.info("测试并发启动场景...")
        
        # 创建多个独立的组件组
        component_groups = {
            'group_a': {
                'db_a': MockSystemComponent('db_a', startup_time=0.1),
                'api_a': MockSystemComponent('api_a', startup_time=0.1, dependencies=['db_a']),
                'ui_a': MockSystemComponent('ui_a', startup_time=0.1, dependencies=['api_a'])
            },
            'group_b': {
                'db_b': MockSystemComponent('db_b', startup_time=0.15),
                'api_b': MockSystemComponent('api_b', startup_time=0.1, dependencies=['db_b']),
                'ui_b': MockSystemComponent('ui_b', startup_time=0.1, dependencies=['api_b'])
            },
            'group_c': {
                'cache_c': MockSystemComponent('cache_c', startup_time=0.05),
                'processor_c': MockSystemComponent('processor_c', startup_time=0.2, dependencies=['cache_c'])
            }
        }
        
        # 创建多个启动管理器
        startup_managers = {}
        startup_tasks = []
        
        for group_name, components in component_groups.items():
            manager = StartupManager()
            
            # 注册组件
            for name, component in components.items():
                manager.register_component(name, component, component.dependencies)
            
            startup_managers[group_name] = manager
            
            # 创建启动任务
            startup_task = asyncio.create_task(
                manager.startup_with_dependencies()
            )
            startup_tasks.append((group_name, startup_task))
        
        # 并发执行所有启动任务
        start_time = time.time()
        startup_results = {}
        
        for group_name, task in startup_tasks:
            try:
                result = await task
                startup_results[group_name] = result
            except Exception as e:
                startup_results[group_name] = False
                logger.info(f"Group {group_name} startup failed: {e}")
        
        end_time = time.time()
        
        # 验证并发启动结果
        assert len(startup_results) == 3
        assert all(startup_results.values())  # 所有组都应该启动成功
        
        # 验证并发启动时间
        total_time = end_time - start_time
        max_sequential_time = max(
            sum(c.startup_time for c in components.values())
            for components in component_groups.values()
        )
        
        # 并发启动应该比最长的串行时间稍长一点，但远小于所有组的串行时间总和
        assert total_time < max_sequential_time * 2
        
        logger.info(f"并发启动时间: {total_time:.3f}秒")
        logger.info(f"最长组串行时间: {max_sequential_time:.3f}秒")
        
        # 验证所有组件都已启动
        for components in component_groups.values():
            for component in components.values():
                assert component.status == 'running'


if __name__ == '__main__':
    pytest.main([__file__, '-v'])