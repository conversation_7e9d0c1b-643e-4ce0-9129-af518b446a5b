import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
依赖管理集成测试
测试Python和Node.js依赖管理的集成功能
"""

import os
import sys
import unittest
import tempfile
import shutil
import json
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


class TestDependencyIntegration(unittest.TestCase):
    """依赖管理集成测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.test_dir = tempfile.mkdtemp()
        self.project_root = self.test_dir
        self.backend_dir = os.path.join(self.test_dir, "web_ui", "backend")
        self.frontend_dir = os.path.join(self.test_dir, "web_ui", "frontend")
        self.cache_dir = os.path.join(self.test_dir, "data", "cache", "dependencies")
        
        # 创建测试目录结构
        os.makedirs(self.backend_dir, exist_ok=True)
        os.makedirs(self.frontend_dir, exist_ok=True)
        os.makedirs(os.path.join(self.cache_dir, "python"), exist_ok=True)
        os.makedirs(os.path.join(self.cache_dir, "node"), exist_ok=True)
        
        # 创建测试requirements.txt
        self.requirements_content = """# FastAPI核心依赖
fastapi==0.110.0
uvicorn[standard]==0.28.0
pydantic>=2.6.0
pydantic-settings>=2.2.0

# 数据库和缓存
sqlalchemy>=2.0.25
redis>=5.0.1

# 数据处理
pandas>=2.2.0
numpy>=1.26.0

# 工具库
python-dotenv>=1.0.0
psutil>=5.9.6

# 测试工具
pytest>=7.4.3
pytest-asyncio>=0.21.1
"""
        
        self.requirements_file = os.path.join(self.backend_dir, "requirements.txt")
        with open(self.requirements_file, 'w', encoding='utf-8') as f:
            f.write(self.requirements_content)
        
        # 创建测试package.json
        self.package_json_data = {
            "name": "quantitative-trading-frontend",
            "version": "2.0.0",
            "description": "量化交易系统现代Web前端",
            "type": "module",
            "scripts": {
                "dev": "vite",
                "build": "tsc && vite build",
                "preview": "vite preview",
                "lint": "eslint . --ext ts,tsx",
                "test": "jest"
            },
            "dependencies": {
                "@ant-design/icons": "^5.6.1",
                "@ant-design/plots": "^2.6.2",
                "antd": "^5.26.6",
                "axios": "^1.6.0",
                "echarts": "^6.0.0",
                "react": "^18.3.1",
                "react-dom": "^18.3.1",
                "react-router-dom": "^6.30.1",
                "dayjs": "^1.11.10",
                "lodash-es": "^4.17.21"
            },
            "devDependencies": {
                "@types/react": "^18.3.23",
                "@types/react-dom": "^18.3.7",
                "@types/lodash-es": "^4.17.12",
                "@vitejs/plugin-react": "^4.7.0",
                "typescript": "^5.8.3",
                "vite": "^4.5.14",
                "eslint": "^8.53.0",
                "@typescript-eslint/eslint-plugin": "^6.10.0",
                "jest": "^29.7.0"
            }
        }
        
        self.package_json_file = os.path.join(self.frontend_dir, "package.json")
        with open(self.package_json_file, 'w', encoding='utf-8') as f:
            json.dump(self.package_json_data, f, indent=2, ensure_ascii=False)
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_dependency_cache_initialization(self):
        """测试依赖缓存初始化"""
        logger.info("测试依赖缓存初始化...")
        
        # 验证缓存目录结构
        python_cache_dir = os.path.join(self.cache_dir, "python")
        node_cache_dir = os.path.join(self.cache_dir, "node")
        
        self.assertTrue(os.path.exists(python_cache_dir))
        self.assertTrue(os.path.exists(node_cache_dir))
        
        # 创建pip缓存目录
        pip_cache_dir = os.path.join(python_cache_dir, "pip")
        os.makedirs(pip_cache_dir, exist_ok=True)
        
        # 创建npm缓存目录
        npm_cache_dir = os.path.join(node_cache_dir, "npm")
        os.makedirs(npm_cache_dir, exist_ok=True)
        
        # 验证缓存目录创建成功
        self.assertTrue(os.path.exists(pip_cache_dir))
        self.assertTrue(os.path.exists(npm_cache_dir))
        
        logger.info(f"  ✓ Python缓存目录: {python_cache_dir}")
        logger.info(f"  ✓ Node.js缓存目录: {node_cache_dir}")
        logger.info("✅ 依赖缓存初始化测试通过")
    
    def test_requirements_and_package_json_parsing(self):
        """测试requirements.txt和package.json解析"""
        logger.info("测试requirements.txt和package.json解析...")
        
        # 解析requirements.txt
        python_packages = []
        with open(self.requirements_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line.startswith('#') or not line:
                    continue
                
                if '==' in line:
                    package_name = line.split('==')[0]
                    python_packages.append(package_name)
                elif '>=' in line:
                    package_name = line.split('>=')[0]
                    python_packages.append(package_name)
        
        # 解析package.json
        with open(self.package_json_file, 'r', encoding='utf-8') as f:
            package_data = json.load(f)
        
        node_dependencies = list(package_data['dependencies'].keys())
        node_dev_dependencies = list(package_data['devDependencies'].keys())
        
        # 验证解析结果
        self.assertGreater(len(python_packages), 0)
        self.assertGreater(len(node_dependencies), 0)
        self.assertGreater(len(node_dev_dependencies), 0)
        
        # 检查关键包
        self.assertIn('fastapi', python_packages)
        self.assertIn('pandas', python_packages)
        self.assertIn('react', node_dependencies)
        self.assertIn('antd', node_dependencies)
        self.assertIn('typescript', node_dev_dependencies)
        
        logger.info(f"  Python包数量: {len(python_packages)}")
        logger.info(f"  Node.js生产依赖数量: {len(node_dependencies)}")
        logger.info(f"  Node.js开发依赖数量: {len(node_dev_dependencies)}")
        logger.info("✅ 依赖文件解析测试通过")
    
    def test_dependency_conflict_detection(self):
        """测试依赖冲突检测"""
        logger.info("测试依赖冲突检测...")
        
        # 模拟Python依赖冲突检测
        python_conflicts = []
        
        # 创建模拟的已安装包信息
        installed_packages = {
            'fastapi': '0.109.0',  # 与要求的0.110.0冲突
            'pandas': '2.1.0',     # 与要求的>=2.2.0冲突
            'numpy': '1.26.0'      # 符合要求
        }
        
        # 检查冲突
        required_packages = {
            'fastapi': ('==', '0.110.0'),
            'pandas': ('>=', '2.2.0'),
            'numpy': ('>=', '1.26.0')
        }
        
        for package, (operator, required_version) in required_packages.items():
            if package in installed_packages:
                installed_version = installed_packages[package]
                if operator == '==' and installed_version != required_version:
                    python_conflicts.append(f"{package}: 需要{required_version}, 已安装{installed_version}")
                elif operator == '>=' and installed_version < required_version:
                    python_conflicts.append(f"{package}: 需要>={required_version}, 已安装{installed_version}")
        
        # 验证冲突检测
        self.assertEqual(len(python_conflicts), 2)  # fastapi和pandas应该有冲突
        
        logger.info(f"  检测到 {len(python_conflicts)} 个Python依赖冲突:")
        for conflict in python_conflicts:
            logger.info(f"    - {conflict}")
        
        logger.info("✅ 依赖冲突检测测试通过")
    
    def test_dependency_installation_workflow(self):
        """测试完整的依赖安装工作流"""
        logger.info("测试完整的依赖安装工作流...")
        
        workflow_steps = []
        
        # 步骤1: 检查文件存在性
        requirements_exists = os.path.exists(self.requirements_file)
        package_json_exists = os.path.exists(self.package_json_file)
        
        workflow_steps.append(("文件检查", requirements_exists and package_json_exists))
        
        # 步骤2: 初始化缓存
        python_cache_dir = os.path.join(self.cache_dir, "python", "pip")
        node_cache_dir = os.path.join(self.cache_dir, "node", "npm")
        os.makedirs(python_cache_dir, exist_ok=True)
        os.makedirs(node_cache_dir, exist_ok=True)
        
        cache_initialized = os.path.exists(python_cache_dir) and os.path.exists(node_cache_dir)
        workflow_steps.append(("缓存初始化", cache_initialized))
        
        # 步骤3: 模拟虚拟环境检查
        venv_dir = os.path.join(self.backend_dir, "venv")
        venv_needed = not os.path.exists(venv_dir)
        workflow_steps.append(("虚拟环境检查", True))  # 总是通过
        
        # 步骤4: 模拟Python依赖安装
        python_install_log = os.path.join(python_cache_dir, "install.log")
        with open(python_install_log, 'w') as f:
            f.write("Successfully installed fastapi uvicorn pandas numpy")
        
        python_install_success = os.path.exists(python_install_log)
        workflow_steps.append(("Python依赖安装", python_install_success))
        
        # 步骤5: 模拟Node.js依赖安装
        node_modules_dir = os.path.join(self.frontend_dir, "node_modules")
        os.makedirs(node_modules_dir, exist_ok=True)
        
        # 创建关键包目录
        key_packages = ["react", "antd", "typescript", "vite"]
        for package in key_packages:
            package_dir = os.path.join(node_modules_dir, package)
            os.makedirs(package_dir, exist_ok=True)
        
        node_install_success = os.path.exists(node_modules_dir)
        workflow_steps.append(("Node.js依赖安装", node_install_success))
        
        # 步骤6: 验证关键包
        python_critical_packages = ["fastapi", "uvicorn", "pandas", "numpy"]
        node_critical_packages = ["react", "antd", "typescript", "vite"]
        
        # 模拟Python包验证
        python_verified = len(python_critical_packages)  # 假设都验证通过
        
        # Node.js包验证
        node_verified = 0
        for package in node_critical_packages:
            package_dir = os.path.join(node_modules_dir, package)
            if os.path.exists(package_dir):
                node_verified += 1
        
        verification_success = (python_verified == len(python_critical_packages) and 
                              node_verified == len(node_critical_packages))
        workflow_steps.append(("关键包验证", verification_success))
        
        # 验证所有步骤都成功
        all_steps_passed = all(success for _, success in workflow_steps)
        self.assertTrue(all_steps_passed)
        
        logger.info("  工作流步骤执行结果:")
        for step_name, success in workflow_steps:
            status = "✓" if success else "✗"
            logger.info(f"    {status} {step_name}")
        
        logger.info("✅ 完整依赖安装工作流测试通过")
    
    def test_dependency_cache_management(self):
        """测试依赖缓存管理"""
        logger.info("测试依赖缓存管理...")
        
        # 创建缓存文件
        python_cache_dir = os.path.join(self.cache_dir, "python")
        node_cache_dir = os.path.join(self.cache_dir, "node")
        
        # Python缓存文件
        python_cache_files = [
            "pip/cache/wheels/fastapi-0.110.0.whl",
            "pip/cache/wheels/uvicorn-0.28.0.whl",
            "parsed_requirements.txt",
            "install.log"
        ]
        
        for cache_file in python_cache_files:
            cache_path = os.path.join(python_cache_dir, cache_file)
            os.makedirs(os.path.dirname(cache_path), exist_ok=True)
            with open(cache_path, 'w') as f:
                f.write(f"cached content for {cache_file}")
        
        # Node.js缓存文件
        node_cache_files = [
            "npm/registry.npmjs.org/react/-/react-18.3.1.tgz",
            "npm/registry.npmjs.org/antd/-/antd-5.26.6.tgz",
            "install.log",
            "node_modules_check.txt"
        ]
        
        for cache_file in node_cache_files:
            cache_path = os.path.join(node_cache_dir, cache_file)
            os.makedirs(os.path.dirname(cache_path), exist_ok=True)
            with open(cache_path, 'w') as f:
                f.write(f"cached content for {cache_file}")
        
        # 计算缓存大小
        def get_cache_size(cache_dir):
            total_size = 0
            for root, dirs, files in os.walk(cache_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    total_size += os.path.getsize(file_path)
            return total_size
        
        python_cache_size = get_cache_size(python_cache_dir)
        node_cache_size = get_cache_size(node_cache_dir)
        
        self.assertGreater(python_cache_size, 0)
        self.assertGreater(node_cache_size, 0)
        
        # 测试缓存清理
        def clean_cache(cache_dir):
            for root, dirs, files in os.walk(cache_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    os.remove(file_path)
        
        # 清理Python缓存
        clean_cache(python_cache_dir)
        python_cache_size_after = get_cache_size(python_cache_dir)
        
        self.assertEqual(python_cache_size_after, 0)
        
        logger.info(f"  Python缓存大小: {python_cache_size} bytes -> {python_cache_size_after} bytes")
        logger.info(f"  Node.js缓存大小: {node_cache_size} bytes")
        logger.info("✅ 依赖缓存管理测试通过")
    
    def test_dependency_update_workflow(self):
        """测试依赖更新工作流"""
        logger.info("测试依赖更新工作流...")
        
        # 模拟检查Python包更新
        python_outdated = {
            'fastapi': {'current': '0.109.0', 'latest': '0.110.0'},
            'pandas': {'current': '2.1.0', 'latest': '2.2.0'}
        }
        
        # 模拟检查Node.js包更新
        node_outdated = {
            'react': {'current': '18.2.0', 'wanted': '18.3.1', 'latest': '18.3.1'},
            'antd': {'current': '5.25.0', 'wanted': '5.26.6', 'latest': '5.26.6'}
        }
        
        # 验证更新检测
        python_updates_available = len(python_outdated) > 0
        node_updates_available = len(node_outdated) > 0
        
        self.assertTrue(python_updates_available)
        self.assertTrue(node_updates_available)
        
        # 模拟更新过程
        update_log = []
        
        # Python更新
        for package, versions in python_outdated.items():
            update_log.append(f"Updated {package}: {versions['current']} -> {versions['latest']}")
        
        # Node.js更新
        for package, versions in node_outdated.items():
            update_log.append(f"Updated {package}: {versions['current']} -> {versions['wanted']}")
        
        # 验证更新日志
        self.assertEqual(len(update_log), 4)
        
        logger.info("  检测到的更新:")
        logger.info(f"    Python包: {len(python_outdated)} 个")
        logger.info(f"    Node.js包: {len(node_outdated)} 个")
        
        logger.info("  更新日志:")
        for log_entry in update_log:
            logger.info(f"    - {log_entry}")
        
        logger.info("✅ 依赖更新工作流测试通过")
    
    def test_dependency_security_audit(self):
        """测试依赖安全审计"""
        logger.info("测试依赖安全审计...")
        
        # 模拟Python安全检查（使用safety或pip-audit）
        python_vulnerabilities = [
            {
                'package': 'requests',
                'version': '2.25.0',
                'vulnerability': 'CVE-2023-32681',
                'severity': 'moderate'
            }
        ]
        
        # 模拟Node.js安全审计
        node_audit_result = {
            'vulnerabilities': {
                'info': 0,
                'low': 1,
                'moderate': 2,
                'high': 0,
                'critical': 0
            },
            'total': 3
        }
        
        # 评估安全状态
        python_has_vulnerabilities = len(python_vulnerabilities) > 0
        node_has_vulnerabilities = node_audit_result['total'] > 0
        
        # 计算风险等级
        def calculate_risk_level(vulnerabilities):
            if isinstance(vulnerabilities, list):
                # Python风格
                high_risk = any(v['severity'] in ['high', 'critical'] for v in vulnerabilities)
                return 'high' if high_risk else 'moderate' if vulnerabilities else 'low'
            else:
                # Node.js风格
                if vulnerabilities['vulnerabilities']['critical'] > 0:
                    return 'critical'
                elif vulnerabilities['vulnerabilities']['high'] > 0:
                    return 'high'
                elif vulnerabilities['vulnerabilities']['moderate'] > 0:
                    return 'moderate'
                elif vulnerabilities['vulnerabilities']['low'] > 0:
                    return 'low'
                else:
                    return 'none'
        
        python_risk = calculate_risk_level(python_vulnerabilities)
        node_risk = calculate_risk_level(node_audit_result)
        
        # 验证安全审计结果
        self.assertTrue(python_has_vulnerabilities)
        self.assertTrue(node_has_vulnerabilities)
        self.assertEqual(python_risk, 'moderate')
        self.assertEqual(node_risk, 'moderate')
        
        logger.info("  安全审计结果:")
        logger.info(f"    Python漏洞: {len(python_vulnerabilities)} 个 (风险等级: {python_risk})")
        logger.info(f"    Node.js漏洞: {node_audit_result['total']} 个 (风险等级: {node_risk})")
        
        if python_vulnerabilities:
            logger.info("    Python漏洞详情:")
            for vuln in python_vulnerabilities:
                logger.info(f"      - {vuln['package']} {vuln['version']}: {vuln['vulnerability']} ({vuln['severity']})")
        
        logger.info("✅ 依赖安全审计测试通过")


def run_dependency_integration_tests():
    """运行依赖管理集成测试套件"""
    logger.info("=" * 60)
    logger.info("🧪 依赖管理集成测试套件")
    logger.info("=" * 60)
    logger.info("测试开始时间:", __import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    logger.info()
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestDependencyIntegration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    logger.info()
    logger.info("=" * 60)
    logger.info("📊 测试结果统计")
    logger.info("=" * 60)
    logger.info(f"总测试数: {result.testsRun}")
    logger.info(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    logger.info(f"失败: {len(result.failures)}")
    logger.info(f"错误: {len(result.errors)}")
    
    if result.failures:
        logger.info("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            logger.info(f"  - {test}: {traceback}")
    
    if result.errors:
        logger.info("\n💥 错误的测试:")
        for test, traceback in result.errors:
            logger.info(f"  - {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    logger.info(f"\n✅ 测试成功率: {success_rate:.1f}%")
    
    if result.wasSuccessful():
        logger.info("\n🎉 所有依赖管理集成测试通过！")
        return True
    else:
        logger.info("\n⚠️ 部分测试失败，请检查上述错误信息")
        return False


if __name__ == "__main__":
    success = run_dependency_integration_tests()
    sys.exit(0 if success else 1)