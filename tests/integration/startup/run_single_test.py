import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个测试运行器

此脚本用于运行单个用户验收测试组件，便于调试和单独验证特定功能。

作者: 系统启动修复团队
创建时间: 2025-01-07
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from tests.integration.startup.test_user_acceptance import UserAcceptanceTestSuite


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="用户验收测试单个组件运行器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run_single_test.py --first-time     # 运行首次安装测试
  python run_single_test.py --scenarios      # 运行常见场景测试
  python run_single_test.py --performance    # 运行性能基准测试
  python run_single_test.py --scenario normal_startup  # 运行特定场景
        """
    )
    
    # 添加命令行参数
    parser.add_argument(
        '--first-time',
        action='store_true',
        help='运行首次安装体验测试'
    )
    
    parser.add_argument(
        '--scenarios',
        action='store_true',
        help='运行所有常见场景测试'
    )
    
    parser.add_argument(
        '--performance',
        action='store_true',
        help='运行性能基准测试'
    )
    
    parser.add_argument(
        '--scenario',
        type=str,
        help='运行特定场景测试（如：normal_startup, restart_scenario等）'
    )
    
    parser.add_argument(
        '--report',
        action='store_true',
        help='生成测试报告'
    )
    
    parser.add_argument(
        '--verbose',
        '-v',
        action='store_true',
        help='详细输出模式'
    )
    
    parser.add_argument(
        '--project-root',
        type=str,
        default=str(project_root),
        help='项目根目录路径'
    )
    
    args = parser.parse_args()
    
    # 创建测试套件
    suite = UserAcceptanceTestSuite(args.project_root)
    
    results = []
    
    try:
        # 根据参数运行相应的测试
        if args.first_time:
            logger.info("🚀 运行首次安装体验测试...")
            result = suite.simulate_first_time_setup()
            results.append(result)
            print_test_result(result, args.verbose)
            
        elif args.scenarios:
            logger.info("🔄 运行常见使用场景测试...")
            scenario_results = suite.test_common_scenarios()
            results.extend(scenario_results)
            print_scenario_results(scenario_results, args.verbose)
            
        elif args.performance:
            logger.info("⚡ 运行性能基准测试...")
            result = suite.performance_benchmark()
            results.append(result)
            print_test_result(result, args.verbose)
            
        elif args.scenario:
            logger.info(f"🧪 运行特定场景测试: {args.scenario}")
            result = run_specific_scenario(suite, args.scenario)
            if result:
                results.append(result)
                print_test_result(result, args.verbose)
            else:
                logger.info(f"❌ 未找到场景: {args.scenario}")
                return 1
                
        elif args.report:
            logger.info("📋 生成测试报告...")
            # 运行所有测试以生成完整报告
            logger.info("运行完整测试套件以生成报告...")
            
            first_time_result = suite.simulate_first_time_setup()
            results.append(first_time_result)
            
            scenario_results = suite.test_common_scenarios()
            results.extend(scenario_results)
            
            performance_result = suite.performance_benchmark()
            results.append(performance_result)
            
            # 生成报告
            report = suite.create_user_experience_report(results)
            logger.info("✅ 报告生成完成")
            
        else:
            parser.print_help()
            return 1
        
        # 计算总体结果
        if results:
            success_count = sum(1 for r in results if r.success)
            total_count = len(results)
            success_rate = (success_count / total_count) * 100
            
            logger.info(f"\n📊 测试结果摘要:")
            logger.info(f"   总测试数: {total_count}")
            logger.info(f"   通过测试: {success_count}")
            logger.info(f"   失败测试: {total_count - success_count}")
            logger.info(f"   成功率: {success_rate:.1f}%")
            
            return 0 if success_count == total_count else 1
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("\n⏹️ 测试被用户中断")
        return 1
    except Exception as e:
        logger.info(f"\n❌ 测试执行失败: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


def print_test_result(result, verbose=False):
    """打印单个测试结果"""
    status = "✅ 通过" if result.success else "❌ 失败"
    logger.info(f"\n{status} {result.test_name}")
    logger.info(f"   耗时: {result.duration:.2f}秒")
    
    if result.user_experience_score is not None:
        logger.info(f"   用户体验评分: {result.user_experience_score:.1f}/10.0")
    
    if result.performance_metrics and verbose:
        logger.info(f"   性能指标:")
        for key, value in result.performance_metrics.items():
            if isinstance(value, float):
                logger.info(f"     {key}: {value:.2f}")
            else:
                logger.info(f"     {key}: {value}")
    
    if not result.success and result.error_message:
        logger.info(f"   错误信息: {result.error_message}")


def print_scenario_results(results, verbose=False):
    """打印场景测试结果"""
    success_count = sum(1 for r in results if r.success)
    total_count = len(results)
    
    logger.info(f"\n📊 场景测试结果: {success_count}/{total_count} 通过")
    logger.info(f"成功率: {(success_count/total_count)*100:.1f}%")
    
    for result in results:
        print_test_result(result, verbose)


def run_specific_scenario(suite, scenario_name):
    """运行特定场景测试"""
    # 场景映射
    scenario_map = {
        'normal_startup': suite._test_normal_startup,
        'restart_scenario': suite._test_restart_scenario,
        'port_conflict': suite._test_port_conflict_recovery,
        'missing_dependency': suite._test_missing_dependency_recovery,
        'config_corruption': suite._test_config_corruption_recovery,
        'venv_rebuild': suite._test_venv_rebuild,
        'service_crash': suite._test_service_crash_recovery,
        'network_issues': suite._test_network_issues,
        'disk_space': suite._test_disk_space_issues,
        'permission_issues': suite._test_permission_issues
    }
    
    if scenario_name in scenario_map:
        return scenario_map[scenario_name]()
    else:
        available_scenarios = list(scenario_map.keys())
        logger.info(f"可用的场景测试: {', '.join(available_scenarios)}")
        return None


if __name__ == "__main__":
    exit(main())