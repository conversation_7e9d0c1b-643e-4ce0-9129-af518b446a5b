import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整启动流程集成测试
测试系统启动的完整工作流程，包括错误恢复和服务依赖关系
"""

import os
import sys
import unittest
import tempfile
import shutil
import subprocess
import time
import json
import threading
from pathlib import Path
from unittest.mock import patch, MagicMock, call

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


class TestCompleteStartupFlow(unittest.TestCase):
    """完整启动流程测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.project_root = str(project_root)
        self.test_dir = tempfile.mkdtemp()
        self.startup_script = os.path.join(self.project_root, "start_system.sh")
        
        # 创建测试环境目录结构
        self.backend_dir = os.path.join(self.test_dir, "web_ui", "backend")
        self.frontend_dir = os.path.join(self.test_dir, "web_ui", "frontend")
        self.logs_dir = os.path.join(self.test_dir, "logs")
        self.data_dir = os.path.join(self.test_dir, "data")
        
        for directory in [self.backend_dir, self.frontend_dir, self.logs_dir, self.data_dir]:
            os.makedirs(directory, exist_ok=True)
        
        # 创建测试配置文件
        self._create_test_configs()
        
        # 启动流程状态跟踪
        self.startup_phases = []
        self.startup_errors = []
        self.startup_warnings = []
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def _create_test_configs(self):
        """创建测试配置文件"""
        # 创建后端requirements.txt
        requirements_content = """fastapi==0.110.0
uvicorn[standard]==0.28.0
pydantic>=2.6.0
sqlalchemy>=2.0.25
pandas>=2.2.0
numpy>=1.26.0
python-dotenv>=1.0.0
pytest>=7.4.3
"""
        with open(os.path.join(self.backend_dir, "requirements.txt"), 'w') as f:
            f.write(requirements_content)
        
        # 创建前端package.json
        package_json = {
            "name": "quantitative-trading-frontend",
            "version": "2.0.0",
            "scripts": {
                "dev": "vite",
                "build": "tsc && vite build"
            },
            "dependencies": {
                "react": "^18.3.1",
                "antd": "^5.26.6",
                "axios": "^1.6.0"
            },
            "devDependencies": {
                "@vitejs/plugin-react": "^4.7.0",
                "typescript": "^5.8.3",
                "vite": "^4.5.14"
            }
        }
        
        with open(os.path.join(self.frontend_dir, "package.json"), 'w') as f:
            json.dump(package_json, f, indent=2)
        
        # 创建环境配置文件
        backend_env = """HOST=127.0.0.1
PORT=8000
DEBUG=true
DATABASE_URL=sqlite:///../../data/db/trading_system.db
LOG_LEVEL=INFO
"""
        with open(os.path.join(self.backend_dir, ".env"), 'w') as f:
            f.write(backend_env)
        
        frontend_env = """VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000
NODE_ENV=development
"""
        with open(os.path.join(self.frontend_dir, ".env"), 'w') as f:
            f.write(frontend_env)
    
    def test_complete_startup_flow(self):
        """测试完整启动流程"""
        logger.info("测试完整启动流程...")
        
        # 定义启动阶段
        startup_phases = [
            "环境检查",
            "虚拟环境管理",
            "依赖安装",
            "配置验证",
            "服务启动",
            "健康检查",
            "状态监控"
        ]
        
        # 模拟每个启动阶段
        startup_results = {}
        
        # 阶段1: 环境检查
        env_check_result = self._simulate_environment_check()
        startup_results["环境检查"] = env_check_result
        self.assertTrue(env_check_result["success"], "环境检查失败")
        
        # 阶段2: 虚拟环境管理
        venv_result = self._simulate_virtual_environment_setup()
        startup_results["虚拟环境管理"] = venv_result
        self.assertTrue(venv_result["success"], "虚拟环境设置失败")
        
        # 阶段3: 依赖安装
        deps_result = self._simulate_dependency_installation()
        startup_results["依赖安装"] = deps_result
        self.assertTrue(deps_result["success"], "依赖安装失败")
        
        # 阶段4: 配置验证
        config_result = self._simulate_configuration_validation()
        startup_results["配置验证"] = config_result
        self.assertTrue(config_result["success"], "配置验证失败")
        
        # 阶段5: 服务启动
        service_result = self._simulate_service_startup()
        startup_results["服务启动"] = service_result
        self.assertTrue(service_result["success"], "服务启动失败")
        
        # 阶段6: 健康检查
        health_result = self._simulate_health_checks()
        startup_results["健康检查"] = health_result
        self.assertTrue(health_result["success"], "健康检查失败")
        
        # 阶段7: 状态监控
        monitor_result = self._simulate_status_monitoring()
        startup_results["状态监控"] = monitor_result
        self.assertTrue(monitor_result["success"], "状态监控失败")
        
        # 验证完整流程
        all_phases_successful = all(result["success"] for result in startup_results.values())
        self.assertTrue(all_phases_successful, "启动流程中存在失败的阶段")
        
        # 计算总启动时间
        total_startup_time = sum(result["duration"] for result in startup_results.values())
        self.assertLess(total_startup_time, 300, "启动时间超过5分钟")
        
        logger.info("  启动阶段执行结果:")
        for phase, result in startup_results.items():
            status = "✓" if result["success"] else "✗"
            logger.info(f"    {status} {phase}: {result['duration']:.1f}秒")
        
        logger.info(f"  总启动时间: {total_startup_time:.1f}秒")
        logger.info("✅ 完整启动流程测试通过")
    
    def _simulate_environment_check(self):
        """模拟环境检查"""
        start_time = time.time()
        
        # 检查Python版本
        python_version_ok = sys.version_info >= (3, 8)
        
        # 检查必需目录
        required_dirs = [self.backend_dir, self.frontend_dir, self.logs_dir]
        dirs_exist = all(os.path.exists(d) for d in required_dirs)
        
        # 检查端口可用性（模拟）
        ports_available = True  # 假设端口可用
        
        success = python_version_ok and dirs_exist and ports_available
        duration = time.time() - start_time
        
        return {
            "success": success,
            "duration": duration,
            "details": {
                "python_version": python_version_ok,
                "directories": dirs_exist,
                "ports": ports_available
            }
        }
    
    def _simulate_virtual_environment_setup(self):
        """模拟虚拟环境设置"""
        start_time = time.time()
        
        venv_dir = os.path.join(self.backend_dir, "venv")
        
        # 模拟创建虚拟环境
        os.makedirs(venv_dir, exist_ok=True)
        os.makedirs(os.path.join(venv_dir, "bin"), exist_ok=True)
        
        # 创建激活脚本
        activate_script = os.path.join(venv_dir, "bin", "activate")
        with open(activate_script, 'w') as f:
            f.write("# Virtual environment activation script\n")
        
        # 验证虚拟环境
        venv_created = os.path.exists(activate_script)
        
        duration = time.time() - start_time
        
        return {
            "success": venv_created,
            "duration": duration,
            "details": {
                "venv_path": venv_dir,
                "activate_script": activate_script
            }
        }
    
    def _simulate_dependency_installation(self):
        """模拟依赖安装"""
        start_time = time.time()
        
        # 模拟Python依赖安装
        python_deps_installed = self._simulate_python_deps_install()
        
        # 模拟Node.js依赖安装
        node_deps_installed = self._simulate_node_deps_install()
        
        success = python_deps_installed and node_deps_installed
        duration = time.time() - start_time
        
        return {
            "success": success,
            "duration": duration,
            "details": {
                "python_deps": python_deps_installed,
                "node_deps": node_deps_installed
            }
        }
    
    def _simulate_python_deps_install(self):
        """模拟Python依赖安装"""
        # 创建site-packages目录
        site_packages = os.path.join(self.backend_dir, "venv", "lib", "python3.8", "site-packages")
        os.makedirs(site_packages, exist_ok=True)
        
        # 模拟安装关键包
        key_packages = ["fastapi", "uvicorn", "pydantic", "sqlalchemy", "pandas"]
        for package in key_packages:
            package_dir = os.path.join(site_packages, package)
            os.makedirs(package_dir, exist_ok=True)
            
            # 创建包信息文件
            with open(os.path.join(package_dir, "__init__.py"), 'w') as f:
                f.write(f"# {package} package\n")
        
        return len(key_packages) == 5
    
    def _simulate_node_deps_install(self):
        """模拟Node.js依赖安装"""
        # 创建node_modules目录
        node_modules = os.path.join(self.frontend_dir, "node_modules")
        os.makedirs(node_modules, exist_ok=True)
        
        # 模拟安装关键包
        key_packages = ["react", "antd", "axios", "vite", "typescript"]
        for package in key_packages:
            package_dir = os.path.join(node_modules, package)
            os.makedirs(package_dir, exist_ok=True)
            
            # 创建package.json
            package_info = {
                "name": package,
                "version": "1.0.0"
            }
            with open(os.path.join(package_dir, "package.json"), 'w') as f:
                json.dump(package_info, f)
        
        return len(key_packages) == 5
    
    def _simulate_configuration_validation(self):
        """模拟配置验证"""
        start_time = time.time()
        
        # 验证后端配置
        backend_env_path = os.path.join(self.backend_dir, ".env")
        backend_config_valid = os.path.exists(backend_env_path)
        
        # 验证前端配置
        frontend_env_path = os.path.join(self.frontend_dir, ".env")
        frontend_config_valid = os.path.exists(frontend_env_path)
        
        # 验证package.json
        package_json_path = os.path.join(self.frontend_dir, "package.json")
        package_json_valid = os.path.exists(package_json_path)
        
        # 验证requirements.txt
        requirements_path = os.path.join(self.backend_dir, "requirements.txt")
        requirements_valid = os.path.exists(requirements_path)
        
        success = all([
            backend_config_valid,
            frontend_config_valid,
            package_json_valid,
            requirements_valid
        ])
        
        duration = time.time() - start_time
        
        return {
            "success": success,
            "duration": duration,
            "details": {
                "backend_env": backend_config_valid,
                "frontend_env": frontend_config_valid,
                "package_json": package_json_valid,
                "requirements": requirements_valid
            }
        }
    
    def _simulate_service_startup(self):
        """模拟服务启动"""
        start_time = time.time()
        
        # 模拟后端服务启动
        backend_started = self._simulate_backend_startup()
        
        # 模拟前端服务启动
        frontend_started = self._simulate_frontend_startup()
        
        success = backend_started and frontend_started
        duration = time.time() - start_time
        
        return {
            "success": success,
            "duration": duration,
            "details": {
                "backend": backend_started,
                "frontend": frontend_started
            }
        }
    
    def _simulate_backend_startup(self):
        """模拟后端服务启动"""
        # 创建PID文件
        pid_file = os.path.join(self.backend_dir, "backend.pid")
        with open(pid_file, 'w') as f:
            f.write("12345")
        
        # 创建日志文件
        log_file = os.path.join(self.logs_dir, "backend.log")
        with open(log_file, 'w') as f:
            f.write("Backend service started successfully\n")
        
        return os.path.exists(pid_file) and os.path.exists(log_file)
    
    def _simulate_frontend_startup(self):
        """模拟前端服务启动"""
        # 创建PID文件
        pid_file = os.path.join(self.frontend_dir, "frontend.pid")
        with open(pid_file, 'w') as f:
            f.write("12346")
        
        # 创建日志文件
        log_file = os.path.join(self.logs_dir, "frontend.log")
        with open(log_file, 'w') as f:
            f.write("Frontend service started successfully\n")
        
        return os.path.exists(pid_file) and os.path.exists(log_file)
    
    def _simulate_health_checks(self):
        """模拟健康检查"""
        start_time = time.time()
        
        # 模拟后端健康检查
        backend_healthy = self._simulate_backend_health_check()
        
        # 模拟前端健康检查
        frontend_healthy = self._simulate_frontend_health_check()
        
        # 模拟连通性测试
        connectivity_ok = self._simulate_connectivity_tests()
        
        success = backend_healthy and frontend_healthy and connectivity_ok
        duration = time.time() - start_time
        
        return {
            "success": success,
            "duration": duration,
            "details": {
                "backend_health": backend_healthy,
                "frontend_health": frontend_healthy,
                "connectivity": connectivity_ok
            }
        }
    
    def _simulate_backend_health_check(self):
        """模拟后端健康检查"""
        # 检查PID文件存在
        pid_file = os.path.join(self.backend_dir, "backend.pid")
        return os.path.exists(pid_file)
    
    def _simulate_frontend_health_check(self):
        """模拟前端健康检查"""
        # 检查PID文件存在
        pid_file = os.path.join(self.frontend_dir, "frontend.pid")
        return os.path.exists(pid_file)
    
    def _simulate_connectivity_tests(self):
        """模拟连通性测试"""
        # 模拟API连通性测试
        api_connectivity = True  # 假设API可连通
        
        # 模拟WebSocket连通性测试
        websocket_connectivity = True  # 假设WebSocket可连通
        
        # 模拟数据库连通性测试
        database_connectivity = True  # 假设数据库可连通
        
        return api_connectivity and websocket_connectivity and database_connectivity
    
    def _simulate_status_monitoring(self):
        """模拟状态监控"""
        start_time = time.time()
        
        # 创建状态监控报告
        status_report = {
            "timestamp": time.time(),
            "services": {
                "backend": {
                    "status": "running",
                    "pid": 12345,
                    "port": 8000
                },
                "frontend": {
                    "status": "running", 
                    "pid": 12346,
                    "port": 3000
                }
            },
            "health_checks": {
                "backend": "healthy",
                "frontend": "healthy",
                "connectivity": "ok"
            }
        }
        
        # 保存状态报告
        status_file = os.path.join(self.logs_dir, "system_status.json")
        with open(status_file, 'w') as f:
            json.dump(status_report, f, indent=2)
        
        success = os.path.exists(status_file)
        duration = time.time() - start_time
        
        return {
            "success": success,
            "duration": duration,
            "details": {
                "status_file": status_file,
                "report": status_report
            }
        }


class TestErrorRecovery(unittest.TestCase):
    """错误恢复机制测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.project_root = str(project_root)
        self.test_dir = tempfile.mkdtemp()
        
        # 创建测试环境
        self.backend_dir = os.path.join(self.test_dir, "web_ui", "backend")
        self.frontend_dir = os.path.join(self.test_dir, "web_ui", "frontend")
        self.logs_dir = os.path.join(self.test_dir, "logs")
        
        for directory in [self.backend_dir, self.frontend_dir, self.logs_dir]:
            os.makedirs(directory, exist_ok=True)
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_error_recovery(self):
        """测试错误恢复机制"""
        logger.info("测试错误恢复机制...")
        
        # 定义错误场景和恢复策略
        error_scenarios = [
            {
                "name": "虚拟环境损坏",
                "error_type": "venv_corrupted",
                "recovery_action": "recreate_venv"
            },
            {
                "name": "依赖安装失败",
                "error_type": "dependency_install_failed",
                "recovery_action": "retry_with_cache_clear"
            },
            {
                "name": "端口被占用",
                "error_type": "port_occupied",
                "recovery_action": "find_alternative_port"
            },
            {
                "name": "配置文件缺失",
                "error_type": "config_missing",
                "recovery_action": "generate_default_config"
            },
            {
                "name": "服务启动失败",
                "error_type": "service_start_failed",
                "recovery_action": "diagnose_and_retry"
            }
        ]
        
        recovery_results = {}
        
        for scenario in error_scenarios:
            logger.info(f"  测试错误场景: {scenario['name']}")
            
            # 模拟错误发生
            error_simulated = self._simulate_error(scenario["error_type"])
            self.assertTrue(error_simulated, f"错误场景 {scenario['name']} 模拟失败")
            
            # 执行恢复操作
            recovery_success = self._execute_recovery(scenario["recovery_action"])
            recovery_results[scenario["name"]] = recovery_success
            
            logger.info(f"    恢复结果: {'成功' if recovery_success else '失败'}")
        
        # 验证所有错误都能成功恢复
        all_recovered = all(recovery_results.values())
        self.assertTrue(all_recovered, "部分错误场景恢复失败")
        
        logger.info("✅ 错误恢复机制测试通过")
    
    def _simulate_error(self, error_type):
        """模拟特定类型的错误"""
        if error_type == "venv_corrupted":
            # 模拟虚拟环境损坏
            venv_dir = os.path.join(self.backend_dir, "venv")
            os.makedirs(venv_dir, exist_ok=True)
            # 创建损坏的激活脚本
            activate_script = os.path.join(venv_dir, "bin", "activate")
            os.makedirs(os.path.dirname(activate_script), exist_ok=True)
            with open(activate_script, 'w') as f:
                f.write("# Corrupted activation script")
            return True
            
        elif error_type == "dependency_install_failed":
            # 模拟依赖安装失败
            error_log = os.path.join(self.logs_dir, "dependency_error.log")
            with open(error_log, 'w') as f:
                f.write("ERROR: Could not install packages due to network error")
            return True
            
        elif error_type == "port_occupied":
            # 模拟端口被占用
            port_error_log = os.path.join(self.logs_dir, "port_error.log")
            with open(port_error_log, 'w') as f:
                f.write("ERROR: Port 8000 is already in use")
            return True
            
        elif error_type == "config_missing":
            # 模拟配置文件缺失（删除.env文件）
            env_file = os.path.join(self.backend_dir, ".env")
            if os.path.exists(env_file):
                os.remove(env_file)
            return not os.path.exists(env_file)
            
        elif error_type == "service_start_failed":
            # 模拟服务启动失败
            service_error_log = os.path.join(self.logs_dir, "service_error.log")
            with open(service_error_log, 'w') as f:
                f.write("ERROR: Service failed to start - database connection failed")
            return True
        
        return False
    
    def _execute_recovery(self, recovery_action):
        """执行恢复操作"""
        if recovery_action == "recreate_venv":
            # 重新创建虚拟环境
            venv_dir = os.path.join(self.backend_dir, "venv")
            if os.path.exists(venv_dir):
                shutil.rmtree(venv_dir)
            
            os.makedirs(venv_dir, exist_ok=True)
            os.makedirs(os.path.join(venv_dir, "bin"), exist_ok=True)
            
            # 创建正确的激活脚本
            activate_script = os.path.join(venv_dir, "bin", "activate")
            with open(activate_script, 'w') as f:
                f.write("# Virtual environment activation script\nexport VIRTUAL_ENV=\"$( cd \"$( dirname \"${BASH_SOURCE[0]}\" )\" && pwd )/..\"\n")
            
            return os.path.exists(activate_script)
            
        elif recovery_action == "retry_with_cache_clear":
            # 清理缓存并重试
            cache_dir = os.path.join(self.test_dir, "cache")
            if os.path.exists(cache_dir):
                shutil.rmtree(cache_dir)
            
            # 模拟重新安装成功
            success_log = os.path.join(self.logs_dir, "dependency_success.log")
            with open(success_log, 'w') as f:
                f.write("SUCCESS: All dependencies installed successfully")
            
            return os.path.exists(success_log)
            
        elif recovery_action == "find_alternative_port":
            # 寻找替代端口
            alternative_ports = [8001, 8002, 8003]
            for port in alternative_ports:
                # 模拟端口可用性检查
                port_available = True  # 假设端口可用
                if port_available:
                    # 更新配置使用新端口
                    config_update_log = os.path.join(self.logs_dir, "port_update.log")
                    with open(config_update_log, 'w') as f:
                        f.write(f"SUCCESS: Updated to use port {port}")
                    return True
            return False
            
        elif recovery_action == "generate_default_config":
            # 生成默认配置
            env_file = os.path.join(self.backend_dir, ".env")
            default_config = """HOST=127.0.0.1
PORT=8000
DEBUG=true
DATABASE_URL=sqlite:///../../data/db/trading_system.db
LOG_LEVEL=INFO
"""
            with open(env_file, 'w') as f:
                f.write(default_config)
            
            return os.path.exists(env_file)
            
        elif recovery_action == "diagnose_and_retry":
            # 诊断问题并重试
            diagnostic_log = os.path.join(self.logs_dir, "diagnostic.log")
            with open(diagnostic_log, 'w') as f:
                f.write("DIAGNOSTIC: Database connection issue resolved\nSUCCESS: Service started successfully")
            
            # 创建服务PID文件表示启动成功
            pid_file = os.path.join(self.backend_dir, "service.pid")
            with open(pid_file, 'w') as f:
                f.write("12345")
            
            return os.path.exists(pid_file)
        
        return False


class TestServiceDependencies(unittest.TestCase):
    """服务依赖关系测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.project_root = str(project_root)
        self.test_dir = tempfile.mkdtemp()
        
        # 创建测试环境
        self.backend_dir = os.path.join(self.test_dir, "web_ui", "backend")
        self.frontend_dir = os.path.join(self.test_dir, "web_ui", "frontend")
        self.data_dir = os.path.join(self.test_dir, "data", "db")
        self.logs_dir = os.path.join(self.test_dir, "logs")
        
        for directory in [self.backend_dir, self.frontend_dir, self.data_dir, self.logs_dir]:
            os.makedirs(directory, exist_ok=True)
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_service_dependencies(self):
        """测试服务依赖关系"""
        logger.info("测试服务依赖关系...")
        
        # 定义服务依赖图
        service_dependencies = {
            "数据库": {
                "dependencies": [],
                "dependents": ["后端API", "数据处理服务"]
            },
            "后端API": {
                "dependencies": ["数据库"],
                "dependents": ["前端服务", "WebSocket服务"]
            },
            "前端服务": {
                "dependencies": ["后端API"],
                "dependents": []
            },
            "WebSocket服务": {
                "dependencies": ["后端API"],
                "dependents": ["前端服务"]
            },
            "数据处理服务": {
                "dependencies": ["数据库"],
                "dependents": ["后端API"]
            }
        }
        
        # 测试依赖启动顺序
        startup_order = self._calculate_startup_order(service_dependencies)
        expected_order = ["数据库", "数据处理服务", "后端API", "WebSocket服务", "前端服务"]
        
        self.assertEqual(startup_order, expected_order, "服务启动顺序不正确")
        
        # 测试依赖关系验证
        dependency_validation = self._validate_dependencies(service_dependencies, startup_order)
        self.assertTrue(dependency_validation, "服务依赖关系验证失败")
        
        # 测试服务启动模拟
        startup_simulation = self._simulate_dependency_startup(startup_order)
        self.assertTrue(startup_simulation["success"], "依赖启动模拟失败")
        
        logger.info(f"  计算的启动顺序: {' -> '.join(startup_order)}")
        logger.info(f"  启动模拟结果: {'成功' if startup_simulation['success'] else '失败'}")
        logger.info("✅ 服务依赖关系测试通过")
    
    def _calculate_startup_order(self, dependencies):
        """计算服务启动顺序（拓扑排序）"""
        # 简化的拓扑排序实现
        in_degree = {}
        graph = {}
        
        # 初始化
        for service in dependencies:
            in_degree[service] = 0
            graph[service] = []
        
        # 构建图和计算入度
        for service, info in dependencies.items():
            for dep in info["dependencies"]:
                if dep in graph:
                    graph[dep].append(service)
                    in_degree[service] += 1
        
        # 拓扑排序
        queue = [service for service, degree in in_degree.items() if degree == 0]
        result = []
        
        while queue:
            current = queue.pop(0)
            result.append(current)
            
            for neighbor in graph[current]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)
        
        return result
    
    def _validate_dependencies(self, dependencies, startup_order):
        """验证依赖关系是否满足启动顺序"""
        service_positions = {service: i for i, service in enumerate(startup_order)}
        
        for service, info in dependencies.items():
            service_pos = service_positions[service]
            
            # 检查所有依赖是否在当前服务之前启动
            for dep in info["dependencies"]:
                if dep in service_positions:
                    dep_pos = service_positions[dep]
                    if dep_pos >= service_pos:
                        return False
        
        return True
    
    def _simulate_dependency_startup(self, startup_order):
        """模拟依赖启动过程"""
        started_services = {}
        startup_log = []
        
        for service in startup_order:
            # 模拟服务启动
            start_time = time.time()
            
            if service == "数据库":
                success = self._start_database_service()
            elif service == "后端API":
                success = self._start_backend_service()
            elif service == "前端服务":
                success = self._start_frontend_service()
            elif service == "WebSocket服务":
                success = self._start_websocket_service()
            elif service == "数据处理服务":
                success = self._start_data_processing_service()
            else:
                success = True  # 默认成功
            
            duration = time.time() - start_time
            started_services[service] = success
            startup_log.append({
                "service": service,
                "success": success,
                "duration": duration
            })
            
            if not success:
                return {
                    "success": False,
                    "failed_service": service,
                    "startup_log": startup_log
                }
        
        return {
            "success": True,
            "started_services": started_services,
            "startup_log": startup_log
        }
    
    def _start_database_service(self):
        """启动数据库服务"""
        # 创建数据库文件
        db_file = os.path.join(self.data_dir, "trading_system.db")
        with open(db_file, 'w') as f:
            f.write("# SQLite database file")
        
        # 创建数据库PID文件
        pid_file = os.path.join(self.data_dir, "database.pid")
        with open(pid_file, 'w') as f:
            f.write("11111")
        
        return os.path.exists(db_file) and os.path.exists(pid_file)
    
    def _start_backend_service(self):
        """启动后端API服务"""
        # 检查数据库是否已启动
        db_pid_file = os.path.join(self.data_dir, "database.pid")
        if not os.path.exists(db_pid_file):
            return False
        
        # 创建后端PID文件
        pid_file = os.path.join(self.backend_dir, "backend.pid")
        with open(pid_file, 'w') as f:
            f.write("22222")
        
        # 创建API日志
        log_file = os.path.join(self.logs_dir, "backend_api.log")
        with open(log_file, 'w') as f:
            f.write("Backend API service started successfully")
        
        return os.path.exists(pid_file)
    
    def _start_frontend_service(self):
        """启动前端服务"""
        # 检查后端API是否已启动
        backend_pid_file = os.path.join(self.backend_dir, "backend.pid")
        if not os.path.exists(backend_pid_file):
            return False
        
        # 创建前端PID文件
        pid_file = os.path.join(self.frontend_dir, "frontend.pid")
        with open(pid_file, 'w') as f:
            f.write("33333")
        
        return os.path.exists(pid_file)
    
    def _start_websocket_service(self):
        """启动WebSocket服务"""
        # 检查后端API是否已启动
        backend_pid_file = os.path.join(self.backend_dir, "backend.pid")
        if not os.path.exists(backend_pid_file):
            return False
        
        # 创建WebSocket PID文件
        pid_file = os.path.join(self.backend_dir, "websocket.pid")
        with open(pid_file, 'w') as f:
            f.write("44444")
        
        return os.path.exists(pid_file)
    
    def _start_data_processing_service(self):
        """启动数据处理服务"""
        # 检查数据库是否已启动
        db_pid_file = os.path.join(self.data_dir, "database.pid")
        if not os.path.exists(db_pid_file):
            return False
        
        # 创建数据处理服务PID文件
        pid_file = os.path.join(self.data_dir, "data_processor.pid")
        with open(pid_file, 'w') as f:
            f.write("55555")
        
        return os.path.exists(pid_file)


def run_complete_startup_flow_tests():
    """运行完整启动流程测试套件"""
    logger.info("=" * 60)
    logger.info("🧪 完整启动流程集成测试套件")
    logger.info("=" * 60)
    logger.info("测试开始时间:", time.strftime('%Y-%m-%d %H:%M:%S'))
    logger.info()
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTests(unittest.TestLoader().loadTestsFromTestCase(TestCompleteStartupFlow))
    test_suite.addTests(unittest.TestLoader().loadTestsFromTestCase(TestErrorRecovery))
    test_suite.addTests(unittest.TestLoader().loadTestsFromTestCase(TestServiceDependencies))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    logger.info()
    logger.info("=" * 60)
    logger.info("📊 测试结果统计")
    logger.info("=" * 60)
    logger.info(f"总测试数: {result.testsRun}")
    logger.info(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    logger.info(f"失败: {len(result.failures)}")
    logger.info(f"错误: {len(result.errors)}")
    
    if result.failures:
        logger.info("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            logger.info(f"  - {test}")
            logger.info(f"    {traceback}")
    
    if result.errors:
        logger.info("\n💥 错误的测试:")
        for test, traceback in result.errors:
            logger.info(f"  - {test}")
            logger.info(f"    {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    logger.info(f"\n✅ 测试成功率: {success_rate:.1f}%")
    
    if result.wasSuccessful():
        logger.info("\n🎉 所有完整启动流程集成测试通过！")
        return True
    else:
        logger.info("\n⚠️ 部分测试失败，请检查上述错误信息")
        return False


if __name__ == "__main__":
    success = run_complete_startup_flow_tests()
    sys.exit(0 if success else 1)