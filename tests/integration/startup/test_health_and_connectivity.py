import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
健康检查和连通性测试集成测试
验证健康检查系统和连通性测试的完整功能
"""

import unittest
import subprocess
import tempfile
import os
import json
import time
from unittest.mock import patch, MagicMock


class TestHealthAndConnectivityIntegration(unittest.TestCase):
    """健康检查和连通性测试集成测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../"))
        self.health_checker_script = os.path.join(
            self.project_root, "scripts/startup/modules/health_checker.sh"
        )
        self.connectivity_tester_script = os.path.join(
            self.project_root, "scripts/startup/modules/connectivity_tester.sh"
        )
        
        # 确保脚本存在
        self.assertTrue(os.path.exists(self.health_checker_script))
        self.assertTrue(os.path.exists(self.connectivity_tester_script))
    
    def test_health_checker_loads_connectivity_tester(self):
        """测试健康检查模块是否正确加载连通性测试模块"""
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            health_checker_content = f.read()
        
        # 检查是否包含连通性测试模块的加载
        self.assertIn("connectivity_tester.sh", health_checker_content)
        self.assertIn("连通性测试模块已加载", health_checker_content)
    
    def test_enhanced_health_checks_function_exists(self):
        """测试增强健康检查函数是否存在"""
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            health_checker_content = f.read()
        
        # 检查增强健康检查函数
        self.assertIn("perform_enhanced_health_checks()", health_checker_content)
        self.assertIn("run_comprehensive_connectivity_tests", health_checker_content)
    
    def test_comprehensive_health_report_generation(self):
        """测试综合健康检查报告生成"""
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            health_checker_content = f.read()
        
        # 检查综合报告生成功能
        report_elements = [
            "generate_comprehensive_health_report",
            "量化交易系统综合健康检查报告",
            "基础健康检查",
            "综合连通性测试",
            "数据库连接测试",
            "WebSocket连接测试",
            "API端点测试",
            "服务间通信测试",
            "网络性能测试"
        ]
        
        for element in report_elements:
            self.assertIn(element, health_checker_content)
    
    def test_all_required_functions_exported(self):
        """测试所有必需的函数是否正确导出"""
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            health_checker_content = f.read()
        
        # 检查函数导出
        exported_functions = [
            "perform_health_checks",
            "health_check_backend", 
            "health_check_frontend",
            "test_api_connectivity",
            "test_database_connection",
            "test_websocket_connection",
            "validate_service_communication",
            "perform_enhanced_health_checks"
        ]
        
        for function in exported_functions:
            self.assertIn(f"export -f", health_checker_content)
            self.assertIn(function, health_checker_content)
    
    def test_connectivity_tester_comprehensive_functions(self):
        """测试连通性测试模块的综合功能"""
        with open(self.connectivity_tester_script, 'r', encoding='utf-8') as f:
            connectivity_content = f.read()
        
        # 检查综合测试功能
        comprehensive_functions = [
            "run_comprehensive_connectivity_tests",
            "test_database_connection_comprehensive",
            "test_websocket_connection_comprehensive", 
            "test_all_api_endpoints",
            "test_inter_service_communication",
            "test_network_performance"
        ]
        
        for function in comprehensive_functions:
            self.assertIn(f"{function}()", connectivity_content)
    
    def test_integration_script_loading(self):
        """测试脚本集成加载"""
        # 测试健康检查脚本是否可以正常加载连通性测试模块
        result = subprocess.run([
            'bash', '-c', 
            f'source {self.health_checker_script} && echo "集成加载成功"'
        ], capture_output=True, text=True, cwd=self.project_root)
        
        self.assertEqual(result.returncode, 0, f"脚本集成加载失败: {result.stderr}")
        self.assertIn("集成加载成功", result.stdout)
    
    def test_health_check_task_requirements_coverage(self):
        """测试健康检查任务需求覆盖"""
        # 验证任务6.1的需求是否都已实现
        task_6_1_requirements = [
            "health_check_backend",      # 检查后端API健康状态
            "health_check_frontend",     # 检查前端服务可用性  
            "test_api_connectivity",     # 测试API连通性
            "generate_health_status_report", # 创建健康检查报告和状态展示
            "display_health_status"      # 状态展示
        ]
        
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            health_checker_content = f.read()
        
        for requirement in task_6_1_requirements:
            self.assertIn(requirement, health_checker_content, 
                         f"任务6.1需求 {requirement} 未实现")
    
    def test_connectivity_task_requirements_coverage(self):
        """测试连通性测试任务需求覆盖"""
        # 验证任务6.2的需求是否都已实现
        task_6_2_requirements = [
            "test_database_connection",      # 测试数据库连接
            "test_websocket_connection",     # 测试WebSocket连接
            "validate_service_communication", # 验证服务间通信
            "diagnose_connectivity_issues"   # 连通性问题诊断和修复建议
        ]
        
        # 检查健康检查模块
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            health_checker_content = f.read()
        
        # 检查连通性测试模块
        with open(self.connectivity_tester_script, 'r', encoding='utf-8') as f:
            connectivity_content = f.read()
        
        combined_content = health_checker_content + connectivity_content
        
        for requirement in task_6_2_requirements:
            self.assertIn(requirement, combined_content, 
                         f"任务6.2需求 {requirement} 未实现")
    
    def test_comprehensive_error_handling(self):
        """测试综合错误处理"""
        # 检查错误处理和诊断功能
        error_handling_elements = [
            "diagnose_backend_issues",
            "diagnose_frontend_issues", 
            "provide_communication_troubleshooting",
            "diagnose_connectivity_issues",
            "故障排除建议",
            "修复建议"
        ]
        
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            health_checker_content = f.read()
        
        with open(self.connectivity_tester_script, 'r', encoding='utf-8') as f:
            connectivity_content = f.read()
        
        combined_content = health_checker_content + connectivity_content
        
        for element in error_handling_elements:
            self.assertIn(element, combined_content)
    
    def test_report_generation_completeness(self):
        """测试报告生成完整性"""
        # 检查各种报告生成功能
        report_functions = [
            "generate_health_status_report",
            "generate_comprehensive_health_report",
            "generate_connectivity_test_report"
        ]
        
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            health_checker_content = f.read()
        
        with open(self.connectivity_tester_script, 'r', encoding='utf-8') as f:
            connectivity_content = f.read()
        
        combined_content = health_checker_content + connectivity_content
        
        for report_function in report_functions:
            self.assertIn(report_function, combined_content)
    
    def test_configuration_completeness(self):
        """测试配置完整性"""
        # 检查健康检查配置
        with open(self.health_checker_script, 'r', encoding='utf-8') as f:
            health_checker_content = f.read()
        
        health_config_items = [
            "HEALTH_CHECK_CONFIG",
            "backend_url",
            "frontend_url", 
            "backend_health_endpoint",
            "websocket_endpoint",
            "database_file"
        ]
        
        for config_item in health_config_items:
            self.assertIn(config_item, health_checker_content)
        
        # 检查连通性测试配置
        with open(self.connectivity_tester_script, 'r', encoding='utf-8') as f:
            connectivity_content = f.read()
        
        connectivity_config_items = [
            "CONNECTIVITY_CONFIG",
            "test_timeout",
            "retry_attempts",
            "websocket_test_duration",
            "database_test_queries"
        ]
        
        for config_item in connectivity_config_items:
            self.assertIn(config_item, connectivity_content)


class TestHealthCheckSystemIntegration(unittest.TestCase):
    """健康检查系统集成测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../"))
        self.test_log_dir = os.path.join(self.project_root, "logs")
        os.makedirs(self.test_log_dir, exist_ok=True)
    
    def test_complete_health_check_workflow(self):
        """测试完整的健康检查工作流程"""
        # 模拟完整的健康检查流程
        workflow_steps = [
            "环境检查",
            "服务状态检查", 
            "健康检查执行",
            "连通性测试",
            "报告生成",
            "状态展示"
        ]
        
        # 验证工作流程的每个步骤都有对应的实现
        health_checker_script = os.path.join(
            self.project_root, "scripts/startup/modules/health_checker.sh"
        )
        
        with open(health_checker_script, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查工作流程相关的关键字
        workflow_keywords = [
            "perform_enhanced_health_checks",
            "health_check_backend",
            "health_check_frontend",
            "run_comprehensive_connectivity_tests",
            "generate_comprehensive_health_report",
            "display_health_status"
        ]
        
        for keyword in workflow_keywords:
            self.assertIn(keyword, content)
    
    def test_system_requirements_mapping(self):
        """测试系统需求映射"""
        # 验证需求6.1, 6.2, 6.3, 6.4, 6.5的实现
        requirements_mapping = {
            "6.1": ["health_check_backend", "health_check_frontend"],
            "6.2": ["test_api_connectivity", "generate_health_status_report"],
            "6.3": ["display_health_status", "健康状态面板"],
            "6.4": ["test_database_connection", "test_websocket_connection"],
            "6.5": ["validate_service_communication", "diagnose_connectivity_issues"]
        }
        
        health_checker_script = os.path.join(
            self.project_root, "scripts/startup/modules/health_checker.sh"
        )
        connectivity_tester_script = os.path.join(
            self.project_root, "scripts/startup/modules/connectivity_tester.sh"
        )
        
        with open(health_checker_script, 'r', encoding='utf-8') as f:
            health_content = f.read()
        
        with open(connectivity_tester_script, 'r', encoding='utf-8') as f:
            connectivity_content = f.read()
        
        combined_content = health_content + connectivity_content
        
        for requirement, implementations in requirements_mapping.items():
            for implementation in implementations:
                self.assertIn(implementation, combined_content, 
                             f"需求 {requirement} 的实现 {implementation} 未找到")


def run_health_and_connectivity_integration_tests():
    """运行健康检查和连通性测试集成测试套件"""
    logger.info("🧪 开始运行健康检查和连通性测试集成测试...")
    
    # 创建测试套件
    loader = unittest.TestLoader()
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTests(loader.loadTestsFromTestCase(TestHealthAndConnectivityIntegration))
    test_suite.addTests(loader.loadTestsFromTestCase(TestHealthCheckSystemIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        logger.info("✅ 所有健康检查和连通性测试集成测试通过！")
        return True
    else:
        logger.info("❌ 部分健康检查和连通性测试集成测试失败")
        logger.info(f"失败数量: {len(result.failures)}")
        logger.info(f"错误数量: {len(result.errors)}")
        return False


if __name__ == "__main__":
    success = run_health_and_connectivity_integration_tests()
    exit(0 if success else 1)