import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户验收测试模块

此模块实现用户验收测试功能，包括首次安装体验模拟、常见场景测试、
性能基准测试和用户体验评估机制。

作者: 系统启动修复团队
创建时间: 2025-01-07
"""

import os
import sys
import time
import json
import shutil
import subprocess
import tempfile
import threading
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import psutil
import requests
from dataclasses import dataclass, asdict


@dataclass
class TestResult:
    """测试结果数据类"""
    test_name: str
    success: bool
    duration: float
    error_message: Optional[str] = None
    performance_metrics: Optional[Dict] = None
    user_experience_score: Optional[float] = None


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    startup_time: float
    memory_usage: float
    cpu_usage: float
    disk_io: float
    network_requests: int
    error_count: int


class UserAcceptanceTestSuite:
    """用户验收测试套件"""
    
    def __init__(self, project_root: str = None):
        """
        初始化测试套件
        
        Args:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.test_results: List[TestResult] = []
        self.temp_dir = None
        self.original_env = dict(os.environ)
        
        # 测试配置
        self.config = {
            'timeout': 300,  # 5分钟超时
            'max_memory_mb': 1024,  # 最大内存使用1GB
            'max_cpu_percent': 80,  # 最大CPU使用80%
            'expected_startup_time': 60,  # 期望启动时间60秒
            'health_check_retries': 30,  # 健康检查重试次数
            'performance_samples': 10  # 性能采样次数
        }
        
        logger.info("🧪 用户验收测试套件初始化完成")
    
    def simulate_first_time_setup(self) -> TestResult:
        """
        模拟首次安装体验
        
        此函数模拟用户首次安装和启动系统的完整体验，
        包括环境检查、依赖安装、配置生成和服务启动。
        
        Returns:
            TestResult: 测试结果
        """
        logger.info("\n🚀 开始模拟首次安装体验...")
        start_time = time.time()
        
        try:
            # 创建临时测试环境
            self.temp_dir = tempfile.mkdtemp(prefix="startup_test_")
            test_project_dir = Path(self.temp_dir) / "test_project"
            
            logger.info(f"📁 创建测试环境: {test_project_dir}")
            
            # 复制项目文件到测试环境
            shutil.copytree(
                self.project_root,
                test_project_dir,
                ignore=shutil.ignore_patterns(
                    '.git', '__pycache__', '*.pyc', 'node_modules',
                    '.venv', 'venv', 'logs', 'data'
                )
            )
            
            # 模拟首次启动环境（删除可能存在的环境文件）
            self._clean_environment(test_project_dir)
            
            # 执行首次启动流程
            startup_result = self._execute_first_startup(test_project_dir)
            
            # 验证启动结果
            validation_result = self._validate_first_startup(test_project_dir)
            
            # 计算用户体验评分
            ux_score = self._calculate_ux_score(startup_result, validation_result)
            
            duration = time.time() - start_time
            
            result = TestResult(
                test_name="首次安装体验模拟",
                success=startup_result['success'] and validation_result['success'],
                duration=duration,
                performance_metrics=startup_result.get('metrics'),
                user_experience_score=ux_score
            )
            
            if not result.success:
                result.error_message = f"启动失败: {startup_result.get('error', '未知错误')}"
            
            logger.info(f"✅ 首次安装体验测试完成 - 耗时: {duration:.2f}秒")
            logger.info(f"📊 用户体验评分: {ux_score:.1f}/10.0")
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            logger.info(f"❌ 首次安装体验测试失败: {str(e)}")
            
            return TestResult(
                test_name="首次安装体验模拟",
                success=False,
                duration=duration,
                error_message=str(e)
            )
        
        finally:
            # 清理测试环境
            if self.temp_dir and os.path.exists(self.temp_dir):
                try:
                    shutil.rmtree(self.temp_dir)
                except Exception as e:
                    logger.info(f"⚠️ 清理测试环境失败: {e}")
    
    def test_common_scenarios(self) -> List[TestResult]:
        """
        测试常见使用场景
        
        测试各种常见的用户使用场景，包括正常启动、重启、
        错误恢复、配置更新等。
        
        Returns:
            List[TestResult]: 测试结果列表
        """
        logger.info("\n🔄 开始测试常见使用场景...")
        
        scenarios = [
            ("正常启动场景", self._test_normal_startup),
            ("重启场景", self._test_restart_scenario),
            ("端口冲突恢复", self._test_port_conflict_recovery),
            ("依赖缺失恢复", self._test_missing_dependency_recovery),
            ("配置文件损坏恢复", self._test_config_corruption_recovery),
            ("虚拟环境重建", self._test_venv_rebuild),
            ("服务异常重启", self._test_service_crash_recovery),
            ("网络连接问题", self._test_network_issues),
            ("磁盘空间不足", self._test_disk_space_issues),
            ("权限问题处理", self._test_permission_issues)
        ]
        
        results = []
        
        for scenario_name, test_func in scenarios:
            logger.info(f"\n🧪 测试场景: {scenario_name}")
            
            try:
                result = test_func()
                result.test_name = scenario_name
                results.append(result)
                
                status = "✅ 通过" if result.success else "❌ 失败"
                logger.info(f"{status} - 耗时: {result.duration:.2f}秒")
                
                if not result.success and result.error_message:
                    logger.info(f"   错误: {result.error_message}")
                    
            except Exception as e:
                logger.info(f"❌ 场景测试异常: {str(e)}")
                results.append(TestResult(
                    test_name=scenario_name,
                    success=False,
                    duration=0,
                    error_message=str(e)
                ))
        
        success_count = sum(1 for r in results if r.success)
        logger.info(f"\n📊 常见场景测试完成: {success_count}/{len(results)} 通过")
        
        return results
    
    def performance_benchmark(self) -> TestResult:
        """
        性能基准测试
        
        测试系统启动和运行的性能指标，包括启动时间、
        内存使用、CPU使用率等关键性能指标。
        
        Returns:
            TestResult: 性能测试结果
        """
        logger.info("\n⚡ 开始性能基准测试...")
        start_time = time.time()
        
        try:
            # 执行多次性能测试取平均值
            performance_samples = []
            
            for i in range(self.config['performance_samples']):
                logger.info(f"📊 执行第 {i+1}/{self.config['performance_samples']} 次性能采样...")
                
                sample = self._run_performance_sample()
                performance_samples.append(sample)
                
                # 等待系统稳定
                time.sleep(2)
            
            # 计算平均性能指标
            avg_metrics = self._calculate_average_metrics(performance_samples)
            
            # 评估性能是否达标
            performance_score = self._evaluate_performance(avg_metrics)
            
            duration = time.time() - start_time
            
            result = TestResult(
                test_name="性能基准测试",
                success=performance_score >= 7.0,  # 7分以上算通过
                duration=duration,
                performance_metrics=asdict(avg_metrics),
                user_experience_score=performance_score
            )
            
            logger.info(f"✅ 性能基准测试完成 - 耗时: {duration:.2f}秒")
            logger.info(f"📊 性能评分: {performance_score:.1f}/10.0")
            self._print_performance_summary(avg_metrics)
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            logger.info(f"❌ 性能基准测试失败: {str(e)}")
            
            return TestResult(
                test_name="性能基准测试",
                success=False,
                duration=duration,
                error_message=str(e)
            )
    
    def create_user_experience_report(self, results: List[TestResult]) -> Dict:
        """
        创建用户体验评估和反馈机制
        
        基于测试结果生成详细的用户体验报告，包括问题分析、
        改进建议和用户反馈收集机制。
        
        Args:
            results: 所有测试结果
            
        Returns:
            Dict: 用户体验报告
        """
        logger.info("\n📋 生成用户体验评估报告...")
        
        # 计算总体统计
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.success)
        total_duration = sum(r.duration for r in results)
        avg_ux_score = sum(r.user_experience_score or 0 for r in results) / total_tests
        
        # 分析失败原因
        failed_tests = [r for r in results if not r.success]
        failure_analysis = self._analyze_failures(failed_tests)
        
        # 生成改进建议
        improvement_suggestions = self._generate_improvement_suggestions(results)
        
        # 创建报告
        report = {
            'report_info': {
                'generated_at': datetime.now().isoformat(),
                'test_environment': {
                    'python_version': sys.version,
                    'platform': sys.platform,
                    'project_root': str(self.project_root)
                }
            },
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': total_tests - passed_tests,
                'success_rate': (passed_tests / total_tests) * 100,
                'total_duration': total_duration,
                'average_ux_score': avg_ux_score
            },
            'test_results': [asdict(r) for r in results],
            'failure_analysis': failure_analysis,
            'improvement_suggestions': improvement_suggestions,
            'user_feedback': {
                'feedback_url': 'https://github.com/your-repo/issues',
                'contact_email': '<EMAIL>',
                'feedback_form': self._create_feedback_form()
            }
        }
        
        # 保存报告到文件
        report_file = self.project_root / 'tests' / 'reports' / 'user_acceptance_report.json'
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 用户体验报告已保存到: {report_file}")
        
        # 打印报告摘要
        self._print_report_summary(report)
        
        return report
    
    def _clean_environment(self, project_dir: Path):
        """清理测试环境，模拟首次安装"""
        logger.info("🧹 清理测试环境...")
        
        # 删除虚拟环境
        venv_paths = [
            project_dir / 'web_ui' / 'backend' / 'venv',
            project_dir / '.venv',
            project_dir / 'venv'
        ]
        
        for venv_path in venv_paths:
            if venv_path.exists():
                shutil.rmtree(venv_path)
        
        # 删除配置文件
        config_files = [
            project_dir / 'web_ui' / '.env',
            project_dir / 'web_ui' / 'backend' / '.env',
            project_dir / 'web_ui' / 'frontend' / '.env'
        ]
        
        for config_file in config_files:
            if config_file.exists():
                config_file.unlink()
        
        # 删除node_modules
        node_modules = project_dir / 'web_ui' / 'frontend' / 'node_modules'
        if node_modules.exists():
            shutil.rmtree(node_modules)
        
        # 删除日志文件
        logs_dir = project_dir / 'logs'
        if logs_dir.exists():
            shutil.rmtree(logs_dir)
        
        logger.info("✅ 测试环境清理完成")
    
    def _execute_first_startup(self, project_dir: Path) -> Dict:
        """执行首次启动流程"""
        logger.info("🚀 执行首次启动流程...")
        
        startup_script = project_dir / 'start_system.sh'
        if not startup_script.exists():
            return {
                'success': False,
                'error': '启动脚本不存在'
            }
        
        # 记录启动前的系统状态
        start_time = time.time()
        initial_memory = psutil.virtual_memory().used
        
        try:
            # 执行启动脚本
            process = subprocess.Popen(
                ['bash', str(startup_script)],
                cwd=project_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 监控启动过程
            stdout, stderr = process.communicate(timeout=self.config['timeout'])
            
            startup_time = time.time() - start_time
            final_memory = psutil.virtual_memory().used
            memory_usage = (final_memory - initial_memory) / 1024 / 1024  # MB
            
            success = process.returncode == 0
            
            return {
                'success': success,
                'stdout': stdout,
                'stderr': stderr,
                'return_code': process.returncode,
                'metrics': {
                    'startup_time': startup_time,
                    'memory_usage': memory_usage
                }
            }
            
        except subprocess.TimeoutExpired:
            process.kill()
            return {
                'success': False,
                'error': f'启动超时（超过{self.config["timeout"]}秒）'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _validate_first_startup(self, project_dir: Path) -> Dict:
        """验证首次启动结果"""
        logger.info("🔍 验证启动结果...")
        
        validation_results = {
            'success': True,
            'checks': {}
        }
        
        # 检查虚拟环境
        venv_path = project_dir / 'web_ui' / 'backend' / 'venv'
        validation_results['checks']['virtual_environment'] = venv_path.exists()
        
        # 检查配置文件
        config_files = [
            project_dir / 'web_ui' / '.env',
            project_dir / 'web_ui' / 'backend' / '.env'
        ]
        
        validation_results['checks']['config_files'] = all(
            f.exists() for f in config_files
        )
        
        # 检查服务健康状态
        health_checks = {
            'backend': self._check_service_health('http://localhost:8000/health'),
            'frontend': self._check_service_health('http://localhost:3000')
        }
        
        validation_results['checks']['services'] = health_checks
        validation_results['checks']['all_services_healthy'] = all(health_checks.values())
        
        # 总体验证结果
        validation_results['success'] = all([
            validation_results['checks']['virtual_environment'],
            validation_results['checks']['config_files'],
            validation_results['checks']['all_services_healthy']
        ])
        
        return validation_results
    
    def _calculate_ux_score(self, startup_result: Dict, validation_result: Dict) -> float:
        """计算用户体验评分"""
        score = 0.0
        
        # 启动成功 (3分)
        if startup_result.get('success', False):
            score += 3.0
        
        # 启动时间 (2分)
        startup_time = startup_result.get('metrics', {}).get('startup_time', float('inf'))
        if startup_time <= 30:
            score += 2.0
        elif startup_time <= 60:
            score += 1.5
        elif startup_time <= 120:
            score += 1.0
        
        # 验证通过 (3分)
        if validation_result.get('success', False):
            score += 3.0
        
        # 内存使用合理 (1分)
        memory_usage = startup_result.get('metrics', {}).get('memory_usage', float('inf'))
        if memory_usage <= self.config['max_memory_mb']:
            score += 1.0
        
        # 无错误输出 (1分)
        if not startup_result.get('stderr'):
            score += 1.0
        
        return min(score, 10.0)
    
    def _check_service_health(self, url: str, timeout: int = 5) -> bool:
        """检查服务健康状态"""
        try:
            response = requests.get(url, timeout=timeout)
            return response.status_code == 200
        except:
            return False
    
    def _test_normal_startup(self) -> TestResult:
        """测试正常启动场景"""
        start_time = time.time()
        
        try:
            # 模拟正常启动
            result = subprocess.run(
                ['bash', 'start_system.sh'],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=120
            )
            
            success = result.returncode == 0
            duration = time.time() - start_time
            
            return TestResult(
                test_name="正常启动场景",
                success=success,
                duration=duration,
                error_message=result.stderr if not success else None
            )
            
        except Exception as e:
            return TestResult(
                test_name="正常启动场景",
                success=False,
                duration=time.time() - start_time,
                error_message=str(e)
            )
    
    def _test_restart_scenario(self) -> TestResult:
        """测试重启场景"""
        start_time = time.time()
        
        try:
            # 先启动系统
            subprocess.run(['bash', 'start_system.sh'], cwd=self.project_root, timeout=60)
            
            # 停止系统
            subprocess.run(['pkill', '-f', 'uvicorn'], timeout=10)
            subprocess.run(['pkill', '-f', 'vite'], timeout=10)
            
            time.sleep(2)
            
            # 重新启动
            result = subprocess.run(
                ['bash', 'start_system.sh'],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=120
            )
            
            success = result.returncode == 0
            duration = time.time() - start_time
            
            return TestResult(
                test_name="重启场景",
                success=success,
                duration=duration,
                error_message=result.stderr if not success else None
            )
            
        except Exception as e:
            return TestResult(
                test_name="重启场景",
                success=False,
                duration=time.time() - start_time,
                error_message=str(e)
            )
    
    def _test_port_conflict_recovery(self) -> TestResult:
        """测试端口冲突恢复"""
        start_time = time.time()
        
        try:
            # 占用8000端口
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.bind(('localhost', 8000))
            sock.listen(1)
            
            try:
                # 尝试启动系统
                result = subprocess.run(
                    ['bash', 'start_system.sh'],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=120
                )
                
                # 检查是否有端口冲突处理
                success = "端口" in result.stdout or "port" in result.stdout.lower()
                
            finally:
                sock.close()
            
            duration = time.time() - start_time
            
            return TestResult(
                test_name="端口冲突恢复",
                success=success,
                duration=duration
            )
            
        except Exception as e:
            return TestResult(
                test_name="端口冲突恢复",
                success=False,
                duration=time.time() - start_time,
                error_message=str(e)
            )
    
    def _test_missing_dependency_recovery(self) -> TestResult:
        """测试依赖缺失恢复"""
        start_time = time.time()
        
        try:
            # 临时移除requirements.txt
            req_file = self.project_root / 'web_ui' / 'backend' / 'requirements.txt'
            backup_file = req_file.with_suffix('.txt.backup')
            
            if req_file.exists():
                shutil.move(req_file, backup_file)
            
            try:
                result = subprocess.run(
                    ['bash', 'start_system.sh'],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=120
                )
                
                # 检查是否有依赖处理逻辑
                success = "依赖" in result.stdout or "requirements" in result.stdout.lower()
                
            finally:
                # 恢复文件
                if backup_file.exists():
                    shutil.move(backup_file, req_file)
            
            duration = time.time() - start_time
            
            return TestResult(
                test_name="依赖缺失恢复",
                success=success,
                duration=duration
            )
            
        except Exception as e:
            return TestResult(
                test_name="依赖缺失恢复",
                success=False,
                duration=time.time() - start_time,
                error_message=str(e)
            )
    
    def _test_config_corruption_recovery(self) -> TestResult:
        """测试配置文件损坏恢复"""
        start_time = time.time()
        
        try:
            # 创建损坏的配置文件
            config_file = self.project_root / 'web_ui' / 'backend' / '.env'
            backup_content = ""
            
            if config_file.exists():
                backup_content = config_file.read_text()
            
            # 写入损坏的配置
            config_file.write_text("INVALID_CONFIG_LINE_WITHOUT_EQUALS")
            
            try:
                result = subprocess.run(
                    ['bash', 'start_system.sh'],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=120
                )
                
                # 检查是否有配置修复逻辑
                success = "配置" in result.stdout or "config" in result.stdout.lower()
                
            finally:
                # 恢复配置
                if backup_content:
                    config_file.write_text(backup_content)
                elif config_file.exists():
                    config_file.unlink()
            
            duration = time.time() - start_time
            
            return TestResult(
                test_name="配置文件损坏恢复",
                success=success,
                duration=duration
            )
            
        except Exception as e:
            return TestResult(
                test_name="配置文件损坏恢复",
                success=False,
                duration=time.time() - start_time,
                error_message=str(e)
            )
    
    def _test_venv_rebuild(self) -> TestResult:
        """测试虚拟环境重建"""
        start_time = time.time()
        
        try:
            # 删除虚拟环境
            venv_path = self.project_root / 'web_ui' / 'backend' / 'venv'
            if venv_path.exists():
                shutil.rmtree(venv_path)
            
            result = subprocess.run(
                ['bash', 'start_system.sh'],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=180
            )
            
            # 检查虚拟环境是否重建
            success = venv_path.exists() and result.returncode == 0
            duration = time.time() - start_time
            
            return TestResult(
                test_name="虚拟环境重建",
                success=success,
                duration=duration,
                error_message=result.stderr if not success else None
            )
            
        except Exception as e:
            return TestResult(
                test_name="虚拟环境重建",
                success=False,
                duration=time.time() - start_time,
                error_message=str(e)
            )
    
    def _test_service_crash_recovery(self) -> TestResult:
        """测试服务异常重启"""
        start_time = time.time()
        
        try:
            # 先启动系统
            subprocess.run(['bash', 'start_system.sh'], cwd=self.project_root, timeout=60)
            
            # 强制杀死后端进程
            subprocess.run(['pkill', '-9', '-f', 'uvicorn'], timeout=10)
            
            time.sleep(2)
            
            # 检查是否有自动重启机制
            result = subprocess.run(
                ['bash', 'start_system.sh'],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=120
            )
            
            success = result.returncode == 0
            duration = time.time() - start_time
            
            return TestResult(
                test_name="服务异常重启",
                success=success,
                duration=duration
            )
            
        except Exception as e:
            return TestResult(
                test_name="服务异常重启",
                success=False,
                duration=time.time() - start_time,
                error_message=str(e)
            )
    
    def _test_network_issues(self) -> TestResult:
        """测试网络连接问题"""
        start_time = time.time()
        
        try:
            # 模拟网络问题（通过环境变量）
            env = os.environ.copy()
            env['HTTP_PROXY'] = 'http://invalid-proxy:8080'
            env['HTTPS_PROXY'] = 'http://invalid-proxy:8080'
            
            result = subprocess.run(
                ['bash', 'start_system.sh'],
                cwd=self.project_root,
                env=env,
                capture_output=True,
                text=True,
                timeout=120
            )
            
            # 检查是否有网络问题处理
            success = "网络" in result.stdout or "network" in result.stdout.lower()
            duration = time.time() - start_time
            
            return TestResult(
                test_name="网络连接问题",
                success=success,
                duration=duration
            )
            
        except Exception as e:
            return TestResult(
                test_name="网络连接问题",
                success=False,
                duration=time.time() - start_time,
                error_message=str(e)
            )
    
    def _test_disk_space_issues(self) -> TestResult:
        """测试磁盘空间不足"""
        start_time = time.time()
        
        try:
            # 检查当前磁盘使用情况
            disk_usage = psutil.disk_usage('/')
            free_space_gb = disk_usage.free / (1024**3)
            
            # 如果空间充足，模拟空间不足的情况
            if free_space_gb > 1:  # 大于1GB
                # 创建大文件占用空间（仅用于测试）
                large_file = self.project_root / 'temp_large_file.tmp'
                
                try:
                    # 创建一个相对较小的文件用于测试
                    with open(large_file, 'wb') as f:
                        f.write(b'0' * (100 * 1024 * 1024))  # 100MB
                    
                    result = subprocess.run(
                        ['bash', 'start_system.sh'],
                        cwd=self.project_root,
                        capture_output=True,
                        text=True,
                        timeout=120
                    )
                    
                    success = result.returncode == 0
                    
                finally:
                    # 清理大文件
                    if large_file.exists():
                        large_file.unlink()
            else:
                # 实际空间不足的情况
                result = subprocess.run(
                    ['bash', 'start_system.sh'],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=120
                )
                
                success = "空间" in result.stdout or "space" in result.stdout.lower()
            
            duration = time.time() - start_time
            
            return TestResult(
                test_name="磁盘空间不足",
                success=success,
                duration=duration
            )
            
        except Exception as e:
            return TestResult(
                test_name="磁盘空间不足",
                success=False,
                duration=time.time() - start_time,
                error_message=str(e)
            )
    
    def _test_permission_issues(self) -> TestResult:
        """测试权限问题处理"""
        start_time = time.time()
        
        try:
            # 创建权限受限的目录
            restricted_dir = self.project_root / 'temp_restricted'
            restricted_dir.mkdir(exist_ok=True)
            
            try:
                # 移除写权限
                os.chmod(restricted_dir, 0o444)
                
                result = subprocess.run(
                    ['bash', 'start_system.sh'],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=120
                )
                
                # 检查是否有权限问题处理
                success = "权限" in result.stdout or "permission" in result.stdout.lower()
                
            finally:
                # 恢复权限并清理
                os.chmod(restricted_dir, 0o755)
                restricted_dir.rmdir()
            
            duration = time.time() - start_time
            
            return TestResult(
                test_name="权限问题处理",
                success=success,
                duration=duration
            )
            
        except Exception as e:
            return TestResult(
                test_name="权限问题处理",
                success=False,
                duration=time.time() - start_time,
                error_message=str(e)
            )
    
    def _run_performance_sample(self) -> PerformanceMetrics:
        """运行单次性能采样"""
        # 记录初始状态
        initial_memory = psutil.virtual_memory().used
        initial_time = time.time()
        
        # 启动系统
        process = subprocess.Popen(
            ['bash', 'start_system.sh'],
            cwd=self.project_root,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 监控性能指标
        max_memory = initial_memory
        max_cpu = 0
        network_requests = 0
        error_count = 0
        
        try:
            # 等待启动完成或超时
            process.wait(timeout=self.config['expected_startup_time'])
            
            startup_time = time.time() - initial_time
            final_memory = psutil.virtual_memory().used
            memory_usage = (final_memory - initial_memory) / 1024 / 1024  # MB
            
            # 获取CPU使用率
            cpu_usage = psutil.cpu_percent(interval=1)
            
            # 检查错误
            if process.returncode != 0:
                error_count += 1
            
            return PerformanceMetrics(
                startup_time=startup_time,
                memory_usage=memory_usage,
                cpu_usage=cpu_usage,
                disk_io=0,  # 简化实现
                network_requests=network_requests,
                error_count=error_count
            )
            
        except subprocess.TimeoutExpired:
            process.kill()
            return PerformanceMetrics(
                startup_time=self.config['expected_startup_time'],
                memory_usage=1000,  # 超时时的惩罚值
                cpu_usage=100,
                disk_io=0,
                network_requests=0,
                error_count=1
            )
    
    def _calculate_average_metrics(self, samples: List[PerformanceMetrics]) -> PerformanceMetrics:
        """计算平均性能指标"""
        if not samples:
            return PerformanceMetrics(0, 0, 0, 0, 0, 0)
        
        return PerformanceMetrics(
            startup_time=sum(s.startup_time for s in samples) / len(samples),
            memory_usage=sum(s.memory_usage for s in samples) / len(samples),
            cpu_usage=sum(s.cpu_usage for s in samples) / len(samples),
            disk_io=sum(s.disk_io for s in samples) / len(samples),
            network_requests=sum(s.network_requests for s in samples) / len(samples),
            error_count=sum(s.error_count for s in samples) / len(samples)
        )
    
    def _evaluate_performance(self, metrics: PerformanceMetrics) -> float:
        """评估性能得分"""
        score = 0.0
        
        # 启动时间评分 (3分)
        if metrics.startup_time <= 30:
            score += 3.0
        elif metrics.startup_time <= 60:
            score += 2.0
        elif metrics.startup_time <= 120:
            score += 1.0
        
        # 内存使用评分 (2分)
        if metrics.memory_usage <= 256:
            score += 2.0
        elif metrics.memory_usage <= 512:
            score += 1.5
        elif metrics.memory_usage <= 1024:
            score += 1.0
        
        # CPU使用评分 (2分)
        if metrics.cpu_usage <= 20:
            score += 2.0
        elif metrics.cpu_usage <= 50:
            score += 1.5
        elif metrics.cpu_usage <= 80:
            score += 1.0
        
        # 错误率评分 (2分)
        if metrics.error_count == 0:
            score += 2.0
        elif metrics.error_count <= 1:
            score += 1.0
        
        # 稳定性评分 (1分)
        if metrics.startup_time < self.config['expected_startup_time']:
            score += 1.0
        
        return min(score, 10.0)
    
    def _print_performance_summary(self, metrics: PerformanceMetrics):
        """打印性能摘要"""
        logger.info(f"📊 性能指标摘要:")
        logger.info(f"   启动时间: {metrics.startup_time:.2f}秒")
        logger.info(f"   内存使用: {metrics.memory_usage:.1f}MB")
        logger.info(f"   CPU使用率: {metrics.cpu_usage:.1f}%")
        logger.info(f"   错误次数: {metrics.error_count}")
    
    def _analyze_failures(self, failed_tests: List[TestResult]) -> Dict:
        """分析失败原因"""
        if not failed_tests:
            return {'total_failures': 0, 'failure_categories': {}}
        
        categories = {}
        
        for test in failed_tests:
            if test.error_message:
                if 'timeout' in test.error_message.lower():
                    categories.setdefault('超时问题', []).append(test.test_name)
                elif 'permission' in test.error_message.lower():
                    categories.setdefault('权限问题', []).append(test.test_name)
                elif 'network' in test.error_message.lower():
                    categories.setdefault('网络问题', []).append(test.test_name)
                elif 'config' in test.error_message.lower():
                    categories.setdefault('配置问题', []).append(test.test_name)
                else:
                    categories.setdefault('其他问题', []).append(test.test_name)
            else:
                categories.setdefault('未知问题', []).append(test.test_name)
        
        return {
            'total_failures': len(failed_tests),
            'failure_categories': categories
        }
    
    def _generate_improvement_suggestions(self, results: List[TestResult]) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        # 分析性能问题
        performance_results = [r for r in results if r.performance_metrics]
        if performance_results:
            avg_startup_time = sum(
                r.performance_metrics.get('startup_time', 0) 
                for r in performance_results
            ) / len(performance_results)
            
            if avg_startup_time > 60:
                suggestions.append("建议优化启动流程，减少启动时间")
            
            avg_memory = sum(
                r.performance_metrics.get('memory_usage', 0) 
                for r in performance_results
            ) / len(performance_results)
            
            if avg_memory > 512:
                suggestions.append("建议优化内存使用，减少资源消耗")
        
        # 分析失败率
        failed_count = sum(1 for r in results if not r.success)
        if failed_count > len(results) * 0.2:  # 失败率超过20%
            suggestions.append("建议加强错误处理和恢复机制")
        
        # 分析用户体验
        ux_scores = [r.user_experience_score for r in results if r.user_experience_score]
        if ux_scores:
            avg_ux_score = sum(ux_scores) / len(ux_scores)
            if avg_ux_score < 7.0:
                suggestions.append("建议改善用户体验，提供更清晰的反馈信息")
        
        if not suggestions:
            suggestions.append("系统表现良好，继续保持当前的质量水平")
        
        return suggestions
    
    def _create_feedback_form(self) -> Dict:
        """创建用户反馈表单"""
        return {
            'questions': [
                {
                    'id': 'startup_experience',
                    'question': '您对系统启动体验的整体评价如何？',
                    'type': 'rating',
                    'scale': '1-10'
                },
                {
                    'id': 'ease_of_use',
                    'question': '系统的易用性如何？',
                    'type': 'rating',
                    'scale': '1-10'
                },
                {
                    'id': 'error_handling',
                    'question': '当遇到问题时，错误信息是否清晰有用？',
                    'type': 'rating',
                    'scale': '1-10'
                },
                {
                    'id': 'performance',
                    'question': '系统性能是否满足您的需求？',
                    'type': 'rating',
                    'scale': '1-10'
                },
                {
                    'id': 'suggestions',
                    'question': '您有什么改进建议？',
                    'type': 'text'
                }
            ],
            'submission_url': '/api/feedback/submit',
            'thank_you_message': '感谢您的反馈，这将帮助我们改进系统！'
        }
    
    def _print_report_summary(self, report: Dict):
        """打印报告摘要"""
        summary = report['summary']
        
        logger.info(f"\n📋 用户验收测试报告摘要")
        logger.info(f"=" * 50)
        logger.info(f"总测试数: {summary['total_tests']}")
        logger.info(f"通过测试: {summary['passed_tests']}")
        logger.info(f"失败测试: {summary['failed_tests']}")
        logger.info(f"成功率: {summary['success_rate']:.1f}%")
        logger.info(f"总耗时: {summary['total_duration']:.2f}秒")
        logger.info(f"平均用户体验评分: {summary['average_ux_score']:.1f}/10.0")
        
        if report['failure_analysis']['total_failures'] > 0:
            logger.info(f"\n❌ 失败分析:")
            for category, tests in report['failure_analysis']['failure_categories'].items():
                logger.info(f"   {category}: {len(tests)}个测试")
        
        logger.info(f"\n💡 改进建议:")
        for i, suggestion in enumerate(report['improvement_suggestions'], 1):
            logger.info(f"   {i}. {suggestion}")


def main():
    """主函数 - 运行用户验收测试套件"""
    logger.info("🧪 启动用户验收测试套件")
    logger.info("=" * 60)
    
    # 创建测试套件
    test_suite = UserAcceptanceTestSuite()
    
    all_results = []
    
    try:
        # 1. 首次安装体验测试
        logger.info("\n🚀 第一阶段: 首次安装体验测试")
        first_time_result = test_suite.simulate_first_time_setup()
        all_results.append(first_time_result)
        
        # 2. 常见场景测试
        logger.info("\n🔄 第二阶段: 常见使用场景测试")
        scenario_results = test_suite.test_common_scenarios()
        all_results.extend(scenario_results)
        
        # 3. 性能基准测试
        logger.info("\n⚡ 第三阶段: 性能基准测试")
        performance_result = test_suite.performance_benchmark()
        all_results.append(performance_result)
        
        # 4. 生成用户体验报告
        logger.info("\n📋 第四阶段: 生成用户体验报告")
        report = test_suite.create_user_experience_report(all_results)
        
        # 输出最终结果
        success_count = sum(1 for r in all_results if r.success)
        total_count = len(all_results)
        
        logger.info(f"\n🎉 用户验收测试完成!")
        logger.info(f"总体结果: {success_count}/{total_count} 测试通过")
        logger.info(f"成功率: {(success_count/total_count)*100:.1f}%")
        
        if success_count == total_count:
            logger.info("✅ 所有测试通过，系统已准备好投入使用！")
            return 0
        else:
            logger.info("⚠️ 部分测试失败，请查看详细报告进行改进")
            return 1
            
    except KeyboardInterrupt:
        logger.info("\n⏹️ 测试被用户中断")
        return 1
    except Exception as e:
        logger.info(f"\n❌ 测试套件执行失败: {str(e)}")
        return 1


if __name__ == "__main__":
    exit(main())