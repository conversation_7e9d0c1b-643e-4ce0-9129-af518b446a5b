import logging
logger = logging.getLogger(__name__)
"""
系统启动和关闭流程集成测试

测试完整的系统启动和关闭流程
"""

import pytest
import asyncio
import time
import os
import signal
import subprocess
import tempfile
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import threading
import json

from src.core.startup_manager import StartupManager
from src.core.shutdown_handler import ShutdownHandler
from src.core.dependency_resolver import DependencyResolver
from src.core.enhanced_health_checker import HealthChecker
from src.monitoring.monitoring_optimizer import MonitoringOptimizer
from src.error_handling.enhanced_error_handler import EnhancedErrorHandler


class TestSystemStartupShutdownIntegration:
    """系统启动关闭集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.startup_manager = None
        self.shutdown_handler = None
        self.monitoring_optimizer = None
        self.error_handler = None
        
        # 模拟系统组件
        self.mock_components = {
            'database': Mock(),
            'api_server': <PERSON>ck(),
            'monitoring': Mock(),
            'data_sources': <PERSON>ck()
        }
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_complete_startup_sequence(self):
        """测试完整的启动序列"""
        logger.info("测试系统启动序列...")
        
        # 创建启动管理器
        self.startup_manager = StartupManager()
        
        # 模拟依赖解析
        with patch.object(self.startup_manager, 'dependency_resolver') as mock_resolver:
            mock_resolver.resolve_dependencies.return_value = [
                'database', 'monitoring', 'api_server', 'data_sources'
            ]
            
            # 模拟健康检查
            with patch.object(self.startup_manager, 'health_checker') as mock_health:
                mock_health.perform_health_checks.return_value = {
                    'overall_status': 'healthy',
                    'component_status': {
                        'database': 'healthy',
                        'monitoring': 'healthy',
                        'api_server': 'healthy',
                        'data_sources': 'healthy'
                    }
                }
                
                # 执行启动
                startup_result = await self.startup_manager.startup_with_dependencies()
                
                # 验证启动结果
                assert startup_result is True
                mock_resolver.resolve_dependencies.assert_called_once()
                mock_health.perform_health_checks.assert_called()
    
    @pytest.mark.asyncio
    async def test_startup_with_dependency_failure(self):
        """测试依赖失败时的启动处理"""
        logger.info("测试依赖失败的启动处理...")
        
        self.startup_manager = StartupManager()
        
        # 模拟依赖解析失败
        with patch.object(self.startup_manager, 'dependency_resolver') as mock_resolver:
            mock_resolver.resolve_dependencies.side_effect = Exception("Dependency resolution failed")
            
            # 执行启动应该失败
            startup_result = await self.startup_manager.startup_with_dependencies()
            
            # 验证启动失败
            assert startup_result is False
    
    @pytest.mark.asyncio
    async def test_graceful_shutdown_sequence(self):
        """测试优雅关闭序列"""
        logger.info("测试系统优雅关闭...")
        
        # 创建关闭处理器
        self.shutdown_handler = ShutdownHandler()
        
        # 模拟运行中的组件
        mock_components = {
            'api_server': Mock(),
            'monitoring': Mock(),
            'database': Mock()
        }
        
        for name, component in mock_components.items():
            component.stop = AsyncMock()
            self.shutdown_handler.register_component(name, component)
        
        # 执行优雅关闭
        shutdown_result = await self.shutdown_handler.graceful_shutdown()
        
        # 验证关闭结果
        assert shutdown_result is True
        
        # 验证所有组件都被停止
        for component in mock_components.values():
            component.stop.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_shutdown_with_timeout(self):
        """测试带超时的关闭处理"""
        logger.info("测试超时关闭处理...")
        
        self.shutdown_handler = ShutdownHandler()
        
        # 模拟一个不响应关闭的组件
        slow_component = Mock()
        slow_component.stop = AsyncMock()
        slow_component.stop.side_effect = asyncio.sleep(10)  # 模拟长时间运行
        
        self.shutdown_handler.register_component('slow_component', slow_component)
        
        # 设置短超时时间
        self.shutdown_handler.shutdown_timeout = 1.0
        
        start_time = time.time()
        shutdown_result = await self.shutdown_handler.graceful_shutdown()
        end_time = time.time()
        
        # 验证超时处理
        assert (end_time - start_time) < 2.0  # 应该在超时时间内完成
        # 超时情况下可能返回False
    
    @pytest.mark.asyncio
    async def test_startup_health_check_integration(self):
        """测试启动时的健康检查集成"""
        logger.info("测试启动健康检查集成...")
        
        # 创建健康检查器
        health_checker = HealthChecker()
        
        # 模拟系统组件健康检查
        with patch.object(health_checker, 'check_database_health') as mock_db_check, \
             patch.object(health_checker, 'check_api_health') as mock_api_check, \
             patch.object(health_checker, 'check_monitoring_health') as mock_monitor_check:
            
            # 设置健康检查结果
            mock_db_check.return_value = {'status': 'healthy', 'response_time': 50}
            mock_api_check.return_value = {'status': 'healthy', 'response_time': 30}
            mock_monitor_check.return_value = {'status': 'healthy', 'response_time': 20}
            
            # 执行健康检查
            health_report = await health_checker.perform_comprehensive_health_check()
            
            # 验证健康检查结果
            assert health_report['overall_status'] == 'healthy'
            assert 'database' in health_report['component_status']
            assert 'api' in health_report['component_status']
            assert 'monitoring' in health_report['component_status']
    
    @pytest.mark.asyncio
    async def test_monitoring_integration_during_startup(self):
        """测试启动过程中的监控集成"""
        logger.info("测试启动监控集成...")
        
        # 创建监控优化器
        self.monitoring_optimizer = MonitoringOptimizer()
        
        # 启动监控
        await self.monitoring_optimizer.start()
        
        # 验证监控组件启动
        assert self.monitoring_optimizer.running is True
        assert self.monitoring_optimizer.polling_manager.running is True
        assert self.monitoring_optimizer.cache_manager.running is True
        
        # 注册一个监控任务
        def test_monitoring_task():
            return "monitoring_active"
        
        self.monitoring_optimizer.register_monitoring_task(
            "startup_test", test_monitoring_task, 5
        )
        
        # 验证任务注册
        assert "startup_test" in self.monitoring_optimizer.polling_manager.polling_callbacks
        
        # 停止监控
        await self.monitoring_optimizer.stop()
        assert self.monitoring_optimizer.running is False
    
    @pytest.mark.asyncio
    async def test_error_handling_during_startup(self):
        """测试启动过程中的错误处理"""
        logger.info("测试启动错误处理...")
        
        # 创建错误处理器
        from src.error_handling.enhanced_error_handler import ErrorHandlingConfig
        config = ErrorHandlingConfig(
            report_directory=self.temp_dir,
            batch_processing=False
        )
        self.error_handler = EnhancedErrorHandler(config)
        
        # 模拟启动过程中的错误
        startup_error = Exception("Database connection failed during startup")
        context = {
            'phase': 'startup',
            'component': 'database',
            'operation': 'connect'
        }
        
        # 处理错误
        error_report = self.error_handler.handle_error(startup_error, context)
        
        # 验证错误处理
        assert error_report is not None
        assert error_report.classified_error.context['phase'] == 'startup'
        assert error_report.classified_error.component == 'database'
        
        # 验证错误被记录
        assert error_report.report_id in self.error_handler.error_reports
    
    @pytest.mark.asyncio
    async def test_dependency_resolution_integration(self):
        """测试依赖解析集成"""
        logger.info("测试依赖解析集成...")
        
        # 创建依赖解析器
        dependency_resolver = DependencyResolver()
        
        # 定义组件依赖关系
        dependencies = {
            'database': [],
            'monitoring': ['database'],
            'api_server': ['database', 'monitoring'],
            'data_sources': ['database'],
            'web_ui': ['api_server']
        }
        
        # 设置依赖关系
        for component, deps in dependencies.items():
            dependency_resolver.add_dependency(component, deps)
        
        # 解析启动顺序
        startup_order = dependency_resolver.resolve_startup_order()
        
        # 验证启动顺序
        assert len(startup_order) == len(dependencies)
        
        # 验证依赖顺序正确
        db_index = startup_order.index('database')
        monitoring_index = startup_order.index('monitoring')
        api_index = startup_order.index('api_server')
        
        assert db_index < monitoring_index  # database应该在monitoring之前
        assert monitoring_index < api_index  # monitoring应该在api_server之前
    
    @pytest.mark.asyncio
    async def test_system_state_persistence(self):
        """测试系统状态持久化"""
        logger.info("测试系统状态持久化...")
        
        # 创建状态文件路径
        state_file = os.path.join(self.temp_dir, 'system_state.json')
        
        # 模拟系统状态
        system_state = {
            'startup_time': datetime.now().isoformat(),
            'components': {
                'database': {'status': 'running', 'pid': 1234},
                'api_server': {'status': 'running', 'pid': 1235},
                'monitoring': {'status': 'running', 'pid': 1236}
            },
            'configuration': {
                'log_level': 'INFO',
                'monitoring_enabled': True
            }
        }
        
        # 保存状态
        with open(state_file, 'w') as f:
            json.dump(system_state, f, indent=2)
        
        # 验证状态文件存在
        assert os.path.exists(state_file)
        
        # 读取并验证状态
        with open(state_file, 'r') as f:
            loaded_state = json.load(f)
        
        assert loaded_state['components']['database']['status'] == 'running'
        assert loaded_state['configuration']['monitoring_enabled'] is True
    
    @pytest.mark.asyncio
    async def test_concurrent_component_startup(self):
        """测试并发组件启动"""
        logger.info("测试并发组件启动...")
        
        # 创建模拟组件
        async def mock_component_startup(name, delay):
            """模拟组件启动"""
            await asyncio.sleep(delay)
            return f"{name}_started"
        
        # 并发启动多个组件
        startup_tasks = [
            mock_component_startup('database', 0.1),
            mock_component_startup('monitoring', 0.2),
            mock_component_startup('api_server', 0.15),
            mock_component_startup('data_sources', 0.05)
        ]
        
        start_time = time.time()
        results = await asyncio.gather(*startup_tasks)
        end_time = time.time()
        
        # 验证并发启动
        assert len(results) == 4
        assert all('_started' in result for result in results)
        
        # 并发启动应该比串行启动快
        assert (end_time - start_time) < 0.5  # 应该小于所有延迟的总和
    
    @pytest.mark.asyncio
    async def test_startup_failure_recovery(self):
        """测试启动失败恢复"""
        logger.info("测试启动失败恢复...")
        
        # 模拟启动管理器
        startup_manager = StartupManager()
        
        # 模拟组件启动失败
        failed_components = []
        
        async def mock_component_startup_with_failure(component_name):
            if component_name == 'problematic_component':
                failed_components.append(component_name)
                raise Exception(f"{component_name} startup failed")
            return f"{component_name} started successfully"
        
        # 尝试启动组件
        components = ['database', 'problematic_component', 'api_server']
        
        startup_results = {}
        for component in components:
            try:
                result = await mock_component_startup_with_failure(component)
                startup_results[component] = {'status': 'success', 'result': result}
            except Exception as e:
                startup_results[component] = {'status': 'failed', 'error': str(e)}
        
        # 验证失败处理
        assert startup_results['database']['status'] == 'success'
        assert startup_results['problematic_component']['status'] == 'failed'
        assert startup_results['api_server']['status'] == 'success'
        
        # 验证失败组件被记录
        assert 'problematic_component' in failed_components
    
    @pytest.mark.asyncio
    async def test_shutdown_cleanup_verification(self):
        """测试关闭清理验证"""
        logger.info("测试关闭清理验证...")
        
        # 创建临时资源文件
        temp_files = []
        for i in range(3):
            temp_file = os.path.join(self.temp_dir, f'temp_resource_{i}.tmp')
            with open(temp_file, 'w') as f:
                f.write(f"temporary resource {i}")
            temp_files.append(temp_file)
        
        # 模拟关闭清理
        def cleanup_resources():
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
        
        # 执行清理
        cleanup_resources()
        
        # 验证资源被清理
        for temp_file in temp_files:
            assert not os.path.exists(temp_file)


class TestSystemIntegrationScenarios:
    """系统集成场景测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_full_system_lifecycle(self):
        """测试完整的系统生命周期"""
        logger.info("测试完整系统生命周期...")
        
        # 阶段1: 系统启动
        startup_manager = StartupManager()
        
        # 模拟启动过程
        with patch.object(startup_manager, 'startup_with_dependencies') as mock_startup:
            mock_startup.return_value = True
            
            startup_success = await startup_manager.startup_with_dependencies()
            assert startup_success is True
        
        # 阶段2: 系统运行
        # 模拟系统运行一段时间
        await asyncio.sleep(0.1)
        
        # 阶段3: 监控和错误处理
        monitoring_optimizer = MonitoringOptimizer()
        await monitoring_optimizer.start()
        
        # 注册监控任务
        def health_check_task():
            return {'status': 'healthy', 'timestamp': datetime.now().isoformat()}
        
        monitoring_optimizer.register_monitoring_task("health_check", health_check_task, 1)
        
        # 运行一段时间
        await asyncio.sleep(0.2)
        
        # 阶段4: 系统关闭
        shutdown_handler = ShutdownHandler()
        
        # 注册需要关闭的组件
        shutdown_handler.register_component('monitoring', monitoring_optimizer)
        
        # 执行关闭
        shutdown_success = await shutdown_handler.graceful_shutdown()
        
        # 验证完整生命周期
        assert startup_success is True
        assert shutdown_success is True
        assert monitoring_optimizer.running is False
    
    @pytest.mark.asyncio
    async def test_system_recovery_after_failure(self):
        """测试系统故障后恢复"""
        logger.info("测试系统故障恢复...")
        
        # 模拟系统运行
        system_components = {
            'database': {'status': 'running', 'health': 'good'},
            'api_server': {'status': 'running', 'health': 'good'},
            'monitoring': {'status': 'running', 'health': 'good'}
        }
        
        # 模拟组件故障
        def simulate_component_failure(component_name):
            if component_name in system_components:
                system_components[component_name]['status'] = 'failed'
                system_components[component_name]['health'] = 'critical'
        
        # 模拟组件恢复
        async def recover_component(component_name):
            if component_name in system_components:
                # 模拟恢复过程
                await asyncio.sleep(0.1)
                system_components[component_name]['status'] = 'running'
                system_components[component_name]['health'] = 'good'
                return True
            return False
        
        # 模拟故障发生
        simulate_component_failure('database')
        assert system_components['database']['status'] == 'failed'
        
        # 模拟故障检测和恢复
        recovery_success = await recover_component('database')
        
        # 验证恢复结果
        assert recovery_success is True
        assert system_components['database']['status'] == 'running'
        assert system_components['database']['health'] == 'good'
    
    @pytest.mark.asyncio
    async def test_system_performance_under_load(self):
        """测试系统负载下的性能"""
        logger.info("测试系统负载性能...")
        
        # 创建监控优化器
        monitoring_optimizer = MonitoringOptimizer()
        await monitoring_optimizer.start()
        
        # 模拟高负载场景
        async def simulate_high_load_task(task_id):
            """模拟高负载任务"""
            start_time = time.time()
            
            # 模拟一些计算工作
            for i in range(1000):
                await asyncio.sleep(0.0001)  # 微小延迟模拟工作
            
            end_time = time.time()
            return {
                'task_id': task_id,
                'execution_time': end_time - start_time,
                'status': 'completed'
            }
        
        # 并发执行多个高负载任务
        load_tasks = [
            simulate_high_load_task(i) for i in range(10)
        ]
        
        start_time = time.time()
        results = await asyncio.gather(*load_tasks)
        end_time = time.time()
        
        # 验证性能结果
        assert len(results) == 10
        assert all(result['status'] == 'completed' for result in results)
        
        total_execution_time = end_time - start_time
        average_task_time = sum(result['execution_time'] for result in results) / len(results)
        
        logger.info(f"总执行时间: {total_execution_time:.3f}秒")
        logger.info(f"平均任务时间: {average_task_time:.3f}秒")
        
        # 性能应该在合理范围内
        assert total_execution_time < 2.0  # 总时间不应超过2秒
        assert average_task_time < 0.5     # 平均任务时间不应超过0.5秒
        
        # 停止监控
        await monitoring_optimizer.stop()
    
    @pytest.mark.asyncio
    async def test_system_configuration_changes(self):
        """测试系统配置变更"""
        logger.info("测试系统配置变更...")
        
        # 初始配置
        initial_config = {
            'logging': {'level': 'INFO', 'file_rotation': True},
            'monitoring': {'interval': 30, 'enabled': True},
            'database': {'pool_size': 10, 'timeout': 30}
        }
        
        # 保存初始配置
        config_file = os.path.join(self.temp_dir, 'system_config.json')
        with open(config_file, 'w') as f:
            json.dump(initial_config, f, indent=2)
        
        # 模拟配置变更
        updated_config = initial_config.copy()
        updated_config['logging']['level'] = 'DEBUG'
        updated_config['monitoring']['interval'] = 15
        updated_config['database']['pool_size'] = 20
        
        # 应用配置变更
        with open(config_file, 'w') as f:
            json.dump(updated_config, f, indent=2)
        
        # 验证配置变更
        with open(config_file, 'r') as f:
            loaded_config = json.load(f)
        
        assert loaded_config['logging']['level'] == 'DEBUG'
        assert loaded_config['monitoring']['interval'] == 15
        assert loaded_config['database']['pool_size'] == 20
        
        # 模拟配置重载
        def reload_configuration():
            with open(config_file, 'r') as f:
                return json.load(f)
        
        reloaded_config = reload_configuration()
        assert reloaded_config == updated_config


if __name__ == '__main__':
    pytest.main([__file__, '-v'])