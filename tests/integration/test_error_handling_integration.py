import logging
logger = logging.getLogger(__name__)
"""
错误处理系统集成测试

测试错误分类、诊断和修复建议系统的集成
"""

import pytest
import asyncio
import tempfile
import os
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock

from src.error_handling.enhanced_error_handler import EnhancedErrorHandler, ErrorHandlingConfig
from src.error_handling.error_classifier import ErrorClassifier, ErrorCategory, ErrorSeverity
from src.error_handling.diagnostic_engine import DiagnosticEngine
from src.error_handling.recommendation_engine import RecommendationEngine
from src.core.enhanced_health_checker import HealthChecker
from src.monitoring.monitoring_optimizer import MonitoringOptimizer


class TestErrorHandlingIntegration:
    """错误处理系统集成测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建错误处理配置
        self.error_config = ErrorHandlingConfig(
            report_directory=self.temp_dir,
            batch_processing=False,
            auto_classification=True,
            generate_recommendations=True
        )
        
        # 创建错误处理器
        self.error_handler = EnhancedErrorHandler(self.error_config)
        
        # 创建其他组件
        self.health_checker = HealthChecker()
        self.monitoring_optimizer = None
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_error_classification_integration(self):
        """测试错误分类集成"""
        logger.info("测试错误分类系统集成...")
        
        # 模拟不同类型的错误
        test_errors = [
            # 数据库连接错误
            {
                'error': ConnectionError("Database connection failed"),
                'context': {'component': 'database', 'operation': 'connect'}
            },
            # API调用错误
            {
                'error': TimeoutError("API request timeout"),
                'context': {'component': 'api_client', 'operation': 'fetch_data'}
            },
            # 配置错误
            {
                'error': ValueError("Invalid configuration value"),
                'context': {'component': 'config', 'operation': 'load_settings'}
            },
            # 内存错误
            {
                'error': MemoryError("Out of memory"),
                'context': {'component': 'data_processor', 'operation': 'process_large_dataset'}
            }
        ]
        
        # 处理所有错误
        error_reports = []
        for error_info in test_errors:
            report = self.error_handler.handle_error(
                error_info['error'], 
                error_info['context']
            )
            error_reports.append(report)
        
        # 验证错误分类
        assert len(error_reports) == 4
        
        # 验证不同错误类型被正确分类
        categories = [report.classified_error.category for report in error_reports]
        assert ErrorCategory.INFRASTRUCTURE in categories  # 数据库连接错误
        assert ErrorCategory.NETWORK in categories  # API超时错误
        assert ErrorCategory.CONFIGURATION in categories  # 配置错误
        assert ErrorCategory.RESOURCE in categories  # 内存错误
        
        # 验证严重性分级
        severities = [report.classified_error.severity for report in error_reports]
        assert ErrorSeverity.CRITICAL in severities  # 内存错误应该是严重的
        assert ErrorSeverity.HIGH in severities  # 数据库连接错误应该是高级别的
        
        # 获取错误统计
        error_stats = self.error_handler.get_error_statistics()
        assert error_stats['total_errors'] == 4
        assert len(error_stats['by_category']) > 0
        assert len(error_stats['by_severity']) > 0
    
    @pytest.mark.asyncio
    async def test_diagnostic_engine_integration(self):
        """测试诊断引擎集成"""
        logger.info("测试诊断引擎集成...")
        
        # 创建诊断引擎
        diagnostic_engine = DiagnosticEngine()
        
        # 模拟系统问题场景
        system_issues = [
            {
                'type': 'high_memory_usage',
                'metrics': {'memory_usage': 95.0, 'swap_usage': 80.0},
                'context': {'component': 'data_processor'}
            },
            {
                'type': 'slow_database_queries',
                'metrics': {'avg_query_time': 5000, 'active_connections': 100},
                'context': {'component': 'database'}
            },
            {
                'type': 'api_rate_limit_exceeded',
                'metrics': {'requests_per_minute': 1000, 'error_rate': 0.3},
                'context': {'component': 'api_client'}
            }
        ]
        
        # 运行诊断
        diagnostic_reports = []
        for issue in system_issues:
            # 创建模拟错误
            error = Exception(f"System issue: {issue['type']}")
            
            # 处理错误并生成诊断
            error_report = self.error_handler.handle_error(error, issue['context'])
            
            # 运行诊断引擎
            diagnostic_result = await diagnostic_engine.diagnose_system_issue(
                error_report.classified_error,
                issue['metrics']
            )
            
            diagnostic_reports.append(diagnostic_result)
        
        # 验证诊断结果
        assert len(diagnostic_reports) == 3
        
        for diagnostic in diagnostic_reports:
            assert diagnostic is not None
            assert 'issue_type' in diagnostic
            assert 'root_cause_analysis' in diagnostic
            assert 'impact_assessment' in diagnostic
            assert 'recommended_actions' in diagnostic
        
        # 验证特定诊断内容
        memory_diagnostic = diagnostic_reports[0]
        assert 'memory' in memory_diagnostic['issue_type'].lower()
        assert len(memory_diagnostic['recommended_actions']) > 0
        
        db_diagnostic = diagnostic_reports[1]
        assert 'database' in db_diagnostic['issue_type'].lower()
        assert 'query' in db_diagnostic['root_cause_analysis'].lower()
    
    @pytest.mark.asyncio
    async def test_recommendation_engine_integration(self):
        """测试建议引擎集成"""
        logger.info("测试建议引擎集成...")
        
        # 创建建议引擎
        recommendation_engine = RecommendationEngine()
        
        # 模拟不同的错误场景
        error_scenarios = [
            {
                'error': ConnectionError("Failed to connect to database"),
                'context': {
                    'component': 'database',
                    'connection_string': 'postgresql://localhost:5432/trading',
                    'retry_count': 3
                }
            },
            {
                'error': FileNotFoundError("Configuration file not found"),
                'context': {
                    'component': 'config',
                    'file_path': '/etc/trading/config.yaml',
                    'operation': 'load_config'
                }
            },
            {
                'error': ImportError("Module 'pandas' not found"),
                'context': {
                    'component': 'data_analysis',
                    'module': 'pandas',
                    'operation': 'import_dependencies'
                }
            }
        ]
        
        # 为每个错误生成建议
        recommendations = []
        for scenario in error_scenarios:
            # 处理错误
            error_report = self.error_handler.handle_error(
                scenario['error'], 
                scenario['context']
            )
            
            # 生成建议
            recommendation = await recommendation_engine.generate_recommendations(
                error_report.classified_error
            )
            
            recommendations.append(recommendation)
        
        # 验证建议生成
        assert len(recommendations) == 3
        
        for recommendation in recommendations:
            assert recommendation is not None
            assert 'immediate_actions' in recommendation
            assert 'preventive_measures' in recommendation
            assert 'configuration_suggestions' in recommendation
            assert len(recommendation['immediate_actions']) > 0
        
        # 验证特定建议内容
        db_recommendation = recommendations[0]
        assert any('connection' in action.lower() for action in db_recommendation['immediate_actions'])
        assert any('database' in action.lower() for action in db_recommendation['immediate_actions'])
        
        config_recommendation = recommendations[1]
        assert any('file' in action.lower() for action in config_recommendation['immediate_actions'])
        assert any('config' in action.lower() for action in config_recommendation['immediate_actions'])
        
        import_recommendation = recommendations[2]
        assert any('install' in action.lower() for action in import_recommendation['immediate_actions'])
        assert any('pandas' in action.lower() for action in import_recommendation['immediate_actions'])
    
    @pytest.mark.asyncio
    async def test_health_check_error_integration(self):
        """测试健康检查与错误处理集成"""
        logger.info("测试健康检查错误处理集成...")
        
        # 模拟健康检查失败
        with patch.object(self.health_checker, 'check_database_health') as mock_db_check, \
             patch.object(self.health_checker, 'check_api_health') as mock_api_check, \
             patch.object(self.health_checker, 'check_monitoring_health') as mock_monitor_check:
            
            # 设置健康检查失败
            mock_db_check.side_effect = ConnectionError("Database health check failed")
            mock_api_check.return_value = {'status': 'unhealthy', 'error': 'API server not responding'}
            mock_monitor_check.return_value = {'status': 'degraded', 'warning': 'High memory usage'}
            
            # 执行健康检查
            try:
                health_report = await self.health_checker.perform_comprehensive_health_check()
            except Exception as e:
                # 处理健康检查过程中的错误
                error_report = self.error_handler.handle_error(e, {
                    'component': 'health_checker',
                    'operation': 'comprehensive_check'
                })
                
                # 验证错误处理
                assert error_report is not None
                assert error_report.classified_error.component == 'health_checker'
            
            # 处理健康检查结果中的问题
            if 'health_report' in locals():
                for component, status in health_report.get('component_status', {}).items():
                    if status.get('status') in ['unhealthy', 'degraded']:
                        # 为每个不健康的组件创建错误报告
                        health_error = Exception(f"Component {component} is {status['status']}")
                        error_report = self.error_handler.handle_error(health_error, {
                            'component': component,
                            'health_status': status,
                            'operation': 'health_monitoring'
                        })
                        
                        assert error_report is not None
        
        # 验证错误统计包含健康检查错误
        error_stats = self.error_handler.get_error_statistics()
        assert error_stats['total_errors'] > 0
    
    @pytest.mark.asyncio
    async def test_monitoring_error_integration(self):
        """测试监控系统错误集成"""
        logger.info("测试监控系统错误集成...")
        
        # 创建监控优化器
        from src.monitoring.monitoring_optimizer import MonitoringOptimizationConfig, OptimizationLevel
        config = MonitoringOptimizationConfig(
            optimization_level=OptimizationLevel.BALANCED,
            enable_adaptive_optimization=True
        )
        self.monitoring_optimizer = MonitoringOptimizer(config)
        
        # 启动监控系统
        await self.monitoring_optimizer.start()
        
        try:
            # 创建会出错的监控任务
            async def failing_monitoring_task():
                import random
                if random.random() < 0.7:  # 70%的失败率
                    raise Exception("Monitoring task failed")
                return "success"
            
            # 注册监控任务
            self.monitoring_optimizer.register_monitoring_task(
                'failing_task', failing_monitoring_task, custom_interval=1
            )
            
            # 运行监控并捕获错误
            monitoring_errors = []
            
            for i in range(10):
                try:
                    result = await failing_monitoring_task()
                except Exception as e:
                    # 使用错误处理器处理监控错误
                    error_report = self.error_handler.handle_error(e, {
                        'component': 'monitoring',
                        'task': 'failing_task',
                        'iteration': i,
                        'timestamp': datetime.now().isoformat()
                    })
                    monitoring_errors.append(error_report)
                
                await asyncio.sleep(0.1)
            
            # 验证监控错误处理
            assert len(monitoring_errors) > 0
            
            # 验证错误分类
            for error_report in monitoring_errors:
                assert error_report.classified_error.component == 'monitoring'
                assert 'task' in error_report.classified_error.context
            
            # 分析错误模式
            error_stats = self.error_handler.get_error_statistics()
            monitoring_errors_count = sum(
                count for category, count in error_stats['by_component'].items()
                if 'monitoring' in category.lower()
            )
            assert monitoring_errors_count > 0
        
        finally:
            await self.monitoring_optimizer.stop()
    
    @pytest.mark.asyncio
    async def test_error_recovery_integration(self):
        """测试错误恢复集成"""
        logger.info("测试错误恢复集成...")
        
        # 模拟可恢复的错误场景
        recovery_scenarios = [
            {
                'error_type': 'temporary_network_error',
                'error': ConnectionError("Temporary network failure"),
                'context': {'component': 'api_client', 'retry_count': 0},
                'recoverable': True
            },
            {
                'error_type': 'resource_exhaustion',
                'error': MemoryError("Insufficient memory"),
                'context': {'component': 'data_processor', 'memory_usage': 95},
                'recoverable': True
            },
            {
                'error_type': 'configuration_error',
                'error': ValueError("Invalid configuration"),
                'context': {'component': 'config', 'config_file': 'invalid.yaml'},
                'recoverable': False
            }
        ]
        
        recovery_results = []
        
        for scenario in recovery_scenarios:
            # 处理错误
            error_report = self.error_handler.handle_error(
                scenario['error'], 
                scenario['context']
            )
            
            # 尝试自动恢复
            recovery_attempted = False
            recovery_successful = False
            
            if scenario['recoverable']:
                recovery_attempted = True
                
                # 模拟恢复操作
                if scenario['error_type'] == 'temporary_network_error':
                    # 模拟网络重连
                    await asyncio.sleep(0.1)
                    recovery_successful = True
                elif scenario['error_type'] == 'resource_exhaustion':
                    # 模拟内存清理
                    await asyncio.sleep(0.1)
                    recovery_successful = True
            
            recovery_result = {
                'error_type': scenario['error_type'],
                'error_report': error_report,
                'recovery_attempted': recovery_attempted,
                'recovery_successful': recovery_successful
            }
            
            recovery_results.append(recovery_result)
        
        # 验证恢复结果
        assert len(recovery_results) == 3
        
        # 验证可恢复错误的处理
        recoverable_results = [r for r in recovery_results if r['recovery_attempted']]
        assert len(recoverable_results) == 2
        
        successful_recoveries = [r for r in recoverable_results if r['recovery_successful']]
        assert len(successful_recoveries) == 2
        
        # 验证不可恢复错误的处理
        non_recoverable_results = [r for r in recovery_results if not r['recovery_attempted']]
        assert len(non_recoverable_results) == 1
        assert non_recoverable_results[0]['error_type'] == 'configuration_error'
    
    @pytest.mark.asyncio
    async def test_error_reporting_integration(self):
        """测试错误报告集成"""
        logger.info("测试错误报告集成...")
        
        # 生成多种类型的错误
        test_errors = [
            (RuntimeError("Runtime error occurred"), {'component': 'runtime'}),
            (ValueError("Invalid value provided"), {'component': 'validation'}),
            (TimeoutError("Operation timed out"), {'component': 'network'}),
            (KeyError("Missing configuration key"), {'component': 'config'}),
            (ImportError("Failed to import module"), {'component': 'dependencies'})
        ]
        
        # 处理所有错误
        error_reports = []
        for error, context in test_errors:
            report = self.error_handler.handle_error(error, context)
            error_reports.append(report)
        
        # 生成综合错误报告
        comprehensive_report = self.error_handler.generate_comprehensive_report()
        
        # 验证综合报告
        assert comprehensive_report is not None
        assert 'summary' in comprehensive_report
        assert 'error_details' in comprehensive_report
        assert 'statistics' in comprehensive_report
        assert 'recommendations' in comprehensive_report
        
        # 验证报告内容
        summary = comprehensive_report['summary']
        assert summary['total_errors'] == 5
        assert summary['unique_components'] == 5
        assert 'most_common_category' in summary
        assert 'most_severe_error' in summary
        
        # 验证错误详情
        error_details = comprehensive_report['error_details']
        assert len(error_details) == 5
        
        for detail in error_details:
            assert 'error_id' in detail
            assert 'timestamp' in detail
            assert 'category' in detail
            assert 'severity' in detail
            assert 'component' in detail
        
        # 验证统计信息
        statistics = comprehensive_report['statistics']
        assert 'by_category' in statistics
        assert 'by_severity' in statistics
        assert 'by_component' in statistics
        assert 'by_time_period' in statistics
        
        # 验证建议
        recommendations = comprehensive_report['recommendations']
        assert len(recommendations) > 0
        
        for recommendation in recommendations:
            assert 'priority' in recommendation
            assert 'description' in recommendation
            assert 'action_items' in recommendation
    
    @pytest.mark.asyncio
    async def test_error_pattern_analysis(self):
        """测试错误模式分析"""
        logger.info("测试错误模式分析...")
        
        # 模拟重复错误模式
        repeated_errors = []
        
        # 生成重复的数据库连接错误
        for i in range(5):
            error = ConnectionError(f"Database connection failed - attempt {i+1}")
            context = {
                'component': 'database',
                'operation': 'connect',
                'attempt': i+1,
                'timestamp': datetime.now().isoformat()
            }
            
            report = self.error_handler.handle_error(error, context)
            repeated_errors.append(report)
            
            await asyncio.sleep(0.1)
        
        # 生成重复的API超时错误
        for i in range(3):
            error = TimeoutError(f"API request timeout - endpoint /api/data")
            context = {
                'component': 'api_client',
                'operation': 'fetch_data',
                'endpoint': '/api/data',
                'attempt': i+1
            }
            
            report = self.error_handler.handle_error(error, context)
            repeated_errors.append(report)
            
            await asyncio.sleep(0.1)
        
        # 分析错误模式
        error_stats = self.error_handler.get_error_statistics()
        
        # 验证重复错误检测
        assert error_stats['total_errors'] == 8
        
        # 验证组件错误分布
        component_stats = error_stats['by_component']
        assert 'database' in component_stats
        assert 'api_client' in component_stats
        assert component_stats['database'] == 5
        assert component_stats['api_client'] == 3
        
        # 验证错误类别分布
        category_stats = error_stats['by_category']
        assert len(category_stats) > 0
        
        # 验证时间模式
        time_stats = error_stats['by_time_period']
        assert 'last_hour' in time_stats
        assert time_stats['last_hour'] == 8
        
        # 检查是否识别出错误模式
        if hasattr(self.error_handler, 'detect_error_patterns'):
            patterns = self.error_handler.detect_error_patterns()
            assert len(patterns) > 0
            
            # 应该检测到数据库连接问题模式
            db_patterns = [p for p in patterns if 'database' in p.get('component', '').lower()]
            assert len(db_patterns) > 0
            
            # 应该检测到API超时问题模式
            api_patterns = [p for p in patterns if 'api' in p.get('component', '').lower()]
            assert len(api_patterns) > 0


if __name__ == '__main__':
    pytest.main([__file__, '-v'])