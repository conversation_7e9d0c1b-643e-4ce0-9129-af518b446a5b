import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文化核心模块测试脚本

测试风险管理、策略管理和回测引擎的中文化功能
"""

import sys
import os
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

def 测试中文化模块():
    """测试所有中文化模块的核心功能"""
    
    logger.info("🔍 开始测试中文化核心模块...")
    
    try:
        # 测试风险管理模块中文化
        logger.info("\n📊 测试风险管理模块...")
        from src.risk import 风险管理器
        
        风险管理 = 风险管理器()
        logger.info(f"✅ 成功创建风险管理器: {type(风险管理).__name__}")
        
        状态 = 风险管理.获取风险摘要()
        logger.info(f"✅ 风险摘要: {len(状态)} 个状态项")
        
        # 测试策略管理模块中文化
        logger.info("\n🎯 测试策略管理模块...")
        from src.market.strategies import 基础策略, 策略上下文, 技术指标策略, 多因子策略
        
        # 创建测试策略
        class 测试策略(基础策略):
            def 获取参数定义(self):
                return {}
            
            def 初始化(self, 上下文):
                pass
            
            def 处理数据(self, 市场数据):
                return []
        
        策略 = 测试策略("测试策略", {"参数1": "值1"})
        logger.info(f"✅ 成功创建基础策略: {策略.名称}")
        
        状态 = 策略.获取状态()
        logger.info(f"✅ 策略状态: {状态['名称']}, 激活: {状态['是否激活']}")
        
        # 测试回测引擎模块中文化
        logger.info("\n🚀 测试回测引擎模块...")
        from src.backtest import 回测引擎, 回测进度
        
        引擎 = 回测引擎(初始资金=100000, 手续费率=0.001)
        logger.info(f"✅ 成功创建回测引擎，初始资金: {引擎.初始资金}")
        
        进度 = 回测进度(100)
        进度.更新(50)
        logger.info(f"✅ 回测进度: {进度.获取进度百分比():.1f}%")
        
        # 测试集成功能
        logger.info("\n🔗 测试模块集成...")
        
        # 向回测引擎添加策略
        引擎.添加策略(策略)
        状态 = 引擎.获取当前状态()
        logger.info(f"✅ 集成测试成功，策略数量: {状态['策略数量']}")
        
        logger.info("\n🎉 所有中文化模块测试通过！")
        return True
        
    except Exception as e:
        logger.info(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def 展示中文化功能():
    """展示中文化功能的具体使用示例"""
    
    logger.info("\n" + "="*50)
    logger.info("📖 中文化功能展示")
    logger.info("="*50)
    
    try:
        # 风险管理器示例
        logger.info("\n💼 风险管理器使用示例:")
        from src.risk import 风险管理器
        
        风险管理 = 风险管理器()
        logger.info(f"• 创建风险管理器成功")
        logger.info(f"• 紧急停止状态: {风险管理.紧急停止激活}")
        logger.info(f"• 规则数量: {len(风险管理.规则)}")
        logger.info(f"• 监控器数量: {len(风险管理.监控器)}")
        
        # 策略示例
        logger.info("\n📈 策略管理示例:")
        from src.market.strategies import 基础策略
        
        class 简单策略(基础策略):
            def 获取参数定义(self):
                return {}
            
            def 初始化(self, 上下文):
                self.日志器.info("策略初始化完成")
            
            def 处理数据(self, 市场数据):
                return []
        
        策略 = 简单策略("我的策略", {"周期": 20, "阈值": 0.05})
        logger.info(f"• 策略名称: {策略.名称}")
        logger.info(f"• 策略参数: {策略.参数}")
        logger.info(f"• 策略状态: {策略.获取状态()}")
        
        # 回测引擎示例
        logger.info("\n⚡ 回测引擎示例:")
        from src.backtest import 回测引擎
        
        引擎 = 回测引擎(
            初始资金=1000000,
            手续费率=0.0015,
            滑点=0.001
        )
        logger.info(f"• 初始资金: ¥{引擎.初始资金:,.2f}")
        logger.info(f"• 手续费率: {引擎.手续费率*100:.3f}%")
        logger.info(f"• 滑点: {引擎.滑点*100:.3f}%")
        
        引擎.添加策略(策略)
        状态 = 引擎.获取当前状态()
        logger.info(f"• 当前状态: {状态}")
        
    except Exception as e:
        logger.info(f"❌ 展示功能时出错: {e}")

if __name__ == "__main__":
    logger.info("🌟 量化交易系统中文化模块测试")
    logger.info("=" * 60)
    
    成功 = 测试中文化模块()
    
    if 成功:
        展示中文化功能()
        logger.info("\n✨ 所有中文化模块功能正常，可以开始使用！")
    else:
        logger.info("\n💥 中文化模块测试失败，需要进一步调试")
        sys.exit(1)