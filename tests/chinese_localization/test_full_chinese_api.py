import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的中文API功能测试

测试策略对比API的完整中文化功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def 测试中文API():
    """测试中文API的核心功能"""
    
    try:
        logger.info("🔍 开始完整的中文API测试...")
        
        # 导入中文API
        from src.analysis.comparison import 策略对比器, 策略数据, 对比方法, 排名方法
        from src.market.data.cache import get_cache_manager
        
        logger.info("✅ 成功导入所有中文API类")
        
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        
        # 策略1：稳健增长
        策略1价值 = pd.Series(
            np.cumprod(1 + np.random.normal(0.001, 0.01, len(dates))),
            index=dates
        )
        
        # 策略2：高风险高收益
        策略2价值 = pd.Series(
            np.cumprod(1 + np.random.normal(0.002, 0.02, len(dates))),
            index=dates
        )
        
        # 创建策略数据对象
        策略1 = 策略数据(
            名称="稳健增长策略",
            组合价值=策略1价值,
            元数据={"类型": "稳健", "风险等级": "低"}
        )
        
        策略2 = 策略数据(
            名称="激进成长策略", 
            组合价值=策略2价值,
            元数据={"类型": "激进", "风险等级": "高"}
        )
        
        logger.info("✨ 成功创建测试策略数据")
        
        # 创建对比器
        对比器 = 策略对比器(无风险利率=0.03)
        logger.info("✅ 成功创建策略对比器")
        
        # 执行对比分析
        对比结果 = 对比器.对比策略(
            策略列表=[策略1, 策略2],
            对比方法列表=[对比方法.夏普比率, 对比方法.总收益率, 对比方法.最大回撤],
            排名方法=排名方法.加权评分
        )
        
        logger.info("🎯 成功执行策略对比分析")
        
        # 验证结果
        assert len(对比结果.策略指标) == 2, "应该有两个策略的指标"
        assert len(对比结果.排名) == 2, "应该有两个策略的排名"
        assert "稳健增长策略" in 对比结果.策略指标, "应该包含策略1"
        assert "激进成长策略" in 对比结果.策略指标, "应该包含策略2"
        
        logger.info("✅ 对比结果验证通过")
        
        # 测试缓存管理器
        缓存管理器 = get_cache_manager()
        缓存统计 = 缓存管理器.get_cache_stats()
        
        logger.info(f"📊 缓存统计: {缓存统计}")
        
        # 输出结果摘要
        logger.info("\n📈 对比分析结果摘要:")
        for i, (策略名称, 评分) in enumerate(对比结果.排名):
            logger.info(f"  {i+1}. {策略名称}: {评分:.4f}")
        
        logger.info(f"\n🔗 相关性分析: {len(对比结果.相关性矩阵)} 个策略的相关性矩阵")
        logger.info(f"⚖️ 最优权重: {对比结果.最优权重}")
        
        logger.info("\n🎉 所有中文API功能测试通过！")
        return True
        
    except Exception as e:
        logger.info(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if 测试中文API():
        logger.info("\n✨ 中文API完全可用，策略对比功能正常运行！")
    else:
        logger.info("\n💥 中文API测试失败，需要进一步调试")
        sys.exit(1)