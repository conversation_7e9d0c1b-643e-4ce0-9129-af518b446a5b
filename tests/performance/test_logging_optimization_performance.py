logger = logging.getLogger(__name__)
"""
日志优化性能测试

测量日志优化对系统性能的影响
"""

import pytest
import asyncio
import time
import tempfile
import os
import logging
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import psutil
import gc
from memory_profiler import profile

from src.monitoring.logging_optimizer import LoggingOptimizer, LoggingOptimizationConfig
from src.monitoring.log_deduplicator import LogDeduplicator
from src.monitoring.log_level_manager import LogLevelManager


class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.memory_start = None
        self.memory_peak = None
        self.cpu_usage = []
        self.log_counts = {}
        self.operation_times = []
    
    def start_measurement(self):
        """开始性能测量"""
        self.start_time = time.time()
        self.memory_start = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        gc.collect()  # 清理垃圾回收
    
    def end_measurement(self):
        """结束性能测量"""
        self.end_time = time.time()
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        self.memory_peak = max(self.memory_peak or 0, current_memory)
    
    def record_operation_time(self, operation_name, duration):
        """记录操作时间"""
        self.operation_times.append({
            'operation': operation_name,
            'duration': duration,
            'timestamp': time.time()
        })
    
    def get_summary(self):
        """获取性能摘要"""
        total_time = self.end_time - self.start_time if self.end_time and self.start_time else 0
        memory_usage = self.memory_peak - self.memory_start if self.memory_peak and self.memory_start else 0
        
        return {
            'total_time': total_time,
            'memory_usage_mb': memory_usage,
            'operations_count': len(self.operation_times),
            'avg_operation_time': sum(op['duration'] for op in self.operation_times) / len(self.operation_times) if self.operation_times else 0,
            'log_counts': self.log_counts.copy()
        }


class TestLoggingOptimizationPerformance:
    """日志优化性能测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.metrics = PerformanceMetrics()
        
        # 创建日志优化器配置
        self.config = LoggingOptimizationConfig(
            deduplication_enabled=True,
            deduplication_window_seconds=60,
            level_optimization_enabled=True,
            batch_processing=True,
            batch_size=100
        )
        
        self.logging_optimizer = None
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_log_deduplication_performance(self):
        """测试日志去重性能"""
        logger.info("测试日志去重性能...")
        
        # 创建日志去重器
        deduplicator = LogDeduplicator(window_seconds=60)
        
        # 性能测试参数
        test_scenarios = [
            {'message_count': 1000, 'unique_ratio': 0.1},    # 10%唯一消息
            {'message_count': 5000, 'unique_ratio': 0.05},   # 5%唯一消息
            {'message_count': 10000, 'unique_ratio': 0.02},  # 2%唯一消息
        ]
        
        performance_results = []
        
        for scenario in test_scenarios:
            logger.info(f"测试场景: {scenario['message_count']} 条消息, {scenario['unique_ratio']*100}% 唯一")
            
            # 生成测试消息
            messages = []
            unique_count = int(scenario['message_count'] * scenario['unique_ratio'])
            
            # 创建唯一消息
            unique_messages = [f"Unique message {i}" for i in range(unique_count)]
            
            # 填充重复消息
            for i in range(scenario['message_count']):
                if i < unique_count:
                    messages.append(unique_messages[i])
                else:
                    # 重复使用唯一消息
                    messages.append(unique_messages[i % unique_count])
            
            # 开始性能测量
            self.metrics.start_measurement()
            
            # 处理消息
            processed_count = 0
            deduplicated_count = 0
            
            start_time = time.time()
            
            for message in messages:
                if deduplicator.should_log(message, 'INFO'):
                    processed_count += 1
                else:
                    deduplicated_count += 1
                
                deduplicator.add_message(message, 'INFO')
            
            end_time = time.time()
            
            # 结束性能测量
            self.metrics.end_measurement()
            
            # 计算性能指标
            processing_time = end_time - start_time
            messages_per_second = scenario['message_count'] / processing_time
            deduplication_ratio = deduplicated_count / scenario['message_count']
            
            result = {
                'scenario': scenario,
                'processing_time': processing_time,
                'messages_per_second': messages_per_second,
                'processed_count': processed_count,
                'deduplicated_count': deduplicated_count,
                'deduplication_ratio': deduplication_ratio,
                'memory_usage': self.metrics.get_summary()['memory_usage_mb']
            }
            
            performance_results.append(result)
            
            logger.info(f"  处理时间: {processing_time:.3f}秒")
            logger.info(f"  处理速度: {messages_per_second:.0f} 消息/秒")
            logger.info(f"  去重率: {deduplication_ratio:.1%}")
            logger.info(f"  内存使用: {result['memory_usage']:.2f}MB")
        
        # 验证性能要求
        for result in performance_results:
            # 处理速度应该足够快
            assert result['messages_per_second'] > 1000, f"处理速度太慢: {result['messages_per_second']}"
            
            # 去重应该有效
            if result['scenario']['unique_ratio'] < 0.1:
                assert result['deduplication_ratio'] > 0.5, f"去重效果不佳: {result['deduplication_ratio']}"
            
            # 内存使用应该合理
            assert result['memory_usage'] < 100, f"内存使用过高: {result['memory_usage']}MB"
        
        return performance_results
    
    @pytest.mark.asyncio
    async def test_log_level_management_performance(self):
        """测试日志级别管理性能"""
        logger.info("测试日志级别管理性能...")
        
        # 创建日志级别管理器
        level_manager = LogLevelManager()
        
        # 设置多个组件的日志级别
        components = [
            'watchfiles.main',
            'data_adapter.binance',
            'data_adapter.yahoo',
            'monitoring.system',
            'api.server',
            'database.connection',
            'cache.redis',
            'strategy.engine'
        ]
        
        # 性能测试
        self.metrics.start_measurement()
        
        # 测试级别设置性能
        start_time = time.time()
        
        for _ in range(1000):  # 重复1000次
            for component in components:
                level_manager.set_component_level(component, 'DEBUG')
                level_manager.get_component_level(component)
        
        level_setting_time = time.time() - start_time
        
        # 测试级别检查性能
        start_time = time.time()
        
        for _ in range(10000):  # 重复10000次
            for component in components:
                level_manager.should_log(component, 'INFO')
        
        level_checking_time = time.time() - start_time
        
        # 测试动态调整性能
        start_time = time.time()
        
        for _ in range(100):
            level_manager.optimize_levels_for_performance()
            level_manager.optimize_levels_for_debugging()
        
        dynamic_adjustment_time = time.time() - start_time
        
        self.metrics.end_measurement()
        
        # 计算性能指标
        level_setting_ops_per_sec = (1000 * len(components) * 2) / level_setting_time
        level_checking_ops_per_sec = (10000 * len(components)) / level_checking_time
        dynamic_adjustment_ops_per_sec = 200 / dynamic_adjustment_time
        
        logger.info(f"级别设置性能: {level_setting_ops_per_sec:.0f} 操作/秒")
        logger.info(f"级别检查性能: {level_checking_ops_per_sec:.0f} 操作/秒")
        logger.info(f"动态调整性能: {dynamic_adjustment_ops_per_sec:.0f} 操作/秒")
        
        # 验证性能要求
        assert level_setting_ops_per_sec > 10000, f"级别设置性能不足: {level_setting_ops_per_sec}"
        assert level_checking_ops_per_sec > 100000, f"级别检查性能不足: {level_checking_ops_per_sec}"
        assert dynamic_adjustment_ops_per_sec > 50, f"动态调整性能不足: {dynamic_adjustment_ops_per_sec}"
        
        return {
            'level_setting_ops_per_sec': level_setting_ops_per_sec,
            'level_checking_ops_per_sec': level_checking_ops_per_sec,
            'dynamic_adjustment_ops_per_sec': dynamic_adjustment_ops_per_sec,
            'memory_usage': self.metrics.get_summary()['memory_usage_mb']
        }
    
    @pytest.mark.asyncio
    async def test_logging_optimizer_integration_performance(self):
        """测试日志优化器集成性能"""
        logger.info("测试日志优化器集成性能...")
        
        # 创建日志优化器
        self.logging_optimizer = LoggingOptimizer(self.config)
        
        # 启动优化器
        await self.logging_optimizer.start()
        
        try:
            # 性能测试场景
            test_scenarios = [
                {
                    'name': '低负载',
                    'log_rate': 100,  # 每秒100条日志
                    'duration': 5,    # 持续5秒
                    'unique_ratio': 0.3
                },
                {
                    'name': '中等负载',
                    'log_rate': 500,  # 每秒500条日志
                    'duration': 5,    # 持续5秒
                    'unique_ratio': 0.1
                },
                {
                    'name': '高负载',
                    'log_rate': 1000, # 每秒1000条日志
                    'duration': 3,    # 持续3秒
                    'unique_ratio': 0.05
                }
            ]
            
            performance_results = []
            
            for scenario in test_scenarios:
                logger.info(f"测试场景: {scenario['name']}")
                
                # 开始性能测量
                self.metrics.start_measurement()
                
                # 生成测试日志
                total_logs = scenario['log_rate'] * scenario['duration']
                unique_count = int(total_logs * scenario['unique_ratio'])
                
                # 创建唯一消息模板
                unique_messages = [
                    f"Database query executed in {{time}}ms",
                    f"API request to {{endpoint}} completed",
                    f"Cache hit for key {{key}}",
                    f"User {{user_id}} performed action {{action}}",
                    f"System memory usage: {{usage}}%"
                ]
                
                # 模拟日志生成
                start_time = time.time()
                processed_logs = 0
                
                async def generate_logs():
                    nonlocal processed_logs
                    
                    for i in range(total_logs):
                        # 选择消息模板
                        template = unique_messages[i % len(unique_messages)]
                        message = template.format(
                            time=50 + (i % 100),
                            endpoint=f"/api/endpoint_{i % 10}",
                            key=f"cache_key_{i % 20}",
                            user_id=1000 + (i % 50),
                            action=f"action_{i % 5}",
                            usage=30 + (i % 40)
                        )
                        
                        # 使用优化器处理日志
                        if self.logging_optimizer.should_log(message, 'INFO'):
                            processed_logs += 1
                        
                        # 控制日志生成速率
                        if i % scenario['log_rate'] == 0 and i > 0:
                            await asyncio.sleep(1.0)
                
                # 执行日志生成
                await generate_logs()
                
                end_time = time.time()
                
                # 结束性能测量
                self.metrics.end_measurement()
                
                # 计算性能指标
                actual_duration = end_time - start_time
                actual_log_rate = total_logs / actual_duration
                processing_efficiency = processed_logs / total_logs
                
                # 获取优化器统计
                optimizer_stats = self.logging_optimizer.get_optimization_statistics()
                
                result = {
                    'scenario': scenario,
                    'actual_duration': actual_duration,
                    'actual_log_rate': actual_log_rate,
                    'total_logs': total_logs,
                    'processed_logs': processed_logs,
                    'processing_efficiency': processing_efficiency,
                    'optimizer_stats': optimizer_stats,
                    'memory_usage': self.metrics.get_summary()['memory_usage_mb']
                }
                
                performance_results.append(result)
                
                logger.info(f"  实际持续时间: {actual_duration:.2f}秒")
                logger.info(f"  实际日志速率: {actual_log_rate:.0f} 日志/秒")
                logger.info(f"  处理效率: {processing_efficiency:.1%}")
                logger.info(f"  内存使用: {result['memory_usage']:.2f}MB")
                logger.info(f"  去重统计: {optimizer_stats.get('deduplication', {})}")
            
            # 验证性能要求
            for result in performance_results:
                scenario = result['scenario']
                
                # 日志处理速率应该满足要求
                assert result['actual_log_rate'] >= scenario['log_rate'] * 0.8, \
                    f"日志处理速率不足: {result['actual_log_rate']} < {scenario['log_rate'] * 0.8}"
                
                # 内存使用应该合理
                assert result['memory_usage'] < 200, \
                    f"内存使用过高: {result['memory_usage']}MB"
                
                # 处理效率应该合理（考虑去重）
                if scenario['unique_ratio'] < 0.1:
                    assert result['processing_efficiency'] < 0.8, \
                        f"去重效果不明显: {result['processing_efficiency']}"
            
            return performance_results
        
        finally:
            await self.logging_optimizer.stop()
    
    @pytest.mark.asyncio
    async def test_concurrent_logging_performance(self):
        """测试并发日志性能"""
        logger.info("测试并发日志性能...")
        
        # 创建日志优化器
        self.logging_optimizer = LoggingOptimizer(self.config)
        await self.logging_optimizer.start()
        
        try:
            # 并发测试参数
            concurrent_scenarios = [
                {'threads': 2, 'logs_per_thread': 1000},
                {'threads': 5, 'logs_per_thread': 500},
                {'threads': 10, 'logs_per_thread': 200}
            ]
            
            performance_results = []
            
            for scenario in concurrent_scenarios:
                logger.info(f"并发测试: {scenario['threads']} 线程, 每线程 {scenario['logs_per_thread']} 条日志")
                
                # 开始性能测量
                self.metrics.start_measurement()
                
                # 创建并发任务
                async def worker_task(worker_id):
                    """工作线程任务"""
                    processed_count = 0
                    
                    for i in range(scenario['logs_per_thread']):
                        message = f"Worker {worker_id} - Message {i} - Data processing completed"
                        
                        if self.logging_optimizer.should_log(message, 'INFO'):
                            processed_count += 1
                        
                        # 小延迟模拟真实场景
                        await asyncio.sleep(0.001)
                    
                    return processed_count
                
                # 启动并发任务
                start_time = time.time()
                
                tasks = [
                    worker_task(i) for i in range(scenario['threads'])
                ]
                
                results = await asyncio.gather(*tasks)
                
                end_time = time.time()
                
                # 结束性能测量
                self.metrics.end_measurement()
                
                # 计算性能指标
                total_duration = end_time - start_time
                total_logs = scenario['threads'] * scenario['logs_per_thread']
                total_processed = sum(results)
                logs_per_second = total_logs / total_duration
                processing_efficiency = total_processed / total_logs
                
                result = {
                    'scenario': scenario,
                    'total_duration': total_duration,
                    'total_logs': total_logs,
                    'total_processed': total_processed,
                    'logs_per_second': logs_per_second,
                    'processing_efficiency': processing_efficiency,
                    'memory_usage': self.metrics.get_summary()['memory_usage_mb']
                }
                
                performance_results.append(result)
                
                logger.info(f"  总持续时间: {total_duration:.2f}秒")
                logger.info(f"  日志处理速率: {logs_per_second:.0f} 日志/秒")
                logger.info(f"  处理效率: {processing_efficiency:.1%}")
                logger.info(f"  内存使用: {result['memory_usage']:.2f}MB")
            
            # 验证并发性能
            for result in performance_results:
                # 并发处理应该有合理的性能
                assert result['logs_per_second'] > 500, \
                    f"并发处理性能不足: {result['logs_per_second']}"
                
                # 内存使用应该稳定
                assert result['memory_usage'] < 150, \
                    f"并发处理内存使用过高: {result['memory_usage']}MB"
            
            return performance_results
        
        finally:
            await self.logging_optimizer.stop()
    
    @pytest.mark.asyncio
    async def test_memory_usage_optimization(self):
        """测试内存使用优化"""
        logger.info("测试内存使用优化...")
        
        # 创建不同配置的优化器进行对比
        configs = [
            {
                'name': '基础配置',
                'config': LoggingOptimizationConfig(
                    deduplication_enabled=False,
                    level_optimization_enabled=False,
                    batch_processing=False
                )
            },
            {
                'name': '优化配置',
                'config': LoggingOptimizationConfig(
                    deduplication_enabled=True,
                    deduplication_window_seconds=30,
                    level_optimization_enabled=True,
                    batch_processing=True,
                    batch_size=50
                )
            }
        ]
        
        memory_results = []
        
        for config_info in configs:
            logger.info(f"测试配置: {config_info['name']}")
            
            # 创建优化器
            optimizer = LoggingOptimizer(config_info['config'])
            await optimizer.start()
            
            try:
                # 开始内存监控
                initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
                peak_memory = initial_memory
                
                # 生成大量日志消息
                message_count = 5000
                
                for i in range(message_count):
                    # 生成重复性较高的消息
                    message = f"Processing item {i % 100} in batch {i // 100}"
                    optimizer.should_log(message, 'INFO')
                    
                    # 定期检查内存使用
                    if i % 500 == 0:
                        current_memory = psutil.Process().memory_info().rss / 1024 / 1024
                        peak_memory = max(peak_memory, current_memory)
                
                # 最终内存检查
                final_memory = psutil.Process().memory_info().rss / 1024 / 1024
                peak_memory = max(peak_memory, final_memory)
                
                # 获取优化器内部统计
                optimizer_stats = optimizer.get_optimization_statistics()
                
                result = {
                    'config_name': config_info['name'],
                    'initial_memory': initial_memory,
                    'peak_memory': peak_memory,
                    'final_memory': final_memory,
                    'memory_growth': final_memory - initial_memory,
                    'peak_growth': peak_memory - initial_memory,
                    'message_count': message_count,
                    'optimizer_stats': optimizer_stats
                }
                
                memory_results.append(result)
                
                logger.info(f"  初始内存: {initial_memory:.2f}MB")
                logger.info(f"  峰值内存: {peak_memory:.2f}MB")
                logger.info(f"  最终内存: {final_memory:.2f}MB")
                logger.info(f"  内存增长: {result['memory_growth']:.2f}MB")
                logger.info(f"  峰值增长: {result['peak_growth']:.2f}MB")
            
            finally:
                await optimizer.stop()
                
                # 强制垃圾回收
                gc.collect()
                await asyncio.sleep(0.1)
        
        # 比较内存使用
        basic_result = memory_results[0]
        optimized_result = memory_results[1]
        
        memory_improvement = (basic_result['memory_growth'] - optimized_result['memory_growth']) / basic_result['memory_growth']
        
        logger.info(f"\n内存优化效果:")
        logger.info(f"  基础配置内存增长: {basic_result['memory_growth']:.2f}MB")
        logger.info(f"  优化配置内存增长: {optimized_result['memory_growth']:.2f}MB")
        logger.info(f"  内存优化率: {memory_improvement:.1%}")
        
        # 验证内存优化效果
        assert memory_improvement > 0, f"内存优化无效果: {memory_improvement}"
        assert optimized_result['memory_growth'] < basic_result['memory_growth'], \
            "优化配置内存使用应该更少"
        
        return memory_results
    
    def test_logging_performance_benchmark(self):
        """日志性能基准测试"""
        logger.info("执行日志性能基准测试...")
        
        # 基准测试参数
        benchmark_params = {
            'message_count': 10000,
            'unique_messages': 100,
            'iterations': 3
        }
        
        # 创建测试消息
        unique_messages = [
            f"Benchmark message {i} with timestamp {{timestamp}}" 
            for i in range(benchmark_params['unique_messages'])
        ]
        
        benchmark_results = []
        
        for iteration in range(benchmark_params['iterations']):
            logger.info(f"基准测试迭代 {iteration + 1}/{benchmark_params['iterations']}")
            
            # 创建日志去重器
            deduplicator = LogDeduplicator(window_seconds=60)
            
            # 开始基准测试
            start_time = time.perf_counter()
            start_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            processed_count = 0
            
            for i in range(benchmark_params['message_count']):
                # 选择消息模板
                template = unique_messages[i % len(unique_messages)]
                message = template.format(timestamp=time.time())
                
                # 处理消息
                if deduplicator.should_log(message, 'INFO'):
                    processed_count += 1
                
                deduplicator.add_message(message, 'INFO')
            
            end_time = time.perf_counter()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            # 计算性能指标
            duration = end_time - start_time
            messages_per_second = benchmark_params['message_count'] / duration
            memory_usage = end_memory - start_memory
            
            result = {
                'iteration': iteration + 1,
                'duration': duration,
                'messages_per_second': messages_per_second,
                'processed_count': processed_count,
                'memory_usage': memory_usage,
                'deduplication_ratio': 1 - (processed_count / benchmark_params['message_count'])
            }
            
            benchmark_results.append(result)
            
            logger.info(f"  持续时间: {duration:.3f}秒")
            logger.info(f"  处理速度: {messages_per_second:.0f} 消息/秒")
            logger.info(f"  内存使用: {memory_usage:.2f}MB")
            logger.info(f"  去重率: {result['deduplication_ratio']:.1%}")
        
        # 计算平均性能
        avg_duration = sum(r['duration'] for r in benchmark_results) / len(benchmark_results)
        avg_messages_per_second = sum(r['messages_per_second'] for r in benchmark_results) / len(benchmark_results)
        avg_memory_usage = sum(r['memory_usage'] for r in benchmark_results) / len(benchmark_results)
        avg_deduplication_ratio = sum(r['deduplication_ratio'] for r in benchmark_results) / len(benchmark_results)
        
        logger.info(f"\n基准测试平均结果:")
        logger.info(f"  平均持续时间: {avg_duration:.3f}秒")
        logger.info(f"  平均处理速度: {avg_messages_per_second:.0f} 消息/秒")
        logger.info(f"  平均内存使用: {avg_memory_usage:.2f}MB")
        logger.info(f"  平均去重率: {avg_deduplication_ratio:.1%}")
        
        # 验证基准性能
        assert avg_messages_per_second > 2000, f"基准性能不足: {avg_messages_per_second} 消息/秒"
        assert avg_memory_usage < 50, f"基准内存使用过高: {avg_memory_usage}MB"
        assert avg_deduplication_ratio > 0.8, f"去重效果不佳: {avg_deduplication_ratio}"
        
        return {
            'benchmark_results': benchmark_results,
            'averages': {
                'duration': avg_duration,
                'messages_per_second': avg_messages_per_second,
                'memory_usage': avg_memory_usage,
                'deduplication_ratio': avg_deduplication_ratio
            }
        }


if __name__ == '__main__':
    pytest.main([__file__, '-v'])