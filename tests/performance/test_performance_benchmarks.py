logger = logging.getLogger(__name__)
"""
性能基准测试

本测试模块用于验证系统性能优化的效果，通过基准测试确保系统在各种负载下
都能保持良好的性能表现。

测试覆盖范围：
1. 系统启动性能基准测试
2. API响应时间基准测试
3. 数据库查询性能基准测试
4. 内存使用效率基准测试
5. 并发处理能力基准测试
6. 缓存系统性能基准测试

作者: 系统修复团队
版本: 1.0.0
创建日期: 2025-01-11
"""

import pytest
import asyncio
import time
import os
import tempfile
import json
import statistics
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging

# 导入性能相关组件
from src.core.performance_optimizer import PerformanceOptimizer
from src.core.application_memory_optimizer import ApplicationMemoryOptimizer
from src.core.cache_manager import CacheManager
from src.core.application_resource_monitor import ApplicationResourceMonitor


class TestPerformanceAndStability:
    """性能和稳定性验证测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.performance_thresholds = {
            'startup_time': 30.0,  # 系统启动时间不超过30秒
            'api_response_time': 2.0,  # API响应时间不超过2秒
            'db_query_time': 1.0,  # 数据库查询时间不超过1秒
            'memory_usage_limit': 80.0,  # 内存使用率不超过80%
            'cpu_usage_limit': 70.0,  # CPU使用率不超过70%
            'cache_hit_rate': 85.0,  # 缓存命中率不低于85%
            'throughput_min': 100.0,  # 最小吞吐量100 req/s
            'concurrent_users': 50  # 支持50个并发用户
        }
        
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_performance_benchmark_tests(self):
        """性能基准测试"""
        logger.info("⚡ 执行性能基准测试...")
        
        # 性能基准测试项目
        benchmark_tests = [
            self._benchmark_system_startup,
            self._benchmark_api_performance,
            self._benchmark_database_performance,
            self._benchmark_memory_efficiency,
            self._benchmark_concurrent_processing,
            self._benchmark_cache_performance
        ]
        
        benchmark_results = {}
        
        for benchmark_func in benchmark_tests:
            benchmark_name = benchmark_func.__name__.replace('_benchmark_', '')
            logger.info(f"  🔧 执行基准测试: {benchmark_name}")
            
            start_time = time.time()
            result = await benchmark_func()
            end_time = time.time()
            
            benchmark_results[benchmark_name] = {
                'result': result,
                'duration': end_time - start_time,
                'timestamp': datetime.now().isoformat()
            }
            
            if result['passed']:
                logger.info(f"  ✅ {benchmark_name} 基准测试通过")
            else:
                logger.info(f"  ❌ {benchmark_name} 基准测试失败")
        
        # 验证所有基准测试都通过
        failed_benchmarks = [
            name for name, result in benchmark_results.items()
            if not result['result']['passed']
        ]
        
        assert len(failed_benchmarks) == 0, f"性能基准测试失败: {failed_benchmarks}"
        
        logger.info("✅ 所有性能基准测试通过")
    
    async def _benchmark_system_startup(self) -> Dict:
        """系统启动性能基准测试"""
        logger.info("    🚀 测试系统启动性能...")
        
        startup_times = []
        
        # 执行多次启动测试
        for i in range(3):
            start_time = time.time()
            
            # 模拟系统启动过程
            await self._simulate_system_startup()
            
            end_time = time.time()
            startup_time = end_time - start_time
            startup_times.append(startup_time)
            
            logger.info(f"      第 {i+1} 次启动: {startup_time:.2f} 秒")
        
        # 计算统计数据
        avg_startup_time = statistics.mean(startup_times)
        
        # 判断是否通过基准测试
        threshold = self.performance_thresholds['startup_time']
        passed = avg_startup_time <= threshold
        
        return {
            'passed': passed,
            'metrics': {
                'average_startup_time': avg_startup_time,
                'threshold': threshold,
                'all_startup_times': startup_times
            }
        }
    
    async def _simulate_system_startup(self):
        """模拟系统启动过程"""
        # 模拟各个启动阶段
        startup_phases = [
            ('初始化配置', 0.1),
            ('启动数据库连接', 0.2),
            ('加载缓存', 0.15),
            ('启动API服务', 0.3),
            ('启动监控系统', 0.1),
            ('系统就绪检查', 0.05)
        ]
        
        for phase_name, duration in startup_phases:
            await asyncio.sleep(duration)
    
    async def _benchmark_api_performance(self) -> Dict:
        """API性能基准测试"""
        logger.info("    📡 测试API性能...")
        
        # 模拟不同类型的API请求
        api_endpoints = [
            {'name': 'get_user_info', 'complexity': 'low'},
            {'name': 'get_system_status', 'complexity': 'medium'},
            {'name': 'generate_report', 'complexity': 'high'}
        ]
        
        api_results = {}
        
        for endpoint in api_endpoints:
            response_times = []
            
            # 对每个端点执行多次请求
            for i in range(10):
                start_time = time.time()
                
                # 模拟API请求处理
                await self._simulate_api_request(endpoint)
                
                end_time = time.time()
                response_time = end_time - start_time
                response_times.append(response_time)
            
            # 计算统计数据
            avg_response_time = statistics.mean(response_times)
            
            api_results[endpoint['name']] = {
                'average_response_time': avg_response_time,
                'all_response_times': response_times
            }
            
            logger.info(f"      {endpoint['name']}: 平均 {avg_response_time:.3f}s")
        
        # 计算整体API性能
        all_avg_times = [result['average_response_time'] for result in api_results.values()]
        overall_avg_time = statistics.mean(all_avg_times)
        
        # 判断是否通过基准测试
        threshold = self.performance_thresholds['api_response_time']
        passed = overall_avg_time <= threshold
        
        return {
            'passed': passed,
            'metrics': {
                'overall_average_response_time': overall_avg_time,
                'threshold': threshold,
                'endpoint_results': api_results
            }
        }
    
    async def _simulate_api_request(self, endpoint: Dict):
        """模拟API请求处理"""
        complexity_delays = {
            'low': 0.05,
            'medium': 0.15,
            'high': 0.3
        }
        
        base_delay = complexity_delays.get(endpoint['complexity'], 0.1)
        
        # 添加一些随机变化
        import random
        actual_delay = base_delay * (0.8 + 0.4 * random.random())
        
        await asyncio.sleep(actual_delay)
    
    async def _benchmark_database_performance(self) -> Dict:
        """数据库性能基准测试"""
        logger.info("    🗄️ 测试数据库性能...")
        
        # 模拟不同类型的数据库操作
        db_operations = [
            {'name': 'simple_select', 'type': 'read', 'complexity': 'low'},
            {'name': 'complex_join', 'type': 'read', 'complexity': 'high'},
            {'name': 'insert_record', 'type': 'write', 'complexity': 'low'}
        ]
        
        db_results = {}
        
        for operation in db_operations:
            query_times = []
            
            # 对每个操作执行多次测试
            for i in range(10):
                start_time = time.time()
                
                # 模拟数据库操作
                await self._simulate_db_operation(operation)
                
                end_time = time.time()
                query_time = end_time - start_time
                query_times.append(query_time)
            
            # 计算统计数据
            avg_query_time = statistics.mean(query_times)
            
            db_results[operation['name']] = {
                'average_query_time': avg_query_time,
                'all_query_times': query_times
            }
            
            logger.info(f"      {operation['name']}: 平均 {avg_query_time:.3f}s")
        
        # 计算整体数据库性能
        all_avg_times = [result['average_query_time'] for result in db_results.values()]
        overall_avg_time = statistics.mean(all_avg_times)
        
        # 判断是否通过基准测试
        threshold = self.performance_thresholds['db_query_time']
        passed = overall_avg_time <= threshold
        
        return {
            'passed': passed,
            'metrics': {
                'overall_average_query_time': overall_avg_time,
                'threshold': threshold,
                'operation_results': db_results
            }
        }
    
    async def _simulate_db_operation(self, operation: Dict):
        """模拟数据库操作"""
        complexity_delays = {
            'low': 0.02,
            'medium': 0.08,
            'high': 0.2
        }
        
        base_delay = complexity_delays.get(operation['complexity'], 0.05)
        
        # 写操作通常比读操作慢一些
        if operation['type'] == 'write':
            base_delay *= 1.5
        
        # 添加一些随机变化
        import random
        actual_delay = base_delay * (0.7 + 0.6 * random.random())
        
        await asyncio.sleep(actual_delay)
    
    async def _benchmark_memory_efficiency(self) -> Dict:
        """内存效率基准测试"""
        logger.info("    🧠 测试内存效率...")
        
        # 模拟内存密集型操作
        memory_operations = [
            self._simulate_large_data_processing,
            self._simulate_cache_operations,
            self._simulate_memory_cleanup
        ]
        
        memory_measurements = []
        
        for operation in memory_operations:
            operation_name = operation.__name__.replace('_simulate_', '')
            logger.info(f"      执行内存操作: {operation_name}")
            
            # 执行操作
            await operation()
            
            memory_measurements.append({
                'operation': operation_name,
                'completed': True
            })
        
        # 判断是否通过基准测试
        passed = len(memory_measurements) == len(memory_operations)
        
        return {
            'passed': passed,
            'metrics': {
                'completed_operations': len(memory_measurements),
                'total_operations': len(memory_operations),
                'operation_measurements': memory_measurements
            }
        }
    
    async def _simulate_large_data_processing(self):
        """模拟大数据处理"""
        # 创建大量数据对象
        large_data = []
        for i in range(1000):
            large_data.append({
                'id': i,
                'data': f'data_item_{i}' * 10,
                'timestamp': datetime.now().isoformat()
            })
        
        # 模拟数据处理
        await asyncio.sleep(0.01)
        
        # 清理数据
        del large_data
    
    async def _simulate_cache_operations(self):
        """模拟缓存操作"""
        cache_data = {}
        
        # 填充缓存
        for i in range(500):
            cache_data[f'key_{i}'] = f'value_{i}' * 20
        
        # 模拟缓存访问
        await asyncio.sleep(0.01)
        
        # 清理缓存
        cache_data.clear()
    
    async def _simulate_memory_cleanup(self):
        """模拟内存清理"""
        # 强制垃圾回收
        import gc
        gc.collect()
        await asyncio.sleep(0.01)
    
    async def _benchmark_concurrent_processing(self) -> Dict:
        """并发处理能力基准测试"""
        logger.info("    ⚡ 测试并发处理能力...")
        
        # 测试不同并发级别
        concurrency_levels = [10, 25, 50]
        concurrency_results = {}
        
        for concurrency in concurrency_levels:
            logger.info(f"      测试并发级别: {concurrency}")
            
            start_time = time.time()
            
            # 创建并发任务
            tasks = [
                self._simulate_concurrent_task(i) 
                for i in range(concurrency)
            ]
            
            # 执行并发任务
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # 统计成功和失败的任务
            successful_tasks = sum(1 for result in results if not isinstance(result, Exception))
            
            # 计算吞吐量
            throughput = successful_tasks / total_time if total_time > 0 else 0
            
            concurrency_results[concurrency] = {
                'total_time': total_time,
                'successful_tasks': successful_tasks,
                'throughput': throughput,
                'success_rate': successful_tasks / len(results) * 100
            }
            
            logger.info(f"        成功: {successful_tasks}/{concurrency}, 吞吐量: {throughput:.1f} tasks/s")
        
        # 找到最大支持的并发级别
        max_supported_concurrency = 0
        for concurrency, result in concurrency_results.items():
            if result['success_rate'] >= 95:  # 95%成功率
                max_supported_concurrency = concurrency
        
        # 判断是否通过基准测试
        threshold = self.performance_thresholds['concurrent_users']
        passed = max_supported_concurrency >= threshold
        
        return {
            'passed': passed,
            'metrics': {
                'max_supported_concurrency': max_supported_concurrency,
                'threshold': threshold,
                'concurrency_results': concurrency_results
            }
        }
    
    async def _simulate_concurrent_task(self, task_id: int):
        """模拟并发任务"""
        # 模拟任务处理时间
        import random
        processing_time = 0.1 + random.random() * 0.2  # 0.1-0.3秒
        
        await asyncio.sleep(processing_time)
        
        # 模拟偶尔的任务失败
        if random.random() < 0.02:  # 2%失败率
            raise Exception(f"Task {task_id} failed")
        
        return f"Task {task_id} completed"
    
    async def _benchmark_cache_performance(self) -> Dict:
        """缓存性能基准测试"""
        logger.info("    💾 测试缓存性能...")
        
        # 创建缓存管理器
        cache_manager = CacheManager()
        
        # 模拟缓存操作
        cache_hits = 0
        cache_misses = 0
        
        # 预填充缓存
        with patch.object(cache_manager, 'set') as mock_set:
            mock_set.return_value = True
            for i in range(50):
                key = f'cache_key_{i}'
                value = f'cache_value_{i}' * 10
                await cache_manager.set(key, value)
        
        # 执行缓存访问测试
        access_times = []
        
        with patch.object(cache_manager, 'get') as mock_get:
            for i in range(100):
                start_time = time.time()
                
                # 80%的概率访问已存在的键，20%访问新键
                import random
                if random.random() < 0.8:
                    # 访问已存在的键
                    key = f'cache_key_{random.randint(0, 49)}'
                    mock_get.return_value = f'cache_value_{random.randint(0, 49)}' * 10
                    result = await cache_manager.get(key)
                    if result is not None:
                        cache_hits += 1
                    else:
                        cache_misses += 1
                else:
                    # 访问新键
                    key = f'new_cache_key_{i}'
                    mock_get.return_value = None
                    result = await cache_manager.get(key)
                    cache_misses += 1
                
                end_time = time.time()
                access_time = end_time - start_time
                access_times.append(access_time)
        
        # 计算缓存统计
        total_accesses = cache_hits + cache_misses
        cache_hit_rate = (cache_hits / total_accesses * 100) if total_accesses > 0 else 0
        avg_access_time = statistics.mean(access_times)
        
        logger.info(f"      缓存命中率: {cache_hit_rate:.1f}%")
        logger.info(f"      平均访问时间: {avg_access_time:.4f}s")
        
        # 判断是否通过基准测试
        threshold = self.performance_thresholds['cache_hit_rate']
        passed = cache_hit_rate >= threshold
        
        return {
            'passed': passed,
            'metrics': {
                'cache_hit_rate': cache_hit_rate,
                'cache_hits': cache_hits,
                'cache_misses': cache_misses,
                'total_accesses': total_accesses,
                'average_access_time': avg_access_time,
                'threshold': threshold
            }
        }
    
    @pytest.mark.asyncio
    async def test_stability_stress_tests(self):
        """稳定性压力测试"""
        logger.info("🛡️ 执行稳定性压力测试...")
        
        # 稳定性测试项目
        stability_tests = [
            self._stress_test_system_load,
            self._test_error_recovery_stability,
            self._test_long_running_stability
        ]
        
        stability_results = {}
        
        for test_func in stability_tests:
            test_name = test_func.__name__.replace('_test_', '').replace('_stress_test_', '')
            logger.info(f"  🔧 执行稳定性测试: {test_name}")
            
            start_time = time.time()
            result = await test_func()
            end_time = time.time()
            
            stability_results[test_name] = {
                'result': result,
                'duration': end_time - start_time,
                'timestamp': datetime.now().isoformat()
            }
            
            if result['passed']:
                logger.info(f"  ✅ {test_name} 稳定性测试通过")
            else:
                logger.info(f"  ❌ {test_name} 稳定性测试失败")
        
        # 验证所有稳定性测试都通过
        failed_tests = [
            name for name, result in stability_results.items()
            if not result['result']['passed']
        ]
        
        assert len(failed_tests) == 0, f"稳定性测试失败: {failed_tests}"
        
        logger.info("✅ 所有稳定性测试通过")
    
    async def _stress_test_system_load(self) -> Dict:
        """系统负载压力测试"""
        logger.info("    💪 执行系统负载压力测试...")
        
        # 压力测试配置
        stress_duration = 30  # 30秒压力测试
        load_levels = [0.5, 0.7, 0.9]  # 不同负载级别
        
        stress_results = {}
        
        for load_level in load_levels:
            logger.info(f"      测试负载级别: {load_level}x")
            
            # 计算并发任务数
            base_concurrency = 10
            concurrency = int(base_concurrency * load_level)
            
            start_time = time.time()
            errors = 0
            completed_tasks = 0
            
            # 创建压力测试任务
            async def stress_task():
                nonlocal errors, completed_tasks
                try:
                    # 模拟工作负载
                    await self._simulate_workload()
                    completed_tasks += 1
                except Exception:
                    errors += 1
            
            # 持续执行压力测试
            end_time = start_time + stress_duration
            
            while time.time() < end_time:
                # 创建一批并发任务
                tasks = [stress_task() for _ in range(concurrency)]
                await asyncio.gather(*tasks, return_exceptions=True)
                
                # 短暂休息
                await asyncio.sleep(0.1)
            
            total_time = time.time() - start_time
            total_tasks = completed_tasks + errors
            error_rate = (errors / total_tasks * 100) if total_tasks > 0 else 0
            
            stress_results[load_level] = {
                'duration': total_time,
                'completed_tasks': completed_tasks,
                'errors': errors,
                'error_rate': error_rate
            }
            
            logger.info(f"        完成任务: {completed_tasks}, 错误: {errors}, 错误率: {error_rate:.2f}%")
        
        # 找到最大支持的负载级别
        max_stable_load = 0
        for load_level, result in stress_results.items():
            if result['error_rate'] <= 1.0:  # 错误率不超过1%
                max_stable_load = load_level
        
        # 判断是否通过稳定性测试
        passed = max_stable_load >= 0.7  # 至少支持0.7x负载
        
        return {
            'passed': passed,
            'metrics': {
                'max_stable_load': max_stable_load,
                'stress_results': stress_results
            }
        }
    
    async def _simulate_workload(self):
        """模拟工作负载"""
        # 模拟CPU密集型任务
        import random
        work_duration = 0.01 + random.random() * 0.02  # 10-30ms
        
        # 模拟一些计算工作
        start = time.time()
        while time.time() - start < work_duration:
            # 简单的计算任务
            _ = sum(i * i for i in range(100))
        
        # 模拟I/O操作
        await asyncio.sleep(0.001)
    
    async def _test_error_recovery_stability(self) -> Dict:
        """错误恢复稳定性测试"""
        logger.info("    🔄 执行错误恢复稳定性测试...")
        
        # 测试不同类型的错误恢复
        error_scenarios = [
            {'type': 'connection_error', 'frequency': 0.1},
            {'type': 'timeout_error', 'frequency': 0.05}
        ]
        
        recovery_results = {}
        
        for scenario in error_scenarios:
            logger.info(f"      测试错误恢复: {scenario['type']}")
            
            total_operations = 50
            successful_recoveries = 0
            
            for i in range(total_operations):
                try:
                    # 根据频率决定是否触发错误
                    import random
                    if random.random() < scenario['frequency']:
                        # 触发错误
                        await self._simulate_error(scenario['type'])
                        
                        # 模拟错误恢复
                        recovery_success = await self._simulate_error_recovery(scenario['type'])
                        
                        if recovery_success:
                            successful_recoveries += 1
                    else:
                        # 正常操作
                        await asyncio.sleep(0.01)
                        successful_recoveries += 1
                        
                except Exception:
                    # 恢复失败
                    pass
            
            # 计算恢复统计
            recovery_rate = successful_recoveries / total_operations * 100
            
            recovery_results[scenario['type']] = {
                'total_operations': total_operations,
                'successful_recoveries': successful_recoveries,
                'recovery_rate': recovery_rate
            }
            
            logger.info(f"        恢复率: {recovery_rate:.1f}%")
        
        # 计算整体恢复稳定性
        overall_recovery_rate = statistics.mean([
            result['recovery_rate'] for result in recovery_results.values()
        ])
        
        # 判断是否通过稳定性测试
        passed = overall_recovery_rate >= 90.0  # 要求90%以上的恢复率
        
        return {
            'passed': passed,
            'metrics': {
                'overall_recovery_rate': overall_recovery_rate,
                'recovery_results': recovery_results
            }
        }
    
    async def _simulate_error(self, error_type: str):
        """模拟错误"""
        error_types = {
            'connection_error': ConnectionError("Connection lost"),
            'timeout_error': TimeoutError("Operation timeout")
        }
        
        error = error_types.get(error_type, Exception("Unknown error"))
        raise error
    
    async def _simulate_error_recovery(self, error_type: str) -> bool:
        """模拟错误恢复"""
        recovery_strategies = {
            'connection_error': 0.1,  # 重连需要100ms
            'timeout_error': 0.05     # 重试需要50ms
        }
        
        recovery_time = recovery_strategies.get(error_type, 0.1)
        await asyncio.sleep(recovery_time)
        
        # 90%的恢复成功率
        import random
        return random.random() < 0.9
    
    async def _test_long_running_stability(self) -> Dict:
        """长时间运行稳定性测试"""
        logger.info("    ⏱️ 执行长时间运行稳定性测试...")
        
        # 长时间运行测试配置
        test_duration = 60  # 1分钟测试
        monitoring_interval = 10  # 每10秒监控一次
        
        start_time = time.time()
        end_time = start_time + test_duration
        
        stability_metrics = []
        error_count = 0
        
        # 启动长时间运行的任务
        long_running_task = asyncio.create_task(self._long_running_workload())
        
        # 定期监控系统状态
        while time.time() < end_time:
            current_time = time.time()
            
            try:
                # 收集系统指标
                stability_metrics.append({
                    'timestamp': current_time,
                    'elapsed_time': current_time - start_time,
                    'status': 'running'
                })
                
                logger.info(f"      运行时间: {current_time - start_time:.0f}s")
                
            except Exception as e:
                error_count += 1
                logger.info(f"      监控错误: {e}")
            
            await asyncio.sleep(monitoring_interval)
        
        # 停止长时间运行的任务
        long_running_task.cancel()
        
        try:
            await long_running_task
        except asyncio.CancelledError:
            pass
        
        # 判断是否通过长时间运行稳定性测试
        passed = error_count == 0 and len(stability_metrics) > 0
        
        return {
            'passed': passed,
            'metrics': {
                'test_duration': test_duration,
                'actual_duration': time.time() - start_time,
                'error_count': error_count,
                'stability_samples': len(stability_metrics)
            }
        }
    
    async def _long_running_workload(self):
        """长时间运行的工作负载"""
        try:
            while True:
                # 模拟持续的工作负载
                await self._simulate_workload()
                await asyncio.sleep(0.1)
        except asyncio.CancelledError:
            logger.info("      长时间运行任务已停止")
            raise
    
    @pytest.mark.asyncio
    async def test_resource_usage_tests(self):
        """资源使用测试"""
        logger.info("📊 执行资源使用测试...")
        
        # 资源使用测试项目
        resource_tests = [
            self._test_cpu_usage,
            self._test_memory_usage,
            self._test_disk_usage
        ]
        
        resource_results = {}
        
        for test_func in resource_tests:
            test_name = test_func.__name__.replace('_test_', '')
            logger.info(f"  🔧 测试资源使用: {test_name}")
            
            # 执行资源使用测试
            result = await test_func()
            resource_results[test_name] = result
            
            logger.info(f"    {test_name} 测试完成")
        
        # 验证资源使用在合理范围内
        for test_name, result in resource_results.items():
            assert result['success'], f"{test_name} 资源使用测试失败"
        
        logger.info("✅ 资源使用测试通过")
    
    async def _test_cpu_usage(self) -> Dict:
        """测试CPU使用"""
        # 模拟CPU密集型任务
        start_time = time.time()
        while time.time() - start_time < 0.5:  # 运行0.5秒
            # 简单的计算任务
            _ = sum(i * i for i in range(1000))
        
        return {'success': True, 'duration': time.time() - start_time}
    
    async def _test_memory_usage(self) -> Dict:
        """测试内存使用"""
        # 模拟内存分配
        large_data = []
        for i in range(1000):
            large_data.append(f'data_item_{i}' * 10)
        
        # 短暂保持数据
        await asyncio.sleep(0.01)
        
        # 清理数据
        del large_data
        import gc
        gc.collect()
        
        return {'success': True, 'allocated_items': 1000}
    
    async def _test_disk_usage(self) -> Dict:
        """测试磁盘使用"""
        # 模拟磁盘I/O
        temp_file = Path(self.temp_dir) / 'disk_test.txt'
        
        # 写入数据
        with open(temp_file, 'w') as f:
            for i in range(100):
                f.write(f'line {i}: test data for disk usage\n')
        
        # 读取数据
        with open(temp_file, 'r') as f:
            content = f.read()
        
        # 清理文件
        temp_file.unlink(missing_ok=True)
        
        return {'success': True, 'content_length': len(content)}
    
    @pytest.mark.asyncio
    async def test_scalability_tests(self):
        """可扩展性测试"""
        logger.info("📈 执行可扩展性测试...")
        
        # 测试不同规模的负载
        scale_levels = [
            {'users': 10, 'duration': 15},
            {'users': 25, 'duration': 15},
            {'users': 50, 'duration': 15}
        ]
        
        scalability_results = {}
        
        for scale in scale_levels:
            logger.info(f"  🔧 测试规模: {scale['users']} 用户")
            
            start_time = time.time()
            
            # 创建模拟用户任务
            user_tasks = [
                self._simulate_user_activity(user_id, scale['duration'])
                for user_id in range(scale['users'])
            ]
            
            # 执行所有用户任务
            results = await asyncio.gather(*user_tasks, return_exceptions=True)
            
            end_time = time.time()
            actual_duration = end_time - start_time
            
            # 统计结果
            successful_users = sum(1 for result in results if not isinstance(result, Exception))
            success_rate = successful_users / len(results) * 100
            
            scalability_results[scale['users']] = {
                'successful_users': successful_users,
                'success_rate': success_rate,
                'actual_duration': actual_duration,
                'expected_duration': scale['duration']
            }
            
            logger.info(f"    成功用户: {successful_users}/{scale['users']} ({success_rate:.1f}%)")
        
        # 验证可扩展性
        for users, result in scalability_results.items():
            assert result['success_rate'] >= 90.0, \
                f"规模 {users} 用户时成功率过低: {result['success_rate']}%"
        
        logger.info("✅ 可扩展性测试通过")
    
    async def _simulate_user_activity(self, user_id: int, duration: int):
        """模拟用户活动"""
        start_time = time.time()
        end_time = start_time + duration
        
        operations_completed = 0
        
        while time.time() < end_time:
            try:
                # 模拟用户操作
                await self._simulate_user_operation(user_id)
                operations_completed += 1
                
                # 用户操作间隔
                await asyncio.sleep(0.1)
                
            except Exception:
                # 记录错误但继续执行
                pass
        
        return operations_completed
    
    async def _simulate_user_operation(self, user_id: int):
        """模拟单个用户操作"""
        # 模拟不同类型的用户操作
        import random
        
        operation_types = [
            ('查看数据', 0.05),
            ('更新信息', 0.1),
            ('搜索内容', 0.08)
        ]
        
        operation_name, duration = random.choice(operation_types)
        
        # 模拟操作处理时间
        await asyncio.sleep(duration)
        
        return operation_name


if __name__ == '__main__':
    pytest.main([__file__, '-v'])