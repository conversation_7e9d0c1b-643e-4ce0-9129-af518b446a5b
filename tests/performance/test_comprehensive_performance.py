import logging
logger = logging.getLogger(__name__)
"""
综合性能测试套件

测试所有系统优化的综合性能效果
"""

import pytest
import asyncio
import time
import tempfile
import os
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import psutil
import gc
from concurrent.futures import ThreadPoolExecutor
import threading

from src.monitoring.logging_optimizer import LoggingOptimizer, LoggingOptimizationConfig
from src.monitoring.monitoring_optimizer import MonitoringOptimizer, MonitoringOptimizationConfig, OptimizationLevel
from src.core.startup_manager import StartupManager
from src.core.enhanced_health_checker import HealthChecker
from src.error_handling.enhanced_error_handler import Enhanced<PERSON>rror<PERSON><PERSON><PERSON>, ErrorHandlingConfig


class SystemPerformanceProfiler:
    """系统性能分析器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.cpu_samples = []
        self.memory_samples = []
        self.operation_metrics = {}
        self.component_metrics = {}
        self.monitoring_active = False
    
    def start_profiling(self):
        """开始性能分析"""
        self.start_time = time.time()
        self.monitoring_active = True
        
        # 启动资源监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_system_resources)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_profiling(self):
        """停止性能分析"""
        self.end_time = time.time()
        self.monitoring_active = False
        
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=2.0)
    
    def _monitor_system_resources(self):
        """监控系统资源"""
        process = psutil.Process()
        
        while self.monitoring_active:
            try:
                # CPU和内存采样
                cpu_percent = process.cpu_percent()
                memory_info = process.memory_info()
                
                timestamp = time.time()
                
                self.cpu_samples.append({
                    'timestamp': timestamp,
                    'cpu_percent': cpu_percent
                })
                
                self.memory_samples.append({
                    'timestamp': timestamp,
                    'rss_mb': memory_info.rss / 1024 / 1024,
                    'vms_mb': memory_info.vms / 1024 / 1024
                })
                
                time.sleep(0.2)  # 每200ms采样
                
            except Exception:
                break
    
    def record_operation_metric(self, component, operation, duration, success=True, metadata=None):
        """记录操作指标"""
        if component not in self.operation_metrics:
            self.operation_metrics[component] = []
        
        metric = {
            'operation': operation,
            'duration': duration,
            'success': success,
            'timestamp': time.time(),
            'metadata': metadata or {}
        }
        
        self.operation_metrics[component].append(metric)
    
    def record_component_metric(self, component, metric_name, value, unit='count'):
        """记录组件指标"""
        if component not in self.component_metrics:
            self.component_metrics[component] = {}
        
        if metric_name not in self.component_metrics[component]:
            self.component_metrics[component][metric_name] = []
        
        self.component_metrics[component][metric_name].append({
            'value': value,
            'unit': unit,
            'timestamp': time.time()
        })
    
    def get_performance_report(self):
        """获取性能报告"""
        total_time = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        # CPU统计
        cpu_stats = {}
        if self.cpu_samples:
            cpu_values = [s['cpu_percent'] for s in self.cpu_samples]
            cpu_stats = {
                'avg_cpu': sum(cpu_values) / len(cpu_values),
                'max_cpu': max(cpu_values),
                'min_cpu': min(cpu_values),
                'samples_count': len(cpu_values)
            }
        
        # 内存统计
        memory_stats = {}
        if self.memory_samples:
            rss_values = [s['rss_mb'] for s in self.memory_samples]
            memory_stats = {
                'avg_memory_mb': sum(rss_values) / len(rss_values),
                'max_memory_mb': max(rss_values),
                'min_memory_mb': min(rss_values),
                'memory_growth_mb': rss_values[-1] - rss_values[0] if len(rss_values) > 1 else 0,
                'samples_count': len(rss_values)
            }
        
        # 操作统计
        operation_stats = {}
        for component, operations in self.operation_metrics.items():
            if operations:
                durations = [op['duration'] for op in operations]
                success_count = sum(1 for op in operations if op['success'])
                
                operation_stats[component] = {
                    'total_operations': len(operations),
                    'success_rate': success_count / len(operations),
                    'avg_duration': sum(durations) / len(durations),
                    'max_duration': max(durations),
                    'min_duration': min(durations)
                }
        
        return {
            'total_time': total_time,
            'cpu_stats': cpu_stats,
            'memory_stats': memory_stats,
            'operation_stats': operation_stats,
            'component_metrics': self.component_metrics
        }


class TestComprehensivePerformance:
    """综合性能测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.profiler = SystemPerformanceProfiler()
        
        # 创建组件
        self.logging_optimizer = None
        self.monitoring_optimizer = None
        self.startup_manager = None
        self.health_checker = None
        self.error_handler = None
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_system_startup_performance(self):
        """测试系统启动性能"""
        logger.info("测试系统启动综合性能...")
        
        # 开始性能分析
        self.profiler.start_profiling()
        
        try:
            # 创建系统组件
            startup_start = time.time()
            
            # 1. 创建错误处理器
            error_config = ErrorHandlingConfig(
                report_directory=self.temp_dir,
                batch_processing=True
            )
            self.error_handler = EnhancedErrorHandler(error_config)
            
            # 2. 创建日志优化器
            logging_config = LoggingOptimizationConfig(
                deduplication_enabled=True,
                level_optimization_enabled=True,
                batch_processing=True
            )
            self.logging_optimizer = LoggingOptimizer(logging_config)
            
            # 3. 创建监控优化器
            monitoring_config = MonitoringOptimizationConfig(
                optimization_level=OptimizationLevel.BALANCED,
                enable_adaptive_optimization=True
            )
            self.monitoring_optimizer = MonitoringOptimizer(monitoring_config)
            
            # 4. 创建健康检查器
            self.health_checker = HealthChecker()
            
            # 5. 创建启动管理器
            self.startup_manager = StartupManager()
            
            component_creation_time = time.time() - startup_start
            self.profiler.record_operation_metric(
                'system', 'component_creation', component_creation_time
            )
            
            # 启动所有组件
            startup_tasks = []
            
            # 启动日志优化器
            log_start = time.time()
            await self.logging_optimizer.start()
            log_startup_time = time.time() - log_start
            self.profiler.record_operation_metric(
                'logging_optimizer', 'startup', log_startup_time
            )
            
            # 启动监控优化器
            monitor_start = time.time()
            await self.monitoring_optimizer.start()
            monitor_startup_time = time.time() - monitor_start
            self.profiler.record_operation_metric(
                'monitoring_optimizer', 'startup', monitor_startup_time
            )
            
            total_startup_time = time.time() - startup_start
            
            logger.info(f"组件创建时间: {component_creation_time:.3f}秒")
            logger.info(f"日志优化器启动: {log_startup_time:.3f}秒")
            logger.info(f"监控优化器启动: {monitor_startup_time:.3f}秒")
            logger.info(f"总启动时间: {total_startup_time:.3f}秒")
            
            # 验证启动性能
            assert total_startup_time < 5.0, f"系统启动时间过长: {total_startup_time}秒"
            assert component_creation_time < 2.0, f"组件创建时间过长: {component_creation_time}秒"
            
            return {
                'total_startup_time': total_startup_time,
                'component_creation_time': component_creation_time,
                'log_startup_time': log_startup_time,
                'monitor_startup_time': monitor_startup_time
            }
        
        finally:
            # 清理组件
            if self.logging_optimizer:
                await self.logging_optimizer.stop()
            if self.monitoring_optimizer:
                await self.monitoring_optimizer.stop()
            
            self.profiler.stop_profiling()
    
    @pytest.mark.asyncio
    async def test_integrated_system_performance(self):
        """测试集成系统性能"""
        logger.info("测试集成系统综合性能...")
        
        # 开始性能分析
        self.profiler.start_profiling()
        
        try:
            # 创建和启动所有组件
            await self._setup_all_components()
            
            # 运行综合性能测试
            test_duration = 10  # 10秒测试
            operations_per_second = 50  # 每秒50个操作
            
            logger.info(f"运行{test_duration}秒综合性能测试...")
            
            # 创建测试任务
            async def log_processing_task():
                """日志处理任务"""
                for i in range(test_duration * 10):  # 每秒10条日志
                    message = f"Test log message {i} - Processing data batch"
                    
                    start_time = time.time()
                    should_log = self.logging_optimizer.should_log(message, 'INFO')
                    duration = time.time() - start_time
                    
                    self.profiler.record_operation_metric(
                        'logging_optimizer', 'should_log', duration, should_log
                    )
                    
                    await asyncio.sleep(0.1)
            
            async def monitoring_task():
                """监控任务"""
                for i in range(test_duration * 5):  # 每秒5次监控
                    start_time = time.time()
                    
                    # 模拟监控数据收集
                    monitoring_data = {
                        'cpu_usage': 30 + (i % 40),
                        'memory_usage': 40 + (i % 30),
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    duration = time.time() - start_time
                    
                    self.profiler.record_operation_metric(
                        'monitoring_optimizer', 'collect_data', duration, True, monitoring_data
                    )
                    
                    await asyncio.sleep(0.2)
            
            async def health_check_task():
                """健康检查任务"""
                for i in range(test_duration * 2):  # 每秒2次健康检查
                    start_time = time.time()
                    
                    # 模拟健康检查
                    try:
                        health_status = {
                            'component': f'test_component_{i % 3}',
                            'status': 'healthy' if i % 10 != 0 else 'degraded',
                            'response_time': 50 + (i % 100)
                        }
                        
                        duration = time.time() - start_time
                        success = health_status['status'] == 'healthy'
                        
                        self.profiler.record_operation_metric(
                            'health_checker', 'check_component', duration, success, health_status
                        )
                        
                    except Exception as e:
                        duration = time.time() - start_time
                        self.profiler.record_operation_metric(
                            'health_checker', 'check_component', duration, False
                        )
                    
                    await asyncio.sleep(0.5)
            
            async def error_handling_task():
                """错误处理任务"""
                for i in range(test_duration):  # 每秒1个错误
                    if i % 5 == 0:  # 每5秒一个错误
                        start_time = time.time()
                        
                        # 模拟错误处理
                        test_error = Exception(f"Test error {i}")
                        context = {
                            'component': f'test_component_{i % 3}',
                            'operation': 'test_operation',
                            'iteration': i
                        }
                        
                        error_report = self.error_handler.handle_error(test_error, context)
                        
                        duration = time.time() - start_time
                        
                        self.profiler.record_operation_metric(
                            'error_handler', 'handle_error', duration, 
                            error_report is not None
                        )
                    
                    await asyncio.sleep(1.0)
            
            # 并发执行所有任务
            start_time = time.time()
            
            await asyncio.gather(
                log_processing_task(),
                monitoring_task(),
                health_check_task(),
                error_handling_task()
            )
            
            end_time = time.time()
            actual_duration = end_time - start_time
            
            logger.info(f"实际测试时间: {actual_duration:.2f}秒")
            
            # 获取组件统计
            log_stats = self.logging_optimizer.get_optimization_statistics()
            monitor_stats = self.monitoring_optimizer.get_monitoring_status()
            error_stats = self.error_handler.get_error_statistics()
            
            # 记录组件指标
            self.profiler.record_component_metric('logging_optimizer', 'processed_logs', 
                                                 log_stats.get('total_processed', 0))
            self.profiler.record_component_metric('monitoring_optimizer', 'active_tasks', 
                                                 monitor_stats.get('components', {}).get('polling_manager', {}).get('active_tasks', 0))
            self.profiler.record_component_metric('error_handler', 'total_errors', 
                                                 error_stats.get('total_errors', 0))
            
            logger.info(f"日志处理统计: {log_stats}")
            logger.info(f"监控系统统计: {monitor_stats.get('statistics', {})}")
            logger.info(f"错误处理统计: {error_stats}")
            
            # 验证综合性能
            assert actual_duration <= test_duration + 2, f"测试执行时间超出预期: {actual_duration}秒"
            
            return {
                'actual_duration': actual_duration,
                'log_stats': log_stats,
                'monitor_stats': monitor_stats,
                'error_stats': error_stats
            }
        
        finally:
            await self._cleanup_all_components()
            self.profiler.stop_profiling()
    
    @pytest.mark.asyncio
    async def test_system_load_performance(self):
        """测试系统负载性能"""
        logger.info("测试系统负载性能...")
        
        # 开始性能分析
        self.profiler.start_profiling()
        
        try:
            # 设置系统组件
            await self._setup_all_components()
            
            # 负载测试场景
            load_scenarios = [
                {'name': '低负载', 'load_factor': 0.3, 'duration': 3},
                {'name': '中等负载', 'load_factor': 0.6, 'duration': 3},
                {'name': '高负载', 'load_factor': 0.9, 'duration': 2}
            ]
            
            load_results = []
            
            for scenario in load_scenarios:
                logger.info(f"测试负载场景: {scenario['name']}")
                
                load_factor = scenario['load_factor']
                duration = scenario['duration']
                
                # 计算操作频率
                log_ops_per_sec = int(20 * load_factor)
                monitor_ops_per_sec = int(10 * load_factor)
                health_ops_per_sec = int(5 * load_factor)
                
                scenario_start = time.time()
                
                # 创建负载任务
                async def load_test_task():
                    operations_completed = 0
                    
                    for i in range(duration * max(log_ops_per_sec, monitor_ops_per_sec)):
                        # 日志操作
                        if i % (max(1, int(1/load_factor))) == 0:
                            message = f"Load test message {i} - {scenario['name']}"
                            start_time = time.time()
                            self.logging_optimizer.should_log(message, 'INFO')
                            op_duration = time.time() - start_time
                            
                            self.profiler.record_operation_metric(
                                'logging_optimizer', f"load_test_{scenario['name']}", op_duration
                            )
                            operations_completed += 1
                        
                        # 监控操作
                        if i % (max(1, int(2/load_factor))) == 0:
                            start_time = time.time()
                            # 模拟监控数据处理
                            await asyncio.sleep(0.001)  # 1ms模拟处理
                            op_duration = time.time() - start_time
                            
                            self.profiler.record_operation_metric(
                                'monitoring_optimizer', f"load_test_{scenario['name']}", op_duration
                            )
                            operations_completed += 1
                        
                        # 控制操作频率
                        await asyncio.sleep(0.01)
                    
                    return operations_completed
                
                # 执行负载测试
                operations_completed = await load_test_task()
                
                scenario_end = time.time()
                scenario_duration = scenario_end - scenario_start
                
                ops_per_second = operations_completed / scenario_duration
                
                result = {
                    'scenario': scenario['name'],
                    'load_factor': load_factor,
                    'duration': scenario_duration,
                    'operations_completed': operations_completed,
                    'ops_per_second': ops_per_second
                }
                
                load_results.append(result)
                
                logger.info(f"  持续时间: {scenario_duration:.2f}秒")
                logger.info(f"  完成操作: {operations_completed}")
                logger.info(f"  操作频率: {ops_per_second:.1f} 操作/秒")
                
                # 短暂休息以稳定系统
                await asyncio.sleep(0.5)
            
            # 分析负载性能
            low_load = load_results[0]
            medium_load = load_results[1]
            high_load = load_results[2]
            
            logger.info(f"\n负载性能对比:")
            logger.info(f"  低负载: {low_load['ops_per_second']:.1f} 操作/秒")
            logger.info(f"  中等负载: {medium_load['ops_per_second']:.1f} 操作/秒")
            logger.info(f"  高负载: {high_load['ops_per_second']:.1f} 操作/秒")
            
            # 验证负载性能
            # 系统应该能够处理不同负载级别
            assert low_load['ops_per_second'] > 10, f"低负载性能不足: {low_load['ops_per_second']}"
            assert medium_load['ops_per_second'] > 15, f"中等负载性能不足: {medium_load['ops_per_second']}"
            assert high_load['ops_per_second'] > 10, f"高负载性能不足: {high_load['ops_per_second']}"
            
            # 高负载下性能下降应该在合理范围内
            performance_degradation = (low_load['ops_per_second'] - high_load['ops_per_second']) / low_load['ops_per_second']
            assert performance_degradation < 0.7, f"高负载性能下降过大: {performance_degradation:.1%}"
            
            return load_results
        
        finally:
            await self._cleanup_all_components()
            self.profiler.stop_profiling()
    
    @pytest.mark.asyncio
    async def test_memory_efficiency_performance(self):
        """测试内存效率性能"""
        logger.info("测试内存效率性能...")
        
        # 开始性能分析
        self.profiler.start_profiling()
        
        try:
            # 记录初始内存
            initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            # 设置系统组件
            await self._setup_all_components()
            
            # 记录启动后内存
            startup_memory = psutil.Process().memory_info().rss / 1024 / 1024
            startup_memory_usage = startup_memory - initial_memory
            
            logger.info(f"初始内存: {initial_memory:.2f}MB")
            logger.info(f"启动后内存: {startup_memory:.2f}MB")
            logger.info(f"启动内存增长: {startup_memory_usage:.2f}MB")
            
            # 运行内存压力测试
            memory_test_duration = 5
            operations_count = 1000
            
            logger.info(f"运行{memory_test_duration}秒内存压力测试...")
            
            # 执行大量操作
            for i in range(operations_count):
                # 日志操作
                message = f"Memory test message {i} - " + "x" * (i % 100)  # 变长消息
                self.logging_optimizer.should_log(message, 'INFO')
                
                # 错误处理操作
                if i % 50 == 0:
                    test_error = Exception(f"Memory test error {i}")
                    self.error_handler.handle_error(test_error, {'test_id': i})
                
                # 定期检查内存
                if i % 100 == 0:
                    current_memory = psutil.Process().memory_info().rss / 1024 / 1024
                    self.profiler.record_component_metric(
                        'system', 'memory_usage_mb', current_memory, 'MB'
                    )
                
                # 小延迟
                if i % 10 == 0:
                    await asyncio.sleep(0.001)
            
            # 记录测试后内存
            final_memory = psutil.Process().memory_info().rss / 1024 / 1024
            test_memory_growth = final_memory - startup_memory
            
            logger.info(f"测试后内存: {final_memory:.2f}MB")
            logger.info(f"测试内存增长: {test_memory_growth:.2f}MB")
            
            # 强制垃圾回收
            gc.collect()
            await asyncio.sleep(0.5)
            
            # 记录垃圾回收后内存
            gc_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_recovered = final_memory - gc_memory
            
            logger.info(f"垃圾回收后内存: {gc_memory:.2f}MB")
            logger.info(f"回收内存: {memory_recovered:.2f}MB")
            
            # 计算内存效率指标
            memory_per_operation = test_memory_growth / operations_count * 1024  # KB per operation
            memory_recovery_rate = memory_recovered / test_memory_growth if test_memory_growth > 0 else 0
            
            logger.info(f"每操作内存使用: {memory_per_operation:.3f}KB")
            logger.info(f"内存回收率: {memory_recovery_rate:.1%}")
            
            # 验证内存效率
            assert startup_memory_usage < 100, f"启动内存使用过高: {startup_memory_usage:.2f}MB"
            assert test_memory_growth < 200, f"测试内存增长过高: {test_memory_growth:.2f}MB"
            assert memory_per_operation < 1.0, f"每操作内存使用过高: {memory_per_operation:.3f}KB"
            
            return {
                'initial_memory': initial_memory,
                'startup_memory_usage': startup_memory_usage,
                'test_memory_growth': test_memory_growth,
                'memory_per_operation': memory_per_operation,
                'memory_recovery_rate': memory_recovery_rate,
                'operations_count': operations_count
            }
        
        finally:
            await self._cleanup_all_components()
            self.profiler.stop_profiling()
    
    async def _setup_all_components(self):
        """设置所有系统组件"""
        # 创建错误处理器
        error_config = ErrorHandlingConfig(
            report_directory=self.temp_dir,
            batch_processing=True
        )
        self.error_handler = EnhancedErrorHandler(error_config)
        
        # 创建日志优化器
        logging_config = LoggingOptimizationConfig(
            deduplication_enabled=True,
            level_optimization_enabled=True,
            batch_processing=True
        )
        self.logging_optimizer = LoggingOptimizer(logging_config)
        await self.logging_optimizer.start()
        
        # 创建监控优化器
        monitoring_config = MonitoringOptimizationConfig(
            optimization_level=OptimizationLevel.BALANCED,
            enable_adaptive_optimization=True
        )
        self.monitoring_optimizer = MonitoringOptimizer(monitoring_config)
        await self.monitoring_optimizer.start()
        
        # 创建健康检查器
        self.health_checker = HealthChecker()
    
    async def _cleanup_all_components(self):
        """清理所有系统组件"""
        if self.logging_optimizer:
            await self.logging_optimizer.stop()
        
        if self.monitoring_optimizer:
            await self.monitoring_optimizer.stop()
        
        # 强制垃圾回收
        gc.collect()
    
    def test_performance_report_generation(self):
        """测试性能报告生成"""
        logger.info("测试性能报告生成...")
        
        # 模拟一些性能数据
        self.profiler.start_profiling()
        
        # 模拟操作
        for i in range(10):
            self.profiler.record_operation_metric(
                'test_component', 'test_operation', 0.1 + (i * 0.01), True
            )
            self.profiler.record_component_metric(
                'test_component', 'test_metric', i * 10
            )
            time.sleep(0.01)
        
        self.profiler.stop_profiling()
        
        # 生成性能报告
        report = self.profiler.get_performance_report()
        
        logger.info(f"性能报告生成完成:")
        logger.info(f"  总时间: {report['total_time']:.3f}秒")
        logger.info(f"  CPU统计: {report['cpu_stats']}")
        logger.info(f"  内存统计: {report['memory_stats']}")
        logger.info(f"  操作统计: {report['operation_stats']}")
        
        # 验证报告内容
        assert report['total_time'] > 0, "应该有总时间记录"
        assert 'test_component' in report['operation_stats'], "应该有操作统计"
        assert report['operation_stats']['test_component']['total_operations'] == 10, "操作数量应该正确"
        
        return report


if __name__ == '__main__':
    pytest.main([__file__, '-v'])