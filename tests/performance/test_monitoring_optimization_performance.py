import logging
logger = logging.getLogger(__name__)
"""
监控系统优化性能测试

验证监控系统资源使用优化
"""

import pytest
import asyncio
import time
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import psutil
import gc
from concurrent.futures import ThreadPoolExecutor
import threading

from src.monitoring.monitoring_optimizer import MonitoringOptimizer, MonitoringOptimizationConfig, OptimizationLevel
from src.monitoring.polling_manager import PollingManager, PollingConfig, SystemActivity
from src.monitoring.cache_manager import CacheManager, CacheConfig
from src.monitoring.throttle_manager import ThrottleManagerRegistry, ThrottleConfig


class MonitoringPerformanceMetrics:
    """监控性能指标收集器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.cpu_samples = []
        self.memory_samples = []
        self.operation_counts = {}
        self.response_times = []
        self.cache_stats = {}
        self.throttle_stats = {}
    
    def start_monitoring(self):
        """开始性能监控"""
        self.start_time = time.time()
        self._monitoring_active = True
        
        # 启动资源监控线程
        self._monitor_thread = threading.Thread(target=self._monitor_resources)
        self._monitor_thread.daemon = True
        self._monitor_thread.start()
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.end_time = time.time()
        self._monitoring_active = False
        
        if hasattr(self, '_monitor_thread'):
            self._monitor_thread.join(timeout=1.0)
    
    def _monitor_resources(self):
        """监控系统资源"""
        process = psutil.Process()
        
        while getattr(self, '_monitoring_active', False):
            try:
                # CPU使用率
                cpu_percent = process.cpu_percent()
                self.cpu_samples.append({
                    'timestamp': time.time(),
                    'cpu_percent': cpu_percent
                })
                
                # 内存使用
                memory_info = process.memory_info()
                self.memory_samples.append({
                    'timestamp': time.time(),
                    'rss_mb': memory_info.rss / 1024 / 1024,
                    'vms_mb': memory_info.vms / 1024 / 1024
                })
                
                time.sleep(0.1)  # 每100ms采样一次
                
            except Exception:
                break
    
    def record_operation(self, operation_name, response_time=None):
        """记录操作"""
        if operation_name not in self.operation_counts:
            self.operation_counts[operation_name] = 0
        
        self.operation_counts[operation_name] += 1
        
        if response_time is not None:
            self.response_times.append({
                'operation': operation_name,
                'response_time': response_time,
                'timestamp': time.time()
            })
    
    def get_performance_summary(self):
        """获取性能摘要"""
        total_time = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        # CPU统计
        cpu_stats = {}
        if self.cpu_samples:
            cpu_values = [s['cpu_percent'] for s in self.cpu_samples]
            cpu_stats = {
                'avg_cpu': sum(cpu_values) / len(cpu_values),
                'max_cpu': max(cpu_values),
                'min_cpu': min(cpu_values)
            }
        
        # 内存统计
        memory_stats = {}
        if self.memory_samples:
            rss_values = [s['rss_mb'] for s in self.memory_samples]
            memory_stats = {
                'avg_memory_mb': sum(rss_values) / len(rss_values),
                'max_memory_mb': max(rss_values),
                'min_memory_mb': min(rss_values),
                'memory_growth_mb': rss_values[-1] - rss_values[0] if len(rss_values) > 1 else 0
            }
        
        # 响应时间统计
        response_stats = {}
        if self.response_times:
            times = [r['response_time'] for r in self.response_times]
            response_stats = {
                'avg_response_time': sum(times) / len(times),
                'max_response_time': max(times),
                'min_response_time': min(times),
                'total_operations': len(times)
            }
        
        return {
            'total_time': total_time,
            'cpu_stats': cpu_stats,
            'memory_stats': memory_stats,
            'response_stats': response_stats,
            'operation_counts': self.operation_counts.copy(),
            'cache_stats': self.cache_stats.copy(),
            'throttle_stats': self.throttle_stats.copy()
        }


class MockDataSource:
    """模拟数据源"""
    
    def __init__(self, name, latency_ms=50, error_rate=0.0, data_size_kb=1):
        self.name = name
        self.latency_ms = latency_ms
        self.error_rate = error_rate
        self.data_size_kb = data_size_kb
        self.call_count = 0
        self.total_response_time = 0
    
    async def fetch_data(self):
        """获取数据"""
        self.call_count += 1
        
        start_time = time.time()
        
        # 模拟网络延迟
        await asyncio.sleep(self.latency_ms / 1000.0)
        
        # 模拟错误
        import random
        if random.random() < self.error_rate:
            raise Exception(f"Data source {self.name} error")
        
        # 模拟数据大小
        data = 'x' * (self.data_size_kb * 1024)
        
        response_time = time.time() - start_time
        self.total_response_time += response_time
        
        return {
            'source': self.name,
            'data': data,
            'timestamp': datetime.now().isoformat(),
            'response_time': response_time
        }
    
    def get_stats(self):
        """获取统计信息"""
        avg_response_time = self.total_response_time / self.call_count if self.call_count > 0 else 0
        
        return {
            'name': self.name,
            'call_count': self.call_count,
            'total_response_time': self.total_response_time,
            'avg_response_time': avg_response_time,
            'configured_latency_ms': self.latency_ms,
            'error_rate': self.error_rate
        }


class TestMonitoringOptimizationPerformance:
    """监控优化性能测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.metrics = MonitoringPerformanceMetrics()
        self.monitoring_optimizer = None
        self.data_sources = {}
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_polling_manager_performance(self):
        """测试轮询管理器性能"""
        logger.info("测试轮询管理器性能...")
        
        # 创建轮询配置
        polling_config = PollingConfig(
            active_interval=1,
            idle_interval=5,
            minimal_interval=30,
            adaptive_polling_enabled=True
        )
        
        # 创建轮询管理器
        polling_manager = PollingManager(polling_config)
        
        # 创建多个数据源
        data_sources = {}
        for i in range(10):
            latency = 50 + (i * 10)  # 50-140ms延迟
            data_sources[f'source_{i}'] = MockDataSource(f'source_{i}', latency_ms=latency)
        
        # 开始性能监控
        self.metrics.start_monitoring()
        
        try:
            # 启动轮询管理器
            await polling_manager.start()
            
            # 注册轮询任务
            for name, source in data_sources.items():
                polling_manager.register_polling_task(name, source.fetch_data, interval=2)
            
            # 运行不同负载场景
            test_scenarios = [
                {'name': '低负载', 'duration': 5, 'activity_level': 0.2},
                {'name': '中等负载', 'duration': 5, 'activity_level': 0.5},
                {'name': '高负载', 'duration': 3, 'activity_level': 0.9}
            ]
            
            performance_results = []
            
            for scenario in test_scenarios:
                logger.info(f"测试场景: {scenario['name']}")
                
                # 模拟系统活动
                system_activity = SystemActivity(
                    timestamp=datetime.now(),
                    cpu_usage=20 + (scenario['activity_level'] * 60),
                    memory_usage=30 + (scenario['activity_level'] * 40),
                    api_requests_per_minute=int(100 * scenario['activity_level']),
                    active_connections=int(20 * scenario['activity_level'])
                )
                
                # 调整轮询频率
                polling_manager.adjust_polling_frequency(system_activity)
                
                # 运行场景
                start_time = time.time()
                
                # 记录初始状态
                initial_stats = {name: source.get_stats() for name, source in data_sources.items()}
                
                # 等待场景完成
                await asyncio.sleep(scenario['duration'])
                
                end_time = time.time()
                
                # 记录最终状态
                final_stats = {name: source.get_stats() for name, source in data_sources.items()}
                
                # 计算性能指标
                total_calls = sum(final_stats[name]['call_count'] - initial_stats[name]['call_count'] 
                                for name in data_sources.keys())
                
                calls_per_second = total_calls / scenario['duration']
                
                avg_response_times = [
                    final_stats[name]['avg_response_time'] 
                    for name in data_sources.keys()
                    if final_stats[name]['call_count'] > initial_stats[name]['call_count']
                ]
                
                avg_response_time = sum(avg_response_times) / len(avg_response_times) if avg_response_times else 0
                
                # 获取轮询统计
                polling_stats = polling_manager.get_polling_stats()
                
                result = {
                    'scenario': scenario,
                    'total_calls': total_calls,
                    'calls_per_second': calls_per_second,
                    'avg_response_time': avg_response_time,
                    'polling_stats': polling_stats,
                    'current_interval': polling_manager.get_current_interval()
                }
                
                performance_results.append(result)
                
                logger.info(f"  总调用次数: {total_calls}")
                logger.info(f"  调用频率: {calls_per_second:.1f} 调用/秒")
                logger.info(f"  平均响应时间: {avg_response_time:.3f}秒")
                logger.info(f"  当前轮询间隔: {result['current_interval']}秒")
            
            # 验证自适应性能
            low_load_result = performance_results[0]
            high_load_result = performance_results[2]
            
            # 高负载时轮询间隔应该更短
            assert high_load_result['current_interval'] <= low_load_result['current_interval'], \
                "高负载时轮询间隔应该更短"
            
            # 调用频率应该合理
            for result in performance_results:
                assert result['calls_per_second'] > 0, "应该有轮询调用"
                assert result['calls_per_second'] < 50, "调用频率不应过高"
            
            return performance_results
        
        finally:
            await polling_manager.stop()
            self.metrics.stop_monitoring()
    
    @pytest.mark.asyncio
    async def test_cache_manager_performance(self):
        """测试缓存管理器性能"""
        logger.info("测试缓存管理器性能...")
        
        # 创建缓存配置
        cache_config = CacheConfig(
            max_size=1000,
            default_ttl_seconds=300,
            cleanup_interval_seconds=60
        )
        
        # 创建缓存管理器
        cache_manager = CacheManager(cache_config)
        
        # 开始性能监控
        self.metrics.start_monitoring()
        
        try:
            await cache_manager.start()
            
            # 性能测试场景
            test_scenarios = [
                {
                    'name': '缓存写入性能',
                    'operation': 'write',
                    'count': 1000,
                    'key_pattern': 'test_key_{}',
                    'value_size_kb': 1
                },
                {
                    'name': '缓存读取性能',
                    'operation': 'read',
                    'count': 2000,
                    'key_pattern': 'test_key_{}',
                    'value_size_kb': 1
                },
                {
                    'name': '缓存混合操作',
                    'operation': 'mixed',
                    'count': 1500,
                    'key_pattern': 'mixed_key_{}',
                    'value_size_kb': 2
                }
            ]
            
            performance_results = []
            
            for scenario in test_scenarios:
                logger.info(f"测试场景: {scenario['name']}")
                
                # 准备测试数据
                test_data = 'x' * (scenario['value_size_kb'] * 1024)
                
                start_time = time.time()
                
                if scenario['operation'] == 'write':
                    # 写入测试
                    for i in range(scenario['count']):
                        key = scenario['key_pattern'].format(i)
                        cache_manager.set(key, test_data, ttl_seconds=300)
                        
                        if i % 100 == 0:
                            self.metrics.record_operation('cache_write')
                
                elif scenario['operation'] == 'read':
                    # 先写入一些数据
                    for i in range(min(500, scenario['count'])):
                        key = scenario['key_pattern'].format(i)
                        cache_manager.set(key, test_data, ttl_seconds=300)
                    
                    # 读取测试
                    hit_count = 0
                    for i in range(scenario['count']):
                        key = scenario['key_pattern'].format(i % 500)
                        value = cache_manager.get(key)
                        if value is not None:
                            hit_count += 1
                        
                        if i % 100 == 0:
                            self.metrics.record_operation('cache_read')
                    
                    scenario['hit_count'] = hit_count
                    scenario['hit_rate'] = hit_count / scenario['count']
                
                elif scenario['operation'] == 'mixed':
                    # 混合操作测试
                    read_count = 0
                    write_count = 0
                    hit_count = 0
                    
                    for i in range(scenario['count']):
                        if i % 3 == 0:  # 写入操作
                            key = scenario['key_pattern'].format(i)
                            cache_manager.set(key, test_data, ttl_seconds=300)
                            write_count += 1
                            self.metrics.record_operation('cache_write')
                        else:  # 读取操作
                            key = scenario['key_pattern'].format(i // 3)
                            value = cache_manager.get(key)
                            if value is not None:
                                hit_count += 1
                            read_count += 1
                            self.metrics.record_operation('cache_read')
                    
                    scenario['read_count'] = read_count
                    scenario['write_count'] = write_count
                    scenario['hit_count'] = hit_count
                    scenario['hit_rate'] = hit_count / read_count if read_count > 0 else 0
                
                end_time = time.time()
                
                # 计算性能指标
                duration = end_time - start_time
                operations_per_second = scenario['count'] / duration
                
                # 获取缓存统计
                cache_stats = cache_manager.get_statistics()
                cache_info = cache_manager.get_cache_info()
                
                result = {
                    'scenario': scenario,
                    'duration': duration,
                    'operations_per_second': operations_per_second,
                    'cache_stats': cache_stats,
                    'cache_info': cache_info
                }
                
                performance_results.append(result)
                
                logger.info(f"  持续时间: {duration:.3f}秒")
                logger.info(f"  操作速度: {operations_per_second:.0f} 操作/秒")
                logger.info(f"  缓存命中率: {scenario.get('hit_rate', 0):.1%}")
                logger.info(f"  缓存大小: {cache_info['current_state']['size']}")
            
            # 验证缓存性能
            for result in performance_results:
                # 操作速度应该足够快
                assert result['operations_per_second'] > 1000, \
                    f"缓存操作速度不足: {result['operations_per_second']}"
                
                # 缓存命中率应该合理
                if 'hit_rate' in result['scenario']:
                    if result['scenario']['operation'] in ['read', 'mixed']:
                        assert result['scenario']['hit_rate'] > 0.5, \
                            f"缓存命中率过低: {result['scenario']['hit_rate']}"
            
            return performance_results
        
        finally:
            await cache_manager.stop()
            self.metrics.stop_monitoring()
    
    @pytest.mark.asyncio
    async def test_throttle_manager_performance(self):
        """测试节流管理器性能"""
        logger.info("测试节流管理器性能...")
        
        # 创建节流管理器注册表
        throttle_registry = ThrottleManagerRegistry()
        
        # 创建不同的节流配置
        throttle_configs = {
            'low_rate': ThrottleConfig(requests_per_second=5.0, burst_size=10),
            'medium_rate': ThrottleConfig(requests_per_second=20.0, burst_size=30),
            'high_rate': ThrottleConfig(requests_per_second=50.0, burst_size=100)
        }
        
        # 注册节流管理器
        throttle_managers = {}
        for name, config in throttle_configs.items():
            manager = throttle_registry.get_throttle_manager(name, config)
            throttle_managers[name] = manager
        
        # 开始性能监控
        self.metrics.start_monitoring()
        
        try:
            # 性能测试场景
            test_scenarios = [
                {
                    'name': '低速率节流测试',
                    'throttle_manager': 'low_rate',
                    'request_count': 100,
                    'request_rate': 10.0  # 每秒10个请求
                },
                {
                    'name': '中等速率节流测试',
                    'throttle_manager': 'medium_rate',
                    'request_count': 200,
                    'request_rate': 30.0  # 每秒30个请求
                },
                {
                    'name': '高速率节流测试',
                    'throttle_manager': 'high_rate',
                    'request_count': 500,
                    'request_rate': 100.0  # 每秒100个请求
                }
            ]
            
            performance_results = []
            
            for scenario in test_scenarios:
                logger.info(f"测试场景: {scenario['name']}")
                
                throttle_manager = throttle_managers[scenario['throttle_manager']]
                
                # 重置统计
                throttle_manager.reset_statistics()
                
                start_time = time.time()
                
                allowed_count = 0
                throttled_count = 0
                
                # 模拟请求
                for i in range(scenario['request_count']):
                    request_start = time.time()
                    
                    if throttle_manager.try_acquire():
                        allowed_count += 1
                        # 模拟请求处理时间
                        processing_time = 0.01  # 10ms
                        await asyncio.sleep(processing_time)
                        throttle_manager.record_success(processing_time * 1000)  # 转换为毫秒
                        
                        self.metrics.record_operation('throttle_allowed', processing_time)
                    else:
                        throttled_count += 1
                        self.metrics.record_operation('throttle_rejected')
                    
                    # 控制请求速率
                    request_interval = 1.0 / scenario['request_rate']
                    await asyncio.sleep(request_interval)
                
                end_time = time.time()
                
                # 计算性能指标
                duration = end_time - start_time
                actual_request_rate = scenario['request_count'] / duration
                throttle_ratio = throttled_count / scenario['request_count']
                
                # 获取节流统计
                throttle_stats = throttle_manager.get_statistics()
                throttle_info = throttle_manager.get_throttle_info()
                
                result = {
                    'scenario': scenario,
                    'duration': duration,
                    'actual_request_rate': actual_request_rate,
                    'allowed_count': allowed_count,
                    'throttled_count': throttled_count,
                    'throttle_ratio': throttle_ratio,
                    'throttle_stats': throttle_stats,
                    'throttle_info': throttle_info
                }
                
                performance_results.append(result)
                
                logger.info(f"  持续时间: {duration:.2f}秒")
                logger.info(f"  实际请求速率: {actual_request_rate:.1f} 请求/秒")
                logger.info(f"  允许请求: {allowed_count}")
                logger.info(f"  节流请求: {throttled_count}")
                logger.info(f"  节流率: {throttle_ratio:.1%}")
            
            # 验证节流性能
            for result in performance_results:
                scenario = result['scenario']
                config = throttle_configs[scenario['throttle_manager']]
                
                # 节流应该有效
                if result['actual_request_rate'] > config.requests_per_second:
                    assert result['throttle_ratio'] > 0, \
                        f"高速率请求应该被节流: {result['throttle_ratio']}"
                
                # 允许的请求数应该接近配置的速率
                expected_allowed = min(
                    scenario['request_count'],
                    int(config.requests_per_second * result['duration'])
                )
                
                # 允许一定的误差范围
                assert abs(result['allowed_count'] - expected_allowed) / expected_allowed < 0.3, \
                    f"允许请求数偏差过大: {result['allowed_count']} vs {expected_allowed}"
            
            return performance_results
        
        finally:
            self.metrics.stop_monitoring()
    
    @pytest.mark.asyncio
    async def test_monitoring_optimizer_integration_performance(self):
        """测试监控优化器集成性能"""
        logger.info("测试监控优化器集成性能...")
        
        # 测试不同优化级别的性能
        optimization_levels = [
            OptimizationLevel.CONSERVATIVE,
            OptimizationLevel.BALANCED,
            OptimizationLevel.AGGRESSIVE
        ]
        
        performance_results = []
        
        for level in optimization_levels:
            logger.info(f"测试优化级别: {level.value}")
            
            # 创建监控优化器配置
            config = MonitoringOptimizationConfig(
                optimization_level=level,
                enable_adaptive_optimization=True,
                performance_monitoring_enabled=True
            )
            
            # 创建监控优化器
            monitoring_optimizer = MonitoringOptimizer(config)
            
            # 创建数据源
            data_sources = {}
            for i in range(5):
                latency = 30 + (i * 20)  # 30-110ms延迟
                data_sources[f'source_{i}'] = MockDataSource(
                    f'source_{i}', 
                    latency_ms=latency,
                    data_size_kb=1 + i  # 1-5KB数据
                )
            
            # 开始性能监控
            self.metrics.start_monitoring()
            
            try:
                # 启动监控优化器
                await monitoring_optimizer.start()
                
                # 注册监控任务
                for name, source in data_sources.items():
                    monitoring_optimizer.register_monitoring_task(
                        name, source.fetch_data, custom_interval=3
                    )
                
                # 运行监控
                test_duration = 10  # 10秒测试
                start_time = time.time()
                
                # 记录初始状态
                initial_stats = {name: source.get_stats() for name, source in data_sources.items()}
                
                # 模拟系统负载变化
                for i in range(test_duration):
                    # 模拟负载变化
                    load_factor = 0.3 + 0.4 * (i / test_duration)  # 从30%到70%
                    
                    system_activity = SystemActivity(
                        timestamp=datetime.now(),
                        cpu_usage=20 + (load_factor * 60),
                        memory_usage=30 + (load_factor * 40),
                        api_requests_per_minute=int(50 + load_factor * 200),
                        active_connections=int(10 + load_factor * 40)
                    )
                    
                    # 调整监控频率
                    monitoring_optimizer.polling_manager.adjust_polling_frequency(system_activity)
                    
                    # 执行优化
                    await monitoring_optimizer.optimize_monitoring_intervals()
                    
                    await asyncio.sleep(1)
                
                end_time = time.time()
                
                # 记录最终状态
                final_stats = {name: source.get_stats() for name, source in data_sources.items()}
                
                # 计算性能指标
                total_calls = sum(final_stats[name]['call_count'] - initial_stats[name]['call_count'] 
                                for name in data_sources.keys())
                
                calls_per_second = total_calls / test_duration
                
                # 获取监控统计
                monitoring_status = monitoring_optimizer.get_monitoring_status()
                
                result = {
                    'optimization_level': level.value,
                    'test_duration': test_duration,
                    'total_calls': total_calls,
                    'calls_per_second': calls_per_second,
                    'monitoring_status': monitoring_status,
                    'data_source_stats': {name: final_stats[name] for name in data_sources.keys()}
                }
                
                performance_results.append(result)
                
                logger.info(f"  测试持续时间: {test_duration}秒")
                logger.info(f"  总调用次数: {total_calls}")
                logger.info(f"  调用频率: {calls_per_second:.1f} 调用/秒")
                logger.info(f"  优化次数: {monitoring_status['statistics']['total_optimizations']}")
            
            finally:
                await monitoring_optimizer.stop()
                self.metrics.stop_monitoring()
                
                # 清理
                gc.collect()
                await asyncio.sleep(0.1)
        
        # 分析不同优化级别的性能差异
        conservative_result = performance_results[0]
        balanced_result = performance_results[1]
        aggressive_result = performance_results[2]
        
        logger.info(f"\n优化级别性能对比:")
        logger.info(f"  保守级别: {conservative_result['calls_per_second']:.1f} 调用/秒")
        logger.info(f"  平衡级别: {balanced_result['calls_per_second']:.1f} 调用/秒")
        logger.info(f"  激进级别: {aggressive_result['calls_per_second']:.1f} 调用/秒")
        
        # 验证优化效果
        # 激进优化应该有更高的调用频率（在合理范围内）
        assert aggressive_result['calls_per_second'] >= conservative_result['calls_per_second'], \
            "激进优化应该有更高的监控频率"
        
        # 所有级别都应该有合理的性能
        for result in performance_results:
            assert result['calls_per_second'] > 0, "应该有监控调用"
            assert result['calls_per_second'] < 100, "调用频率不应过高"
        
        return performance_results
    
    @pytest.mark.asyncio
    async def test_resource_usage_optimization(self):
        """测试资源使用优化"""
        logger.info("测试资源使用优化...")
        
        # 对比测试：优化前后的资源使用
        test_configs = [
            {
                'name': '未优化配置',
                'config': MonitoringOptimizationConfig(
                    optimization_level=OptimizationLevel.CONSERVATIVE,
                    enable_adaptive_optimization=False,
                    performance_monitoring_enabled=False
                )
            },
            {
                'name': '优化配置',
                'config': MonitoringOptimizationConfig(
                    optimization_level=OptimizationLevel.AGGRESSIVE,
                    enable_adaptive_optimization=True,
                    performance_monitoring_enabled=True
                )
            }
        ]
        
        resource_results = []
        
        for config_info in test_configs:
            logger.info(f"测试配置: {config_info['name']}")
            
            # 创建监控优化器
            monitoring_optimizer = MonitoringOptimizer(config_info['config'])
            
            # 创建多个数据源
            data_sources = {}
            for i in range(8):
                data_sources[f'resource_source_{i}'] = MockDataSource(
                    f'resource_source_{i}',
                    latency_ms=20 + (i * 10),
                    data_size_kb=2
                )
            
            # 开始详细的资源监控
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024
            initial_cpu_time = process.cpu_times()
            
            resource_samples = []
            
            try:
                # 启动监控优化器
                await monitoring_optimizer.start()
                
                # 注册监控任务
                for name, source in data_sources.items():
                    monitoring_optimizer.register_monitoring_task(
                        name, source.fetch_data, custom_interval=2
                    )
                
                # 运行资源监控测试
                test_duration = 8  # 8秒测试
                sample_interval = 0.5  # 每0.5秒采样
                
                for i in range(int(test_duration / sample_interval)):
                    # 采样资源使用
                    memory_info = process.memory_info()
                    cpu_percent = process.cpu_percent()
                    
                    sample = {
                        'timestamp': time.time(),
                        'memory_mb': memory_info.rss / 1024 / 1024,
                        'cpu_percent': cpu_percent,
                        'iteration': i
                    }
                    
                    resource_samples.append(sample)
                    
                    # 模拟系统活动变化
                    activity_level = 0.2 + 0.6 * (i / (test_duration / sample_interval))
                    
                    system_activity = SystemActivity(
                        timestamp=datetime.now(),
                        cpu_usage=15 + (activity_level * 70),
                        memory_usage=25 + (activity_level * 50),
                        api_requests_per_minute=int(30 + activity_level * 150),
                        active_connections=int(5 + activity_level * 25)
                    )
                    
                    # 调整监控
                    monitoring_optimizer.polling_manager.adjust_polling_frequency(system_activity)
                    
                    await asyncio.sleep(sample_interval)
                
                # 计算资源使用统计
                final_memory = process.memory_info().rss / 1024 / 1024
                final_cpu_time = process.cpu_times()
                
                memory_usage = final_memory - initial_memory
                cpu_time_used = (final_cpu_time.user + final_cpu_time.system) - \
                               (initial_cpu_time.user + initial_cpu_time.system)
                
                # 计算平均资源使用
                avg_memory = sum(s['memory_mb'] for s in resource_samples) / len(resource_samples)
                avg_cpu = sum(s['cpu_percent'] for s in resource_samples) / len(resource_samples)
                max_memory = max(s['memory_mb'] for s in resource_samples)
                max_cpu = max(s['cpu_percent'] for s in resource_samples)
                
                # 获取监控统计
                monitoring_status = monitoring_optimizer.get_monitoring_status()
                
                # 获取数据源统计
                total_calls = sum(source.call_count for source in data_sources.values())
                
                result = {
                    'config_name': config_info['name'],
                    'test_duration': test_duration,
                    'memory_usage_mb': memory_usage,
                    'cpu_time_used': cpu_time_used,
                    'avg_memory_mb': avg_memory,
                    'avg_cpu_percent': avg_cpu,
                    'max_memory_mb': max_memory,
                    'max_cpu_percent': max_cpu,
                    'total_calls': total_calls,
                    'calls_per_second': total_calls / test_duration,
                    'monitoring_status': monitoring_status,
                    'resource_samples': resource_samples
                }
                
                resource_results.append(result)
                
                logger.info(f"  内存使用增长: {memory_usage:.2f}MB")
                logger.info(f"  CPU时间使用: {cpu_time_used:.3f}秒")
                logger.info(f"  平均内存使用: {avg_memory:.2f}MB")
                logger.info(f"  平均CPU使用: {avg_cpu:.1f}%")
                logger.info(f"  总调用次数: {total_calls}")
                logger.info(f"  调用频率: {result['calls_per_second']:.1f} 调用/秒")
            
            finally:
                await monitoring_optimizer.stop()
                
                # 强制垃圾回收
                gc.collect()
                await asyncio.sleep(0.2)
        
        # 比较资源使用优化效果
        unoptimized_result = resource_results[0]
        optimized_result = resource_results[1]
        
        memory_improvement = (unoptimized_result['memory_usage_mb'] - optimized_result['memory_usage_mb']) / \
                           unoptimized_result['memory_usage_mb'] if unoptimized_result['memory_usage_mb'] > 0 else 0
        
        cpu_improvement = (unoptimized_result['avg_cpu_percent'] - optimized_result['avg_cpu_percent']) / \
                         unoptimized_result['avg_cpu_percent'] if unoptimized_result['avg_cpu_percent'] > 0 else 0
        
        efficiency_improvement = (optimized_result['calls_per_second'] - unoptimized_result['calls_per_second']) / \
                               unoptimized_result['calls_per_second'] if unoptimized_result['calls_per_second'] > 0 else 0
        
        logger.info(f"\n资源使用优化效果:")
        logger.info(f"  内存使用改善: {memory_improvement:.1%}")
        logger.info(f"  CPU使用改善: {cpu_improvement:.1%}")
        logger.info(f"  效率提升: {efficiency_improvement:.1%}")
        
        # 验证优化效果
        # 优化配置应该有更好的资源效率
        if memory_improvement > 0:
            assert memory_improvement > -0.2, "内存使用不应显著增加"
        
        if cpu_improvement > 0:
            assert cpu_improvement > -0.2, "CPU使用不应显著增加"
        
        # 效率应该有所提升或至少保持
        assert efficiency_improvement >= -0.1, "效率不应显著下降"
        
        return resource_results


if __name__ == '__main__':
    pytest.main([__file__, '-v'])