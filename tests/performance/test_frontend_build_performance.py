import logging
logger = logging.getLogger(__name__)
"""
前端构建时间和启动性能测试

测试前端构建时间和启动性能
"""

import pytest
import time
import tempfile
import os
import json
import shutil
import random
from pathlib import Path
import psutil
import threading


class BuildPerformanceMetrics:
    """构建性能指标收集器"""
    
    def __init__(self):
        self.build_start_time = None
        self.build_end_time = None
        self.build_steps = []
        self.resource_usage = []
        self.file_sizes = {}
    
    def start_build_timing(self):
        """开始构建计时"""
        self.build_start_time = time.time()
    
    def end_build_timing(self):
        """结束构建计时"""
        self.build_end_time = time.time()
    
    def record_build_step(self, step_name, duration, output_size=None):
        """记录构建步骤"""
        self.build_steps.append({
            'step': step_name,
            'duration': duration,
            'output_size': output_size,
            'timestamp': time.time()
        })
    
    def get_build_summary(self):
        """获取构建摘要"""
        total_build_time = self.build_end_time - self.build_start_time if self.build_end_time and self.build_start_time else 0
        step_times = {step['step']: step['duration'] for step in self.build_steps}
        
        return {
            'total_build_time': total_build_time,
            'step_times': step_times,
            'file_sizes': self.file_sizes.copy()
        }


class MockFrontendProject:
    """模拟前端项目"""
    
    def __init__(self, project_dir):
        self.project_dir = Path(project_dir)
        self.source_files = []
        
    def create_project_structure(self):
        """创建项目结构"""
        # 创建基本目录
        directories = ['src', 'src/components', 'public', 'build']
        for dir_path in directories:
            (self.project_dir / dir_path).mkdir(parents=True, exist_ok=True)
        
        # 创建package.json
        package_json = {
            "name": "test-frontend-project",
            "version": "1.0.0",
            "scripts": {"build": "react-scripts build"},
            "dependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}
        }
        
        with open(self.project_dir / 'package.json', 'w') as f:
            json.dump(package_json, f, indent=2)
        
        # 创建源文件
        self.create_source_files()
    
    def create_source_files(self):
        """创建源文件"""
        files_to_create = [
            ('src/App.tsx', self.get_app_content()),
            ('src/components/Dashboard.tsx', self.get_component_content('Dashboard')),
            ('src/utils/api.ts', self.get_api_content()),
            ('src/App.css', self.get_css_content()),
            ('public/index.html', self.get_html_content())
        ]
        
        for file_path, content in files_to_create:
            full_path = self.project_dir / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            with open(full_path, 'w') as f:
                f.write(content)
            self.source_files.append(str(full_path))
    
    def get_app_content(self):
        return '''
import React from 'react';
import './App.css';
import Dashboard from './components/Dashboard';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Trading System</h1>
      </header>
      <main>
        <Dashboard />
      </main>
    </div>
  );
}

export default App;
'''
    
    def get_component_content(self, name):
        return f'''
import React, {{ useState, useEffect }} from 'react';

const {name}: React.FC = () => {{
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {{
    const loadData = async () => {{
      try {{
        // Simulate data loading
        await new Promise(resolve => setTimeout(resolve, 100));
        setData({{message: '{name} loaded'}});
      }} catch (error) {{
        console.error('Failed to load data:', error);
      }} finally {{
        setLoading(false);
      }}
    }};

    loadData();
  }}, []);

  if (loading) {{
    return <div>Loading {name.lower()}...</div>;
  }}

  return (
    <div className="{name.lower()}">
      <h2>{name}</h2>
      <div>{{data?.message}}</div>
    </div>
  );
}};

export default {name};
'''
    
    def get_api_content(self):
        return '''
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

export const fetchData = async (endpoint: string) => {
  const response = await fetch(`${API_BASE_URL}${endpoint}`);
  if (!response.ok) {
    throw new Error(`Failed to fetch ${endpoint}`);
  }
  return response.json();
};
'''
    
    def get_css_content(self):
        return '''
.App {
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}
'''
    
    def get_html_content(self):
        return '''
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Trading System</title>
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
'''
    
    def get_project_stats(self):
        """获取项目统计信息"""
        stats = {
            'total_files': len(self.source_files),
            'file_sizes': {},
            'total_size_kb': 0
        }
        
        for file_path in self.source_files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                stats['file_sizes'][file_path] = size
                stats['total_size_kb'] += size / 1024
        
        return stats


class TestFrontendBuildPerformance:
    """前端构建性能测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.metrics = BuildPerformanceMetrics()
        self.mock_project = None
    
    def teardown_method(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_project_creation_performance(self):
        """测试项目创建性能"""
        logger.info("测试项目创建性能...")
        
        project_dir = os.path.join(self.temp_dir, 'test_project')
        
        start_time = time.time()
        self.mock_project = MockFrontendProject(project_dir)
        self.mock_project.create_project_structure()
        end_time = time.time()
        
        creation_time = end_time - start_time
        project_stats = self.mock_project.get_project_stats()
        
        logger.info(f"项目创建时间: {creation_time:.3f}秒")
        logger.info(f"创建文件数量: {project_stats['total_files']}")
        logger.info(f"项目总大小: {project_stats['total_size_kb']:.2f}KB")
        
        assert creation_time < 1.0, f"项目创建时间过长: {creation_time}秒"
        assert project_stats['total_files'] > 3, "应该创建足够的文件"
        
        return {'creation_time': creation_time, 'project_stats': project_stats}
    
    def test_mock_build_performance(self):
        """测试模拟构建性能"""
        logger.info("测试模拟构建性能...")
        
        project_dir = os.path.join(self.temp_dir, 'build_test_project')
        self.mock_project = MockFrontendProject(project_dir)
        self.mock_project.create_project_structure()
        
        self.metrics.start_build_timing()
        
        # 模拟构建步骤
        build_steps = [
            {'name': 'dependency_resolution', 'duration': random.uniform(0.5, 1.5)},
            {'name': 'typescript_compilation', 'duration': random.uniform(1.0, 3.0)},
            {'name': 'bundling', 'duration': random.uniform(2.0, 5.0)},
            {'name': 'optimization', 'duration': random.uniform(1.0, 2.5)}
        ]
        
        for step in build_steps:
            step_start = time.time()
            time.sleep(step['duration'] * 0.1)  # 缩短实际等待时间
            step_end = time.time()
            
            actual_duration = step['duration']  # 使用模拟时间
            output_size = random.randint(100, 1000)  # KB
            
            self.metrics.record_build_step(step['name'], actual_duration, output_size)
            logger.info(f"  {step['name']}: {actual_duration:.2f}秒, 输出: {output_size}KB")
        
        self.metrics.end_build_timing()
        
        # 创建模拟构建文件
        build_dir = os.path.join(project_dir, 'build')
        os.makedirs(build_dir, exist_ok=True)
        
        build_files = [
            ('static/js/main.js', random.randint(800, 1200)),
            ('static/css/main.css', random.randint(50, 150)),
            ('index.html', random.randint(2, 8))
        ]
        
        total_build_size = 0
        for file_path, size_kb in build_files:
            full_path = os.path.join(build_dir, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            
            with open(full_path, 'w') as f:
                f.write('x' * (size_kb * 1024))
            
            self.metrics.file_sizes[file_path] = size_kb
            total_build_size += size_kb
        
        build_summary = self.metrics.get_build_summary()
        
        logger.info(f"总构建时间: {build_summary['total_build_time']:.2f}秒")
        logger.info(f"构建产物大小: {total_build_size}KB")
        
        assert build_summary['total_build_time'] < 15.0, f"构建时间过长: {build_summary['total_build_time']}秒"
        assert total_build_size > 500, f"构建产物过小: {total_build_size}KB"
        
        return build_summary
    
    def test_startup_performance_simulation(self):
        """测试启动性能模拟"""
        logger.info("测试前端启动性能模拟...")
        
        startup_scenarios = [
            {'name': '冷启动', 'cache_available': False, 'bundle_size_kb': 1200},
            {'name': '热启动', 'cache_available': True, 'bundle_size_kb': 1200},
            {'name': '优化启动', 'cache_available': True, 'bundle_size_kb': 800, 'code_splitting': True}
        ]
        
        startup_results = []
        
        for scenario in startup_scenarios:
            logger.info(f"测试场景: {scenario['name']}")
            
            startup_metrics = {'scenario': scenario['name'], 'phases': []}
            
            # 模拟启动阶段
            phases = [
                ('html_parse', 0.05 + (0.02 if not scenario['cache_available'] else 0)),
                ('resource_download', (scenario['bundle_size_kb'] / 1000) * (0.5 if not scenario['cache_available'] else 0.05)),
                ('js_execution', (scenario['bundle_size_kb'] / 1000) * (0.7 if not scenario.get('code_splitting') else 0.5)),
                ('first_render', 0.1),
                ('interactive', 0.2)
            ]
            
            for phase_name, phase_time in phases:
                startup_metrics['phases'].append((phase_name, phase_time))
            
            total_startup_time = sum(phase[1] for phase in startup_metrics['phases'])
            startup_metrics['total_startup_time'] = total_startup_time
            
            startup_results.append(startup_metrics)
            
            logger.info(f"  总启动时间: {total_startup_time:.3f}秒")
        
        # 验证启动性能
        cold_start = startup_results[0]
        hot_start = startup_results[1]
        optimized_start = startup_results[2]
        
        logger.info(f"\n启动性能对比:")
        logger.info(f"  冷启动: {cold_start['total_startup_time']:.3f}秒")
        logger.info(f"  热启动: {hot_start['total_startup_time']:.3f}秒")
        logger.info(f"  优化启动: {optimized_start['total_startup_time']:.3f}秒")
        
        assert hot_start['total_startup_time'] < cold_start['total_startup_time'], "热启动应该更快"
        assert optimized_start['total_startup_time'] <= hot_start['total_startup_time'], "优化启动应该最快"
        
        return startup_results
    
    def test_bundle_size_analysis(self):
        """测试包大小分析"""
        logger.info("测试包大小分析...")
        
        bundle_configs = [
            {'name': '基础配置', 'optimization': False, 'base_size': 500},
            {'name': '优化配置', 'optimization': True, 'base_size': 500},
            {'name': '分割配置', 'optimization': True, 'code_splitting': True, 'base_size': 500}
        ]
        
        bundle_results = []
        
        for config in bundle_configs:
            logger.info(f"分析配置: {config['name']}")
            
            # 计算包大小
            total_size = config['base_size']
            
            if config.get('optimization'):
                total_size = int(total_size * 0.65)  # 优化减少35%
            
            gzipped_size = int(total_size * 0.25)  # Gzip压缩
            
            analysis = {
                'config_name': config['name'],
                'total_size': total_size,
                'gzipped_size': gzipped_size,
                'code_splitting': config.get('code_splitting', False)
            }
            
            bundle_results.append(analysis)
            
            logger.info(f"  总大小: {total_size}KB")
            logger.info(f"  Gzip后: {gzipped_size}KB")
        
        # 验证优化效果
        basic_config = bundle_results[0]
        optimized_config = bundle_results[1]
        
        size_reduction = (basic_config['total_size'] - optimized_config['total_size']) / basic_config['total_size']
        logger.info(f"\n包大小减少: {size_reduction:.1%}")
        
        assert optimized_config['total_size'] < basic_config['total_size'], "优化应该减少包大小"
        assert size_reduction > 0.2, f"包大小减少应该显著: {size_reduction}"
        
        return bundle_results


if __name__ == '__main__':
    pytest.main([__file__, '-v'])