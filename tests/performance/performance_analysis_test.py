import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
性能分析测试脚本
测试系统各个模块的性能表现
"""

import time
import sys
import tracemalloc
from memory_profiler import profile
sys.path.append('.')

from src.main import UnifiedTradingSystem

def measure_startup_time():
    """测量系统启动时间"""
    logger.info("=== 系统启动时间测试 ===")
    start_time = time.time()
    
    try:
        system = UnifiedTradingSystem()
        end_time = time.time()
        startup_time = end_time - start_time
        logger.info(f"系统启动时间: {startup_time:.2f} 秒")
        return system, startup_time
    except Exception as e:
        logger.info(f"启动失败: {e}")
        return None, 0

@profile
def test_memory_usage():
    """测试内存使用情况"""
    logger.info("\n=== 内存使用测试 ===")
    
    # 启动内存跟踪
    tracemalloc.start()
    
    try:
        system = UnifiedTradingSystem()
        
        # 获取系统状态
        status = system.get_system_status()
        
        # 获取配置信息
        config = system.get_config()
        
        # 获取内存使用情况
        current, peak = tracemalloc.get_traced_memory()
        logger.info(f"当前内存使用: {current / 1024 / 1024:.2f} MB")
        logger.info(f"峰值内存使用: {peak / 1024 / 1024:.2f} MB")
        
        tracemalloc.stop()
        return system
        
    except Exception as e:
        logger.info(f"内存测试失败: {e}")
        tracemalloc.stop()
        return None

def test_operation_performance(system):
    """测试各种操作的性能"""
    if not system:
        return
        
    logger.info("\n=== 操作性能测试 ===")
    
    # 测试获取系统状态的性能
    start_time = time.time()
    for i in range(10):
        status = system.get_system_status()
    end_time = time.time()
    avg_time = (end_time - start_time) / 10
    logger.info(f"获取系统状态平均时间: {avg_time:.4f} 秒")
    
    # 测试配置获取性能
    start_time = time.time()
    for i in range(10):
        config = system.get_config()
    end_time = time.time()
    avg_time = (end_time - start_time) / 10
    logger.info(f"获取配置信息平均时间: {avg_time:.4f} 秒")

if __name__ == "__main__":
    logger.info("开始性能分析测试...")
    
    # 测试启动时间
    system, startup_time = measure_startup_time()
    
    # 测试内存使用
    system = test_memory_usage()
    
    # 测试操作性能
    test_operation_performance(system)
    
    logger.info("\n性能分析测试完成!")