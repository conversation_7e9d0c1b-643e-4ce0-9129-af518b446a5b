import logging
logger = logging.getLogger(__name__)
"""
数据源故障切换测试
测试数据源失败时的自动切换和恢复机制
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor

from src.market.data.data_source_manager import DataSourceManager
from src.market.data.adapters.yahoo_finance_adapter import YahooFinanceAdapter
from src.market.data.adapters.akshare_adapter import AkshareAdapter
from src.market.data.adapters.binance_adapter import BinanceAdapter
from src.market.data.adapters.fred_adapter import FredAdapter
from src.market.data.failover.failover_manager import FailoverManager


class TestDataSourceFailover:
    """数据源故障切换测试"""
    
    @pytest.fixture
    def sample_market_data(self):
        """创建样本市场数据"""
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        return pd.DataFrame({
            'open': np.random.randn(len(dates)).cumsum() + 100,
            'high': np.random.randn(len(dates)).cumsum() + 105,
            'low': np.random.randn(len(dates)).cumsum() + 95,
            'close': np.random.randn(len(dates)).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)
    
    @pytest.fixture
    def failover_config(self):
        """故障切换配置"""
        return {
            'primary_sources': {
                'us_stocks': 'yahoo_finance',
                'china_stocks': 'akshare',
                'crypto': 'binance',
                'economic': 'fred'
            },
            'backup_sources': {
                'us_stocks': ['alpha_vantage', 'quandl', 'iex'],
                'china_stocks': ['wind', 'choice'],
                'crypto': ['coinbase', 'kraken'],
                'economic': ['bloomberg', 'reuters']
            },
            'retry_config': {
                'max_retries': 3,
                'retry_delay': 1,  # 秒
                'backoff_factor': 2
            },
            'health_check': {
                'interval': 60,  # 秒
                'timeout': 10    # 秒
            }
        }
    
    def test_primary_source_failure_detection(self, sample_market_data, failover_config):
        """测试主数据源故障检测"""
        
        class MockFailoverManager(FailoverManager):
            def __init__(self, config):
                super().__init__(config)
                self.failure_count = 0
            
            def detect_source_failure(self, source_name, error):
                """检测数据源故障"""
                self.failure_count += 1
                
                # 定义故障类型
                if isinstance(error, ConnectionError):
                    return 'connection_failure'
                elif isinstance(error, TimeoutError):
                    return 'timeout_failure'
                elif isinstance(error, ValueError):
                    return 'data_quality_failure'
                else:
                    return 'unknown_failure'
        
        manager = MockFailoverManager(failover_config)
        
        # 测试不同类型的故障检测
        connection_error = ConnectionError("无法连接到数据源")
        failure_type = manager.detect_source_failure('yahoo_finance', connection_error)
        assert failure_type == 'connection_failure'
        
        timeout_error = TimeoutError("请求超时")
        failure_type = manager.detect_source_failure('yahoo_finance', timeout_error)
        assert failure_type == 'timeout_failure'
        
        data_error = ValueError("数据格式错误")
        failure_type = manager.detect_source_failure('yahoo_finance', data_error)
        assert failure_type == 'data_quality_failure'
        
        assert manager.failure_count == 3
    
    def test_automatic_failover_to_backup_source(self, sample_market_data, failover_config):
        """测试自动切换到备用数据源"""
        
        class MockDataSourceManager(DataSourceManager):
            def __init__(self):
                super().__init__()
                self.failover_manager = FailoverManager(failover_config)
                self.call_history = []
            
            def get_historical_data_with_failover(self, symbol, start_date, end_date, asset_type='us_stocks'):
                """带故障切换的数据获取"""
                primary_source = failover_config['primary_sources'][asset_type]
                backup_sources = failover_config['backup_sources'][asset_type]
                
                # 尝试主数据源
                try:
                    self.call_history.append(f"trying_primary_{primary_source}")
                    if primary_source == 'yahoo_finance':
                        raise ConnectionError("Yahoo Finance连接失败")
                    return sample_market_data
                except Exception as e:
                    self.call_history.append(f"primary_failed_{primary_source}")
                    
                    # 尝试备用数据源
                    for backup_source in backup_sources:
                        try:
                            self.call_history.append(f"trying_backup_{backup_source}")
                            if backup_source == 'alpha_vantage':
                                return sample_market_data  # 模拟成功
                            else:
                                raise ConnectionError(f"{backup_source}也失败了")
                        except Exception:
                            self.call_history.append(f"backup_failed_{backup_source}")
                            continue
                    
                    raise Exception("所有数据源都失败了")
        
        manager = MockDataSourceManager()
        
        # 执行带故障切换的数据获取
        data = manager.get_historical_data_with_failover(
            symbol='AAPL',
            start_date='2023-01-01',
            end_date='2023-12-31',
            asset_type='us_stocks'
        )
        
        # 验证故障切换流程
        assert data is not None
        assert len(data) > 0
        
        # 验证调用历史
        expected_calls = [
            'trying_primary_yahoo_finance',
            'primary_failed_yahoo_finance',
            'trying_backup_alpha_vantage'
        ]
        
        for expected_call in expected_calls:
            assert expected_call in manager.call_history
    
    def test_retry_mechanism_with_exponential_backoff(self, sample_market_data, failover_config):
        """测试指数退避重试机制"""
        
        class MockRetryManager:
            def __init__(self, config):
                self.config = config['retry_config']
                self.attempt_times = []
            
            def retry_with_backoff(self, func, *args, **kwargs):
                """带指数退避的重试"""
                max_retries = self.config['max_retries']
                base_delay = self.config['retry_delay']
                backoff_factor = self.config['backoff_factor']
                
                for attempt in range(max_retries + 1):
                    try:
                        self.attempt_times.append(time.time())
                        
                        # 模拟前几次失败，最后一次成功
                        if attempt < max_retries:
                            raise ConnectionError(f"尝试 {attempt + 1} 失败")
                        else:
                            return sample_market_data
                    
                    except Exception as e:
                        if attempt == max_retries:
                            raise e
                        
                        # 计算退避延迟
                        delay = base_delay * (backoff_factor ** attempt)
                        time.sleep(delay)
                
                raise Exception("重试次数已用完")
        
        retry_manager = MockRetryManager(failover_config)
        
        # 执行重试
        start_time = time.time()
        
        def mock_data_fetch():
            return "数据获取"
        
        result = retry_manager.retry_with_backoff(mock_data_fetch)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 验证重试次数
        assert len(retry_manager.attempt_times) == 4  # 3次重试 + 1次成功
        
        # 验证指数退避时间
        expected_min_time = 1 + 2 + 4  # 1*2^0 + 1*2^1 + 1*2^2
        assert total_time >= expected_min_time * 0.9  # 允许一些时间误差
    
    def test_data_source_health_monitoring(self, failover_config):
        """测试数据源健康监控"""
        
        class MockHealthMonitor:
            def __init__(self, config):
                self.config = config['health_check']
                self.source_status = {}
                self.monitoring_active = False
            
            def start_monitoring(self, sources):
                """开始监控数据源健康状态"""
                self.monitoring_active = True
                
                for source in sources:
                    self.source_status[source] = {
                        'status': 'unknown',
                        'last_check': None,
                        'response_time': None,
                        'error_count': 0
                    }
            
            def check_source_health(self, source_name):
                """检查单个数据源健康状态"""
                start_time = time.time()
                
                try:
                    # 模拟健康检查
                    if source_name == 'yahoo_finance':
                        # 模拟Yahoo Finance偶尔失败
                        if np.random.random() < 0.2:  # 20%失败率
                            raise ConnectionError("健康检查失败")
                    
                    response_time = time.time() - start_time
                    
                    self.source_status[source_name].update({
                        'status': 'healthy',
                        'last_check': datetime.now(),
                        'response_time': response_time,
                        'error_count': 0
                    })
                    
                    return True
                
                except Exception as e:
                    self.source_status[source_name].update({
                        'status': 'unhealthy',
                        'last_check': datetime.now(),
                        'response_time': None,
                        'error_count': self.source_status[source_name]['error_count'] + 1
                    })
                    
                    return False
            
            def get_healthy_sources(self):
                """获取健康的数据源列表"""
                return [
                    source for source, status in self.source_status.items()
                    if status['status'] == 'healthy'
                ]
        
        monitor = MockHealthMonitor(failover_config)
        
        # 开始监控
        sources = ['yahoo_finance', 'akshare', 'binance', 'fred']
        monitor.start_monitoring(sources)
        
        assert monitor.monitoring_active
        assert len(monitor.source_status) == 4
        
        # 执行健康检查
        for _ in range(10):  # 多次检查以测试随机失败
            for source in sources:
                monitor.check_source_health(source)
        
        # 验证健康状态记录
        for source in sources:
            status = monitor.source_status[source]
            assert 'status' in status
            assert 'last_check' in status
            assert status['status'] in ['healthy', 'unhealthy']
        
        # 获取健康的数据源
        healthy_sources = monitor.get_healthy_sources()
        assert isinstance(healthy_sources, list)
    
    def test_data_cache_during_failover(self, sample_market_data):
        """测试故障切换期间的数据缓存"""
        
        class MockDataCache:
            def __init__(self):
                self.cache = {}
                self.cache_hits = 0
                self.cache_misses = 0
            
            def get_cache_key(self, symbol, start_date, end_date, source):
                """生成缓存键"""
                return f"{symbol}_{start_date}_{end_date}_{source}"
            
            def get_cached_data(self, symbol, start_date, end_date, source):
                """获取缓存数据"""
                cache_key = self.get_cache_key(symbol, start_date, end_date, source)
                
                if cache_key in self.cache:
                    self.cache_hits += 1
                    return self.cache[cache_key]
                else:
                    self.cache_misses += 1
                    return None
            
            def cache_data(self, symbol, start_date, end_date, source, data):
                """缓存数据"""
                cache_key = self.get_cache_key(symbol, start_date, end_date, source)
                self.cache[cache_key] = {
                    'data': data,
                    'timestamp': datetime.now(),
                    'ttl': 3600  # 1小时TTL
                }
            
            def is_cache_valid(self, cache_entry):
                """检查缓存是否有效"""
                if cache_entry is None:
                    return False
                
                age = (datetime.now() - cache_entry['timestamp']).total_seconds()
                return age < cache_entry['ttl']
        
        class MockDataSourceWithCache(DataSourceManager):
            def __init__(self):
                super().__init__()
                self.cache = MockDataCache()
                self.source_calls = 0
            
            def get_historical_data_with_cache(self, symbol, start_date, end_date, source):
                """带缓存的数据获取"""
                # 首先尝试缓存
                cached_data = self.cache.get_cached_data(symbol, start_date, end_date, source)
                
                if cached_data and self.cache.is_cache_valid(cached_data):
                    return cached_data['data']
                
                # 缓存未命中，从数据源获取
                try:
                    self.source_calls += 1
                    
                    # 模拟数据源偶尔失败
                    if self.source_calls % 3 == 0:  # 每3次调用失败一次
                        raise ConnectionError("数据源暂时不可用")
                    
                    data = sample_market_data
                    
                    # 缓存数据
                    self.cache.cache_data(symbol, start_date, end_date, source, data)
                    
                    return data
                
                except Exception:
                    # 数据源失败时，尝试返回过期的缓存数据
                    if cached_data:
                        return cached_data['data']  # 返回过期但可用的数据
                    else:
                        raise
        
        manager = MockDataSourceWithCache()
        
        # 第一次调用 - 缓存未命中
        data1 = manager.get_historical_data_with_cache('AAPL', '2023-01-01', '2023-12-31', 'yahoo_finance')
        assert data1 is not None
        assert manager.cache.cache_misses == 1
        assert manager.cache.cache_hits == 0
        
        # 第二次调用 - 缓存命中
        data2 = manager.get_historical_data_with_cache('AAPL', '2023-01-01', '2023-12-31', 'yahoo_finance')
        assert data2 is not None
        assert manager.cache.cache_hits == 1
        
        # 第三次调用 - 数据源失败，但缓存可用
        data3 = manager.get_historical_data_with_cache('AAPL', '2023-01-01', '2023-12-31', 'yahoo_finance')
        assert data3 is not None  # 应该返回缓存数据
    
    def test_concurrent_failover_handling(self, sample_market_data, failover_config):
        """测试并发故障切换处理"""
        
        class MockConcurrentManager:
            def __init__(self):
                self.active_requests = {}
                self.completed_requests = []
                self.failed_requests = []
            
            def handle_concurrent_requests(self, requests):
                """处理并发请求"""
                
                def process_single_request(request_id, symbol, source):
                    """处理单个请求"""
                    self.active_requests[request_id] = {
                        'symbol': symbol,
                        'source': source,
                        'start_time': time.time()
                    }
                    
                    try:
                        # 模拟不同的处理时间和失败率
                        processing_time = np.random.uniform(0.1, 0.5)
                        time.sleep(processing_time)
                        
                        # 模拟部分请求失败
                        if np.random.random() < 0.3:  # 30%失败率
                            raise ConnectionError(f"请求 {request_id} 失败")
                        
                        # 成功返回数据
                        result = {
                            'request_id': request_id,
                            'symbol': symbol,
                            'data': sample_market_data,
                            'source': source,
                            'processing_time': processing_time
                        }
                        
                        self.completed_requests.append(result)
                        return result
                    
                    except Exception as e:
                        error_result = {
                            'request_id': request_id,
                            'symbol': symbol,
                            'error': str(e),
                            'source': source
                        }
                        
                        self.failed_requests.append(error_result)
                        raise e
                    
                    finally:
                        if request_id in self.active_requests:
                            del self.active_requests[request_id]
                
                # 使用线程池处理并发请求
                with ThreadPoolExecutor(max_workers=5) as executor:
                    futures = []
                    
                    for i, (symbol, source) in enumerate(requests):
                        future = executor.submit(process_single_request, i, symbol, source)
                        futures.append(future)
                    
                    # 等待所有请求完成
                    results = []
                    for future in futures:
                        try:
                            result = future.result(timeout=2)
                            results.append(result)
                        except Exception:
                            pass  # 失败的请求已经记录在failed_requests中
                    
                    return results
        
        manager = MockConcurrentManager()
        
        # 创建并发请求
        concurrent_requests = [
            ('AAPL', 'yahoo_finance'),
            ('GOOGL', 'yahoo_finance'),
            ('MSFT', 'yahoo_finance'),
            ('TSLA', 'yahoo_finance'),
            ('AMZN', 'yahoo_finance'),
            ('000001.SZ', 'akshare'),
            ('000002.SZ', 'akshare'),
            ('BTCUSDT', 'binance'),
            ('ETHUSDT', 'binance'),
            ('ADAUSDT', 'binance')
        ]
        
        # 处理并发请求
        results = manager.handle_concurrent_requests(concurrent_requests)
        
        # 验证并发处理结果
        total_requests = len(concurrent_requests)
        successful_requests = len(manager.completed_requests)
        failed_requests = len(manager.failed_requests)
        
        assert successful_requests + failed_requests == total_requests
        assert successful_requests > 0  # 至少有一些成功
        
        # 验证成功请求的数据完整性
        for result in manager.completed_requests:
            assert 'request_id' in result
            assert 'symbol' in result
            assert 'data' in result
            assert result['data'] is not None
    
    def test_failover_performance_impact(self, sample_market_data):
        """测试故障切换对性能的影响"""
        
        class PerformanceTestManager:
            def __init__(self):
                self.performance_metrics = {
                    'normal_requests': [],
                    'failover_requests': []
                }
            
            def simulate_normal_request(self, symbol):
                """模拟正常请求"""
                start_time = time.time()
                
                # 模拟正常的数据获取延迟
                time.sleep(0.1)
                data = sample_market_data
                
                end_time = time.time()
                response_time = end_time - start_time
                
                self.performance_metrics['normal_requests'].append(response_time)
                return data
            
            def simulate_failover_request(self, symbol):
                """模拟故障切换请求"""
                start_time = time.time()
                
                # 模拟主数据源失败
                time.sleep(0.1)  # 主数据源尝试时间
                
                # 模拟切换到备用数据源
                time.sleep(0.05)  # 切换延迟
                time.sleep(0.15)  # 备用数据源响应时间
                
                data = sample_market_data
                
                end_time = time.time()
                response_time = end_time - start_time
                
                self.performance_metrics['failover_requests'].append(response_time)
                return data
            
            def analyze_performance_impact(self):
                """分析性能影响"""
                normal_times = self.performance_metrics['normal_requests']
                failover_times = self.performance_metrics['failover_requests']
                
                if not normal_times or not failover_times:
                    return None
                
                normal_avg = np.mean(normal_times)
                failover_avg = np.mean(failover_times)
                
                performance_impact = {
                    'normal_avg_time': normal_avg,
                    'failover_avg_time': failover_avg,
                    'slowdown_factor': failover_avg / normal_avg,
                    'additional_latency': failover_avg - normal_avg
                }
                
                return performance_impact
        
        perf_manager = PerformanceTestManager()
        
        # 执行正常请求测试
        for i in range(20):
            perf_manager.simulate_normal_request(f'STOCK_{i}')
        
        # 执行故障切换请求测试
        for i in range(20):
            perf_manager.simulate_failover_request(f'STOCK_{i}')
        
        # 分析性能影响
        impact = perf_manager.analyze_performance_impact()
        
        assert impact is not None
        assert impact['normal_avg_time'] > 0
        assert impact['failover_avg_time'] > 0
        assert impact['slowdown_factor'] > 1  # 故障切换应该更慢
        assert impact['additional_latency'] > 0
        
        # 故障切换的延迟应该在合理范围内
        assert impact['slowdown_factor'] < 5  # 不应该超过5倍延迟
        assert impact['additional_latency'] < 1  # 额外延迟不超过1秒
        
        logger.info(f"故障切换性能影响分析:")
        logger.info(f"正常请求平均时间: {impact['normal_avg_time']:.3f}秒")
        logger.info(f"故障切换平均时间: {impact['failover_avg_time']:.3f}秒")
        logger.info(f"延迟倍数: {impact['slowdown_factor']:.2f}x")
        logger.info(f"额外延迟: {impact['additional_latency']:.3f}秒")