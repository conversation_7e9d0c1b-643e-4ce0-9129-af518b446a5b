import logging
logger = logging.getLogger(__name__)
"""
大数据量性能测试
测试系统在大数据量下的性能表现
"""
import pytest
import pandas as pd
import numpy as np
import time
import psutil
import gc
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

from src.backtest.backtest_engine import BacktestEngine
from src.market.data.data_source_manager import DataSourceManager
from src.market.strategies.strategy_framework import BaseStrategy
from src.domain.models.portfolio_manager import PortfolioManager


class TestPerformanceLoadTesting:
    """性能和负载测试"""
    
    @pytest.fixture
    def large_dataset(self):
        """创建大数据集"""
        # 5年日线数据，100个股票
        start_date = datetime(2019, 1, 1)
        end_date = datetime(2023, 12, 31)
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        
        symbols = [f'STOCK_{i:03d}' for i in range(100)]
        
        data = {}
        np.random.seed(42)
        
        for symbol in symbols:
            # 生成随机价格数据
            returns = np.random.randn(len(dates)) * 0.02
            prices = 100 * np.exp(returns.cumsum())
            
            data[symbol] = pd.DataFrame({
                'open': prices * (1 + np.random.randn(len(dates)) * 0.001),
                'high': prices * (1 + np.abs(np.random.randn(len(dates))) * 0.002),
                'low': prices * (1 - np.abs(np.random.randn(len(dates))) * 0.002),
                'close': prices,
                'volume': np.random.randint(10000, 1000000, len(dates))
            }, index=dates)
        
        return data
    
    @pytest.fixture
    def high_frequency_dataset(self):
        """创建高频数据集"""
        # 1个月分钟级数据，10个股票
        start_date = datetime(2023, 12, 1)
        end_date = datetime(2023, 12, 31)
        dates = pd.date_range(start=start_date, end=end_date, freq='1min')
        
        # 只保留交易时间（9:30-16:00）
        trading_hours = dates[(dates.hour >= 9) & (dates.hour < 16)]
        trading_hours = trading_hours[~((trading_hours.hour == 9) & (trading_hours.minute < 30))]
        
        symbols = [f'HF_STOCK_{i}' for i in range(10)]
        
        data = {}
        np.random.seed(42)
        
        for symbol in symbols:
            returns = np.random.randn(len(trading_hours)) * 0.001
            prices = 100 * np.exp(returns.cumsum())
            
            data[symbol] = pd.DataFrame({
                'open': prices * (1 + np.random.randn(len(trading_hours)) * 0.0001),
                'high': prices * (1 + np.abs(np.random.randn(len(trading_hours))) * 0.0002),
                'low': prices * (1 - np.abs(np.random.randn(len(trading_hours))) * 0.0002),
                'close': prices,
                'volume': np.random.randint(100, 10000, len(trading_hours))
            }, index=trading_hours)
        
        return data
    
    def test_large_dataset_backtest_performance(self, large_dataset):
        """测试大数据集回测性能"""
        
        class SimpleStrategy(BaseStrategy):
            def generate_signals(self, data):
                signals = pd.DataFrame(index=data.index)
                signals['signal'] = 0
                
                # 简单移动平均策略
                short_ma = data['close'].rolling(window=20).mean()
                long_ma = data['close'].rolling(window=50).mean()
                
                signals.loc[short_ma > long_ma, 'signal'] = 1
                signals.loc[short_ma < long_ma, 'signal'] = -1
                
                return signals
        
        strategy = SimpleStrategy()
        symbols = list(large_dataset.keys())[:20]  # 测试20个股票
        
        # 监控内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        backtest_engine = BacktestEngine(
            initial_capital=1000000,
            commission=0.001
        )
        
        with patch.object(DataSourceManager, 'get_historical_data') as mock_data:
            def get_data_side_effect(symbol, start_date, end_date):
                return large_dataset.get(symbol)
            
            mock_data.side_effect = get_data_side_effect
            
            # 记录开始时间
            start_time = time.time()
            
            # 运行回测
            results = backtest_engine.run_backtest(
                strategy=strategy,
                symbols=symbols,
                start_date='2019-01-01',
                end_date='2023-12-31'
            )
            
            # 记录结束时间
            end_time = time.time()
            execution_time = end_time - start_time
        
        # 检查内存使用
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # 性能断言
        assert execution_time < 300  # 应在5分钟内完成
        assert memory_increase < 2000  # 内存增长不超过2GB
        assert results is not None
        
        # 清理内存
        del results
        gc.collect()
        
        logger.info(f"大数据集回测性能:")
        logger.info(f"执行时间: {execution_time:.2f}秒")
        logger.info(f"内存增长: {memory_increase:.2f}MB")
    
    def test_high_frequency_data_processing(self, high_frequency_dataset):
        """测试高频数据处理性能"""
        
        class HighFrequencyStrategy(BaseStrategy):
            def generate_signals(self, data):
                signals = pd.DataFrame(index=data.index)
                signals['signal'] = 0
                
                # 基于VWAP的策略
                vwap = (data['close'] * data['volume']).rolling(window=20).sum() / data['volume'].rolling(window=20).sum()
                
                signals.loc[data['close'] > vwap * 1.001, 'signal'] = 1
                signals.loc[data['close'] < vwap * 0.999, 'signal'] = -1
                
                return signals
        
        strategy = HighFrequencyStrategy()
        symbols = list(high_frequency_dataset.keys())
        
        # 监控性能
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024
        
        backtest_engine = BacktestEngine(
            initial_capital=100000,
            commission=0.0001  # 更低的手续费适合高频交易
        )
        
        with patch.object(DataSourceManager, 'get_historical_data') as mock_data:
            def get_data_side_effect(symbol, start_date, end_date):
                return high_frequency_dataset.get(symbol)
            
            mock_data.side_effect = get_data_side_effect
            
            start_time = time.time()
            
            results = backtest_engine.run_backtest(
                strategy=strategy,
                symbols=symbols,
                start_date='2023-12-01',
                end_date='2023-12-31'
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
        
        final_memory = process.memory_info().rss / 1024 / 1024
        memory_increase = final_memory - initial_memory
        
        # 高频数据性能要求更严格
        assert execution_time < 120  # 2分钟内完成
        assert memory_increase < 1000  # 内存增长不超过1GB
        assert results is not None
        
        logger.info(f"高频数据处理性能:")
        logger.info(f"执行时间: {execution_time:.2f}秒")
        logger.info(f"内存增长: {memory_increase:.2f}MB")
    
    def test_concurrent_backtest_performance(self, large_dataset):
        """测试并发回测性能"""
        
        class TestStrategy(BaseStrategy):
            def generate_signals(self, data):
                signals = pd.DataFrame(index=data.index)
                signals['signal'] = 0
                
                # 随机策略用于测试
                np.random.seed(42)
                signals['signal'] = np.random.choice([-1, 0, 1], size=len(data), p=[0.1, 0.8, 0.1])
                
                return signals
        
        def run_single_backtest(symbol_batch):
            """运行单个回测任务"""
            strategy = TestStrategy()
            backtest_engine = BacktestEngine(initial_capital=100000)
            
            with patch.object(DataSourceManager, 'get_historical_data') as mock_data:
                def get_data_side_effect(symbol, start_date, end_date):
                    return large_dataset.get(symbol)
                
                mock_data.side_effect = get_data_side_effect
                
                return backtest_engine.run_backtest(
                    strategy=strategy,
                    symbols=symbol_batch,
                    start_date='2023-01-01',
                    end_date='2023-12-31'
                )
        
        symbols = list(large_dataset.keys())[:40]  # 使用40个股票
        
        # 将股票分成4批
        symbol_batches = [symbols[i:i+10] for i in range(0, len(symbols), 10)]
        
        # 测试串行执行
        start_time = time.time()
        serial_results = []
        for batch in symbol_batches:
            result = run_single_backtest(batch)
            serial_results.append(result)
        serial_time = time.time() - start_time
        
        # 测试并行执行
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=4) as executor:
            parallel_results = list(executor.map(run_single_backtest, symbol_batches))
        parallel_time = time.time() - start_time
        
        # 验证结果
        assert len(serial_results) == len(parallel_results) == 4
        assert all(result is not None for result in serial_results)
        assert all(result is not None for result in parallel_results)
        
        # 并行应该更快
        speedup = serial_time / parallel_time
        assert speedup > 1.5  # 至少1.5倍加速
        
        logger.info(f"并发性能测试:")
        logger.info(f"串行执行时间: {serial_time:.2f}秒")
        logger.info(f"并行执行时间: {parallel_time:.2f}秒")
        logger.info(f"加速比: {speedup:.2f}x")
    
    def test_memory_leak_detection(self, large_dataset):
        """测试内存泄漏检测"""
        
        class MemoryTestStrategy(BaseStrategy):
            def generate_signals(self, data):
                signals = pd.DataFrame(index=data.index)
                signals['signal'] = 0
                
                # 创建一些临时数据结构
                temp_data = data.copy()
                rolling_mean = temp_data['close'].rolling(window=50).mean()
                
                signals.loc[temp_data['close'] > rolling_mean, 'signal'] = 1
                signals.loc[temp_data['close'] < rolling_mean, 'signal'] = -1
                
                return signals
        
        strategy = MemoryTestStrategy()
        symbols = list(large_dataset.keys())[:5]
        
        process = psutil.Process()
        memory_usage = []
        
        # 运行多次回测，监控内存使用
        for i in range(10):
            initial_memory = process.memory_info().rss / 1024 / 1024
            
            backtest_engine = BacktestEngine(initial_capital=100000)
            
            with patch.object(DataSourceManager, 'get_historical_data') as mock_data:
                def get_data_side_effect(symbol, start_date, end_date):
                    return large_dataset.get(symbol)
                
                mock_data.side_effect = get_data_side_effect
                
                results = backtest_engine.run_backtest(
                    strategy=strategy,
                    symbols=symbols,
                    start_date='2023-01-01',
                    end_date='2023-12-31'
                )
            
            # 强制垃圾回收
            del results
            del backtest_engine
            gc.collect()
            
            final_memory = process.memory_info().rss / 1024 / 1024
            memory_usage.append(final_memory)
        
        # 检查内存是否持续增长（可能的内存泄漏）
        memory_trend = np.polyfit(range(len(memory_usage)), memory_usage, 1)[0]
        
        # 内存增长趋势应该很小
        assert memory_trend < 10  # 每次迭代内存增长不超过10MB
        
        logger.info(f"内存泄漏检测:")
        logger.info(f"内存使用趋势: {memory_trend:.2f}MB/次")
        logger.info(f"最终内存使用: {memory_usage[-1]:.2f}MB")
    
    def test_data_processing_bottlenecks(self, large_dataset):
        """测试数据处理瓶颈"""
        
        symbols = list(large_dataset.keys())[:10]
        
        # 测试不同的数据处理方法
        processing_times = {}
        
        # 方法1: 逐个处理
        start_time = time.time()
        for symbol in symbols:
            data = large_dataset[symbol]
            # 模拟数据处理
            _ = data['close'].rolling(window=20).mean()
            _ = data['close'].rolling(window=50).mean()
        processing_times['sequential'] = time.time() - start_time
        
        # 方法2: 向量化处理
        start_time = time.time()
        combined_data = pd.concat([large_dataset[symbol]['close'] for symbol in symbols], axis=1)
        combined_data.columns = symbols
        _ = combined_data.rolling(window=20).mean()
        _ = combined_data.rolling(window=50).mean()
        processing_times['vectorized'] = time.time() - start_time
        
        # 向量化应该更快
        speedup = processing_times['sequential'] / processing_times['vectorized']
        assert speedup > 2  # 至少2倍加速
        
        logger.info(f"数据处理性能:")
        logger.info(f"逐个处理: {processing_times['sequential']:.2f}秒")
        logger.info(f"向量化处理: {processing_times['vectorized']:.2f}秒")
        logger.info(f"加速比: {speedup:.2f}x")