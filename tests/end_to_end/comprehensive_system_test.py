import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
量化交易系统全面系统测试

本测试模块用于验证量化交易系统的所有核心功能是否正常工作，
包括技术指标计算、风险管理、数据源连接、回测引擎和信号处理等。

测试覆盖范围：
1. 技术指标引擎的接口一致性测试
2. 风险管理器的变量作用域和计算准确性测试
3. 数据源管理器的健康检查和连接状态测试
4. 回测引擎的信号处理和策略执行测试
5. 交易信号的验证和格式检查测试

测试目标：
- 确保所有已知问题已修复
- 验证系统各模块间的集成正常
- 检查关键业务逻辑的正确性
- 保证系统的稳定性和可靠性

执行方式：
    python tests/comprehensive_system_test.py

作者: 量化交易系统开发团队
版本: 2.0.0
创建日期: 2024-01-01
最后修改: 2024-07-31
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_comprehensive_system():
    """
    执行全面系统测试
    
    测试流程：
    1. 初始化测试环境和变量
    2. 逐个执行各模块的功能测试
    3. 记录测试结果和错误信息
    4. 生成测试报告和建议
    
    Returns:
        bool: 所有测试是否通过
    """
    logger.info("🔧 量化交易系统全面验证")
    logger.info("=" * 50)
    
    all_tests_passed = True
    
    # 测试模块1: 技术指标引擎接口一致性测试
    logger.info("\n📊 测试技术指标列表获取...")
    try:
        from src.indicators.engine import IndicatorEngine
        
        # 创建指标引擎实例
        engine = IndicatorEngine()
        
        # 测试用例1.1: 验证 get_available_indicators 返回格式
        # 期望返回: Dict[str, str] - 指标名称到描述的映射
        available_indicators = engine.get_available_indicators()
        if not isinstance(available_indicators, dict):
            logger.info(f"❌ get_available_indicators() 返回类型错误: {type(available_indicators)}, 期望: dict")
            all_tests_passed = False
        else:
            logger.info(f"✅ get_available_indicators() 返回正确格式: {len(available_indicators)} 个指标")
        
        # 测试用例1.2: 验证 get_indicator_names 返回格式
        # 期望返回: List[str] - 指标名称列表
        indicator_names = engine.get_indicator_names()
        if not isinstance(indicator_names, list):
            logger.info(f"❌ get_indicator_names() 返回类型错误: {type(indicator_names)}, 期望: list")
            all_tests_passed = False
        else:
            logger.info(f"✅ get_indicator_names() 返回正确格式: {len(indicator_names)} 个指标")
            
        # 测试用例1.3: 验证两个接口返回的指标数量一致性
        if len(available_indicators) != len(indicator_names):
            logger.info(f"❌ 指标数量不一致: available_indicators={len(available_indicators)}, indicator_names={len(indicator_names)}")
            all_tests_passed = False
        else:
            logger.info("✅ 指标数量一致性检查通过")
            
    except Exception as e:
        logger.info(f"❌ 技术指标测试失败: {e}")
        all_tests_passed = False
    
    # 测试模块2: 风险管理器变量作用域和计算准确性测试
    logger.info("\n⚠️  测试风险管理器变量作用域...")
    try:
        from src.risk.manager import RiskManager
        from src.market.strategies.models.portfolio import Portfolio
        
        # 创建风险管理器和测试投资组合
        risk_manager = RiskManager()
        portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
        
        # 测试用例2.1: 验证风险指标计算不会出现变量作用域错误
        # 这里之前存在 position_values 变量作用域问题，现已修复
        metrics = risk_manager.calculate_risk_metrics(portfolio)
        
        if metrics is None:
            logger.info("❌ 风险指标计算返回 None")
            all_tests_passed = False
        else:
            logger.info(f"✅ 风险指标计算成功: 投资组合价值 {metrics.portfolio_value}")
            
        # 测试用例2.2: 验证风险指标的基本合理性
        if hasattr(metrics, 'portfolio_value') and metrics.portfolio_value != 100000.0:
            logger.info(f"❌ 投资组合价值计算错误: 期望 100000.0, 实际 {metrics.portfolio_value}")
            all_tests_passed = False
        else:
            logger.info("✅ 投资组合价值计算正确")
            
        # 测试用例2.3: 验证风险指标对象的完整性
        required_attributes = ['portfolio_value', 'var_95', 'max_drawdown']
        for attr in required_attributes:
            if not hasattr(metrics, attr):
                logger.info(f"❌ 风险指标缺少属性: {attr}")
                all_tests_passed = False
        
        if all_tests_passed:
            logger.info("✅ 风险指标对象完整性检查通过")
            
    except Exception as e:
        logger.info(f"❌ 风险管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        all_tests_passed = False
    
    # 3. 测试数据源健康检查返回格式
    logger.info("\n🔌 测试数据源健康检查...")
    try:
        from src.market.data.adapters.manager import DataSourceManager
        
        manager = DataSourceManager()
        
        # 测试健康检查返回格式
        health_results = manager.health_check_all()
        
        if not isinstance(health_results, dict):
            logger.info(f"❌ health_check_all() 返回类型错误: {type(health_results)}, 期望: dict")
            all_tests_passed = False
        else:
            logger.info(f"✅ health_check_all() 返回正确格式: {len(health_results)} 个数据源")
            
            # 检查每个数据源的返回格式
            for name, result in health_results.items():
                if not isinstance(result, dict):
                    logger.info(f"❌ 数据源 {name} 返回格式错误: {type(result)}")
                    all_tests_passed = False
                elif 'healthy' not in result or 'message' not in result:
                    logger.info(f"❌ 数据源 {name} 缺少必要字段: {result.keys()}")
                    all_tests_passed = False
                else:
                    logger.info(f"  ✅ 数据源 {name}: {'健康' if result['healthy'] else '不健康'}")
            
    except Exception as e:
        logger.info(f"❌ 数据源健康检查测试失败: {e}")
        import traceback
        traceback.print_exc()
        all_tests_passed = False
    
    # 4. 测试回测引擎信号处理
    logger.info("\n🔄 测试回测引擎信号处理...")
    try:
        from src.backtest.engine import BacktestEngine
        from src.market.strategies.base import BaseStrategy
        from src.market.strategies.models.trading import Signal, SignalAction
        from src.market.strategies.models.market_data import UnifiedMarketData
        import pandas as pd
        import numpy as np
        
        # 创建简单测试策略
        from src.market.strategies.parameters import StrategyParameter, ParameterType
        
        class TestStrategy(BaseStrategy):
            def __init__(self):
                super().__init__("test_strategy", {"period": 10})
            
            def get_parameter_definitions(self):
                return {
                    "period": StrategyParameter(
                        name="period",
                        param_type=ParameterType.INTEGER,
                        default_value=10,
                        min_value=1,
                        max_value=100,
                        description="Period for calculation"
                    )
                }
            
            def initialize(self, context):
                pass
            
            def on_data(self, data):
                pass
            
            def generate_signals(self, data: pd.DataFrame) -> list:
                if len(data) > 0:
                    signal = Signal(
                        symbol="TEST",
                        action=SignalAction.BUY,  # 使用 action 而不是 signal_type
                        quantity=100,
                        price=data['close'].iloc[-1],
                        timestamp=datetime.now(),
                        confidence=0.8,
                        strategy_id=self.name
                    )
                    return [signal]
                return []
        
        # 创建回测引擎
        engine = BacktestEngine(initial_capital=100000.0)
        strategy = TestStrategy()
        engine.add_strategy(strategy)
        
        # 创建测试数据 (DataFrame格式)
        dates = pd.date_range(start='2023-01-01', periods=5, freq='D')
        test_data = pd.DataFrame({
            'timestamp': dates,
            'symbol': ['TEST'] * 5,
            'open': [100.0 + i for i in range(5)],
            'high': [105.0 + i for i in range(5)],
            'low': [95.0 + i for i in range(5)],
            'close': [102.0 + i for i in range(5)],
            'volume': [1000] * 5
        })
        
        # 设置数据源并运行回测
        engine.set_data_feed(test_data)
        result = engine.run_backtest()
        
        if result is None:
            logger.info("❌ 回测结果为 None")
            all_tests_passed = False
        else:
            logger.info(f"✅ 回测运行成功: 状态 {result.status}")
            
    except Exception as e:
        logger.info(f"❌ 回测引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        all_tests_passed = False
    
    # 5. 测试信号验证
    logger.info("\n📡 测试信号验证...")
    try:
        from src.market.strategies.models.trading import Signal, SignalAction
        
        # 创建测试信号
        signal = Signal(
            symbol="TEST",
            action=SignalAction.BUY,
            quantity=100,
            price=150.0,
            timestamp=datetime.now(),
            confidence=0.8,
            strategy_id="test_strategy"
        )
        
        # 测试验证方法
        is_valid = signal.validate()
        
        if not is_valid:
            logger.info("❌ 信号验证失败")
            all_tests_passed = False
        else:
            logger.info("✅ 信号验证成功")
            
    except Exception as e:
        logger.info(f"❌ 信号验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        all_tests_passed = False
    
    # 总结
    logger.info("\n" + "=" * 50)
    if all_tests_passed:
        logger.info("🎉 所有测试通过！系统运行正常。")
        return True
    else:
        logger.info("❌ 发现问题需要修复。")
        return False

if __name__ == "__main__":
    success = test_comprehensive_system()
    sys.exit(0 if success else 1)