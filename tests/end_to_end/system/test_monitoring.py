logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
测试统一监控系统
"""

import sys
import os
import time
import importlib.util
from pathlib import Path

def load_monitoring_module():
    """直接加载monitoring模块"""
    monitoring_file = Path(__file__).parent / "src" / "core" / "unified_monitoring.py"
    spec = importlib.util.spec_from_file_location("unified_monitoring", monitoring_file)
    module = importlib.util.module_from_spec(spec)
    
    # 添加必要的路径和模块
    sys.path.insert(0, str(Path(__file__).parent))
    
    # 创建mock模块
    import logging
    
    class MockLogging:
        @staticmethod
        def get_logger(name):
            return logging.getLogger(name)
    
    class MockConfigManager:
        def validate_config(self):
            return {}  # 无错误
    
    class MockErrorHandler:
        def __init__(self):
            self.error_records = []
        
        def handle_error(self, exception, context=None, component="test", operation="test", severity=None):
            logger.info(f"Mock错误处理: {exception}")
    
    sys.modules['src.utils.logging'] = MockLogging()
    sys.modules['src.core.unified_config'] = type('MockConfigModule', (), {
        'get_config_manager': lambda: MockConfigManager()
    })
    sys.modules['src.core.unified_error_handler'] = type('MockErrorModule', (), {
        'get_error_handler': lambda: MockErrorHandler(),
        'ErrorSeverity': type('MockErrorSeverity', (), {
            'LOW': 'low', 'MEDIUM': 'medium', 'HIGH': 'high', 'CRITICAL': 'critical'
        }),
        'ErrorCategory': type('MockErrorCategory', (), {
            'SYSTEM': 'system', 'DATA': 'data', 'API': 'api', 'NETWORK': 'network', 'CONFIGURATION': 'configuration'
        })
    })
    
    # 安装psutil如果没有的话
    try:
        import psutil
    except ImportError:
        logger.info("⚠️  psutil未安装，某些监控功能可能无法使用")
        # 创建psutil mock
        class MockPsutil:
            @staticmethod
            def cpu_percent(interval=1):
                return 45.0
            
            @staticmethod
            def virtual_memory():
                class MemoryMock:
                    percent = 65.0
                    total = 8 * 1024**3  # 8GB
                    available = 3 * 1024**3  # 3GB
                return MemoryMock()
            
            @staticmethod
            def disk_usage(path):
                class DiskMock:
                    total = 500 * 1024**3  # 500GB
                    used = 200 * 1024**3   # 200GB
                    free = 300 * 1024**3   # 300GB
                return DiskMock()
            
            @staticmethod
            def boot_time():
                return time.time() - 86400  # 1天前启动
        
        sys.modules['psutil'] = MockPsutil()
    
    spec.loader.exec_module(module)
    return module

def test_monitoring_system():
    """测试监控系统"""
    logger.info("🧪 测试统一监控和健康检查系统...")
    
    try:
        # 加载monitoring模块
        monitoring_module = load_monitoring_module()
        monitoring_system = monitoring_module.get_monitoring_system()
        
        logger.info("✅ 监控系统初始化完成")
        
        # 测试健康检查注册
        logger.info("\n🔧 测试自定义健康检查注册:")
        def custom_check():
            return {
                "status": monitoring_module.HealthStatus.HEALTHY,
                "message": "自定义检查通过"
            }
        
        monitoring_system.register_health_check(
            "custom_test",
            "自定义测试检查",
            custom_check,
            interval=30
        )
        logger.info("  ✅ 自定义健康检查注册成功")
        
        # 测试指标记录
        logger.info("\n📊 测试指标记录:")
        monitoring_system.record_metric("test_metric", 42.0, monitoring_module.MetricType.GAUGE)
        monitoring_system.record_metric("test_counter", 1.0, monitoring_module.MetricType.COUNTER)
        monitoring_system.record_metric("response_time", 0.5, monitoring_module.MetricType.TIMER, 
                                      labels={"endpoint": "/api/test"})
        logger.info("  ✅ 指标记录成功")
        
        # 测试告警规则
        logger.info("\n⚠️  测试告警规则:")
        monitoring_system.register_alert_rule(
            "test_alert",
            "test_metric > 40",
            monitoring_module.ErrorSeverity.MEDIUM,
            "测试指标过高: {test_metric}"
        )
        
        # 触发告警
        monitoring_system.record_metric("test_metric", 45.0)
        logger.info("  ✅ 告警规则测试成功")
        
        # 运行健康检查
        logger.info("\n🔍 运行健康检查:")
        health_results = monitoring_system.run_all_health_checks()
        for check_name, result in health_results.items():
            status = result.get("status")
            message = result.get("message", "")
            status_emoji = {
                "healthy": "✅",
                "warning": "⚠️ ",
                "critical": "❌",
                "unknown": "❓"
            }.get(status.value if hasattr(status, 'value') else status, "❓")
            
            logger.info(f"  {status_emoji} {check_name}: {message}")
        
        # 获取整体健康状态
        logger.info("\n📋 整体健康状态:")
        overall_health = monitoring_system.get_overall_health_status()
        overall_status = overall_health.get("overall_status")
        status_emoji = {
            "healthy": "✅",
            "warning": "⚠️ ",
            "critical": "❌",
            "unknown": "❓"
        }.get(overall_status.value if hasattr(overall_status, 'value') else overall_status, "❓")
        
        logger.info(f"  整体状态: {status_emoji} {overall_status}")
        
        status_counts = overall_health.get("status_counts", {})
        logger.info("  状态统计:")
        for status, count in status_counts.items():
            if count > 0:
                logger.info(f"    {status}: {count}")
        
        # 获取系统指标
        logger.info("\n📈 系统指标:")
        metrics = monitoring_system.get_system_metrics()
        for metric_name, metric_data in list(metrics.items())[:5]:  # 显示前5个
            current_value = metric_data.get("current_value", 0)
            description = metric_data.get("description", "")
            logger.info(f"  {metric_name}: {current_value:.2f} ({description})")
        
        logger.info(f"  总共收集了 {len(metrics)} 个指标")
        
        # 测试监控启停
        logger.info("\n🔄 测试监控启停:")
        monitoring_system.start_monitoring()
        logger.info("  ✅ 监控已启动")
        
        logger.info("  等待5秒以观察监控运行...")
        time.sleep(5)
        
        monitoring_system.stop_monitoring()
        logger.info("  🛑 监控已停止")
        
        logger.info("\n✅ 统一监控和健康检查系统测试完成")
        return True
        
    except Exception as e:
        logger.info(f"\n❌ 监控系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_monitoring_system()
    sys.exit(0 if success else 1)