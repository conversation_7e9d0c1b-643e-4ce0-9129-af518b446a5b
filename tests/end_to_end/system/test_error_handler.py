logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
测试统一错误处理系统
"""

import sys
import os
import importlib.util
from pathlib import Path

def load_error_handler_module():
    """直接加载error_handler模块"""
    error_handler_file = Path(__file__).parent / "src" / "core" / "unified_error_handler.py"
    spec = importlib.util.spec_from_file_location("unified_error_handler", error_handler_file)
    module = importlib.util.module_from_spec(spec)
    
    # 添加必要的路径和模块
    sys.path.insert(0, str(Path(__file__).parent))
    
    # 创建mock模块
    import logging
    
    class MockLogging:
        @staticmethod
        def get_logger(name):
            return logging.getLogger(name)
    
    class MockMessages:
        @staticmethod
        def get_message_manager():
            class MockMessageManager:
                def get_message(self, key, **kwargs):
                    return f"Mock中文消息: {key} - {kwargs}"
            return MockMessageManager()
    
    sys.modules['src.utils.logging'] = MockLogging()
    sys.modules['src.utils.messages'] = MockMessages()
    
    spec.loader.exec_module(module)
    return module

def test_error_handling():
    """测试错误处理系统"""
    logger.info("🧪 测试统一错误处理系统...")
    
    try:
        # 加载error handler模块
        error_handler_module = load_error_handler_module()
        error_handler = error_handler_module.get_error_handler()
        
        logger.info("✅ 错误处理系统初始化完成")
        
        # 测试不同类型的错误处理
        logger.info("\n🔍 测试错误处理:")
        
        # 测试1: 连接错误
        try:
            raise ConnectionError("无法连接到数据库")
        except Exception as e:
            error_record = error_handler.handle_error(
                e,
                context={"operation": "database_connect", "host": "localhost"},
                component="database_manager",
                operation="connect",
                category=error_handler_module.ErrorCategory.DATA,
                severity=error_handler_module.ErrorSeverity.HIGH
            )
            logger.info(f"  处理连接错误: {error_record.chinese_message}")
        
        # 测试2: 文件不存在错误
        try:
            raise FileNotFoundError("配置文件不存在: /path/to/config.yaml")
        except Exception as e:
            error_record = error_handler.handle_error(
                e,
                context={"config_file": "/path/to/config.yaml"},
                component="config_manager",
                operation="load_config",
                category=error_handler_module.ErrorCategory.CONFIGURATION,
                severity=error_handler_module.ErrorSeverity.MEDIUM
            )
            logger.info(f"  处理配置错误: {error_record.chinese_message}")
        
        # 测试3: API错误
        try:
            raise ValueError("无效的股票代码: INVALID")
        except Exception as e:
            error_record = error_handler.handle_error(
                e,
                context={"symbol": "INVALID", "api": "yahoo_finance"},
                component="data_fetcher",
                operation="fetch_data",
                category=error_handler_module.ErrorCategory.API,
                severity=error_handler_module.ErrorSeverity.LOW
            )
            logger.info(f"  处理API错误: {error_record.chinese_message}")
        
        # 测试错误统计
        logger.info(f"\n📊 错误统计:")
        stats = error_handler.get_error_stats()
        logger.info(f"  总错误数: {stats['total_errors']}")
        logger.info(f"  按类别统计: {stats['by_category']}")
        logger.info(f"  按严重程度统计: {stats['by_severity']}")
        logger.info(f"  按组件统计: {stats['by_component']}")
        
        # 测试最近错误
        logger.info(f"\n📋 最近错误记录:")
        recent_errors = error_handler.get_recent_errors(3)
        for i, error_record in enumerate(recent_errors, 1):
            logger.info(f"  {i}. [{error_record.context.error_id}] {error_record.context.component}: {error_record.chinese_message}")
        
        # 测试恢复建议
        logger.info(f"\n🔧 恢复建议测试:")
        if recent_errors:
            latest_error = recent_errors[0]
            logger.info(f"  错误: {latest_error.chinese_message}")
            logger.info(f"  恢复建议:")
            for action in latest_error.recovery_actions:
                logger.info(f"    - {action}")
        
        logger.info("\n✅ 错误处理系统测试完成")
        return True
        
    except Exception as e:
        logger.info(f"\n❌ 错误处理系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_error_handling()
    sys.exit(0 if success else 1)