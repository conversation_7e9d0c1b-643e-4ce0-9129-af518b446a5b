logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
测试统一配置管理系统
"""

import sys
import os
import importlib.util
from pathlib import Path

def load_unified_config():
    """直接加载unified_config模块"""
    config_file = Path(__file__).parent / "src" / "core" / "unified_config.py"
    spec = importlib.util.spec_from_file_location("unified_config", config_file)
    module = importlib.util.module_from_spec(spec)
    
    # 添加必要的路径和模块
    sys.path.insert(0, str(Path(__file__).parent))
    
    # 创建基本的logging mock
    import logging
    
    class MockLogging:
        @staticmethod
        def get_logger(name):
            return logging.getLogger(name)
    
    sys.modules['src.utils.logging'] = MockLogging()
    
    spec.loader.exec_module(module)
    return module

def test_config_loading():
    """测试配置加载"""
    logger.info("🧪 测试统一配置管理系统...")
    
    try:
        # 加载unified_config模块
        unified_config_module = load_unified_config()
        config_mgr = unified_config_module.get_config_manager()
        
        logger.info("\n📋 配置文件加载状态:")
        config_info = config_mgr.get_config_info()
        for info in config_info:
            logger.info(f"  ✅ {info['name']}: {info['keys_count']} 个配置项")
            logger.info(f"      来源: {info['source_file']}")
            logger.info(f"      更新时间: {info['last_updated']}")
            logger.info()
        
        logger.info(f"📊 总共加载了 {len(config_info)} 个配置节")
        
        # 测试配置获取
        logger.info("\n🔍 测试配置获取:")
        
        # 测试主配置
        system_name = config_mgr.get_config("main", "system.name")
        if system_name:
            logger.info(f"  系统名称: {system_name}")
        
        # 测试数据源配置
        data_sources = config_mgr.get_config("data_sources")
        if data_sources:
            logger.info(f"  数据源配置: {len(data_sources)} 个数据源")
            for source in data_sources:
                enabled = data_sources[source].get("enabled", False)
                status = "启用" if enabled else "禁用"
                logger.info(f"    - {source}: {status}")
        
        # 测试环境配置
        env_config = config_mgr.get_config("env_development")
        if env_config:
            api_port = env_config.get("api", {}).get("port")
            if api_port:
                logger.info(f"  开发环境API端口: {api_port}")
        
        # 测试Web UI配置
        frontend_config = config_mgr.get_config("web_ui_frontend")
        if frontend_config:
            logger.info(f"  前端应用名称: {frontend_config.get('name', 'N/A')}")
        
        # 验证配置完整性
        logger.info("\n🔧 配置验证:")
        errors = config_mgr.validate_config()
        if errors:
            logger.info("  发现配置问题:")
            for section, error_list in errors.items():
                for error in error_list:
                    logger.info(f"    ❌ {section}: {error}")
        else:
            logger.info("  ✅ 所有配置验证通过")
        
        logger.info("\n✅ 配置管理系统测试完成")
        return True
        
    except Exception as e:
        logger.info(f"\n❌ 配置管理系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config_loading()
    sys.exit(0 if success else 1)