logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
测试统一工具库
"""

import sys
import os
import importlib.util
import time
from pathlib import Path
import pandas as pd
import numpy as np

def load_tools_module():
    """直接加载tools模块"""
    tools_file = Path(__file__).parent / "src" / "utils" / "tools.py"
    spec = importlib.util.spec_from_file_location("tools", tools_file)
    module = importlib.util.module_from_spec(spec)
    
    # 添加必要的路径和模块
    sys.path.insert(0, str(Path(__file__).parent))
    
    # 创建mock模块
    import logging
    
    class MockLogging:
        @staticmethod
        def get_logger(name):
            return logging.getLogger(name)
    
    sys.modules['src.utils.logging'] = MockLogging()
    
    # 安装psutil如果没有的话
    try:
        import psutil
    except ImportError:
        logger.info("⚠️  psutil未安装，部分功能可能无法使用")
    
    spec.loader.exec_module(module)
    return module

def load_trading_tools_module():
    """加载trading_tools模块"""
    trading_tools_file = Path(__file__).parent / "src" / "utils" / "trading_tools.py"
    spec = importlib.util.spec_from_file_location("trading_tools", trading_tools_file)
    module = importlib.util.module_from_spec(spec)
    
    sys.path.insert(0, str(Path(__file__).parent))
    
    import logging
    class MockLogging:
        @staticmethod
        def get_logger(name):
            return logging.getLogger(name)
    
    sys.modules['src.utils.logging'] = MockLogging()
    sys.modules['src.utils.tools'] = load_tools_module()
    
    spec.loader.exec_module(module)
    return module

def test_general_tools():
    """测试通用工具类"""
    logger.info("🧪 测试通用工具类...")
    
    try:
        tools = load_tools_module()
        
        # 测试日期时间工具
        logger.info("\n📅 测试日期时间工具:")
        dt_tools = tools.DateTimeTools
        
        current_time = dt_tools.now_str()
        logger.info(f"  当前时间: {current_time}")
        
        trading_days = dt_tools.get_trading_days("2024-01-01", "2024-01-07")
        logger.info(f"  2024年第一周的交易日: {trading_days}")
        
        # 测试文件工具
        logger.info("\n📁 测试文件工具:")
        ft = tools.FileTools
        
        # 创建测试目录
        test_dir = ft.ensure_dir("test_output")
        logger.info(f"  创建测试目录: {test_dir}")
        
        # 测试JSON操作
        test_data = {"test": "data", "timestamp": current_time}
        json_file = test_dir / "test.json"
        
        if ft.safe_write_json(json_file, test_data):
            logger.info(f"  JSON写入成功: {json_file}")
            
            read_data = ft.safe_read_json(json_file)
            if read_data == test_data:
                logger.info("  JSON读取验证成功")
        
        # 测试数据工具
        logger.info("\n🔢 测试数据工具:")
        dt = tools.DataTools
        
        safe_result = dt.safe_divide(10, 3, default=0)
        logger.info(f"  安全除法 10/3 = {safe_result:.2f}")
        
        percentage = dt.percentage(25, 100)
        logger.info(f"  百分比计算 25/100 = {percentage}%")
        
        # 测试验证工具
        logger.info("\n✅ 测试验证工具:")
        vt = tools.ValidationTools
        
        email_valid = vt.is_valid_email("<EMAIL>")
        logger.info(f"  邮箱验证 '<EMAIL>': {email_valid}")
        
        date_valid = vt.is_valid_date("2024-01-01")
        logger.info(f"  日期验证 '2024-01-01': {date_valid}")
        
        # 测试性能工具
        logger.info("\n⚡ 测试性能工具:")
        pt = tools.PerformanceTools
        
        @pt.timer
        def test_function():
            time.sleep(0.1)
            return "test complete"
        
        result = test_function()
        logger.info(f"  计时器装饰器测试: {result}")
        
        @pt.retry(max_attempts=3, delay=0.1)
        def unstable_function(attempt_count=[0]):
            attempt_count[0] += 1
            if attempt_count[0] < 3:
                raise Exception("模拟失败")
            return "重试成功"
        
        retry_result = unstable_function()
        logger.info(f"  重试装饰器测试: {retry_result}")
        
        # 测试安全工具
        logger.info("\n🔒 测试安全工具:")
        st = tools.SecurityTools
        
        token = st.generate_token(16)
        logger.info(f"  生成令牌: {token[:8]}...")
        
        password_hash, salt = st.hash_password("test_password")
        password_valid = st.verify_password("test_password", password_hash, salt)
        logger.info(f"  密码验证: {password_valid}")
        
        # 测试系统工具
        logger.info("\n💻 测试系统工具:")
        sys_tools = tools.SystemTools
        
        system_info = sys_tools.get_system_info()
        logger.info(f"  Python版本: {system_info.python_version}")
        logger.info(f"  CPU核心数: {system_info.cpu_count}")
        
        memory_usage = sys_tools.get_memory_usage()
        if memory_usage:
            logger.info(f"  进程内存使用: {memory_usage.get('process_memory_mb', 0):.1f} MB")
        
        logger.info("✅ 通用工具类测试完成")
        return True
        
    except Exception as e:
        logger.info(f"❌ 通用工具类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trading_tools():
    """测试交易工具类"""
    logger.info("\n🧪 测试交易工具类...")
    
    try:
        trading_tools = load_trading_tools_module()
        
        # 创建测试数据
        logger.info("\n📊 创建测试OHLCV数据:")
        dates = pd.date_range('2024-01-01', '2024-01-10', freq='D')
        np.random.seed(42)  # 为了可重复性
        
        # 生成模拟价格数据
        base_price = 100
        returns = np.random.normal(0, 0.02, len(dates))
        prices = base_price * (1 + returns).cumprod()
        
        test_data = pd.DataFrame(index=dates)
        test_data['close'] = prices
        test_data['open'] = test_data['close'].shift(1).fillna(base_price) * (1 + np.random.normal(0, 0.001, len(dates)))
        test_data['high'] = np.maximum(test_data['open'], test_data['close']) * (1 + np.random.uniform(0, 0.005, len(dates)))
        test_data['low'] = np.minimum(test_data['open'], test_data['close']) * (1 - np.random.uniform(0, 0.005, len(dates)))
        test_data['volume'] = np.random.randint(1000, 10000, len(dates))
        
        logger.info(f"  生成了 {len(test_data)} 条OHLCV数据")
        
        # 测试数据验证
        logger.info("\n🔍 测试数据验证:")
        tdt = trading_tools.TradingDataTools
        
        validation_result = tdt.validate_ohlcv_data(test_data)
        logger.info(f"  数据有效性: {validation_result['is_valid']}")
        logger.info(f"  数据质量评分: {validation_result['quality_score']:.2f}")
        if validation_result['issues']:
            logger.info(f"  发现问题: {validation_result['issues']}")
        
        # 测试数据清洗
        logger.info("\n🧹 测试数据清洗:")
        # 人为添加一些问题数据
        dirty_data = test_data.copy()
        dirty_data.iloc[2] = np.nan  # 添加缺失值
        dirty_data.iloc[5, dirty_data.columns.get_loc('close')] = 1000  # 添加异常值
        
        clean_data = tdt.clean_market_data(dirty_data)
        logger.info(f"  原始数据缺失值: {dirty_data.isnull().sum().sum()}")
        logger.info(f"  清洗后缺失值: {clean_data.isnull().sum().sum()}")
        
        # 测试收益率计算
        logger.info("\n📈 测试收益率计算:")
        returns = tdt.calculate_returns(clean_data, method="simple")
        log_returns = tdt.calculate_returns(clean_data, method="log")
        
        logger.info(f"  简单收益率均值: {returns.mean():.4f}")
        logger.info(f"  对数收益率均值: {log_returns.mean():.4f}")
        
        # 测试技术指标
        logger.info("\n📊 测试技术指标:")
        ti_tools = trading_tools.TechnicalIndicatorTools
        
        # 移动平均
        sma_5 = ti_tools.moving_average(clean_data['close'], 5, "simple")
        ema_5 = ti_tools.moving_average(clean_data['close'], 5, "exponential")
        logger.info(f"  SMA(5)最新值: {sma_5.iloc[-1]:.2f}")
        logger.info(f"  EMA(5)最新值: {ema_5.iloc[-1]:.2f}")
        
        # RSI
        rsi = ti_tools.rsi(clean_data['close'], window=7)  # 使用较短周期因为数据少
        if not rsi.isna().all():
            logger.info(f"  RSI最新值: {rsi.dropna().iloc[-1]:.2f}")
        
        # 布林带
        bb = ti_tools.bollinger_bands(clean_data['close'], window=5)
        if not bb['middle'].isna().all():
            logger.info(f"  布林带中轨最新值: {bb['middle'].dropna().iloc[-1]:.2f}")
        
        # MACD
        macd = ti_tools.macd(clean_data['close'], fast=5, slow=8, signal=3)  # 使用较短周期
        if not macd['macd'].isna().all():
            logger.info(f"  MACD最新值: {macd['macd'].dropna().iloc[-1]:.4f}")
        
        # 测试投资组合分析
        logger.info("\n💼 测试投资组合分析:")
        pa_tools = trading_tools.PortfolioAnalysisTools
        
        # 创建多资产收益数据
        returns_data = pd.DataFrame(index=dates[1:])  # 去掉第一天因为收益率计算
        returns_data['Asset1'] = np.random.normal(0.001, 0.02, len(dates)-1)
        returns_data['Asset2'] = np.random.normal(0.0005, 0.015, len(dates)-1)
        
        # 计算投资组合收益
        weights = {"Asset1": 0.6, "Asset2": 0.4}
        portfolio_returns = pa_tools.calculate_portfolio_returns(weights, returns_data)
        
        logger.info(f"  投资组合平均收益率: {portfolio_returns.mean():.4f}")
        
        # 计算风险指标
        risk_metrics = pa_tools.calculate_risk_metrics(portfolio_returns, risk_free_rate=0.02)
        logger.info(f"  夏普比率: {risk_metrics.get('sharpe_ratio', 0):.3f}")
        logger.info(f"  最大回撤: {risk_metrics.get('max_drawdown', 0):.3f}")
        
        # 测试数据质量报告
        logger.info("\n📋 测试数据质量报告:")
        dq_tools = trading_tools.DataQualityTools
        
        quality_report = dq_tools.generate_data_quality_report(clean_data, "TEST_SYMBOL")
        logger.info(f"  符号: {quality_report.symbol}")
        logger.info(f"  数据范围: {quality_report.start_date} 到 {quality_report.end_date}")
        logger.info(f"  总记录数: {quality_report.total_records}")
        logger.info(f"  数据质量评分: {quality_report.data_quality_score:.2f}")
        
        logger.info("✅ 交易工具类测试完成")
        return True
        
    except Exception as e:
        logger.info(f"❌ 交易工具类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_files():
    """清理测试文件"""
    try:
        import shutil
        test_dir = Path("test_output")
        if test_dir.exists():
            shutil.rmtree(test_dir)
            logger.info("🧹 测试文件清理完成")
    except:
        pass

def main():
    """主测试函数"""
    logger.info("🚀 开始测试统一工具库...")
    
    success_count = 0
    
    # 测试通用工具
    if test_general_tools():
        success_count += 1
    
    # 测试交易工具
    if test_trading_tools():
        success_count += 1
    
    # 清理测试文件
    cleanup_test_files()
    
    # 总结
    total_tests = 2
    logger.info(f"\n📊 测试结果总结:")
    logger.info(f"  成功: {success_count}/{total_tests}")
    logger.info(f"  失败: {total_tests - success_count}/{total_tests}")
    
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！")
        return 0
    else:
        logger.info("❌ 部分测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())