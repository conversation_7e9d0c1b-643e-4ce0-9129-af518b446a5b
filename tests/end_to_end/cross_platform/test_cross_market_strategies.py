"""
跨市场策略测试场景
测试跨不同市场的策略执行和协调
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import pytz

from src.market.strategies.multi_market_strategy import MultiMarketStrategy
from src.market.strategies.cross_market_arbitrage import CrossMarketArbitrageStrategy
from src.market.data.data_source_manager import DataSourceManager
from src.domain.models.portfolio_manager import PortfolioManager


class TestCrossMarketStrategies:
    """跨市场策略测试"""
    
    @pytest.fixture
    def setup_market_data(self):
        """设置多市场测试数据"""
        # 美股交易时间 (UTC)
        us_dates = pd.date_range(
            start='2023-01-01 14:30:00',  # 9:30 AM EST
            end='2023-12-31 21:00:00',   # 4:00 PM EST
            freq='1H',
            tz='UTC'
        )
        us_dates = us_dates[us_dates.dayofweek < 5]  # 工作日
        
        # A股交易时间 (UTC)
        cn_dates = pd.date_range(
            start='2023-01-01 01:30:00',  # 9:30 AM CST
            end='2023-12-31 07:00:00',   # 3:00 PM CST
            freq='1H',
            tz='UTC'
        )
        cn_dates = cn_dates[cn_dates.dayofweek < 5]  # 工作日
        
        # 加密货币24/7交易
        crypto_dates = pd.date_range(
            start='2023-01-01',
            end='2023-12-31',
            freq='1H',
            tz='UTC'
        )
        
        np.random.seed(42)
        
        # 美股数据 (SPY ETF)
        us_data = pd.DataFrame({
            'open': 400 + np.random.randn(len(us_dates)).cumsum() * 2,
            'high': 405 + np.random.randn(len(us_dates)).cumsum() * 2,
            'low': 395 + np.random.randn(len(us_dates)).cumsum() * 2,
            'close': 400 + np.random.randn(len(us_dates)).cumsum() * 2,
            'volume': np.random.randint(1000000, 5000000, len(us_dates))
        }, index=us_dates)
        
        # A股数据 (沪深300 ETF)
        cn_data = pd.DataFrame({
            'open': 4000 + np.random.randn(len(cn_dates)).cumsum() * 20,
            'high': 4050 + np.random.randn(len(cn_dates)).cumsum() * 20,
            'low': 3950 + np.random.randn(len(cn_dates)).cumsum() * 20,
            'close': 4000 + np.random.randn(len(cn_dates)).cumsum() * 20,
            'volume': np.random.randint(100000, 1000000, len(cn_dates))
        }, index=cn_dates)
        
        # 加密货币数据 (BTC)
        crypto_data = pd.DataFrame({
            'open': 30000 + np.random.randn(len(crypto_dates)).cumsum() * 100,
            'high': 31000 + np.random.randn(len(crypto_dates)).cumsum() * 100,
            'low': 29000 + np.random.randn(len(crypto_dates)).cumsum() * 100,
            'close': 30000 + np.random.randn(len(crypto_dates)).cumsum() * 100,
            'volume': np.random.randint(100, 10000, len(crypto_dates))
        }, index=crypto_dates)
        
        return {
            'SPY': us_data,
            '510300.SH': cn_data,  # 沪深300ETF
            'BTCUSDT': crypto_data
        }
    
    @pytest.fixture
    def setup_exchange_rates(self):
        """设置汇率数据"""
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        
        return pd.DataFrame({
            'USDCNY': 6.5 + np.random.randn(len(dates)) * 0.1,  # 美元兑人民币
            'USDJPY': 110 + np.random.randn(len(dates)) * 2,    # 美元兑日元
            'EURUSD': 1.1 + np.random.randn(len(dates)) * 0.05  # 欧元兑美元
        }, index=dates)
    
    def test_cross_market_correlation_analysis(self, setup_market_data):
        """测试跨市场相关性分析"""
        market_data = setup_market_data
        
        class CorrelationAnalyzer:
            def analyze_cross_market_correlation(self, data_dict, window=30):
                """分析跨市场相关性"""
                # 将所有市场数据对齐到相同时间频率
                aligned_data = {}
                
                for symbol, data in data_dict.items():
                    # 转换为日频数据
                    daily_data = data.resample('D').last().dropna()
                    aligned_data[symbol] = daily_data['close']
                
                # 创建组合DataFrame
                combined_df = pd.DataFrame(aligned_data)
                combined_df = combined_df.dropna()
                
                # 计算滚动相关性
                correlations = {}
                symbols = list(combined_df.columns)
                
                for i in range(len(symbols)):
                    for j in range(i+1, len(symbols)):
                        pair = f"{symbols[i]}_{symbols[j]}"
                        correlations[pair] = combined_df[symbols[i]].rolling(window).corr(
                            combined_df[symbols[j]]
                        )
                
                return correlations
        
        analyzer = CorrelationAnalyzer()
        correlations = analyzer.analyze_cross_market_correlation(market_data)
        
        # 验证相关性分析结果
        assert len(correlations) == 3  # 3个市场，3对相关性
        
        expected_pairs = ['SPY_510300.SH', 'SPY_BTCUSDT', '510300.SH_BTCUSDT']
        for pair in expected_pairs:
            assert pair in correlations
            assert isinstance(correlations[pair], pd.Series)
            assert len(correlations[pair].dropna()) > 0
            
            # 相关性应该在-1到1之间
            valid_corr = correlations[pair].dropna()
            assert (valid_corr >= -1).all()
            assert (valid_corr <= 1).all()
    
    def test_time_zone_arbitrage_strategy(self, setup_market_data, setup_exchange_rates):
        """测试时区套利策略"""
        
        class TimeZoneArbitrageStrategy(MultiMarketStrategy):
            def __init__(self):
                super().__init__("timezone_arbitrage")
                self.lookback_window = 24  # 24小时回看窗口
            
            def generate_signals(self, market_data, exchange_rates=None):
                signals = {}
                
                # 分析美股收盘对A股开盘的影响
                us_data = market_data.get('SPY')
                cn_data = market_data.get('510300.SH')
                
                if us_data is not None and cn_data is not None:
                    # 计算美股隔夜收益
                    us_overnight_returns = us_data['close'].pct_change()
                    
                    # 预测A股开盘方向
                    cn_signals = pd.DataFrame(index=cn_data.index)
                    cn_signals['signal'] = 0
                    
                    # 简化策略：美股大涨预示A股上涨
                    for date in cn_data.index.date:
                        # 找到前一个美股交易日的收益
                        us_prev_day = us_data[us_data.index.date == date - timedelta(days=1)]
                        if not us_prev_day.empty:
                            us_return = us_prev_day['close'].iloc[-1] / us_prev_day['open'].iloc[0] - 1
                            
                            # 根据美股表现生成A股信号
                            if us_return > 0.02:  # 美股涨超2%
                                cn_signals.loc[cn_signals.index.date == date, 'signal'] = 1
                            elif us_return < -0.02:  # 美股跌超2%
                                cn_signals.loc[cn_signals.index.date == date, 'signal'] = -1
                    
                    signals['510300.SH'] = cn_signals
                
                return signals
        
        strategy = TimeZoneArbitrageStrategy()
        market_data = setup_market_data
        exchange_rates = setup_exchange_rates
        
        # 生成时区套利信号
        signals = strategy.generate_signals(market_data, exchange_rates)
        
        # 验证信号生成
        assert '510300.SH' in signals
        cn_signals = signals['510300.SH']
        
        assert isinstance(cn_signals, pd.DataFrame)
        assert 'signal' in cn_signals.columns
        assert len(cn_signals) > 0
        
        # 验证信号值合理性
        unique_signals = cn_signals['signal'].unique()
        assert all(signal in [-1, 0, 1] for signal in unique_signals)
    
    def test_currency_hedged_strategy(self, setup_market_data, setup_exchange_rates):
        """测试货币对冲策略"""
        
        class CurrencyHedgedStrategy(MultiMarketStrategy):
            def __init__(self):
                super().__init__("currency_hedged")
            
            def generate_signals(self, market_data, exchange_rates):
                signals = {}
                
                # 对于A股投资，考虑汇率对冲
                cn_data = market_data.get('510300.SH')
                usdcny_rate = exchange_rates['USDCNY']
                
                if cn_data is not None:
                    cn_signals = pd.DataFrame(index=cn_data.index)
                    cn_signals['signal'] = 0
                    cn_signals['hedge_ratio'] = 0
                    
                    # 基础策略信号
                    returns = cn_data['close'].pct_change()
                    momentum = returns.rolling(window=10).mean()
                    
                    cn_signals.loc[momentum > 0.01, 'signal'] = 1
                    cn_signals.loc[momentum < -0.01, 'signal'] = -1
                    
                    # 计算汇率对冲比例
                    for date in cn_data.index:
                        try:
                            # 获取对应日期的汇率
                            rate_date = date.date()
                            if rate_date in usdcny_rate.index:
                                current_rate = usdcny_rate.loc[rate_date]
                                rate_ma = usdcny_rate.rolling(window=30).mean().loc[rate_date]
                                
                                # 汇率偏离均值时增加对冲比例
                                rate_deviation = (current_rate - rate_ma) / rate_ma
                                hedge_ratio = min(abs(rate_deviation) * 2, 1.0)  # 最大100%对冲
                                
                                cn_signals.loc[date, 'hedge_ratio'] = hedge_ratio
                        except (KeyError, IndexError):
                            cn_signals.loc[date, 'hedge_ratio'] = 0.5  # 默认50%对冲
                    
                    signals['510300.SH'] = cn_signals
                
                return signals
        
        strategy = CurrencyHedgedStrategy()
        market_data = setup_market_data
        exchange_rates = setup_exchange_rates
        
        # 生成货币对冲信号
        signals = strategy.generate_signals(market_data, exchange_rates)
        
        # 验证对冲策略
        assert '510300.SH' in signals
        cn_signals = signals['510300.SH']
        
        assert 'signal' in cn_signals.columns
        assert 'hedge_ratio' in cn_signals.columns
        
        # 验证对冲比例合理性
        hedge_ratios = cn_signals['hedge_ratio'].dropna()
        assert (hedge_ratios >= 0).all()
        assert (hedge_ratios <= 1).all()
    
    def test_cross_asset_momentum_strategy(self, setup_market_data):
        """测试跨资产动量策略"""
        
        class CrossAssetMomentumStrategy(MultiMarketStrategy):
            def __init__(self):
                super().__init__("cross_asset_momentum")
            
            def generate_signals(self, market_data, lookback=20):
                signals = {}
                
                # 计算所有资产的动量得分
                momentum_scores = {}
                
                for symbol, data in market_data.items():
                    # 计算多时间框架动量
                    daily_data = data.resample('D').last().dropna()
                    
                    # 短期动量 (5天)
                    short_momentum = daily_data['close'].pct_change(5)
                    # 中期动量 (20天)
                    medium_momentum = daily_data['close'].pct_change(20)
                    # 长期动量 (60天)
                    long_momentum = daily_data['close'].pct_change(60)
                    
                    # 综合动量得分
                    momentum_score = (
                        short_momentum * 0.5 +
                        medium_momentum * 0.3 +
                        long_momentum * 0.2
                    )
                    
                    momentum_scores[symbol] = momentum_score
                
                # 基于相对动量排名生成信号
                for symbol in market_data.keys():
                    signal_df = pd.DataFrame(index=market_data[symbol].index)
                    signal_df['signal'] = 0
                    
                    # 将小时数据对齐到日频
                    daily_index = signal_df.resample('D').last().index
                    
                    for date in daily_index:
                        if date in momentum_scores[symbol].index:
                            # 获取当日所有资产的动量得分
                            current_scores = {}
                            for s in momentum_scores.keys():
                                if date in momentum_scores[s].index:
                                    current_scores[s] = momentum_scores[s].loc[date]
                            
                            if len(current_scores) >= 2:
                                # 排名前1/3的资产做多，后1/3做空
                                sorted_assets = sorted(current_scores.items(), 
                                                     key=lambda x: x[1], reverse=True)
                                
                                n_assets = len(sorted_assets)
                                top_third = n_assets // 3
                                
                                if symbol in [asset[0] for asset in sorted_assets[:top_third]]:
                                    signal_value = 1  # 做多
                                elif symbol in [asset[0] for asset in sorted_assets[-top_third:]]:
                                    signal_value = -1  # 做空
                                else:
                                    signal_value = 0  # 中性
                                
                                # 将信号应用到当日所有时间点
                                signal_df.loc[signal_df.index.date == date.date(), 'signal'] = signal_value
                    
                    signals[symbol] = signal_df
                
                return signals
        
        strategy = CrossAssetMomentumStrategy()
        market_data = setup_market_data
        
        # 生成跨资产动量信号
        signals = strategy.generate_signals(market_data)
        
        # 验证信号生成
        assert len(signals) == len(market_data)
        
        for symbol, signal_df in signals.items():
            assert isinstance(signal_df, pd.DataFrame)
            assert 'signal' in signal_df.columns
            assert len(signal_df) > 0
            
            # 验证信号值
            unique_signals = signal_df['signal'].unique()
            assert all(signal in [-1, 0, 1] for signal in unique_signals)
    
    def test_volatility_regime_strategy(self, setup_market_data):
        """测试波动率制度策略"""
        
        class VolatilityRegimeStrategy(MultiMarketStrategy):
            def __init__(self):
                super().__init__("volatility_regime")
            
            def identify_volatility_regime(self, returns, window=30):
                """识别波动率制度"""
                volatility = returns.rolling(window).std() * np.sqrt(252)  # 年化波动率
                
                # 定义波动率制度
                vol_quantiles = volatility.quantile([0.33, 0.67])
                
                regime = pd.Series(index=returns.index, dtype=str)
                regime[volatility <= vol_quantiles.iloc[0]] = 'low_vol'
                regime[(volatility > vol_quantiles.iloc[0]) & 
                      (volatility <= vol_quantiles.iloc[1])] = 'medium_vol'
                regime[volatility > vol_quantiles.iloc[1]] = 'high_vol'
                
                return regime
            
            def generate_signals(self, market_data):
                signals = {}
                
                for symbol, data in market_data.items():
                    daily_data = data.resample('D').last().dropna()
                    returns = daily_data['close'].pct_change()
                    
                    # 识别波动率制度
                    vol_regime = self.identify_volatility_regime(returns)
                    
                    signal_df = pd.DataFrame(index=data.index)
                    signal_df['signal'] = 0
                    signal_df['regime'] = ''
                    
                    # 根据波动率制度调整策略
                    for date in daily_data.index:
                        if date in vol_regime.index:
                            regime = vol_regime.loc[date]
                            
                            # 不同制度下的策略
                            if regime == 'low_vol':
                                # 低波动率：动量策略
                                if len(returns.loc[:date]) >= 10:
                                    momentum = returns.loc[:date].tail(10).mean()
                                    signal_value = 1 if momentum > 0.005 else (-1 if momentum < -0.005 else 0)
                                else:
                                    signal_value = 0
                            elif regime == 'high_vol':
                                # 高波动率：均值回归策略
                                if len(returns.loc[:date]) >= 20:
                                    recent_return = returns.loc[:date].tail(5).sum()
                                    signal_value = -1 if recent_return > 0.05 else (1 if recent_return < -0.05 else 0)
                                else:
                                    signal_value = 0
                            else:
                                # 中等波动率：保守策略
                                signal_value = 0
                            
                            # 将信号应用到当日所有时间点
                            signal_df.loc[signal_df.index.date == date.date(), 'signal'] = signal_value
                            signal_df.loc[signal_df.index.date == date.date(), 'regime'] = regime
                    
                    signals[symbol] = signal_df
                
                return signals
        
        strategy = VolatilityRegimeStrategy()
        market_data = setup_market_data
        
        # 生成波动率制度信号
        signals = strategy.generate_signals(market_data)
        
        # 验证信号生成
        for symbol, signal_df in signals.items():
            assert 'signal' in signal_df.columns
            assert 'regime' in signal_df.columns
            
            # 验证制度识别
            regimes = signal_df['regime'].unique()
            expected_regimes = ['low_vol', 'medium_vol', 'high_vol', '']
            assert all(regime in expected_regimes for regime in regimes)
    
    def test_multi_market_portfolio_optimization(self, setup_market_data, setup_exchange_rates):
        """测试多市场投资组合优化"""
        
        class MultiMarketOptimizer:
            def __init__(self):
                self.risk_free_rate = 0.02
            
            def optimize_portfolio(self, market_data, exchange_rates, target_return=0.12):
                """多市场投资组合优化"""
                # 计算各市场收益率
                returns_data = {}
                
                for symbol, data in market_data.items():
                    daily_data = data.resample('D').last().dropna()
                    returns = daily_data['close'].pct_change().dropna()
                    
                    # 对于非美元资产，考虑汇率影响
                    if symbol == '510300.SH':  # A股，需要考虑汇率
                        usd_cny = exchange_rates['USDCNY'].reindex(returns.index, method='ffill')
                        fx_returns = usd_cny.pct_change()
                        # 美元投资者的总收益 = 本币收益 + 汇率收益
                        total_returns = (1 + returns) * (1 + fx_returns) - 1
                        returns_data[symbol] = total_returns.dropna()
                    else:
                        returns_data[symbol] = returns
                
                # 创建收益率矩阵
                returns_df = pd.DataFrame(returns_data).dropna()
                
                if len(returns_df) < 30:  # 数据不足
                    return None
                
                # 计算协方差矩阵
                cov_matrix = returns_df.cov() * 252  # 年化
                mean_returns = returns_df.mean() * 252  # 年化
                
                # 简化的均值方差优化
                n_assets = len(mean_returns)
                
                # 等权重作为基准
                equal_weights = np.array([1/n_assets] * n_assets)
                
                # 计算风险平价权重
                def risk_parity_weights(cov_matrix):
                    inv_vol = 1 / np.sqrt(np.diag(cov_matrix))
                    weights = inv_vol / inv_vol.sum()
                    return weights
                
                rp_weights = risk_parity_weights(cov_matrix)
                
                return {
                    'symbols': list(returns_df.columns),
                    'equal_weights': equal_weights,
                    'risk_parity_weights': rp_weights,
                    'expected_returns': mean_returns,
                    'covariance_matrix': cov_matrix,
                    'correlation_matrix': returns_df.corr()
                }
        
        optimizer = MultiMarketOptimizer()
        market_data = setup_market_data
        exchange_rates = setup_exchange_rates
        
        # 执行投资组合优化
        optimization_result = optimizer.optimize_portfolio(market_data, exchange_rates)
        
        if optimization_result is not None:
            # 验证优化结果
            assert 'symbols' in optimization_result
            assert 'equal_weights' in optimization_result
            assert 'risk_parity_weights' in optimization_result
            
            # 验证权重和为1
            eq_weights = optimization_result['equal_weights']
            rp_weights = optimization_result['risk_parity_weights']
            
            assert abs(eq_weights.sum() - 1.0) < 0.01
            assert abs(rp_weights.sum() - 1.0) < 0.01
            
            # 验证权重为正
            assert (eq_weights >= 0).all()
            assert (rp_weights >= 0).all()
    
    def test_cross_market_execution_coordination(self, setup_market_data):
        """测试跨市场执行协调"""
        
        class CrossMarketExecutor:
            def __init__(self):
                self.market_sessions = {
                    'US': {'start': 14, 'end': 21},  # UTC时间
                    'CN': {'start': 1, 'end': 7},   # UTC时间
                    'CRYPTO': {'start': 0, 'end': 24}  # 24/7
                }
            
            def is_market_open(self, market, timestamp):
                """检查市场是否开放"""
                hour = timestamp.hour
                session = self.market_sessions.get(market, {'start': 0, 'end': 24})
                
                if market == 'CRYPTO':
                    return True
                
                return session['start'] <= hour < session['end']
            
            def coordinate_execution(self, signals, market_data):
                """协调跨市场执行"""
                execution_plan = []
                
                for symbol, signal_df in signals.items():
                    # 确定市场
                    if 'USDT' in symbol:
                        market = 'CRYPTO'
                    elif '.SH' in symbol or '.SZ' in symbol:
                        market = 'CN'
                    else:
                        market = 'US'
                    
                    # 生成执行计划
                    for timestamp, row in signal_df.iterrows():
                        if row['signal'] != 0:
                            can_execute = self.is_market_open(market, timestamp)
                            
                            execution_plan.append({
                                'timestamp': timestamp,
                                'symbol': symbol,
                                'signal': row['signal'],
                                'market': market,
                                'can_execute': can_execute,
                                'execution_delay': 0 if can_execute else self._calculate_delay(market, timestamp)
                            })
                
                return execution_plan
            
            def _calculate_delay(self, market, timestamp):
                """计算执行延迟"""
                session = self.market_sessions[market]
                current_hour = timestamp.hour
                
                if current_hour < session['start']:
                    return session['start'] - current_hour
                else:
                    return 24 - current_hour + session['start']
        
        executor = CrossMarketExecutor()
        
        # 创建测试信号
        test_signals = {}
        for symbol, data in setup_market_data.items():
            signal_df = pd.DataFrame(index=data.index[:100])  # 取前100个时间点
            signal_df['signal'] = np.random.choice([-1, 0, 1], size=len(signal_df), p=[0.1, 0.8, 0.1])
            test_signals[symbol] = signal_df
        
        # 协调执行
        execution_plan = executor.coordinate_execution(test_signals, setup_market_data)
        
        # 验证执行计划
        assert isinstance(execution_plan, list)
        assert len(execution_plan) > 0
        
        for plan in execution_plan:
            assert 'timestamp' in plan
            assert 'symbol' in plan
            assert 'signal' in plan
            assert 'market' in plan
            assert 'can_execute' in plan
            assert 'execution_delay' in plan
            
            # 验证市场分类正确
            if 'USDT' in plan['symbol']:
                assert plan['market'] == 'CRYPTO'
            elif '.SH' in plan['symbol']:
                assert plan['market'] == 'CN'
            else:
                assert plan['market'] == 'US'