#!/bin/bash

# 配置文件自动化设置脚本
# 版本: 1.0.0
# 描述: 自动创建和配置所有必要的配置文件

set -euo pipefail

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  ${NC}$1"
}

log_success() {
    echo -e "${GREEN}✅ ${NC}$1"
}

log_warning() {
    echo -e "${YELLOW}⚠️  ${NC}$1"
}

log_error() {
    echo -e "${RED}❌ ${NC}$1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
CONFIG_ROOT="$PROJECT_ROOT/config"

echo "🚀 配置文件自动化设置"
echo "项目根目录: $PROJECT_ROOT"
echo "配置根目录: $CONFIG_ROOT"
echo

# 检查配置模板是否存在
check_templates() {
    log_info "检查配置模板..."
    
    local templates_dir="$CONFIG_ROOT/templates"
    local required_templates=(
        "backend.env.template"
        "web-ui.env.template"
        "api.yaml.template"
        "database.yaml.template"
    )
    
    for template in "${required_templates[@]}"; do
        if [ ! -f "$templates_dir/$template" ]; then
            log_warning "模板文件不存在: $template，将创建默认模板"
            create_default_template "$template"
        else
            log_success "模板文件存在: $template"
        fi
    done
}

# 创建默认模板
create_default_template() {
    local template_name="$1"
    local templates_dir="$CONFIG_ROOT/templates"
    
    mkdir -p "$templates_dir"
    
    case "$template_name" in
        "backend.env.template")
            cat > "$templates_dir/$template_name" << 'EOF'
# 后端服务配置
HOST=127.0.0.1
PORT=8000
DEBUG=true
RELOAD=true

# 数据库配置
DATABASE_URL=sqlite:///../../data/db/trading_system.db
DATABASE_ECHO=false

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/backend.log

# CORS配置
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# API配置
API_PREFIX=/api/v1
API_TITLE=量化交易系统API
API_VERSION=2.0.0

# 安全配置
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-here
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 数据源API密钥
YAHOO_FINANCE_API_KEY=optional
TUSHARE_API_TOKEN=optional
BINANCE_API_KEY=optional
BINANCE_SECRET_KEY=optional
FRED_API_KEY=optional

# 性能配置
MAX_WORKERS=4
CACHE_TTL=300
REQUEST_TIMEOUT=30
EOF
            ;;
        "web-ui.env.template")
            cat > "$templates_dir/$template_name" << 'EOF'
# 前端配置
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000
VITE_APP_TITLE=量化交易系统
VITE_APP_VERSION=2.0.0

# 环境配置
NODE_ENV=development
VITE_MODE=development

# 功能开关
VITE_ENABLE_DEBUG=true
VITE_ENABLE_MOCK=false
VITE_ENABLE_WEBSOCKET=true

# 图表配置
VITE_CHART_LIBRARY=lightweight-charts
VITE_CHART_THEME=light

# 缓存配置
VITE_CACHE_ENABLED=true
VITE_CACHE_TTL=300

# 错误追踪
VITE_ERROR_TRACKING=false
VITE_SENTRY_DSN=optional
EOF
            ;;
        "api.yaml.template")
            cat > "$templates_dir/$template_name" << 'EOF'
# API服务配置
server:
  host: 127.0.0.1
  port: 8000
  debug: true
  reload: true
  workers: 1

# 中间件配置
middleware:
  cors:
    allow_origins:
      - "http://localhost:3000"
      - "http://127.0.0.1:3000"
    allow_credentials: true
    allow_methods: ["*"]
    allow_headers: ["*"]
  
  rate_limit:
    enabled: true
    requests_per_minute: 100
    
  compression:
    enabled: true
    minimum_size: 1000

# API配置
api:
  prefix: /api/v1
  title: 量化交易系统API
  version: 2.0.0
  description: 量化交易系统后端API服务
  
# 文档配置
docs:
  enabled: true
  redoc_url: /redoc
  swagger_url: /docs
EOF
            ;;
        "database.yaml.template")
            cat > "$templates_dir/$template_name" << 'EOF'
# 数据库配置
database:
  # 主数据库配置
  primary:
    url: sqlite:///../../data/db/trading_system.db
    echo: false
    pool_size: 20
    max_overflow: 30
    pool_timeout: 30
    pool_recycle: 3600
    
  # 测试数据库配置
  test:
    url: sqlite:///../../data/db/test_trading_system.db
    echo: false
    
# 缓存配置
cache:
  redis:
    enabled: false
    host: localhost
    port: 6379
    db: 0
    password: null
    
  memory:
    enabled: true
    max_size: 1000
    ttl: 300

# 数据源配置
data_sources:
  yahoo_finance:
    enabled: true
    rate_limit: 5
    timeout: 30
    
  tushare:
    enabled: false
    api_token: null
    rate_limit: 200
    
  binance:
    enabled: false
    api_key: null
    secret_key: null
    
  fred:
    enabled: false
    api_key: null
EOF
            ;;
    esac
    
    log_success "创建默认模板: $template_name"
}

# 生成配置文件
generate_config_files() {
    log_info "生成配置文件..."
    
    # 生成后端配置
    local backend_env="$PROJECT_ROOT/web_ui/backend/.env"
    if [ ! -f "$backend_env" ]; then
        log_info "生成后端环境配置..."
        cp "$CONFIG_ROOT/templates/backend.env.template" "$backend_env"
        log_success "后端配置文件已生成: $backend_env"
    else
        log_warning "后端配置文件已存在，跳过生成"
    fi
    
    # 生成前端配置
    local frontend_env="$PROJECT_ROOT/web_ui/frontend/.env"
    if [ ! -f "$frontend_env" ]; then
        log_info "生成前端环境配置..."
        cp "$CONFIG_ROOT/templates/web-ui.env.template" "$frontend_env"
        log_success "前端配置文件已生成: $frontend_env"
    else
        log_warning "前端配置文件已存在，跳过生成"
    fi
    
    # 生成API配置
    local api_config="$CONFIG_ROOT/api/config.yaml"
    mkdir -p "$(dirname "$api_config")"
    if [ ! -f "$api_config" ]; then
        log_info "生成API配置..."
        cp "$CONFIG_ROOT/templates/api.yaml.template" "$api_config"
        log_success "API配置文件已生成: $api_config"
    else
        log_warning "API配置文件已存在，跳过生成"
    fi
    
    # 生成数据库配置
    local db_config="$CONFIG_ROOT/database/config.yaml"
    mkdir -p "$(dirname "$db_config")"
    if [ ! -f "$db_config" ]; then
        log_info "生成数据库配置..."
        cp "$CONFIG_ROOT/templates/database.yaml.template" "$db_config"
        log_success "数据库配置文件已生成: $db_config"
    else
        log_warning "数据库配置文件已存在，跳过生成"
    fi
}

# 验证配置文件
validate_configs() {
    log_info "验证配置文件..."
    
    local config_files=(
        "$PROJECT_ROOT/web_ui/backend/.env"
        "$PROJECT_ROOT/web_ui/frontend/.env"
        "$CONFIG_ROOT/api/config.yaml"
        "$CONFIG_ROOT/database/config.yaml"
    )
    
    for config_file in "${config_files[@]}"; do
        if [ -f "$config_file" ]; then
            log_success "配置文件有效: $config_file"
        else
            log_error "配置文件缺失: $config_file"
        fi
    done
}

# 创建配置目录结构
create_config_structure() {
    log_info "创建配置目录结构..."
    
    local config_dirs=(
        "$CONFIG_ROOT/api"
        "$CONFIG_ROOT/database"
        "$CONFIG_ROOT/security"
        "$CONFIG_ROOT/logging"
        "$CONFIG_ROOT/monitoring"
        "$CONFIG_ROOT/user"
        "$CONFIG_ROOT/backups"
    )
    
    for dir in "${config_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_success "创建目录: $dir"
        else
            log_info "目录已存在: $dir"
        fi
    done
}

# 备份现有配置
backup_existing_configs() {
    log_info "备份现有配置文件..."
    
    local backup_dir="$CONFIG_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    local config_files=(
        "$PROJECT_ROOT/web_ui/backend/.env"
        "$PROJECT_ROOT/web_ui/frontend/.env"
    )
    
    for config_file in "${config_files[@]}"; do
        if [ -f "$config_file" ]; then
            cp "$config_file" "$backup_dir/"
            log_success "备份配置文件: $(basename "$config_file")"
        fi
    done
    
    if [ "$(ls -A "$backup_dir" 2>/dev/null)" ]; then
        log_success "配置文件已备份到: $backup_dir"
    else
        rmdir "$backup_dir"
        log_info "没有需要备份的配置文件"
    fi
}

# 显示配置摘要
show_config_summary() {
    echo
    echo "📋 配置文件设置摘要"
    echo "================================"
    echo
    echo "✅ 配置目录结构已创建"
    echo "✅ 配置模板已准备"
    echo "✅ 环境配置文件已生成"
    echo "✅ API配置文件已生成"
    echo "✅ 数据库配置文件已生成"
    echo
    echo "📁 重要配置文件位置："
    echo "  - 后端配置: web_ui/backend/.env"
    echo "  - 前端配置: web_ui/frontend/.env"
    echo "  - API配置: config/api/config.yaml"
    echo "  - 数据库配置: config/database/config.yaml"
    echo
    echo "🔧 下一步操作："
    echo "  1. 编辑配置文件以设置API密钥和密码"
    echo "  2. 根据环境需要调整配置参数"
    echo "  3. 运行 ./start_system.sh 启动系统"
    echo
}

# 主函数
main() {
    echo "开始配置文件自动化设置..."
    echo
    
    # 切换到项目根目录
    cd "$PROJECT_ROOT"
    
    # 执行设置步骤
    backup_existing_configs
    create_config_structure
    check_templates
    generate_config_files
    validate_configs
    show_config_summary
    
    log_success "配置文件设置完成！"
}

# 运行主函数
main "$@"
