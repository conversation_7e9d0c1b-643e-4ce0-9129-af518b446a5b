"""Configuration settings management."""

from typing import Dict, Any, Optional
import os
import yaml
from pathlib import Path

from src.common.utils.logging import get_logger


class Config:
    """Central configuration management."""
    
    def __init__(self):
        self.logger = get_logger("config")
        self._config: Dict[str, Any] = {}
        self._load_default_config()
        
    def _load_default_config(self):
        """Load default configuration."""
        self._config = {
            "database": {
                "url": "sqlite:///data/db/trading_system.db",
                "echo": False
            },
            "api": {
                "host": "0.0.0.0",
                "port": 8000,
                "reload": True
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            },
            "trading": {
                "max_position_size": 0.1,
                "risk_tolerance": 0.05
            }
        }
        
    def load_from_file(self, config_path: str):
        """Load configuration from file."""
        try:
            config_file = Path(config_path)
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f)
                    if file_config:
                        self._config.update(file_config)
                        self.logger.info(f"Loaded configuration from {config_path}")
        except Exception as e:
            self.logger.error(f"Failed to load config from {config_path}: {e}")
            
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value."""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
                
        return value
        
    def set(self, key: str, value: Any):
        """Set configuration value."""
        keys = key.split('.')
        config_dict = self._config
        
        for k in keys[:-1]:
            if k not in config_dict:
                config_dict[k] = {}
            config_dict = config_dict[k]
            
        config_dict[keys[-1]] = value
        
    def get_all(self) -> Dict[str, Any]:
        """Get all configuration."""
        return self._config.copy()


# Global configuration instance
config = Config()

# Try to load from default locations
config_paths = [
    "config/config.yaml",
    "config/settings.yaml", 
    os.environ.get("CONFIG_PATH", "")
]

for path in config_paths:
    if path and os.path.exists(path):
        config.load_from_file(path)
        break