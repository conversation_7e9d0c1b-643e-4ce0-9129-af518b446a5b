"""
Configuration Loader
Supports loading multiple configuration formats with hot reload and caching.
"""

import os
import json
import yaml
import configparser
import threading
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from src.common.utils.logging import get_logger


@dataclass
class ConfigCache:
    """Configuration cache entry."""
    data: Dict[str, Any]
    file_path: Path
    last_modified: datetime
    last_accessed: datetime
    access_count: int = 0


class ConfigFileHandler(FileSystemEventHandler):
    """File system event handler for configuration file changes."""
    
    def __init__(self, config_loader):
        """
        Initialize file handler.
        
        Args:
            config_loader: Reference to ConfigLoader instance
        """
        self.config_loader = config_loader
        self.logger = get_logger("config_file_handler")
    
    def on_modified(self, event):
        """Handle file modification events."""
        if not event.is_directory:
            file_path = Path(event.src_path)
            if self.config_loader._is_config_file(file_path):
                self.logger.info(f"Configuration file modified: {file_path}")
                self.config_loader._handle_file_change(file_path)
    
    def on_created(self, event):
        """Handle file creation events."""
        if not event.is_directory:
            file_path = Path(event.src_path)
            if self.config_loader._is_config_file(file_path):
                self.logger.info(f"Configuration file created: {file_path}")
                self.config_loader._handle_file_change(file_path)
    
    def on_deleted(self, event):
        """Handle file deletion events."""
        if not event.is_directory:
            file_path = Path(event.src_path)
            if self.config_loader._is_config_file(file_path):
                self.logger.info(f"Configuration file deleted: {file_path}")
                self.config_loader._handle_file_deletion(file_path)


class ConfigLoader:
    """
    Configuration loader with support for multiple formats and hot reload.
    
    Supports YAML, JSON, INI, and .env file formats with intelligent
    merging, caching, and change notification.
    """
    
    def __init__(self, config_manager=None):
        """
        Initialize configuration loader.
        
        Args:
            config_manager: Reference to configuration manager
        """
        self.logger = get_logger("config_loader")
        self.config_manager = config_manager
        
        # Configuration cache
        self._cache: Dict[str, ConfigCache] = {}
        self._cache_lock = threading.RLock()
        
        # File watching
        self._observer: Optional[Observer] = None
        self._watched_paths: set = set()
        self._change_callbacks: List[Callable[[Path, Dict[str, Any]], None]] = []
        
        # Configuration merging rules
        self._merge_strategies: Dict[str, str] = {
            'default': 'deep_merge',  # deep merge by default
            'list': 'replace',        # replace lists
            'credentials': 'replace'  # replace sensitive data
        }
        
        # Environment variable expansion
        self._env_var_pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'
        
        # Supported file extensions
        self._supported_extensions = {'.yaml', '.yml', '.json', '.ini', '.env'}
        
        self.logger.info("Configuration loader initialized")
    
    def load_file(self, file_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """
        Load configuration from a file.
        
        Args:
            file_path: Path to configuration file
            
        Returns:
            Configuration data or None if loading failed
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            self.logger.warning(f"Configuration file not found: {file_path}")
            return None
        
        # Check cache first
        cache_key = str(file_path.absolute())
        cached_config = self._get_cached_config(cache_key, file_path)
        
        if cached_config is not None:
            return cached_config
        
        # Load from file
        try:
            config_data = self._load_file_by_format(file_path)
            
            if config_data is not None:
                # Expand environment variables
                config_data = self._expand_environment_variables(config_data)
                
                # Cache the configuration
                self._cache_config(cache_key, config_data, file_path)
                
                self.logger.debug(f"Loaded configuration from: {file_path}")
                return config_data
            
        except Exception as e:
            self.logger.error(f"Failed to load configuration from {file_path}: {e}")
        
        return None
    
    def load_multiple_files(self, file_paths: List[Union[str, Path]], 
                          merge_strategy: str = 'deep_merge') -> Dict[str, Any]:
        """
        Load and merge multiple configuration files.
        
        Args:
            file_paths: List of configuration file paths
            merge_strategy: Strategy for merging configurations
            
        Returns:
            Merged configuration data
        """
        merged_config = {}
        
        for file_path in file_paths:
            config_data = self.load_file(file_path)
            
            if config_data:
                merged_config = self._merge_configs(merged_config, config_data, merge_strategy)
        
        self.logger.info(f"Merged {len(file_paths)} configuration files")
        return merged_config
    
    def load_environment_config(self, environment: str, base_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Load environment-specific configuration with inheritance.
        
        Args:
            environment: Environment name (development, production, etc.)
            base_config: Base configuration to merge with
            
        Returns:
            Environment-specific configuration
        """
        if base_config is None:
            base_config = {}
        
        # Load base configuration files
        config_files = []
        
        if self.config_manager:
            config_root = self.config_manager.config_root
            
            # Base system configuration
            base_files = [
                config_root / "core" / "system.yaml",
                config_root / "core" / "markets.yaml",
                config_root / "datasources" / "sources.yaml"
            ]
            
            config_files.extend([f for f in base_files if f.exists()])
            
            # Environment-specific configuration
            env_file = config_root / "environments" / f"{environment}.yaml"
            if env_file.exists():
                config_files.append(env_file)
        
        # Load and merge all configuration files
        merged_config = self.load_multiple_files(config_files)
        
        # Merge with base configuration
        if base_config:
            merged_config = self._merge_configs(base_config, merged_config)
        
        # Set environment metadata
        merged_config['_environment'] = environment
        merged_config['_loaded_at'] = datetime.now().isoformat()
        
        return merged_config
    
    def save_config(self, config_data: Dict[str, Any], file_path: Union[str, Path], 
                   format_type: str = None) -> bool:
        """
        Save configuration to file.
        
        Args:
            config_data: Configuration data to save
            file_path: Target file path
            format_type: File format (auto-detected if None)
            
        Returns:
            True if save was successful
        """
        file_path = Path(file_path)
        
        if format_type is None:
            format_type = self._detect_format(file_path)
        
        try:
            # Ensure parent directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save based on format
            if format_type in ['yaml', 'yml']:
                self._save_yaml(config_data, file_path)
            elif format_type == 'json':
                self._save_json(config_data, file_path)
            elif format_type == 'ini':
                self._save_ini(config_data, file_path)
            elif format_type == 'env':
                self._save_env(config_data, file_path)
            else:
                raise ValueError(f"Unsupported format: {format_type}")
            
            # Update cache
            cache_key = str(file_path.absolute())
            self._cache_config(cache_key, config_data, file_path)
            
            self.logger.info(f"Saved configuration to: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save configuration to {file_path}: {e}")
            return False
    
    def enable_hot_reload(self, watch_paths: List[Union[str, Path]] = None):
        """
        Enable hot reload for configuration files.
        
        Args:
            watch_paths: Paths to watch for changes (defaults to config manager paths)
        """
        if self._observer is not None:
            self.logger.warning("Hot reload is already enabled")
            return
        
        if watch_paths is None and self.config_manager:
            watch_paths = [self.config_manager.config_root]
        elif watch_paths is None:
            watch_paths = [Path.cwd() / "config"]
        
        try:
            self._observer = Observer()
            event_handler = ConfigFileHandler(self)
            
            for watch_path in watch_paths:
                watch_path = Path(watch_path)
                if watch_path.exists():
                    self._observer.schedule(event_handler, str(watch_path), recursive=True)
                    self._watched_paths.add(watch_path)
                    self.logger.info(f"Watching configuration path: {watch_path}")
            
            self._observer.start()
            self.logger.info("Hot reload enabled for configuration files")
            
        except Exception as e:
            self.logger.error(f"Failed to enable hot reload: {e}")
    
    def disable_hot_reload(self):
        """Disable hot reload for configuration files."""
        if self._observer is not None:
            self._observer.stop()
            self._observer.join()
            self._observer = None
            self._watched_paths.clear()
            self.logger.info("Hot reload disabled")
    
    def add_change_callback(self, callback: Callable[[Path, Dict[str, Any]], None]):
        """
        Add callback for configuration changes.
        
        Args:
            callback: Function to call when configuration changes
        """
        self._change_callbacks.append(callback)
        self.logger.debug(f"Added configuration change callback: {callback.__name__}")
    
    def remove_change_callback(self, callback: Callable[[Path, Dict[str, Any]], None]):
        """
        Remove configuration change callback.
        
        Args:
            callback: Callback function to remove
        """
        if callback in self._change_callbacks:
            self._change_callbacks.remove(callback)
            self.logger.debug(f"Removed configuration change callback: {callback.__name__}")
    
    def clear_cache(self):
        """Clear configuration cache."""
        with self._cache_lock:
            self._cache.clear()
            self.logger.info("Configuration cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get configuration cache statistics."""
        with self._cache_lock:
            total_entries = len(self._cache)
            total_access_count = sum(entry.access_count for entry in self._cache.values())
            
            return {
                'total_entries': total_entries,
                'total_access_count': total_access_count,
                'cache_keys': list(self._cache.keys())
            }
    
    def _get_cached_config(self, cache_key: str, file_path: Path) -> Optional[Dict[str, Any]]:
        """Get configuration from cache if valid."""
        with self._cache_lock:
            if cache_key not in self._cache:
                return None
            
            cache_entry = self._cache[cache_key]
            
            # Check if file has been modified
            try:
                file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_mtime > cache_entry.last_modified:
                    # File has been modified, remove from cache
                    del self._cache[cache_key]
                    return None
            except OSError:
                # File doesn't exist, remove from cache
                del self._cache[cache_key]
                return None
            
            # Update access statistics
            cache_entry.last_accessed = datetime.now()
            cache_entry.access_count += 1
            
            return cache_entry.data.copy()
    
    def _cache_config(self, cache_key: str, config_data: Dict[str, Any], file_path: Path):
        """Cache configuration data."""
        with self._cache_lock:
            try:
                file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                
                self._cache[cache_key] = ConfigCache(
                    data=config_data.copy(),
                    file_path=file_path,
                    last_modified=file_mtime,
                    last_accessed=datetime.now()
                )
            except OSError:
                # File doesn't exist, don't cache
                pass
    
    def _load_file_by_format(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Load file based on its format."""
        format_type = self._detect_format(file_path)
        
        if format_type in ['yaml', 'yml']:
            return self._load_yaml(file_path)
        elif format_type == 'json':
            return self._load_json(file_path)
        elif format_type == 'ini':
            return self._load_ini(file_path)
        elif format_type == 'env':
            return self._load_env(file_path)
        else:
            self.logger.warning(f"Unsupported configuration format: {format_type}")
            return None
    
    def _detect_format(self, file_path: Path) -> str:
        """Detect configuration file format."""
        suffix = file_path.suffix.lower()
        
        if suffix in ['.yaml', '.yml']:
            return 'yaml'
        elif suffix == '.json':
            return 'json'
        elif suffix == '.ini':
            return 'ini'
        elif suffix == '.env':
            return 'env'
        else:
            return 'unknown'
    
    def _load_yaml(self, file_path: Path) -> Dict[str, Any]:
        """Load YAML configuration file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    
    def _load_json(self, file_path: Path) -> Dict[str, Any]:
        """Load JSON configuration file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _load_ini(self, file_path: Path) -> Dict[str, Any]:
        """Load INI configuration file."""
        config = configparser.ConfigParser()
        config.read(file_path, encoding='utf-8')
        
        # Convert to nested dictionary
        result = {}
        for section_name in config.sections():
            result[section_name] = dict(config[section_name])
        
        return result
    
    def _load_env(self, file_path: Path) -> Dict[str, Any]:
        """Load .env configuration file."""
        config = {}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # Skip empty lines and comments
                if not line or line.startswith('#'):
                    continue
                
                # Parse key=value pairs
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # Remove quotes if present
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    
                    config[key] = value
                else:
                    self.logger.warning(f"Invalid .env format at line {line_num} in {file_path}")
        
        return config
    
    def _save_yaml(self, config_data: Dict[str, Any], file_path: Path):
        """Save configuration as YAML."""
        with open(file_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    def _save_json(self, config_data: Dict[str, Any], file_path: Path):
        """Save configuration as JSON."""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    def _save_ini(self, config_data: Dict[str, Any], file_path: Path):
        """Save configuration as INI."""
        config = configparser.ConfigParser()
        
        for section_name, section_data in config_data.items():
            if isinstance(section_data, dict):
                config[section_name] = {}
                for key, value in section_data.items():
                    config[section_name][key] = str(value)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            config.write(f)
    
    def _save_env(self, config_data: Dict[str, Any], file_path: Path):
        """Save configuration as .env file."""
        with open(file_path, 'w', encoding='utf-8') as f:
            for key, value in config_data.items():
                if isinstance(value, str) and (' ' in value or '"' in value):
                    f.write(f'{key}="{value}"\n')
                else:
                    f.write(f'{key}={value}\n')
    
    def _merge_configs(self, base_config: Dict[str, Any], new_config: Dict[str, Any], 
                      strategy: str = 'deep_merge') -> Dict[str, Any]:
        """Merge two configuration dictionaries."""
        if strategy == 'replace':
            return new_config.copy()
        elif strategy == 'shallow_merge':
            result = base_config.copy()
            result.update(new_config)
            return result
        elif strategy == 'deep_merge':
            return self._deep_merge(base_config, new_config)
        else:
            self.logger.warning(f"Unknown merge strategy: {strategy}, using deep_merge")
            return self._deep_merge(base_config, new_config)
    
    def _deep_merge(self, base: Dict[str, Any], new: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries."""
        result = base.copy()
        
        for key, value in new.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _expand_environment_variables(self, config_data: Any) -> Any:
        """Recursively expand environment variables in configuration."""
        import re
        
        if isinstance(config_data, dict):
            return {key: self._expand_environment_variables(value) for key, value in config_data.items()}
        elif isinstance(config_data, list):
            return [self._expand_environment_variables(item) for item in config_data]
        elif isinstance(config_data, str):
            def replace_env_var(match):
                var_name = match.group(1)
                default_value = match.group(2) if match.group(2) is not None else ""
                return os.getenv(var_name, default_value)
            
            return re.sub(self._env_var_pattern, replace_env_var, config_data)
        else:
            return config_data
    
    def _is_config_file(self, file_path: Path) -> bool:
        """Check if file is a configuration file."""
        return file_path.suffix.lower() in self._supported_extensions
    
    def _handle_file_change(self, file_path: Path):
        """Handle configuration file change."""
        try:
            # Remove from cache to force reload
            cache_key = str(file_path.absolute())
            with self._cache_lock:
                if cache_key in self._cache:
                    del self._cache[cache_key]
            
            # Load new configuration
            new_config = self.load_file(file_path)
            
            if new_config is not None:
                # Notify callbacks
                for callback in self._change_callbacks:
                    try:
                        callback(file_path, new_config)
                    except Exception as e:
                        self.logger.error(f"Error in configuration change callback: {e}")
                
                self.logger.info(f"Configuration reloaded: {file_path}")
            
        except Exception as e:
            self.logger.error(f"Error handling configuration file change {file_path}: {e}")
    
    def _handle_file_deletion(self, file_path: Path):
        """Handle configuration file deletion."""
        # Remove from cache
        cache_key = str(file_path.absolute())
        with self._cache_lock:
            if cache_key in self._cache:
                del self._cache[cache_key]
        
        self.logger.warning(f"Configuration file deleted: {file_path}")
    
    def __del__(self):
        """Cleanup when loader is destroyed."""
        if self._observer is not None:
            self.disable_hot_reload()