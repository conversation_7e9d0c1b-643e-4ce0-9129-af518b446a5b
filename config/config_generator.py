import logging
logger = logging.getLogger(__name__)
"""
Configuration Generator
Automatically generates configuration files from templates and defaults.
"""

import os
import secrets
import string
from pathlib import Path
from typing import Dict, Any, List, Optional, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime
from jinja2 import Template, Environment, FileSystemLoader

from src.common.utils.logging import get_logger


@dataclass
class ConfigTemplate:
    """Configuration template definition."""
    name: str
    template_path: str
    target_path: str
    variables: Dict[str, Any] = field(default_factory=dict)
    required_variables: List[str] = field(default_factory=list)
    description: str = ""
    version: str = "1.0"


@dataclass
class GenerationContext:
    """Context for configuration generation."""
    environment: str
    project_name: str = "Quantitative Trading System"
    version: str = "1.0.0"
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    user_inputs: Dict[str, Any] = field(default_factory=dict)
    auto_generated: bool = True


class ConfigGenerator:
    """
    Configuration generator with template system and interactive wizard.
    
    Generates configuration files from templates with environment-specific
    defaults and user customization options.
    """
    
    def __init__(self, config_manager=None):
        """
        Initialize configuration generator.
        
        Args:
            config_manager: Reference to configuration manager
        """
        self.logger = get_logger("config_generator")
        self.config_manager = config_manager
        
        # Template system
        self._templates: Dict[str, ConfigTemplate] = {}
        self._template_env: Optional[Environment] = None
        
        # Default values for different environments
        self._environment_defaults = {
            'development': {
                'debug': True,
                'log_level': 'DEBUG',
                'database_echo': True,
                'api_cors_origins': ['*'],
                'security_strict': False
            },
            'testing': {
                'debug': False,
                'log_level': 'INFO',
                'database_echo': False,
                'api_cors_origins': ['http://localhost:3000'],
                'security_strict': True
            },
            'staging': {
                'debug': False,
                'log_level': 'INFO',
                'database_echo': False,
                'api_cors_origins': ['https://staging.example.com'],
                'security_strict': True
            },
            'production': {
                'debug': False,
                'log_level': 'WARNING',
                'database_echo': False,
                'api_cors_origins': ['https://example.com'],
                'security_strict': True
            }
        }
        
        # Configuration generators for different types
        self._config_generators = {
            'system': self._generate_system_config,
            'environment': self._generate_environment_config,
            'datasources': self._generate_datasources_config,
            'markets': self._generate_markets_config,
            'logging': self._generate_logging_config,
            'security': self._generate_security_config,
            'monitoring': self._generate_monitoring_config
        }
        
        # Initialize template system
        self._initialize_templates()
        
        self.logger.info("Configuration generator initialized")
    
    def _initialize_templates(self):
        """Initialize template system and load templates."""
        if self.config_manager:
            template_dir = self.config_manager.config_root / "templates"
        else:
            template_dir = Path("config/templates")
        
        if template_dir.exists():
            self._template_env = Environment(
                loader=FileSystemLoader(str(template_dir)),
                trim_blocks=True,
                lstrip_blocks=True
            )
            self.logger.info(f"Template system initialized with directory: {template_dir}")
        else:
            self.logger.warning(f"Template directory not found: {template_dir}")
    
    def generate_config(self, config_type: str, environment: str = 'development', 
                       user_inputs: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        Generate configuration of specified type.
        
        Args:
            config_type: Type of configuration to generate
            environment: Target environment
            user_inputs: User-provided configuration values
            
        Returns:
            Generated configuration data
        """
        if config_type not in self._config_generators:
            self.logger.error(f"Unknown configuration type: {config_type}")
            return None
        
        context = GenerationContext(
            environment=environment,
            user_inputs=user_inputs or {}
        )
        
        try:
            generator_func = self._config_generators[config_type]
            config_data = generator_func(context)
            
            if config_data:
                self.logger.info(f"Generated {config_type} configuration for environment: {environment}")
                return config_data
            else:
                self.logger.error(f"Failed to generate {config_type} configuration")
                return None
                
        except Exception as e:
            self.logger.error(f"Error generating {config_type} configuration: {e}")
            return None
    
    def generate_environment_config(self, environment: str) -> Dict[str, Any]:
        """
        Generate environment-specific configuration.
        
        Args:
            environment: Environment name
            
        Returns:
            Environment configuration
        """
        return self.generate_config('environment', environment)
    
    def generate_all_configs(self, environment: str = 'development', 
                           user_inputs: Dict[str, Any] = None) -> Dict[str, Dict[str, Any]]:
        """
        Generate all configuration files for an environment.
        
        Args:
            environment: Target environment
            user_inputs: User-provided configuration values
            
        Returns:
            Dictionary of all generated configurations
        """
        all_configs = {}
        
        config_types = ['system', 'environment', 'datasources', 'markets', 'logging', 'security']
        
        for config_type in config_types:
            config_data = self.generate_config(config_type, environment, user_inputs)
            if config_data:
                all_configs[config_type] = config_data
        
        self.logger.info(f"Generated {len(all_configs)} configuration files for environment: {environment}")
        return all_configs
    
    def interactive_config_wizard(self) -> Dict[str, Any]:
        """
        Run interactive configuration wizard.
        
        Returns:
            User-provided configuration values
        """
        logger.info("=== Configuration Wizard ===")
        logger.info("This wizard will help you set up your trading system configuration.")
        logger.info()
        
        user_inputs = {}
        
        # Environment selection
        environments = ['development', 'testing', 'staging', 'production']
        logger.info("Available environments:")
        for i, env in enumerate(environments, 1):
            logger.info(f"  {i}. {env}")
        
        while True:
            try:
                choice = input("Select environment (1-4): ").strip()
                env_index = int(choice) - 1
                if 0 <= env_index < len(environments):
                    user_inputs['environment'] = environments[env_index]
                    break
                else:
                    logger.info("Invalid choice. Please select 1-4.")
            except ValueError:
                logger.info("Invalid input. Please enter a number.")
        
        logger.info(f"\nSelected environment: {user_inputs['environment']}")
        logger.info()
        
        # Database configuration
        logger.info("=== Database Configuration ===")
        user_inputs['database_host'] = input("Database host [localhost]: ").strip() or "localhost"
        
        while True:
            try:
                port_input = input("Database port [5432]: ").strip()
                user_inputs['database_port'] = int(port_input) if port_input else 5432
                break
            except ValueError:
                logger.info("Invalid port number. Please enter a valid integer.")
        
        user_inputs['database_name'] = input("Database name [trading_system]: ").strip() or "trading_system"
        user_inputs['database_username'] = input("Database username [postgres]: ").strip() or "postgres"
        user_inputs['database_password'] = input("Database password (optional): ").strip()
        
        # API configuration
        logger.info("\n=== API Configuration ===")
        user_inputs['api_host'] = input("API host [0.0.0.0]: ").strip() or "0.0.0.0"
        
        while True:
            try:
                port_input = input("API port [8000]: ").strip()
                user_inputs['api_port'] = int(port_input) if port_input else 8000
                break
            except ValueError:
                logger.info("Invalid port number. Please enter a valid integer.")
        
        # Security configuration
        logger.info("\n=== Security Configuration ===")
        generate_secrets = input("Generate secure secrets automatically? [Y/n]: ").strip().lower()
        user_inputs['generate_secrets'] = generate_secrets != 'n'
        
        if not user_inputs['generate_secrets']:
            user_inputs['jwt_secret'] = input("JWT secret (min 32 chars): ").strip()
            user_inputs['app_secret'] = input("Application secret (min 32 chars): ").strip()
        
        # Data sources
        logger.info("\n=== Data Sources ===")
        enable_demo_data = input("Enable demo data sources? [Y/n]: ").strip().lower()
        user_inputs['enable_demo_data'] = enable_demo_data != 'n'
        
        logger.info("\nConfiguration wizard completed!")
        return user_inputs
    
    def create_template(self, template_name: str, template_content: str, 
                       target_path: str, variables: Dict[str, Any] = None) -> ConfigTemplate:
        """
        Create a new configuration template.
        
        Args:
            template_name: Template name
            template_content: Template content (Jinja2 format)
            target_path: Target file path for generated config
            variables: Template variables
            
        Returns:
            Created template
        """
        template = ConfigTemplate(
            name=template_name,
            template_path=f"{template_name}.j2",
            target_path=target_path,
            variables=variables or {}
        )
        
        self._templates[template_name] = template
        
        # Save template to file if template environment is available
        if self._template_env and self.config_manager:
            template_file = self.config_manager.config_root / "templates" / f"{template_name}.j2"
            template_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(template_content)
        
        self.logger.info(f"Created configuration template: {template_name}")
        return template
    
    def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """
        Render configuration template.
        
        Args:
            template_name: Template name
            context: Template context variables
            
        Returns:
            Rendered template content
        """
        if not self._template_env:
            raise RuntimeError("Template environment not initialized")
        
        try:
            template = self._template_env.get_template(f"{template_name}.j2")
            return template.render(**context)
        except Exception as e:
            self.logger.error(f"Error rendering template {template_name}: {e}")
            raise
    
    def _generate_system_config(self, context: GenerationContext) -> Dict[str, Any]:
        """Generate system configuration."""
        env_defaults = self._environment_defaults.get(context.environment, {})
        user_inputs = context.user_inputs
        
        # Generate secure secrets if needed
        jwt_secret = user_inputs.get('jwt_secret')
        app_secret = user_inputs.get('app_secret')
        
        if user_inputs.get('generate_secrets', True):
            jwt_secret = self._generate_secret(64)
            app_secret = self._generate_secret(64)
        
        config = {
            'database': {
                'host': user_inputs.get('database_host', 'localhost'),
                'port': user_inputs.get('database_port', 5432),
                'database': user_inputs.get('database_name', 'trading_system'),
                'username': user_inputs.get('database_username', 'postgres'),
                'password': user_inputs.get('database_password', ''),
                'pool_size': 10,
                'echo': env_defaults.get('database_echo', False)
            },
            'api': {
                'host': user_inputs.get('api_host', '0.0.0.0'),
                'port': user_inputs.get('api_port', 8000),
                'debug': env_defaults.get('debug', False),
                'cors_origins': env_defaults.get('api_cors_origins', ['*']),
                'rate_limit': '100/minute',
                'auth_enabled': True,
                'jwt_secret': jwt_secret or self._generate_secret(64),
                'docs_enabled': context.environment != 'production',
                'max_request_size': 16777216,  # 16MB
                'request_timeout': 30
            },
            'logging': {
                'level': env_defaults.get('log_level', 'INFO'),
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file_path': 'logs/trading_system.log',
                'max_file_size': 10485760,  # 10MB
                'backup_count': 5
            },
            'security': {
                'secret_key': app_secret or self._generate_secret(64),
                'algorithm': 'HS256',
                'access_token_expire_minutes': 30,
                'refresh_token_expire_days': 7,
                'password_min_length': 8,
                'max_login_attempts': 5,
                'lockout_duration_minutes': 15
            },
            'risk': {
                'max_position_size': 0.1,
                'max_portfolio_risk': 0.02,
                'stop_loss_pct': 0.05,
                'max_drawdown_pct': 0.2,
                'max_correlation': 0.8,
                'position_limit_per_symbol': 0.05
            },
            'backtest': {
                'initial_capital': 100000.0,
                'commission_rate': 0.001,
                'slippage_rate': 0.0005,
                'benchmark_symbol': 'SPY',
                'cash_interest_rate': 0.02
            }
        }
        
        return config
    
    def _generate_environment_config(self, context: GenerationContext) -> Dict[str, Any]:
        """Generate environment-specific configuration."""
        env_defaults = self._environment_defaults.get(context.environment, {})
        
        config = {
            'environment': context.environment,
            'debug': env_defaults.get('debug', False),
            'testing': context.environment == 'testing',
            'production': context.environment == 'production',
            'features': {
                'web_ui_enabled': True,
                'api_docs_enabled': context.environment != 'production',
                'metrics_enabled': True,
                'monitoring_enabled': context.environment in ['staging', 'production']
            },
            'performance': {
                'cache_enabled': True,
                'cache_ttl': 300,  # 5 minutes
                'async_processing': True,
                'worker_processes': 4 if context.environment == 'production' else 2
            }
        }
        
        return config
    
    def _generate_datasources_config(self, context: GenerationContext) -> Dict[str, Any]:
        """Generate data sources configuration."""
        config = {}
        
        # Demo data source (always available)
        if context.user_inputs.get('enable_demo_data', True):
            config['demo'] = {
                'adapter_class': 'src.market.adapters.DemoDataAdapter',
                'enabled': True,
                'priority': 1,
                'rate_limit': {
                    'requests_per_second': 10
                },
                'default_params': {
                    'symbols': ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN'],
                    'timeframe': '1d',
                    'history_days': 365
                }
            }
        
        # Alpha Vantage (if API key provided)
        if context.user_inputs.get('alpha_vantage_api_key'):
            config['alpha_vantage'] = {
                'adapter_class': 'src.market.adapters.AlphaVantageAdapter',
                'enabled': True,
                'priority': 2,
                'rate_limit': {
                    'requests_per_minute': 5
                },
                'credentials': {
                    'api_key': context.user_inputs['alpha_vantage_api_key']
                },
                'default_params': {
                    'outputsize': 'compact'
                }
            }
        
        # Yahoo Finance (free tier)
        config['yahoo_finance'] = {
            'adapter_class': 'src.market.adapters.YahooFinanceAdapter',
            'enabled': True,
            'priority': 3,
            'rate_limit': {
                'requests_per_second': 2
            },
            'default_params': {
                'period': '1y',
                'interval': '1d'
            }
        }
        
        return config
    
    def _generate_markets_config(self, context: GenerationContext) -> Dict[str, Any]:
        """Generate markets configuration."""
        config = {
            'markets': {
                'NYSE': {
                    'name': 'New York Stock Exchange',
                    'timezone': 'America/New_York',
                    'currency': 'USD',
                    'trading_hours': {
                        'monday': {'open': '09:30', 'close': '16:00'},
                        'tuesday': {'open': '09:30', 'close': '16:00'},
                        'wednesday': {'open': '09:30', 'close': '16:00'},
                        'thursday': {'open': '09:30', 'close': '16:00'},
                        'friday': {'open': '09:30', 'close': '16:00'}
                    },
                    'holidays': []
                },
                'NASDAQ': {
                    'name': 'NASDAQ Stock Market',
                    'timezone': 'America/New_York',
                    'currency': 'USD',
                    'trading_hours': {
                        'monday': {'open': '09:30', 'close': '16:00'},
                        'tuesday': {'open': '09:30', 'close': '16:00'},
                        'wednesday': {'open': '09:30', 'close': '16:00'},
                        'thursday': {'open': '09:30', 'close': '16:00'},
                        'friday': {'open': '09:30', 'close': '16:00'}
                    },
                    'holidays': []
                }
            },
            'default_market': 'NYSE',
            'supported_asset_types': ['stock', 'etf', 'option', 'future']
        }
        
        return config
    
    def _generate_logging_config(self, context: GenerationContext) -> Dict[str, Any]:
        """Generate logging configuration."""
        env_defaults = self._environment_defaults.get(context.environment, {})
        
        config = {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'standard': {
                    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                },
                'detailed': {
                    'format': '%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s'
                },
                'json': {
                    'format': '{"timestamp": "%(asctime)s", "logger": "%(name)s", "level": "%(levelname)s", "message": "%(message)s"}'
                }
            },
            'handlers': {
                'console': {
                    'class': 'logging.StreamHandler',
                    'level': env_defaults.get('log_level', 'INFO'),
                    'formatter': 'standard',
                    'stream': 'ext://sys.stdout'
                },
                'file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': env_defaults.get('log_level', 'INFO'),
                    'formatter': 'detailed',
                    'filename': 'logs/trading_system.log',
                    'maxBytes': 10485760,  # 10MB
                    'backupCount': 5
                },
                'error_file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': 'ERROR',
                    'formatter': 'detailed',
                    'filename': 'logs/errors.log',
                    'maxBytes': 10485760,  # 10MB
                    'backupCount': 5
                }
            },
            'loggers': {
                '': {  # root logger
                    'handlers': ['console', 'file'],
                    'level': env_defaults.get('log_level', 'INFO'),
                    'propagate': False
                },
                'src': {
                    'handlers': ['console', 'file', 'error_file'],
                    'level': env_defaults.get('log_level', 'INFO'),
                    'propagate': False
                }
            }
        }
        
        return config
    
    def _generate_security_config(self, context: GenerationContext) -> Dict[str, Any]:
        """Generate security configuration."""
        env_defaults = self._environment_defaults.get(context.environment, {})
        
        config = {
            'authentication': {
                'enabled': True,
                'jwt_algorithm': 'HS256',
                'access_token_expire_minutes': 30,
                'refresh_token_expire_days': 7,
                'password_policy': {
                    'min_length': 8,
                    'require_uppercase': True,
                    'require_lowercase': True,
                    'require_numbers': True,
                    'require_special_chars': True
                }
            },
            'authorization': {
                'enabled': True,
                'default_role': 'user',
                'roles': {
                    'admin': ['read', 'write', 'delete', 'manage_users'],
                    'trader': ['read', 'write', 'execute_trades'],
                    'analyst': ['read', 'analyze'],
                    'user': ['read']
                }
            },
            'rate_limiting': {
                'enabled': True,
                'default_limit': '100/minute',
                'limits': {
                    'login': '5/minute',
                    'api': '1000/hour',
                    'data_request': '100/minute'
                }
            },
            'security_headers': {
                'enabled': True,
                'strict_transport_security': context.environment == 'production',
                'content_security_policy': context.environment == 'production',
                'x_frame_options': 'DENY',
                'x_content_type_options': 'nosniff'
            },
            'encryption': {
                'algorithm': 'AES-256-GCM',
                'key_rotation_days': 90 if context.environment == 'production' else 365
            }
        }
        
        return config
    
    def _generate_monitoring_config(self, context: GenerationContext) -> Dict[str, Any]:
        """Generate monitoring configuration."""
        config = {
            'metrics': {
                'enabled': True,
                'collection_interval': 60,  # seconds
                'retention_days': 30,
                'exporters': {
                    'prometheus': {
                        'enabled': context.environment in ['staging', 'production'],
                        'port': 9090
                    },
                    'file': {
                        'enabled': True,
                        'path': 'logs/metrics.log'
                    }
                }
            },
            'health_checks': {
                'enabled': True,
                'interval': 30,  # seconds
                'endpoints': [
                    '/health',
                    '/health/database',
                    '/health/cache',
                    '/health/external_apis'
                ]
            },
            'alerts': {
                'enabled': context.environment in ['staging', 'production'],
                'channels': {
                    'email': {
                        'enabled': False,
                        'smtp_server': '',
                        'recipients': []
                    },
                    'webhook': {
                        'enabled': False,
                        'url': ''
                    }
                },
                'rules': {
                    'high_cpu': {
                        'condition': 'cpu_usage > 80',
                        'duration': '5m',
                        'severity': 'warning'
                    },
                    'high_memory': {
                        'condition': 'memory_usage > 90',
                        'duration': '2m',
                        'severity': 'critical'
                    },
                    'api_errors': {
                        'condition': 'error_rate > 5',
                        'duration': '1m',
                        'severity': 'warning'
                    }
                }
            }
        }
        
        return config
    
    def _generate_secret(self, length: int = 64) -> str:
        """Generate a secure random secret."""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    def migrate_config(self, old_config: Dict[str, Any], target_version: str) -> Dict[str, Any]:
        """
        Migrate configuration to a new version.
        
        Args:
            old_config: Old configuration data
            target_version: Target configuration version
            
        Returns:
            Migrated configuration
        """
        # This is a placeholder for configuration migration logic
        # In a real implementation, you would have version-specific migration functions
        
        migrated_config = old_config.copy()
        
        # Add migration metadata
        migrated_config['_migration'] = {
            'from_version': old_config.get('_version', 'unknown'),
            'to_version': target_version,
            'migrated_at': datetime.now().isoformat(),
            'migration_notes': []
        }
        
        # Example migration: add new fields with defaults
        if 'security' not in migrated_config:
            migrated_config['security'] = self._generate_security_config(
                GenerationContext(environment='development')
            )
            migrated_config['_migration']['migration_notes'].append('Added security configuration')
        
        migrated_config['_version'] = target_version
        
        self.logger.info(f"Migrated configuration to version {target_version}")
        return migrated_config