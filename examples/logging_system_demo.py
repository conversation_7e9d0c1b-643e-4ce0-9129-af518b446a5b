#!/usr/bin/env python3
"""
日志管理和监控系统演示
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.monitoring.logging_manager import LoggingManager, <PERSON>ert


def demo_logging_system():
    """演示日志管理系统功能"""
    logger.info("=== 日志管理和监控系统演示 ===\n")
    
    # 初始化日志管理器
    logging_manager = LoggingManager()
    
    try:
        # 1. 设置日志配置
        logger.info("1. 设置日志配置...")
        logging_manager.setup_logging_configuration()
        logger = logging.getLogger("demo")
        logger.info("日志系统初始化完成")
        logger.info("✓ 日志配置完成\n")
        
        # 2. 演示日志轮转
        logger.info("2. 演示日志轮转功能...")
        rotation_config = logging_manager.log_rotator.get_rotation_config()
        logger.info(f"轮转配置: {rotation_config}")
        
        # 创建一些测试日志
        for i in range(10):
            logger.info(f"测试日志消息 {i}")
            logger.warning(f"测试警告消息 {i}")
            if i % 3 == 0:
                logger.error(f"测试错误消息 {i}")
        
        # 执行日志轮转
        logging_manager.rotate_log_files()
        logger.info("✓ 日志轮转完成\n")
        
        # 3. 演示日志分析
        logger.info("3. 演示日志分析功能...")
        
        # 获取日志统计
        if logging_manager.log_analyzer:
            stats = logging_manager.log_analyzer.get_log_statistics()
            logger.info(f"日志统计: {stats}")
            
            # 分析错误模式
            patterns = logging_manager.analyze_error_patterns()
            logger.info(f"发现 {len(patterns)} 个错误模式")
            
            # 检测异常
            anomalies = logging_manager.log_analyzer.detect_anomalies()
            logger.info(f"检测到 {len(anomalies)} 个异常")
            
            # 搜索日志
            search_results = logging_manager.log_analyzer.search_logs("ERROR", limit=5)
            logger.info(f"搜索到 {len(search_results)} 条错误日志")
        
        logger.info("✓ 日志分析完成\n")
        
        # 4. 演示告警系统
        logger.info("4. 演示告警系统功能...")
        
        # 发送测试告警
        logging_manager.send_alert(
            title="系统演示告警",
            message="这是一个演示告警，用于测试告警系统功能",
            severity="medium",
            source="demo",
            tags=["demo", "test"],
            metadata={"demo_key": "demo_value"}
        )
        
        # 发送高优先级告警
        logging_manager.send_alert(
            title="高优先级告警",
            message="这是一个高优先级告警",
            severity="high",
            source="demo",
            tags=["demo", "high_priority"]
        )
        
        # 获取告警统计
        alert_stats = logging_manager.get_alert_statistics()
        logger.info(f"告警统计: {alert_stats}")
        
        # 获取活跃告警
        active_alerts = logging_manager.alert_manager.get_active_alerts()
        logger.info(f"活跃告警数量: {len(active_alerts)}")
        
        for alert in active_alerts:
            logger.info(f"  - {alert.title} ({alert.severity})")
        
        logger.info("✓ 告警系统演示完成\n")
        
        # 5. 演示错误追踪
        logger.info("5. 演示错误追踪功能...")
        
        # 模拟一些错误
        try:
            raise ValueError("这是一个测试错误")
        except Exception as e:
            logging_manager.error_tracker.track_error(e, {"context": "demo"})
        
        try:
            raise ConnectionError("连接超时")
        except Exception as e:
            logging_manager.error_tracker.track_error(e, {"context": "network"})
        
        # 获取错误摘要
        error_summary = logging_manager.error_tracker.get_error_summary()
        logger.info(f"错误摘要: {error_summary}")
        logger.info("✓ 错误追踪演示完成\n")
        
        # 6. 演示告警规则管理
        logger.info("6. 演示告警规则管理...")
        
        from src.core.monitoring.logging_manager import AlertRule
        
        # 添加自定义告警规则
        custom_rule = AlertRule(
            id="demo_rule",
            name="演示告警规则",
            condition="demo_condition",
            severity="medium",
            cooldown_minutes=5,
            channels=["log", "email"]
        )
        
        logging_manager.alert_manager.add_alert_rule(custom_rule)
        logger.info(f"添加告警规则: {custom_rule.name}")
        
        # 列出所有规则
        rules = logging_manager.alert_manager.alert_rules
        logger.info(f"当前告警规则数量: {len(rules)}")
        for rule_id, rule in rules.items():
            logger.info(f"  - {rule.name} ({rule.severity})")
        
        logger.info("✓ 告警规则管理演示完成\n")
        
        logger.info("=== 演示完成 ===")
        logger.info("\n日志管理和监控系统功能演示成功！")
        logger.info("主要功能包括:")
        logger.info("- 统一的日志配置和管理")
        logger.info("- 自动日志轮转和压缩")
        logger.info("- 智能日志分析和模式识别")
        logger.info("- 多渠道告警和通知")
        logger.info("- 错误追踪和统计")
        logger.info("- 告警规则管理")
        
    except Exception as e:
        logger.info(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


def demo_advanced_features():
    """演示高级功能"""
    logger.info("\n=== 高级功能演示 ===\n")
    
    logging_manager = LoggingManager()
    logging_manager.setup_logging_configuration()
    
    # 演示日志搜索
    logger.info("1. 日志搜索功能...")
    if logging_manager.log_analyzer:
        # 搜索特定时间范围的日志
        from datetime import datetime, timedelta
        
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        results = logging_manager.log_analyzer.search_logs(
            query="ERROR",
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            level="ERROR",
            limit=10
        )
        
        logger.info(f"搜索结果: {len(results)} 条记录")
        for result in results[:3]:  # 显示前3条
            logger.info(f"  - {result.get('timestamp', 'N/A')}: {result.get('content', '')[:100]}...")
    
    # 演示异常检测
    logger.info("\n2. 异常检测功能...")
    if logging_manager.log_analyzer:
        anomalies = logging_manager.log_analyzer.detect_anomalies()
        logger.info(f"检测到 {len(anomalies)} 个异常:")
        for anomaly in anomalies:
            logger.info(f"  - {anomaly['type']}: {anomaly['description']}")
            logger.info(f"    建议: {anomaly['recommendation']}")
    
    # 演示告警去重
    logger.info("\n3. 告警去重功能...")
    
    # 发送重复告警
    for i in range(3):
        logging_manager.send_alert(
            title="重复告警测试",
            message="这是一个重复的告警消息",
            severity="medium",
            source="demo"
        )
        time.sleep(1)  # 短暂延迟
    
    active_alerts = logging_manager.alert_manager.get_active_alerts()
    duplicate_alerts = [a for a in active_alerts if "重复告警" in a.title]
    logger.info(f"重复告警数量: {len(duplicate_alerts)} (应该被去重)")
    
    logger.info("\n=== 高级功能演示完成 ===")


if __name__ == "__main__":
    # 基础功能演示
    demo_logging_system()
    
    # 高级功能演示
    demo_advanced_features()