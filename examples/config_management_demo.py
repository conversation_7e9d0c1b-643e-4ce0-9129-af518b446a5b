import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
Configuration Management System Demo

This script demonstrates the comprehensive configuration management system
including validation, generation, backup, and restore capabilities.
"""

import tempfile
import shutil
from pathlib import Path

import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import ConfigurationManager


def main():
    """Demonstrate configuration management system capabilities."""
    logger.info("=== Configuration Management System Demo ===\n")
    
    # Create temporary directory for demo
    temp_dir = tempfile.mkdtemp()
    temp_path = Path(temp_dir)
    
    try:
        # Initialize configuration manager
        logger.info("1. Initializing Configuration Manager...")
        config_manager = ConfigurationManager(str(temp_path), environment='development')
        logger.info(f"   - Config root: {config_manager.config_root}")
        logger.info(f"   - Environment: {config_manager.environment}")
        logger.info()
        
        # Fix missing configurations
        logger.info("2. Generating Missing Configurations...")
        fixed_configs = config_manager.fix_missing_configs()
        logger.info(f"   - Generated {len(fixed_configs)} configuration files:")
        for config_file in fixed_configs:
            logger.info(f"     * {config_file}")
        logger.info()
        
        # Validate configurations
        logger.info("3. Validating Configurations...")
        validation_result = config_manager.validate_all_configs()
        logger.info(f"   - Validation result: {'✓ Valid' if validation_result.is_valid else '✗ Invalid'}")
        
        if validation_result.errors:
            logger.info(f"   - Errors ({len(validation_result.errors)}):")
            for error in validation_result.errors[:3]:  # Show first 3 errors
                logger.info(f"     * {error}")
            if len(validation_result.errors) > 3:
                logger.info(f"     ... and {len(validation_result.errors) - 3} more")
        
        if validation_result.warnings:
            logger.info(f"   - Warnings ({len(validation_result.warnings)}):")
            for warning in validation_result.warnings[:3]:  # Show first 3 warnings
                logger.info(f"     * {warning}")
            if len(validation_result.warnings) > 3:
                logger.info(f"     ... and {len(validation_result.warnings) - 3} more")
        logger.info()
        
        # Set some configuration values
        logger.info("4. Setting Configuration Values...")
        config_manager.set_config("demo.test_value", "Hello, World!")
        config_manager.set_config("demo.nested.value", 42)
        config_manager.set_config("demo.list_value", ["item1", "item2", "item3"])
        
        # Get configuration values
        test_value = config_manager.get_config("demo.test_value")
        nested_value = config_manager.get_config("demo.nested.value")
        list_value = config_manager.get_config("demo.list_value")
        
        logger.info(f"   - demo.test_value: {test_value}")
        logger.info(f"   - demo.nested.value: {nested_value}")
        logger.info(f"   - demo.list_value: {list_value}")
        logger.info()
        
        # Create backup
        logger.info("5. Creating Configuration Backup...")
        backup_info = config_manager.backup_configurations("Demo backup")
        logger.info(f"   - Backup created: {backup_info.backup_path}")
        logger.info(f"   - Files backed up: {len(backup_info.config_files)}")
        logger.info()
        
        # Modify configuration
        logger.info("6. Modifying Configuration...")
        config_manager.set_config("demo.test_value", "Modified value!")
        modified_value = config_manager.get_config("demo.test_value")
        logger.info(f"   - Modified demo.test_value: {modified_value}")
        logger.info()
        
        # Restore from backup
        logger.info("7. Restoring from Backup...")
        success = config_manager.restore_configurations(backup_info.backup_path)
        logger.info(f"   - Restore result: {'✓ Success' if success else '✗ Failed'}")
        
        restored_value = config_manager.get_config("demo.test_value")
        logger.info(f"   - Restored demo.test_value: {restored_value}")
        logger.info()
        
        # Show configuration file formats
        logger.info("8. Demonstrating Multiple File Formats...")
        
        # Save as YAML
        test_config = {
            'database': {'host': 'localhost', 'port': 5432},
            'api': {'host': '0.0.0.0', 'port': 8000}
        }
        
        yaml_file = temp_path / "demo.yaml"
        json_file = temp_path / "demo.json"
        env_file = temp_path / "demo.env"
        
        config_manager.loader.save_config(test_config, yaml_file)
        config_manager.loader.save_config(test_config, json_file)
        
        # Save as .env
        env_config = {
            'DATABASE_HOST': 'localhost',
            'DATABASE_PORT': '5432',
            'API_HOST': '0.0.0.0',
            'API_PORT': '8000'
        }
        config_manager.loader.save_config(env_config, env_file)
        
        logger.info(f"   - YAML file: {yaml_file.name} ({'✓' if yaml_file.exists() else '✗'})")
        logger.info(f"   - JSON file: {json_file.name} ({'✓' if json_file.exists() else '✗'})")
        logger.info(f"   - ENV file: {env_file.name} ({'✓' if env_file.exists() else '✗'})")
        logger.info()
        
        # Load and merge configurations
        logger.info("9. Loading and Merging Configurations...")
        merged_config = config_manager.loader.load_multiple_files([yaml_file, json_file])
        logger.info(f"   - Merged config keys: {list(merged_config.keys())}")
        logger.info(f"   - Database host: {merged_config.get('database', {}).get('host')}")
        logger.info()
        
        # Generate configuration for different environment
        logger.info("10. Generating Production Configuration...")
        prod_config = config_manager.generator.generate_config('system', 'production', {
            'database_host': 'prod-db.example.com',
            'database_port': 5432,
            'api_host': '0.0.0.0',
            'api_port': 443,
            'generate_secrets': True
        })
        
        if prod_config:
            logger.info("    - Production configuration generated successfully")
            logger.info(f"    - Database host: {prod_config.get('database', {}).get('host')}")
            logger.info(f"    - API port: {prod_config.get('api', {}).get('port')}")
            logger.info(f"    - Debug mode: {prod_config.get('api', {}).get('debug')}")
        logger.info()
        
        # Show backup history
        logger.info("11. Configuration Backup History...")
        history = config_manager.get_backup_history()
        logger.info(f"    - Total backups: {len(history)}")
        for i, backup in enumerate(history, 1):
            logger.info(f"    - Backup {i}: {backup.description} ({backup.timestamp.strftime('%Y-%m-%d %H:%M:%S')})")
        logger.info()
        
        # Environment switching
        logger.info("12. Environment Switching...")
        original_env = config_manager.get_environment()
        logger.info(f"    - Current environment: {original_env}")
        
        success = config_manager.set_environment('production')
        if success:
            new_env = config_manager.get_environment()
            logger.info(f"    - Switched to: {new_env}")
            
            # Switch back
            config_manager.set_environment(original_env)
            logger.info(f"    - Switched back to: {config_manager.get_environment()}")
        logger.info()
        
        logger.info("=== Demo Complete ===")
        logger.info(f"All configuration files are in: {temp_path}")
        logger.info("You can explore the generated files to see the configuration structure.")
        
    except Exception as e:
        logger.info(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up (comment out if you want to inspect the files)
        # shutil.rmtree(temp_dir)
        logger.info(f"\nDemo files preserved in: {temp_dir}")


if __name__ == "__main__":
    main()