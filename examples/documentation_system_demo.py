import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
Documentation Management System Demo

This example demonstrates the complete documentation management system
including document management, generation, validation, and maintenance guides.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from documentation import (
    DocumentationManager,
    DocGenerator,
    DocValidator,
    MaintenanceGuide,
    DocumentationConfig,
    DocumentType
)


def main():
    """Demonstrate the documentation management system."""
    logger.info("🔧 Documentation Management System Demo")
    logger.info("=" * 50)
    
    # Setup configuration
    base_dir = Path(__file__).parent / "demo_docs"
    config = DocumentationConfig(
        base_path=base_dir / "docs",
        output_path=base_dir / "output",
        template_path=base_dir / "templates",
        supported_languages=["en", "zh"],
        default_language="en",
        auto_update_enabled=True,
        version_control_enabled=True,
        validation_enabled=True,
        search_index_enabled=True,
        check_links=True,
        spell_check_enabled=True
    )
    
    # Initialize components
    logger.info("\n📚 Initializing Documentation System...")
    doc_manager = DocumentationManager(config)
    doc_generator = DocGenerator(config)
    doc_validator = DocValidator(config)
    maintenance_guide = MaintenanceGuide(config)
    
    # Create sample source file for documentation generation
    logger.info("\n📝 Creating sample source files...")
    sample_api_file = create_sample_api_file(base_dir)
    sample_code_file = create_sample_code_file(base_dir)
    
    # Generate API documentation
    logger.info("\n🔄 Generating API documentation...")
    api_output = config.output_path / "api_documentation.md"
    api_doc_info = doc_generator.generate_api_documentation(
        source_paths=[sample_api_file],
        output_path=api_output,
        title="Sample API Documentation"
    )
    
    # Register the generated document
    doc_manager.register_document(
        file_path=api_doc_info.file_path,
        title=api_doc_info.title,
        doc_type=api_doc_info.type,
        author="Documentation System",
        description=api_doc_info.description,
        tags=api_doc_info.tags
    )
    
    # Generate code documentation
    logger.info("\n📖 Generating code documentation...")
    code_output = config.output_path / "code_documentation.md"
    code_doc_info = doc_generator.generate_code_documentation(
        source_path=sample_code_file,
        output_path=code_output,
        doc_type="module"
    )
    
    # Register the code documentation
    doc_manager.register_document(
        file_path=code_doc_info.file_path,
        title=code_doc_info.title,
        doc_type=code_doc_info.type,
        author="Documentation System",
        description=code_doc_info.description,
        tags=code_doc_info.tags
    )
    
    # Validate documents
    logger.info("\n✅ Validating documents...")
    documents = doc_manager.list_documents()
    validation_results = doc_validator.validate_multiple_documents(documents)
    
    # Generate validation report
    validation_report_path = config.output_path / "validation_report.md"
    doc_validator.generate_validation_report(validation_results, validation_report_path)
    
    # Display validation summary
    total_docs = len(validation_results)
    valid_docs = len([r for r in validation_results.values() if r.is_valid])
    avg_score = sum(r.score for r in validation_results.values()) / total_docs if total_docs > 0 else 0
    
    logger.info(f"   📊 Validation Summary:")
    logger.info(f"   - Total Documents: {total_docs}")
    logger.info(f"   - Valid Documents: {valid_docs}")
    logger.info(f"   - Average Quality Score: {avg_score:.2f}/100")
    
    # Add maintenance entries
    logger.info("\n🔧 Creating maintenance documentation...")
    
    # Add maintenance procedures
    maintenance_guide.add_maintenance_entry(
        title="System Backup Procedure",
        category="backup",
        description="Complete system backup procedure for data protection",
        steps=[
            "Stop all running services",
            "Create database backup using pg_dump",
            "Archive application files",
            "Verify backup integrity",
            "Store backup in secure location",
            "Restart services"
        ],
        prerequisites=["Administrative access", "Sufficient disk space"],
        warnings=["Ensure services are stopped before backup", "Verify backup before deletion"],
        difficulty="medium",
        estimated_time="45 minutes",
        tags=["backup", "critical", "database"]
    )
    
    maintenance_guide.add_maintenance_entry(
        title="Security Update Installation",
        category="security",
        description="Install security updates and patches",
        steps=[
            "Check for available updates",
            "Review update changelog",
            "Create system backup",
            "Install updates in test environment",
            "Validate functionality",
            "Install updates in production",
            "Monitor system stability"
        ],
        prerequisites=["System backup", "Test environment"],
        warnings=["Test updates before production deployment"],
        difficulty="hard",
        estimated_time="2 hours",
        tags=["security", "updates", "critical"]
    )
    
    # Add troubleshooting entries
    maintenance_guide.add_troubleshooting_entry(
        problem="Application fails to start",
        symptoms=[
            "Service status shows 'failed'",
            "Error messages in system logs",
            "Port not listening",
            "Process not running"
        ],
        solutions=[
            "Check configuration files for syntax errors",
            "Verify database connectivity",
            "Check file permissions",
            "Review system logs for detailed error messages",
            "Restart dependent services",
            "Clear temporary files and caches"
        ],
        category="system",
        severity="high"
    )
    
    maintenance_guide.add_troubleshooting_entry(
        problem="Slow system performance",
        symptoms=[
            "High CPU usage",
            "Long response times",
            "Memory usage near capacity",
            "Disk I/O bottlenecks"
        ],
        solutions=[
            "Identify resource-intensive processes",
            "Optimize database queries",
            "Clear system caches",
            "Add more memory if needed",
            "Optimize application configuration",
            "Consider load balancing"
        ],
        category="performance",
        severity="medium"
    )
    
    # Add best practices
    maintenance_guide.add_best_practice(
        title="Regular System Monitoring",
        description="Implement comprehensive system monitoring for proactive maintenance",
        category="monitoring",
        benefits=[
            "Early problem detection",
            "Improved system reliability",
            "Better resource planning",
            "Reduced downtime"
        ],
        implementation_steps=[
            "Set up monitoring tools (Prometheus, Grafana)",
            "Configure alerting rules",
            "Create monitoring dashboards",
            "Establish response procedures",
            "Regular review of metrics"
        ]
    )
    
    maintenance_guide.add_best_practice(
        title="Automated Testing Pipeline",
        description="Implement automated testing for code quality assurance",
        category="development",
        benefits=[
            "Consistent code quality",
            "Faster development cycles",
            "Reduced manual testing effort",
            "Early bug detection"
        ],
        implementation_steps=[
            "Set up CI/CD pipeline",
            "Write comprehensive unit tests",
            "Implement integration tests",
            "Configure automated deployment",
            "Monitor test coverage"
        ]
    )
    
    # Generate maintenance documentation
    logger.info("\n📋 Generating maintenance documentation...")
    
    # Generate maintenance manual
    manual_output = config.output_path / "maintenance_manual.md"
    manual_doc_info = maintenance_guide.generate_maintenance_manual(manual_output)
    
    # Generate troubleshooting guide
    troubleshooting_output = config.output_path / "troubleshooting_guide.md"
    troubleshooting_doc_info = maintenance_guide.generate_troubleshooting_guide(troubleshooting_output)
    
    # Generate best practices guide
    best_practices_output = config.output_path / "best_practices_guide.md"
    best_practices_doc_info = maintenance_guide.generate_best_practices_guide(best_practices_output)
    
    # Generate interactive index
    index_output = config.output_path / "maintenance_index.md"
    index_doc_info = maintenance_guide.generate_interactive_index(index_output)
    
    # Register maintenance documents
    for doc_info in [manual_doc_info, troubleshooting_doc_info, best_practices_doc_info, index_doc_info]:
        doc_manager.register_document(
            file_path=doc_info.file_path,
            title=doc_info.title,
            doc_type=doc_info.type,
            author="Maintenance System",
            description=doc_info.description,
            tags=doc_info.tags
        )
    
    # Demonstrate search functionality
    logger.info("\n🔍 Demonstrating search functionality...")
    
    # Search documents
    search_results = doc_manager.search_documents("API")
    logger.info(f"   📄 Found {len(search_results)} documents matching 'API'")
    
    # Search maintenance entries
    maintenance_results = maintenance_guide.search_maintenance_entries("backup")
    logger.info(f"   🔧 Found {len(maintenance_results)} maintenance entries matching 'backup'")
    
    # Search troubleshooting entries
    troubleshooting_results = maintenance_guide.search_troubleshooting_entries("performance")
    logger.info(f"   🚨 Found {len(troubleshooting_results)} troubleshooting entries matching 'performance'")
    
    # Display statistics
    logger.info("\n📊 Documentation Statistics:")
    stats = doc_manager.get_document_stats()
    logger.info(f"   - Total Documents: {stats.total_documents}")
    logger.info(f"   - Documents by Type:")
    for doc_type, count in stats.documents_by_type.items():
        logger.info(f"     • {doc_type.value}: {count}")
    logger.info(f"   - Documents by Language:")
    for language, count in stats.documents_by_language.items():
        logger.info(f"     • {language}: {count}")
    
    # List generated files
    logger.info(f"\n📁 Generated Documentation Files:")
    output_files = list(config.output_path.glob("*.md"))
    for file_path in sorted(output_files):
        file_size = file_path.stat().st_size
        logger.info(f"   📄 {file_path.name} ({file_size:,} bytes)")
    
    logger.info(f"\n✅ Documentation system demo completed successfully!")
    logger.info(f"📂 All files generated in: {config.output_path}")
    logger.info(f"🔍 Check the validation report for document quality assessment")
    logger.info(f"📚 Review the maintenance documentation for system procedures")


def create_sample_api_file(base_dir: Path) -> Path:
    """Create a sample API file for documentation generation."""
    api_file = base_dir / "sample_api.py"
    api_file.parent.mkdir(parents=True, exist_ok=True)
    
    api_content = '''
"""
Sample API module for documentation generation demo.

This module provides REST API endpoints for user management
and demonstrates various API documentation features.
"""

from flask import Flask, request, jsonify
from dataclasses import dataclass
from typing import List, Optional

app = Flask(__name__)

@dataclass
class User:
    """User data model."""
    id: int
    name: str
    email: str
    active: bool = True

@dataclass
class UserResponse:
    """API response model for user operations."""
    success: bool
    message: str
    data: Optional[User] = None

# Sample data
users_db = [
    User(1, "John Doe", "<EMAIL>"),
    User(2, "Jane Smith", "<EMAIL>")
]

@app.route('/api/users', methods=['GET'])
def get_users():
    """
    Get all users.
    
    Returns a list of all users in the system.
    Supports pagination through query parameters.
    """
    page = request.args.get('page', 1, type=int)
    limit = request.args.get('limit', 10, type=int)
    
    start = (page - 1) * limit
    end = start + limit
    
    return jsonify({
        'users': [user.__dict__ for user in users_db[start:end]],
        'total': len(users_db),
        'page': page,
        'limit': limit
    })

@app.route('/api/users/<int:user_id>', methods=['GET'])
def get_user(user_id):
    """
    Get user by ID.
    
    Args:
        user_id: The unique identifier for the user
        
    Returns:
        User data if found, 404 error if not found
    """
    user = next((u for u in users_db if u.id == user_id), None)
    if user:
        return jsonify(user.__dict__)
    return jsonify({'error': 'User not found'}), 404

@app.route('/api/users', methods=['POST'])
def create_user():
    """
    Create a new user.
    
    Expects JSON payload with user data.
    Validates required fields and creates new user.
    """
    data = request.get_json()
    
    if not data or 'name' not in data or 'email' not in data:
        return jsonify({'error': 'Name and email are required'}), 400
    
    new_id = max(u.id for u in users_db) + 1 if users_db else 1
    new_user = User(
        id=new_id,
        name=data['name'],
        email=data['email'],
        active=data.get('active', True)
    )
    
    users_db.append(new_user)
    return jsonify(new_user.__dict__), 201

@app.route('/api/users/<int:user_id>', methods=['PUT'])
def update_user(user_id):
    """
    Update existing user.
    
    Args:
        user_id: The unique identifier for the user to update
        
    Updates user data with provided JSON payload.
    """
    user = next((u for u in users_db if u.id == user_id), None)
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    user.name = data.get('name', user.name)
    user.email = data.get('email', user.email)
    user.active = data.get('active', user.active)
    
    return jsonify(user.__dict__)

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
def delete_user(user_id):
    """
    Delete user by ID.
    
    Args:
        user_id: The unique identifier for the user to delete
        
    Removes user from the system permanently.
    """
    global users_db
    users_db = [u for u in users_db if u.id != user_id]
    return jsonify({'message': 'User deleted successfully'})

if __name__ == '__main__':
    app.run(debug=True)
'''
    
    api_file.write_text(api_content)
    return api_file


def create_sample_code_file(base_dir: Path) -> Path:
    """Create a sample code file for documentation generation."""
    code_file = base_dir / "sample_calculator.py"
    code_file.parent.mkdir(parents=True, exist_ok=True)
    
    code_content = '''
"""
Advanced Calculator Module

This module provides a comprehensive calculator with basic arithmetic operations,
scientific functions, and memory management capabilities.

Example:
    >>> calc = Calculator()
    >>> result = calc.add(10, 5)
    >>> logger.info(result)
    15
"""

import math
from typing import List, Union, Optional
from enum import Enum

class OperationType(Enum):
    """Enumeration of supported operation types."""
    ADDITION = "addition"
    SUBTRACTION = "subtraction"
    MULTIPLICATION = "multiplication"
    DIVISION = "division"
    POWER = "power"
    ROOT = "root"

class CalculationHistory:
    """Manages calculation history and memory functions."""
    
    def __init__(self):
        """Initialize empty calculation history."""
        self.history: List[str] = []
        self.memory: float = 0.0
    
    def add_entry(self, operation: str, result: float) -> None:
        """
        Add a calculation entry to history.
        
        Args:
            operation: String representation of the operation
            result: The calculated result
        """
        entry = f"{operation} = {result}"
        self.history.append(entry)
        
        # Keep only last 100 entries
        if len(self.history) > 100:
            self.history = self.history[-100:]
    
    def get_history(self) -> List[str]:
        """
        Get calculation history.
        
        Returns:
            List of calculation history entries
        """
        return self.history.copy()
    
    def clear_history(self) -> None:
        """Clear all calculation history."""
        self.history.clear()
    
    def store_memory(self, value: float) -> None:
        """
        Store value in memory.
        
        Args:
            value: Value to store in memory
        """
        self.memory = value
    
    def recall_memory(self) -> float:
        """
        Recall value from memory.
        
        Returns:
            Value stored in memory
        """
        return self.memory
    
    def clear_memory(self) -> None:
        """Clear memory storage."""
        self.memory = 0.0

class Calculator:
    """
    Advanced calculator with arithmetic and scientific operations.
    
    This calculator supports basic arithmetic operations, scientific functions,
    memory management, and maintains a history of calculations.
    
    Attributes:
        precision: Number of decimal places for results
        history: Calculation history manager
    """
    
    def __init__(self, precision: int = 10):
        """
        Initialize calculator with specified precision.
        
        Args:
            precision: Number of decimal places for results (default: 10)
        """
        self.precision = precision
        self.history = CalculationHistory()
        self._last_result: Optional[float] = None
    
    def add(self, a: float, b: float) -> float:
        """
        Add two numbers.
        
        Args:
            a: First number
            b: Second number
            
        Returns:
            Sum of a and b
            
        Example:
            >>> calc = Calculator()
            >>> calc.add(5, 3)
            8.0
        """
        result = round(a + b, self.precision)
        self.history.add_entry(f"{a} + {b}", result)
        self._last_result = result
        return result
    
    def subtract(self, a: float, b: float) -> float:
        """
        Subtract second number from first number.
        
        Args:
            a: Number to subtract from
            b: Number to subtract
            
        Returns:
            Difference of a and b
        """
        result = round(a - b, self.precision)
        self.history.add_entry(f"{a} - {b}", result)
        self._last_result = result
        return result
    
    def multiply(self, a: float, b: float) -> float:
        """
        Multiply two numbers.
        
        Args:
            a: First number
            b: Second number
            
        Returns:
            Product of a and b
        """
        result = round(a * b, self.precision)
        self.history.add_entry(f"{a} × {b}", result)
        self._last_result = result
        return result
    
    def divide(self, a: float, b: float) -> float:
        """
        Divide first number by second number.
        
        Args:
            a: Dividend
            b: Divisor
            
        Returns:
            Quotient of a and b
            
        Raises:
            ValueError: If divisor is zero
        """
        if b == 0:
            raise ValueError("Cannot divide by zero")
        
        result = round(a / b, self.precision)
        self.history.add_entry(f"{a} ÷ {b}", result)
        self._last_result = result
        return result
    
    def power(self, base: float, exponent: float) -> float:
        """
        Raise base to the power of exponent.
        
        Args:
            base: Base number
            exponent: Exponent
            
        Returns:
            Result of base^exponent
        """
        result = round(pow(base, exponent), self.precision)
        self.history.add_entry(f"{base}^{exponent}", result)
        self._last_result = result
        return result
    
    def square_root(self, number: float) -> float:
        """
        Calculate square root of a number.
        
        Args:
            number: Number to find square root of
            
        Returns:
            Square root of the number
            
        Raises:
            ValueError: If number is negative
        """
        if number < 0:
            raise ValueError("Cannot calculate square root of negative number")
        
        result = round(math.sqrt(number), self.precision)
        self.history.add_entry(f"√{number}", result)
        self._last_result = result
        return result
    
    def factorial(self, n: int) -> int:
        """
        Calculate factorial of a number.
        
        Args:
            n: Non-negative integer
            
        Returns:
            Factorial of n
            
        Raises:
            ValueError: If n is negative or not an integer
        """
        if not isinstance(n, int) or n < 0:
            raise ValueError("Factorial is only defined for non-negative integers")
        
        result = math.factorial(n)
        self.history.add_entry(f"{n}!", result)
        self._last_result = float(result)
        return result
    
    def percentage(self, value: float, percent: float) -> float:
        """
        Calculate percentage of a value.
        
        Args:
            value: Base value
            percent: Percentage to calculate
            
        Returns:
            Percentage of the value
        """
        result = round((value * percent) / 100, self.precision)
        self.history.add_entry(f"{percent}% of {value}", result)
        self._last_result = result
        return result
    
    def get_last_result(self) -> Optional[float]:
        """
        Get the result of the last calculation.
        
        Returns:
            Last calculation result or None if no calculations performed
        """
        return self._last_result
    
    def reset(self) -> None:
        """Reset calculator state including history and memory."""
        self.history.clear_history()
        self.history.clear_memory()
        self._last_result = None

def create_scientific_calculator() -> Calculator:
    """
    Factory function to create a scientific calculator.
    
    Returns:
        Calculator instance configured for scientific operations
    """
    return Calculator(precision=15)

# Module-level convenience functions
def quick_add(a: float, b: float) -> float:
    """
    Quick addition without calculator instance.
    
    Args:
        a: First number
        b: Second number
        
    Returns:
        Sum of a and b
    """
    return a + b

def quick_multiply(a: float, b: float) -> float:
    """
    Quick multiplication without calculator instance.
    
    Args:
        a: First number
        b: Second number
        
    Returns:
        Product of a and b
    """
    return a * b

# Constants
PI = math.pi
E = math.e
GOLDEN_RATIO = (1 + math.sqrt(5)) / 2
'''
    
    code_file.write_text(code_content)
    return code_file


if __name__ == "__main__":
    main()