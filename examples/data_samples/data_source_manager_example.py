import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
Enhanced Data Source Manager usage example.

This example demonstrates the advanced features of the multi-data source management system.
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path
import time

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.market.data.adapters.manager import DataSourceManager


def main():
    """Main example function."""
    logger.info("=== Enhanced Data Source Manager Example ===\n")
    
    try:
        # Initialize manager with configuration
        logger.info("1. Initializing Data Source Manager...")
        manager = DataSourceManager("config/data_sources.yaml")
        
        logger.info(f"   ✓ Manager initialized with {len(manager.adapters)} adapters")
        logger.info(f"   ✓ Background scheduler started: {manager.scheduler_running}")
        logger.info()
        
        # Show available adapters
        logger.info("2. Available data source adapters:")
        for name, adapter in manager.adapters.items():
            status = "✓ Healthy" if adapter.is_healthy else "✗ Unhealthy"
            logger.info(f"   {name}:")
            logger.info(f"     Market: {adapter.get_market_type()}")
            logger.info(f"     Exchange: {adapter.get_exchange_name()}")
            logger.info(f"     Priority: {adapter.config.priority}")
            logger.info(f"     Status: {status}")
            logger.info(f"     Enabled: {adapter.config.enabled}")
        logger.info()
        
        # Health check all adapters
        logger.info("3. Performing health checks on all adapters...")
        health_results = manager.health_check_all()
        
        for name, result in health_results.items():
            status = "✓ Healthy" if result['healthy'] else "✗ Unhealthy"
            logger.info(f"   {name}: {status}")
            if result['message']:
                logger.info(f"     Message: {result['message']}")
        logger.info()
        
        # Test symbol validation across adapters
        logger.info("4. Testing symbol validation across adapters:")
        test_symbols = ['AAPL', 'BTCUSDT', '000001.SH', 'GDP']
        
        for symbol in test_symbols:
            logger.info(f"   {symbol}:")
            priority_order = manager.get_adapter_priority_order(symbol)
            if priority_order:
                logger.info(f"     Available in: {', '.join(priority_order)}")
                logger.info(f"     Primary adapter: {priority_order[0]}")
                
                # Get failover adapter
                failover = manager.get_failover_adapter(priority_order[0], symbol)
                if failover:
                    logger.info(f"     Failover adapter: {failover}")
                else:
                    logger.info(f"     No failover available")
            else:
                logger.info(f"     Not available in any adapter")
        logger.info()
        
        # Demonstrate failover functionality
        logger.info("5. Testing failover functionality with AAPL...")
        start_date = datetime.now() - timedelta(days=7)
        end_date = datetime.now()
        
        success, data_or_error, adapter_used = manager.fetch_with_failover(
            'AAPL', start_date, end_date, '1d', max_attempts=3
        )
        
        if success:
            data = data_or_error
            logger.info(f"   ✓ Successfully fetched {len(data)} records using {adapter_used}")
            if data:
                latest = data[-1]
                logger.info(f"   Latest data: {latest.timestamp.strftime('%Y-%m-%d')}")
                logger.info(f"   Close price: ${latest.close:.2f}")
        else:
            logger.info(f"   ✗ Failed to fetch data: {data_or_error}")
        logger.info()
        
        # Add and manage sync schedules
        logger.info("6. Managing data synchronization schedules...")
        
        # Add a sync schedule
        success = manager.add_sync_schedule(
            'daily_stocks',
            ['AAPL', 'GOOGL', 'MSFT'],
            interval_minutes=60,  # Every hour
            days_back=1
        )
        
        if success:
            logger.info("   ✓ Added daily stocks sync schedule")
        
        # Add another schedule
        success = manager.add_sync_schedule(
            'crypto_updates',
            ['BTCUSDT', 'ETHUSDT'],
            interval_minutes=30,  # Every 30 minutes
            days_back=1
        )
        
        if success:
            logger.info("   ✓ Added crypto updates sync schedule")
        
        logger.info(f"   Active schedules: {len(manager.sync_schedules)}")
        for schedule_id, config in manager.sync_schedules.items():
            logger.info(f"     {schedule_id}: {len(config['symbols'])} symbols, every {config['interval_minutes']} min")
        logger.info()
        
        # Get comprehensive performance report
        logger.info("7. Generating comprehensive performance report...")
        report = manager.get_comprehensive_performance_report()
        
        logger.info(f"   Report generated at: {report['generated_at']}")
        logger.info(f"   Total adapters: {report['total_adapters']}")
        logger.info(f"   Healthy adapters: {report['healthy_adapters']}")
        logger.info(f"   Total requests (last hour): {report['summary']['total_requests_last_hour']}")
        logger.info(f"   Total requests (last day): {report['summary']['total_requests_last_day']}")
        logger.info(f"   Error rate: {report['summary']['error_rate']:.1f}%")
        
        logger.info("   Adapter details:")
        for name, adapter_info in report['adapters'].items():
            if 'error' not in adapter_info:
                logger.info(f"     {name}:")
                logger.info(f"       Market: {adapter_info['market']}")
                logger.info(f"       Healthy: {adapter_info['healthy']}")
                logger.info(f"       Priority: {adapter_info['priority']}")
                perf = adapter_info['performance']
                logger.info(f"       Requests (hour): {perf.get('requests_last_hour', 0)}")
        logger.info()
        
        # Test market correlation analysis
        logger.info("8. Analyzing market correlations...")
        correlation_symbols = ['AAPL', 'GOOGL']  # Tech stocks
        
        correlation_matrix = manager.analyze_market_correlations(
            correlation_symbols,
            start_date,
            end_date,
            cache_duration_hours=1
        )
        
        if correlation_matrix is not None:
            logger.info("   ✓ Correlation analysis completed")
            logger.info("   Correlation matrix:")
            for symbol1 in correlation_symbols:
                for symbol2 in correlation_symbols:
                    if symbol1 != symbol2:
                        corr = correlation_matrix.loc[symbol1, symbol2]
                        logger.info(f"     {symbol1} vs {symbol2}: {corr:.3f}")
        else:
            logger.info("   ✗ Correlation analysis failed (insufficient data)")
        logger.info()
        
        # Test economic-market analysis (if FRED is available)
        logger.info("9. Economic-market data analysis...")
        if any(adapter.get_market_type() == 'ECONOMIC' for adapter in manager.adapters.values()):
            analysis = manager.get_economic_market_analysis(
                ['GDP', 'UNRATE'],  # Economic indicators
                ['AAPL'],           # Market symbols
                start_date - timedelta(days=365),  # Longer period for economic data
                end_date
            )
            
            if 'error' not in analysis:
                logger.info("   ✓ Economic-market analysis completed")
                logger.info(f"   Economic indicators analyzed: {len(analysis['economic_indicators'])}")
                logger.info(f"   Market symbols analyzed: {len(analysis['market_symbols'])}")
                logger.info(f"   Correlations calculated: {len(analysis['correlations'])}")
                
                if analysis['correlations']:
                    logger.info("   Key correlations:")
                    for corr_name, corr_info in list(analysis['correlations'].items())[:3]:
                        logger.info(f"     {corr_name}: {corr_info['correlation']:.3f} ({corr_info['significance']})")
            else:
                logger.info(f"   ✗ Economic analysis failed: {analysis['error']}")
        else:
            logger.info("   ⚠ No economic data adapters available")
        logger.info()
        
        # Show sync status
        logger.info("10. Data synchronization status:")
        sync_status = manager.get_sync_status()
        
        logger.info(f"    Total synced symbols: {sync_status['total_synced_symbols']}")
        if sync_status['last_sync_times']:
            logger.info("    Recent sync times:")
            for symbol, sync_time in list(sync_status['last_sync_times'].items())[:5]:
                logger.info(f"      {symbol}: {sync_time}")
        
        logger.info("    Adapter status:")
        for name, status in sync_status['adapters_status'].items():
            health = "✓" if status['healthy'] else "✗"
            logger.info(f"      {name}: {health} (enabled: {status['enabled']})")
        logger.info()
        
        # Data coverage report
        logger.info("11. Data coverage report:")
        coverage = manager.get_data_coverage_report()
        
        logger.info(f"    Generated at: {coverage['generated_at']}")
        logger.info(f"    Total adapters: {coverage['total_adapters']}")
        logger.info(f"    Healthy adapters: {coverage['healthy_adapters']}")
        
        logger.info("    Coverage by adapter:")
        for name, adapter_coverage in coverage['adapters'].items():
            if 'error' not in adapter_coverage:
                logger.info(f"      {name}:")
                logger.info(f"        Market: {adapter_coverage['market']}")
                logger.info(f"        Exchange: {adapter_coverage['exchange']}")
                logger.info(f"        Symbols: {adapter_coverage['symbol_count']}")
                if adapter_coverage['symbols']:
                    logger.info(f"        Sample: {', '.join(adapter_coverage['symbols'][:3])}...")
        logger.info()
        
        # Cleanup sync schedules
        logger.info("12. Cleaning up sync schedules...")
        for schedule_id in list(manager.sync_schedules.keys()):
            success = manager.remove_sync_schedule(schedule_id)
            if success:
                logger.info(f"    ✓ Removed schedule: {schedule_id}")
        logger.info()
        
        logger.info("=== Example completed successfully! ===")
        
        # Stop the scheduler
        logger.info("\nStopping background scheduler...")
        manager.stop_scheduler()
        logger.info("✓ Scheduler stopped")
        
    except Exception as e:
        logger.info(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()