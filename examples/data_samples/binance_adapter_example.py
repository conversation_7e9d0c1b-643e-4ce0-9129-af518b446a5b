import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
Binance adapter usage example.

This example demonstrates how to use the Binance adapter to fetch cryptocurrency data.
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.market.data.adapters.binance_adapter import BinanceAdapter
from src.market.data.adapters.base import DataSourceConfig


def main():
    """Main example function."""
    logger.info("=== Binance Adapter Example ===\n")
    
    # Create configuration
    config = DataSourceConfig(
        name='binance_example',
        adapter_class='src.data.adapters.binance_adapter.BinanceAdapter',
        enabled=True,
        priority=1,
        rate_limit={
            'requests_per_minute': 60,
            'requests_per_second': 10
        },
        credentials={
            'api_key': '',  # Optional - leave empty for public data
            'api_secret': ''
        },
        default_params={}
    )
    
    try:
        # Initialize adapter
        logger.info("1. Initializing Binance adapter...")
        adapter = BinanceAdapter(config)
        logger.info(f"   ✓ Adapter initialized successfully")
        logger.info(f"   Market: {adapter.get_market_type()}")
        logger.info(f"   Exchange: {adapter.get_exchange_name()}")
        logger.info(f"   Default Currency: {adapter.get_default_currency()}")
        logger.info()
        
        # Health check
        logger.info("2. Performing health check...")
        is_healthy, message = adapter.health_check()
        logger.info(f"   Health Status: {'✓ Healthy' if is_healthy else '✗ Unhealthy'}")
        logger.info(f"   Message: {message}")
        logger.info()
        
        # Get supported intervals
        logger.info("3. Supported time intervals:")
        intervals = adapter.get_supported_intervals()
        logger.info(f"   {', '.join(intervals)}")
        logger.info()
        
        # Get available symbols (first 20)
        logger.info("4. Available trading pairs (first 20):")
        symbols = adapter.get_available_symbols()[:20]
        for i, symbol in enumerate(symbols, 1):
            logger.info(f"   {i:2d}. {symbol}")
        logger.info(f"   ... and {len(adapter.get_available_symbols()) - 20} more")
        logger.info()
        
        # Test symbol validation
        logger.info("5. Symbol validation:")
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'INVALID_SYMBOL']
        for symbol in test_symbols:
            is_valid = adapter.validate_symbol(symbol)
            status = '✓ Valid' if is_valid else '✗ Invalid'
            logger.info(f"   {symbol}: {status}")
        logger.info()
        
        # Get market info for popular pairs
        logger.info("6. Market information for popular pairs:")
        popular_pairs = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        for symbol in popular_pairs:
            market_info = adapter.get_market_info(symbol)
            if market_info:
                logger.info(f"   {symbol}:")
                logger.info(f"     Name: {market_info.name}")
                logger.info(f"     Currency: {market_info.currency}")
                logger.info(f"     Lot Size: {market_info.lot_size}")
                logger.info(f"     Tick Size: {market_info.tick_size}")
                logger.info(f"     Active: {market_info.is_active}")
            else:
                logger.info(f"   {symbol}: No market info available")
        logger.info()
        
        # Fetch historical data
        logger.info("7. Fetching historical data for BTCUSDT...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)  # Last 7 days
        
        logger.info(f"   Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        logger.info(f"   Interval: 1d (daily)")
        
        data = adapter.fetch_historical_data('BTCUSDT', start_date, end_date, '1d')
        
        if data:
            logger.info(f"   ✓ Fetched {len(data)} records")
            logger.info("   Sample data (first 3 records):")
            for i, record in enumerate(data[:3], 1):
                logger.info(f"     {i}. {record.timestamp.strftime('%Y-%m-%d')}:")
                logger.info(f"        Open: ${record.open:,.2f}")
                logger.info(f"        High: ${record.high:,.2f}")
                logger.info(f"        Low: ${record.low:,.2f}")
                logger.info(f"        Close: ${record.close:,.2f}")
                logger.info(f"        Volume: {record.volume:,.2f} BTC")
                logger.info(f"        Turnover: ${record.turnover:,.2f}")
        else:
            logger.info("   ✗ No data retrieved")
        logger.info()
        
        # Get 24hr ticker
        logger.info("8. Getting 24hr ticker for BTCUSDT...")
        ticker = adapter.get_24hr_ticker('BTCUSDT')
        if ticker:
            logger.info(f"   Last Price: ${ticker['last_price']:,.2f}")
            logger.info(f"   24h Change: ${ticker['price_change']:,.2f} ({ticker['price_change_percent']:.2f}%)")
            logger.info(f"   24h High: ${ticker['high_price']:,.2f}")
            logger.info(f"   24h Low: ${ticker['low_price']:,.2f}")
            logger.info(f"   24h Volume: {ticker['volume']:,.2f} BTC")
            logger.info(f"   24h Quote Volume: ${ticker['quote_volume']:,.2f}")
        else:
            logger.info("   ✗ No ticker data available")
        logger.info()
        
        # Search symbols
        logger.info("9. Searching for symbols containing 'ETH':")
        search_results = adapter.search_symbols('ETH', limit=5)
        for i, result in enumerate(search_results, 1):
            logger.info(f"   {i}. {result['symbol']} - {result['name']}")
            logger.info(f"      Base: {result['base_asset']}, Quote: {result['quote_asset']}")
        logger.info()
        
        # Get server time
        logger.info("10. Binance server time:")
        server_time = adapter.get_server_time()
        logger.info(f"    Server Time: {server_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        logger.info()
        
        # Performance stats
        logger.info("11. Adapter performance statistics:")
        stats = adapter.get_performance_stats()
        logger.info(f"    Total Requests: {stats['total_requests']}")
        logger.info(f"    Requests (Last Hour): {stats['requests_last_hour']}")
        logger.info(f"    Requests (Last Day): {stats['requests_last_day']}")
        logger.info(f"    Can Make Request: {stats['can_make_request']}")
        logger.info(f"    Wait Time: {stats['wait_time']:.2f} seconds")
        logger.info()
        
        logger.info("=== Example completed successfully! ===")
        
    except Exception as e:
        logger.info(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()