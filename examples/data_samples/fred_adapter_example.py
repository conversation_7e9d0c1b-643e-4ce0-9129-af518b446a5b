import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
FRED adapter usage example.

This example demonstrates how to use the FRED adapter to fetch US economic data.
Note: You need a FRED API key to run this example. Get one free at:
https://fred.stlouisfed.org/docs/api/api_key.html
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.market.data.adapters.fred_adapter import FREDAdapter
from src.market.data.adapters.base import DataSourceConfig


def main():
    """Main example function."""
    logger.info("=== FRED Adapter Example ===\n")

    # Try to get API key from environment variable first, then from config
    api_key = os.getenv('FRED_API_KEY')
    if not api_key:
        # Use the API key from config file
        api_key = '217a773fd2056856b98bbe71e524f23e'
        logger.info("ℹ️  Using API key from configuration file")
    else:
        logger.info("ℹ️  Using API key from environment variable")

    # Create configuration
    config = DataSourceConfig(
        name='fred_example',
        adapter_class='src.data.adapters.fred_adapter.FREDAdapter',
        enabled=True,
        priority=1,
        rate_limit={
            'requests_per_minute': 120,
            'requests_per_day': 100000
        },
        credentials={
            'api_key': api_key
        },
        default_params={}
    )
    
    try:
        # Initialize adapter
        logger.info("1. Initializing FRED adapter...")
        adapter = FREDAdapter(config)
        logger.info(f"   ✓ Adapter initialized successfully")
        logger.info(f"   Market: {adapter.get_market_type()}")
        logger.info(f"   Exchange: {adapter.get_exchange_name()}")
        logger.info(f"   Default Currency: {adapter.get_default_currency()}")
        logger.info()
        
        # Health check
        logger.info("2. Performing health check...")
        is_healthy, message = adapter.health_check()
        logger.info(f"   Health Status: {'✓ Healthy' if is_healthy else '✗ Unhealthy'}")
        logger.info(f"   Message: {message}")
        logger.info()
        
        # Get supported intervals
        logger.info("3. Supported time intervals:")
        intervals = adapter.get_supported_intervals()
        interval_names = {
            'd': 'Daily', 'w': 'Weekly', 'm': 'Monthly', 
            'q': 'Quarterly', 'a': 'Annual'
        }
        for interval in intervals:
            logger.info(f"   {interval} - {interval_names.get(interval, interval)}")
        logger.info()
        
        # Get popular series
        logger.info("4. Popular economic indicators (first 15):")
        popular_series = adapter.get_popular_series()[:15]
        for i, series_id in enumerate(popular_series, 1):
            series_info = adapter.get_series_info(series_id)
            title = series_info.get('title', series_id) if series_info else series_id
            logger.info(f"   {i:2d}. {series_id}: {title}")
        logger.info(f"   ... and {len(adapter.get_popular_series()) - 15} more")
        logger.info()
        
        # Get categories
        logger.info("5. Economic data categories:")
        categories = adapter.get_categories()
        for i, (category, series_list) in enumerate(categories.items(), 1):
            logger.info(f"   {i:2d}. {category} ({len(series_list)} indicators)")
        logger.info()
        
        # Get series info for key indicators
        logger.info("6. Key economic indicators information:")
        key_indicators = ['GDP', 'UNRATE', 'CPIAUCSL', 'FEDFUNDS']
        for indicator in key_indicators:
            series_info = adapter.get_series_info(indicator)
            if series_info:
                logger.info(f"   {indicator}:")
                logger.info(f"     Title: {series_info.get('title', 'N/A')}")
                logger.info(f"     Units: {series_info.get('units', 'N/A')}")
                logger.info(f"     Frequency: {series_info.get('frequency', 'N/A')}")
                logger.info(f"     Seasonal Adj: {series_info.get('seasonal_adjustment', 'N/A')}")
            else:
                logger.info(f"   {indicator}: No info available")
        logger.info()
        
        # Fetch historical data for GDP
        logger.info("7. Fetching historical GDP data...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365*2)  # Last 2 years
        
        logger.info(f"   Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        logger.info(f"   Series: GDP (Gross Domestic Product)")
        
        gdp_data = adapter.fetch_historical_data('GDP', start_date, end_date)
        
        if gdp_data:
            logger.info(f"   ✓ Fetched {len(gdp_data)} records")
            logger.info("   Recent GDP data (last 5 quarters):")
            for i, record in enumerate(gdp_data[-5:], 1):
                logger.info(f"     {i}. {record.timestamp.strftime('%Y-%m-%d')}:")
                logger.info(f"        Value: ${record.value:,.1f} {record.unit}")
                logger.info(f"        Frequency: {record.frequency}")
        else:
            logger.info("   ✗ No data retrieved")
        logger.info()
        
        # Get latest values for key indicators
        logger.info("8. Latest values for key indicators:")
        key_indicators = ['GDP', 'UNRATE', 'CPIAUCSL', 'FEDFUNDS']
        for indicator in key_indicators:
            latest = adapter.get_latest_value(indicator)
            if latest:
                logger.info(f"   {indicator}:")
                logger.info(f"     Latest Value: {latest['value']:.2f} {latest['units']}")
                logger.info(f"     Date: {latest['date'].strftime('%Y-%m-%d')}")
                logger.info(f"     Title: {latest['title']}")
            else:
                logger.info(f"   {indicator}: No latest data available")
        logger.info()
        
        # Search for inflation-related series
        logger.info("9. Searching for inflation-related series:")
        search_results = adapter.search_series('inflation', limit=5)
        for i, result in enumerate(search_results, 1):
            logger.info(f"   {i}. {result['id']}: {result['title']}")
            logger.info(f"      Units: {result['units']}, Frequency: {result['frequency']}")
        logger.info()
        
        # Fetch multiple series
        logger.info("10. Fetching multiple economic indicators...")
        multi_series = ['UNRATE', 'CPIAUCSL', 'FEDFUNDS']
        start_date_multi = datetime.now() - timedelta(days=365)  # Last year
        
        multi_data = adapter.fetch_multiple_series(multi_series, start_date_multi, end_date)
        
        logger.info(f"    Fetched data for {len(multi_data)} series:")
        for series_id, data in multi_data.items():
            logger.info(f"     {series_id}: {len(data)} data points")
            if data:
                latest_point = data[-1]
                logger.info(f"       Latest: {latest_point.value:.2f} on {latest_point.timestamp.strftime('%Y-%m-%d')}")
        logger.info()
        
        # Get series by category
        logger.info("11. Employment indicators:")
        employment_series = adapter.get_series_by_category('Employment and Labor')
        for series_id in employment_series[:5]:  # First 5
            series_info = adapter.get_series_info(series_id)
            title = series_info.get('title', series_id) if series_info else series_id
            logger.info(f"    {series_id}: {title}")
        logger.info()
        
        # Performance stats
        logger.info("12. Adapter performance statistics:")
        stats = adapter.get_performance_stats()
        logger.info(f"    Total Requests: {stats['total_requests']}")
        logger.info(f"    Requests (Last Hour): {stats['requests_last_hour']}")
        logger.info(f"    Requests (Last Day): {stats['requests_last_day']}")
        logger.info(f"    Can Make Request: {stats['can_make_request']}")
        logger.info(f"    Wait Time: {stats['wait_time']:.2f} seconds")
        logger.info()
        
        logger.info("=== Example completed successfully! ===")
        
    except Exception as e:
        logger.info(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()