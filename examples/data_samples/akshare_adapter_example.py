import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
Example usage of AkshareAdapter for A-share market data.

This example demonstrates how to use the AkshareAdapter to fetch Chinese A-share
market data including stocks and indices.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path
import os

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

# Set up environment to find config module
os.environ['PYTHONPATH'] = str(project_root)

from src.market.data.adapters.base import DataSourceConfig
from src.market.data.adapters.akshare_adapter import AkshareAdapter


def main():
    """Main example function."""
    logger.info("=== Akshare Adapter Example ===\n")
    
    config = DataSourceConfig(
        name='akshare_example',
        adapter_class='src.data.adapters.akshare_adapter.AkshareAdapter',
        enabled=True,
        priority=1,
        rate_limit={'requests_per_minute': 100},
        credentials={},
        default_params={}
    )
    
    try:
        # Initialize adapter
        logger.info("1. Initializing Akshare adapter...")
        adapter = AkshareAdapter(config)
        logger.info("✅ Adapter initialized successfully")
        
        # Health check
        logger.info("\n2. Performing health check...")
        is_healthy, message = adapter.health_check()
        if is_healthy:
            logger.info(f"✅ {message}")
        else:
            logger.info(f"❌ {message}")
            return
        
        # Get available symbols (sample)
        logger.info("\n3. Getting available symbols...")
        symbols = adapter.get_available_symbols()
        logger.info(f"✅ Found {len(symbols)} symbols")
        logger.info(f"   Sample symbols: {symbols[:10]}")
        
        # Test with a popular A-share stock (平安银行)
        test_symbol = '000001.SZ'
        logger.info(f"\n4. Testing with symbol: {test_symbol}")
        
        # Validate symbol
        is_valid = adapter.validate_symbol(test_symbol)
        logger.info(f"   Symbol validation: {'✅ Valid' if is_valid else '❌ Invalid'}")
        
        if not is_valid:
            logger.info("   Skipping data fetch for invalid symbol")
            return
        
        # Get market info
        logger.info(f"\n5. Getting market info for {test_symbol}...")
        market_info = adapter.get_market_info(test_symbol)
        if market_info:
            logger.info(f"   Name: {market_info.name}")
            logger.info(f"   Exchange: {market_info.exchange}")
            logger.info(f"   Currency: {market_info.currency}")
            logger.info(f"   Lot size: {market_info.lot_size}")
            logger.info(f"   Tick size: {market_info.tick_size}")
        
        # Fetch historical data
        logger.info(f"\n6. Fetching historical data for {test_symbol}...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)  # Last 30 days
        
        data = adapter.fetch_historical_data(test_symbol, start_date, end_date)
        logger.info(f"✅ Fetched {len(data)} records")
        
        if data:
            logger.info("   Sample data (first 3 records):")
            for i, record in enumerate(data[:3]):
                logger.info(f"   {i+1}. {record.timestamp.strftime('%Y-%m-%d')}: "
                      f"Open={record.open:.2f}, High={record.high:.2f}, "
                      f"Low={record.low:.2f}, Close={record.close:.2f}, "
                      f"Volume={record.volume:,.0f}")
        
        # Test with major index (上证指数)
        index_symbol = '000001.SH'
        logger.info(f"\n7. Testing with index: {index_symbol}")
        
        market_info = adapter.get_market_info(index_symbol)
        if market_info:
            logger.info(f"   Index name: {market_info.name}")
        
        index_data = adapter.fetch_historical_data(index_symbol, start_date, end_date)
        logger.info(f"✅ Fetched {len(index_data)} index records")
        
        if index_data:
            latest = index_data[-1]
            logger.info(f"   Latest: {latest.timestamp.strftime('%Y-%m-%d')}: "
                  f"Close={latest.close:.2f}")
        
        # Get trading calendar
        logger.info(f"\n8. Getting trading calendar...")
        calendar_start = datetime.now() - timedelta(days=10)
        calendar_end = datetime.now()
        trading_days = adapter.get_trading_calendar(calendar_start, calendar_end)
        logger.info(f"✅ Found {len(trading_days)} trading days in the last 10 days")
        
        # Search stocks
        logger.info(f"\n9. Searching stocks with keyword '银行'...")
        search_results = adapter.search_stocks('银行', limit=5)
        logger.info(f"✅ Found {len(search_results)} matching stocks:")
        for result in search_results:
            logger.info(f"   {result['symbol']}: {result['name']} ({result['exchange']})")
        
        logger.info(f"\n🎉 All tests completed successfully!")
        
    except Exception as e:
        logger.info(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()