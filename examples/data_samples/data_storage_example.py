import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
Example demonstrating the data storage and access layer functionality.
"""

import sys
import os
from datetime import datetime, timedelta

# Add the src directory to the path
# Add project root to path for imports
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)

from src.market.data.manager import DataManager
from src.models.market_data import UnifiedMarketData, EconomicData, MarketInfo


def main():
    """Demonstrate data storage functionality."""
    logger.info("=== Quantitative Trading System - Data Storage Example ===\n")
    
    # Initialize data manager with SQLite
    logger.info("1. Initializing data manager...")
    data_manager = DataManager(use_sqlite=True)
    logger.info("✓ Data manager initialized with SQLite backend\n")
    
    # Create sample market data
    logger.info("2. Creating sample market data...")
    sample_data = [
        UnifiedMarketData(
            symbol="AAPL",
            timestamp=datetime(2023, 1, 1) + timedelta(days=i),
            open=150.0 + i,
            high=155.0 + i,
            low=149.0 + i,
            close=154.0 + i,
            volume=1000000 + i * 10000,
            market="US",
            exchange="NASDAQ",
            currency="USD",
            adj_close=154.0 + i
        ) for i in range(5)
    ]
    
    # Save market data in batch
    saved_count = data_manager.save_market_data_batch(sample_data)
    logger.info(f"✓ Saved {saved_count} market data records\n")
    
    # Retrieve and display market data
    logger.info("3. Retrieving market data...")
    start_date = datetime(2022, 12, 31)
    end_date = datetime(2023, 1, 10)
    
    df = data_manager.get_market_data("AAPL", start_date, end_date, "US")
    logger.info(f"✓ Retrieved {len(df)} records for AAPL")
    logger.info("Sample data:")
    logger.info(df.head())
    logger.info()
    
    # Create and save market info
    logger.info("4. Creating market information...")
    market_info = MarketInfo(
        symbol="AAPL",
        name="Apple Inc.",
        market="US",
        exchange="NASDAQ",
        currency="USD",
        lot_size=1.0,
        tick_size=0.01,
        trading_hours={"open": "09:30", "close": "16:00"},
        timezone="America/New_York"
    )
    
    data_manager.save_market_info(market_info)
    logger.info("✓ Market information saved\n")
    
    # Create and save economic data
    logger.info("5. Creating economic data...")
    economic_data = EconomicData(
        series_id="GDP",
        timestamp=datetime(2023, 1, 1),
        value=25000.0,
        unit="Billions of Dollars",
        frequency="Quarterly",
        seasonal_adjustment="Seasonally Adjusted"
    )
    
    data_manager.save_economic_data(economic_data)
    logger.info("✓ Economic data saved\n")
    
    # Get data coverage report
    logger.info("6. Generating data coverage report...")
    coverage_report = data_manager.get_data_coverage_report()
    logger.info("Coverage Report:")
    logger.info(f"- Total symbols: {coverage_report['market_data']['total_symbols']}")
    logger.info(f"- Total records: {coverage_report['market_data']['total_records']}")
    logger.info(f"- Available markets: {coverage_report['markets']}")
    logger.info()
    
    # Get data quality report
    logger.info("7. Generating data quality report...")
    quality_report = data_manager.get_data_quality_report("AAPL", "US")
    logger.info("Quality Report for AAPL:")
    logger.info(f"- Date range: {quality_report['date_range']['start']} to {quality_report['date_range']['end']}")
    logger.info(f"- Total records: {quality_report['records']['total']}")
    logger.info(f"- Completeness: {quality_report['records']['completeness']:.2%}")
    logger.info(f"- Zero volume records: {quality_report['price_anomalies']['zero_volume']}")
    logger.info()
    
    # Get available symbols
    logger.info("8. Getting available symbols...")
    symbols = data_manager.get_available_symbols("US")
    logger.info(f"✓ Available US symbols: {symbols}")
    logger.info()
    
    # Get latest data
    logger.info("9. Getting latest data...")
    latest = data_manager.get_latest_market_data("AAPL", "US")
    if latest:
        logger.info(f"✓ Latest AAPL data: {latest.timestamp} - Close: ${latest.close}")
    logger.info()
    
    # Database statistics
    logger.info("10. Database statistics...")
    stats = data_manager.get_database_stats()
    logger.info("Database Statistics:")
    for table, table_stats in stats.items():
        if isinstance(table_stats, dict) and 'row_count' in table_stats:
            logger.info(f"- {table}: {table_stats['row_count']} rows")
    logger.info()
    
    # Cleanup
    logger.info("11. Cleaning up...")
    data_manager.close()
    logger.info("✓ Data manager closed\n")
    
    logger.info("=== Example completed successfully! ===")


if __name__ == "__main__":
    main()