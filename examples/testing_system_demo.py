logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
Testing System Demo

This script demonstrates the comprehensive testing management and quality assurance system.
It shows how to use the TestManager to run different types of tests and generate reports.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from testing.test_manager import TestManager
from testing.models import TestType


async def main():
    """Main demo function."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("=" * 60)
    logger.info("TESTING MANAGEMENT SYSTEM DEMO")
    logger.info("=" * 60)
    
    # Initialize TestManager
    project_root = Path(__file__).parent.parent
    test_manager = TestManager(
        project_root=str(project_root),
        max_workers=2,
        enable_parallel=True
    )
    
    logger.info(f"Project root: {project_root}")
    logger.info(f"Test manager initialized with {test_manager.max_workers} workers")
    logger.info()
    
    try:
        # Demo 1: Run all tests
        logger.info("1. Running all tests...")
        logger.info("-" * 40)
        
        report = await test_manager.run_all_tests(
            test_types={TestType.UNIT},  # Only run unit tests for demo
            include_coverage=False,  # Skip coverage for demo
            parallel=False  # Sequential for clearer output
        )
        
        logger.info(f"Test execution completed!")
        logger.info(f"Total tests: {report.total_tests}")
        logger.info(f"Passed: {report.total_passed}")
        logger.info(f"Failed: {report.total_failed}")
        logger.info(f"Success rate: {report.overall_success_rate:.1f}%")
        logger.info(f"Duration: {report.total_duration:.2f}s")
        logger.info()
        
        # Demo 2: Analyze test quality
        logger.info("2. Analyzing test quality...")
        logger.info("-" * 40)
        
        analysis = test_manager.analyze_test_quality(report)
        logger.info(f"Overall quality: {analysis['overall_quality']}")
        logger.info(f"Issues found: {len(analysis['issues'])}")
        for issue in analysis['issues']:
            logger.info(f"  - {issue}")
        
        logger.info(f"Recommendations: {len(analysis['recommendations'])}")
        for rec in analysis['recommendations']:
            logger.info(f"  - {rec}")
        logger.info()
        
        # Demo 3: Save report
        logger.info("3. Saving test report...")
        logger.info("-" * 40)
        
        report_path = test_manager.save_report(report)
        logger.info(f"Report saved to: {report_path}")
        logger.info()
        
        # Demo 4: Test discovery
        logger.info("4. Test discovery...")
        logger.info("-" * 40)
        
        unit_tests = await test_manager.unit_test_runner.discover_tests()
        logger.info(f"Unit tests discovered: {len(unit_tests)}")
        for test_file in unit_tests[:5]:  # Show first 5
            logger.info(f"  - {Path(test_file).name}")
        if len(unit_tests) > 5:
            logger.info(f"  ... and {len(unit_tests) - 5} more")
        logger.info()
        
        # Demo 5: Test statistics
        logger.info("5. Test statistics...")
        logger.info("-" * 40)
        
        stats = test_manager.unit_test_runner.get_test_statistics(unit_tests)
        logger.info(f"Total test files: {stats['total_files']}")
        logger.info(f"Estimated test functions: {stats['total_estimated_tests']}")
        logger.info(f"Test directories: {len(stats['files_by_directory'])}")
        for directory, count in stats['files_by_directory'].items():
            logger.info(f"  - {Path(directory).name}: {count} files")
        logger.info()
        
        # Demo 6: Validation
        logger.info("6. Test structure validation...")
        logger.info("-" * 40)
        
        validation = await test_manager.unit_test_runner.validate_test_structure()
        logger.info(f"Test structure valid: {validation['valid']}")
        if validation['issues']:
            logger.info("Issues found:")
            for issue in validation['issues']:
                logger.info(f"  - {issue}")
        if validation['recommendations']:
            logger.info("Recommendations:")
            for rec in validation['recommendations']:
                logger.info(f"  - {rec}")
        logger.info()
        
        logger.info("=" * 60)
        logger.info("DEMO COMPLETED SUCCESSFULLY")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.info(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)