import logging
logger = logging.getLogger(__name__)
"""
Basic usage example of the quantitative trading system core components.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime
from src.models.market_data import MarketData
from src.models.trading import Trade, OrderSide
from src.models.portfolio import Portfolio
from config.settings import config


def main():
    """Demonstrate basic usage of core components."""
    logger.info("=== 量化交易系统基础使用示例 ===\n")
    
    # 1. 创建市场数据
    logger.info("1. 创建市场数据")
    market_data = MarketData(
        symbol="AAPL",
        timestamp=datetime(2023, 1, 1),
        open=150.0,
        high=155.0,
        low=149.0,
        close=154.0,
        volume=1000000
    )
    logger.info(f"市场数据: {market_data.symbol} - 收盘价: ${market_data.close}")
    logger.info(f"数据验证: {'通过' if market_data.validate() else '失败'}\n")
    
    # 2. 创建投资组合
    logger.info("2. 创建投资组合")
    portfolio = Portfolio(
        initial_capital=100000.0,
        cash=100000.0
    )
    logger.info(f"初始资金: ${portfolio.initial_capital:,.2f}")
    logger.info(f"可用现金: ${portfolio.cash:,.2f}\n")
    
    # 3. 执行交易
    logger.info("3. 执行买入交易")
    buy_trade = Trade(
        id="trade_001",
        symbol="AAPL",
        side=OrderSide.BUY,
        quantity=100,
        price=150.0,
        timestamp=datetime(2023, 1, 1),
        commission=1.0
    )
    
    portfolio.add_trade(buy_trade)
    logger.info(f"买入: {buy_trade.quantity}股 {buy_trade.symbol} @ ${buy_trade.price}")
    logger.info(f"手续费: ${buy_trade.commission}")
    logger.info(f"剩余现金: ${portfolio.cash:,.2f}")
    logger.info(f"持仓: {list(portfolio.positions.keys())}\n")
    
    # 4. 更新市场价格
    logger.info("4. 更新市场价格")
    current_prices = {"AAPL": 160.0}
    portfolio.update_positions_market_value(current_prices)
    
    position = portfolio.get_position("AAPL")
    logger.info(f"当前价格: ${current_prices['AAPL']}")
    logger.info(f"持仓数量: {position.quantity}股")
    logger.info(f"平均成本: ${position.avg_price:.2f}")
    logger.info(f"市场价值: ${position.market_value:,.2f}")
    logger.info(f"未实现盈亏: ${position.unrealized_pnl:,.2f}")
    logger.info(f"投资组合总价值: ${portfolio.total_value:,.2f}")
    logger.info(f"总收益率: {portfolio.get_total_return():.2%}\n")
    
    # 5. 配置管理示例
    logger.info("5. 配置管理")
    logger.info(f"回测初始资金: ${config.backtest.initial_capital:,.2f}")
    logger.info(f"手续费率: {config.backtest.commission_rate:.3%}")
    logger.info(f"最大仓位限制: {config.risk.max_position_size:.1%}")
    logger.info(f"止损比例: {config.risk.stop_loss_pct:.1%}")
    
    # 6. 数据源配置
    logger.info(f"\n6. 数据源配置")
    for name, source_config in config.data_sources.items():
        status = "启用" if source_config.enabled else "禁用"
        logger.info(f"- {name}: {status} (优先级: {source_config.priority})")


if __name__ == "__main__":
    main()