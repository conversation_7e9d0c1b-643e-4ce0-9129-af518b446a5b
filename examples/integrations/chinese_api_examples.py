import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文化API使用示例

展示如何使用中文化的量化交易系统API
"""

def 风险管理示例():
    """风险管理器使用示例"""
    from src.risk import 风险管理器
    
    # 创建风险管理器
    风险管理 = 风险管理器()
    
    # 获取风险摘要
    状态 = 风险管理.获取风险摘要()
    logger.info(f"风险管理器状态: {状态}")
    
    return 风险管理

def 策略开发示例():
    """策略开发示例"""
    from src.market.strategies import 基础策略, 策略上下文
    
    class 示例策略(基础策略):
        def 获取参数定义(self):
            return {}
        
        def 初始化(self, 上下文):
            self.日志器.info("策略初始化完成")
        
        def 处理数据(self, 市场数据):
            # 策略逻辑实现
            return []
    
    # 创建策略实例
    策略 = 示例策略("我的策略", {"参数1": "值1"})
    return 策略

def 回测引擎示例():
    """回测引擎使用示例"""
    from src.backtest import 回测引擎
    
    # 创建回测引擎
    引擎 = 回测引擎(
        初始资金=100000,
        手续费率=0.001,
        滑点=0.0005
    )
    
    logger.info(f"回测引擎创建成功，初始资金: {引擎.初始资金}")
    return 引擎

def 策略对比示例():
    """策略对比分析示例"""
    from src.analysis.comparison import 策略对比器, 对比方法, 排名方法
    
    # 创建对比器
    对比器 = 策略对比器(无风险利率=0.03)
    
    logger.info("策略对比器创建成功")
    logger.info(f"支持的对比方法: {[方法.value for 方法 in 对比方法]}")
    logger.info(f"支持的排名方法: {[方法.value for 方法 in 排名方法]}")
    
    return 对比器

if __name__ == "__main__":
    logger.info("🌟 中文化API使用示例")
    logger.info("=" * 50)
    
    try:
        logger.info("\n📊 风险管理示例:")
        风险管理示例()
        
        logger.info("\n🎯 策略开发示例:")
        策略开发示例()
        
        logger.info("\n🚀 回测引擎示例:")
        回测引擎示例()
        
        logger.info("\n📈 策略对比示例:")
        策略对比示例()
        
        logger.info("\n✨ 所有示例运行成功!")
        
    except Exception as e:
        logger.info(f"❌ 示例运行出错: {e}")
