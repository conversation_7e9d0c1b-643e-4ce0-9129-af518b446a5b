logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
后端服务修复系统演示

该示例展示了如何使用后端修复管理器来检测和修复各种后端问题。
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.monitoring.backend_fix_manager import BackendFixManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def main():
    """主演示函数"""
    logger.info("=" * 60)
    logger.info("后端服务修复系统演示")
    logger.info("=" * 60)
    
    # 创建后端修复管理器
    fix_manager = BackendFixManager()
    
    logger.info("\n1. 修复API端点问题...")
    api_result = await fix_manager.fix_api_endpoints()
    logger.info(f"   状态: {api_result.status.value}")
    logger.info(f"   消息: {api_result.message}")
    logger.info(f"   耗时: {api_result.duration:.2f}秒")
    
    logger.info("\n2. 修复数据库连接问题...")
    db_result = await fix_manager.fix_database_connections()
    logger.info(f"   状态: {db_result.status.value}")
    logger.info(f"   消息: {db_result.message}")
    logger.info(f"   耗时: {db_result.duration:.2f}秒")
    
    logger.info("\n3. 修复服务依赖问题...")
    service_result = await fix_manager.fix_service_dependencies()
    logger.info(f"   状态: {service_result.status.value}")
    logger.info(f"   消息: {service_result.message}")
    logger.info(f"   耗时: {service_result.duration:.2f}秒")
    
    logger.info("\n4. 优化后端性能...")
    perf_result = await fix_manager.optimize_performance()
    logger.info(f"   状态: {perf_result.status.value}")
    logger.info(f"   消息: {perf_result.message}")
    logger.info(f"   耗时: {perf_result.duration:.2f}秒")
    
    logger.info("\n5. 运行全面后端修复...")
    comprehensive_results = await fix_manager.run_comprehensive_fix()
    
    logger.info("\n   全面修复结果:")
    for component, result in comprehensive_results.items():
        logger.info(f"   - {component}: {result.status.value} - {result.message}")
    
    logger.info("\n6. 查看修复历史...")
    history = fix_manager.get_fix_history()
    logger.info(f"   总修复次数: {len(history)}")
    
    logger.info("\n7. 查看性能指标...")
    metrics = fix_manager.get_performance_metrics()
    logger.info(f"   总修复: {metrics['total_fixes']}")
    logger.info(f"   成功修复: {metrics['successful_fixes']}")
    logger.info(f"   失败修复: {metrics['failed_fixes']}")
    logger.info(f"   平均修复时间: {metrics['average_fix_time']:.2f}秒")
    
    logger.info("\n" + "=" * 60)
    logger.info("演示完成！")
    logger.info("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())