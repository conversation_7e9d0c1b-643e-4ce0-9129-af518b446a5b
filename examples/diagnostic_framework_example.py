logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
系统诊断框架使用示例。

此脚本演示如何使用综合系统诊断框架来检查系统健康状态、检测问题并生成修复建议。
"""

import asyncio
import logging
import sys
import os

# 添加src到路径以便导入
# Add project root to path for imports
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)

from monitoring.system_diagnostic_engine import SystemDiagnosticEngine
from monitoring.health_checker import HealthChecker
from monitoring.problem_detector import ProblemDetector
from monitoring.report_generator import ReportGenerator


async def main():
    """主示例函数。"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("=== 系统诊断框架示例 ===\n")
    
    # 创建并配置诊断引擎
    logger.info("1. 初始化诊断框架...")
    engine = SystemDiagnosticEngine()
    health_checker = HealthChecker()
    problem_detector = ProblemDetector()
    report_generator = ReportGenerator()
    
    # 注入依赖
    engine.set_health_checker(health_checker)
    engine.set_problem_detector(problem_detector)
    engine.set_report_generator(report_generator)
    
    # 注册要监控的组件
    components = ['frontend', 'backend', 'database', 'system', 'configuration']
    for component in components:
        engine.register_component(component)
    
    logger.info(f"已注册 {len(components)} 个组件进行监控\n")
    
    # 运行全面系统诊断
    logger.info("2. 运行全面系统诊断...")
    try:
        report = await engine.run_full_diagnosis()
        
        logger.info(f"诊断在 {report.execution_time:.2f} 秒内完成")
        logger.info(f"系统健康状态: {report.system_health.value.upper()}")
        logger.info(f"总问题数: {report.total_issues}")
        logger.info(f"严重问题数: {report.critical_issues}")
        logger.info(f"修复建议数: {len(report.recommendations)}\n")
        
    except Exception as e:
        logger.info(f"诊断过程中出错: {e}")
        return
    
    # 显示组件健康状态摘要
    logger.info("3. 组件健康状态摘要:")
    logger.info("-" * 40)
    for component, health in report.component_health.items():
        status_symbol = {
            'healthy': '✅',
            'warning': '⚠️',
            'critical': '❌',
            'unknown': '❓',
            'offline': '🔴'
        }.get(health.status.value, '❓')
        
        response_time = f" ({health.response_time:.2f}s)" if health.response_time else ""
        logger.info(f"{status_symbol} {component.upper()}: {health.status.value.upper()}{response_time}")
        
        if health.issues:
            for issue in health.issues[:2]:  # 显示前2个问题
                logger.info(f"   - {issue}")
    logger.info()
    
    # 显示检测到的问题
    if report.problems:
        logger.info("4. 检测到的问题:")
        logger.info("-" * 40)
        for i, problem in enumerate(report.problems[:5], 1):  # 显示前5个问题
            severity_symbol = {
                'low': '🟢',
                'medium': '🟡',
                'high': '🟠',
                'critical': '🔴'
            }.get(problem.severity.value, '🟡')
            
            logger.info(f"{severity_symbol} {i}. {problem.title}")
            logger.info(f"   类别: {problem.category.value}")
            logger.info(f"   严重性: {problem.severity.value.upper()}")
            logger.info(f"   描述: {problem.description}")
            if problem.affected_components:
                logger.info(f"   受影响组件: {', '.join(problem.affected_components)}")
            logger.info()
    else:
        logger.info("4. 未检测到问题! ✅\n")
    
    # 显示修复建议
    if report.recommendations:
        logger.info("5. 修复建议:")
        logger.info("-" * 40)
        for i, rec in enumerate(report.recommendations[:3], 1):  # 显示前3个建议
            risk_symbol = {
                'low': '🟢',
                'medium': '🟡',
                'high': '🔴'
            }.get(rec.risk_level.value, '🟡')
            
            type_symbol = {
                'automatic': '🤖',
                'manual': '👤',
                'restart': '🔄',
                'configuration': '⚙️',
                'update': '📦'
            }.get(rec.fix_type.value, '🔧')
            
            logger.info(f"{type_symbol} {i}. {rec.title}")
            logger.info(f"   类型: {rec.fix_type.value} | 风险: {risk_symbol} {rec.risk_level.value} | 时间: {rec.estimated_time}分钟")
            logger.info(f"   描述: {rec.description}")
            
            if rec.steps:
                logger.info("   步骤:")
                for step in rec.steps[:3]:  # 显示前3个步骤
                    logger.info(f"     - {step}")
                if len(rec.steps) > 3:
                    logger.info(f"     ... 还有 {len(rec.steps) - 3} 个步骤")
            logger.info()
    else:
        logger.info("5. 无需修复建议! ✅\n")
    
    # 生成并显示不同类型的报告
    logger.info("6. 生成报告...")
    logger.info("-" * 40)
    
    # 摘要报告
    summary_report = report_generator.generate_report(report, 'summary')
    logger.info("摘要报告:")
    logger.info(summary_report[:300] + "..." if len(summary_report) > 300 else summary_report)
    logger.info()
    
    # 将报告导出到文件
    try:
        # 创建报告目录
        reports_dir = "diagnostic_reports"
        os.makedirs(reports_dir, exist_ok=True)
        
        # 以不同格式导出
        timestamp = report.timestamp.strftime("%Y%m%d_%H%M%S")
        
        json_path = f"{reports_dir}/diagnostic_report_{timestamp}.json"
        txt_path = f"{reports_dir}/diagnostic_report_{timestamp}.txt"
        html_path = f"{reports_dir}/diagnostic_report_{timestamp}.html"
        
        success_json = report_generator.export_report(report, json_path, 'json')
        success_txt = report_generator.export_report(report, txt_path, 'txt')
        success_html = report_generator.export_report(report, html_path, 'html')
        
        logger.info("7. 报告导出结果:")
        logger.info("-" * 40)
        logger.info(f"JSON报告: {'✅ 已导出' if success_json else '❌ 失败'} -> {json_path}")
        logger.info(f"文本报告: {'✅ 已导出' if success_txt else '❌ 失败'} -> {txt_path}")
        logger.info(f"HTML报告: {'✅ 已导出' if success_html else '❌ 失败'} -> {html_path}")
        logger.info()
        
    except Exception as e:
        logger.info(f"导出报告时出错: {e}\n")
    
    # 显示引擎统计信息
    stats = engine.get_statistics()
    logger.info("8. 诊断引擎统计信息:")
    logger.info("-" * 40)
    logger.info(f"总诊断次数: {stats['diagnosis_count']}")
    logger.info(f"平均诊断时间: {stats['average_diagnosis_time']:.2f}秒")
    logger.info(f"已注册组件数: {stats['registered_components']}")
    logger.info(f"历史记录大小: {stats['history_size']}")
    logger.info()
    
    # 检查单个组件健康状态
    logger.info("9. 单个组件健康检查示例:")
    logger.info("-" * 40)
    try:
        frontend_health = await engine.check_component_health('frontend')
        logger.info(f"前端状态: {frontend_health.status.value.upper()}")
        if frontend_health.response_time:
            logger.info(f"前端响应时间: {frontend_health.response_time:.2f}秒")
        if frontend_health.issues:
            logger.info(f"前端问题: {', '.join(frontend_health.issues[:2])}")
    except Exception as e:
        logger.info(f"检查前端健康状态时出错: {e}")
    
    logger.info("\n=== 诊断框架示例完成 ===")
    
    # 提供后续步骤
    logger.info("\n后续步骤:")
    logger.info("- 查看 'diagnostic_reports' 目录中生成的报告")
    logger.info("- 处理识别出的任何严重或高优先级问题")
    logger.info("- 定期运行诊断以监控系统健康状态")
    logger.info("- 通过添加自己的问题模式和修复模板来自定义框架")


if __name__ == "__main__":
    # 运行示例
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("\n示例被用户中断")
    except Exception as e:
        logger.info(f"示例运行失败，错误: {e}")
        import traceback
        traceback.print_exc()