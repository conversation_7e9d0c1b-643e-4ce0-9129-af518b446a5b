import logging
logger = logging.getLogger(__name__)

"""
Advanced Calculator Module

This module provides a comprehensive calculator with basic arithmetic operations,
scientific functions, and memory management capabilities.

Example:
    >>> calc = Calculator()
    >>> result = calc.add(10, 5)
    >>> logger.info(result)
    15
"""

import math
from typing import List, Union, Optional
from enum import Enum

class OperationType(Enum):
    """Enumeration of supported operation types."""
    ADDITION = "addition"
    SUBTRACTION = "subtraction"
    MULTIPLICATION = "multiplication"
    DIVISION = "division"
    POWER = "power"
    ROOT = "root"

class CalculationHistory:
    """Manages calculation history and memory functions."""
    
    def __init__(self):
        """Initialize empty calculation history."""
        self.history: List[str] = []
        self.memory: float = 0.0
    
    def add_entry(self, operation: str, result: float) -> None:
        """
        Add a calculation entry to history.
        
        Args:
            operation: String representation of the operation
            result: The calculated result
        """
        entry = f"{operation} = {result}"
        self.history.append(entry)
        
        # Keep only last 100 entries
        if len(self.history) > 100:
            self.history = self.history[-100:]
    
    def get_history(self) -> List[str]:
        """
        Get calculation history.
        
        Returns:
            List of calculation history entries
        """
        return self.history.copy()
    
    def clear_history(self) -> None:
        """Clear all calculation history."""
        self.history.clear()
    
    def store_memory(self, value: float) -> None:
        """
        Store value in memory.
        
        Args:
            value: Value to store in memory
        """
        self.memory = value
    
    def recall_memory(self) -> float:
        """
        Recall value from memory.
        
        Returns:
            Value stored in memory
        """
        return self.memory
    
    def clear_memory(self) -> None:
        """Clear memory storage."""
        self.memory = 0.0

class Calculator:
    """
    Advanced calculator with arithmetic and scientific operations.
    
    This calculator supports basic arithmetic operations, scientific functions,
    memory management, and maintains a history of calculations.
    
    Attributes:
        precision: Number of decimal places for results
        history: Calculation history manager
    """
    
    def __init__(self, precision: int = 10):
        """
        Initialize calculator with specified precision.
        
        Args:
            precision: Number of decimal places for results (default: 10)
        """
        self.precision = precision
        self.history = CalculationHistory()
        self._last_result: Optional[float] = None
    
    def add(self, a: float, b: float) -> float:
        """
        Add two numbers.
        
        Args:
            a: First number
            b: Second number
            
        Returns:
            Sum of a and b
            
        Example:
            >>> calc = Calculator()
            >>> calc.add(5, 3)
            8.0
        """
        result = round(a + b, self.precision)
        self.history.add_entry(f"{a} + {b}", result)
        self._last_result = result
        return result
    
    def subtract(self, a: float, b: float) -> float:
        """
        Subtract second number from first number.
        
        Args:
            a: Number to subtract from
            b: Number to subtract
            
        Returns:
            Difference of a and b
        """
        result = round(a - b, self.precision)
        self.history.add_entry(f"{a} - {b}", result)
        self._last_result = result
        return result
    
    def multiply(self, a: float, b: float) -> float:
        """
        Multiply two numbers.
        
        Args:
            a: First number
            b: Second number
            
        Returns:
            Product of a and b
        """
        result = round(a * b, self.precision)
        self.history.add_entry(f"{a} × {b}", result)
        self._last_result = result
        return result
    
    def divide(self, a: float, b: float) -> float:
        """
        Divide first number by second number.
        
        Args:
            a: Dividend
            b: Divisor
            
        Returns:
            Quotient of a and b
            
        Raises:
            ValueError: If divisor is zero
        """
        if b == 0:
            raise ValueError("Cannot divide by zero")
        
        result = round(a / b, self.precision)
        self.history.add_entry(f"{a} ÷ {b}", result)
        self._last_result = result
        return result
    
    def power(self, base: float, exponent: float) -> float:
        """
        Raise base to the power of exponent.
        
        Args:
            base: Base number
            exponent: Exponent
            
        Returns:
            Result of base^exponent
        """
        result = round(pow(base, exponent), self.precision)
        self.history.add_entry(f"{base}^{exponent}", result)
        self._last_result = result
        return result
    
    def square_root(self, number: float) -> float:
        """
        Calculate square root of a number.
        
        Args:
            number: Number to find square root of
            
        Returns:
            Square root of the number
            
        Raises:
            ValueError: If number is negative
        """
        if number < 0:
            raise ValueError("Cannot calculate square root of negative number")
        
        result = round(math.sqrt(number), self.precision)
        self.history.add_entry(f"√{number}", result)
        self._last_result = result
        return result
    
    def factorial(self, n: int) -> int:
        """
        Calculate factorial of a number.
        
        Args:
            n: Non-negative integer
            
        Returns:
            Factorial of n
            
        Raises:
            ValueError: If n is negative or not an integer
        """
        if not isinstance(n, int) or n < 0:
            raise ValueError("Factorial is only defined for non-negative integers")
        
        result = math.factorial(n)
        self.history.add_entry(f"{n}!", result)
        self._last_result = float(result)
        return result
    
    def percentage(self, value: float, percent: float) -> float:
        """
        Calculate percentage of a value.
        
        Args:
            value: Base value
            percent: Percentage to calculate
            
        Returns:
            Percentage of the value
        """
        result = round((value * percent) / 100, self.precision)
        self.history.add_entry(f"{percent}% of {value}", result)
        self._last_result = result
        return result
    
    def get_last_result(self) -> Optional[float]:
        """
        Get the result of the last calculation.
        
        Returns:
            Last calculation result or None if no calculations performed
        """
        return self._last_result
    
    def reset(self) -> None:
        """Reset calculator state including history and memory."""
        self.history.clear_history()
        self.history.clear_memory()
        self._last_result = None

def create_scientific_calculator() -> Calculator:
    """
    Factory function to create a scientific calculator.
    
    Returns:
        Calculator instance configured for scientific operations
    """
    return Calculator(precision=15)

# Module-level convenience functions
def quick_add(a: float, b: float) -> float:
    """
    Quick addition without calculator instance.
    
    Args:
        a: First number
        b: Second number
        
    Returns:
        Sum of a and b
    """
    return a + b

def quick_multiply(a: float, b: float) -> float:
    """
    Quick multiplication without calculator instance.
    
    Args:
        a: First number
        b: Second number
        
    Returns:
        Product of a and b
    """
    return a * b

# Constants
PI = math.pi
E = math.e
GOLDEN_RATIO = (1 + math.sqrt(5)) / 2
