import logging
logger = logging.getLogger(__name__)
"""
Portfolio Management Demo

This example demonstrates the enhanced portfolio management capabilities
including position tracking, performance analysis, and rebalancing.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
import pandas as pd
import numpy as np

from src.models.portfolio import Portfolio
from src.models.portfolio_manager import PortfolioManager, PortfolioAllocation
from src.models.trading import Trade, OrderSide


def create_sample_trade(trade_id: str, symbol: str, side: str, quantity: float, 
                       price: float, commission: float = 5.0) -> Trade:
    """Create a sample trade for demonstration."""
    return Trade(
        id=trade_id,
        symbol=symbol,
        side=OrderSide.BUY if side == "BUY" else OrderSide.SELL,
        quantity=quantity,
        price=price,
        timestamp=datetime.now(),
        commission=commission
    )


def main():
    """Demonstrate portfolio management functionality."""
    logger.info("=== Portfolio Management Demo ===\n")
    
    # 1. Create portfolio with initial capital
    logger.info("1. Creating portfolio with $100,000 initial capital")
    portfolio = Portfolio(initial_capital=100000.0, cash=100000.0)
    manager = PortfolioManager(portfolio)
    
    logger.info(f"Initial portfolio value: ${portfolio.total_value:,.2f}")
    logger.info(f"Initial cash: ${portfolio.cash:,.2f}\n")
    
    # 2. Add some trades
    logger.info("2. Adding sample trades")
    trades = [
        create_sample_trade("trade_1", "AAPL", "BUY", 100, 150.0),
        create_sample_trade("trade_2", "GOOGL", "BUY", 25, 2000.0),
        create_sample_trade("trade_3", "MSFT", "BUY", 50, 300.0),
        create_sample_trade("trade_4", "TSLA", "BUY", 30, 800.0),
    ]
    
    for trade in trades:
        portfolio.add_trade(trade)
        logger.info(f"Added trade: {trade.side.value} {trade.quantity} {trade.symbol} @ ${trade.price}")
    
    logger.info(f"\nPortfolio value after trades: ${portfolio.total_value:,.2f}")
    logger.info(f"Cash remaining: ${portfolio.cash:,.2f}\n")
    
    # 3. Update market prices to simulate market movements
    logger.info("3. Updating market prices")
    market_prices = {
        "AAPL": 160.0,   # +6.67%
        "GOOGL": 2100.0, # +5.00%
        "MSFT": 290.0,   # -3.33%
        "TSLA": 850.0,   # +6.25%
    }
    
    portfolio.update_positions_market_value(market_prices)
    
    for symbol, price in market_prices.items():
        position = portfolio.get_position(symbol)
        if position:
            pnl_pct = (price - position.avg_price) / position.avg_price * 100
            logger.info(f"{symbol}: ${position.avg_price:.2f} -> ${price:.2f} ({pnl_pct:+.2f}%)")
    
    logger.info(f"\nUpdated portfolio value: ${portfolio.total_value:,.2f}")
    logger.info(f"Total unrealized P&L: ${portfolio.unrealized_pnl:,.2f}\n")
    
    # 4. Analyze current allocation
    logger.info("4. Current Portfolio Allocation")
    allocation = manager.get_current_allocation()
    for symbol, weight in allocation.items():
        logger.info(f"{symbol}: {weight:.2%}")
    
    logger.info()
    
    # 5. Set target allocation and analyze drift
    logger.info("5. Setting target allocation and analyzing drift")
    target_allocations = [
        PortfolioAllocation("AAPL", 0.25),
        PortfolioAllocation("GOOGL", 0.25),
        PortfolioAllocation("MSFT", 0.25),
        PortfolioAllocation("TSLA", 0.20),
        PortfolioAllocation("CASH", 0.05)
    ]
    
    manager.set_target_allocation(target_allocations)
    
    logger.info("Target allocation:")
    for alloc in target_allocations:
        logger.info(f"{alloc.symbol}: {alloc.target_weight:.2%}")
    
    logger.info("\nAllocation drift:")
    drift = manager.analyze_allocation_drift()
    for symbol, drift_value in drift.items():
        logger.info(f"{symbol}: {drift_value:+.2%}")
    
    logger.info()
    
    # 6. Get rebalancing recommendations
    logger.info("6. Rebalancing Recommendations")
    recommendations = manager.get_rebalance_recommendations(market_prices)
    
    if recommendations:
        for rec in recommendations:
            logger.info(f"{rec.symbol}: {rec.recommended_action} {rec.recommended_quantity:.2f} shares "
                  f"(${rec.recommended_value:,.2f})")
    else:
        logger.info("No rebalancing needed at current thresholds")
    
    logger.info()
    
    # 7. Portfolio concentration analysis
    logger.info("7. Portfolio Concentration Analysis")
    concentration = manager.analyze_position_concentration()
    logger.info(f"Number of positions: {concentration['number_of_positions']}")
    logger.info(f"Concentration ratio (top 5): {concentration['concentration_ratio']:.2%}")
    logger.info(f"Herfindahl Index: {concentration['herfindahl_index']:.4f}")
    logger.info(f"Effective positions: {concentration['effective_positions']:.2f}")
    logger.info(f"Largest position weight: {concentration['largest_position_weight']:.2%}")
    logger.info()
    
    # 8. Add some daily returns for performance metrics
    logger.info("8. Performance Metrics (simulated daily returns)")
    # Simulate some daily returns
    np.random.seed(42)  # For reproducible results
    daily_returns = np.random.normal(0.001, 0.02, 30)  # 30 days of returns
    portfolio.daily_returns = daily_returns.tolist()
    
    metrics = portfolio.get_performance_metrics()
    logger.info(f"Total return: {metrics['total_return']:.2%}")
    logger.info(f"Annualized return: {metrics['annualized_return']:.2%}")
    logger.info(f"Volatility: {metrics['volatility']:.2%}")
    logger.info(f"Sharpe ratio: {metrics['sharpe_ratio']:.2f}")
    logger.info(f"Sortino ratio: {metrics['sortino_ratio']:.2f}")
    logger.info(f"Max drawdown: {metrics['max_drawdown']:.2%}")
    logger.info()
    
    # 9. Risk metrics
    logger.info("9. Risk Metrics")
    var_95 = manager.calculate_var(0.05)
    var_99 = manager.calculate_var(0.01)
    es_95 = manager.calculate_expected_shortfall(0.05)
    
    logger.info(f"Value at Risk (95%): ${var_95:,.2f}")
    logger.info(f"Value at Risk (99%): ${var_99:,.2f}")
    logger.info(f"Expected Shortfall (95%): ${es_95:,.2f}")
    logger.info()
    
    # 10. Cash flow summary
    logger.info("10. Cash Flow Summary")
    cash_flow_summary = portfolio.get_cash_flow_summary()
    logger.info(f"Total outflow: ${cash_flow_summary['total_outflow']:,.2f}")
    logger.info(f"Trade flows: ${cash_flow_summary['trade_flows']:,.2f}")
    logger.info(f"Commission paid: ${cash_flow_summary['commission_paid']:,.2f}")
    logger.info()
    
    # 11. Generate comprehensive report
    logger.info("11. Comprehensive Performance Report")
    report = manager.generate_performance_report()
    
    logger.info("Portfolio Summary:")
    summary = report['portfolio_summary']
    logger.info(f"  Initial Capital: ${summary['initial_capital']:,.2f}")
    logger.info(f"  Current Value: ${summary['current_value']:,.2f}")
    logger.info(f"  Number of Positions: {summary['number_of_positions']}")
    logger.info(f"  Total Trades: {summary['total_trades']}")
    
    logger.info("\nRisk Metrics:")
    risk_metrics = report['risk_metrics']
    logger.info(f"  VaR (95%): ${risk_metrics['var_95']:,.2f}")
    logger.info(f"  VaR (99%): ${risk_metrics['var_99']:,.2f}")
    logger.info()
    
    # 12. Export data to DataFrames
    logger.info("12. Exporting Data to DataFrames")
    dataframes = manager.export_to_dataframe()
    
    logger.info("Available DataFrames:")
    for name, df in dataframes.items():
        if not df.empty:
            logger.info(f"  {name}: {len(df)} records")
        else:
            logger.info(f"  {name}: empty")
    
    logger.info("\nPortfolio History (last 5 records):")
    if not dataframes['portfolio_history'].empty:
        logger.info(dataframes['portfolio_history'].tail().to_string())
    
    logger.info("\n=== Demo Complete ===")


if __name__ == "__main__":
    main()