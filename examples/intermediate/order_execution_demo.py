import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
Demonstration of enhanced order execution simulation.

This script shows the different order types, slippage, commission,
partial fills, and rejection handling in the order execution system.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import uuid
from datetime import datetime
from src.backtest.executor import OrderExecutor, ExecutionConfig
from src.models.trading import Order, OrderSide, OrderType
from src.models.market_data import MarketData
from src.models.portfolio import Portfolio, Position


def create_sample_data():
    """Create sample market data and portfolio."""
    market_data = MarketData(
        symbol="AAPL",
        timestamp=datetime(2024, 1, 15, 10, 0),
        open=150.0,
        high=152.0,
        low=149.0,
        close=151.0,
        volume=1000000
    )
    
    portfolio = Portfolio(initial_capital=100000, cash=50000)
    # Add existing position
    position = Position(
        symbol="AAPL",
        quantity=100,
        avg_price=145.0,
        market_value=15100.0,
        unrealized_pnl=600.0
    )
    portfolio.positions["AAPL"] = position
    
    return market_data, portfolio


def demo_order_types():
    """Demonstrate different order types."""
    logger.info("=== Order Types Demonstration ===\n")
    
    market_data, portfolio = create_sample_data()
    
    # Create executor with demo configuration
    config = ExecutionConfig(
        commission_rate=0.001,
        slippage_rate=0.0005,
        partial_fill_probability=0.2,  # 20% chance of partial fills
        rejection_probability=0.05     # 5% rejection rate
    )
    executor = OrderExecutor(config)
    executor.set_random_seed(42)  # For consistent demo results
    
    logger.info(f"Market Data: {market_data.symbol} @ ${market_data.close}")
    logger.info(f"Portfolio Cash: ${portfolio.cash:,.2f}")
    logger.info(f"Current Position: {portfolio.positions['AAPL'].quantity} shares @ ${portfolio.positions['AAPL'].avg_price}\n")
    
    # 1. Market Order
    logger.info("1. Market Order (Buy 50 shares)")
    market_order = Order(
        id=str(uuid.uuid4()),
        symbol="AAPL",
        side=OrderSide.BUY,
        order_type=OrderType.MARKET,
        quantity=50
    )
    
    fill = executor.execute_order(market_order, market_data, portfolio)
    if fill:
        logger.info(f"   ✓ Executed: {fill.quantity} shares @ ${fill.price:.4f}")
        logger.info(f"   Commission: ${fill.commission:.2f}")
        logger.info(f"   Status: {market_order.status.value}")
    else:
        logger.info(f"   ✗ Rejected: {market_order.status.value}")
    logger.info()
    
    # 2. Limit Order
    logger.info("2. Limit Order (Buy 30 shares @ $151.00)")
    limit_order = Order(
        id=str(uuid.uuid4()),
        symbol="AAPL",
        side=OrderSide.BUY,
        order_type=OrderType.LIMIT,
        quantity=30,
        price=151.00
    )
    
    fill = executor.execute_order(limit_order, market_data, portfolio)
    if fill:
        logger.info(f"   ✓ Executed: {fill.quantity} shares @ ${fill.price:.4f}")
        logger.info(f"   Commission: ${fill.commission:.2f}")
        logger.info(f"   Status: {limit_order.status.value}")
    else:
        logger.info(f"   ✗ Rejected: {limit_order.status.value}")
    logger.info()
    
    # 3. Stop Order
    logger.info("3. Stop Order (Sell 25 shares, stop @ $150.00)")
    stop_order = Order(
        id=str(uuid.uuid4()),
        symbol="AAPL",
        side=OrderSide.SELL,
        order_type=OrderType.STOP,
        quantity=25,
        stop_price=150.00
    )
    
    fill = executor.execute_order(stop_order, market_data, portfolio)
    if fill:
        logger.info(f"   ✓ Executed: {fill.quantity} shares @ ${fill.price:.4f}")
        logger.info(f"   Commission: ${fill.commission:.2f}")
        logger.info(f"   Status: {stop_order.status.value}")
    else:
        logger.info(f"   ✗ Not triggered or rejected: {stop_order.status.value}")
    logger.info()
    
    # 4. Stop-Limit Order
    logger.info("4. Stop-Limit Order (Sell 20 shares, stop @ $150.00, limit @ $149.50)")
    stop_limit_order = Order(
        id=str(uuid.uuid4()),
        symbol="AAPL",
        side=OrderSide.SELL,
        order_type=OrderType.STOP_LIMIT,
        quantity=20,
        stop_price=150.00,
        price=149.50
    )
    
    fill = executor.execute_order(stop_limit_order, market_data, portfolio)
    if fill:
        logger.info(f"   ✓ Executed: {fill.quantity} shares @ ${fill.price:.4f}")
        logger.info(f"   Commission: ${fill.commission:.2f}")
        logger.info(f"   Status: {stop_limit_order.status.value}")
    else:
        logger.info(f"   ✗ Not triggered or rejected: {stop_limit_order.status.value}")
    logger.info()


def demo_partial_fills():
    """Demonstrate partial fill functionality."""
    logger.info("=== Partial Fills Demonstration ===\n")
    
    market_data, portfolio = create_sample_data()
    
    # Configure for high partial fill probability
    config = ExecutionConfig(
        partial_fill_probability=1.0,  # Always partial fill
        min_fill_ratio=0.3,
        max_fill_ratio=0.7
    )
    executor = OrderExecutor(config)
    executor.set_random_seed(123)
    
    order = Order(
        id=str(uuid.uuid4()),
        symbol="AAPL",
        side=OrderSide.BUY,
        order_type=OrderType.MARKET,
        quantity=100
    )
    
    logger.info(f"Original Order: {order.quantity} shares")
    
    fill = executor.execute_order(order, market_data, portfolio)
    if fill:
        logger.info(f"Partial Fill: {fill.quantity} shares @ ${fill.price:.4f}")
        logger.info(f"Remaining: {order.quantity} shares")
        logger.info(f"Order Status: {order.status.value}")
    logger.info()


def demo_cost_analysis():
    """Demonstrate cost analysis with different order sizes."""
    logger.info("=== Cost Analysis Demonstration ===\n")
    
    market_data, portfolio = create_sample_data()
    
    config = ExecutionConfig(
        commission_rate=0.001,
        slippage_rate=0.0005,
        market_impact_rate=0.0001
    )
    executor = OrderExecutor(config)
    executor.set_random_seed(456)
    
    order_sizes = [10, 100, 1000]
    
    logger.info("Order Size | Execution Price | Commission | Total Cost")
    logger.info("-" * 55)
    
    for size in order_sizes:
        order = Order(
            id=str(uuid.uuid4()),
            symbol="AAPL",
            side=OrderSide.BUY,
            order_type=OrderType.MARKET,
            quantity=size
        )
        
        fill = executor.execute_order(order, market_data, portfolio)
        if fill:
            total_cost = fill.quantity * fill.price + fill.commission
            logger.info(f"{size:10d} | ${fill.price:13.4f} | ${fill.commission:9.2f} | ${total_cost:9.2f}")
    logger.info()


def demo_rejection_scenarios():
    """Demonstrate order rejection scenarios."""
    logger.info("=== Order Rejection Scenarios ===\n")
    
    market_data, portfolio = create_sample_data()
    
    # 1. Insufficient funds
    logger.info("1. Insufficient Funds Test")
    poor_portfolio = Portfolio(initial_capital=1000, cash=500)
    
    config = ExecutionConfig()
    executor = OrderExecutor(config)
    
    large_order = Order(
        id=str(uuid.uuid4()),
        symbol="AAPL",
        side=OrderSide.BUY,
        order_type=OrderType.MARKET,
        quantity=100  # Would cost ~$15,100
    )
    
    fill = executor.execute_order(large_order, market_data, poor_portfolio)
    if fill:
        logger.info("   ✓ Order executed")
    else:
        logger.info(f"   ✗ Order rejected: {large_order.status.value}")
    logger.info()
    
    # 2. Insufficient shares
    logger.info("2. Insufficient Shares Test")
    empty_portfolio = Portfolio(initial_capital=100000, cash=50000)
    
    sell_order = Order(
        id=str(uuid.uuid4()),
        symbol="AAPL",
        side=OrderSide.SELL,
        order_type=OrderType.MARKET,
        quantity=100  # Don't have any shares
    )
    
    fill = executor.execute_order(sell_order, market_data, empty_portfolio)
    if fill:
        logger.info("   ✓ Order executed")
    else:
        logger.info(f"   ✗ Order rejected: {sell_order.status.value}")
    logger.info()
    
    # 3. Price tolerance
    logger.info("3. Price Tolerance Test")
    config = ExecutionConfig(price_tolerance=0.01)  # Very strict 1%
    executor = OrderExecutor(config)
    
    far_limit_order = Order(
        id=str(uuid.uuid4()),
        symbol="AAPL",
        side=OrderSide.BUY,
        order_type=OrderType.LIMIT,
        quantity=10,
        price=140.00  # Too far from market price ($151)
    )
    
    fill = executor.execute_order(far_limit_order, market_data, portfolio)
    if fill:
        logger.info("   ✓ Order executed")
    else:
        logger.info(f"   ✗ Order rejected: {far_limit_order.status.value}")
    logger.info()


def main():
    """Run all demonstrations."""
    logger.info("Enhanced Order Execution Simulation Demo")
    logger.info("=" * 50)
    logger.info()
    
    demo_order_types()
    demo_partial_fills()
    demo_cost_analysis()
    demo_rejection_scenarios()
    
    logger.info("Demo completed!")


if __name__ == "__main__":
    main()