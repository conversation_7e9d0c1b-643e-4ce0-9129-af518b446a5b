import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
Strategy Framework Example

This example demonstrates how to use the strategy framework to create,
manage, and execute trading strategies.
"""

import sys
import os
# Add project root to path for imports
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)

from src.market.strategies.base import BaseStrategy, StrategyContext
from src.market.strategies.parameters import StrategyParameter, ParameterType, ParameterValidator
from src.market.strategies.lifecycle import StrategyLifecycleManager, StrategyState
from src.market.strategies.signals import Signal, SignalType, SignalManager
from datetime import datetime
import pandas as pd
import numpy as np


class MovingAverageCrossoverStrategy(BaseStrategy):
    """
    移动平均交叉策略示例
    
    当短期移动平均线上穿长期移动平均线时产生买入信号，
    当短期移动平均线下穿长期移动平均线时产生卖出信号。
    """
    
    def get_parameter_definitions(self):
        """定义策略参数"""
        return {
            'short_period': StrategyParameter(
                name='short_period',
                param_type=ParameterType.INTEGER,
                default_value=10,
                required=True,
                description='短期移动平均周期',
                min_value=1,
                max_value=50,
                validator=ParameterValidator.time_period
            ),
            'long_period': StrategyParameter(
                name='long_period',
                param_type=ParameterType.INTEGER,
                default_value=30,
                required=True,
                description='长期移动平均周期',
                min_value=10,
                max_value=200,
                validator=ParameterValidator.time_period
            ),
            'min_confidence': StrategyParameter(
                name='min_confidence',
                param_type=ParameterType.FLOAT,
                default_value=0.6,
                required=False,
                description='最小信号置信度',
                min_value=0.0,
                max_value=1.0,
                validator=ParameterValidator.probability
            )
        }
    
    def initialize(self, context: StrategyContext):
        """策略初始化"""
        self.context = context
        self.is_initialized = True
        
        # 获取参数
        self.short_period = self.get_parameter('short_period')
        self.long_period = self.get_parameter('long_period')
        self.min_confidence = self.get_parameter('min_confidence')
        
        # 验证参数逻辑
        if self.short_period >= self.long_period:
            raise ValueError("短期周期必须小于长期周期")
        
        # 初始化状态变量
        self.last_signal = None
        self.signal_count = 0
        
        self.logger.info(f"策略 {self.name} 初始化完成")
        self.logger.info(f"参数: 短期={self.short_period}, 长期={self.long_period}")
    
    def on_data(self, data):
        """处理市场数据并生成信号"""
        if not self.is_initialized:
            return []
        
        signals = []
        
        # 如果是单个标的的数据
        if hasattr(data, 'symbol'):
            signal = self._process_single_symbol(data)
            if signal:
                signals.append(signal)
        
        # 如果是多个标的的数据字典
        elif isinstance(data, dict):
            for symbol, market_data in data.items():
                signal = self._process_single_symbol(market_data)
                if signal:
                    signals.append(signal)
        
        return signals
    
    def _process_single_symbol(self, market_data):
        """处理单个标的的数据"""
        try:
            # 获取历史数据
            historical_data = self.context.get_historical_data(
                symbol=market_data.symbol,
                lookback=max(self.long_period + 10, 50)  # 确保有足够的数据
            )
            
            if len(historical_data) < self.long_period:
                self.logger.warning(f"数据不足: {market_data.symbol}")
                return None
            
            # 计算移动平均线
            short_ma = historical_data['close'].rolling(window=self.short_period).mean()
            long_ma = historical_data['close'].rolling(window=self.long_period).mean()
            
            # 获取最新的移动平均值
            current_short_ma = short_ma.iloc[-1]
            current_long_ma = long_ma.iloc[-1]
            prev_short_ma = short_ma.iloc[-2]
            prev_long_ma = long_ma.iloc[-2]
            
            # 检查交叉信号
            signal_type = None
            confidence = 0.0
            
            # 金叉：短期MA上穿长期MA
            if (prev_short_ma <= prev_long_ma and 
                current_short_ma > current_long_ma):
                signal_type = SignalType.BUY
                # 计算置信度（基于MA差距和趋势强度）
                ma_diff_ratio = (current_short_ma - current_long_ma) / current_long_ma
                confidence = min(0.9, 0.5 + abs(ma_diff_ratio) * 10)
            
            # 死叉：短期MA下穿长期MA
            elif (prev_short_ma >= prev_long_ma and 
                  current_short_ma < current_long_ma):
                signal_type = SignalType.SELL
                ma_diff_ratio = (current_long_ma - current_short_ma) / current_long_ma
                confidence = min(0.9, 0.5 + abs(ma_diff_ratio) * 10)
            
            # 生成信号
            if signal_type and confidence >= self.min_confidence:
                signal = Signal(
                    symbol=market_data.symbol,
                    signal_type=signal_type,
                    timestamp=datetime.now(),
                    strategy_name=self.name,
                    confidence=confidence,
                    price=market_data.close,
                    metadata={
                        'short_ma': current_short_ma,
                        'long_ma': current_long_ma,
                        'ma_diff_ratio': ma_diff_ratio if 'ma_diff_ratio' in locals() else 0,
                        'short_period': self.short_period,
                        'long_period': self.long_period
                    }
                )
                
                self.signal_count += 1
                self.last_signal = signal
                
                return signal
            
        except Exception as e:
            self.logger.error(f"处理数据时发生错误: {e}")
        
        return None
    
    def on_signal(self, signal: Signal):
        """信号生成后的处理"""
        self.logger.info(f"生成信号: {signal.symbol} {signal.signal_type.value} "
                        f"置信度={signal.confidence:.2f} 价格={signal.price}")
    
    def cleanup(self):
        """策略清理"""
        self.logger.info(f"策略 {self.name} 清理完成，共生成 {self.signal_count} 个信号")
        super().cleanup()


class RSIMeanReversionStrategy(BaseStrategy):
    """
    RSI均值回归策略示例
    
    当RSI低于超卖阈值时产生买入信号，
    当RSI高于超买阈值时产生卖出信号。
    """
    
    def get_parameter_definitions(self):
        return {
            'rsi_period': StrategyParameter(
                name='rsi_period',
                param_type=ParameterType.INTEGER,
                default_value=14,
                required=True,
                description='RSI计算周期',
                min_value=2,
                max_value=50
            ),
            'oversold_threshold': StrategyParameter(
                name='oversold_threshold',
                param_type=ParameterType.FLOAT,
                default_value=30.0,
                required=True,
                description='超卖阈值',
                min_value=10.0,
                max_value=40.0
            ),
            'overbought_threshold': StrategyParameter(
                name='overbought_threshold',
                param_type=ParameterType.FLOAT,
                default_value=70.0,
                required=True,
                description='超买阈值',
                min_value=60.0,
                max_value=90.0
            )
        }
    
    def initialize(self, context: StrategyContext):
        self.context = context
        self.is_initialized = True
        
        self.rsi_period = self.get_parameter('rsi_period')
        self.oversold_threshold = self.get_parameter('oversold_threshold')
        self.overbought_threshold = self.get_parameter('overbought_threshold')
        
        if self.oversold_threshold >= self.overbought_threshold:
            raise ValueError("超卖阈值必须小于超买阈值")
        
        self.logger.info(f"RSI策略 {self.name} 初始化完成")
    
    def on_data(self, data):
        """简化的RSI策略实现"""
        if not self.is_initialized:
            return []
        
        # 这里简化实现，实际应该计算真正的RSI
        # 为了演示目的，我们模拟RSI值
        import random
        mock_rsi = random.uniform(20, 80)
        
        signals = []
        symbol = data.symbol if hasattr(data, 'symbol') else 'MOCK'
        
        if mock_rsi < self.oversold_threshold:
            signal = Signal(
                symbol=symbol,
                signal_type=SignalType.BUY,
                timestamp=datetime.now(),
                strategy_name=self.name,
                confidence=0.7,
                metadata={'rsi': mock_rsi}
            )
            signals.append(signal)
        elif mock_rsi > self.overbought_threshold:
            signal = Signal(
                symbol=symbol,
                signal_type=SignalType.SELL,
                timestamp=datetime.now(),
                strategy_name=self.name,
                confidence=0.7,
                metadata={'rsi': mock_rsi}
            )
            signals.append(signal)
        
        return signals


def demonstrate_strategy_framework():
    """演示策略框架的完整使用流程"""
    logger.info("策略框架演示")
    logger.info("=" * 60)
    
    # 1. 创建生命周期管理器
    logger.info("\n1. 创建策略生命周期管理器")
    lifecycle_manager = StrategyLifecycleManager()
    
    # 添加状态变化回调
    def on_state_change(strategy_name, old_state, new_state):
        logger.info(f"   策略 {strategy_name}: {old_state} -> {new_state}")
    
    lifecycle_manager.add_state_change_callback(on_state_change)
    
    # 2. 创建策略实例
    logger.info("\n2. 创建策略实例")
    
    # 移动平均策略
    ma_strategy = MovingAverageCrossoverStrategy(
        name='MA_Crossover',
        parameters={
            'short_period': 5,
            'long_period': 20,
            'min_confidence': 0.6
        }
    )
    logger.info(f"   创建策略: {ma_strategy.name}")
    
    # RSI策略
    rsi_strategy = RSIMeanReversionStrategy(
        name='RSI_MeanReversion',
        parameters={
            'rsi_period': 14,
            'oversold_threshold': 25.0,
            'overbought_threshold': 75.0
        }
    )
    logger.info(f"   创建策略: {rsi_strategy.name}")
    
    # 3. 创建策略上下文
    logger.info("\n3. 创建策略上下文")
    
    # 模拟数据管理器
    class MockDataManager:
        def get_historical_data(self, symbol, lookback, end_date=None):
            # 生成模拟历史数据
            dates = pd.date_range(end=datetime.now(), periods=lookback, freq='D')
            np.random.seed(42)  # 确保可重复性
            prices = 100 + np.cumsum(np.random.randn(lookback) * 0.5)
            
            return pd.DataFrame({
                'timestamp': dates,
                'open': prices * (1 + np.random.randn(lookback) * 0.001),
                'high': prices * (1 + abs(np.random.randn(lookback)) * 0.002),
                'low': prices * (1 - abs(np.random.randn(lookback)) * 0.002),
                'close': prices,
                'volume': np.random.randint(1000, 10000, lookback)
            })
    
    # 创建上下文
    context1 = StrategyContext(data_manager=MockDataManager())
    context2 = StrategyContext(data_manager=MockDataManager())
    
    # 4. 注册策略
    logger.info("\n4. 注册策略到生命周期管理器")
    lifecycle_manager.register_strategy(ma_strategy, context1)
    lifecycle_manager.register_strategy(rsi_strategy, context2)
    
    # 5. 初始化策略
    logger.info("\n5. 初始化策略")
    lifecycle_manager.initialize_strategy('MA_Crossover')
    lifecycle_manager.initialize_strategy('RSI_MeanReversion')
    
    # 6. 启动策略
    logger.info("\n6. 启动策略")
    lifecycle_manager.start_strategy('MA_Crossover')
    lifecycle_manager.start_strategy('RSI_MeanReversion')
    
    # 7. 模拟数据处理
    logger.info("\n7. 模拟市场数据处理")
    
    # 模拟市场数据
    class MockMarketData:
        def __init__(self, symbol, price):
            self.symbol = symbol
            self.timestamp = datetime.now()
            self.open = price
            self.high = price * 1.01
            self.low = price * 0.99
            self.close = price
            self.volume = 10000
    
    # 处理几轮数据
    symbols = ['AAPL', 'GOOGL', 'MSFT']
    for i in range(3):
        logger.info(f"\n   第 {i+1} 轮数据处理:")
        for symbol in symbols:
            price = 100 + i * 5 + hash(symbol) % 20
            market_data = MockMarketData(symbol, price)
            
            # 处理MA策略
            ma_signals = lifecycle_manager.process_data('MA_Crossover', market_data)
            if ma_signals:
                for signal in ma_signals:
                    logger.info(f"     MA策略信号: {signal.symbol} {signal.signal_type.value} "
                          f"置信度={signal.confidence:.2f}")
            
            # 处理RSI策略
            rsi_signals = lifecycle_manager.process_data('RSI_MeanReversion', market_data)
            if rsi_signals:
                for signal in rsi_signals:
                    logger.info(f"     RSI策略信号: {signal.symbol} {signal.signal_type.value} "
                          f"置信度={signal.confidence:.2f}")
    
    # 8. 查看策略状态
    logger.info("\n8. 查看策略状态")
    all_strategies = lifecycle_manager.get_all_strategies()
    for name, info in all_strategies.items():
        status = info['status']
        logger.info(f"   策略 {name}:")
        logger.info(f"     状态: {info['state']}")
        logger.info(f"     已初始化: {status['is_initialized']}")
        logger.info(f"     运行中: {status['is_active']}")
        logger.info(f"     参数: {status['parameters']}")
    
    # 9. 暂停和恢复策略
    logger.info("\n9. 暂停和恢复策略")
    lifecycle_manager.pause_strategy('MA_Crossover')
    logger.info(f"   MA策略状态: {lifecycle_manager.get_strategy_state('MA_Crossover')}")
    
    lifecycle_manager.start_strategy('MA_Crossover')
    logger.info(f"   MA策略状态: {lifecycle_manager.get_strategy_state('MA_Crossover')}")
    
    # 10. 关闭策略
    logger.info("\n10. 关闭策略框架")
    lifecycle_manager.shutdown()
    
    logger.info("\n策略框架演示完成！")


def demonstrate_parameter_validation():
    """演示参数验证功能"""
    logger.info("\n参数验证演示")
    logger.info("-" * 40)
    
    # 创建参数定义
    param = StrategyParameter(
        name='test_param',
        param_type=ParameterType.INTEGER,
        default_value=10,
        required=True,
        min_value=1,
        max_value=100,
        validator=ParameterValidator.positive_number
    )
    
    # 测试有效值
    test_values = [5, 50, 100, 0, 150, -5, 'invalid']
    
    for value in test_values:
        try:
            param.validate(value)
            logger.info(f"   ✓ 值 {value} 验证通过")
        except ValueError as e:
            logger.info(f"   ✗ 值 {value} 验证失败: {e}")
        except Exception as e:
            logger.info(f"   ✗ 值 {value} 验证异常: {e}")


def demonstrate_signal_management():
    """演示信号管理功能"""
    logger.info("\n信号管理演示")
    logger.info("-" * 40)
    
    from src.market.strategies.signals import SignalQualityAssessor, SignalAggregator
    
    # 创建信号管理器
    signal_manager = SignalManager()
    quality_assessor = SignalQualityAssessor()
    aggregator = SignalAggregator()
    
    # 创建测试信号
    signals = [
        Signal(
            symbol='AAPL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            strategy_name='Strategy1',
            confidence=0.8,
            price=150.0
        ),
        Signal(
            symbol='AAPL',
            signal_type=SignalType.SELL,
            timestamp=datetime.now(),
            strategy_name='Strategy2',
            confidence=0.6,
            price=149.0
        ),
        Signal(
            symbol='GOOGL',
            signal_type=SignalType.BUY,
            timestamp=datetime.now(),
            strategy_name='Strategy1',
            confidence=0.9,
            price=2500.0
        )
    ]
    
    # 添加信号
    for signal in signals:
        signal_manager.add_signal(signal)
        logger.info(f"   添加信号: {signal.symbol} {signal.signal_type.value}")
    
    # 信号质量评估
    logger.info("\n   信号质量评估:")
    for signal in signals:
        quality = quality_assessor.assess_signal_quality(signal)
        logger.info(f"     {signal.symbol} {signal.signal_type.value}: "
              f"综合质量={quality['overall_quality']:.2f}")
    
    # 信号聚合
    logger.info("\n   信号聚合:")
    aggregated = aggregator.aggregate_signals(signals, 'highest_confidence')
    for signal in aggregated:
        logger.info(f"     聚合结果: {signal.symbol} {signal.signal_type.value} "
              f"置信度={signal.confidence:.2f}")
    
    # 统计信息
    stats = signal_manager.get_signal_statistics()
    logger.info(f"\n   信号统计: {stats}")


if __name__ == "__main__":
    try:
        demonstrate_strategy_framework()
        demonstrate_parameter_validation()
        demonstrate_signal_management()
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 策略框架演示成功完成！")
        logger.info("   - 策略基础类和接口 ✓")
        logger.info("   - 策略上下文管理器 ✓") 
        logger.info("   - 策略生命周期管理 ✓")
        logger.info("   - 策略参数管理和验证 ✓")
        logger.info("   - 信号生成和管理 ✓")
        
    except Exception as e:
        logger.info(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)