import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
Technical Indicators Library Example

This example demonstrates the usage of the technical indicators library
including built-in indicators, calculation engine, and custom indicators.
"""

import pandas as pd
import numpy as np
import sys
import os

# Add project root to path for imports
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)

from src.indicators import (
    sma, ema, rsi, macd, bollinger_bands, atr, obv, vwap,
    IndicatorEngine, get_engine, calculate_indicator
)


def create_sample_data():
    """Create sample OHLCV data for demonstration"""
    np.random.seed(42)
    dates = pd.date_range(start='2023-01-01', periods=100, freq='D')
    
    # Generate realistic price data
    prices = [100]
    for i in range(99):
        change = np.random.randn() * 0.02  # 2% daily volatility
        prices.append(prices[-1] * (1 + change))
    
    # Generate OHLCV data
    closes = np.array(prices)
    highs = closes * (1 + np.abs(np.random.randn(100) * 0.01))
    lows = closes * (1 - np.abs(np.random.randn(100) * 0.01))
    opens = closes * (1 + np.random.randn(100) * 0.005)
    volumes = np.random.randint(1000, 10000, 100)
    
    return pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates)


def demonstrate_basic_indicators():
    """Demonstrate basic technical indicators"""
    logger.info("=== Basic Technical Indicators ===")
    
    data = create_sample_data()
    logger.info(f"Sample data shape: {data.shape}")
    logger.info(f"Date range: {data.index[0]} to {data.index[-1]}")
    logger.info()
    
    # Moving Averages
    logger.info("1. Moving Averages:")
    sma_20 = sma(data['close'], 20)
    ema_20 = ema(data['close'], 20)
    logger.info(f"   SMA(20) last value: {sma_20.iloc[-1]:.2f}")
    logger.info(f"   EMA(20) last value: {ema_20.iloc[-1]:.2f}")
    logger.info()
    
    # Momentum Indicators
    logger.info("2. Momentum Indicators:")
    rsi_14 = rsi(data['close'], 14)
    macd_result = macd(data['close'])
    logger.info(f"   RSI(14) last value: {rsi_14.iloc[-1]:.2f}")
    logger.info(f"   MACD last value: {macd_result['macd'].iloc[-1]:.4f}")
    logger.info(f"   MACD Signal last value: {macd_result['signal'].iloc[-1]:.4f}")
    logger.info()
    
    # Volatility Indicators
    logger.info("3. Volatility Indicators:")
    bb_result = bollinger_bands(data['close'], 20, 2.0)
    atr_14 = atr(data, 14)
    logger.info(f"   Bollinger Upper: {bb_result['upper'].iloc[-1]:.2f}")
    logger.info(f"   Bollinger Lower: {bb_result['lower'].iloc[-1]:.2f}")
    logger.info(f"   ATR(14): {atr_14.iloc[-1]:.2f}")
    logger.info()
    
    # Volume Indicators
    logger.info("4. Volume Indicators:")
    obv_result = obv(data)
    vwap_result = vwap(data, period=20)
    logger.info(f"   OBV last value: {obv_result.iloc[-1]:.0f}")
    logger.info(f"   VWAP(20) last value: {vwap_result.iloc[-1]:.2f}")
    logger.info()


def demonstrate_indicator_engine():
    """Demonstrate the indicator calculation engine"""
    logger.info("=== Indicator Calculation Engine ===")
    
    data = create_sample_data()
    engine = get_engine()
    
    logger.info(f"Available indicators: {len(engine.get_available_indicators())}")
    logger.info(f"Categories: {set(engine.get_indicators_by_category('moving_averages'))}")
    logger.info()
    
    # Single indicator calculation
    logger.info("1. Single Indicator Calculation:")
    result = calculate_indicator('sma', data['close'], period=20)
    logger.info(f"   Indicator: {result.indicator_name}")
    logger.info(f"   Parameters: {result.params}")
    logger.info(f"   Calculation time: {result.calculation_time:.4f}s")
    logger.info(f"   Cache hit: {result.cache_hit}")
    logger.info()
    
    # Multiple indicators
    logger.info("2. Multiple Indicators:")
    indicators = [
        ('sma', {'period': 10}),
        ('ema', {'period': 10}),
        ('rsi', {'period': 14})
    ]
    
    results = engine.calculate_multiple(indicators, data['close'])
    for name, result in results.items():
        logger.info(f"   {name}: last value = {result.data.iloc[-1]:.2f}")
    logger.info()
    
    # Caching demonstration
    logger.info("3. Caching Performance:")
    import time
    
    # First calculation (no cache)
    start = time.time()
    result1 = calculate_indicator('bollinger_bands', data['close'], period=20)
    time1 = time.time() - start
    
    # Second calculation (cached)
    start = time.time()
    result2 = calculate_indicator('bollinger_bands', data['close'], period=20)
    time2 = time.time() - start
    
    logger.info(f"   First calculation: {time1:.4f}s (cache hit: {result1.cache_hit})")
    logger.info(f"   Second calculation: {time2:.4f}s (cache hit: {result2.cache_hit})")
    logger.info(f"   Speed improvement: {time1/time2:.1f}x")
    logger.info()


def demonstrate_custom_indicators():
    """Demonstrate custom indicator framework"""
    logger.info("=== Custom Indicator Framework ===")
    
    data = create_sample_data()
    manager = CustomIndicatorManager(get_engine())
    
    # 1. Formula-based indicator
    logger.info("1. Formula-based Indicator:")
    
    metadata = IndicatorMetadata(
        name="price_momentum",
        description="Price momentum indicator",
        category="custom",
        author="Demo User",
        tags=["momentum", "custom"]
    )
    
    # Create a simple momentum indicator: (close - close[n]) / close[n] * 100
    formula_indicator = manager.create_formula_indicator(
        "price_momentum",
        "(close - close.shift(period)) / close.shift(period) * 100",
        metadata
    )
    
    # Calculate using the engine
    momentum_result = calculate_indicator('price_momentum', data['close'], period=10)
    logger.info(f"   Price Momentum (10-day): {momentum_result.data.iloc[-1]:.2f}%")
    logger.info()
    
    # 2. Composite indicator
    logger.info("2. Composite Indicator:")
    
    def trend_strength(components, **params):
        """Combine SMA and EMA to create trend strength indicator"""
        sma_data = components['sma']
        ema_data = components['ema']
        
        # When EMA > SMA, trend is strong (positive)
        # When EMA < SMA, trend is weak (negative)
        strength = (ema_data - sma_data) / sma_data * 100
        return strength
    
    components = [
        ('sma', {'period': 20}),
        ('ema', {'period': 20})
    ]
    
    composite_indicator = manager.create_composite_indicator(
        "trend_strength",
        components,
        trend_strength,
        IndicatorMetadata(
            name="trend_strength",
            description="Trend strength based on SMA/EMA divergence",
            category="trend"
        )
    )
    
    trend_result = calculate_indicator('trend_strength', data['close'])
    logger.info(f"   Trend Strength: {trend_result.data.iloc[-1]:.2f}%")
    logger.info()
    
    # 3. Custom indicator testing
    logger.info("3. Custom Indicator Testing:")
    
    test_framework = IndicatorTestFramework()
    
    # Create test data
    test_data = pd.Series([100, 102, 104, 106, 108])
    expected_momentum = (test_data - test_data.shift(2)) / test_data.shift(2) * 100
    
    # Add test case
    test_framework.add_test_case(
        "momentum_test",
        formula_indicator,
        test_data,
        expected_momentum,
        {"period": 2},
        tolerance=1e-10
    )
    
    # Run tests
    test_results = test_framework.run_tests()
    logger.info(f"   Test results: {test_results}")
    logger.info()
    
    # 4. Documentation generation
    logger.info("4. Documentation Generation:")
    doc = formula_indicator.generate_documentation()
    logger.info("   Generated documentation preview:")
    logger.info("   " + "\n   ".join(doc.split('\n')[:10]))
    logger.info("   ...")
    logger.info()


def demonstrate_performance_comparison():
    """Demonstrate performance comparison between different approaches"""
    logger.info("=== Performance Comparison ===")
    
    # Create larger dataset for performance testing
    np.random.seed(42)
    large_data = pd.Series(np.random.randn(10000).cumsum() + 100)
    
    import time
    
    # Direct function call
    start = time.time()
    direct_result = sma(large_data, 50)
    direct_time = time.time() - start
    
    # Engine call (first time - no cache)
    engine = get_engine()
    engine.clear_cache()  # Clear cache for fair comparison
    
    start = time.time()
    engine_result = calculate_indicator('sma', large_data, period=50)
    engine_time = time.time() - start
    
    # Engine call (second time - cached)
    start = time.time()
    cached_result = calculate_indicator('sma', large_data, period=50)
    cached_time = time.time() - start
    
    logger.info(f"Dataset size: {len(large_data)} points")
    logger.info(f"Direct function call: {direct_time:.4f}s")
    logger.info(f"Engine call (no cache): {engine_time:.4f}s")
    logger.info(f"Engine call (cached): {cached_time:.4f}s")
    logger.info(f"Cache speedup: {engine_time/cached_time:.1f}x")
    logger.info()
    
    # Verify results are identical
    results_match = np.allclose(direct_result.dropna(), engine_result.data.dropna())
    logger.info(f"Results identical: {results_match}")
    logger.info()


def main():
    """Main demonstration function"""
    logger.info("Technical Indicators Library Demonstration")
    logger.info("=" * 50)
    logger.info()
    
    try:
        demonstrate_basic_indicators()
        demonstrate_indicator_engine()
        demonstrate_custom_indicators()
        demonstrate_performance_comparison()
        
        logger.info("=== Summary ===")
        logger.info("✅ Basic indicators working correctly")
        logger.info("✅ Indicator engine with caching functional")
        logger.info("✅ Custom indicator framework operational")
        logger.info("✅ Performance optimization effective")
        logger.info()
        logger.info("The technical indicators library is ready for use!")
        
    except Exception as e:
        logger.info(f"❌ Error during demonstration: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()