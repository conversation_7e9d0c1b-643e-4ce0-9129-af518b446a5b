# 项目文件统计报告

**生成时间**: 2025-08-11 16:25:49
**项目路径**: /Users/<USER>/PycharmProjects/PythonProject

## 📊 总体统计

- **总文件数**: 1,176
- **总大小**: 26.7MB

## 📁 按目录统计

| 目录 | 文件数 | 大小 | 主要内容 |
|------|--------|------|----------|
| src | 246 | 3.5MB | 核心源代码 |
| tests | 245 | 2.4MB | 测试文件 |
| web_ui_backend | 117 | 13.2MB | 后端API |
| web_ui_frontend | 363 | 5.0MB | 前端界面 |
| docs | 33 | 345.0KB | 项目文档 |
| config | 40 | 955.2KB | 配置文件 |
| scripts | 93 | 1.1MB | 工具脚本 |
| tools | 39 | 264.5KB | 开发工具 |

## 📋 按文件类型统计

| 文件类型 | 数量 | 大小 | 说明 |
|----------|------|------|------|
| Python源码文件 | 362 | 4.7MB | python_source |
| 文档文件 | 112 | 737.9KB | documentation |
| 前端配置文件 | 32 | 510.0KB | frontend_config |
| Python测试文件 | 236 | 2.6MB | python_test |
| 配置文件 | 2 | 2.1KB | config_files |
| 其他文件 | 42 | 13.1MB | other |
| 前端源码文件 | 340 | 4.5MB | frontend_source |
| 前端样式文件 | 6 | 31.3KB | frontend_style |
| 脚本文件 | 43 | 613.9KB | script_files |
| 数据文件 | 1 | 7.6KB | data_files |

## 📈 项目规模评估

根据统计结果，该项目包含 1,176 个文件，
总大小为 26.7MB（不包含开发依赖）。

**项目规模**: 中到大型项目
**复杂度**: 高（包含前后端、测试、文档等完整体系）
**维护性**: 良好（有完整的测试和文档体系）

---

*注意：统计结果已排除 venv、node_modules、__pycache__ 等开发依赖目录*