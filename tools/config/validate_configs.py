import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
配置文件验证工具

验证项目中所有配置文件的正确性、一致性和完整性，
确保配置符合规范并能正常加载。

功能特性：
- 验证YAML/JSON配置文件语法
- 检查配置字段完整性
- 验证配置值的有效性
- 检查配置间的一致性
- 验证环境特定配置
- 生成验证报告

作者: 量化交易系统开发团队
版本: 1.0.0
创建日期: 2025-01-15
"""

import os
import sys
import json
import yaml
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import re
import importlib.util


@dataclass
class ValidationIssue:
    """验证问题数据类"""
    level: str  # 'error', 'warning', 'info'
    category: str  # 'syntax', 'schema', 'consistency', 'security'
    message: str
    file_path: str
    line_number: Optional[int] = None
    suggestions: List[str] = None


@dataclass
class ConfigValidationReport:
    """配置验证报告"""
    timestamp: str
    total_files: int
    valid_files: int
    issues: List[ValidationIssue]
    summary: Dict[str, int]
    recommendations: List[str]


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.config_dir = project_root / "config"
        self.issues: List[ValidationIssue] = []
        
        # 配置文件模式
        self.config_patterns = {
            "yaml": ["*.yaml", "*.yml"],
            "json": ["*.json"],
            "env": ["*.env", ".env*"],
            "ini": ["*.ini", "*.cfg"],
            "toml": ["*.toml"]
        }
        
        # 必需的配置字段模式
        self.required_fields = {
            "database": ["host", "port", "name"],
            "api": ["base_url", "timeout"],
            "logging": ["level", "format"],
            "security": ["secret_key"],
            "trading": ["initial_capital", "risk_limit"]
        }
        
        # 敏感信息模式
        self.sensitive_patterns = [
            r"password",
            r"secret",
            r"key",
            r"token",
            r"credential",
            r"auth"
        ]
    
    def validate_all(self) -> ConfigValidationReport:
        """验证所有配置文件"""
        logger.info("🔍 开始配置文件验证...")
        
        config_files = self._find_config_files()
        logger.info(f"📁 发现 {len(config_files)} 个配置文件")
        
        valid_count = 0
        for file_path in config_files:
            logger.info(f"  验证: {file_path.relative_to(self.project_root)}")
            
            if self._validate_file(file_path):
                valid_count += 1
        
        self._validate_consistency()
        
        return self._generate_report(len(config_files), valid_count)
    
    def _find_config_files(self) -> List[Path]:
        """查找所有配置文件"""
        config_files = []
        
        # 在config目录中查找
        if self.config_dir.exists():
            for file_type, patterns in self.config_patterns.items():
                for pattern in patterns:
                    config_files.extend(self.config_dir.rglob(pattern))
        
        # 在项目根目录查找特定配置文件
        root_configs = [
            "pyproject.toml",
            ".env",
            "docker-compose.yml",
            "package.json"
        ]
        
        for config_name in root_configs:
            config_path = self.project_root / config_name
            if config_path.exists():
                config_files.append(config_path)
        
        return list(set(config_files))  # 去重
    
    def _validate_file(self, file_path: Path) -> bool:
        """验证单个配置文件"""
        try:
            # 语法验证
            if not self._validate_syntax(file_path):
                return False
            
            # 内容验证
            config_data = self._load_config(file_path)
            if config_data is None:
                return False
            
            # 模式验证
            self._validate_schema(file_path, config_data)
            
            # 安全验证
            self._validate_security(file_path, config_data)
            
            return True
            
        except Exception as e:
            self.issues.append(ValidationIssue(
                level="error",
                category="syntax",
                message=f"验证失败: {e}",
                file_path=str(file_path),
                suggestions=["检查文件格式和权限"]
            ))
            return False
    
    def _validate_syntax(self, file_path: Path) -> bool:
        """验证文件语法"""
        file_ext = file_path.suffix.lower()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if file_ext in ['.yaml', '.yml']:
                yaml.safe_load(content)
            elif file_ext == '.json':
                json.loads(content)
            elif file_ext == '.toml':
                try:
                    import tomli
                    tomli.loads(content)
                except ImportError:
                    # 简单的TOML语法检查
                    if '[' in content and ']' in content:
                        pass  # 基本格式检查
            elif file_ext in ['.env']:
                # 检查环境变量格式
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '=' not in line:
                            self.issues.append(ValidationIssue(
                                level="error",
                                category="syntax",
                                message="环境变量格式错误",
                                file_path=str(file_path),
                                line_number=i,
                                suggestions=["使用 KEY=VALUE 格式"]
                            ))
                            return False
            
            return True
            
        except yaml.YAMLError as e:
            self.issues.append(ValidationIssue(
                level="error",
                category="syntax",
                message=f"YAML语法错误: {e}",
                file_path=str(file_path),
                line_number=getattr(e, 'problem_mark', {}).get('line'),
                suggestions=["检查YAML语法，注意缩进和特殊字符"]
            ))
            return False
            
        except json.JSONDecodeError as e:
            self.issues.append(ValidationIssue(
                level="error",
                category="syntax",
                message=f"JSON语法错误: {e}",
                file_path=str(file_path),
                line_number=e.lineno,
                suggestions=["检查JSON语法，注意逗号和引号"]
            ))
            return False
            
        except UnicodeDecodeError as e:
            self.issues.append(ValidationIssue(
                level="error",
                category="syntax",
                message=f"文件编码错误: {e}",
                file_path=str(file_path),
                suggestions=["确保文件使用UTF-8编码"]
            ))
            return False
    
    def _load_config(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """加载配置文件内容"""
        file_ext = file_path.suffix.lower()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if file_ext in ['.yaml', '.yml']:
                return yaml.safe_load(content)
            elif file_ext == '.json':
                return json.loads(content)
            elif file_ext in ['.env']:
                # 解析环境变量
                config = {}
                for line in content.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        config[key.strip()] = value.strip()
                return config
            elif file_ext == '.toml':
                try:
                    import tomli
                    return tomli.loads(content)
                except ImportError:
                    return {}  # 跳过TOML文件如果没有解析器
            
            return {}
            
        except Exception as e:
            self.issues.append(ValidationIssue(
                level="error",
                category="syntax",
                message=f"无法加载配置: {e}",
                file_path=str(file_path),
                suggestions=["检查文件格式和内容"]
            ))
            return None
    
    def _validate_schema(self, file_path: Path, config_data: Dict[str, Any]):
        """验证配置模式"""
        file_name = file_path.stem.lower()
        
        # 检查必需字段
        for config_type, required_fields in self.required_fields.items():
            if config_type in file_name or config_type in str(file_path).lower():
                missing_fields = []
                
                for field in required_fields:
                    if not self._has_nested_field(config_data, field):
                        missing_fields.append(field)
                
                if missing_fields:
                    self.issues.append(ValidationIssue(
                        level="warning",
                        category="schema",
                        message=f"缺少必需的配置字段: {', '.join(missing_fields)}",
                        file_path=str(file_path),
                        suggestions=[f"添加字段: {field}" for field in missing_fields]
                    ))
        
        # 检查配置值的有效性
        self._validate_values(file_path, config_data)
    
    def _has_nested_field(self, data: Dict[str, Any], field: str) -> bool:
        """检查嵌套字段是否存在"""
        parts = field.split('.')
        current = data
        
        for part in parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return False
        
        return True
    
    def _validate_values(self, file_path: Path, config_data: Dict[str, Any]):
        """验证配置值"""
        def validate_recursive(data, prefix=""):
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{prefix}.{key}" if prefix else key
                    
                    # 检查端口号
                    if "port" in key.lower() and isinstance(value, int):
                        if not (1 <= value <= 65535):
                            self.issues.append(ValidationIssue(
                                level="error",
                                category="schema",
                                message=f"无效的端口号: {value}",
                                file_path=str(file_path),
                                suggestions=["端口号应在1-65535范围内"]
                            ))
                    
                    # 检查URL格式
                    if "url" in key.lower() and isinstance(value, str):
                        if not (value.startswith("http://") or value.startswith("https://")):
                            self.issues.append(ValidationIssue(
                                level="warning",
                                category="schema",
                                message=f"可能无效的URL格式: {value}",
                                file_path=str(file_path),
                                suggestions=["确保URL以http://或https://开头"]
                            ))
                    
                    # 检查时间间隔
                    if "timeout" in key.lower() and isinstance(value, (int, float)):
                        if value <= 0:
                            self.issues.append(ValidationIssue(
                                level="warning",
                                category="schema",
                                message=f"超时值应为正数: {value}",
                                file_path=str(file_path),
                                suggestions=["设置合理的超时时间"]
                            ))
                    
                    validate_recursive(value, current_path)
        
        validate_recursive(config_data)
    
    def _validate_security(self, file_path: Path, config_data: Dict[str, Any]):
        """验证安全配置"""
        def check_security_recursive(data, prefix=""):
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{prefix}.{key}" if prefix else key
                    
                    # 检查敏感信息
                    for pattern in self.sensitive_patterns:
                        if re.search(pattern, key.lower()) and isinstance(value, str):
                            # 检查是否为明文敏感信息
                            if len(value) < 10 or value in ["password", "secret", "key"]:
                                self.issues.append(ValidationIssue(
                                    level="warning",
                                    category="security",
                                    message=f"可能的弱密码或明文敏感信息: {key}",
                                    file_path=str(file_path),
                                    suggestions=["使用强密码并考虑使用环境变量"]
                                ))
                            
                            # 检查是否应该使用环境变量
                            if not file_path.name.startswith('.env'):
                                self.issues.append(ValidationIssue(
                                    level="info",
                                    category="security",
                                    message=f"建议将敏感信息移到环境变量: {key}",
                                    file_path=str(file_path),
                                    suggestions=["使用环境变量存储敏感信息"]
                                ))
                    
                    check_security_recursive(value, current_path)
        
        check_security_recursive(config_data)
    
    def _validate_consistency(self):
        """验证配置间一致性"""
        logger.info("  验证配置一致性...")
        
        # 这里可以添加更复杂的一致性检查
        # 例如：数据库配置在不同文件中的一致性等
        pass
    
    def _generate_report(self, total_files: int, valid_files: int) -> ConfigValidationReport:
        """生成验证报告"""
        summary = {
            "total": len(self.issues),
            "errors": len([i for i in self.issues if i.level == "error"]),
            "warnings": len([i for i in self.issues if i.level == "warning"]),
            "info": len([i for i in self.issues if i.level == "info"])
        }
        
        recommendations = [
            "使用版本控制管理配置文件",
            "定期验证配置文件",
            "使用环境变量存储敏感信息",
            "保持配置文件的一致性"
        ]
        
        if summary["errors"] > 0:
            recommendations.insert(0, "优先修复错误级别的配置问题")
        
        return ConfigValidationReport(
            timestamp=datetime.now().isoformat(),
            total_files=total_files,
            valid_files=valid_files,
            issues=self.issues,
            summary=summary,
            recommendations=recommendations
        )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="配置文件验证工具")
    parser.add_argument("--project-root", type=Path,
                       help="项目根目录路径")
    parser.add_argument("--config-dir", type=Path,
                       help="配置文件目录路径")
    parser.add_argument("--output", "-o", type=Path,
                       help="输出报告文件路径")
    parser.add_argument("--format", choices=["json", "text"], default="text",
                       help="输出格式")
    parser.add_argument("--strict", action="store_true",
                       help="严格模式，将警告视为错误")
    
    args = parser.parse_args()
    
    # 确定项目根目录
    if args.project_root:
        project_root = args.project_root
    else:
        # 从当前脚本位置推导
        project_root = Path(__file__).parent.parent.parent
    
    if not project_root.exists():
        logger.info(f"❌ 项目根目录不存在: {project_root}")
        sys.exit(1)
    
    # 执行验证
    validator = ConfigValidator(project_root)
    if args.config_dir:
        validator.config_dir = args.config_dir
    
    report = validator.validate_all()
    
    # 输出报告
    if args.format == "json":
        output_content = json.dumps(asdict(report), indent=2, ensure_ascii=False)
    else:
        output_content = format_text_report(report)
    
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(output_content)
        logger.info(f"📝 报告已保存到: {args.output}")
    else:
        logger.info(output_content)
    
    # 退出码
    if report.summary["errors"] > 0:
        sys.exit(1)
    elif args.strict and report.summary["warnings"] > 0:
        sys.exit(2)
    else:
        sys.exit(0)


def format_text_report(report: ConfigValidationReport) -> str:
    """格式化文本报告"""
    lines = [
        "=" * 60,
        "⚙️  配置文件验证报告",
        "=" * 60,
        f"验证时间: {report.timestamp}",
        f"总文件数: {report.total_files}",
        f"有效文件: {report.valid_files}",
        "",
        "📊 问题统计:",
        f"  总问题数: {report.summary['total']}",
        f"  错误: {report.summary['errors']}",
        f"  警告: {report.summary['warnings']}",
        f"  信息: {report.summary['info']}",
        ""
    ]
    
    if report.issues:
        lines.append("🔍 发现的问题:")
        lines.append("")
        
        for issue in report.issues:
            icon = "❌" if issue.level == "error" else "⚠️" if issue.level == "warning" else "ℹ️"
            lines.append(f"{icon} [{issue.category.upper()}] {issue.message}")
            lines.append(f"   文件: {issue.file_path}")
            if issue.line_number:
                lines.append(f"   行号: {issue.line_number}")
            if issue.suggestions:
                lines.append(f"   建议: {'; '.join(issue.suggestions)}")
            lines.append("")
    
    if report.recommendations:
        lines.append("💡 建议:")
        for rec in report.recommendations:
            lines.append(f"  • {rec}")
        lines.append("")
    
    lines.append("=" * 60)
    
    return "\n".join(lines)


if __name__ == "__main__":
    main()
