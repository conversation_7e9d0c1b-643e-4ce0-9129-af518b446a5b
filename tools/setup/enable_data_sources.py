import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
数据源启用工具
帮助用户快速配置和启用所有数据源
"""

import os
import yaml
from pathlib import Path

def main():
    logger.info("🔧 数据源配置工具")
    logger.info("=" * 40)
    
    # 检查当前配置
    config_path = Path("config/data_sources.yaml")
    if not config_path.exists():
        logger.info("❌ 配置文件不存在")
        return
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    logger.info("📊 当前数据源状态:")
    for name, settings in config.items():
        status = "✅ 启用" if settings.get('enabled', False) else "🔴 禁用"
        logger.info(f"  - {name}: {status}")
    
    logger.info("\n🔧 配置指南:")
    
    # FRED配置
    if not config.get('fred', {}).get('enabled', False):
        logger.info("\n🏦 FRED (经济数据) - 当前禁用")
        logger.info("  1. 访问 https://fred.stlouisfed.org/docs/api/api_key.html")
        logger.info("  2. 免费申请API Key后运行:")
        logger.info("     export FRED_API_KEY=your_key")
        logger.info("  3. 或添加到.env文件:")
        logger.info("     echo 'FRED_API_KEY=your_key' >> .env")
        
        api_key = input("\n输入FRED API Key (回车跳过): ").strip()
        if api_key:
            config['fred']['enabled'] = True
            config['fred']['credentials']['api_key'] = api_key
            logger.info("✅ FRED已启用")
    
    # 保存配置
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    logger.info("\n📊 更新后的数据源状态:")
    for name, settings in config.items():
        status = "✅ 启用" if settings.get('enabled', False) else "🔴 禁用"
        logger.info(f"  - {name}: {status}")
    
    logger.info("\n💡 提示:")
    logger.info("  - Yahoo Finance: 免费，无需配置")
    logger.info("  - Binance: 免费，无需配置")
    logger.info("  - Akshare: 免费，无需配置")  # Updated from Tushare to Akshare
    logger.info("  - FRED: 需要API Key，免费服务")
    
    logger.info("\n🎉 配置完成！")

if __name__ == "__main__":
    main()