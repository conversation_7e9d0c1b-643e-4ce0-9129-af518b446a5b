import logging
logger = logging.getLogger(__name__)
"""
兼容性策略包 - 已移动到tools/compat/

这个包提供了从tools.compat.strategies到src.strategies的兼容性转发
"""

import sys
from pathlib import Path

# 确保src目录在Python路径中
project_root = Path(__file__).resolve().parent.parent.parent
src_dir = project_root / "src"

if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

# 直接转发到src.strategies
try:
    from src.market.strategies import *  # type: ignore[F401,F403]
except ImportError as e:
    logger.info(f"Warning: Could not import from src.strategies: {e}")

# 为了向后兼容，也注册到strategies命名空间
if "strategies" not in sys.modules:
    try:
        import src.market.strategies
        sys.modules["strategies"] = src.strategies
    except ImportError:
        pass