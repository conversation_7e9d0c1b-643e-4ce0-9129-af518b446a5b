#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web UI 文件夹整理工具

功能:
- 清理不必要的文件和目录
- 重新组织文件结构
- 创建规范的目录结构
- 生成整理报告
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Set
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebUIOrganizer:
    def __init__(self, web_ui_path: str):
        self.web_ui_path = Path(web_ui_path)
        self.report = {
            'start_time': datetime.now().isoformat(),
            'cleaned_files': [],
            'moved_files': [],
            'created_dirs': [],
            'errors': [],
            'stats': {
                'files_cleaned': 0,
                'files_moved': 0,
                'dirs_created': 0,
                'space_saved_mb': 0
            }
        }
        
        # 定义要清理的文件和目录模式
        self.cleanup_patterns = {
            'cache_dirs': ['__pycache__', '.pytest_cache', 'node_modules/.cache'],
            'log_files': ['*.log', '*.log.*'],
            'temp_files': ['*.tmp', '*.temp', '.DS_Store', 'Thumbs.db'],
            'build_artifacts': ['dist', 'build', '.next']
        }
        
        # 定义标准目录结构
        self.standard_structure = {
            'backend': {
                'app': ['api', 'core', 'models', 'services', 'middleware'],
                'config': [],
                'data': ['db'],
                'docs': [],
                'examples': [],
                'logs': ['archived'],
                'tests': ['unit', 'integration', 'e2e'],
                'scripts': []
            },
            'frontend': {
                'src': ['components', 'pages', 'hooks', 'services', 'store', 'types', 'utils'],
                'public': [],
                'docs': [],
                'tests': ['unit', 'integration', 'e2e']
            },
            'docs': [],
            'shared': {
                'types': [],
                'utils': [],
                'configs': []
            }
        }
    
    def clean_cache_files(self):
        """清理缓存文件和目录"""
        logger.info("🧹 开始清理缓存文件...")
        
        # 清理 Python 缓存
        for pycache in self.web_ui_path.rglob('__pycache__'):
            if pycache.is_dir():
                try:
                    size_before = self._get_dir_size(pycache)
                    shutil.rmtree(pycache)
                    self.report['cleaned_files'].append(str(pycache))
                    self.report['stats']['space_saved_mb'] += size_before / (1024 * 1024)
                    logger.info(f"   删除: {pycache}")
                except Exception as e:
                    self.report['errors'].append(f"删除 {pycache} 失败: {e}")
        
        # 清理 pytest 缓存
        for pytest_cache in self.web_ui_path.rglob('.pytest_cache'):
            if pytest_cache.is_dir():
                try:
                    size_before = self._get_dir_size(pytest_cache)
                    shutil.rmtree(pytest_cache)
                    self.report['cleaned_files'].append(str(pytest_cache))
                    self.report['stats']['space_saved_mb'] += size_before / (1024 * 1024)
                    logger.info(f"   删除: {pytest_cache}")
                except Exception as e:
                    self.report['errors'].append(f"删除 {pytest_cache} 失败: {e}")
    
    def archive_log_files(self):
        """归档日志文件"""
        logger.info("📁 开始归档日志文件...")
        
        backend_logs = self.web_ui_path / 'backend' / 'logs'
        if backend_logs.exists():
            archived_dir = backend_logs / 'archived'
            archived_dir.mkdir(exist_ok=True)
            
            # 归档旧的日志文件
            for log_file in backend_logs.glob('*.log'):
                if log_file.name not in ['backend.log', 'system.log']:  # 保留当前日志
                    try:
                        archive_path = archived_dir / f"{log_file.stem}_{datetime.now().strftime('%Y%m%d')}.log"
                        shutil.move(log_file, archive_path)
                        self.report['moved_files'].append(f"{log_file} -> {archive_path}")
                        logger.info(f"   归档: {log_file.name}")
                    except Exception as e:
                        self.report['errors'].append(f"归档 {log_file} 失败: {e}")
    
    def organize_directory_structure(self):
        """组织目录结构"""
        logger.info("📂 开始组织目录结构...")
        
        # 确保基本目录存在
        base_dirs = ['backend', 'frontend', 'docs', 'shared']
        for base_dir in base_dirs:
            dir_path = self.web_ui_path / base_dir
            if not dir_path.exists():
                dir_path.mkdir()
                self.report['created_dirs'].append(str(dir_path))
                logger.info(f"   创建目录: {base_dir}")
        
        # 创建标准子目录结构
        for main_dir, sub_structure in self.standard_structure.items():
            main_path = self.web_ui_path / main_dir
            if main_path.exists():
                self._create_subdirectories(main_path, sub_structure)
    
    def _create_subdirectories(self, parent_path: Path, structure: Dict):
        """递归创建子目录"""
        if isinstance(structure, dict):
            for dir_name, subdirs in structure.items():
                dir_path = parent_path / dir_name
                if not dir_path.exists():
                    dir_path.mkdir(exist_ok=True)
                    self.report['created_dirs'].append(str(dir_path))
                    logger.info(f"   创建子目录: {dir_path.relative_to(self.web_ui_path)}")
                
                # 创建README文件（如果不存在）
                readme_path = dir_path / 'README.md'
                if not readme_path.exists():
                    self._create_readme(readme_path, dir_name)
                
                # 递归创建更深层的目录
                if isinstance(subdirs, list):
                    for subdir in subdirs:
                        sub_path = dir_path / subdir
                        if not sub_path.exists():
                            sub_path.mkdir(exist_ok=True)
                            self.report['created_dirs'].append(str(sub_path))
                            logger.info(f"   创建子目录: {sub_path.relative_to(self.web_ui_path)}")
                elif isinstance(subdirs, dict):
                    self._create_subdirectories(dir_path, subdirs)
        elif isinstance(structure, list):
            for subdir in structure:
                sub_path = parent_path / subdir
                if not sub_path.exists():
                    sub_path.mkdir(exist_ok=True)
                    self.report['created_dirs'].append(str(sub_path))
                    logger.info(f"   创建子目录: {sub_path.relative_to(self.web_ui_path)}")
    
    def _create_readme(self, readme_path: Path, dir_name: str):
        """为目录创建README文件"""
        readme_content = f"""# {dir_name.title()}

此目录包含 {dir_name} 相关的文件。

## 结构说明

请参考项目文档了解详细的目录结构规范。

---
*自动生成于: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        try:
            readme_path.write_text(readme_content, encoding='utf-8')
            logger.info(f"   创建README: {readme_path.relative_to(self.web_ui_path)}")
        except Exception as e:
            self.report['errors'].append(f"创建 {readme_path} 失败: {e}")
    
    def update_main_readme(self):
        """更新主README文件"""
        logger.info("📝 更新主README文件...")
        
        readme_path = self.web_ui_path / 'README.md'
        
        # 统计信息
        backend_files = len(list((self.web_ui_path / 'backend').rglob('*.py'))) if (self.web_ui_path / 'backend').exists() else 0
        frontend_files = len(list((self.web_ui_path / 'frontend/src').rglob('*.tsx'))) + len(list((self.web_ui_path / 'frontend/src').rglob('*.ts'))) if (self.web_ui_path / 'frontend/src').exists() else 0
        
        readme_content = f"""# 🌐 Web UI 系统 (2025年更新)

> 量化交易系统的完整Web界面解决方案

**版本**: v2.1.3 | **更新日期**: {datetime.now().strftime('%Y-%m-%d')} | **架构**: 前后端分离

## 📊 项目统计

- **后端文件**: {backend_files}个Python文件
- **前端文件**: {frontend_files}个TypeScript/React文件
- **总大小**: ~1.1GB (包含依赖)
- **技术栈**: FastAPI + React + TypeScript

## 🗂️ 目录结构

```
web_ui/
├── 📋 README.md                    # 项目总览
├── 🔧 backend/                     # FastAPI后端服务
│   ├── app/                        # 应用核心代码
│   │   ├── api/                    # API端点
│   │   ├── core/                   # 核心配置
│   │   ├── models/                 # 数据模型
│   │   ├── services/               # 业务服务
│   │   └── middleware/             # 中间件
│   ├── config/                     # 配置文件
│   ├── data/                       # 数据存储
│   ├── docs/                       # 后端文档
│   ├── logs/                       # 日志文件
│   ├── tests/                      # 测试代码
│   └── main.py                     # 应用入口
├── 🎨 frontend/                    # React前端应用
│   ├── src/                        # 源代码
│   │   ├── components/             # 可复用组件
│   │   ├── pages/                  # 页面组件
│   │   ├── hooks/                  # 自定义Hook
│   │   ├── services/               # API服务
│   │   ├── store/                  # 状态管理
│   │   ├── types/                  # TypeScript类型
│   │   └── utils/                  # 工具函数
│   ├── public/                     # 静态资源
│   └── package.json                # 项目配置
├── 📚 docs/                        # Web界面文档
├── 🔗 shared/                      # 前后端共享代码
│   ├── types/                      # 共享类型定义
│   ├── utils/                      # 共享工具函数
│   └── configs/                    # 共享配置
```

## 🚀 快速开始

### 后端启动
```bash
cd backend/
python -m pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 前端启动
```bash
cd frontend/
npm install
npm run dev
```

## 🌐 访问地址

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **WebSocket**: ws://localhost:8000/ws

## 🎯 主要功能

### 后端功能
- **API服务**: RESTful API和WebSocket实时数据
- **数据管理**: 市场数据获取和存储
- **策略管理**: 交易策略配置和执行
- **风险控制**: 实时风险监控和告警
- **性能优化**: 缓存和异步处理

### 前端功能
- **仪表板**: 实时数据展示和系统监控
- **策略管理**: 可视化策略配置和管理
- **回测分析**: 策略回测和性能分析
- **风险监控**: 实时风险指标和告警
- **数据可视化**: 图表和报表展示

## 🔧 开发指南

### 代码规范
- **后端**: 遵循PEP 8，使用FastAPI最佳实践
- **前端**: 遵循React/TypeScript规范，使用ESLint
- **测试**: 单元测试覆盖率>80%
- **文档**: 每个API都有详细文档

### 部署指南
- **开发环境**: 本地开发服务器
- **测试环境**: Docker容器化部署
- **生产环境**: Kubernetes集群部署
- **监控**: 集成日志和性能监控

## 📈 性能优化

- **前端**: 组件懒加载、虚拟滚动、缓存策略
- **后端**: 异步处理、数据库优化、缓存机制
- **网络**: CDN加速、WebSocket长连接
- **监控**: 实时性能监控和告警

---

📝 *最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*  
📝 *维护者: 开发团队*  
📝 *整理工具: web_ui_organizer.py*
"""
        
        try:
            readme_path.write_text(readme_content, encoding='utf-8')
            logger.info("   更新完成: README.md")
        except Exception as e:
            self.report['errors'].append(f"更新 README.md 失败: {e}")
    
    def generate_report(self):
        """生成整理报告"""
        self.report['end_time'] = datetime.now().isoformat()
        self.report['stats']['files_cleaned'] = len(self.report['cleaned_files'])
        self.report['stats']['files_moved'] = len(self.report['moved_files'])
        self.report['stats']['dirs_created'] = len(self.report['created_dirs'])
        
        report_path = self.web_ui_path / f"organization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(self.report, f, indent=2, ensure_ascii=False)
            logger.info(f"📊 报告已生成: {report_path}")
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
    
    def _get_dir_size(self, path: Path) -> int:
        """计算目录大小（字节）"""
        total_size = 0
        try:
            for file_path in path.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception:
            pass
        return total_size
    
    def run_organization(self):
        """执行完整的整理流程"""
        logger.info(f"🚀 开始整理 Web UI 目录: {self.web_ui_path}")
        
        try:
            # 1. 清理缓存文件
            self.clean_cache_files()
            
            # 2. 归档日志文件
            self.archive_log_files()
            
            # 3. 组织目录结构
            self.organize_directory_structure()
            
            # 4. 更新主README
            self.update_main_readme()
            
            # 5. 生成报告
            self.generate_report()
            
            logger.info("✅ Web UI 整理完成!")
            logger.info(f"   清理文件: {self.report['stats']['files_cleaned']} 个")
            logger.info(f"   移动文件: {self.report['stats']['files_moved']} 个")
            logger.info(f"   创建目录: {self.report['stats']['dirs_created']} 个")
            logger.info(f"   节省空间: {self.report['stats']['space_saved_mb']:.2f} MB")
            
            if self.report['errors']:
                logger.warning(f"⚠️ 遇到 {len(self.report['errors'])} 个错误，请查看报告")
            
        except Exception as e:
            logger.error(f"整理过程中发生错误: {e}")
            self.report['errors'].append(f"整理失败: {e}")


def main():
    """主函数"""
    import argparse
    from pathlib import Path

    parser = argparse.ArgumentParser(description="整理 web_ui 目录结构与产物")
    parser.add_argument("--web-ui-path", dest="web_ui_path", default=None,
                        help="web_ui 目录的路径，默认为项目根目录下的 web_ui")
    args = parser.parse_args()

    # 计算默认的项目根目录 (tools/maintenance/ -> 项目根为 parents[2])
    default_root = Path(__file__).resolve().parents[2]
    default_web_ui = default_root / "web_ui"

    web_ui_path = Path(args.web_ui_path) if args.web_ui_path else default_web_ui

    if not web_ui_path.exists():
        logger.info(f"错误: Web UI 目录不存在: {web_ui_path}")
        return

    organizer = WebUIOrganizer(str(web_ui_path))
    organizer.run_organization()


if __name__ == "__main__":
    main()