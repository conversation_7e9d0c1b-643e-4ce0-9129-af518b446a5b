#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档完整性检查工具

功能:
- 检查所有README文件的完整性
- 验证文档版本和日期一致性
- 生成文档质量报告
- 发现缺失或过期的文档
"""

import os
import re
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Set
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DocumentationChecker:
    def __init__(self, project_path: str):
        self.project_path = Path(project_path)
        self.current_date = '2025-08-10'
        self.current_version = 'v2.1.3'
        
        self.report = {
            'check_time': datetime.now().isoformat(),
            'version_inconsistencies': [],
            'date_inconsistencies': [],
            'missing_elements': [],
            'outdated_docs': [],
            'quality_issues': [],
            'statistics': {}
        }
        
        # 版本模式匹配
        self.version_patterns = [
            r'\*\*版本\*\*:\s*([vV]?\d+\.\d+\.\d+)',
            r'版本:\s*([vV]?\d+\.\d+\.\d+)',
            r'Version:\s*([vV]?\d+\.\d+\.\d+)',
            r'v(\d+\.\d+\.\d+)'
        ]
        
        # 日期模式匹配
        self.date_patterns = [
            r'\*\*更新日期\*\*:\s*(\d{4}-\d{2}-\d{2})',
            r'更新日期:\s*(\d{4}-\d{2}-\d{2})',
            r'Last updated:\s*(\d{4}-\d{2}-\d{2})',
            r'*更新:\s*(\d{4}-\d{2}-\d{2})*'
        ]
    
    def find_all_docs(self):
        """查找所有文档文件"""
        logger.info("🔍 搜索所有文档文件...")
        
        docs = {
            'readme_files': list(self.project_path.rglob('README.md')),
            'md_files': list(self.project_path.rglob('*.md')),
        }
        
        # 过滤掉node_modules等目录
        exclude_patterns = ['node_modules', '.git', '__pycache__', 'venv', '.pytest_cache']
        
        for doc_type, files in docs.items():
            filtered_files = []
            for file_path in files:
                if not any(pattern in str(file_path) for pattern in exclude_patterns):
                    filtered_files.append(file_path)
            docs[doc_type] = filtered_files
        
        logger.info(f"   找到 {len(docs['readme_files'])} 个README文件")
        logger.info(f"   找到 {len(docs['md_files'])} 个Markdown文件")
        
        return docs
    
    def check_version_consistency(self, file_path: Path, content: str):
        """检查版本一致性"""
        versions_found = []
        
        for pattern in self.version_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if not match.startswith('v') and not match.startswith('V'):
                    match = 'v' + match
                versions_found.append(match)
        
        # 检查是否有不一致的版本
        unique_versions = set(versions_found)
        if len(unique_versions) > 1:
            self.report['version_inconsistencies'].append({
                'file': str(file_path.relative_to(self.project_path)),
                'versions_found': list(unique_versions),
                'expected': self.current_version
            })
        elif unique_versions and self.current_version not in unique_versions:
            self.report['version_inconsistencies'].append({
                'file': str(file_path.relative_to(self.project_path)),
                'versions_found': list(unique_versions),
                'expected': self.current_version
            })
    
    def check_date_consistency(self, file_path: Path, content: str):
        """检查日期一致性"""
        dates_found = []
        
        for pattern in self.date_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            dates_found.extend(matches)
        
        # 检查是否有过期的日期
        unique_dates = set(dates_found)
        if unique_dates and self.current_date not in unique_dates:
            self.report['date_inconsistencies'].append({
                'file': str(file_path.relative_to(self.project_path)),
                'dates_found': list(unique_dates),
                'expected': self.current_date
            })
    
    def check_required_elements(self, file_path: Path, content: str):
        """检查必需元素"""
        relative_path = file_path.relative_to(self.project_path)
        missing_elements = []
        
        # 检查主要README文件的必需元素
        if relative_path.name == 'README.md' and len(relative_path.parts) <= 2:
            required_elements = [
                ('项目描述', [r'#.*?\n', r'>.*?']),
                ('版本信息', [r'\*\*版本\*\*', r'版本:']),
                ('更新日期', [r'\*\*更新日期\*\*', r'更新日期:']),
                ('目录结构', [r'目录结构', r'## 🗂️', r'```']),
            ]
            
            for element_name, patterns in required_elements:
                found = any(re.search(pattern, content, re.IGNORECASE) for pattern in patterns)
                if not found:
                    missing_elements.append(element_name)
        
        if missing_elements:
            self.report['missing_elements'].append({
                'file': str(relative_path),
                'missing': missing_elements
            })
    
    def check_quality_issues(self, file_path: Path, content: str):
        """检查文档质量问题"""
        relative_path = file_path.relative_to(self.project_path)
        issues = []
        
        # 检查文档长度
        if len(content) < 100:
            issues.append("文档内容过短")
        
        # 检查是否有TODO或FIXME
        if re.search(r'\b(TODO|FIXME|XXX)\b', content, re.IGNORECASE):
            issues.append("包含待办事项标记")
        
        # 检查是否有断开的链接格式
        broken_links = re.findall(r'\]\([^)]*\)', content)
        for link in broken_links:
            if ')' in link and not link.strip('](').strip():
                issues.append("可能存在断开的链接")
                break
        
        # 检查是否有重复的标题
        headings = re.findall(r'^#+\s*(.+)$', content, re.MULTILINE)
        if len(headings) != len(set(headings)):
            issues.append("存在重复的标题")
        
        if issues:
            self.report['quality_issues'].append({
                'file': str(relative_path),
                'issues': issues
            })
    
    def check_single_document(self, file_path: Path):
        """检查单个文档"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # 各种检查
            self.check_version_consistency(file_path, content)
            self.check_date_consistency(file_path, content)
            self.check_required_elements(file_path, content)
            self.check_quality_issues(file_path, content)
            
        except Exception as e:
            logger.error(f"检查文档 {file_path} 时出错: {e}")
            self.report['quality_issues'].append({
                'file': str(file_path.relative_to(self.project_path)),
                'issues': [f"读取文件出错: {e}"]
            })
    
    def generate_statistics(self, docs: Dict):
        """生成统计信息"""
        logger.info("📊 生成统计信息...")
        
        self.report['statistics'] = {
            'total_readme_files': len(docs['readme_files']),
            'total_md_files': len(docs['md_files']),
            'version_inconsistencies': len(self.report['version_inconsistencies']),
            'date_inconsistencies': len(self.report['date_inconsistencies']),
            'missing_elements': len(self.report['missing_elements']),
            'quality_issues': len(self.report['quality_issues']),
            'overall_score': 0
        }
        
        # 计算总体质量评分
        total_files = len(docs['readme_files'])
        if total_files > 0:
            issues_count = (
                self.report['statistics']['version_inconsistencies'] +
                self.report['statistics']['date_inconsistencies'] +
                self.report['statistics']['missing_elements'] +
                self.report['statistics']['quality_issues']
            )
            self.report['statistics']['overall_score'] = max(0, 100 - (issues_count / total_files * 20))
    
    def generate_report(self):
        """生成检查报告"""
        logger.info("📋 生成检查报告...")
        
        report_path = self.project_path / f"documentation_check_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(self.report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📄 报告已生成: {report_path.name}")
            return report_path
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return None
    
    def print_summary(self):
        """打印检查摘要"""
        stats = self.report['statistics']
        
        logger.info(f"\n{'='*60}")
        logger.info(f"📊 文档质量检查报告")
        logger.info(f"{'='*60}")
        logger.info(f"📁 总文档数量: {stats['total_md_files']} 个")
        logger.info(f"📋 README文件: {stats['total_readme_files']} 个")
        logger.info(f"📈 质量评分: {stats['overall_score']:.1f}/100")
        logger.info(f"\n{'='*60}")
        logger.info(f"📊 问题统计:")
        logger.info(f"{'='*60}")
        logger.info(f"⚠️  版本不一致: {stats['version_inconsistencies']} 个")
        logger.info(f"📅 日期不一致: {stats['date_inconsistencies']} 个")
        logger.info(f"❌ 缺失要素: {stats['missing_elements']} 个")
        logger.info(f"🔍 质量问题: {stats['quality_issues']} 个")
        
        # 显示具体问题
        if self.report['version_inconsistencies']:
            logger.info(f"\n📝 版本不一致的文件:")
            for issue in self.report['version_inconsistencies'][:5]:  # 只显示前5个
                logger.info(f"   - {issue['file']}: {issue['versions_found']} (期望: {issue['expected']})")
            if len(self.report['version_inconsistencies']) > 5:
                logger.info(f"   ... 还有 {len(self.report['version_inconsistencies']) - 5} 个文件")
        
        if self.report['date_inconsistencies']:
            logger.info(f"\n📅 日期不一致的文件:")
            for issue in self.report['date_inconsistencies'][:5]:  # 只显示前5个
                logger.info(f"   - {issue['file']}: {issue['dates_found']} (期望: {issue['expected']})")
            if len(self.report['date_inconsistencies']) > 5:
                logger.info(f"   ... 还有 {len(self.report['date_inconsistencies']) - 5} 个文件")
        
        logger.info(f"\n{'='*60}")
        
        # 质量评级
        score = stats['overall_score']
        if score >= 90:
            logger.info(f"🌟 文档质量: 优秀")
        elif score >= 80:
            logger.info(f"👍 文档质量: 良好")
        elif score >= 70:
            logger.info(f"⚠️  文档质量: 一般")
        else:
            logger.info(f"❌ 文档质量: 需要改进")
        
        logger.info(f"{'='*60}\n")
    
    def run_full_check(self):
        """执行完整的文档检查"""
        logger.info("🚀 开始文档完整性检查...")
        logger.info(f"项目路径: {self.project_path}")
        logger.info(f"检查版本: {self.current_version}")
        logger.info(f"检查日期: {self.current_date}")
        
        try:
            # 1. 查找所有文档
            docs = self.find_all_docs()
            
            # 2. 检查README文件
            logger.info("📋 检查README文件...")
            for i, readme_file in enumerate(docs['readme_files']):
                if i % 50 == 0:  # 每50个文件显示一次进度
                    logger.info(f"   进度: {i+1}/{len(docs['readme_files'])}")
                self.check_single_document(readme_file)
            
            # 3. 生成统计信息
            self.generate_statistics(docs)
            
            # 4. 生成报告
            report_path = self.generate_report()
            
            # 5. 打印摘要
            self.print_summary()
            
            logger.info("✅ 文档检查完成!")
            
            return report_path
            
        except Exception as e:
            logger.error(f"文档检查过程中发生错误: {e}")
            return None


def main():
    """主函数"""
    project_path = "/Users/<USER>/PycharmProjects/PythonProject"
    
    if not os.path.exists(project_path):
        logger.info(f"错误: 项目目录不存在: {project_path}")
        return
    
    checker = DocumentationChecker(project_path)
    checker.run_full_check()


if __name__ == "__main__":
    main()