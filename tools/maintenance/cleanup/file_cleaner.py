#!/usr/bin/env python3
"""
文件清理维护工具

定期清理临时文件、过期文件、缓存文件和日志文件。
支持自定义清理规则和安全的文件删除机制。
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Set
import shutil
from datetime import datetime, timedelta
import fnmatch
import hashlib
import tempfile

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/file_cleaner.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FileCleaner:
    """文件清理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.config_file = self.project_root / "config" / "file_cleaner.json"
        self.cleanup_log = []
        self.dry_run = False
        
        # 默认清理规则
        self.default_rules = {
            "temp_files": {
                "description": "临时文件清理",
                "patterns": ["*.tmp", "*.temp", "*~", ".DS_Store", "Thumbs.db"],
                "directories": [".", "src", "web_ui", "tools", "scripts"],
                "max_age_days": 1,
                "enabled": True
            },
            "cache_files": {
                "description": "缓存文件清理",
                "patterns": ["*.cache", "*.pkl", "__pycache__"],
                "directories": ["data/cache", "src", "web_ui/frontend/node_modules/.cache"],
                "max_age_days": 7,
                "enabled": True
            },
            "log_files": {
                "description": "过期日志文件清理",
                "patterns": ["*.log", "*.log.*"],
                "directories": ["logs"],
                "max_age_days": 30,
                "max_size_mb": 100,
                "keep_recent": 10,
                "enabled": True
            },
            "backup_files": {
                "description": "过期备份文件清理",
                "patterns": ["*.backup", "*.bak", "backup_*"],
                "directories": ["data/backups"],
                "max_age_days": 90,
                "keep_recent": 5,
                "enabled": True
            },
            "build_artifacts": {
                "description": "构建产物清理",
                "patterns": ["dist", "build", "*.egg-info", "node_modules"],
                "directories": [".", "web_ui/frontend", "web_ui/backend"],
                "max_age_days": 7,
                "enabled": False
            },
            "test_artifacts": {
                "description": "测试产物清理",
                "patterns": [".pytest_cache", ".coverage", "htmlcov", "*.cover"],
                "directories": [".", "tests"],
                "max_age_days": 3,
                "enabled": True
            }
        }
    
    def load_cleanup_config(self) -> Dict:
        """加载清理配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认规则和用户配置
                rules = self.default_rules.copy()
                rules.update(config.get('rules', {}))
                config['rules'] = rules
                return config
            else:
                return {
                    "version": "1.0",
                    "description": "文件清理配置",
                    "rules": self.default_rules,
                    "global_settings": {
                        "safe_mode": True,
                        "backup_before_delete": True,
                        "max_files_per_batch": 1000,
                        "excluded_directories": [".git", ".kiro", "venv", "env"]
                    }
                }
        except Exception as e:
            logger.error(f"加载清理配置失败: {e}")
            return {"rules": self.default_rules, "global_settings": {}}
    
    def save_cleanup_config(self, config: Dict):
        """保存清理配置"""
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info(f"清理配置已保存: {self.config_file}")
        except Exception as e:
            logger.error(f"保存清理配置失败: {e}")
    
    def is_excluded_directory(self, directory: Path, excluded_dirs: List[str]) -> bool:
        """检查目录是否被排除"""
        try:
            dir_parts = directory.parts
            for excluded in excluded_dirs:
                if excluded in dir_parts:
                    return True
            return False
        except Exception:
            return False
    
    def matches_pattern(self, file_path: Path, patterns: List[str]) -> bool:
        """检查文件是否匹配模式"""
        try:
            file_name = file_path.name
            for pattern in patterns:
                if fnmatch.fnmatch(file_name, pattern):
                    return True
                # 检查目录名匹配
                if file_path.is_dir() and fnmatch.fnmatch(file_name, pattern):
                    return True
            return False
        except Exception:
            return False
    
    def is_file_old_enough(self, file_path: Path, max_age_days: int) -> bool:
        """检查文件是否足够旧"""
        try:
            if max_age_days <= 0:
                return False
            
            file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
            cutoff_time = datetime.now() - timedelta(days=max_age_days)
            return file_mtime < cutoff_time
        except Exception:
            return False
    
    def is_file_too_large(self, file_path: Path, max_size_mb: Optional[int]) -> bool:
        """检查文件是否过大"""
        try:
            if max_size_mb is None or max_size_mb <= 0:
                return False
            
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            return file_size_mb > max_size_mb
        except Exception:
            return False
    
    def get_files_to_clean(self, rule_name: str, rule_config: Dict) -> List[Path]:
        """获取需要清理的文件列表"""
        files_to_clean = []
        
        try:
            patterns = rule_config.get('patterns', [])
            directories = rule_config.get('directories', ['.'])
            max_age_days = rule_config.get('max_age_days', 0)
            max_size_mb = rule_config.get('max_size_mb')
            
            config = self.load_cleanup_config()
            excluded_dirs = config.get('global_settings', {}).get('excluded_directories', [])
            
            for directory in directories:
                dir_path = self.project_root / directory
                if not dir_path.exists():
                    continue
                
                if self.is_excluded_directory(dir_path, excluded_dirs):
                    logger.info(f"跳过排除目录: {dir_path}")
                    continue
                
                # 递归查找匹配的文件
                for root, dirs, files in os.walk(dir_path):
                    root_path = Path(root)
                    
                    # 检查是否在排除目录中
                    if self.is_excluded_directory(root_path, excluded_dirs):
                        dirs.clear()  # 不递归进入排除的目录
                        continue
                    
                    # 检查目录是否匹配模式
                    for dir_name in dirs.copy():
                        dir_full_path = root_path / dir_name
                        if self.matches_pattern(dir_full_path, patterns):
                            if max_age_days == 0 or self.is_file_old_enough(dir_full_path, max_age_days):
                                files_to_clean.append(dir_full_path)
                                dirs.remove(dir_name)  # 不递归进入匹配的目录
                    
                    # 检查文件是否匹配模式
                    for file_name in files:
                        file_path = root_path / file_name
                        if self.matches_pattern(file_path, patterns):
                            should_clean = True
                            
                            # 检查年龄限制
                            if max_age_days > 0 and not self.is_file_old_enough(file_path, max_age_days):
                                should_clean = False
                            
                            # 检查大小限制
                            if should_clean and self.is_file_too_large(file_path, max_size_mb):
                                should_clean = True
                            
                            if should_clean:
                                files_to_clean.append(file_path)
            
            # 应用保留最近文件的规则
            keep_recent = rule_config.get('keep_recent', 0)
            if keep_recent > 0 and len(files_to_clean) > keep_recent:
                # 按修改时间排序，保留最新的文件
                files_to_clean.sort(key=lambda f: f.stat().st_mtime, reverse=True)
                files_to_clean = files_to_clean[keep_recent:]
            
            logger.info(f"规则 '{rule_name}' 找到 {len(files_to_clean)} 个文件需要清理")
            return files_to_clean
            
        except Exception as e:
            logger.error(f"获取清理文件列表失败 {rule_name}: {e}")
            return []
    
    def backup_file_before_delete(self, file_path: Path) -> Optional[Path]:
        """删除前备份文件"""
        try:
            backup_dir = self.project_root / "data" / "backups" / "deleted_files"
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"{file_path.name}.deleted.{timestamp}"
            backup_path = backup_dir / backup_name
            
            if file_path.is_file():
                shutil.copy2(file_path, backup_path)
            elif file_path.is_dir():
                shutil.copytree(file_path, backup_path)
            
            return backup_path
            
        except Exception as e:
            logger.error(f"备份文件失败 {file_path}: {e}")
            return None
    
    def delete_file_safely(self, file_path: Path, backup_before_delete: bool = True) -> bool:
        """安全删除文件"""
        try:
            if not file_path.exists():
                return True
            
            backup_path = None
            if backup_before_delete:
                backup_path = self.backup_file_before_delete(file_path)
            
            if self.dry_run:
                logger.info(f"[预览] 将删除: {file_path}")
                return True
            
            if file_path.is_file():
                file_path.unlink()
            elif file_path.is_dir():
                shutil.rmtree(file_path)
            
            # 记录删除日志
            self.cleanup_log.append({
                "action": "delete",
                "file_path": str(file_path),
                "backup_path": str(backup_path) if backup_path else None,
                "file_size": file_path.stat().st_size if file_path.exists() else 0,
                "timestamp": datetime.now().isoformat()
            })
            
            logger.info(f"已删除: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"删除文件失败 {file_path}: {e}")
            return False
    
    def clean_by_rule(self, rule_name: str, rule_config: Dict) -> Dict:
        """按规则清理文件"""
        try:
            if not rule_config.get('enabled', True):
                logger.info(f"规则 '{rule_name}' 已禁用，跳过")
                return {"rule": rule_name, "files_found": 0, "files_deleted": 0, "errors": 0}
            
            logger.info(f"开始执行清理规则: {rule_name}")
            logger.info(f"规则描述: {rule_config.get('description', '无描述')}")
            
            files_to_clean = self.get_files_to_clean(rule_name, rule_config)
            
            config = self.load_cleanup_config()
            global_settings = config.get('global_settings', {})
            backup_before_delete = global_settings.get('backup_before_delete', True)
            max_files_per_batch = global_settings.get('max_files_per_batch', 1000)
            
            # 限制批次大小
            if len(files_to_clean) > max_files_per_batch:
                logger.warning(f"文件数量 ({len(files_to_clean)}) 超过批次限制 ({max_files_per_batch})，仅处理前 {max_files_per_batch} 个")
                files_to_clean = files_to_clean[:max_files_per_batch]
            
            deleted_count = 0
            error_count = 0
            total_size = 0
            
            for file_path in files_to_clean:
                try:
                    if file_path.exists():
                        file_size = file_path.stat().st_size
                        if self.delete_file_safely(file_path, backup_before_delete):
                            deleted_count += 1
                            total_size += file_size
                        else:
                            error_count += 1
                except Exception as e:
                    logger.error(f"处理文件失败 {file_path}: {e}")
                    error_count += 1
            
            result = {
                "rule": rule_name,
                "files_found": len(files_to_clean),
                "files_deleted": deleted_count,
                "errors": error_count,
                "total_size_mb": round(total_size / (1024 * 1024), 2)
            }
            
            logger.info(f"规则 '{rule_name}' 执行完成: 找到 {result['files_found']} 个文件，删除 {result['files_deleted']} 个，错误 {result['errors']} 个，释放空间 {result['total_size_mb']} MB")
            
            return result
            
        except Exception as e:
            logger.error(f"执行清理规则失败 {rule_name}: {e}")
            return {"rule": rule_name, "files_found": 0, "files_deleted": 0, "errors": 1}
    
    def clean_all(self, rules_filter: Optional[List[str]] = None) -> Dict:
        """执行所有清理规则"""
        try:
            logger.info("开始执行文件清理...")
            
            config = self.load_cleanup_config()
            rules = config.get('rules', {})
            
            if rules_filter:
                rules = {name: rule for name, rule in rules.items() if name in rules_filter}
            
            results = []
            total_files_found = 0
            total_files_deleted = 0
            total_errors = 0
            total_size_mb = 0
            
            for rule_name, rule_config in rules.items():
                result = self.clean_by_rule(rule_name, rule_config)
                results.append(result)
                
                total_files_found += result['files_found']
                total_files_deleted += result['files_deleted']
                total_errors += result['errors']
                total_size_mb += result.get('total_size_mb', 0)
            
            summary = {
                "timestamp": datetime.now().isoformat(),
                "dry_run": self.dry_run,
                "rules_executed": len(results),
                "total_files_found": total_files_found,
                "total_files_deleted": total_files_deleted,
                "total_errors": total_errors,
                "total_size_mb": round(total_size_mb, 2),
                "results": results
            }
            
            logger.info(f"文件清理完成: 执行 {summary['rules_executed']} 个规则，找到 {summary['total_files_found']} 个文件，删除 {summary['total_files_deleted']} 个，错误 {summary['total_errors']} 个，释放空间 {summary['total_size_mb']} MB")
            
            return summary
            
        except Exception as e:
            logger.error(f"执行文件清理失败: {e}")
            return {"error": str(e)}
    
    def save_cleanup_log(self):
        """保存清理日志"""
        try:
            log_dir = self.project_root / "logs" / "maintenance"
            log_dir.mkdir(parents=True, exist_ok=True)
            
            log_file = log_dir / f"file_cleanup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(self.cleanup_log, f, ensure_ascii=False, indent=2)
            
            logger.info(f"清理日志已保存: {log_file}")
            
        except Exception as e:
            logger.error(f"保存清理日志失败: {e}")
    
    def get_disk_usage_info(self) -> Dict:
        """获取磁盘使用信息"""
        try:
            total, used, free = shutil.disk_usage(self.project_root)
            
            return {
                "total_gb": round(total / (1024**3), 2),
                "used_gb": round(used / (1024**3), 2),
                "free_gb": round(free / (1024**3), 2),
                "usage_percent": round((used / total) * 100, 2)
            }
        except Exception as e:
            logger.error(f"获取磁盘使用信息失败: {e}")
            return {}

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="文件清理维护工具")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    parser.add_argument("--rules", nargs="+", help="指定要执行的清理规则")
    parser.add_argument("--dry-run", action="store_true", help="预览模式，不实际删除文件")
    parser.add_argument("--list-rules", action="store_true", help="列出所有清理规则")
    parser.add_argument("--create-config", action="store_true", help="创建默认配置文件")
    parser.add_argument("--disk-info", action="store_true", help="显示磁盘使用信息")
    
    args = parser.parse_args()
    
    try:
        cleaner = FileCleaner(args.project_root)
        cleaner.dry_run = args.dry_run
        
        if args.list_rules:
            config = cleaner.load_cleanup_config()
            rules = config.get('rules', {})
            logger.info("可用的清理规则:")
            for rule_name, rule_config in rules.items():
                status = "启用" if rule_config.get('enabled', True) else "禁用"
                logger.info(f"  - {rule_name}: {rule_config.get('description', '无描述')} ({status})")
            return
        
        if args.create_config:
            config = cleaner.load_cleanup_config()
            cleaner.save_cleanup_config(config)
            logger.info(f"默认配置文件已创建: {cleaner.config_file}")
            return
        
        if args.disk_info:
            disk_info = cleaner.get_disk_usage_info()
            logger.info("磁盘使用信息:")
            logger.info(f"  总容量: {disk_info.get('total_gb', 0)} GB")
            logger.info(f"  已使用: {disk_info.get('used_gb', 0)} GB")
            logger.info(f"  可用空间: {disk_info.get('free_gb', 0)} GB")
            logger.info(f"  使用率: {disk_info.get('usage_percent', 0)}%")
            return
        
        # 执行清理
        summary = cleaner.clean_all(args.rules)
        cleaner.save_cleanup_log()
        
        if "error" in summary:
            logger.error(f"清理失败: {summary['error']}")
            sys.exit(1)
        else:
            logger.info(f"\n清理摘要:")
            logger.info(f"  执行规则: {summary['rules_executed']}")
            logger.info(f"  找到文件: {summary['total_files_found']}")
            logger.info(f"  删除文件: {summary['total_files_deleted']}")
            logger.info(f"  错误数量: {summary['total_errors']}")
            logger.info(f"  释放空间: {summary['total_size_mb']} MB")
            
            if args.dry_run:
                logger.info("\n注意: 这是预览模式，没有实际删除文件")
        
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()