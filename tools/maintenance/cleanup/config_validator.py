#!/usr/bin/env python3
"""
配置验证维护工具

定期验证配置文件的正确性、完整性和一致性。
支持多种配置文件格式和自定义验证规则。
"""

import os
import sys
import json
import yaml
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import re
from datetime import datetime
import hashlib
import jsonschema
from configparser import ConfigParser

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/config_validation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ConfigValidator:
    """配置验证器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.config_dir = self.project_root / "config"
        self.validation_log = []
        
        # 默认验证规则
        self.default_validation_rules = {
            "yaml_files": {
                "description": "YAML配置文件验证",
                "patterns": ["*.yaml", "*.yml"],
                "directories": ["config", "config/environments"],
                "validators": ["syntax", "schema", "required_fields"],
                "enabled": True
            },
            "json_files": {
                "description": "JSON配置文件验证",
                "patterns": ["*.json"],
                "directories": ["config", ".kiro"],
                "validators": ["syntax", "schema"],
                "enabled": True
            },
            "env_files": {
                "description": "环境变量文件验证",
                "patterns": ["*.env", ".env*"],
                "directories": [".", "web_ui", "config/environments"],
                "validators": ["syntax", "required_vars"],
                "enabled": True
            },
            "python_config": {
                "description": "Python配置文件验证",
                "patterns": ["settings.py", "config.py"],
                "directories": ["config", "src"],
                "validators": ["syntax", "imports"],
                "enabled": True
            }
        }
        
        # 配置文件架构定义
        self.config_schemas = {
            "main_config": {
                "type": "object",
                "required": ["system", "database", "api"],
                "properties": {
                    "system": {
                        "type": "object",
                        "required": ["name", "version", "environment"],
                        "properties": {
                            "name": {"type": "string"},
                            "version": {"type": "string"},
                            "environment": {"type": "string", "enum": ["development", "testing", "staging", "production"]},
                            "debug": {"type": "boolean"}
                        }
                    },
                    "database": {
                        "type": "object",
                        "required": ["url"],
                        "properties": {
                            "url": {"type": "string"},
                            "echo": {"type": "boolean"},
                            "pool_size": {"type": "integer", "minimum": 1},
                            "max_overflow": {"type": "integer", "minimum": 0}
                        }
                    },
                    "api": {
                        "type": "object",
                        "required": ["host", "port"],
                        "properties": {
                            "host": {"type": "string"},
                            "port": {"type": "integer", "minimum": 1, "maximum": 65535},
                            "cors_origins": {"type": "array", "items": {"type": "string"}}
                        }
                    }
                }
            }
        }
    
    def load_validation_config(self) -> Dict:
        """加载验证配置"""
        try:
            config_file = self.config_dir / "validation_rules.json"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认规则和用户配置
                rules = self.default_validation_rules.copy()
                rules.update(config.get('rules', {}))
                config['rules'] = rules
                return config
            else:
                return {
                    "version": "1.0",
                    "description": "配置验证规则",
                    "rules": self.default_validation_rules,
                    "global_settings": {
                        "strict_mode": False,
                        "fail_on_warning": False,
                        "backup_invalid_configs": True
                    }
                }
        except Exception as e:
            logger.error(f"加载验证配置失败: {e}")
            return {"rules": self.default_validation_rules, "global_settings": {}}
    
    def save_validation_config(self, config: Dict):
        """保存验证配置"""
        try:
            config_file = self.config_dir / "validation_rules.json"
            config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info(f"验证配置已保存: {config_file}")
        except Exception as e:
            logger.error(f"保存验证配置失败: {e}")
    
    def find_config_files(self, patterns: List[str], directories: List[str]) -> List[Path]:
        """查找配置文件"""
        config_files = []
        
        try:
            for directory in directories:
                dir_path = self.project_root / directory
                if not dir_path.exists():
                    continue
                
                for pattern in patterns:
                    # 使用glob查找匹配的文件
                    for file_path in dir_path.rglob(pattern):
                        if file_path.is_file():
                            config_files.append(file_path)
            
            return list(set(config_files))  # 去重
            
        except Exception as e:
            logger.error(f"查找配置文件失败: {e}")
            return []
    
    def validate_yaml_syntax(self, file_path: Path) -> Tuple[bool, str]:
        """验证YAML文件语法"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                yaml.safe_load(f)
            return True, "YAML语法正确"
        except yaml.YAMLError as e:
            return False, f"YAML语法错误: {e}"
        except Exception as e:
            return False, f"读取文件失败: {e}"
    
    def validate_json_syntax(self, file_path: Path) -> Tuple[bool, str]:
        """验证JSON文件语法"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json.load(f)
            return True, "JSON语法正确"
        except json.JSONDecodeError as e:
            return False, f"JSON语法错误: {e}"
        except Exception as e:
            return False, f"读取文件失败: {e}"
    
    def validate_env_syntax(self, file_path: Path) -> Tuple[bool, str]:
        """验证环境变量文件语法"""
        try:
            errors = []
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '=' not in line:
                            errors.append(f"第{line_num}行: 缺少等号分隔符")
                        else:
                            key, value = line.split('=', 1)
                            if not key.strip():
                                errors.append(f"第{line_num}行: 变量名为空")
            
            if errors:
                return False, "; ".join(errors)
            else:
                return True, "环境变量文件语法正确"
                
        except Exception as e:
            return False, f"读取文件失败: {e}"
    
    def validate_python_syntax(self, file_path: Path) -> Tuple[bool, str]:
        """验证Python配置文件语法"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # 编译Python代码检查语法
            compile(source_code, str(file_path), 'exec')
            return True, "Python语法正确"
            
        except SyntaxError as e:
            return False, f"Python语法错误: 第{e.lineno}行: {e.msg}"
        except Exception as e:
            return False, f"读取文件失败: {e}"
    
    def validate_schema(self, file_path: Path, config_data: Any) -> Tuple[bool, str]:
        """验证配置文件架构"""
        try:
            # 根据文件名或路径确定使用哪个架构
            schema_name = None
            if file_path.name in ["config.yaml", "config.yml"]:
                schema_name = "main_config"
            
            if schema_name and schema_name in self.config_schemas:
                schema = self.config_schemas[schema_name]
                jsonschema.validate(config_data, schema)
                return True, f"架构验证通过 ({schema_name})"
            else:
                return True, "未找到对应架构，跳过验证"
                
        except jsonschema.ValidationError as e:
            return False, f"架构验证失败: {e.message}"
        except Exception as e:
            return False, f"架构验证错误: {e}"
    
    def validate_required_fields(self, file_path: Path, config_data: Any) -> Tuple[bool, str]:
        """验证必需字段"""
        try:
            if not isinstance(config_data, dict):
                return True, "非字典类型，跳过必需字段验证"
            
            # 根据文件类型定义必需字段
            required_fields = {}
            
            if file_path.name in ["config.yaml", "config.yml"]:
                required_fields = {
                    "system": ["name", "version", "environment"],
                    "database": ["url"],
                    "api": ["host", "port"]
                }
            
            errors = []
            for section, fields in required_fields.items():
                if section not in config_data:
                    errors.append(f"缺少必需节点: {section}")
                    continue
                
                section_data = config_data[section]
                if not isinstance(section_data, dict):
                    errors.append(f"节点 {section} 应为字典类型")
                    continue
                
                for field in fields:
                    if field not in section_data:
                        errors.append(f"节点 {section} 缺少必需字段: {field}")
            
            if errors:
                return False, "; ".join(errors)
            else:
                return True, "必需字段验证通过"
                
        except Exception as e:
            return False, f"必需字段验证错误: {e}"
    
    def validate_required_env_vars(self, file_path: Path) -> Tuple[bool, str]:
        """验证必需的环境变量"""
        try:
            # 根据文件名定义必需的环境变量
            required_vars = []
            
            # 后端env文件
            if file_path.name.endswith("backend.env") or file_path.name == ".env":
                required_vars = [
                    "ENVIRONMENT", "DATABASE_URL", "API_HOST", "API_PORT", "SECRET_KEY"
                ]
            # 前端env文件 (Vite 项目)
            elif file_path.name in {".env", ".env.local", ".env.development"} and "web_ui" in str(file_path):
                required_vars = [
                    "NODE_ENV", "VITE_API_BASE_URL"
                ]
            
            if not required_vars:
                return True, "未定义必需环境变量，跳过验证"
            
            # 读取环境变量文件
            env_vars = {}
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key.strip()] = value.strip()
            
            # 检查必需变量
            missing_vars = []
            for var in required_vars:
                if var not in env_vars:
                    missing_vars.append(var)
            
            if missing_vars:
                return False, f"缺少必需环境变量: {', '.join(missing_vars)}"
            else:
                return True, "必需环境变量验证通过"
                
        except Exception as e:
            return False, f"环境变量验证错误: {e}"
    
    def validate_python_imports(self, file_path: Path) -> Tuple[bool, str]:
        """验证Python配置文件的导入"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # 查找import语句
            import_lines = []
            for line_num, line in enumerate(source_code.split('\n'), 1):
                line = line.strip()
                if line.startswith('import ') or line.startswith('from '):
                    import_lines.append((line_num, line))
            
            # 尝试执行导入检查
            errors = []
            for line_num, import_line in import_lines:
                try:
                    exec(import_line)
                except ImportError as e:
                    errors.append(f"第{line_num}行导入失败: {e}")
                except Exception as e:
                    errors.append(f"第{line_num}行导入错误: {e}")
            
            if errors:
                return False, "; ".join(errors)
            else:
                return True, "Python导入验证通过"
                
        except Exception as e:
            return False, f"Python导入验证错误: {e}"
    
    def validate_single_file(self, file_path: Path, validators: List[str]) -> Dict:
        """验证单个配置文件"""
        try:
            logger.info(f"验证配置文件: {file_path}")
            
            validation_result = {
                "file_path": str(file_path),
                "file_size": file_path.stat().st_size,
                "last_modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                "validators": [],
                "passed": True,
                "errors": [],
                "warnings": []
            }
            
            # 读取文件内容（用于架构验证）
            config_data = None
            if file_path.suffix in ['.yaml', '.yml']:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config_data = yaml.safe_load(f)
                except:
                    pass
            elif file_path.suffix == '.json':
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                except:
                    pass
            
            # 执行各种验证器
            for validator in validators:
                validator_result = {"name": validator, "passed": True, "message": ""}
                
                if validator == "syntax":
                    if file_path.suffix in ['.yaml', '.yml']:
                        passed, message = self.validate_yaml_syntax(file_path)
                    elif file_path.suffix == '.json':
                        passed, message = self.validate_json_syntax(file_path)
                    elif file_path.suffix == '.env' or file_path.name.startswith('.env'):
                        passed, message = self.validate_env_syntax(file_path)
                    elif file_path.suffix == '.py':
                        passed, message = self.validate_python_syntax(file_path)
                    else:
                        passed, message = True, "未知文件类型，跳过语法验证"
                    
                    validator_result["passed"] = passed
                    validator_result["message"] = message
                
                elif validator == "schema" and config_data is not None:
                    passed, message = self.validate_schema(file_path, config_data)
                    validator_result["passed"] = passed
                    validator_result["message"] = message
                
                elif validator == "required_fields" and config_data is not None:
                    passed, message = self.validate_required_fields(file_path, config_data)
                    validator_result["passed"] = passed
                    validator_result["message"] = message
                
                elif validator == "required_vars":
                    passed, message = self.validate_required_env_vars(file_path)
                    validator_result["passed"] = passed
                    validator_result["message"] = message
                
                elif validator == "imports":
                    passed, message = self.validate_python_imports(file_path)
                    validator_result["passed"] = passed
                    validator_result["message"] = message
                
                validation_result["validators"].append(validator_result)
                
                if not validator_result["passed"]:
                    validation_result["passed"] = False
                    validation_result["errors"].append(f"{validator}: {validator_result['message']}")
            
            # 记录验证日志
            self.validation_log.append({
                "action": "validate_file",
                "file_path": str(file_path),
                "passed": validation_result["passed"],
                "error_count": len(validation_result["errors"]),
                "timestamp": datetime.now().isoformat()
            })
            
            if validation_result["passed"]:
                logger.info(f"配置文件验证通过: {file_path}")
            else:
                logger.error(f"配置文件验证失败: {file_path}")
                for error in validation_result["errors"]:
                    logger.error(f"  - {error}")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"验证配置文件失败 {file_path}: {e}")
            return {
                "file_path": str(file_path),
                "passed": False,
                "errors": [f"验证过程异常: {e}"],
                "validators": []
            }
    
    def validate_by_rule(self, rule_name: str, rule_config: Dict) -> Dict:
        """按规则验证配置文件"""
        try:
            if not rule_config.get('enabled', True):
                logger.info(f"验证规则 '{rule_name}' 已禁用，跳过")
                return {"rule": rule_name, "files_found": 0, "files_passed": 0, "files_failed": 0}
            
            logger.info(f"开始执行验证规则: {rule_name}")
            logger.info(f"规则描述: {rule_config.get('description', '无描述')}")
            
            patterns = rule_config.get('patterns', [])
            directories = rule_config.get('directories', ['.'])
            validators = rule_config.get('validators', ['syntax'])
            
            # 查找配置文件
            config_files = self.find_config_files(patterns, directories)
            
            results = []
            passed_count = 0
            failed_count = 0
            
            for config_file in config_files:
                result = self.validate_single_file(config_file, validators)
                results.append(result)
                
                if result["passed"]:
                    passed_count += 1
                else:
                    failed_count += 1
            
            rule_result = {
                "rule": rule_name,
                "files_found": len(config_files),
                "files_passed": passed_count,
                "files_failed": failed_count,
                "results": results
            }
            
            logger.info(f"规则 '{rule_name}' 执行完成: 找到 {rule_result['files_found']} 个文件，通过 {rule_result['files_passed']} 个，失败 {rule_result['files_failed']} 个")
            
            return rule_result
            
        except Exception as e:
            logger.error(f"执行验证规则失败 {rule_name}: {e}")
            return {"rule": rule_name, "files_found": 0, "files_passed": 0, "files_failed": 1, "error": str(e)}
    
    def validate_all(self, rules_filter: Optional[List[str]] = None) -> Dict:
        """执行所有验证规则"""
        try:
            logger.info("开始执行配置文件验证...")
            
            config = self.load_validation_config()
            rules = config.get('rules', {})
            
            if rules_filter:
                rules = {name: rule for name, rule in rules.items() if name in rules_filter}
            
            results = []
            total_files_found = 0
            total_files_passed = 0
            total_files_failed = 0
            
            for rule_name, rule_config in rules.items():
                result = self.validate_by_rule(rule_name, rule_config)
                results.append(result)
                
                total_files_found += result['files_found']
                total_files_passed += result['files_passed']
                total_files_failed += result['files_failed']
            
            summary = {
                "timestamp": datetime.now().isoformat(),
                "rules_executed": len(results),
                "total_files_found": total_files_found,
                "total_files_passed": total_files_passed,
                "total_files_failed": total_files_failed,
                "success_rate": round((total_files_passed / total_files_found * 100) if total_files_found > 0 else 0, 2),
                "results": results
            }
            
            logger.info(f"配置文件验证完成: 执行 {summary['rules_executed']} 个规则，找到 {summary['total_files_found']} 个文件，通过 {summary['total_files_passed']} 个，失败 {summary['total_files_failed']} 个，成功率 {summary['success_rate']}%")
            
            return summary
            
        except Exception as e:
            logger.error(f"执行配置文件验证失败: {e}")
            return {"error": str(e)}
    
    def save_validation_log(self):
        """保存验证日志"""
        try:
            log_dir = self.project_root / "logs" / "maintenance"
            log_dir.mkdir(parents=True, exist_ok=True)
            
            log_file = log_dir / f"config_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(self.validation_log, f, ensure_ascii=False, indent=2)
            
            logger.info(f"验证日志已保存: {log_file}")
            
        except Exception as e:
            logger.error(f"保存验证日志失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="配置验证维护工具")
    parser.add_argument("--project-root", default=".", help="项目根目录路径")
    parser.add_argument("--rules", nargs="+", help="指定要执行的验证规则")
    parser.add_argument("--list-rules", action="store_true", help="列出所有验证规则")
    parser.add_argument("--create-config", action="store_true", help="创建默认验证配置文件")
    parser.add_argument("--file", help="验证单个配置文件")
    parser.add_argument("--strict", action="store_true", help="严格模式，警告也视为失败")
    
    args = parser.parse_args()
    
    try:
        validator = ConfigValidator(args.project_root)
        
        if args.list_rules:
            config = validator.load_validation_config()
            rules = config.get('rules', {})
            logger.info("可用的验证规则:")
            for rule_name, rule_config in rules.items():
                status = "启用" if rule_config.get('enabled', True) else "禁用"
                logger.info(f"  - {rule_name}: {rule_config.get('description', '无描述')} ({status})")
            return
        
        if args.create_config:
            config = validator.load_validation_config()
            validator.save_validation_config(config)
            logger.info(f"默认验证配置文件已创建: {validator.config_dir / 'validation_rules.json'}")
            return
        
        if args.file:
            file_path = Path(args.file)
            if not file_path.exists():
                logger.error(f"文件不存在: {file_path}")
                sys.exit(1)
            
            result = validator.validate_single_file(file_path, ["syntax", "schema", "required_fields"])
            if result["passed"]:
                logger.info(f"配置文件验证通过: {file_path}")
                sys.exit(0)
            else:
                logger.info(f"配置文件验证失败: {file_path}")
                for error in result["errors"]:
                    logger.info(f"  - {error}")
                sys.exit(1)
        
        # 执行验证
        summary = validator.validate_all(args.rules)
        validator.save_validation_log()
        
        if "error" in summary:
            logger.error(f"验证失败: {summary['error']}")
            sys.exit(1)
        else:
            logger.info(f"\n验证摘要:")
            logger.info(f"  执行规则: {summary['rules_executed']}")
            logger.info(f"  找到文件: {summary['total_files_found']}")
            logger.info(f"  通过文件: {summary['total_files_passed']}")
            logger.info(f"  失败文件: {summary['total_files_failed']}")
            logger.info(f"  成功率: {summary['success_rate']}%")
            
            if summary['total_files_failed'] > 0:
                if args.strict:
                    sys.exit(1)
                else:
                    logger.warning("存在验证失败的文件，请检查日志")
        
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()