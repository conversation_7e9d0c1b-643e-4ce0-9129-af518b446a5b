import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
项目文件清理工具

自动清理项目中的临时文件、缓存文件、构建产物等，
保持项目目录的整洁性和仓库的卫生状态。

功能特性：
- 清理Python缓存文件(__pycache__, *.pyc等)
- 清理Node.js构建产物(node_modules, dist等)
- 清理日志文件和临时文件
- 清理数据库运行时文件
- 智能备份重要文件
- 生成清理报告

作者: 量化交易系统开发团队
版本: 1.0.0
创建日期: 2025-01-15
"""

import os
import sys
import shutil
import argparse
from pathlib import Path
from typing import List, Dict, Set, Tuple
from dataclasses import dataclass
from datetime import datetime
import json
import fnmatch


@dataclass
class CleanupResult:
    """清理结果数据类"""
    deleted_files: List[str]
    deleted_dirs: List[str]
    backed_up_files: List[str]
    errors: List[str]
    total_size_freed: int
    cleanup_time: str


class FileCleanupManager:
    """文件清理管理器"""
    
    def __init__(self, project_root: Path, dry_run: bool = False):
        self.project_root = project_root
        self.dry_run = dry_run
        self.result = CleanupResult([], [], [], [], 0, "")
        
        # 清理规则配置
        self.cleanup_patterns = {
            "python_cache": [
                "__pycache__/",
                "*.pyc",
                "*.pyo", 
                "*.pyd",
                ".pytest_cache/",
                ".mypy_cache/",
                ".coverage",
                "htmlcov/",
                ".tox/"
            ],
            "nodejs_build": [
                "node_modules/",
                "dist/", 
                "build/",
                "*.log",
                "npm-debug.log*",
                "yarn-debug.log*",
                "yarn-error.log*"
            ],
            "logs_temp": [
                "logs/*.log",
                "*.tmp",
                "*.temp",
                "temp/",
                "tmp/",
                ".DS_Store",
                "Thumbs.db"
            ],
            "database_runtime": [
                "*.db-wal",
                "*.db-shm", 
                "*.sqlite-wal",
                "*.sqlite-shm"
            ],
            "build_artifacts": [
                "*.map",
                "*.min.js",
                "*.min.css",
                ".vscode/",
                ".idea/"
            ]
        }
        
        # 需要备份的重要文件模式
        self.backup_patterns = [
            "config/*.json",
            "data/*.db",
            "data/exports/*.csv"
        ]
        
        # 绝对不能删除的文件/目录
        self.protected_paths = [
            ".git/",
            "src/",
            "docs/",
            "config/",
            "README.md",
            "requirements.txt",
            "package.json",
            ".gitignore"
        ]
    
    def cleanup(self, categories: List[str] = None) -> CleanupResult:
        """执行清理操作"""
        self.result.cleanup_time = datetime.now().isoformat()
        
        logger.info(f"🧹 开始文件清理... {'(预览模式)' if self.dry_run else ''}")
        logger.info(f"📁 项目路径: {self.project_root}")
        
        # 如果没有指定类别，清理所有类别
        if not categories:
            categories = list(self.cleanup_patterns.keys())
        
        for category in categories:
            if category in self.cleanup_patterns:
                logger.info(f"\n  清理类别: {category}")
                self._cleanup_category(category)
            else:
                logger.info(f"⚠️  未知的清理类别: {category}")
        
        self._generate_summary()
        return self.result
    
    def _cleanup_category(self, category: str):
        """清理指定类别的文件"""
        patterns = self.cleanup_patterns[category]
        
        for pattern in patterns:
            logger.info(f"    处理模式: {pattern}")
            
            if pattern.endswith("/"):
                # 目录模式
                self._cleanup_directories(pattern.rstrip("/"))
            else:
                # 文件模式
                self._cleanup_files(pattern)
    
    def _cleanup_files(self, pattern: str):
        """清理匹配模式的文件"""
        for file_path in self.project_root.rglob(pattern):
            if self._is_protected(file_path):
                continue
                
            try:
                # 检查是否需要备份
                if self._should_backup(file_path):
                    self._backup_file(file_path)
                
                # 删除文件
                if not self.dry_run:
                    file_size = file_path.stat().st_size
                    file_path.unlink()
                    self.result.total_size_freed += file_size
                
                self.result.deleted_files.append(str(file_path))
                logger.info(f"      ✓ 删除文件: {file_path.relative_to(self.project_root)}")
                
            except Exception as e:
                error_msg = f"删除文件失败 {file_path}: {e}"
                self.result.errors.append(error_msg)
                logger.info(f"      ❌ {error_msg}")
    
    def _cleanup_directories(self, pattern: str):
        """清理匹配模式的目录"""
        for dir_path in self.project_root.rglob(pattern):
            if not dir_path.is_dir() or self._is_protected(dir_path):
                continue
            
            try:
                # 计算目录大小
                dir_size = self._get_directory_size(dir_path)
                
                # 删除目录
                if not self.dry_run:
                    shutil.rmtree(dir_path)
                    self.result.total_size_freed += dir_size
                
                self.result.deleted_dirs.append(str(dir_path))
                logger.info(f"      ✓ 删除目录: {dir_path.relative_to(self.project_root)}")
                
            except Exception as e:
                error_msg = f"删除目录失败 {dir_path}: {e}"
                self.result.errors.append(error_msg)
                logger.info(f"      ❌ {error_msg}")
    
    def _is_protected(self, path: Path) -> bool:
        """检查路径是否受保护"""
        relative_path = path.relative_to(self.project_root)
        
        for protected in self.protected_paths:
            if str(relative_path).startswith(protected):
                return True
            
            # 检查父目录是否受保护
            for parent in relative_path.parents:
                if str(parent) == protected.rstrip("/"):
                    return True
        
        return False
    
    def _should_backup(self, file_path: Path) -> bool:
        """检查文件是否需要备份"""
        relative_path = file_path.relative_to(self.project_root)
        
        for pattern in self.backup_patterns:
            if fnmatch.fnmatch(str(relative_path), pattern):
                return True
        
        return False
    
    def _backup_file(self, file_path: Path):
        """备份文件"""
        backup_dir = self.project_root / "backup" / "cleanup" / datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        relative_path = file_path.relative_to(self.project_root)
        backup_path = backup_dir / relative_path
        backup_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            if not self.dry_run:
                shutil.copy2(file_path, backup_path)
            
            self.result.backed_up_files.append(str(backup_path))
            logger.info(f"      💾 备份文件: {relative_path}")
            
        except Exception as e:
            error_msg = f"备份文件失败 {file_path}: {e}"
            self.result.errors.append(error_msg)
            logger.info(f"      ❌ {error_msg}")
    
    def _get_directory_size(self, dir_path: Path) -> int:
        """计算目录大小"""
        total_size = 0
        try:
            for file_path in dir_path.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception:
            pass
        return total_size
    
    def _generate_summary(self):
        """生成清理总结"""
        logger.info(f"\n📊 清理总结:")
        logger.info(f"  删除文件: {len(self.result.deleted_files)} 个")
        logger.info(f"  删除目录: {len(self.result.deleted_dirs)} 个")
        logger.info(f"  备份文件: {len(self.result.backed_up_files)} 个")
        logger.info(f"  释放空间: {self._format_size(self.result.total_size_freed)}")
        
        if self.result.errors:
            logger.info(f"  错误数量: {len(self.result.errors)} 个")
            logger.info("  错误详情:")
            for error in self.result.errors[:5]:  # 只显示前5个错误
                logger.info(f"    - {error}")
            if len(self.result.errors) > 5:
                logger.info(f"    ... 还有 {len(self.result.errors) - 5} 个错误")
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="项目文件清理工具")
    parser.add_argument("--project-root", type=Path,
                       help="项目根目录路径")
    parser.add_argument("--categories", "-c", nargs="+",
                       choices=["python_cache", "nodejs_build", "logs_temp", 
                              "database_runtime", "build_artifacts"],
                       help="要清理的文件类别")
    parser.add_argument("--dry-run", "-n", action="store_true",
                       help="预览模式，不实际删除文件")
    parser.add_argument("--output", "-o", type=Path,
                       help="输出报告文件路径")
    parser.add_argument("--force", "-f", action="store_true",
                       help="强制清理，跳过确认")
    
    args = parser.parse_args()
    
    # 确定项目根目录
    if args.project_root:
        project_root = args.project_root
    else:
        # 从当前脚本位置推导
        project_root = Path(__file__).parent.parent.parent
    
    if not project_root.exists():
        logger.info(f"❌ 项目根目录不存在: {project_root}")
        sys.exit(1)
    
    # 安全确认
    if not args.dry_run and not args.force:
        logger.info(f"⚠️  即将清理项目: {project_root}")
        logger.info("此操作将删除临时文件和缓存，确认继续吗？")
        response = input("输入 'yes' 确认: ")
        if response.lower() != 'yes':
            logger.info("取消清理操作")
            sys.exit(0)
    
    # 执行清理
    cleanup_manager = FileCleanupManager(project_root, dry_run=args.dry_run)
    result = cleanup_manager.cleanup(args.categories)
    
    # 保存报告
    if args.output:
        report = {
            "timestamp": result.cleanup_time,
            "project_root": str(project_root),
            "dry_run": args.dry_run,
            "categories": args.categories or ["all"],
            "results": {
                "deleted_files_count": len(result.deleted_files),
                "deleted_dirs_count": len(result.deleted_dirs),
                "backed_up_files_count": len(result.backed_up_files),
                "errors_count": len(result.errors),
                "total_size_freed": result.total_size_freed
            },
            "details": {
                "deleted_files": result.deleted_files,
                "deleted_dirs": result.deleted_dirs,
                "backed_up_files": result.backed_up_files,
                "errors": result.errors
            }
        }
        
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        logger.info(f"\n📝 报告已保存到: {args.output}")
    
    # 清理完成
    if result.errors:
        logger.info(f"\n⚠️  清理完成，但有 {len(result.errors)} 个错误")
        sys.exit(1)
    else:
        logger.info(f"\n✅ 清理完成!")
        sys.exit(0)


if __name__ == "__main__":
    main()
