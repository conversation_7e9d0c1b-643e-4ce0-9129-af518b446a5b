import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
文件组织规范工具测试脚本

测试文件组织规则引擎、目录结构验证和文件放置指导功能。
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tools.file_organization import (
    FileOrganizationRulesEngine,
    DirectoryStructureValidator,
    FilePlacementGuide
)


def test_rules_engine():
    """测试文件组织规则引擎"""
    logger.info("🧪 测试文件组织规则引擎...")
    
    engine = FileOrganizationRulesEngine()
    
    # 测试文件分类
    test_files = [
        "src/application/main.py",
        "tests/test_main.py", 
        "config/config.yaml",
        "docs/README.md",
        "data/sample.csv",
        "logs/system.log",
        "scripts/deploy.sh"
    ]
    
    logger.info("\n📂 文件分类测试:")
    for file_path in test_files:
        rule = engine.classify_file(file_path)
        if rule:
            logger.info(f"  ✅ {file_path} → {rule.name} ({rule.description})")
        else:
            logger.info(f"  ❌ {file_path} → 未分类")
    
    # 测试批量分类
    classification = engine.batch_classify_files(test_files)
    logger.info(f"\n📊 批量分类结果: {len(classification)} 个分类")
    for rule_name, files in classification.items():
        logger.info(f"  {rule_name}: {len(files)} 个文件")
    
    logger.info("✅ 规则引擎测试完成")


def test_structure_validator():
    """测试目录结构验证器"""
    logger.info("\n🧪 测试目录结构验证器...")
    
    validator = DirectoryStructureValidator()
    
    # 测试项目结构验证
    result = validator.validate_project_structure()
    
    logger.info(f"\n📋 项目结构验证结果: {'✅ 通过' if result.is_valid else '❌ 未通过'}")
    
    if result.errors:
        logger.info(f"🚨 错误数量: {len(result.errors)}")
        for error in result.errors[:3]:  # 只显示前3个
            logger.info(f"  - {error}")
    
    if result.warnings:
        logger.info(f"⚠️ 警告数量: {len(result.warnings)}")
        for warning in result.warnings[:3]:  # 只显示前3个
            logger.info(f"  - {warning}")
    
    if result.suggestions:
        logger.info(f"💡 建议数量: {len(result.suggestions)}")
        for suggestion in result.suggestions[:3]:  # 只显示前3个
            logger.info(f"  - {suggestion}")
    
    # 测试结构摘要
    summary = validator.get_structure_summary()
    logger.info(f"\n📊 项目摘要:")
    logger.info(f"  总文件数: {summary['total_files']}")
    logger.info(f"  总目录数: {summary['total_directories']}")
    
    logger.info("✅ 结构验证器测试完成")


def test_placement_guide():
    """测试文件放置指导工具"""
    logger.info("\n🧪 测试文件放置指导工具...")
    
    guide = FilePlacementGuide()
    
    # 测试单个文件建议
    test_files = [
        "new_strategy.py",
        "test_new_feature.py",
        "app_config.yaml",
        "user_guide.md",
        "market_data.csv",
        "deploy_script.sh"
    ]
    
    logger.info("\n🎯 文件放置建议测试:")
    for file_path in test_files:
        suggestion = guide.get_placement_suggestion(file_path)
        logger.info(f"  📄 {file_path}")
        logger.info(f"    建议位置: {suggestion.suggested_location}")
        logger.info(f"    置信度: {suggestion.confidence:.1%}")
        logger.info(f"    理由: {suggestion.reason}")
        logger.info()
    
    # 测试批量分析
    suggestions = guide.analyze_file_batch(test_files)
    
    high_confidence = sum(1 for s in suggestions.values() if s.confidence >= 0.8)
    medium_confidence = sum(1 for s in suggestions.values() if 0.6 <= s.confidence < 0.8)
    low_confidence = sum(1 for s in suggestions.values() if s.confidence < 0.6)
    
    logger.info(f"📊 批量分析结果:")
    logger.info(f"  高置信度建议: {high_confidence} 个")
    logger.info(f"  中等置信度建议: {medium_confidence} 个") 
    logger.info(f"  低置信度建议: {low_confidence} 个")
    
    logger.info("✅ 放置指导工具测试完成")


def test_integration():
    """集成测试"""
    logger.info("\n🧪 集成测试...")
    
    # 测试工具协同工作
    engine = FileOrganizationRulesEngine()
    validator = DirectoryStructureValidator()
    guide = FilePlacementGuide()
    
    # 模拟新文件放置流程
    new_file = "new_trading_strategy.py"
    
    logger.info(f"\n🔄 模拟文件放置流程: {new_file}")
    
    # 1. 获取放置建议
    suggestion = guide.get_placement_suggestion(new_file)
    logger.info(f"1. 放置建议: {suggestion.suggested_location} (置信度: {suggestion.confidence:.1%})")
    
    # 2. 检查目标目录
    target_dir = suggestion.suggested_location.rstrip('/')
    dir_result = validator.validate_directory(target_dir)
    logger.info(f"2. 目标目录验证: {'✅ 通过' if dir_result.is_valid else '❌ 未通过'}")
    
    # 3. 应用组织规则
    rule = engine.classify_file(f"{suggestion.suggested_location}{new_file}")
    logger.info(f"3. 应用规则: {rule.name if rule else '无匹配规则'}")
    
    logger.info("✅ 集成测试完成")


def main():
    """主测试函数"""
    logger.info("🚀 开始文件组织规范工具测试")
    logger.info("=" * 50)
    
    try:
        test_rules_engine()
        test_structure_validator()
        test_placement_guide()
        test_integration()
        
        logger.info("\n" + "=" * 50)
        logger.info("🎉 所有测试完成！")
        
    except Exception as e:
        logger.info(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()