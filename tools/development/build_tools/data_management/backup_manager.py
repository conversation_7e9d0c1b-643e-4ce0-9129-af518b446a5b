"""
数据备份策略管理器

实现数据文件的备份和恢复功能。
"""

import os
import shutil
import tarfile
import zipfile
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, timedelta
import json
import hashlib
import threading
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class DataBackupManager:
    """数据备份管理器"""
    
    def __init__(self, data_root: str = "data", backup_root: str = "data/backups"):
        """
        初始化数据备份管理器
        
        Args:
            data_root: 数据根目录路径
            backup_root: 备份根目录路径
        """
        self.data_root = Path(data_root)
        self.backup_root = Path(backup_root)
        self.backup_root.mkdir(parents=True, exist_ok=True)
        
        self.backup_strategies = self._load_backup_strategies()
        self.backup_history = []
        self._lock = threading.Lock()
    
    def _load_backup_strategies(self) -> Dict:
        """加载备份策略配置"""
        return {
            # 数据库文件备份策略
            "database_backup": {
                "source_patterns": ["db/*.db", "db/*.sqlite*"],
                "backup_frequency": "daily",  # daily, weekly, monthly
                "retention_days": 30,
                "compression": "gzip",
                "incremental": False,
                "priority": "high",
                "description": "数据库文件每日备份"
            },
            # 重要配置文件备份策略
            "config_backup": {
                "source_patterns": ["../config/**/*.yaml", "../config/**/*.json"],
                "backup_frequency": "weekly",
                "retention_days": 90,
                "compression": "zip",
                "incremental": False,
                "priority": "high",
                "description": "配置文件每周备份"
            },
            # CSV数据文件备份策略
            "csv_data_backup": {
                "source_patterns": ["csv/*.csv", "csv/*.tsv"],
                "backup_frequency": "weekly",
                "retention_days": 60,
                "compression": "gzip",
                "incremental": True,
                "priority": "medium",
                "description": "CSV数据文件每周增量备份"
            },
            # 导出文件备份策略
            "exports_backup": {
                "source_patterns": ["exports/**/*"],
                "backup_frequency": "monthly",
                "retention_days": 180,
                "compression": "zip",
                "incremental": True,
                "priority": "low",
                "description": "导出文件每月备份"
            },
            # 缓存文件备份策略（仅重要缓存）
            "cache_backup": {
                "source_patterns": ["cache/cache_index.pkl"],
                "backup_frequency": "daily",
                "retention_days": 7,
                "compression": "gzip",
                "incremental": False,
                "priority": "low",
                "description": "重要缓存文件每日备份"
            }
        }
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """
        计算文件的MD5哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件的MD5哈希值
        """
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return ""
    
    def _find_files_by_patterns(self, patterns: List[str]) -> List[Path]:
        """
        根据模式查找文件
        
        Args:
            patterns: 文件模式列表
            
        Returns:
            匹配的文件列表
        """
        files = []
        for pattern in patterns:
            # 处理相对路径模式
            if pattern.startswith("../"):
                search_root = self.data_root.parent
                pattern = pattern[3:]  # 移除 "../"
            else:
                search_root = self.data_root
            
            try:
                matched_files = list(search_root.glob(pattern))
                files.extend([f for f in matched_files if f.is_file()])
            except Exception as e:
                logger.error(f"查找文件模式失败 {pattern}: {e}")
        
        return files
    
    def _create_backup_archive(self, files: List[Path], backup_path: Path, 
                             compression: str = "gzip") -> bool:
        """
        创建备份归档文件
        
        Args:
            files: 要备份的文件列表
            backup_path: 备份文件路径
            compression: 压缩方式 (gzip, zip, none)
            
        Returns:
            是否成功创建备份
        """
        try:
            if compression == "gzip":
                with tarfile.open(backup_path, "w:gz") as tar:
                    for file_path in files:
                        # 计算相对路径以保持目录结构
                        arcname = file_path.relative_to(self.data_root.parent)
                        tar.add(file_path, arcname=arcname)
                        
            elif compression == "zip":
                with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                    for file_path in files:
                        arcname = file_path.relative_to(self.data_root.parent)
                        zip_file.write(file_path, arcname=arcname)
                        
            else:  # no compression
                # 创建目录结构并复制文件
                backup_dir = backup_path.with_suffix("")
                backup_dir.mkdir(parents=True, exist_ok=True)
                
                for file_path in files:
                    rel_path = file_path.relative_to(self.data_root.parent)
                    target_path = backup_dir / rel_path
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(file_path, target_path)
            
            logger.info(f"备份归档创建成功: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"创建备份归档失败 {backup_path}: {e}")
            return False
    
    def create_backup(self, strategy_name: str, custom_suffix: str = None) -> Optional[Dict]:
        """
        创建备份
        
        Args:
            strategy_name: 备份策略名称
            custom_suffix: 自定义备份文件后缀
            
        Returns:
            备份信息字典，失败时返回None
        """
        if strategy_name not in self.backup_strategies:
            logger.error(f"未知的备份策略: {strategy_name}")
            return None
        
        strategy = self.backup_strategies[strategy_name]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 构建备份文件名
        suffix = custom_suffix or strategy_name
        compression_ext = {
            "gzip": ".tar.gz",
            "zip": ".zip",
            "none": ""
        }.get(strategy["compression"], ".tar.gz")
        
        backup_filename = f"backup_{suffix}_{timestamp}{compression_ext}"
        backup_path = self.backup_root / backup_filename
        
        # 查找要备份的文件
        files_to_backup = self._find_files_by_patterns(strategy["source_patterns"])
        
        if not files_to_backup:
            logger.warning(f"没有找到要备份的文件: {strategy_name}")
            return None
        
        # 检查是否需要增量备份
        if strategy["incremental"]:
            files_to_backup = self._filter_files_for_incremental_backup(
                files_to_backup, strategy_name
            )
        
        # 创建备份
        with self._lock:
            success = self._create_backup_archive(
                files_to_backup, backup_path, strategy["compression"]
            )
        
        if success:
            # 记录备份信息
            backup_info = {
                "backup_id": f"{strategy_name}_{timestamp}",
                "strategy_name": strategy_name,
                "backup_path": str(backup_path),
                "timestamp": datetime.now().isoformat(),
                "file_count": len(files_to_backup),
                "backup_size_mb": round(backup_path.stat().st_size / (1024 * 1024), 2),
                "compression": strategy["compression"],
                "incremental": strategy["incremental"],
                "files": [str(f) for f in files_to_backup]
            }
            
            self.backup_history.append(backup_info)
            logger.info(f"备份创建成功: {backup_info['backup_id']}")
            return backup_info
        
        return None
    
    def _filter_files_for_incremental_backup(self, files: List[Path], 
                                           strategy_name: str) -> List[Path]:
        """
        为增量备份过滤文件
        
        Args:
            files: 文件列表
            strategy_name: 策略名称
            
        Returns:
            需要增量备份的文件列表
        """
        # 获取最后一次备份的文件信息
        last_backup = None
        for backup in reversed(self.backup_history):
            if backup["strategy_name"] == strategy_name:
                last_backup = backup
                break
        
        if not last_backup:
            # 如果没有历史备份，则备份所有文件
            return files
        
        # 比较文件修改时间和哈希值
        files_to_backup = []
        last_backup_time = datetime.fromisoformat(last_backup["timestamp"])
        
        for file_path in files:
            try:
                file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                
                # 如果文件在上次备份后被修改，则需要备份
                if file_mtime > last_backup_time:
                    files_to_backup.append(file_path)
                    continue
                
                # 检查文件哈希值是否改变（更精确的检查）
                current_hash = self._calculate_file_hash(file_path)
                if current_hash and str(file_path) in last_backup.get("file_hashes", {}):
                    if current_hash != last_backup["file_hashes"][str(file_path)]:
                        files_to_backup.append(file_path)
                
            except Exception as e:
                logger.error(f"检查文件增量备份状态失败 {file_path}: {e}")
                # 出错时保守地包含该文件
                files_to_backup.append(file_path)
        
        logger.info(f"增量备份: {len(files_to_backup)}/{len(files)} 个文件需要备份")
        return files_to_backup
    
    def restore_backup(self, backup_id: str, restore_path: str = None, 
                      selective_files: List[str] = None) -> bool:
        """
        恢复备份
        
        Args:
            backup_id: 备份ID
            restore_path: 恢复路径，默认为原位置
            selective_files: 选择性恢复的文件列表
            
        Returns:
            是否成功恢复
        """
        # 查找备份信息
        backup_info = None
        for backup in self.backup_history:
            if backup["backup_id"] == backup_id:
                backup_info = backup
                break
        
        if not backup_info:
            logger.error(f"未找到备份: {backup_id}")
            return False
        
        backup_path = Path(backup_info["backup_path"])
        if not backup_path.exists():
            logger.error(f"备份文件不存在: {backup_path}")
            return False
        
        # 确定恢复路径
        if restore_path is None:
            restore_path = self.data_root.parent
        else:
            restore_path = Path(restore_path)
        
        try:
            # 根据压缩方式恢复文件
            if backup_path.suffix == ".gz" or backup_path.name.endswith(".tar.gz"):
                with tarfile.open(backup_path, "r:gz") as tar:
                    if selective_files:
                        # 选择性恢复
                        for member in tar.getmembers():
                            if any(sel_file in member.name for sel_file in selective_files):
                                tar.extract(member, restore_path)
                    else:
                        # 恢复所有文件
                        tar.extractall(restore_path)
                        
            elif backup_path.suffix == ".zip":
                with zipfile.ZipFile(backup_path, 'r') as zip_file:
                    if selective_files:
                        # 选择性恢复
                        for file_info in zip_file.filelist:
                            if any(sel_file in file_info.filename for sel_file in selective_files):
                                zip_file.extract(file_info, restore_path)
                    else:
                        # 恢复所有文件
                        zip_file.extractall(restore_path)
            
            else:
                # 无压缩的目录恢复
                backup_dir = backup_path
                if backup_dir.is_dir():
                    shutil.copytree(backup_dir, restore_path, dirs_exist_ok=True)
            
            logger.info(f"备份恢复成功: {backup_id} -> {restore_path}")
            return True
            
        except Exception as e:
            logger.error(f"恢复备份失败 {backup_id}: {e}")
            return False
    
    def cleanup_old_backups(self, strategy_name: str = None, dry_run: bool = False) -> Dict[str, int]:
        """
        清理过期的备份文件
        
        Args:
            strategy_name: 特定策略名称，None表示清理所有策略
            dry_run: 是否只是预览
            
        Returns:
            清理统计信息
        """
        stats = {"deleted": 0, "freed_mb": 0, "errors": 0}
        
        strategies_to_clean = [strategy_name] if strategy_name else self.backup_strategies.keys()
        
        for strategy in strategies_to_clean:
            if strategy not in self.backup_strategies:
                continue
            
            retention_days = self.backup_strategies[strategy]["retention_days"]
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            # 查找过期的备份
            backups_to_delete = []
            for backup in self.backup_history:
                if backup["strategy_name"] == strategy:
                    backup_time = datetime.fromisoformat(backup["timestamp"])
                    if backup_time < cutoff_date:
                        backups_to_delete.append(backup)
            
            # 删除过期备份
            for backup in backups_to_delete:
                backup_path = Path(backup["backup_path"])
                try:
                    if backup_path.exists():
                        backup_size = backup_path.stat().st_size / (1024 * 1024)
                        
                        if not dry_run:
                            if backup_path.is_file():
                                backup_path.unlink()
                            else:
                                shutil.rmtree(backup_path)
                            
                            # 从历史记录中移除
                            self.backup_history.remove(backup)
                            
                            logger.info(f"已删除过期备份: {backup['backup_id']}")
                        else:
                            logger.info(f"[预览] 将删除过期备份: {backup['backup_id']}")
                        
                        stats["deleted"] += 1
                        stats["freed_mb"] += backup_size
                        
                except Exception as e:
                    logger.error(f"删除备份失败 {backup['backup_id']}: {e}")
                    stats["errors"] += 1
        
        return stats
    
    def create_scheduled_backups(self, strategies: List[str] = None) -> Dict[str, Optional[Dict]]:
        """
        创建计划备份
        
        Args:
            strategies: 要执行的策略列表，None表示所有策略
            
        Returns:
            各策略的备份结果
        """
        strategies = strategies or list(self.backup_strategies.keys())
        results = {}
        
        # 使用线程池并行执行备份
        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_strategy = {
                executor.submit(self.create_backup, strategy): strategy 
                for strategy in strategies
            }
            
            for future in future_to_strategy:
                strategy = future_to_strategy[future]
                try:
                    result = future.result(timeout=300)  # 5分钟超时
                    results[strategy] = result
                except Exception as e:
                    logger.error(f"策略 {strategy} 备份失败: {e}")
                    results[strategy] = None
        
        return results
    
    def get_backup_status(self) -> Dict:
        """
        获取备份状态报告
        
        Returns:
            备份状态字典
        """
        status = {
            "timestamp": datetime.now().isoformat(),
            "backup_root": str(self.backup_root),
            "total_backups": len(self.backup_history),
            "strategies": {},
            "recent_backups": self.backup_history[-10:],  # 最近10个备份
            "storage_usage": {}
        }
        
        # 分析各策略状态
        for strategy_name, strategy in self.backup_strategies.items():
            strategy_backups = [b for b in self.backup_history if b["strategy_name"] == strategy_name]
            
            last_backup = strategy_backups[-1] if strategy_backups else None
            total_size = sum(b["backup_size_mb"] for b in strategy_backups)
            
            status["strategies"][strategy_name] = {
                "description": strategy["description"],
                "backup_count": len(strategy_backups),
                "total_size_mb": round(total_size, 2),
                "last_backup": last_backup["timestamp"] if last_backup else None,
                "retention_days": strategy["retention_days"],
                "frequency": strategy["backup_frequency"]
            }
        
        # 计算存储使用情况
        if self.backup_root.exists():
            total_size = sum(
                f.stat().st_size for f in self.backup_root.rglob("*") if f.is_file()
            )
            status["storage_usage"] = {
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "file_count": len(list(self.backup_root.rglob("*")))
            }
        
        return status
    
    def save_backup_history(self, output_path: str = None):
        """
        保存备份历史到文件
        
        Args:
            output_path: 输出文件路径
        """
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = self.backup_root / f"backup_history_{timestamp}.json"
        
        history_data = {
            "export_timestamp": datetime.now().isoformat(),
            "backup_strategies": self.backup_strategies,
            "backup_history": self.backup_history
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(history_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"备份历史已保存到: {output_path}")


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建备份管理器实例
    backup_manager = DataBackupManager()
    
    # 获取备份状态
    logger.info("获取备份状态...")
    status = backup_manager.get_backup_status()
    logger.info(json.dumps(status, ensure_ascii=False, indent=2))
    
    # 创建测试备份
    logger.info("\n创建数据库备份...")
    backup_info = backup_manager.create_backup("database_backup")
    if backup_info:
        logger.info(f"备份创建成功: {backup_info['backup_id']}")
    
    # 清理过期备份（预览）
    logger.info("\n预览过期备份清理...")
    cleanup_stats = backup_manager.cleanup_old_backups(dry_run=True)
    logger.info(f"清理统计: {cleanup_stats}")