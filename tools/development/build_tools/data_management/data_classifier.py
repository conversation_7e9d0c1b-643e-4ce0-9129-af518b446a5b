"""
数据文件自动分类器

按类型和用途自动分类数据文件到相应的data/子目录。
"""

import os
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import mimetypes
import json

logger = logging.getLogger(__name__)


class DataFileClassifier:
    """数据文件自动分类器"""
    
    def __init__(self, data_root: str = "data"):
        """
        初始化数据文件分类器
        
        Args:
            data_root: 数据根目录路径
        """
        self.data_root = Path(data_root)
        self.classification_rules = self._load_classification_rules()
        self._ensure_directories()
    
    def _load_classification_rules(self) -> Dict:
        """加载文件分类规则"""
        return {
            # 缓存文件规则
            "cache": {
                "extensions": [".pkl", ".cache", ".tmp"],
                "patterns": ["cache_", "temp_", "tmp_"],
                "directory": "cache",
                "description": "缓存和临时数据文件"
            },
            # CSV数据文件规则
            "csv": {
                "extensions": [".csv", ".tsv"],
                "patterns": ["market_data", "price_data", "trading_data"],
                "directory": "csv", 
                "description": "CSV格式的原始数据文件"
            },
            # 数据库文件规则
            "database": {
                "extensions": [".db", ".sqlite", ".sqlite3", ".db-shm", ".db-wal"],
                "patterns": ["trading_system", "risk_monitoring", "backend_"],
                "directory": "db",
                "description": "数据库文件"
            },
            # 导出文件规则
            "exports": {
                "extensions": [".xlsx", ".xls", ".json", ".xml", ".pdf"],
                "patterns": ["export_", "report_", "analysis_"],
                "directory": "exports",
                "description": "导出的数据和报告文件"
            },
            # 备份文件规则
            "backups": {
                "extensions": [".bak", ".backup", ".tar.gz", ".zip"],
                "patterns": ["backup_", "bak_"],
                "directory": "backups",
                "description": "备份文件"
            },
            # 日志数据文件规则
            "logs_data": {
                "extensions": [".log", ".txt"],
                "patterns": ["data_", "trading_log", "market_log"],
                "directory": "logs",
                "description": "数据相关的日志文件"
            }
        }
    
    def _ensure_directories(self):
        """确保所有必需的子目录存在"""
        for rule in self.classification_rules.values():
            dir_path = self.data_root / rule["directory"]
            dir_path.mkdir(parents=True, exist_ok=True)
            
            # 创建.gitkeep文件以保持空目录
            gitkeep_file = dir_path / ".gitkeep"
            if not gitkeep_file.exists():
                gitkeep_file.touch()
    
    def classify_file(self, file_path: str) -> Optional[str]:
        """
        分类单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            分类后的目标目录，如果无法分类则返回None
        """
        file_path = Path(file_path)
        filename = file_path.name.lower()
        extension = file_path.suffix.lower()
        
        # 按规则匹配文件
        for category, rule in self.classification_rules.items():
            # 检查文件扩展名
            if extension in rule["extensions"]:
                logger.info(f"文件 {filename} 按扩展名 {extension} 分类为 {category}")
                return rule["directory"]
            
            # 检查文件名模式
            for pattern in rule["patterns"]:
                if pattern in filename:
                    logger.info(f"文件 {filename} 按模式 {pattern} 分类为 {category}")
                    return rule["directory"]
        
        logger.warning(f"无法分类文件: {filename}")
        return None
    
    def move_file_to_category(self, source_path: str, target_category: str, 
                            create_subdirs: bool = True) -> bool:
        """
        将文件移动到指定分类目录
        
        Args:
            source_path: 源文件路径
            target_category: 目标分类目录
            create_subdirs: 是否根据日期创建子目录
            
        Returns:
            是否成功移动
        """
        try:
            source_path = Path(source_path)
            if not source_path.exists():
                logger.error(f"源文件不存在: {source_path}")
                return False
            
            # 构建目标路径
            target_dir = self.data_root / target_category
            
            # 根据日期创建子目录
            if create_subdirs and target_category in ["exports", "backups"]:
                date_subdir = datetime.now().strftime("%Y-%m")
                target_dir = target_dir / date_subdir
                target_dir.mkdir(parents=True, exist_ok=True)
            
            target_path = target_dir / source_path.name
            
            # 处理文件名冲突
            if target_path.exists():
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                name_parts = source_path.stem, timestamp, source_path.suffix
                new_name = f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
                target_path = target_dir / new_name
            
            # 移动文件
            shutil.move(str(source_path), str(target_path))
            logger.info(f"文件已移动: {source_path} -> {target_path}")
            return True
            
        except Exception as e:
            logger.error(f"移动文件失败 {source_path}: {e}")
            return False
    
    def classify_directory(self, directory_path: str, 
                          exclude_patterns: List[str] = None) -> Dict[str, List[str]]:
        """
        分类目录中的所有文件
        
        Args:
            directory_path: 要分类的目录路径
            exclude_patterns: 排除的文件模式列表
            
        Returns:
            分类结果字典 {category: [file_list]}
        """
        exclude_patterns = exclude_patterns or [".git", "__pycache__", ".DS_Store"]
        directory_path = Path(directory_path)
        results = {}
        
        if not directory_path.exists():
            logger.error(f"目录不存在: {directory_path}")
            return results
        
        # 遍历目录中的所有文件
        for file_path in directory_path.rglob("*"):
            if file_path.is_file():
                # 检查是否应该排除
                should_exclude = any(pattern in str(file_path) for pattern in exclude_patterns)
                if should_exclude:
                    continue
                
                # 分类文件
                category = self.classify_file(str(file_path))
                if category:
                    if category not in results:
                        results[category] = []
                    results[category].append(str(file_path))
        
        return results
    
    def auto_organize_data_directory(self, dry_run: bool = False) -> Dict[str, int]:
        """
        自动整理data目录中的文件
        
        Args:
            dry_run: 是否只是预览而不实际移动文件
            
        Returns:
            整理统计信息
        """
        stats = {"moved": 0, "skipped": 0, "errors": 0}
        
        # 获取data目录中的所有文件（排除已经在子目录中的文件）
        for file_path in self.data_root.iterdir():
            if file_path.is_file():
                category = self.classify_file(str(file_path))
                if category:
                    if not dry_run:
                        success = self.move_file_to_category(str(file_path), category)
                        if success:
                            stats["moved"] += 1
                        else:
                            stats["errors"] += 1
                    else:
                        logger.info(f"[预览] 将移动文件 {file_path.name} 到 {category}/")
                        stats["moved"] += 1
                else:
                    stats["skipped"] += 1
                    logger.info(f"跳过无法分类的文件: {file_path.name}")
        
        return stats
    
    def get_classification_report(self) -> Dict:
        """
        获取当前数据目录的分类报告
        
        Returns:
            分类报告字典
        """
        report = {
            "timestamp": datetime.now().isoformat(),
            "data_root": str(self.data_root),
            "categories": {}
        }
        
        for category, rule in self.classification_rules.items():
            category_dir = self.data_root / rule["directory"]
            if category_dir.exists():
                files = list(category_dir.rglob("*"))
                file_count = len([f for f in files if f.is_file()])
                total_size = sum(f.stat().st_size for f in files if f.is_file())
                
                report["categories"][category] = {
                    "directory": rule["directory"],
                    "description": rule["description"],
                    "file_count": file_count,
                    "total_size_mb": round(total_size / (1024 * 1024), 2),
                    "last_modified": max(
                        (f.stat().st_mtime for f in files if f.is_file()), 
                        default=0
                    )
                }
        
        return report
    
    def save_classification_report(self, output_path: str = None):
        """
        保存分类报告到文件
        
        Args:
            output_path: 输出文件路径，默认保存到data/reports/目录
        """
        if output_path is None:
            reports_dir = self.data_root / "reports"
            reports_dir.mkdir(exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = reports_dir / f"classification_report_{timestamp}.json"
        
        report = self.get_classification_report()
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"分类报告已保存到: {output_path}")


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建分类器实例
    classifier = DataFileClassifier()
    
    # 生成分类报告
    logger.info("生成数据文件分类报告...")
    report = classifier.get_classification_report()
    logger.info(json.dumps(report, ensure_ascii=False, indent=2))
    
    # 自动整理数据目录（预览模式）
    logger.info("\n预览数据目录整理...")
    stats = classifier.auto_organize_data_directory(dry_run=True)
    logger.info(f"预览结果: {stats}")