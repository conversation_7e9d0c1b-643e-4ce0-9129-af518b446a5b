import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
数据文件管理系统演示脚本

展示数据文件分类、清理、备份和监控功能的完整工作流程。
"""

import sys
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from tools.data_management import (
    DataFileClassifier,
    DataFileCleaner,
    DataBackupManager,
    DataStorageMonitor
)


def print_section(title):
    """打印章节标题"""
    logger.info(f"\n{'='*60}")
    logger.info(f" {title}")
    logger.info(f"{'='*60}")


def demo_file_classification():
    """演示文件分类功能"""
    print_section("数据文件分类演示")
    
    classifier = DataFileClassifier()
    
    # 生成分类报告
    logger.info("📊 生成文件分类报告...")
    report = classifier.get_classification_report()
    
    logger.info(f"数据根目录: {report['data_root']}")
    logger.info(f"报告时间: {report['timestamp']}")
    logger.info("\n各类别文件统计:")
    
    for category, stats in report['categories'].items():
        logger.info(f"  {stats['description']}: {stats['file_count']} 个文件, {stats['total_size_mb']}MB")
    
    # 预览自动整理
    logger.info("\n🗂️ 预览自动整理操作...")
    stats = classifier.auto_organize_data_directory(dry_run=True)
    logger.info(f"预计移动 {stats['moved']} 个文件, 跳过 {stats['skipped']} 个文件")
    
    return classifier


def demo_file_cleaning():
    """演示文件清理功能"""
    print_section("数据文件清理演示")
    
    cleaner = DataFileCleaner()
    
    # 生成清理报告
    logger.info("🧹 生成文件清理报告...")
    report = cleaner.get_cleanup_report()
    
    logger.info("各清理规则分析:")
    for rule_name, analysis in report['rules_analysis'].items():
        logger.info(f"  {analysis['description']}: 可清理 {analysis['files_to_clean']} 个文件, "
              f"释放 {analysis['potential_freed_mb']}MB")
    
    # 预览清理操作
    logger.info("\n🗑️ 预览清理操作...")
    all_stats = cleaner.clean_all_rules(dry_run=True)
    
    total_files = sum(stats['deleted'] for stats in all_stats.values())
    total_space = sum(stats['freed_mb'] for stats in all_stats.values())
    
    logger.info(f"预计清理 {total_files} 个文件, 释放 {total_space:.1f}MB 空间")
    
    return cleaner


def demo_backup_management():
    """演示备份管理功能"""
    print_section("数据备份管理演示")
    
    backup_manager = DataBackupManager()
    
    # 获取备份状态
    logger.info("💾 获取备份状态...")
    status = backup_manager.get_backup_status()
    
    logger.info(f"备份根目录: {status['backup_root']}")
    logger.info(f"历史备份数量: {status['total_backups']}")
    
    logger.info("\n备份策略配置:")
    for strategy_name, strategy_info in status['strategies'].items():
        logger.info(f"  {strategy_info['description']}: "
              f"{strategy_info['backup_count']} 个备份, "
              f"{strategy_info['total_size_mb']}MB, "
              f"频率: {strategy_info['frequency']}")
    
    # 尝试创建测试备份
    logger.info("\n📦 尝试创建数据库备份...")
    backup_info = backup_manager.create_backup("database_backup")
    
    if backup_info:
        logger.info(f"备份创建成功:")
        logger.info(f"  备份ID: {backup_info['backup_id']}")
        logger.info(f"  文件数量: {backup_info['file_count']}")
        logger.info(f"  备份大小: {backup_info['backup_size_mb']}MB")
        logger.info(f"  备份路径: {backup_info['backup_path']}")
    else:
        logger.info("没有找到需要备份的文件")
    
    return backup_manager


def demo_storage_monitoring():
    """演示存储监控功能"""
    print_section("数据存储监控演示")
    
    monitor = DataStorageMonitor()
    
    # 收集监控数据
    logger.info("📊 收集存储监控数据...")
    monitoring_data = monitor.collect_monitoring_data()
    
    # 显示磁盘使用情况
    disk_usage = monitoring_data['disk_usage']
    logger.info(f"磁盘使用情况:")
    logger.info(f"  总容量: {disk_usage['total_gb']}GB")
    logger.info(f"  已使用: {disk_usage['used_gb']}GB ({disk_usage['usage_percent']}%)")
    logger.info(f"  可用空间: {disk_usage['free_gb']}GB")
    
    # 显示目录统计
    dir_stats = monitoring_data['directory_stats']
    logger.info(f"\n目录统计:")
    logger.info(f"  数据总大小: {dir_stats['total_size_mb']}MB")
    logger.info(f"  文件总数: {dir_stats['total_files']}")
    
    logger.info(f"\n子目录详情:")
    for dirname, stats in dir_stats['subdirectories'].items():
        logger.info(f"  {dirname}: {stats['size_mb']}MB ({stats['file_count']} 个文件)")
    
    # 显示告警信息
    alerts = monitoring_data['alerts']
    if alerts:
        logger.info(f"\n⚠️ 当前告警 ({len(alerts)} 个):")
        for alert in alerts:
            level_icon = "🔴" if alert['level'] == 'critical' else "🟡"
            logger.info(f"  {level_icon} {alert['message']}")
    else:
        logger.info(f"\n✅ 无告警")
    
    # 生成监控报告
    logger.info(f"\n📈 生成监控报告...")
    report = monitor.get_monitoring_report(hours=1)
    
    if 'error' not in report:
        logger.info(f"报告时间范围: {report['report_period_hours']} 小时")
        logger.info(f"增长分析:")
        growth = report['growth_analysis']
        logger.info(f"  大小增长: {growth['size_growth_mb']}MB")
        logger.info(f"  文件增长: {growth['file_growth']} 个")
        logger.info(f"  平均增长速度: {growth['average_size_growth_per_hour']}MB/小时")
        
        logger.info(f"\n优化建议:")
        for recommendation in report['recommendations']:
            logger.info(f"  💡 {recommendation}")
    else:
        logger.info(f"报告生成失败: {report['error']}")
    
    return monitor


def demo_integration():
    """演示系统集成功能"""
    print_section("系统集成演示")
    
    logger.info("🔄 执行完整的数据管理工作流程...")
    
    # 1. 文件分类
    logger.info("\n1️⃣ 执行文件分类...")
    classifier = DataFileClassifier()
    classify_stats = classifier.auto_organize_data_directory(dry_run=True)
    logger.info(f"   分类结果: 移动 {classify_stats['moved']} 个文件")
    
    # 2. 存储监控
    logger.info("\n2️⃣ 执行存储监控...")
    monitor = DataStorageMonitor()
    monitoring_data = monitor.collect_monitoring_data()
    logger.info(f"   监控结果: 磁盘使用率 {monitoring_data['disk_usage']['usage_percent']}%")
    
    # 3. 文件清理
    logger.info("\n3️⃣ 执行文件清理...")
    cleaner = DataFileCleaner()
    cleanup_stats = cleaner.clean_all_rules(dry_run=True)
    total_cleanup = sum(stats['deleted'] for stats in cleanup_stats.values())
    logger.info(f"   清理结果: 可清理 {total_cleanup} 个文件")
    
    # 4. 数据备份
    logger.info("\n4️⃣ 执行数据备份...")
    backup_manager = DataBackupManager()
    backup_status = backup_manager.get_backup_status()
    logger.info(f"   备份结果: 当前有 {backup_status['total_backups']} 个备份")
    
    logger.info("\n✅ 完整工作流程演示完成!")


def main():
    """主演示函数"""
    logger.info("🚀 数据文件管理系统演示")
    logger.info("本演示将展示系统的四个核心功能模块")
    
    try:
        # 演示各个功能模块
        demo_file_classification()
        time.sleep(1)
        
        demo_file_cleaning()
        time.sleep(1)
        
        demo_backup_management()
        time.sleep(1)
        
        demo_storage_monitoring()
        time.sleep(1)
        
        demo_integration()
        
        print_section("演示完成")
        logger.info("🎉 数据文件管理系统演示成功完成!")
        logger.info("\n系统功能总结:")
        logger.info("  ✅ 数据文件自动分类 - 智能识别和组织文件")
        logger.info("  ✅ 数据文件清理机制 - 自动清理过期和临时文件")
        logger.info("  ✅ 数据备份策略管理 - 多策略备份和恢复")
        logger.info("  ✅ 数据存储监控 - 实时监控和智能告警")
        logger.info("\n使用命令行工具:")
        logger.info("  python tools/data_management/cli.py --help")
        
    except KeyboardInterrupt:
        logger.info("\n\n演示被用户中断")
    except Exception as e:
        logger.info(f"\n\n演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()