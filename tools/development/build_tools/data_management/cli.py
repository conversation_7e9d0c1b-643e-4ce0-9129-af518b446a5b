logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
数据文件管理系统命令行工具

提供数据文件分类、清理、备份和监控的统一命令行接口。
"""

import argparse
import logging
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from tools.data_management import (
    DataFileClassifier,
    DataFileCleaner,
    DataBackupManager,
    DataStorageMonitor
)


def setup_logging(verbose: bool = False):
    """设置日志配置"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def classify_command(args):
    """执行文件分类命令"""
    classifier = DataFileClassifier(args.data_root)
    
    if args.report:
        # 生成分类报告
        report = classifier.get_classification_report()
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"分类报告已保存到: {args.output}")
        else:
            logger.info(json.dumps(report, ensure_ascii=False, indent=2))
    
    elif args.organize:
        # 自动整理数据目录
        logger.info("开始自动整理数据目录...")
        stats = classifier.auto_organize_data_directory(dry_run=args.dry_run)
        logger.info(f"整理完成: 移动 {stats['moved']} 个文件, "
              f"跳过 {stats['skipped']} 个文件, "
              f"错误 {stats['errors']} 个")
    
    elif args.classify_file:
        # 分类单个文件
        category = classifier.classify_file(args.classify_file)
        if category:
            logger.info(f"文件 {args.classify_file} 应分类到: {category}")
            if not args.dry_run:
                success = classifier.move_file_to_category(args.classify_file, category)
                if success:
                    logger.info("文件移动成功")
                else:
                    logger.info("文件移动失败")
        else:
            logger.info(f"无法分类文件: {args.classify_file}")


def clean_command(args):
    """执行文件清理命令"""
    cleaner = DataFileCleaner(args.data_root)
    
    if args.report:
        # 生成清理报告
        report = cleaner.get_cleanup_report()
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"清理报告已保存到: {args.output}")
        else:
            logger.info(json.dumps(report, ensure_ascii=False, indent=2))
    
    elif args.rule:
        # 按指定规则清理
        logger.info(f"执行清理规则: {args.rule}")
        stats = cleaner.clean_files_by_rule(args.rule, dry_run=args.dry_run)
        logger.info(f"清理完成: 删除 {stats['deleted']} 个文件, "
              f"释放 {stats['freed_mb']:.1f}MB 空间, "
              f"错误 {stats['errors']} 个")
    
    elif args.all:
        # 执行所有清理规则
        logger.info("执行所有清理规则...")
        exclude_rules = args.exclude.split(',') if args.exclude else []
        all_stats = cleaner.clean_all_rules(dry_run=args.dry_run, exclude_rules=exclude_rules)
        
        total_deleted = sum(stats['deleted'] for stats in all_stats.values())
        total_freed = sum(stats['freed_mb'] for stats in all_stats.values())
        total_errors = sum(stats['errors'] for stats in all_stats.values())
        
        logger.info(f"全部清理完成: 删除 {total_deleted} 个文件, "
              f"释放 {total_freed:.1f}MB 空间, "
              f"错误 {total_errors} 个")
        
        # 清理空目录
        if not args.dry_run:
            empty_dirs = cleaner.clean_empty_directories(dry_run=args.dry_run)
            logger.info(f"清理空目录: {empty_dirs} 个")


def backup_command(args):
    """执行备份命令"""
    backup_manager = DataBackupManager(args.data_root, args.backup_root)
    
    if args.status:
        # 显示备份状态
        status = backup_manager.get_backup_status()
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(status, f, ensure_ascii=False, indent=2)
            logger.info(f"备份状态已保存到: {args.output}")
        else:
            logger.info(json.dumps(status, ensure_ascii=False, indent=2))
    
    elif args.create:
        # 创建备份
        logger.info(f"创建备份: {args.create}")
        backup_info = backup_manager.create_backup(args.create, args.suffix)
        if backup_info:
            logger.info(f"备份创建成功: {backup_info['backup_id']}")
            logger.info(f"备份文件: {backup_info['backup_path']}")
            logger.info(f"文件数量: {backup_info['file_count']}")
            logger.info(f"备份大小: {backup_info['backup_size_mb']}MB")
        else:
            logger.info("备份创建失败")
    
    elif args.restore:
        # 恢复备份
        logger.info(f"恢复备份: {args.restore}")
        success = backup_manager.restore_backup(
            args.restore, 
            args.restore_path, 
            args.selective_files.split(',') if args.selective_files else None
        )
        if success:
            logger.info("备份恢复成功")
        else:
            logger.info("备份恢复失败")
    
    elif args.cleanup:
        # 清理过期备份
        logger.info("清理过期备份...")
        stats = backup_manager.cleanup_old_backups(args.strategy, dry_run=args.dry_run)
        logger.info(f"清理完成: 删除 {stats['deleted']} 个备份, "
              f"释放 {stats['freed_mb']:.1f}MB 空间, "
              f"错误 {stats['errors']} 个")
    
    elif args.scheduled:
        # 执行计划备份
        logger.info("执行计划备份...")
        strategies = args.strategies.split(',') if args.strategies else None
        results = backup_manager.create_scheduled_backups(strategies)
        
        success_count = sum(1 for result in results.values() if result is not None)
        logger.info(f"计划备份完成: {success_count}/{len(results)} 个策略成功")
        
        for strategy, result in results.items():
            if result:
                logger.info(f"  {strategy}: 成功 ({result['backup_size_mb']}MB)")
            else:
                logger.info(f"  {strategy}: 失败")


def monitor_command(args):
    """执行监控命令"""
    monitor = DataStorageMonitor(args.data_root, args.interval)
    
    if args.status:
        # 显示当前状态
        monitoring_data = monitor.collect_monitoring_data()
        
        logger.info("=== 存储监控状态 ===")
        logger.info(f"磁盘使用率: {monitoring_data['disk_usage']['usage_percent']}%")
        logger.info(f"可用空间: {monitoring_data['disk_usage']['free_gb']}GB")
        logger.info(f"数据总大小: {monitoring_data['directory_stats']['total_size_mb']}MB")
        logger.info(f"文件总数: {monitoring_data['directory_stats']['total_files']}")
        
        # 显示子目录统计
        logger.info("\n=== 子目录统计 ===")
        for dirname, stats in monitoring_data['directory_stats']['subdirectories'].items():
            logger.info(f"{dirname}: {stats['size_mb']}MB ({stats['file_count']} 个文件)")
        
        # 显示告警
        if monitoring_data['alerts']:
            logger.info("\n=== 当前告警 ===")
            for alert in monitoring_data['alerts']:
                logger.info(f"{alert['level'].upper()}: {alert['message']}")
        else:
            logger.info("\n无告警")
    
    elif args.report:
        # 生成监控报告
        logger.info(f"生成 {args.hours} 小时监控报告...")
        report = monitor.get_monitoring_report(args.hours)
        
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"监控报告已保存到: {args.output}")
        else:
            logger.info(json.dumps(report, ensure_ascii=False, indent=2))
    
    elif args.start:
        # 启动后台监控
        logger.info(f"启动后台监控 (间隔: {args.interval}秒)...")
        monitor.start_monitoring()
        
        try:
            logger.info("监控运行中，按 Ctrl+C 停止...")
            import time
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("\n停止监控...")
            monitor.stop_monitoring()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="数据文件管理系统命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 生成文件分类报告
  python cli.py classify --report
  
  # 自动整理数据目录（预览）
  python cli.py classify --organize --dry-run
  
  # 执行所有清理规则（预览）
  python cli.py clean --all --dry-run
  
  # 创建数据库备份
  python cli.py backup --create database_backup
  
  # 显示存储监控状态
  python cli.py monitor --status
  
  # 生成24小时监控报告
  python cli.py monitor --report --hours 24
        """
    )
    
    parser.add_argument('--data-root', default='data', help='数据根目录路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不实际执行操作')
    parser.add_argument('--output', '-o', help='输出文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 文件分类命令
    classify_parser = subparsers.add_parser('classify', help='文件分类管理')
    classify_group = classify_parser.add_mutually_exclusive_group(required=True)
    classify_group.add_argument('--report', action='store_true', help='生成分类报告')
    classify_group.add_argument('--organize', action='store_true', help='自动整理数据目录')
    classify_group.add_argument('--classify-file', help='分类指定文件')
    
    # 文件清理命令
    clean_parser = subparsers.add_parser('clean', help='文件清理管理')
    clean_group = clean_parser.add_mutually_exclusive_group(required=True)
    clean_group.add_argument('--report', action='store_true', help='生成清理报告')
    clean_group.add_argument('--rule', help='执行指定清理规则')
    clean_group.add_argument('--all', action='store_true', help='执行所有清理规则')
    clean_parser.add_argument('--exclude', help='排除的规则列表（逗号分隔）')
    
    # 备份管理命令
    backup_parser = subparsers.add_parser('backup', help='备份管理')
    backup_parser.add_argument('--backup-root', default='data/backups', help='备份根目录')
    backup_group = backup_parser.add_mutually_exclusive_group(required=True)
    backup_group.add_argument('--status', action='store_true', help='显示备份状态')
    backup_group.add_argument('--create', help='创建指定策略的备份')
    backup_group.add_argument('--restore', help='恢复指定备份ID')
    backup_group.add_argument('--cleanup', action='store_true', help='清理过期备份')
    backup_group.add_argument('--scheduled', action='store_true', help='执行计划备份')
    backup_parser.add_argument('--suffix', help='备份文件自定义后缀')
    backup_parser.add_argument('--restore-path', help='恢复路径')
    backup_parser.add_argument('--selective-files', help='选择性恢复的文件列表（逗号分隔）')
    backup_parser.add_argument('--strategy', help='指定策略名称')
    backup_parser.add_argument('--strategies', help='策略列表（逗号分隔）')
    
    # 存储监控命令
    monitor_parser = subparsers.add_parser('monitor', help='存储监控')
    monitor_parser.add_argument('--interval', type=int, default=300, help='监控间隔（秒）')
    monitor_group = monitor_parser.add_mutually_exclusive_group(required=True)
    monitor_group.add_argument('--status', action='store_true', help='显示当前状态')
    monitor_group.add_argument('--report', action='store_true', help='生成监控报告')
    monitor_group.add_argument('--start', action='store_true', help='启动后台监控')
    monitor_parser.add_argument('--hours', type=int, default=24, help='报告时间范围（小时）')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    setup_logging(args.verbose)
    
    try:
        if args.command == 'classify':
            classify_command(args)
        elif args.command == 'clean':
            clean_command(args)
        elif args.command == 'backup':
            backup_command(args)
        elif args.command == 'monitor':
            monitor_command(args)
    except KeyboardInterrupt:
        logger.info("\n操作被用户中断")
    except Exception as e:
        logger.info(f"执行出错: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()