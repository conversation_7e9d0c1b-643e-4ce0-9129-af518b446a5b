import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
数据文件管理系统测试

测试数据文件分类、清理、备份和监控功能。
"""

import unittest
import tempfile
import shutil
import json
from pathlib import Path
from datetime import datetime, timedelta
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from tools.data_management import (
    DataFileClassifier,
    DataFileCleaner,
    DataBackupManager,
    DataStorageMonitor
)


class TestDataFileClassifier(unittest.TestCase):
    """测试数据文件分类器"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.classifier = DataFileClassifier(str(self.test_dir))
        
        # 创建测试文件
        self.test_files = [
            "test_cache.pkl",
            "market_data.csv", 
            "trading_system.db",
            "export_report.xlsx",
            "backup_data.tar.gz",
            "trading_log.log"
        ]
        
        for filename in self.test_files:
            (self.test_dir / filename).touch()
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.test_dir)
    
    def test_classify_file(self):
        """测试文件分类功能"""
        # 测试缓存文件分类
        category = self.classifier.classify_file(str(self.test_dir / "test_cache.pkl"))
        self.assertEqual(category, "cache")
        
        # 测试CSV文件分类
        category = self.classifier.classify_file(str(self.test_dir / "market_data.csv"))
        self.assertEqual(category, "csv")
        
        # 测试数据库文件分类
        category = self.classifier.classify_file(str(self.test_dir / "trading_system.db"))
        self.assertEqual(category, "db")
    
    def test_classification_report(self):
        """测试分类报告生成"""
        report = self.classifier.get_classification_report()
        
        self.assertIn("timestamp", report)
        self.assertIn("data_root", report)
        self.assertIn("categories", report)
        
        # 检查分类目录是否存在
        for category in self.classifier.classification_rules.keys():
            self.assertIn(category, report["categories"])
    
    def test_auto_organize(self):
        """测试自动整理功能"""
        # 预览模式测试
        stats = self.classifier.auto_organize_data_directory(dry_run=True)
        
        self.assertIn("moved", stats)
        self.assertIn("skipped", stats)
        self.assertIn("errors", stats)
        
        # 验证文件数量
        expected_moved = len([f for f in self.test_files 
                            if self.classifier.classify_file(str(self.test_dir / f))])
        self.assertEqual(stats["moved"], expected_moved)


class TestDataFileCleaner(unittest.TestCase):
    """测试数据文件清理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.cleaner = DataFileCleaner(str(self.test_dir))
        
        # 创建测试目录结构
        (self.test_dir / "cache").mkdir()
        (self.test_dir / "logs").mkdir()
        
        # 创建测试文件（设置不同的修改时间）
        old_time = datetime.now() - timedelta(days=10)
        recent_time = datetime.now() - timedelta(hours=1)
        
        # 创建过期缓存文件
        old_cache = self.test_dir / "cache" / "old_cache.pkl"
        old_cache.touch()
        import os
        os.utime(old_cache, (old_time.timestamp(), old_time.timestamp()))
        
        # 创建最近的缓存文件
        recent_cache = self.test_dir / "cache" / "recent_cache.pkl"
        recent_cache.touch()
        os.utime(recent_cache, (recent_time.timestamp(), recent_time.timestamp()))
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.test_dir)
    
    def test_find_files_to_clean(self):
        """测试查找需要清理的文件"""
        files_to_clean = self.cleaner.find_files_to_clean("cache_files")
        
        # 应该找到过期的缓存文件
        self.assertTrue(len(files_to_clean) > 0)
        
        # 检查返回格式
        for file_path, reason in files_to_clean:
            self.assertIsInstance(file_path, Path)
            self.assertIsInstance(reason, str)
    
    def test_cleanup_report(self):
        """测试清理报告生成"""
        report = self.cleaner.get_cleanup_report()
        
        self.assertIn("timestamp", report)
        self.assertIn("data_root", report)
        self.assertIn("rules_analysis", report)
        
        # 检查规则分析
        for rule_name in self.cleaner.cleanup_rules.keys():
            self.assertIn(rule_name, report["rules_analysis"])
    
    def test_clean_files_dry_run(self):
        """测试清理文件（预览模式）"""
        stats = self.cleaner.clean_files_by_rule("cache_files", dry_run=True)
        
        self.assertIn("deleted", stats)
        self.assertIn("freed_mb", stats)
        self.assertIn("errors", stats)
        
        # 预览模式不应该实际删除文件
        cache_files = list((self.test_dir / "cache").glob("*.pkl"))
        self.assertTrue(len(cache_files) > 0)


class TestDataBackupManager(unittest.TestCase):
    """测试数据备份管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.backup_dir = self.test_dir / "backups"
        self.backup_manager = DataBackupManager(str(self.test_dir), str(self.backup_dir))
        
        # 创建测试数据目录和文件
        (self.test_dir / "db").mkdir()
        (self.test_dir / "db" / "test.db").touch()
        (self.test_dir / "db" / "test.sqlite").touch()
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.test_dir)
    
    def test_backup_status(self):
        """测试备份状态获取"""
        status = self.backup_manager.get_backup_status()
        
        self.assertIn("timestamp", status)
        self.assertIn("backup_root", status)
        self.assertIn("strategies", status)
        
        # 检查策略配置
        for strategy_name in self.backup_manager.backup_strategies.keys():
            self.assertIn(strategy_name, status["strategies"])
    
    def test_create_backup(self):
        """测试创建备份"""
        # 创建数据库备份
        backup_info = self.backup_manager.create_backup("database_backup")
        
        if backup_info:  # 如果有文件可以备份
            self.assertIn("backup_id", backup_info)
            self.assertIn("backup_path", backup_info)
            self.assertIn("file_count", backup_info)
            self.assertIn("backup_size_mb", backup_info)
            
            # 验证备份文件是否存在
            backup_path = Path(backup_info["backup_path"])
            self.assertTrue(backup_path.exists())
    
    def test_cleanup_old_backups(self):
        """测试清理过期备份"""
        # 预览模式测试
        stats = self.backup_manager.cleanup_old_backups(dry_run=True)
        
        self.assertIn("deleted", stats)
        self.assertIn("freed_mb", stats)
        self.assertIn("errors", stats)


class TestDataStorageMonitor(unittest.TestCase):
    """测试数据存储监控器"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.monitor = DataStorageMonitor(str(self.test_dir))
        
        # 创建测试目录结构
        (self.test_dir / "cache").mkdir()
        (self.test_dir / "db").mkdir()
        (self.test_dir / "exports").mkdir()
        
        # 创建测试文件
        (self.test_dir / "cache" / "test.pkl").touch()
        (self.test_dir / "db" / "test.db").touch()
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.test_dir)
    
    def test_get_disk_usage(self):
        """测试获取磁盘使用情况"""
        usage = self.monitor.get_disk_usage()
        
        self.assertIn("path", usage)
        self.assertIn("total_gb", usage)
        self.assertIn("used_gb", usage)
        self.assertIn("free_gb", usage)
        self.assertIn("usage_percent", usage)
        self.assertIn("timestamp", usage)
        
        # 验证数据类型
        self.assertIsInstance(usage["usage_percent"], (int, float))
        self.assertTrue(0 <= usage["usage_percent"] <= 100)
    
    def test_get_directory_stats(self):
        """测试获取目录统计信息"""
        stats = self.monitor.get_directory_stats()
        
        self.assertIn("path", stats)
        self.assertIn("timestamp", stats)
        self.assertIn("subdirectories", stats)
        self.assertIn("total_size_mb", stats)
        self.assertIn("total_files", stats)
        
        # 检查子目录统计
        self.assertIn("cache", stats["subdirectories"])
        self.assertIn("db", stats["subdirectories"])
    
    def test_collect_monitoring_data(self):
        """测试收集监控数据"""
        monitoring_data = self.monitor.collect_monitoring_data()
        
        self.assertIn("timestamp", monitoring_data)
        self.assertIn("disk_usage", monitoring_data)
        self.assertIn("directory_stats", monitoring_data)
        self.assertIn("system_info", monitoring_data)
        self.assertIn("alerts", monitoring_data)
        
        # 验证告警列表
        self.assertIsInstance(monitoring_data["alerts"], list)
    
    def test_monitoring_report(self):
        """测试监控报告生成"""
        # 先收集一些数据
        self.monitor.collect_monitoring_data()
        
        report = self.monitor.get_monitoring_report(hours=1)
        
        self.assertIn("report_period_hours", report)
        self.assertIn("report_timestamp", report)
        self.assertIn("current_status", report)
        self.assertIn("recommendations", report)


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = Path(tempfile.mkdtemp())
        
        # 创建完整的测试环境
        self.classifier = DataFileClassifier(str(self.test_dir))
        self.cleaner = DataFileCleaner(str(self.test_dir))
        self.backup_manager = DataBackupManager(str(self.test_dir))
        self.monitor = DataStorageMonitor(str(self.test_dir))
        
        # 创建测试文件
        test_files = [
            "test_data.csv",
            "cache_file.pkl", 
            "old_temp.tmp",
            "database.db"
        ]
        
        for filename in test_files:
            (self.test_dir / filename).touch()
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.test_dir)
    
    def test_full_workflow(self):
        """测试完整工作流程"""
        # 1. 文件分类
        classify_stats = self.classifier.auto_organize_data_directory(dry_run=True)
        self.assertGreater(classify_stats["moved"], 0)
        
        # 2. 监控状态
        monitoring_data = self.monitor.collect_monitoring_data()
        self.assertIn("directory_stats", monitoring_data)
        
        # 3. 清理报告
        cleanup_report = self.cleaner.get_cleanup_report()
        self.assertIn("rules_analysis", cleanup_report)
        
        # 4. 备份状态
        backup_status = self.backup_manager.get_backup_status()
        self.assertIn("strategies", backup_status)
        
        logger.info("集成测试完成：所有组件正常工作")


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_classes = [
        TestDataFileClassifier,
        TestDataFileCleaner,
        TestDataBackupManager,
        TestDataStorageMonitor,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    logger.info(f"\n测试结果:")
    logger.info(f"运行测试: {result.testsRun}")
    logger.info(f"失败: {len(result.failures)}")
    logger.info(f"错误: {len(result.errors)}")
    
    if result.failures:
        logger.info("\n失败的测试:")
        for test, traceback in result.failures:
            logger.info(f"- {test}: {traceback}")
    
    if result.errors:
        logger.info("\n错误的测试:")
        for test, traceback in result.errors:
            logger.info(f"- {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)