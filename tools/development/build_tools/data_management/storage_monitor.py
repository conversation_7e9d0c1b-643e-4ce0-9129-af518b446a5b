"""
数据存储监控工具

监控磁盘空间使用和文件增长情况。
"""

import os
import psutil
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import json
import threading
import time
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class DataStorageMonitor:
    """数据存储监控器"""
    
    def __init__(self, data_root: str = "data", monitoring_interval: int = 300):
        """
        初始化数据存储监控器
        
        Args:
            data_root: 数据根目录路径
            monitoring_interval: 监控间隔（秒）
        """
        self.data_root = Path(data_root)
        self.monitoring_interval = monitoring_interval
        self.monitoring_active = False
        self.monitoring_thread = None
        
        # 存储监控数据
        self.storage_history = deque(maxlen=1000)  # 保留最近1000条记录
        self.directory_stats = {}
        self.alert_thresholds = self._load_alert_thresholds()
        self.alerts = []
        
        self._lock = threading.Lock()
    
    def _load_alert_thresholds(self) -> Dict:
        """加载告警阈值配置"""
        return {
            # 磁盘空间告警阈值
            "disk_usage": {
                "warning_percent": 80,  # 磁盘使用率超过80%时警告
                "critical_percent": 90,  # 磁盘使用率超过90%时严重告警
                "description": "磁盘空间使用率告警"
            },
            # 目录大小告警阈值
            "directory_size": {
                "cache_max_mb": 1000,    # cache目录最大1GB
                "db_max_mb": 5000,       # db目录最大5GB
                "exports_max_mb": 2000,  # exports目录最大2GB
                "logs_max_mb": 500,      # logs目录最大500MB
                "description": "目录大小告警"
            },
            # 文件数量告警阈值
            "file_count": {
                "cache_max_files": 1000,   # cache目录最大1000个文件
                "exports_max_files": 500,  # exports目录最大500个文件
                "logs_max_files": 100,     # logs目录最大100个文件
                "description": "文件数量告警"
            },
            # 文件增长速度告警阈值
            "growth_rate": {
                "size_growth_mb_per_hour": 100,  # 每小时增长超过100MB时告警
                "file_growth_per_hour": 50,      # 每小时新增文件超过50个时告警
                "description": "文件增长速度告警"
            }
        }
    
    def get_disk_usage(self, path: str = None) -> Dict:
        """
        获取磁盘使用情况
        
        Args:
            path: 要检查的路径，默认为数据根目录
            
        Returns:
            磁盘使用情况字典
        """
        if path is None:
            path = self.data_root
        
        try:
            usage = psutil.disk_usage(str(path))
            
            return {
                "path": str(path),
                "total_gb": round(usage.total / (1024**3), 2),
                "used_gb": round(usage.used / (1024**3), 2),
                "free_gb": round(usage.free / (1024**3), 2),
                "usage_percent": round((usage.used / usage.total) * 100, 2),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"获取磁盘使用情况失败 {path}: {e}")
            return {}
    
    def get_directory_stats(self, directory: Path = None) -> Dict:
        """
        获取目录统计信息
        
        Args:
            directory: 要统计的目录，默认为数据根目录
            
        Returns:
            目录统计信息字典
        """
        if directory is None:
            directory = self.data_root
        
        stats = {
            "path": str(directory),
            "timestamp": datetime.now().isoformat(),
            "subdirectories": {},
            "total_size_mb": 0,
            "total_files": 0,
            "file_types": defaultdict(int),
            "largest_files": []
        }
        
        try:
            # 统计子目录
            for subdir in directory.iterdir():
                if subdir.is_dir() and not subdir.name.startswith('.'):
                    subdir_stats = self._get_single_directory_stats(subdir)
                    stats["subdirectories"][subdir.name] = subdir_stats
                    stats["total_size_mb"] += subdir_stats["size_mb"]
                    stats["total_files"] += subdir_stats["file_count"]
            
            # 统计根目录直接文件
            root_files = [f for f in directory.iterdir() if f.is_file()]
            for file_path in root_files:
                file_size = file_path.stat().st_size / (1024 * 1024)
                stats["total_size_mb"] += file_size
                stats["total_files"] += 1
                
                # 统计文件类型
                file_ext = file_path.suffix.lower()
                stats["file_types"][file_ext or "无扩展名"] += 1
                
                # 记录大文件
                if file_size > 10:  # 大于10MB的文件
                    stats["largest_files"].append({
                        "name": file_path.name,
                        "size_mb": round(file_size, 2),
                        "modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                    })
            
            # 排序最大文件列表
            stats["largest_files"].sort(key=lambda x: x["size_mb"], reverse=True)
            stats["largest_files"] = stats["largest_files"][:10]  # 只保留前10个
            
            stats["total_size_mb"] = round(stats["total_size_mb"], 2)
            
        except Exception as e:
            logger.error(f"获取目录统计失败 {directory}: {e}")
        
        return stats
    
    def _get_single_directory_stats(self, directory: Path) -> Dict:
        """
        获取单个目录的统计信息
        
        Args:
            directory: 目录路径
            
        Returns:
            目录统计信息
        """
        stats = {
            "size_mb": 0,
            "file_count": 0,
            "last_modified": 0,
            "file_types": defaultdict(int)
        }
        
        try:
            for file_path in directory.rglob("*"):
                if file_path.is_file():
                    file_size = file_path.stat().st_size / (1024 * 1024)
                    file_mtime = file_path.stat().st_mtime
                    
                    stats["size_mb"] += file_size
                    stats["file_count"] += 1
                    stats["last_modified"] = max(stats["last_modified"], file_mtime)
                    
                    # 统计文件类型
                    file_ext = file_path.suffix.lower()
                    stats["file_types"][file_ext or "无扩展名"] += 1
            
            stats["size_mb"] = round(stats["size_mb"], 2)
            if stats["last_modified"] > 0:
                stats["last_modified"] = datetime.fromtimestamp(stats["last_modified"]).isoformat()
            else:
                stats["last_modified"] = None
                
        except Exception as e:
            logger.error(f"获取目录统计失败 {directory}: {e}")
        
        return stats
    
    def check_alerts(self, current_stats: Dict) -> List[Dict]:
        """
        检查告警条件
        
        Args:
            current_stats: 当前统计数据
            
        Returns:
            告警列表
        """
        alerts = []
        timestamp = datetime.now().isoformat()
        
        # 检查磁盘使用率告警
        disk_usage = self.get_disk_usage()
        if disk_usage:
            usage_percent = disk_usage["usage_percent"]
            thresholds = self.alert_thresholds["disk_usage"]
            
            if usage_percent >= thresholds["critical_percent"]:
                alerts.append({
                    "type": "disk_usage",
                    "level": "critical",
                    "message": f"磁盘使用率达到 {usage_percent}%，超过严重告警阈值 {thresholds['critical_percent']}%",
                    "timestamp": timestamp,
                    "value": usage_percent,
                    "threshold": thresholds["critical_percent"]
                })
            elif usage_percent >= thresholds["warning_percent"]:
                alerts.append({
                    "type": "disk_usage",
                    "level": "warning",
                    "message": f"磁盘使用率达到 {usage_percent}%，超过警告阈值 {thresholds['warning_percent']}%",
                    "timestamp": timestamp,
                    "value": usage_percent,
                    "threshold": thresholds["warning_percent"]
                })
        
        # 检查目录大小告警
        dir_thresholds = self.alert_thresholds["directory_size"]
        for subdir_name, subdir_stats in current_stats.get("subdirectories", {}).items():
            threshold_key = f"{subdir_name}_max_mb"
            if threshold_key in dir_thresholds:
                max_size = dir_thresholds[threshold_key]
                current_size = subdir_stats["size_mb"]
                
                if current_size > max_size:
                    alerts.append({
                        "type": "directory_size",
                        "level": "warning",
                        "message": f"目录 {subdir_name} 大小 {current_size}MB 超过阈值 {max_size}MB",
                        "timestamp": timestamp,
                        "directory": subdir_name,
                        "value": current_size,
                        "threshold": max_size
                    })
        
        # 检查文件数量告警
        file_thresholds = self.alert_thresholds["file_count"]
        for subdir_name, subdir_stats in current_stats.get("subdirectories", {}).items():
            threshold_key = f"{subdir_name}_max_files"
            if threshold_key in file_thresholds:
                max_files = file_thresholds[threshold_key]
                current_files = subdir_stats["file_count"]
                
                if current_files > max_files:
                    alerts.append({
                        "type": "file_count",
                        "level": "warning",
                        "message": f"目录 {subdir_name} 文件数量 {current_files} 超过阈值 {max_files}",
                        "timestamp": timestamp,
                        "directory": subdir_name,
                        "value": current_files,
                        "threshold": max_files
                    })
        
        # 检查增长速度告警（需要历史数据）
        if len(self.storage_history) >= 2:
            alerts.extend(self._check_growth_rate_alerts(current_stats))
        
        return alerts
    
    def _check_growth_rate_alerts(self, current_stats: Dict) -> List[Dict]:
        """
        检查增长速度告警
        
        Args:
            current_stats: 当前统计数据
            
        Returns:
            增长速度告警列表
        """
        alerts = []
        
        # 获取1小时前的数据进行比较
        one_hour_ago = datetime.now() - timedelta(hours=1)
        historical_stats = None
        
        for stats in reversed(self.storage_history):
            stats_time = datetime.fromisoformat(stats["timestamp"])
            if stats_time <= one_hour_ago:
                historical_stats = stats
                break
        
        if not historical_stats:
            return alerts
        
        # 计算增长速度
        time_diff_hours = (
            datetime.fromisoformat(current_stats["timestamp"]) - 
            datetime.fromisoformat(historical_stats["timestamp"])
        ).total_seconds() / 3600
        
        if time_diff_hours > 0:
            # 大小增长速度
            size_growth = current_stats["total_size_mb"] - historical_stats["total_size_mb"]
            size_growth_rate = size_growth / time_diff_hours
            
            # 文件数量增长速度
            file_growth = current_stats["total_files"] - historical_stats["total_files"]
            file_growth_rate = file_growth / time_diff_hours
            
            growth_thresholds = self.alert_thresholds["growth_rate"]
            timestamp = datetime.now().isoformat()
            
            if size_growth_rate > growth_thresholds["size_growth_mb_per_hour"]:
                alerts.append({
                    "type": "growth_rate",
                    "level": "warning",
                    "message": f"数据大小增长速度 {size_growth_rate:.1f}MB/小时 超过阈值 {growth_thresholds['size_growth_mb_per_hour']}MB/小时",
                    "timestamp": timestamp,
                    "value": size_growth_rate,
                    "threshold": growth_thresholds["size_growth_mb_per_hour"]
                })
            
            if file_growth_rate > growth_thresholds["file_growth_per_hour"]:
                alerts.append({
                    "type": "growth_rate",
                    "level": "warning",
                    "message": f"文件数量增长速度 {file_growth_rate:.1f}个/小时 超过阈值 {growth_thresholds['file_growth_per_hour']}个/小时",
                    "timestamp": timestamp,
                    "value": file_growth_rate,
                    "threshold": growth_thresholds["file_growth_per_hour"]
                })
        
        return alerts
    
    def collect_monitoring_data(self) -> Dict:
        """
        收集监控数据
        
        Returns:
            监控数据字典
        """
        monitoring_data = {
            "timestamp": datetime.now().isoformat(),
            "disk_usage": self.get_disk_usage(),
            "directory_stats": self.get_directory_stats(),
            "system_info": {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_io": psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {}
            }
        }
        
        # 检查告警
        alerts = self.check_alerts(monitoring_data["directory_stats"])
        monitoring_data["alerts"] = alerts
        
        # 添加到历史记录
        with self._lock:
            self.storage_history.append(monitoring_data)
            if alerts:
                self.alerts.extend(alerts)
                # 只保留最近100个告警
                self.alerts = self.alerts[-100:]
        
        return monitoring_data
    
    def start_monitoring(self):
        """启动后台监控"""
        if self.monitoring_active:
            logger.warning("监控已经在运行中")
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        logger.info(f"存储监控已启动，监控间隔: {self.monitoring_interval}秒")
    
    def stop_monitoring(self):
        """停止后台监控"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("存储监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                monitoring_data = self.collect_monitoring_data()
                
                # 记录告警
                if monitoring_data["alerts"]:
                    for alert in monitoring_data["alerts"]:
                        logger.warning(f"存储告警: {alert['message']}")
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"监控循环出错: {e}")
                time.sleep(60)  # 出错时等待1分钟再继续
    
    def get_monitoring_report(self, hours: int = 24) -> Dict:
        """
        获取监控报告
        
        Args:
            hours: 报告时间范围（小时）
            
        Returns:
            监控报告字典
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 过滤指定时间范围内的数据
        recent_data = [
            data for data in self.storage_history
            if datetime.fromisoformat(data["timestamp"]) >= cutoff_time
        ]
        
        if not recent_data:
            return {"error": "没有足够的监控数据"}
        
        # 计算统计信息
        latest_data = recent_data[-1]
        oldest_data = recent_data[0]
        
        # 计算增长趋势
        size_growth = (
            latest_data["directory_stats"]["total_size_mb"] - 
            oldest_data["directory_stats"]["total_size_mb"]
        )
        file_growth = (
            latest_data["directory_stats"]["total_files"] - 
            oldest_data["directory_stats"]["total_files"]
        )
        
        # 统计告警
        recent_alerts = [
            alert for alert in self.alerts
            if datetime.fromisoformat(alert["timestamp"]) >= cutoff_time
        ]
        
        alert_summary = defaultdict(int)
        for alert in recent_alerts:
            alert_summary[alert["type"]] += 1
        
        report = {
            "report_period_hours": hours,
            "report_timestamp": datetime.now().isoformat(),
            "current_status": {
                "disk_usage": latest_data["disk_usage"],
                "total_size_mb": latest_data["directory_stats"]["total_size_mb"],
                "total_files": latest_data["directory_stats"]["total_files"],
                "subdirectories": latest_data["directory_stats"]["subdirectories"]
            },
            "growth_analysis": {
                "size_growth_mb": round(size_growth, 2),
                "file_growth": file_growth,
                "average_size_growth_per_hour": round(size_growth / hours, 2),
                "average_file_growth_per_hour": round(file_growth / hours, 2)
            },
            "alert_summary": {
                "total_alerts": len(recent_alerts),
                "alert_types": dict(alert_summary),
                "recent_alerts": recent_alerts[-10:]  # 最近10个告警
            },
            "recommendations": self._generate_recommendations(latest_data, size_growth, file_growth)
        }
        
        return report
    
    def _generate_recommendations(self, latest_data: Dict, size_growth: float, file_growth: int) -> List[str]:
        """
        生成优化建议
        
        Args:
            latest_data: 最新监控数据
            size_growth: 大小增长量
            file_growth: 文件增长量
            
        Returns:
            建议列表
        """
        recommendations = []
        
        # 磁盘空间建议
        disk_usage = latest_data["disk_usage"]["usage_percent"]
        if disk_usage > 85:
            recommendations.append("磁盘使用率较高，建议清理不必要的文件或扩展存储空间")
        
        # 目录大小建议
        subdirs = latest_data["directory_stats"]["subdirectories"]
        for dirname, stats in subdirs.items():
            if dirname == "cache" and stats["size_mb"] > 500:
                recommendations.append("缓存目录过大，建议执行缓存清理")
            elif dirname == "logs" and stats["size_mb"] > 200:
                recommendations.append("日志目录过大，建议执行日志轮转和清理")
            elif dirname == "exports" and stats["size_mb"] > 1000:
                recommendations.append("导出目录过大，建议清理旧的导出文件")
        
        # 增长速度建议
        if size_growth > 1000:  # 24小时内增长超过1GB
            recommendations.append("数据增长速度较快，建议检查是否有异常的数据写入")
        
        if file_growth > 1000:  # 24小时内新增超过1000个文件
            recommendations.append("文件数量增长较快，建议检查临时文件生成情况")
        
        # 文件类型建议
        file_types = latest_data["directory_stats"].get("file_types", {})
        if file_types.get(".tmp", 0) > 50:
            recommendations.append("临时文件数量较多，建议定期清理临时文件")
        
        if not recommendations:
            recommendations.append("当前存储状态良好，无需特殊处理")
        
        return recommendations
    
    def save_monitoring_report(self, output_path: str = None, hours: int = 24):
        """
        保存监控报告到文件
        
        Args:
            output_path: 输出文件路径
            hours: 报告时间范围
        """
        if output_path is None:
            reports_dir = self.data_root / "reports"
            reports_dir.mkdir(exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = reports_dir / f"storage_monitoring_report_{timestamp}.json"
        
        report = self.get_monitoring_report(hours)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"监控报告已保存到: {output_path}")


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建存储监控器实例
    monitor = DataStorageMonitor()
    
    # 收集当前监控数据
    logger.info("收集存储监控数据...")
    monitoring_data = monitor.collect_monitoring_data()
    
    # 显示当前状态
    logger.info(f"磁盘使用率: {monitoring_data['disk_usage']['usage_percent']}%")
    logger.info(f"数据总大小: {monitoring_data['directory_stats']['total_size_mb']}MB")
    logger.info(f"文件总数: {monitoring_data['directory_stats']['total_files']}")
    
    # 显示告警
    if monitoring_data["alerts"]:
        logger.info("\n当前告警:")
        for alert in monitoring_data["alerts"]:
            logger.info(f"- {alert['level'].upper()}: {alert['message']}")
    else:
        logger.info("\n无告警")
    
    # 生成监控报告
    logger.info("\n生成监控报告...")
    report = monitor.get_monitoring_report(hours=1)  # 1小时报告
    logger.info(json.dumps(report, ensure_ascii=False, indent=2))