"""
数据文件清理机制

自动清理过期的缓存和临时文件。
"""

import os
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import json
import fnmatch

logger = logging.getLogger(__name__)


class DataFileCleaner:
    """数据文件清理器"""
    
    def __init__(self, data_root: str = "data"):
        """
        初始化数据文件清理器
        
        Args:
            data_root: 数据根目录路径
        """
        self.data_root = Path(data_root)
        self.cleanup_rules = self._load_cleanup_rules()
        self.cleanup_log = []
    
    def _load_cleanup_rules(self) -> Dict:
        """加载清理规则配置"""
        return {
            # 缓存文件清理规则
            "cache_files": {
                "directories": ["cache"],
                "file_patterns": ["*.pkl", "*.cache", "*.tmp", "cache_*"],
                "max_age_days": 7,  # 7天后清理
                "max_size_mb": 500,  # 超过500MB时清理最旧的文件
                "keep_recent_count": 10,  # 保留最近的10个文件
                "description": "清理过期的缓存文件"
            },
            # 临时文件清理规则
            "temp_files": {
                "directories": ["cache", "exports", "logs"],
                "file_patterns": ["temp_*", "tmp_*", "*.tmp", "*.temp"],
                "max_age_days": 1,  # 1天后清理
                "max_size_mb": 100,
                "keep_recent_count": 5,
                "description": "清理临时文件"
            },
            # 日志文件清理规则
            "log_files": {
                "directories": ["logs"],
                "file_patterns": ["*.log", "*.log.*"],
                "max_age_days": 30,  # 30天后清理
                "max_size_mb": 1000,  # 超过1GB时清理
                "keep_recent_count": 50,
                "description": "清理过期的日志文件"
            },
            # 导出文件清理规则
            "export_files": {
                "directories": ["exports"],
                "file_patterns": ["export_*", "report_*", "*.xlsx", "*.pdf"],
                "max_age_days": 90,  # 90天后清理
                "max_size_mb": 2000,  # 超过2GB时清理
                "keep_recent_count": 100,
                "description": "清理过期的导出文件"
            },
            # 数据库备份文件清理规则
            "db_backup_files": {
                "directories": ["db", "backups"],
                "file_patterns": ["*.bak", "*.backup", "backup_*"],
                "max_age_days": 60,  # 60天后清理
                "max_size_mb": 1500,
                "keep_recent_count": 20,
                "description": "清理过期的数据库备份文件"
            }
        }
    
    def _get_file_age_days(self, file_path: Path) -> int:
        """
        获取文件的年龄（天数）
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件年龄（天数）
        """
        try:
            file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
            age = datetime.now() - file_mtime
            return age.days
        except Exception as e:
            logger.error(f"获取文件年龄失败 {file_path}: {e}")
            return 0
    
    def _get_file_size_mb(self, file_path: Path) -> float:
        """
        获取文件大小（MB）
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件大小（MB）
        """
        try:
            return file_path.stat().st_size / (1024 * 1024)
        except Exception as e:
            logger.error(f"获取文件大小失败 {file_path}: {e}")
            return 0
    
    def _matches_pattern(self, filename: str, patterns: List[str]) -> bool:
        """
        检查文件名是否匹配任何模式
        
        Args:
            filename: 文件名
            patterns: 模式列表
            
        Returns:
            是否匹配
        """
        return any(fnmatch.fnmatch(filename.lower(), pattern.lower()) for pattern in patterns)
    
    def find_files_to_clean(self, rule_name: str) -> List[Tuple[Path, str]]:
        """
        根据规则查找需要清理的文件
        
        Args:
            rule_name: 清理规则名称
            
        Returns:
            需要清理的文件列表 [(file_path, reason)]
        """
        if rule_name not in self.cleanup_rules:
            logger.error(f"未知的清理规则: {rule_name}")
            return []
        
        rule = self.cleanup_rules[rule_name]
        files_to_clean = []
        
        # 遍历指定目录
        for directory in rule["directories"]:
            dir_path = self.data_root / directory
            if not dir_path.exists():
                continue
            
            # 查找匹配的文件
            matching_files = []
            for file_path in dir_path.rglob("*"):
                if file_path.is_file() and self._matches_pattern(file_path.name, rule["file_patterns"]):
                    matching_files.append(file_path)
            
            # 按修改时间排序（最新的在前）
            matching_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
            
            # 应用清理规则
            for i, file_path in enumerate(matching_files):
                file_age = self._get_file_age_days(file_path)
                file_size = self._get_file_size_mb(file_path)
                
                # 检查年龄规则
                if file_age > rule["max_age_days"]:
                    files_to_clean.append((file_path, f"文件过期 ({file_age}天)"))
                    continue
                
                # 检查保留数量规则
                if i >= rule["keep_recent_count"]:
                    files_to_clean.append((file_path, f"超出保留数量 (第{i+1}个)"))
                    continue
            
            # 检查总大小规则
            total_size = sum(self._get_file_size_mb(f) for f in matching_files)
            if total_size > rule["max_size_mb"]:
                # 从最旧的文件开始删除，直到总大小符合要求
                current_size = total_size
                for file_path in reversed(matching_files):
                    if current_size <= rule["max_size_mb"]:
                        break
                    
                    file_size = self._get_file_size_mb(file_path)
                    if (file_path, f"文件过期 ({self._get_file_age_days(file_path)}天)") not in files_to_clean and \
                       (file_path, f"超出保留数量 (第{matching_files.index(file_path)+1}个)") not in files_to_clean:
                        files_to_clean.append((file_path, f"目录大小超限 ({total_size:.1f}MB)"))
                        current_size -= file_size
        
        return files_to_clean
    
    def clean_files_by_rule(self, rule_name: str, dry_run: bool = False) -> Dict[str, int]:
        """
        按规则清理文件
        
        Args:
            rule_name: 清理规则名称
            dry_run: 是否只是预览而不实际删除
            
        Returns:
            清理统计信息
        """
        stats = {"deleted": 0, "freed_mb": 0, "errors": 0}
        
        files_to_clean = self.find_files_to_clean(rule_name)
        
        for file_path, reason in files_to_clean:
            try:
                file_size = self._get_file_size_mb(file_path)
                
                if not dry_run:
                    file_path.unlink()
                    logger.info(f"已删除文件: {file_path} (原因: {reason})")
                else:
                    logger.info(f"[预览] 将删除文件: {file_path} (原因: {reason})")
                
                stats["deleted"] += 1
                stats["freed_mb"] += file_size
                
                # 记录清理日志
                self.cleanup_log.append({
                    "timestamp": datetime.now().isoformat(),
                    "action": "delete" if not dry_run else "preview",
                    "file_path": str(file_path),
                    "reason": reason,
                    "size_mb": file_size,
                    "rule": rule_name
                })
                
            except Exception as e:
                logger.error(f"删除文件失败 {file_path}: {e}")
                stats["errors"] += 1
        
        return stats
    
    def clean_all_rules(self, dry_run: bool = False, 
                       exclude_rules: List[str] = None) -> Dict[str, Dict[str, int]]:
        """
        执行所有清理规则
        
        Args:
            dry_run: 是否只是预览
            exclude_rules: 要排除的规则列表
            
        Returns:
            所有规则的清理统计信息
        """
        exclude_rules = exclude_rules or []
        all_stats = {}
        
        for rule_name in self.cleanup_rules:
            if rule_name in exclude_rules:
                logger.info(f"跳过清理规则: {rule_name}")
                continue
            
            logger.info(f"执行清理规则: {rule_name}")
            stats = self.clean_files_by_rule(rule_name, dry_run)
            all_stats[rule_name] = stats
            
            logger.info(f"规则 {rule_name} 完成: 删除 {stats['deleted']} 个文件, "
                       f"释放 {stats['freed_mb']:.1f}MB 空间")
        
        return all_stats
    
    def clean_empty_directories(self, dry_run: bool = False) -> int:
        """
        清理空目录
        
        Args:
            dry_run: 是否只是预览
            
        Returns:
            删除的空目录数量
        """
        deleted_count = 0
        
        # 从最深层开始检查，确保能删除嵌套的空目录
        for root, dirs, files in os.walk(self.data_root, topdown=False):
            root_path = Path(root)
            
            # 跳过根目录和重要目录
            if root_path == self.data_root:
                continue
            
            # 检查目录是否为空（只包含.gitkeep文件也算空）
            contents = list(root_path.iterdir())
            is_empty = len(contents) == 0 or (
                len(contents) == 1 and contents[0].name == ".gitkeep"
            )
            
            if is_empty:
                try:
                    if not dry_run:
                        shutil.rmtree(root_path)
                        logger.info(f"已删除空目录: {root_path}")
                    else:
                        logger.info(f"[预览] 将删除空目录: {root_path}")
                    
                    deleted_count += 1
                    
                except Exception as e:
                    logger.error(f"删除空目录失败 {root_path}: {e}")
        
        return deleted_count
    
    def get_cleanup_report(self) -> Dict:
        """
        获取清理报告
        
        Returns:
            清理报告字典
        """
        report = {
            "timestamp": datetime.now().isoformat(),
            "data_root": str(self.data_root),
            "rules_analysis": {},
            "cleanup_log": self.cleanup_log[-50:]  # 最近50条记录
        }
        
        # 分析每个规则的潜在清理情况
        for rule_name, rule in self.cleanup_rules.items():
            files_to_clean = self.find_files_to_clean(rule_name)
            total_size = sum(self._get_file_size_mb(f[0]) for f in files_to_clean)
            
            report["rules_analysis"][rule_name] = {
                "description": rule["description"],
                "files_to_clean": len(files_to_clean),
                "potential_freed_mb": round(total_size, 2),
                "max_age_days": rule["max_age_days"],
                "max_size_mb": rule["max_size_mb"]
            }
        
        return report
    
    def save_cleanup_log(self, output_path: str = None):
        """
        保存清理日志到文件
        
        Args:
            output_path: 输出文件路径
        """
        if output_path is None:
            logs_dir = self.data_root / "logs"
            logs_dir.mkdir(exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = logs_dir / f"cleanup_log_{timestamp}.json"
        
        log_data = {
            "cleanup_session": datetime.now().isoformat(),
            "cleanup_log": self.cleanup_log
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"清理日志已保存到: {output_path}")
    
    def schedule_cleanup(self, schedule_config: Dict = None) -> Dict:
        """
        计划清理任务
        
        Args:
            schedule_config: 计划配置
            
        Returns:
            计划执行结果
        """
        if schedule_config is None:
            schedule_config = {
                "daily_rules": ["temp_files", "cache_files"],
                "weekly_rules": ["log_files", "export_files"],
                "monthly_rules": ["db_backup_files"]
            }
        
        # 这里可以集成到系统的定时任务中
        # 目前只是返回建议的执行计划
        return {
            "daily_cleanup": schedule_config.get("daily_rules", []),
            "weekly_cleanup": schedule_config.get("weekly_rules", []),
            "monthly_cleanup": schedule_config.get("monthly_rules", []),
            "next_daily_run": (datetime.now() + timedelta(days=1)).isoformat(),
            "next_weekly_run": (datetime.now() + timedelta(weeks=1)).isoformat(),
            "next_monthly_run": (datetime.now() + timedelta(days=30)).isoformat()
        }


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建清理器实例
    cleaner = DataFileCleaner()
    
    # 生成清理报告
    logger.info("生成数据文件清理报告...")
    report = cleaner.get_cleanup_report()
    logger.info(json.dumps(report, ensure_ascii=False, indent=2))
    
    # 预览清理操作
    logger.info("\n预览清理操作...")
    stats = cleaner.clean_all_rules(dry_run=True)
    for rule_name, rule_stats in stats.items():
        logger.info(f"{rule_name}: 将删除 {rule_stats['deleted']} 个文件, "
              f"释放 {rule_stats['freed_mb']:.1f}MB")