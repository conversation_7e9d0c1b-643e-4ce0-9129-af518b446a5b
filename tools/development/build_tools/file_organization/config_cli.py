import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
配置管理系统命令行界面

本模块提供配置管理系统的命令行接口，支持配置加载、验证、
环境管理、模板管理等功能的命令行操作。

作者: 量化交易系统开发团队
版本: 1.0.0
"""

import sys
import json
import argparse
from pathlib import Path
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from tools.file_organization.config_management_system import ConfigurationManager
from tools.file_organization.config_loader import ConfigurationLoader
from tools.file_organization.config_validator import ConfigurationValidationManager
from tools.file_organization.environment_manager import EnvironmentManager
from tools.file_organization.template_manager import TemplateManager


class ConfigCLI:
    """配置管理系统命令行界面"""
    
    def __init__(self):
        """初始化CLI"""
        self.config_manager = ConfigurationManager()
        self.config_loader = ConfigurationLoader()
        self.validation_manager = ConfigurationValidationManager()
        self.env_manager = EnvironmentManager()
        self.template_manager = TemplateManager()
    
    def create_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description="量化交易系统配置管理工具",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
示例用法:
  %(prog)s load config --env development          # 加载开发环境配置
  %(prog)s validate config.yaml                   # 验证配置文件
  %(prog)s env list                               # 列出所有环境
  %(prog)s env create staging --from development  # 创建新环境
  %(prog)s template list                          # 列出所有模板
  %(prog)s template generate backend.env.template output.env --var HOST=localhost
            """
        )
        
        subparsers = parser.add_subparsers(dest='command', help='可用命令')
        
        # 配置加载命令
        self._add_load_parser(subparsers)
        
        # 配置验证命令
        self._add_validate_parser(subparsers)
        
        # 环境管理命令
        self._add_env_parser(subparsers)
        
        # 模板管理命令
        self._add_template_parser(subparsers)
        
        # 系统状态命令
        self._add_status_parser(subparsers)
        
        return parser
    
    def _add_load_parser(self, subparsers):
        """添加配置加载命令解析器"""
        load_parser = subparsers.add_parser('load', help='加载配置文件')
        load_parser.add_argument('config_name', help='配置文件名')
        load_parser.add_argument('--env', '--environment', help='环境名称')
        load_parser.add_argument('--format', choices=['yaml', 'json'], default='yaml', help='输出格式')
        load_parser.add_argument('--output', '-o', help='输出文件路径')
        load_parser.add_argument('--validate', action='store_true', help='加载后验证配置')
        load_parser.add_argument('--no-cache', action='store_true', help='不使用缓存')
    
    def _add_validate_parser(self, subparsers):
        """添加配置验证命令解析器"""
        validate_parser = subparsers.add_parser('validate', help='验证配置文件')
        validate_parser.add_argument('config_file', help='配置文件路径')
        validate_parser.add_argument('--validator', default='default', help='验证器名称')
        validate_parser.add_argument('--strict', action='store_true', help='严格模式，警告也视为错误')
    
    def _add_env_parser(self, subparsers):
        """添加环境管理命令解析器"""
        env_parser = subparsers.add_parser('env', help='环境管理')
        env_subparsers = env_parser.add_subparsers(dest='env_command', help='环境管理命令')
        
        # 列出环境
        env_subparsers.add_parser('list', help='列出所有环境')
        
        # 显示当前环境
        env_subparsers.add_parser('current', help='显示当前环境')
        
        # 切换环境
        switch_parser = env_subparsers.add_parser('switch', help='切换环境')
        switch_parser.add_argument('environment', help='目标环境名称')
        
        # 创建环境
        create_parser = env_subparsers.add_parser('create', help='创建新环境')
        create_parser.add_argument('environment', help='环境名称')
        create_parser.add_argument('--from', dest='from_env', help='从指定环境复制配置')
        create_parser.add_argument('--description', help='环境描述')
        create_parser.add_argument('--config', help='配置文件路径')
        
        # 更新环境
        update_parser = env_subparsers.add_parser('update', help='更新环境配置')
        update_parser.add_argument('environment', help='环境名称')
        update_parser.add_argument('--config', help='配置文件路径')
        update_parser.add_argument('--merge', action='store_true', help='合并更新而不是替换')
        
        # 删除环境
        delete_parser = env_subparsers.add_parser('delete', help='删除环境')
        delete_parser.add_argument('environment', help='环境名称')
        delete_parser.add_argument('--force', action='store_true', help='强制删除（即使是当前环境）')
        
        # 导出环境
        export_parser = env_subparsers.add_parser('export', help='导出环境配置')
        export_parser.add_argument('environment', help='环境名称')
        export_parser.add_argument('--output', '-o', help='输出文件路径')
        export_parser.add_argument('--format', choices=['yaml', 'json'], default='yaml', help='输出格式')
        
        # 导入环境
        import_parser = env_subparsers.add_parser('import', help='导入环境配置')
        import_parser.add_argument('environment', help='环境名称')
        import_parser.add_argument('config_file', help='配置文件路径')
        import_parser.add_argument('--overwrite', action='store_true', help='覆盖已存在的环境')
        
        # 验证环境
        validate_parser = env_subparsers.add_parser('validate', help='验证环境配置')
        validate_parser.add_argument('environment', help='环境名称')
        
        # 比较环境
        diff_parser = env_subparsers.add_parser('diff', help='比较两个环境的配置')
        diff_parser.add_argument('env1', help='第一个环境')
        diff_parser.add_argument('env2', help='第二个环境')
    
    def _add_template_parser(self, subparsers):
        """添加模板管理命令解析器"""
        template_parser = subparsers.add_parser('template', help='模板管理')
        template_subparsers = template_parser.add_subparsers(dest='template_command', help='模板管理命令')
        
        # 列出模板
        list_parser = template_subparsers.add_parser('list', help='列出所有模板')
        list_parser.add_argument('--category', help='按分类过滤')
        list_parser.add_argument('--tag', action='append', help='按标签过滤')
        
        # 显示模板信息
        info_parser = template_subparsers.add_parser('info', help='显示模板信息')
        info_parser.add_argument('template_name', help='模板名称')
        
        # 创建模板
        create_parser = template_subparsers.add_parser('create', help='创建新模板')
        create_parser.add_argument('template_name', help='模板名称')
        create_parser.add_argument('template_file', help='模板内容文件')
        create_parser.add_argument('--description', help='模板描述')
        create_parser.add_argument('--category', default='general', help='模板分类')
        create_parser.add_argument('--tag', action='append', help='模板标签')
        
        # 生成配置
        generate_parser = template_subparsers.add_parser('generate', help='从模板生成配置文件')
        generate_parser.add_argument('template_name', help='模板名称')
        generate_parser.add_argument('output_file', help='输出文件路径')
        generate_parser.add_argument('--var', action='append', help='模板变量，格式：key=value')
        generate_parser.add_argument('--vars-file', help='变量文件路径（JSON或YAML）')
        generate_parser.add_argument('--format', choices=['auto', 'yaml', 'json', 'env'], default='auto', help='输出格式')
        
        # 预览模板
        preview_parser = template_subparsers.add_parser('preview', help='预览模板生成结果')
        preview_parser.add_argument('template_name', help='模板名称')
        preview_parser.add_argument('--var', action='append', help='模板变量，格式：key=value')
        preview_parser.add_argument('--vars-file', help='变量文件路径（JSON或YAML）')
        
        # 验证模板变量
        validate_parser = template_subparsers.add_parser('validate', help='验证模板变量')
        validate_parser.add_argument('template_name', help='模板名称')
        validate_parser.add_argument('--var', action='append', help='模板变量，格式：key=value')
        validate_parser.add_argument('--vars-file', help='变量文件路径（JSON或YAML）')
        
        # 删除模板
        delete_parser = template_subparsers.add_parser('delete', help='删除模板')
        delete_parser.add_argument('template_name', help='模板名称')
        delete_parser.add_argument('--no-backup', action='store_true', help='不创建备份')
        
        # 导出模板
        export_parser = template_subparsers.add_parser('export', help='导出模板')
        export_parser.add_argument('template_name', help='模板名称')
        export_parser.add_argument('--output', '-o', help='输出文件路径')
        
        # 导入模板
        import_parser = template_subparsers.add_parser('import', help='导入模板')
        import_parser.add_argument('template_file', help='模板文件路径')
        import_parser.add_argument('--name', help='模板名称（默认使用文件名）')
        import_parser.add_argument('--overwrite', action='store_true', help='覆盖已存在的模板')
    
    def _add_status_parser(self, subparsers):
        """添加系统状态命令解析器"""
        status_parser = subparsers.add_parser('status', help='显示系统状态')
        status_parser.add_argument('--format', choices=['text', 'json'], default='text', help='输出格式')
    
    def run(self, args: List[str] = None) -> int:
        """运行CLI"""
        parser = self.create_parser()
        parsed_args = parser.parse_args(args)
        
        if not parsed_args.command:
            parser.print_help()
            return 1
        
        try:
            if parsed_args.command == 'load':
                return self._handle_load(parsed_args)
            elif parsed_args.command == 'validate':
                return self._handle_validate(parsed_args)
            elif parsed_args.command == 'env':
                return self._handle_env(parsed_args)
            elif parsed_args.command == 'template':
                return self._handle_template(parsed_args)
            elif parsed_args.command == 'status':
                return self._handle_status(parsed_args)
            else:
                logger.info(f"未知命令: {parsed_args.command}")
                return 1
                
        except Exception as e:
            logger.info(f"❌ 错误: {str(e)}")
            return 1
    
    def _handle_load(self, args) -> int:
        """处理配置加载命令"""
        try:
            config_data = self.config_loader.load_config(
                args.config_name, 
                args.env, 
                use_cache=not args.no_cache
            )
            
            if args.validate:
                result = self.validation_manager.validate_config(config_data)
                if not result.is_valid:
                    logger.info("⚠️ 配置验证发现问题:")
                    for error in result.errors:
                        logger.info(f"  - {error}")
                if result.warnings:
                    logger.info("⚠️ 配置警告:")
                    for warning in result.warnings:
                        logger.info(f"  - {warning}")
            
            # 输出配置
            if args.format == 'json':
                output = json.dumps(config_data, indent=2, ensure_ascii=False)
            else:
                import yaml
                output = yaml.dump(config_data, default_flow_style=False, allow_unicode=True)
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(output)
                logger.info(f"✅ 配置已保存到: {args.output}")
            else:
                logger.info(output)
            
            return 0
            
        except Exception as e:
            logger.info(f"❌ 加载配置失败: {str(e)}")
            return 1
    
    def _handle_validate(self, args) -> int:
        """处理配置验证命令"""
        try:
            result = self.validation_manager.validate_config_file(
                args.config_file, args.validator
            )
            
            if result.is_valid:
                logger.info("✅ 配置验证通过")
                if result.warnings:
                    logger.info("⚠️ 警告:")
                    for warning in result.warnings:
                        logger.info(f"  - {warning}")
                return 0
            else:
                logger.info("❌ 配置验证失败:")
                for error in result.errors:
                    logger.info(f"  - {error}")
                
                if result.warnings and not args.strict:
                    logger.info("⚠️ 警告:")
                    for warning in result.warnings:
                        logger.info(f"  - {warning}")
                
                return 1 if args.strict and result.warnings else (1 if result.errors else 0)
                
        except Exception as e:
            logger.info(f"❌ 验证失败: {str(e)}")
            return 1
    
    def _handle_env(self, args) -> int:
        """处理环境管理命令"""
        try:
            if args.env_command == 'list':
                environments = self.env_manager.list_environments()
                logger.info(f"📋 可用环境 ({len(environments)} 个):")
                for env_info in environments:
                    status = "✅ 当前" if env_info.is_active else "⭕"
                    logger.info(f"  {status} {env_info.name}: {env_info.description}")
                    if env_info.tags:
                        logger.info(f"      标签: {', '.join(env_info.tags)}")
            
            elif args.env_command == 'current':
                current = self.env_manager.get_current_environment()
                logger.info(f"📍 当前环境: {current}")
            
            elif args.env_command == 'switch':
                self.env_manager.set_current_environment(args.environment)
                logger.info(f"✅ 已切换到环境: {args.environment}")
            
            elif args.env_command == 'create':
                if args.from_env:
                    self.env_manager.copy_environment(
                        args.from_env, args.environment, 
                        args.description or f"从 {args.from_env} 复制的环境"
                    )
                elif args.config:
                    self.env_manager.import_environment(args.environment, args.config)
                else:
                    # 创建空环境
                    self.env_manager.create_environment(
                        args.environment, 
                        {'environment': args.environment},
                        args.description or f"{args.environment} 环境"
                    )
                logger.info(f"✅ 成功创建环境: {args.environment}")
            
            elif args.env_command == 'update':
                if not args.config:
                    logger.info("❌ 需要指定配置文件路径")
                    return 1
                
                import yaml
                with open(args.config, 'r', encoding='utf-8') as f:
                    if args.config.endswith('.json'):
                        config_data = json.load(f)
                    else:
                        config_data = yaml.safe_load(f)
                
                self.env_manager.update_environment(
                    args.environment, config_data, merge=args.merge
                )
                logger.info(f"✅ 成功更新环境: {args.environment}")
            
            elif args.env_command == 'delete':
                self.env_manager.delete_environment(args.environment, force=args.force)
                logger.info(f"✅ 成功删除环境: {args.environment}")
            
            elif args.env_command == 'export':
                output_file = self.env_manager.export_environment(
                    args.environment, args.output, args.format
                )
                logger.info(f"✅ 环境配置已导出: {output_file}")
            
            elif args.env_command == 'import':
                self.env_manager.import_environment(
                    args.environment, args.config_file, overwrite=args.overwrite
                )
                logger.info(f"✅ 成功导入环境: {args.environment}")
            
            elif args.env_command == 'validate':
                errors = self.env_manager.validate_environment(args.environment)
                if errors:
                    logger.info("❌ 环境验证失败:")
                    for error in errors:
                        logger.info(f"  - {error}")
                    return 1
                else:
                    logger.info("✅ 环境验证通过")
            
            elif args.env_command == 'diff':
                diff = self.env_manager.get_environment_diff(args.env1, args.env2)
                logger.info(f"📊 环境差异: {args.env1} vs {args.env2}")
                
                if diff['added']:
                    logger.info("➕ 新增配置:")
                    for key in diff['added']:
                        logger.info(f"  + {key}")
                
                if diff['removed']:
                    logger.info("➖ 删除配置:")
                    for key in diff['removed']:
                        logger.info(f"  - {key}")
                
                if diff['modified']:
                    logger.info("🔄 修改配置:")
                    for key in diff['modified']:
                        logger.info(f"  ~ {key}")
                
                if not any(diff.values()):
                    logger.info("✅ 两个环境配置相同")
            
            return 0
            
        except Exception as e:
            logger.info(f"❌ 环境操作失败: {str(e)}")
            return 1
    
    def _handle_template(self, args) -> int:
        """处理模板管理命令"""
        try:
            if args.template_command == 'list':
                templates = self.template_manager.list_templates(
                    category=args.category, tags=args.tag
                )
                logger.info(f"📄 可用模板 ({len(templates)} 个):")
                for template_info in templates:
                    logger.info(f"  - {template_info.name}: {template_info.description}")
                    logger.info(f"    分类: {template_info.category}, 版本: {template_info.version}")
                    if template_info.tags:
                        logger.info(f"    标签: {', '.join(template_info.tags)}")
                    if template_info.variables:
                        var_names = [v.name for v in template_info.variables]
                        logger.info(f"    变量: {', '.join(var_names)}")
                    logger.info()
            
            elif args.template_command == 'info':
                template_info = self.template_manager.get_template_info(args.template_name)
                if not template_info:
                    logger.info(f"❌ 模板不存在: {args.template_name}")
                    return 1
                
                logger.info(f"📄 模板信息: {template_info.name}")
                logger.info(f"描述: {template_info.description}")
                logger.info(f"分类: {template_info.category}")
                logger.info(f"版本: {template_info.version}")
                logger.info(f"作者: {template_info.author}")
                
                if template_info.tags:
                    logger.info(f"标签: {', '.join(template_info.tags)}")
                
                if template_info.variables:
                    logger.info("变量:")
                    for var in template_info.variables:
                        required = "必需" if var.is_required else "可选"
                        default = f" (默认: {var.default_value})" if var.default_value else ""
                        logger.info(f"  - {var.name} ({var.variable_type}, {required}): {var.description}{default}")
            
            elif args.template_command == 'create':
                with open(args.template_file, 'r', encoding='utf-8') as f:
                    template_content = f.read()
                
                self.template_manager.create_template(
                    args.template_name, template_content,
                    description=args.description or "",
                    category=args.category,
                    tags=args.tag or []
                )
                logger.info(f"✅ 成功创建模板: {args.template_name}")
            
            elif args.template_command == 'generate':
                variables = self._parse_variables(args.var, args.vars_file)
                
                self.template_manager.generate_config_from_template(
                    args.template_name, args.output_file,
                    variables=variables, format=args.format
                )
                logger.info(f"✅ 成功生成配置文件: {args.output_file}")
            
            elif args.template_command == 'preview':
                variables = self._parse_variables(args.var, args.vars_file)
                
                preview = self.template_manager.preview_template(
                    args.template_name, variables
                )
                logger.info("👀 模板预览:")
                logger.info("-" * 50)
                logger.info(preview)
                logger.info("-" * 50)
            
            elif args.template_command == 'validate':
                variables = self._parse_variables(args.var, args.vars_file)
                
                errors = self.template_manager.validate_template_variables(
                    args.template_name, variables
                )
                
                if errors:
                    logger.info("❌ 模板变量验证失败:")
                    for error in errors:
                        logger.info(f"  - {error}")
                    return 1
                else:
                    logger.info("✅ 模板变量验证通过")
            
            elif args.template_command == 'delete':
                self.template_manager.delete_template(
                    args.template_name, create_backup=not args.no_backup
                )
                logger.info(f"✅ 成功删除模板: {args.template_name}")
            
            elif args.template_command == 'export':
                output_file = self.template_manager.export_template(
                    args.template_name, args.output
                )
                logger.info(f"✅ 模板已导出: {output_file}")
            
            elif args.template_command == 'import':
                self.template_manager.import_template(
                    args.template_file, template_name=args.name,
                    overwrite=args.overwrite
                )
                template_name = args.name or Path(args.template_file).stem
                logger.info(f"✅ 成功导入模板: {template_name}")
            
            return 0
            
        except Exception as e:
            logger.info(f"❌ 模板操作失败: {str(e)}")
            return 1
    
    def _handle_status(self, args) -> int:
        """处理系统状态命令"""
        try:
            status = self.config_manager.get_system_status()
            
            if args.format == 'json':
                logger.info(json.dumps(status, indent=2, ensure_ascii=False))
            else:
                logger.info("🔧 配置管理系统状态")
                logger.info("=" * 50)
                logger.info(f"📁 配置目录: {status['config_directory']}")
                logger.info(f"🌍 当前环境: {status['current_environment']}")
                logger.info(f"📋 可用环境: {', '.join(status['available_environments'])}")
                logger.info(f"📄 可用模板: {', '.join(status['available_templates'])}")
                logger.info(f"💾 已加载配置: {', '.join(status['loaded_configurations'])}")
                logger.info(f"✅ 验证规则数量: {status['validation_rules_count']}")
            
            return 0
            
        except Exception as e:
            logger.info(f"❌ 获取系统状态失败: {str(e)}")
            return 1
    
    def _parse_variables(self, var_args: Optional[List[str]], 
                        vars_file: Optional[str]) -> Dict[str, str]:
        """解析模板变量"""
        variables = {}
        
        # 从命令行参数解析变量
        if var_args:
            for var_arg in var_args:
                if '=' not in var_arg:
                    raise ValueError(f"变量格式错误: {var_arg}，应为 key=value")
                key, value = var_arg.split('=', 1)
                variables[key.strip()] = value.strip()
        
        # 从文件加载变量
        if vars_file:
            vars_path = Path(vars_file)
            if not vars_path.exists():
                raise FileNotFoundError(f"变量文件不存在: {vars_file}")
            
            with open(vars_path, 'r', encoding='utf-8') as f:
                if vars_file.endswith('.json'):
                    file_vars = json.load(f)
                else:
                    import yaml
                    file_vars = yaml.safe_load(f)
            
            # 文件中的变量会被命令行参数覆盖
            for key, value in file_vars.items():
                if key not in variables:
                    variables[key] = str(value)
        
        return variables


def main():
    """主函数"""
    cli = ConfigCLI()
    return cli.run()


if __name__ == "__main__":
    sys.exit(main())