#!/usr/bin/env python3
"""
文件组织监控和告警系统

提供目录结构监控、文件放置规范检查、存储空间监控和配置文件变更追踪功能。
"""

import os
import json
import time
import shutil
import hashlib
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import psutil


@dataclass
class DirectoryChangeEvent:
    """目录变更事件"""
    event_type: str  # created, deleted, modified, moved
    path: str
    timestamp: datetime
    old_path: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class ComplianceViolation:
    """规范违规记录"""
    file_path: str
    violation_type: str
    expected_location: str
    severity: str  # low, medium, high
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class StorageMetrics:
    """存储指标"""
    total_space: int
    used_space: int
    free_space: int
    usage_percentage: float
    file_count: int
    directory_count: int
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class ConfigChangeEvent:
    """配置文件变更事件"""
    config_path: str
    change_type: str  # modified, created, deleted
    old_hash: Optional[str]
    new_hash: Optional[str]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


class DirectoryStructureMonitor(FileSystemEventHandler):
    """目录结构变更监控器"""
    
    def __init__(self, project_root: str, log_file: str = "logs/monitoring/directory_changes.log"):
        self.project_root = Path(project_root)
        self.log_file = Path(log_file)
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.logger = logging.getLogger('directory_monitor')
        self.logger.setLevel(logging.INFO)
        
        # 创建文件处理器
        handler = logging.FileHandler(self.log_file, encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        
        # 变更事件存储
        self.events: List[DirectoryChangeEvent] = []
        self.events_file = self.log_file.parent / "directory_events.json"
        
        # 加载历史事件
        self._load_events()
    
    def on_created(self, event):
        """处理创建事件"""
        if event.is_directory:
            change_event = DirectoryChangeEvent(
                event_type="created",
                path=str(Path(event.src_path).relative_to(self.project_root)),
                timestamp=datetime.now()
            )
            self._record_event(change_event)
    
    def on_deleted(self, event):
        """处理删除事件"""
        if event.is_directory:
            change_event = DirectoryChangeEvent(
                event_type="deleted",
                path=str(Path(event.src_path).relative_to(self.project_root)),
                timestamp=datetime.now()
            )
            self._record_event(change_event)
    
    def on_moved(self, event):
        """处理移动事件"""
        if event.is_directory:
            change_event = DirectoryChangeEvent(
                event_type="moved",
                path=str(Path(event.dest_path).relative_to(self.project_root)),
                old_path=str(Path(event.src_path).relative_to(self.project_root)),
                timestamp=datetime.now()
            )
            self._record_event(change_event)
    
    def _record_event(self, event: DirectoryChangeEvent):
        """记录变更事件"""
        self.events.append(event)
        self.logger.info(f"目录结构变更: {event.event_type} - {event.path}")
        
        # 保存事件到文件
        self._save_events()
        
        # 检查是否需要告警
        self._check_alerts(event)
    
    def _load_events(self):
        """加载历史事件"""
        if self.events_file.exists():
            try:
                with open(self.events_file, 'r', encoding='utf-8') as f:
                    events_data = json.load(f)
                    for event_data in events_data:
                        event_data['timestamp'] = datetime.fromisoformat(event_data['timestamp'])
                        self.events.append(DirectoryChangeEvent(**event_data))
            except Exception as e:
                self.logger.error(f"加载历史事件失败: {e}")
    
    def _save_events(self):
        """保存事件到文件"""
        try:
            # 只保留最近30天的事件
            cutoff_date = datetime.now() - timedelta(days=30)
            recent_events = [e for e in self.events if e.timestamp > cutoff_date]
            
            with open(self.events_file, 'w', encoding='utf-8') as f:
                json.dump([e.to_dict() for e in recent_events], f, ensure_ascii=False, indent=2)
            
            self.events = recent_events
        except Exception as e:
            self.logger.error(f"保存事件失败: {e}")
    
    def _check_alerts(self, event: DirectoryChangeEvent):
        """检查是否需要告警"""
        # 检查关键目录的变更
        critical_dirs = ['src', 'config', 'tests', 'docs', 'web_ui']
        
        if any(event.path.startswith(dir_name) for dir_name in critical_dirs):
            self.logger.warning(f"关键目录变更告警: {event.event_type} - {event.path}")
    
    def get_recent_changes(self, hours: int = 24) -> List[DirectoryChangeEvent]:
        """获取最近的变更"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [e for e in self.events if e.timestamp > cutoff_time]


class FileComplianceChecker:
    """文件放置规范检查器"""
    
    def __init__(self, project_root: str, rules_file: str = "tools/file_organization/compliance_rules.json"):
        self.project_root = Path(project_root)
        self.rules_file = Path(rules_file)
        self.violations: List[ComplianceViolation] = []
        
        # 设置日志
        self.logger = logging.getLogger('compliance_checker')
        self.logger.setLevel(logging.INFO)
        
        # 加载规范规则
        self.rules = self._load_rules()
    
    def _load_rules(self) -> Dict[str, Any]:
        """加载规范规则"""
        default_rules = {
            "file_patterns": {
                "*.py": {
                    "allowed_dirs": ["src", "tests", "tools", "scripts", "examples"],
                    "forbidden_dirs": ["data", "logs", "docs"]
                },
                "*.md": {
                    "allowed_dirs": ["docs", ".", "examples"],
                    "forbidden_dirs": ["src", "data", "logs"]
                },
                "*.yaml": {
                    "allowed_dirs": ["config", "tools"],
                    "forbidden_dirs": ["src", "data", "logs"]
                },
                "*.json": {
                    "allowed_dirs": ["config", "tools", "data"],
                    "forbidden_dirs": ["src"]
                }
            },
            "directory_rules": {
                "src": {
                    "max_files_per_dir": 15,
                    "required_subdirs": ["__init__.py"]
                },
                "tests": {
                    "max_files_per_dir": 20,
                    "mirror_src_structure": True
                },
                "config": {
                    "max_files_per_dir": 10,
                    "allowed_extensions": [".yaml", ".json", ".env"]
                }
            }
        }
        
        if self.rules_file.exists():
            try:
                with open(self.rules_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"加载规范规则失败: {e}")
                return default_rules
        else:
            # 创建默认规则文件
            self.rules_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.rules_file, 'w', encoding='utf-8') as f:
                json.dump(default_rules, f, ensure_ascii=False, indent=2)
            return default_rules
    
    def check_compliance(self) -> List[ComplianceViolation]:
        """检查文件放置规范"""
        self.violations.clear()
        
        # 检查文件放置规则
        self._check_file_placement()
        
        # 检查目录规则
        self._check_directory_rules()
        
        # 保存违规记录
        self._save_violations()
        
        return self.violations
    
    def _should_ignore_path(self, path: Path) -> bool:
        """检查路径是否应该被忽略"""
        ignore_patterns = self.rules.get("ignore_patterns", [])
        relative_path = str(path.relative_to(self.project_root))
        
        # 检查特殊目录
        special_dirs = self.rules.get("special_directories", {})
        for dir_name, dir_info in special_dirs.items():
            if dir_info.get("ignore_monitoring", False):
                if relative_path.startswith(dir_name + "/") or relative_path == dir_name:
                    return True
        
        for pattern in ignore_patterns:
            # 简单的模式匹配
            if pattern.endswith("/**"):
                # 目录匹配
                dir_pattern = pattern[:-3]
                if relative_path.startswith(dir_pattern + "/") or relative_path == dir_pattern:
                    return True
            elif pattern.endswith("**"):
                # 递归匹配
                dir_pattern = pattern[:-2]
                if relative_path.startswith(dir_pattern):
                    return True
            elif "*" in pattern:
                # 通配符匹配（简单实现）
                import fnmatch
                if fnmatch.fnmatch(relative_path, pattern):
                    return True
            else:
                # 精确匹配
                if relative_path == pattern or relative_path.endswith("/" + pattern):
                    return True
        
        return False

    def _check_file_placement(self):
        """检查文件放置规则"""
        file_patterns = self.rules.get("file_patterns", {})
        
        for pattern, rules in file_patterns.items():
            # 查找匹配的文件
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file() and not self._should_ignore_path(file_path):
                    relative_path = file_path.relative_to(self.project_root)
                    dir_parts = relative_path.parts[:-1]  # 排除文件名
                    
                    if dir_parts:
                        top_dir = dir_parts[0]
                        
                        # 检查是否在允许的目录中
                        allowed_dirs = rules.get("allowed_dirs", [])
                        forbidden_dirs = rules.get("forbidden_dirs", [])
                        
                        if allowed_dirs and top_dir not in allowed_dirs:
                            violation = ComplianceViolation(
                                file_path=str(relative_path),
                                violation_type="wrong_directory",
                                expected_location=f"应该在以下目录之一: {', '.join(allowed_dirs)}",
                                severity="medium",
                                timestamp=datetime.now()
                            )
                            self.violations.append(violation)
                        
                        if forbidden_dirs and top_dir in forbidden_dirs:
                            violation = ComplianceViolation(
                                file_path=str(relative_path),
                                violation_type="forbidden_directory",
                                expected_location=f"不应该在目录: {top_dir}",
                                severity="high",
                                timestamp=datetime.now()
                            )
                            self.violations.append(violation)
    
    def _check_directory_rules(self):
        """检查目录规则"""
        directory_rules = self.rules.get("directory_rules", {})
        
        for dir_name, rules in directory_rules.items():
            dir_path = self.project_root / dir_name
            
            if not dir_path.exists():
                continue
            
            # 检查每个子目录的文件数量
            max_files = rules.get("max_files_per_dir", float('inf'))
            
            for subdir in dir_path.rglob("*"):
                if subdir.is_dir() and not self._should_ignore_path(subdir):
                    file_count = len([f for f in subdir.iterdir() if f.is_file() and not self._should_ignore_path(f)])
                    
                    if file_count > max_files:
                        violation = ComplianceViolation(
                            file_path=str(subdir.relative_to(self.project_root)),
                            violation_type="too_many_files",
                            expected_location=f"目录文件数量不应超过 {max_files}，当前: {file_count}",
                            severity="low",
                            timestamp=datetime.now()
                        )
                        self.violations.append(violation)
    
    def _save_violations(self):
        """保存违规记录"""
        violations_file = Path("logs/monitoring/compliance_violations.json")
        violations_file.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(violations_file, 'w', encoding='utf-8') as f:
                json.dump([v.to_dict() for v in self.violations], f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存违规记录失败: {e}")
    
    def get_violations_by_severity(self, severity: str) -> List[ComplianceViolation]:
        """按严重程度获取违规记录"""
        return [v for v in self.violations if v.severity == severity]


class StorageMonitor:
    """存储空间监控工具"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.metrics_history: List[StorageMetrics] = []
        self.metrics_file = Path("logs/monitoring/storage_metrics.json")
        self.metrics_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.logger = logging.getLogger('storage_monitor')
        self.logger.setLevel(logging.INFO)
        
        # 加载历史指标
        self._load_metrics()
    
    def collect_metrics(self) -> StorageMetrics:
        """收集存储指标"""
        # 获取磁盘使用情况
        disk_usage = shutil.disk_usage(self.project_root)
        
        # 统计文件和目录数量
        file_count = 0
        directory_count = 0
        
        for item in self.project_root.rglob("*"):
            if item.is_file():
                file_count += 1
            elif item.is_dir():
                directory_count += 1
        
        metrics = StorageMetrics(
            total_space=disk_usage.total,
            used_space=disk_usage.used,
            free_space=disk_usage.free,
            usage_percentage=(disk_usage.used / disk_usage.total) * 100,
            file_count=file_count,
            directory_count=directory_count,
            timestamp=datetime.now()
        )
        
        self.metrics_history.append(metrics)
        self._save_metrics()
        
        # 检查告警条件
        self._check_storage_alerts(metrics)
        
        return metrics
    
    def _load_metrics(self):
        """加载历史指标"""
        if self.metrics_file.exists():
            try:
                with open(self.metrics_file, 'r', encoding='utf-8') as f:
                    metrics_data = json.load(f)
                    for metric_data in metrics_data:
                        metric_data['timestamp'] = datetime.fromisoformat(metric_data['timestamp'])
                        self.metrics_history.append(StorageMetrics(**metric_data))
            except Exception as e:
                self.logger.error(f"加载历史指标失败: {e}")
    
    def _save_metrics(self):
        """保存指标到文件"""
        try:
            # 只保留最近30天的指标
            cutoff_date = datetime.now() - timedelta(days=30)
            recent_metrics = [m for m in self.metrics_history if m.timestamp > cutoff_date]
            
            with open(self.metrics_file, 'w', encoding='utf-8') as f:
                json.dump([m.to_dict() for m in recent_metrics], f, ensure_ascii=False, indent=2)
            
            self.metrics_history = recent_metrics
        except Exception as e:
            self.logger.error(f"保存指标失败: {e}")
    
    def _check_storage_alerts(self, metrics: StorageMetrics):
        """检查存储告警"""
        # 磁盘使用率告警
        if metrics.usage_percentage > 90:
            self.logger.critical(f"磁盘使用率告警: {metrics.usage_percentage:.1f}%")
        elif metrics.usage_percentage > 80:
            self.logger.warning(f"磁盘使用率警告: {metrics.usage_percentage:.1f}%")
        
        # 文件数量增长告警
        if len(self.metrics_history) > 1:
            previous_metrics = self.metrics_history[-2]
            file_growth = metrics.file_count - previous_metrics.file_count
            
            if file_growth > 1000:  # 文件数量增长超过1000
                self.logger.warning(f"文件数量快速增长: +{file_growth} 个文件")
    
    def get_growth_trend(self, days: int = 7) -> Dict[str, float]:
        """获取增长趋势"""
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_metrics = [m for m in self.metrics_history if m.timestamp > cutoff_date]
        
        if len(recent_metrics) < 2:
            return {"file_growth": 0, "space_growth": 0}
        
        oldest = recent_metrics[0]
        newest = recent_metrics[-1]
        
        return {
            "file_growth": newest.file_count - oldest.file_count,
            "space_growth": newest.used_space - oldest.used_space
        }


class ConfigChangeTracker:
    """配置文件变更追踪器"""
    
    def __init__(self, project_root: str, config_dirs: List[str] = None):
        self.project_root = Path(project_root)
        self.config_dirs = config_dirs or ["config", ".kiro", "tools"]
        self.changes: List[ConfigChangeEvent] = []
        self.file_hashes: Dict[str, str] = {}
        
        # 设置日志
        self.logger = logging.getLogger('config_tracker')
        self.logger.setLevel(logging.INFO)
        
        # 变更历史文件
        self.changes_file = Path("logs/monitoring/config_changes.json")
        self.changes_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 哈希文件
        self.hashes_file = Path("logs/monitoring/config_hashes.json")
        
        # 加载历史数据
        self._load_changes()
        self._load_hashes()
        
        # 初始化配置文件哈希
        self._initialize_hashes()
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return ""
    
    def _initialize_hashes(self):
        """初始化配置文件哈希"""
        for config_dir in self.config_dirs:
            config_path = self.project_root / config_dir
            if config_path.exists():
                for config_file in config_path.rglob("*"):
                    if config_file.is_file() and self._is_config_file(config_file):
                        relative_path = str(config_file.relative_to(self.project_root))
                        if relative_path not in self.file_hashes:
                            self.file_hashes[relative_path] = self._calculate_file_hash(config_file)
        
        self._save_hashes()
    
    def _is_config_file(self, file_path: Path) -> bool:
        """判断是否为配置文件"""
        config_extensions = {'.yaml', '.yml', '.json', '.env', '.conf', '.ini', '.toml', '.md'}
        return file_path.suffix.lower() in config_extensions
    
    def check_changes(self) -> List[ConfigChangeEvent]:
        """检查配置文件变更"""
        new_changes = []
        
        for config_dir in self.config_dirs:
            config_path = self.project_root / config_dir
            if config_path.exists():
                for config_file in config_path.rglob("*"):
                    if config_file.is_file() and self._is_config_file(config_file):
                        relative_path = str(config_file.relative_to(self.project_root))
                        current_hash = self._calculate_file_hash(config_file)
                        old_hash = self.file_hashes.get(relative_path)
                        
                        if old_hash is None:
                            # 新创建的配置文件
                            change = ConfigChangeEvent(
                                config_path=relative_path,
                                change_type="created",
                                old_hash=None,
                                new_hash=current_hash,
                                timestamp=datetime.now()
                            )
                            new_changes.append(change)
                            self.file_hashes[relative_path] = current_hash
                        
                        elif old_hash != current_hash:
                            # 配置文件被修改
                            change = ConfigChangeEvent(
                                config_path=relative_path,
                                change_type="modified",
                                old_hash=old_hash,
                                new_hash=current_hash,
                                timestamp=datetime.now()
                            )
                            new_changes.append(change)
                            self.file_hashes[relative_path] = current_hash
        
        # 检查删除的配置文件
        existing_files = set()
        for config_dir in self.config_dirs:
            config_path = self.project_root / config_dir
            if config_path.exists():
                for config_file in config_path.rglob("*"):
                    if config_file.is_file() and self._is_config_file(config_file):
                        existing_files.add(str(config_file.relative_to(self.project_root)))
        
        for tracked_file in list(self.file_hashes.keys()):
            if tracked_file not in existing_files:
                change = ConfigChangeEvent(
                    config_path=tracked_file,
                    change_type="deleted",
                    old_hash=self.file_hashes[tracked_file],
                    new_hash=None,
                    timestamp=datetime.now()
                )
                new_changes.append(change)
                del self.file_hashes[tracked_file]
        
        # 记录变更
        for change in new_changes:
            self._record_change(change)
        
        self._save_hashes()
        return new_changes
    
    def _record_change(self, change: ConfigChangeEvent):
        """记录配置变更"""
        self.changes.append(change)
        self.logger.info(f"配置文件变更: {change.change_type} - {change.config_path}")
        
        # 保存变更记录
        self._save_changes()
    
    def _load_changes(self):
        """加载历史变更"""
        if self.changes_file.exists():
            try:
                with open(self.changes_file, 'r', encoding='utf-8') as f:
                    changes_data = json.load(f)
                    for change_data in changes_data:
                        change_data['timestamp'] = datetime.fromisoformat(change_data['timestamp'])
                        self.changes.append(ConfigChangeEvent(**change_data))
            except Exception as e:
                self.logger.error(f"加载历史变更失败: {e}")
    
    def _save_changes(self):
        """保存变更记录"""
        try:
            # 只保留最近30天的变更
            cutoff_date = datetime.now() - timedelta(days=30)
            recent_changes = [c for c in self.changes if c.timestamp > cutoff_date]
            
            with open(self.changes_file, 'w', encoding='utf-8') as f:
                json.dump([c.to_dict() for c in recent_changes], f, ensure_ascii=False, indent=2)
            
            self.changes = recent_changes
        except Exception as e:
            self.logger.error(f"保存变更记录失败: {e}")
    
    def _load_hashes(self):
        """加载文件哈希"""
        if self.hashes_file.exists():
            try:
                with open(self.hashes_file, 'r', encoding='utf-8') as f:
                    self.file_hashes = json.load(f)
            except Exception as e:
                self.logger.error(f"加载文件哈希失败: {e}")
    
    def _save_hashes(self):
        """保存文件哈希"""
        try:
            with open(self.hashes_file, 'w', encoding='utf-8') as f:
                json.dump(self.file_hashes, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存文件哈希失败: {e}")
    
    def get_recent_changes(self, hours: int = 24) -> List[ConfigChangeEvent]:
        """获取最近的配置变更"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [c for c in self.changes if c.timestamp > cutoff_time]


class FileOrganizationMonitoringSystem:
    """文件组织监控系统主类"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        
        # 初始化各个监控组件
        self.directory_monitor = DirectoryStructureMonitor(str(self.project_root))
        self.compliance_checker = FileComplianceChecker(str(self.project_root))
        self.storage_monitor = StorageMonitor(str(self.project_root))
        self.config_tracker = ConfigChangeTracker(str(self.project_root))
        
        # 文件系统观察器
        self.observer = Observer()
        self.observer.schedule(self.directory_monitor, str(self.project_root), recursive=True)
        
        # 设置主日志
        self.logger = logging.getLogger('monitoring_system')
        self.logger.setLevel(logging.INFO)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def start_monitoring(self):
        """启动监控系统"""
        self.logger.info("启动文件组织监控系统...")
        
        # 启动目录结构监控
        self.observer.start()
        self.logger.info("目录结构监控已启动")
        
        # 执行初始检查
        self.run_compliance_check()
        self.collect_storage_metrics()
        self.check_config_changes()
        
        self.logger.info("文件组织监控系统启动完成")
    
    def stop_monitoring(self):
        """停止监控系统"""
        self.logger.info("停止文件组织监控系统...")
        self.observer.stop()
        self.observer.join()
        self.logger.info("文件组织监控系统已停止")
    
    def run_compliance_check(self) -> List[ComplianceViolation]:
        """运行规范检查"""
        self.logger.info("开始文件放置规范检查...")
        violations = self.compliance_checker.check_compliance()
        
        if violations:
            self.logger.warning(f"发现 {len(violations)} 个规范违规")
            for violation in violations:
                self.logger.warning(f"违规: {violation.file_path} - {violation.violation_type}")
        else:
            self.logger.info("文件放置规范检查通过")
        
        return violations
    
    def collect_storage_metrics(self) -> StorageMetrics:
        """收集存储指标"""
        self.logger.info("收集存储空间指标...")
        metrics = self.storage_monitor.collect_metrics()
        
        self.logger.info(f"存储使用率: {metrics.usage_percentage:.1f}%")
        self.logger.info(f"文件总数: {metrics.file_count}")
        self.logger.info(f"目录总数: {metrics.directory_count}")
        
        return metrics
    
    def check_config_changes(self) -> List[ConfigChangeEvent]:
        """检查配置文件变更"""
        self.logger.info("检查配置文件变更...")
        changes = self.config_tracker.check_changes()
        
        if changes:
            self.logger.info(f"发现 {len(changes)} 个配置文件变更")
            for change in changes:
                self.logger.info(f"配置变更: {change.change_type} - {change.config_path}")
        else:
            self.logger.info("未发现配置文件变更")
        
        return changes
    
    def generate_monitoring_report(self) -> Dict[str, Any]:
        """生成监控报告"""
        self.logger.info("生成监控报告...")
        
        # 收集各种数据
        recent_directory_changes = self.directory_monitor.get_recent_changes(24)
        violations = self.compliance_checker.check_compliance()
        storage_metrics = self.storage_monitor.collect_metrics()
        recent_config_changes = self.config_tracker.get_recent_changes(24)
        storage_trend = self.storage_monitor.get_growth_trend(7)
        
        report = {
            "report_time": datetime.now().isoformat(),
            "directory_changes": {
                "count": len(recent_directory_changes),
                "changes": [c.to_dict() for c in recent_directory_changes]
            },
            "compliance_violations": {
                "total": len(violations),
                "high_severity": len([v for v in violations if v.severity == "high"]),
                "medium_severity": len([v for v in violations if v.severity == "medium"]),
                "low_severity": len([v for v in violations if v.severity == "low"]),
                "violations": [v.to_dict() for v in violations]
            },
            "storage_metrics": storage_metrics.to_dict(),
            "storage_trend": storage_trend,
            "config_changes": {
                "count": len(recent_config_changes),
                "changes": [c.to_dict() for c in recent_config_changes]
            }
        }
        
        # 保存报告
        report_file = Path("logs/monitoring/monitoring_report.json")
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"监控报告已保存到: {report_file}")
        return report


if __name__ == "__main__":
    # 示例使用
    monitoring_system = FileOrganizationMonitoringSystem()
    
    try:
        # 启动监控
        monitoring_system.start_monitoring()
        
        # 生成报告
        report = monitoring_system.generate_monitoring_report()
        logger.info(f"监控报告生成完成，共发现 {report['compliance_violations']['total']} 个违规")
        
        # 保持监控运行（在实际使用中可能需要长期运行）
        logger.info("监控系统正在运行，按 Ctrl+C 停止...")
        time.sleep(10)  # 示例中只运行10秒
        
    except KeyboardInterrupt:
        logger.info("\n收到停止信号...")
    finally:
        monitoring_system.stop_monitoring()
        logger.info("监控系统已停止")