import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
文件组织工具命令行界面

提供项目文件组织相关的命令行工具，包括项目初始化、结构验证等功能。
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from scripts.setup.initialize_project_structure import ProjectStructureInitializer
from tools.file_organization.structure_validator import ProjectStructureValidator


def init_command(args):
    """执行项目初始化命令"""
    logger.info(f"🚀 开始初始化项目结构...")
    logger.info(f"📁 项目目录: {args.project_root or '当前目录'}")
    logger.info(f"📋 使用模板: {args.template}")
    
    initializer = ProjectStructureInitializer(args.project_root)
    
    if args.list_templates:
        templates_dir = initializer.templates_dir
        if templates_dir.exists():
            templates = [f.stem for f in templates_dir.glob('*.json')]
            logger.info("\n📋 可用的项目模板:")
            for template in templates:
                logger.info(f"  • {template}")
        else:
            logger.info("❌ 未找到模板目录")
        return
    
    success = initializer.initialize_project(args.template)
    
    if success:
        logger.info("\n✅ 项目结构初始化成功！")
        logger.info("\n📝 下一步建议:")
        logger.info("  1. 安装依赖: pip install -r requirements.txt")
        logger.info("  2. 配置系统: 编辑config/目录下的配置文件")
        logger.info("  3. 验证结构: python tools/file_organization/cli.py validate")
        logger.info("  4. 查看文档: cat README.md")
    else:
        logger.info("\n❌ 项目结构初始化失败")
        sys.exit(1)


def validate_command(args):
    """执行项目结构验证命令"""
    logger.info(f"🔍 开始验证项目结构...")
    logger.info(f"📁 项目目录: {args.project_root or '当前目录'}")
    logger.info(f"📋 使用模板: {args.template}")
    
    validator = ProjectStructureValidator(args.project_root)
    summary = validator.validate_project_structure(args.template)
    
    if args.json:
        import json
        logger.info(json.dumps(summary, ensure_ascii=False, indent=2))
    else:
        validator.print_validation_report(summary)
    
    # 根据验证结果设置退出码
    if summary['overall_status'] == 'fail':
        sys.exit(1)
    elif summary['overall_status'] == 'warning':
        sys.exit(2)


def fix_command(args):
    """执行项目结构修复命令"""
    logger.info(f"🔧 开始修复项目结构问题...")
    
    # 首先验证当前结构
    validator = ProjectStructureValidator(args.project_root)
    summary = validator.validate_project_structure(args.template)
    
    if summary['overall_status'] == 'pass':
        logger.info("✅ 项目结构已经符合规范，无需修复")
        return
    
    # 分析需要修复的问题
    issues_to_fix = []
    
    for result in summary['results']:
        if result['status'] == 'fail':
            if result['type'] == 'directory_existence':
                issues_to_fix.append({
                    'type': 'create_directory',
                    'path': result['path'],
                    'message': f"创建缺失的目录: {result['path']}"
                })
        elif result['status'] == 'warning':
            if result['type'] == 'readme_existence':
                issues_to_fix.append({
                    'type': 'create_readme',
                    'path': result['path'],
                    'message': f"创建缺失的README文件: {result['path']}"
                })
    
    if not issues_to_fix:
        logger.info("ℹ️  没有发现可以自动修复的问题")
        return
    
    logger.info(f"\n🔧 发现 {len(issues_to_fix)} 个可修复的问题:")
    for issue in issues_to_fix:
        logger.info(f"  • {issue['message']}")
    
    if not args.auto_fix:
        response = input("\n是否继续修复这些问题？(y/N): ")
        if response.lower() not in ['y', 'yes', '是']:
            logger.info("❌ 用户取消修复操作")
            return
    
    # 执行修复
    fixed_count = 0
    for issue in issues_to_fix:
        try:
            if issue['type'] == 'create_directory':
                Path(issue['path']).mkdir(parents=True, exist_ok=True)
                logger.info(f"✅ 已创建目录: {issue['path']}")
                fixed_count += 1
            elif issue['type'] == 'create_readme':
                readme_path = Path(issue['path'])
                if not readme_path.exists():
                    dir_name = readme_path.parent.name
                    content = f"""# {dir_name}

## 目录说明

此目录用于存放相关文件。

## 使用方法

请按照项目文件组织规范放置文件。
"""
                    with open(readme_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    logger.info(f"✅ 已创建README文件: {issue['path']}")
                    fixed_count += 1
        except Exception as e:
            logger.info(f"❌ 修复失败 {issue['path']}: {e}")
    
    logger.info(f"\n🎉 修复完成！成功修复 {fixed_count} 个问题")
    
    # 重新验证
    if fixed_count > 0:
        logger.info("\n🔍 重新验证项目结构...")
        summary = validator.validate_project_structure(args.template)
        validator.print_validation_report(summary)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='项目文件组织工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 初始化项目结构
  python tools/file_organization/cli.py init
  
  # 使用指定模板初始化
  python tools/file_organization/cli.py init --template basic_python
  
  # 列出可用模板
  python tools/file_organization/cli.py init --list-templates
  
  # 验证项目结构
  python tools/file_organization/cli.py validate
  
  # 自动修复项目结构问题
  python tools/file_organization/cli.py fix --auto-fix
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 初始化命令
    init_parser = subparsers.add_parser('init', help='初始化项目结构')
    init_parser.add_argument(
        '--template', 
        default='quantitative_trading',
        help='项目模板名称 (默认: quantitative_trading)'
    )
    init_parser.add_argument(
        '--project-root',
        help='项目根目录路径 (默认: 当前目录)'
    )
    init_parser.add_argument(
        '--list-templates',
        action='store_true',
        help='列出可用的项目模板'
    )
    
    # 验证命令
    validate_parser = subparsers.add_parser('validate', help='验证项目结构')
    validate_parser.add_argument(
        '--template',
        default='quantitative_trading',
        help='项目模板名称 (默认: quantitative_trading)'
    )
    validate_parser.add_argument(
        '--project-root',
        help='项目根目录路径 (默认: 当前目录)'
    )
    validate_parser.add_argument(
        '--json',
        action='store_true',
        help='以JSON格式输出结果'
    )
    
    # 修复命令
    fix_parser = subparsers.add_parser('fix', help='修复项目结构问题')
    fix_parser.add_argument(
        '--template',
        default='quantitative_trading',
        help='项目模板名称 (默认: quantitative_trading)'
    )
    fix_parser.add_argument(
        '--project-root',
        help='项目根目录路径 (默认: 当前目录)'
    )
    fix_parser.add_argument(
        '--auto-fix',
        action='store_true',
        help='自动修复问题，不询问确认'
    )
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'init':
            init_command(args)
        elif args.command == 'validate':
            validate_command(args)
        elif args.command == 'fix':
            fix_command(args)
    except KeyboardInterrupt:
        logger.info("\n\n❌ 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.info(f"\n❌ 执行过程中发生错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()