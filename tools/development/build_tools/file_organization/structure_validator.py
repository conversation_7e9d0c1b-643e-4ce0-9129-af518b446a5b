#!/usr/bin/env python3
"""
项目结构验证工具

此工具用于验证项目结构是否符合组织规范，检查目录完整性、
权限设置和文件放置规则。
"""

import os
import json
import stat
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ProjectStructureValidator:
    """项目结构验证器"""
    
    def __init__(self, project_root: str = None):
        """
        初始化项目结构验证器
        
        Args:
            project_root: 项目根目录路径，默认为当前目录
        """
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.templates_dir = self.project_root / "scripts/setup/templates"
        self.validation_results = []
        
    def load_template(self, template_name: str) -> Dict:
        """
        加载项目结构模板
        
        Args:
            template_name: 模板名称
            
        Returns:
            模板配置字典
        """
        template_file = self.templates_dir / f"{template_name}.json"
        
        if not template_file.exists():
            raise FileNotFoundError(f"模板文件不存在: {template_file}")
            
        with open(template_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def validate_directory_existence(self, directories: List[Dict]) -> List[Dict]:
        """
        验证目录是否存在
        
        Args:
            directories: 目录配置列表
            
        Returns:
            验证结果列表
        """
        results = []
        
        for dir_config in directories:
            dir_path = self.project_root / dir_config['path']
            
            result = {
                'type': 'directory_existence',
                'path': str(dir_path),
                'expected': True,
                'actual': dir_path.exists(),
                'status': 'pass' if dir_path.exists() else 'fail',
                'message': f"目录存在检查: {dir_config['path']}"
            }
            
            if not dir_path.exists():
                result['message'] += " - 目录不存在"
            elif not dir_path.is_dir():
                result['status'] = 'fail'
                result['message'] += " - 路径存在但不是目录"
                
            results.append(result)
            
        return results
    
    def validate_directory_permissions(self, directories: List[Dict]) -> List[Dict]:
        """
        验证目录权限设置
        
        Args:
            directories: 目录配置列表
            
        Returns:
            验证结果列表
        """
        results = []
        
        for dir_config in directories:
            if 'permissions' not in dir_config:
                continue
                
            dir_path = self.project_root / dir_config['path']
            
            if not dir_path.exists():
                continue
                
            expected_perm = dir_config['permissions']
            actual_perm = oct(stat.S_IMODE(dir_path.stat().st_mode))[-3:]
            
            result = {
                'type': 'directory_permissions',
                'path': str(dir_path),
                'expected': expected_perm,
                'actual': actual_perm,
                'status': 'pass' if actual_perm == expected_perm else 'warning',
                'message': f"目录权限检查: {dir_config['path']} (期望: {expected_perm}, 实际: {actual_perm})"
            }
            
            results.append(result)
            
        return results
    
    def validate_file_organization(self) -> List[Dict]:
        """
        验证文件组织规范
        
        Returns:
            验证结果列表
        """
        results = []
        
        # 检查源代码文件是否在正确位置
        src_path = self.project_root / 'src'
        if src_path.exists():
            for py_file in src_path.rglob('*.py'):
                # 检查是否有__pycache__目录在版本控制中
                if '__pycache__' in str(py_file):
                    results.append({
                        'type': 'file_organization',
                        'path': str(py_file),
                        'status': 'warning',
                        'message': f"发现__pycache__目录，应该被.gitignore忽略: {py_file}"
                    })
        
        # 检查配置文件是否在config目录
        config_extensions = ['.yaml', '.yml', '.json', '.ini', '.conf']
        for ext in config_extensions:
            for config_file in self.project_root.rglob(f'*{ext}'):
                # 跳过在config目录中的文件
                if 'config' in str(config_file.relative_to(self.project_root)):
                    continue
                # 跳过特殊文件
                if config_file.name in ['package.json', 'tsconfig.json', '.eslintrc.json']:
                    continue
                    
                results.append({
                    'type': 'file_organization',
                    'path': str(config_file),
                    'status': 'warning',
                    'message': f"配置文件可能应该放在config/目录中: {config_file.name}"
                })
        
        return results
    
    def validate_readme_files(self, directories: List[Dict]) -> List[Dict]:
        """
        验证README文件是否存在
        
        Args:
            directories: 目录配置列表
            
        Returns:
            验证结果列表
        """
        results = []
        
        for dir_config in directories:
            if not dir_config.get('create_readme', False):
                continue
                
            dir_path = self.project_root / dir_config['path']
            readme_file = dir_path / 'README.md'
            
            if not dir_path.exists():
                continue
                
            result = {
                'type': 'readme_existence',
                'path': str(readme_file),
                'expected': True,
                'actual': readme_file.exists(),
                'status': 'pass' if readme_file.exists() else 'warning',
                'message': f"README文件检查: {dir_config['path']}/README.md"
            }
            
            if not readme_file.exists():
                result['message'] += " - README文件不存在"
                
            results.append(result)
            
        return results
    
    def validate_gitkeep_files(self, directories: List[Dict]) -> List[Dict]:
        """
        验证.gitkeep文件是否存在
        
        Args:
            directories: 目录配置列表
            
        Returns:
            验证结果列表
        """
        results = []
        
        for dir_config in directories:
            if not dir_config.get('create_gitkeep', False):
                continue
                
            dir_path = self.project_root / dir_config['path']
            gitkeep_file = dir_path / '.gitkeep'
            
            if not dir_path.exists():
                continue
                
            # 检查目录是否为空（除了.gitkeep文件）
            dir_contents = list(dir_path.iterdir())
            is_empty = len(dir_contents) == 0 or (len(dir_contents) == 1 and gitkeep_file in dir_contents)
            
            result = {
                'type': 'gitkeep_existence',
                'path': str(gitkeep_file),
                'expected': is_empty,
                'actual': gitkeep_file.exists(),
                'status': 'pass',
                'message': f".gitkeep文件检查: {dir_config['path']}/.gitkeep"
            }
            
            if is_empty and not gitkeep_file.exists():
                result['status'] = 'warning'
                result['message'] += " - 空目录缺少.gitkeep文件"
            elif not is_empty and gitkeep_file.exists():
                result['status'] = 'info'
                result['message'] += " - 目录不为空，可以删除.gitkeep文件"
                
            results.append(result)
            
        return results
    
    def check_directory_size_limits(self) -> List[Dict]:
        """
        检查目录文件数量是否超过建议限制
        
        Returns:
            验证结果列表
        """
        results = []
        
        # 建议的目录文件数量限制
        size_limits = {
            'src': 15,
            'src/strategies': 10,
            'src/indicators': 15,
            'tests': 20,
            'docs': 20,
            'examples': 15,
            'scripts': 10,
            'tools': 10
        }
        
        for dir_path, limit in size_limits.items():
            full_path = self.project_root / dir_path
            
            if not full_path.exists():
                continue
                
            # 计算直接子文件数量（不包括子目录）
            file_count = len([f for f in full_path.iterdir() if f.is_file()])
            
            result = {
                'type': 'directory_size',
                'path': str(full_path),
                'expected': f"<= {limit}",
                'actual': file_count,
                'status': 'pass' if file_count <= limit else 'warning',
                'message': f"目录文件数量检查: {dir_path} ({file_count}/{limit})"
            }
            
            if file_count > limit:
                result['message'] += f" - 建议创建子目录进行分类"
                
            results.append(result)
            
        return results
    
    def validate_project_structure(self, template_name: str = 'quantitative_trading') -> Dict:
        """
        验证完整的项目结构
        
        Args:
            template_name: 模板名称
            
        Returns:
            验证结果汇总
        """
        logger.info(f"开始验证项目结构，使用模板: {template_name}")
        
        try:
            # 加载模板
            template = self.load_template(template_name)
            directories = template.get('directories', [])
            
            # 执行各项验证
            all_results = []
            
            # 目录存在性验证
            all_results.extend(self.validate_directory_existence(directories))
            
            # 目录权限验证
            all_results.extend(self.validate_directory_permissions(directories))
            
            # 文件组织规范验证
            all_results.extend(self.validate_file_organization())
            
            # README文件验证
            all_results.extend(self.validate_readme_files(directories))
            
            # .gitkeep文件验证
            all_results.extend(self.validate_gitkeep_files(directories))
            
            # 目录大小检查
            all_results.extend(self.check_directory_size_limits())
            
            # 统计结果
            pass_count = len([r for r in all_results if r['status'] == 'pass'])
            warning_count = len([r for r in all_results if r['status'] == 'warning'])
            fail_count = len([r for r in all_results if r['status'] == 'fail'])
            info_count = len([r for r in all_results if r['status'] == 'info'])
            
            summary = {
                'total_checks': len(all_results),
                'pass_count': pass_count,
                'warning_count': warning_count,
                'fail_count': fail_count,
                'info_count': info_count,
                'overall_status': 'fail' if fail_count > 0 else ('warning' if warning_count > 0 else 'pass'),
                'results': all_results
            }
            
            logger.info(f"验证完成: {pass_count}通过, {warning_count}警告, {fail_count}失败, {info_count}信息")
            
            return summary
            
        except Exception as e:
            logger.error(f"验证过程中发生错误: {e}")
            return {
                'total_checks': 0,
                'pass_count': 0,
                'warning_count': 0,
                'fail_count': 1,
                'info_count': 0,
                'overall_status': 'error',
                'error': str(e),
                'results': []
            }
    
    def print_validation_report(self, summary: Dict) -> None:
        """
        打印验证报告
        
        Args:
            summary: 验证结果汇总
        """
        logger.info("\n" + "="*60)
        logger.info("项目结构验证报告")
        logger.info("="*60)
        
        if 'error' in summary:
            logger.info(f"❌ 验证过程中发生错误: {summary['error']}")
            return
        
        # 打印汇总信息
        logger.info(f"总检查项: {summary['total_checks']}")
        logger.info(f"✅ 通过: {summary['pass_count']}")
        logger.info(f"⚠️  警告: {summary['warning_count']}")
        logger.info(f"❌ 失败: {summary['fail_count']}")
        logger.info(f"ℹ️  信息: {summary['info_count']}")
        logger.info(f"整体状态: {summary['overall_status'].upper()}")
        
        # 按状态分组显示详细结果
        status_groups = {
            'fail': '❌ 失败项',
            'warning': '⚠️  警告项',
            'info': 'ℹ️  信息项'
        }
        
        for status, title in status_groups.items():
            items = [r for r in summary['results'] if r['status'] == status]
            if items:
                logger.info(f"\n{title}:")
                for item in items:
                    logger.info(f"  - {item['message']}")
        
        logger.info("\n" + "="*60)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='项目结构验证工具')
    parser.add_argument(
        '--template',
        default='quantitative_trading',
        help='项目模板名称 (默认: quantitative_trading)'
    )
    parser.add_argument(
        '--project-root',
        help='项目根目录路径 (默认: 当前目录)'
    )
    parser.add_argument(
        '--json-output',
        action='store_true',
        help='以JSON格式输出结果'
    )
    
    args = parser.parse_args()
    
    validator = ProjectStructureValidator(args.project_root)
    summary = validator.validate_project_structure(args.template)
    
    if args.json_output:
        logger.info(json.dumps(summary, ensure_ascii=False, indent=2))
    else:
        validator.print_validation_report(summary)
    
    # 根据验证结果设置退出码
    if summary['overall_status'] == 'fail':
        exit(1)
    elif summary['overall_status'] == 'warning':
        exit(2)
    else:
        exit(0)


if __name__ == '__main__':
    main()