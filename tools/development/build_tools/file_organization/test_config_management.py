import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
配置管理系统测试脚本

本脚本测试配置管理系统的各个组件功能，包括配置加载、验证、
环境管理、模板管理等功能。

作者: 量化交易系统开发团队
版本: 1.0.0
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from tools.file_organization.config_management_system import ConfigurationManager
from tools.file_organization.config_loader import ConfigurationLoader
from tools.file_organization.config_validator import ConfigurationValidationManager, ValidationRule
from tools.file_organization.environment_manager import EnvironmentManager
from tools.file_organization.template_manager import TemplateManager


class ConfigManagementTester:
    """配置管理系统测试器"""
    
    def __init__(self):
        """初始化测试器"""
        # 创建临时测试目录
        self.test_dir = Path(tempfile.mkdtemp(prefix="config_test_"))
        self.config_dir = self.test_dir / "config"
        
        logger.info(f"🧪 测试目录: {self.test_dir}")
        
        # 初始化管理器
        self.config_manager = ConfigurationManager(str(self.config_dir))
        self.config_loader = ConfigurationLoader(str(self.config_dir))
        self.validation_manager = ConfigurationValidationManager()
        self.env_manager = EnvironmentManager(str(self.config_dir))
        self.template_manager = TemplateManager(str(self.config_dir))
        
        # 测试结果
        self.test_results = []
    
    def __del__(self):
        """清理测试目录"""
        if hasattr(self, 'test_dir') and self.test_dir.exists():
            shutil.rmtree(self.test_dir)
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        logger.info("🚀 开始配置管理系统测试")
        logger.info("=" * 60)
        
        # 准备测试数据
        self._setup_test_data()
        
        # 运行各项测试
        tests = [
            ("配置加载器测试", self._test_config_loader),
            ("配置验证器测试", self._test_config_validator),
            ("环境管理器测试", self._test_environment_manager),
            ("模板管理器测试", self._test_template_manager),
            ("配置管理系统集成测试", self._test_integration)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n📋 {test_name}")
            logger.info("-" * 40)
            
            try:
                success = test_func()
                self.test_results.append((test_name, success))
                
                if success:
                    logger.info(f"✅ {test_name} 通过")
                else:
                    logger.info(f"❌ {test_name} 失败")
                    
            except Exception as e:
                logger.info(f"❌ {test_name} 异常: {str(e)}")
                self.test_results.append((test_name, False))
        
        # 显示测试结果
        self._show_test_results()
        
        # 返回总体测试结果
        return all(result for _, result in self.test_results)
    
    def _setup_test_data(self) -> None:
        """准备测试数据"""
        logger.info("📝 准备测试数据...")
        
        # 创建测试配置文件
        test_config = {
            'database': {
                'host': 'localhost',
                'port': 5432,
                'database': 'test_db',
                'username': 'test_user',
                'password': 'test_pass',
                'pool_size': 5
            },
            'api': {
                'host': '127.0.0.1',
                'port': 8000,
                'debug': True,
                'jwt_secret': 'test-secret-key'
            },
            'risk': {
                'max_position_size': 0.1,
                'max_portfolio_risk': 0.02,
                'stop_loss_pct': 0.05
            },
            'logging': {
                'level': 'DEBUG',
                'file_path': 'logs/test.log',
                'max_file_size': 1048576,
                'backup_count': 3
            }
        }
        
        # 保存主配置文件
        import yaml
        config_file = self.config_dir / "test_config.yaml"
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(test_config, f, default_flow_style=False, allow_unicode=True)
        
        # 创建环境配置
        dev_config = test_config.copy()
        dev_config['environment'] = 'development'
        dev_config['api']['debug'] = True
        
        env_file = self.config_dir / "environments" / "development.yaml"
        env_file.parent.mkdir(parents=True, exist_ok=True)
        with open(env_file, 'w', encoding='utf-8') as f:
            yaml.dump(dev_config, f, default_flow_style=False, allow_unicode=True)
        
        # 创建测试模板
        template_content = """# 测试配置模板
database:
  host: {DB_HOST}
  port: {DB_PORT}
  database: {DB_NAME}
  username: {DB_USER}
  password: {DB_PASSWORD}

api:
  host: {API_HOST}
  port: {API_PORT}
  debug: {DEBUG_MODE}
"""
        
        template_file = self.config_dir / "templates" / "test_template.template"
        template_file.parent.mkdir(parents=True, exist_ok=True)
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        logger.info("✅ 测试数据准备完成")
    
    def _test_config_loader(self) -> bool:
        """测试配置加载器"""
        try:
            # 测试基础配置加载
            config = self.config_loader.load_config("test_config")
            assert 'database' in config, "配置中缺少database部分"
            assert config['database']['host'] == 'localhost', "数据库主机配置错误"
            logger.info("✓ 基础配置加载测试通过")
            
            # 测试环境配置加载
            dev_config = self.config_loader.load_config("test_config", "development")
            assert dev_config['environment'] == 'development', "环境配置加载错误"
            assert dev_config['api']['debug'] is True, "环境特定配置未生效"
            logger.info("✓ 环境配置加载测试通过")
            
            # 测试缓存功能
            cached_config = self.config_loader.load_config("test_config")
            assert cached_config == config, "缓存配置不一致"
            logger.info("✓ 配置缓存测试通过")
            
            # 测试配置重新加载
            reloaded_config = self.config_loader.reload_config("test_config")
            assert reloaded_config == config, "重新加载配置不一致"
            logger.info("✓ 配置重新加载测试通过")
            
            # 测试配置结构验证
            required_keys = ["database.host", "database.port", "api.host", "api.port"]
            errors = self.config_loader.validate_config_structure(config, required_keys)
            assert len(errors) == 0, f"配置结构验证失败: {errors}"
            logger.info("✓ 配置结构验证测试通过")
            
            return True
            
        except Exception as e:
            logger.info(f"❌ 配置加载器测试失败: {str(e)}")
            return False
    
    def _test_config_validator(self) -> bool:
        """测试配置验证器"""
        try:
            # 测试默认验证器
            config = self.config_loader.load_config("test_config")
            result = self.validation_manager.validate_config(config)
            assert result.is_valid, f"默认验证失败: {result.errors}"
            logger.info("✓ 默认验证器测试通过")
            
            # 测试自定义验证规则
            custom_rules = [
                ValidationRule(
                    field_path="database.pool_size",
                    rule_type="range",
                    rule_value=(1, 20),
                    error_message="连接池大小必须在1-20范围内"
                ),
                ValidationRule(
                    field_path="api.jwt_secret",
                    rule_type="length",
                    rule_value=(8, 256),
                    error_message="JWT密钥长度必须在8-256字符之间"
                )
            ]
            
            for rule in custom_rules:
                self.validation_manager.add_validation_rule(rule)
            
            result = self.validation_manager.validate_config(config)
            assert result.is_valid, f"自定义验证失败: {result.errors}"
            logger.info("✓ 自定义验证规则测试通过")
            
            # 测试验证失败情况
            invalid_config = config.copy()
            invalid_config['database']['port'] = 99999  # 超出端口范围
            
            result = self.validation_manager.validate_config(invalid_config)
            assert not result.is_valid, "应该验证失败但却通过了"
            assert len(result.errors) > 0, "验证失败但没有错误消息"
            logger.info("✓ 验证失败情况测试通过")
            
            return True
            
        except Exception as e:
            logger.info(f"❌ 配置验证器测试失败: {str(e)}")
            return False
    
    def _test_environment_manager(self) -> bool:
        """测试环境管理器"""
        try:
            # 测试环境列表
            environments = self.env_manager.list_environments()
            assert len(environments) > 0, "没有找到任何环境"
            logger.info(f"✓ 环境列表测试通过，找到 {len(environments)} 个环境")
            
            # 测试当前环境
            current_env = self.env_manager.get_current_environment()
            assert current_env is not None, "当前环境为空"
            logger.info(f"✓ 当前环境测试通过: {current_env}")
            
            # 测试环境配置加载
            if self.env_manager.environment_exists('development'):
                dev_config = self.env_manager.load_environment_config('development')
                assert 'environment' in dev_config, "环境配置中缺少environment字段"
                logger.info("✓ 环境配置加载测试通过")
            
            # 测试创建新环境
            test_env_config = {
                'environment': 'test_env',
                'database': {
                    'host': 'test-db',
                    'port': 5432,
                    'database': 'test_db'
                }
            }
            
            self.env_manager.create_environment('test_env', test_env_config, "测试环境")
            assert self.env_manager.environment_exists('test_env'), "测试环境创建失败"
            logger.info("✓ 环境创建测试通过")
            
            # 测试环境更新
            updates = {
                'database': {
                    'host': 'updated-host'
                }
            }
            
            self.env_manager.update_environment('test_env', updates)
            updated_config = self.env_manager.load_environment_config('test_env')
            assert updated_config['database']['host'] == 'updated-host', "环境更新失败"
            logger.info("✓ 环境更新测试通过")
            
            # 测试环境验证
            errors = self.env_manager.validate_environment('test_env')
            # 注意：这里可能有验证错误，但不应该有致命错误
            logger.info(f"✓ 环境验证测试通过，发现 {len(errors)} 个问题")
            
            # 测试环境删除
            self.env_manager.delete_environment('test_env', force=True)
            assert not self.env_manager.environment_exists('test_env'), "测试环境删除失败"
            logger.info("✓ 环境删除测试通过")
            
            return True
            
        except Exception as e:
            logger.info(f"❌ 环境管理器测试失败: {str(e)}")
            return False
    
    def _test_template_manager(self) -> bool:
        """测试模板管理器"""
        try:
            # 测试模板列表
            templates = self.template_manager.list_templates()
            logger.info(f"✓ 模板列表测试通过，找到 {len(templates)} 个模板")
            
            # 测试创建模板
            template_content = """# 新测试模板
test_config:
  value1: {VALUE1}
  value2: {VALUE2}
  flag: {FLAG}
"""
            
            self.template_manager.create_template(
                'new_test_template', 
                template_content,
                description="新的测试模板",
                category="test",
                tags=["test", "demo"]
            )
            
            assert self.template_manager.template_exists('new_test_template'), "模板创建失败"
            logger.info("✓ 模板创建测试通过")
            
            # 测试模板信息获取
            template_info = self.template_manager.get_template_info('new_test_template')
            assert template_info is not None, "模板信息获取失败"
            assert template_info.description == "新的测试模板", "模板描述不正确"
            assert 'test' in template_info.tags, "模板标签不正确"
            logger.info("✓ 模板信息获取测试通过")
            
            # 测试模板变量验证
            variables = {
                'VALUE1': 'test_value_1',
                'VALUE2': 'test_value_2',
                'FLAG': 'true'
            }
            
            errors = self.template_manager.validate_template_variables('new_test_template', variables)
            logger.info(f"✓ 模板变量验证测试通过，发现 {len(errors)} 个问题")
            
            # 测试模板预览
            preview = self.template_manager.preview_template('new_test_template', variables)
            assert 'test_value_1' in preview, "模板变量替换失败"
            assert 'test_value_2' in preview, "模板变量替换失败"
            logger.info("✓ 模板预览测试通过")
            
            # 测试从模板生成配置
            output_file = self.test_dir / "generated_config.yaml"
            self.template_manager.generate_config_from_template(
                'new_test_template', 
                str(output_file),
                variables=variables
            )
            
            assert output_file.exists(), "配置文件生成失败"
            
            with open(output_file, 'r', encoding='utf-8') as f:
                generated_content = f.read()
                assert 'test_value_1' in generated_content, "生成的配置文件内容不正确"
            
            logger.info("✓ 配置文件生成测试通过")
            
            # 测试模板删除
            self.template_manager.delete_template('new_test_template')
            assert not self.template_manager.template_exists('new_test_template'), "模板删除失败"
            logger.info("✓ 模板删除测试通过")
            
            return True
            
        except Exception as e:
            logger.info(f"❌ 模板管理器测试失败: {str(e)}")
            return False
    
    def _test_integration(self) -> bool:
        """测试系统集成功能"""
        try:
            # 测试配置管理系统状态
            status = self.config_manager.get_system_status()
            assert 'config_directory' in status, "系统状态缺少配置目录信息"
            assert 'current_environment' in status, "系统状态缺少当前环境信息"
            logger.info("✓ 系统状态测试通过")
            
            # 测试完整的配置加载和验证流程
            config = self.config_manager.load_config("test_config", "development", validate=True)
            assert 'database' in config, "集成配置加载失败"
            assert config['environment'] == 'development', "环境配置未正确合并"
            logger.info("✓ 集成配置加载和验证测试通过")
            
            # 测试环境切换和配置重新加载
            original_env = self.env_manager.get_current_environment()
            
            if self.env_manager.environment_exists('development'):
                self.env_manager.set_current_environment('development')
                current_env = self.env_manager.get_current_environment()
                assert current_env == 'development', "环境切换失败"
                logger.info("✓ 环境切换测试通过")
                
                # 恢复原环境
                if self.env_manager.environment_exists(original_env):
                    self.env_manager.set_current_environment(original_env)
            
            return True
            
        except Exception as e:
            logger.info(f"❌ 系统集成测试失败: {str(e)}")
            return False
    
    def _show_test_results(self) -> None:
        """显示测试结果"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 测试结果汇总")
        logger.info("=" * 60)
        
        passed = 0
        failed = 0
        
        for test_name, success in self.test_results:
            status = "✅ 通过" if success else "❌ 失败"
            logger.info(f"{status} {test_name}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        total = len(self.test_results)
        logger.info(f"\n📈 总计: {total} 个测试，{passed} 个通过，{failed} 个失败")
        
        if failed == 0:
            logger.info("🎉 所有测试都通过了！")
        else:
            logger.info(f"⚠️ 有 {failed} 个测试失败，请检查相关功能")


def main():
    """主函数"""
    logger.info("🧪 配置管理系统测试")
    logger.info("=" * 60)
    
    tester = ConfigManagementTester()
    
    try:
        success = tester.run_all_tests()
        
        if success:
            logger.info("\n🎉 所有测试通过！配置管理系统工作正常。")
            return 0
        else:
            logger.info("\n❌ 部分测试失败，请检查相关功能。")
            return 1
            
    except KeyboardInterrupt:
        logger.info("\n⏹️ 测试被用户中断")
        return 1
    except Exception as e:
        logger.info(f"\n💥 测试过程中发生异常: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())