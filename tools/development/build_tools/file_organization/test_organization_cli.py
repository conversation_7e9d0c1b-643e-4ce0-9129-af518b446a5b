import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
测试文件组织系统命令行工具

提供简单易用的命令行接口来管理测试文件组织。
"""

import argparse
import sys
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tools.file_organization.test_organization_system import (
    TestOrganizationSystem,
    TestFileClassifier,
    UnitTestMirrorTool,
    IntegrationTestOrganizer,
    EndToEndTestManager
)


def cmd_organize_all(args):
    """组织所有测试文件"""
    system = TestOrganizationSystem(args.project_root)
    report = system.organize_all_tests(dry_run=args.dry_run)
    logger.info(system.generate_report(report))


def cmd_classify_tests(args):
    """分类测试文件"""
    classifier = TestFileClassifier(args.project_root)
    test_files = classifier.organize_test_files(dry_run=args.dry_run)
    
    logger.info(f"测试文件分类结果 ({'试运行' if args.dry_run else '实际执行'}):")
    logger.info("=" * 50)
    
    by_type = {}
    for test_file in test_files:
        test_type = test_file.test_type.value
        if test_type not in by_type:
            by_type[test_type] = []
        by_type[test_type].append(test_file.file_path.name)
    
    for test_type, files in by_type.items():
        logger.info(f"\n{test_type.upper()} ({len(files)} 个文件):")
        for file in files:
            logger.info(f"  - {file}")


def cmd_mirror_structure(args):
    """镜像单元测试结构"""
    mirror_tool = UnitTestMirrorTool(args.project_root)
    
    if args.dry_run:
        report = mirror_tool.validate_mirror_structure()
        logger.info("单元测试结构验证结果:")
        logger.info("=" * 40)
        logger.info(f"缺失目录: {len(report['missing_directories'])}")
        logger.info(f"缺失测试文件: {len(report['missing_test_files'])}")
        logger.info(f"孤立测试文件: {len(report['orphaned_test_files'])}")
        logger.info(f"有效映射: {len(report['valid_mappings'])}")
        
        if report['missing_test_files']:
            logger.info("\n缺失的测试文件:")
            for file in report['missing_test_files'][:10]:  # 只显示前10个
                logger.info(f"  - {file}")
            if len(report['missing_test_files']) > 10:
                logger.info(f"  ... 还有 {len(report['missing_test_files']) - 10} 个文件")
    else:
        report = mirror_tool.mirror_src_structure(create_missing=True)
        logger.info("单元测试结构镜像结果:")
        logger.info("=" * 40)
        logger.info(f"创建目录: {len(report['created_directories'])}")
        logger.info(f"创建测试文件: {len(report['created_test_files'])}")
        logger.info(f"现有文件: {len(report['existing_files'])}")
        logger.info(f"错误: {len(report['errors'])}")
        
        if report['created_test_files']:
            logger.info("\n创建的测试文件:")
            for file in report['created_test_files'][:10]:
                logger.info(f"  - {file}")
            if len(report['created_test_files']) > 10:
                logger.info(f"  ... 还有 {len(report['created_test_files']) - 10} 个文件")


def cmd_organize_integration(args):
    """组织集成测试"""
    organizer = IntegrationTestOrganizer(args.project_root)
    
    if not args.dry_run:
        organizer.create_integration_test_structure()
        report = organizer.organize_integration_tests()
        
        logger.info("集成测试组织结果:")
        logger.info("=" * 30)
        logger.info(f"组织文件: {len(report['organized_files'])}")
        logger.info(f"创建目录: {len(report['created_directories'])}")
        logger.info(f"错误: {len(report['errors'])}")
        
        if report['organized_files']:
            logger.info("\n组织的文件:")
            for file in report['organized_files']:
                logger.info(f"  - {file}")
    else:
        logger.info("试运行模式 - 集成测试组织将创建以下目录结构:")
        for category in organizer.integration_categories.keys():
            logger.info(f"  - tests/integration/{category}/")


def cmd_organize_e2e(args):
    """组织端到端测试"""
    manager = EndToEndTestManager(args.project_root)
    
    if not args.dry_run:
        manager.create_e2e_test_structure()
        report = manager.organize_e2e_tests()
        
        logger.info("端到端测试组织结果:")
        logger.info("=" * 30)
        logger.info(f"组织文件: {len(report['organized_files'])}")
        logger.info(f"创建目录: {len(report['created_directories'])}")
        logger.info(f"错误: {len(report['errors'])}")
        
        if report['organized_files']:
            logger.info("\n组织的文件:")
            for file in report['organized_files']:
                logger.info(f"  - {file}")
    else:
        logger.info("试运行模式 - 端到端测试组织将创建以下目录结构:")
        for scenario in manager.e2e_scenarios.keys():
            logger.info(f"  - tests/end_to_end/{scenario}/")


def cmd_create_e2e_template(args):
    """创建端到端测试模板"""
    manager = EndToEndTestManager(args.project_root)
    test_file = manager.create_e2e_test_template(args.scenario, args.test_name)
    logger.info(f"创建端到端测试模板: {test_file}")


def cmd_validate_structure(args):
    """验证测试结构"""
    system = TestOrganizationSystem(args.project_root)
    
    logger.info("验证测试文件结构...")
    logger.info("=" * 40)
    
    # 验证单元测试镜像
    mirror_report = system.mirror_tool.validate_mirror_structure()
    logger.info(f"单元测试镜像验证:")
    logger.info(f"  缺失目录: {len(mirror_report['missing_directories'])}")
    logger.info(f"  缺失测试文件: {len(mirror_report['missing_test_files'])}")
    logger.info(f"  孤立测试文件: {len(mirror_report['orphaned_test_files'])}")
    logger.info(f"  有效映射: {len(mirror_report['valid_mappings'])}")
    
    # 检查目录结构
    tests_root = args.project_root / "tests"
    required_dirs = ["unit", "integration", "end_to_end"]
    
    logger.info(f"\n测试目录结构检查:")
    for dir_name in required_dirs:
        dir_path = tests_root / dir_name
        status = "✓" if dir_path.exists() else "✗"
        logger.info(f"  {status} tests/{dir_name}/")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="测试文件组织系统命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 组织所有测试文件（试运行）
  python test_organization_cli.py organize-all --dry-run
  
  # 实际执行测试文件组织
  python test_organization_cli.py organize-all
  
  # 只分类测试文件
  python test_organization_cli.py classify --dry-run
  
  # 镜像单元测试结构
  python test_organization_cli.py mirror
  
  # 验证测试结构
  python test_organization_cli.py validate
        """
    )
    
    parser.add_argument("--project-root", type=Path, default=Path.cwd(),
                       help="项目根目录路径 (默认: 当前目录)")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # organize-all 命令
    organize_parser = subparsers.add_parser("organize-all", help="组织所有测试文件")
    organize_parser.add_argument("--dry-run", action="store_true", help="试运行模式，不实际修改文件")
    organize_parser.set_defaults(func=cmd_organize_all)
    
    # classify 命令
    classify_parser = subparsers.add_parser("classify", help="分类测试文件")
    classify_parser.add_argument("--dry-run", action="store_true", help="试运行模式，不实际修改文件")
    classify_parser.set_defaults(func=cmd_classify_tests)
    
    # mirror 命令
    mirror_parser = subparsers.add_parser("mirror", help="镜像单元测试结构")
    mirror_parser.add_argument("--dry-run", action="store_true", help="试运行模式，不实际修改文件")
    mirror_parser.set_defaults(func=cmd_mirror_structure)
    
    # organize-integration 命令
    integration_parser = subparsers.add_parser("organize-integration", help="组织集成测试")
    integration_parser.add_argument("--dry-run", action="store_true", help="试运行模式，不实际修改文件")
    integration_parser.set_defaults(func=cmd_organize_integration)
    
    # organize-e2e 命令
    e2e_parser = subparsers.add_parser("organize-e2e", help="组织端到端测试")
    e2e_parser.add_argument("--dry-run", action="store_true", help="试运行模式，不实际修改文件")
    e2e_parser.set_defaults(func=cmd_organize_e2e)
    
    # create-e2e-template 命令
    template_parser = subparsers.add_parser("create-e2e-template", help="创建端到端测试模板")
    template_parser.add_argument("scenario", help="测试场景 (user_workflows, api_automation, system_integration, performance, cross_platform)")
    template_parser.add_argument("test_name", help="测试名称")
    template_parser.set_defaults(func=cmd_create_e2e_template)
    
    # validate 命令
    validate_parser = subparsers.add_parser("validate", help="验证测试结构")
    validate_parser.set_defaults(func=cmd_validate_structure)
    
    args = parser.parse_args()
    
    if not hasattr(args, 'func'):
        parser.print_help()
        return
    
    try:
        args.func(args)
    except Exception as e:
        logger.info(f"错误: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()