import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
测试文件组织系统的测试

验证测试文件组织系统的各个组件功能是否正常工作。
"""

import unittest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tools.file_organization.test_organization_system import (
    TestOrganizationSystem,
    TestFileClassifier,
    UnitTestMirrorTool,
    IntegrationTestOrganizer,
    EndToEndTestManager,
    TestType
)


class TestTestFileClassifier(unittest.TestCase):
    """测试文件分类器测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.classifier = TestFileClassifier(self.temp_dir)
        
        # 创建测试目录结构
        (self.temp_dir / "tests").mkdir()
        (self.temp_dir / "src").mkdir()
    
    def tearDown(self):
        """测试后置清理"""
        shutil.rmtree(self.temp_dir)
    
    def test_classify_unit_test_file(self):
        """测试单元测试文件分类"""
        test_file = self.temp_dir / "tests" / "test_example.py"
        test_file.touch()
        
        result = self.classifier.classify_test_file(test_file)
        self.assertEqual(result, TestType.UNIT)
    
    def test_classify_integration_test_file(self):
        """测试集成测试文件分类"""
        test_file = self.temp_dir / "tests" / "test_integration_example.py"
        test_file.touch()
        
        result = self.classifier.classify_test_file(test_file)
        self.assertEqual(result, TestType.INTEGRATION)
    
    def test_classify_e2e_test_file(self):
        """测试端到端测试文件分类"""
        test_file = self.temp_dir / "tests" / "test_e2e_example.py"
        test_file.touch()
        
        result = self.classifier.classify_test_file(test_file)
        self.assertEqual(result, TestType.END_TO_END)
    
    def test_get_target_directory(self):
        """测试获取目标目录"""
        target_dir = self.classifier.get_target_directory(TestType.UNIT, "core")
        expected = self.temp_dir / "tests" / "unit" / "core"
        self.assertEqual(target_dir, expected)


class TestUnitTestMirrorTool(unittest.TestCase):
    """单元测试镜像工具测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.mirror_tool = UnitTestMirrorTool(self.temp_dir)
        
        # 创建测试目录结构
        (self.temp_dir / "src").mkdir()
        (self.temp_dir / "src" / "core").mkdir()
        (self.temp_dir / "tests" / "unit").mkdir(parents=True)
        
        # 创建测试源文件
        (self.temp_dir / "src" / "example.py").touch()
        (self.temp_dir / "src" / "core" / "engine.py").touch()
    
    def tearDown(self):
        """测试后置清理"""
        shutil.rmtree(self.temp_dir)
    
    def test_mirror_src_structure(self):
        """测试镜像源代码结构"""
        report = self.mirror_tool.mirror_src_structure(create_missing=True)
        
        # 验证创建了对应的测试文件
        self.assertTrue((self.temp_dir / "tests" / "unit" / "test_example.py").exists())
        self.assertTrue((self.temp_dir / "tests" / "unit" / "core" / "test_engine.py").exists())
        
        # 验证报告内容
        self.assertIn("test_example.py", report["created_test_files"])
        self.assertIn("core/test_engine.py", report["created_test_files"])
    
    def test_validate_mirror_structure(self):
        """测试验证镜像结构"""
        # 先创建镜像结构
        self.mirror_tool.mirror_src_structure(create_missing=True)
        
        # 验证结构
        report = self.mirror_tool.validate_mirror_structure()
        
        # 应该没有缺失的文件
        self.assertEqual(len(report["missing_test_files"]), 0)
        self.assertGreater(len(report["valid_mappings"]), 0)


class TestIntegrationTestOrganizer(unittest.TestCase):
    """集成测试组织器测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.organizer = IntegrationTestOrganizer(self.temp_dir)
        
        # 创建测试目录结构
        (self.temp_dir / "tests" / "integration").mkdir(parents=True)
        
        # 创建测试文件
        (self.temp_dir / "tests" / "integration" / "test_data_integration.py").touch()
        (self.temp_dir / "tests" / "integration" / "test_system_integration.py").touch()
    
    def tearDown(self):
        """测试后置清理"""
        shutil.rmtree(self.temp_dir)
    
    def test_create_integration_test_structure(self):
        """测试创建集成测试结构"""
        self.organizer.create_integration_test_structure()
        
        # 验证创建了预期的目录
        for category in self.organizer.integration_categories.keys():
            category_dir = self.temp_dir / "tests" / "integration" / category
            self.assertTrue(category_dir.exists())
            self.assertTrue((category_dir / "__init__.py").exists())
    
    def test_categorize_integration_test(self):
        """测试集成测试分类"""
        result = self.organizer._categorize_integration_test("test_data_integration.py")
        self.assertEqual(result, "data")
        
        result = self.organizer._categorize_integration_test("test_system_integration.py")
        self.assertEqual(result, "system")


class TestEndToEndTestManager(unittest.TestCase):
    """端到端测试管理器测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.manager = EndToEndTestManager(self.temp_dir)
        
        # 创建测试目录结构
        (self.temp_dir / "tests" / "end_to_end").mkdir(parents=True)
        
        # 创建测试文件
        (self.temp_dir / "tests" / "end_to_end" / "test_api_automation.py").touch()
        (self.temp_dir / "tests" / "end_to_end" / "test_performance_load.py").touch()
    
    def tearDown(self):
        """测试后置清理"""
        shutil.rmtree(self.temp_dir)
    
    def test_create_e2e_test_structure(self):
        """测试创建端到端测试结构"""
        self.manager.create_e2e_test_structure()
        
        # 验证创建了预期的目录
        for scenario in self.manager.e2e_scenarios.keys():
            scenario_dir = self.temp_dir / "tests" / "end_to_end" / scenario
            self.assertTrue(scenario_dir.exists())
            self.assertTrue((scenario_dir / "__init__.py").exists())
    
    def test_categorize_e2e_test(self):
        """测试端到端测试分类"""
        result = self.manager._categorize_e2e_test("test_api_automation.py")
        self.assertEqual(result, "api_automation")
        
        result = self.manager._categorize_e2e_test("test_performance_load.py")
        self.assertEqual(result, "performance")
    
    def test_create_e2e_test_template(self):
        """测试创建端到端测试模板"""
        test_file = self.manager.create_e2e_test_template("user_workflows", "login_flow")
        
        # 验证文件被创建
        self.assertTrue(test_file.exists())
        
        # 验证文件内容
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self.assertIn("login_flow端到端测试", content)
        self.assertIn("TestLoginFlowE2E", content)
        self.assertIn("test_login_flow_complete_flow", content)


class TestTestOrganizationSystem(unittest.TestCase):
    """测试文件组织系统测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.system = TestOrganizationSystem(self.temp_dir)
        
        # 创建基本目录结构
        (self.temp_dir / "src").mkdir()
        (self.temp_dir / "src" / "core").mkdir()
        (self.temp_dir / "tests").mkdir()
        
        # 创建测试源文件
        (self.temp_dir / "src" / "example.py").touch()
        (self.temp_dir / "src" / "core" / "engine.py").touch()
        
        # 创建测试文件
        (self.temp_dir / "tests" / "test_example.py").touch()
        (self.temp_dir / "tests" / "test_integration_example.py").touch()
    
    def tearDown(self):
        """测试后置清理"""
        shutil.rmtree(self.temp_dir)
    
    def test_organize_all_tests_dry_run(self):
        """测试组织所有测试文件（试运行模式）"""
        report = self.system.organize_all_tests(dry_run=True)
        
        # 验证报告结构
        self.assertIn("classification", report)
        self.assertIn("mirror", report)
        self.assertIn("integration", report)
        self.assertIn("e2e", report)
        self.assertIn("summary", report)
        
        # 验证摘要状态
        self.assertEqual(report["summary"]["status"], "成功")
        self.assertEqual(report["summary"]["mode"], "试运行")
    
    def test_generate_report(self):
        """测试生成报告"""
        # 创建测试报告数据
        test_report = {
            "classification": {
                "total_files": 2,
                "by_type": {
                    "unit": 1,
                    "integration": 1
                }
            },
            "mirror": {
                "created_directories": ["core"],
                "created_test_files": ["test_example.py", "core/test_engine.py"],
                "existing_files": [],
                "errors": []
            },
            "integration": {"message": "试运行模式 - 跳过集成测试组织"},
            "e2e": {"message": "试运行模式 - 跳过端到端测试组织"},
            "summary": {
                "status": "成功",
                "mode": "试运行",
                "total_operations": 4
            }
        }
        
        report_text = self.system.generate_report(test_report)
        
        # 验证报告内容
        self.assertIn("测试文件组织系统报告", report_text)
        self.assertIn("执行状态: 成功", report_text)
        self.assertIn("执行模式: 试运行", report_text)
        self.assertIn("总文件数: 2", report_text)


class TestSystemIntegration(unittest.TestCase):
    """系统集成测试类"""
    
    def setUp(self):
        """测试前置设置"""
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # 创建完整的项目结构
        self._create_project_structure()
    
    def tearDown(self):
        """测试后置清理"""
        shutil.rmtree(self.temp_dir)
    
    def _create_project_structure(self):
        """创建项目结构"""
        # 创建源代码目录
        src_dirs = [
            "src/core",
            "src/data",
            "src/strategies",
            "src/indicators",
            "src/risk"
        ]
        
        for dir_path in src_dirs:
            (self.temp_dir / dir_path).mkdir(parents=True)
        
        # 创建源文件
        src_files = [
            "src/application/main.py",
            "src/core/engine.py",
            "src/core/trading.py",
            "src/data/manager.py",
            "src/data/cache.py",
            "src/strategies/base.py",
            "src/indicators/moving_averages.py",
            "src/risk/manager.py"
        ]
        
        for file_path in src_files:
            (self.temp_dir / file_path).touch()
        
        # 创建测试目录
        (self.temp_dir / "tests").mkdir()
        
        # 创建一些现有的测试文件
        test_files = [
            "tests/test_main.py",
            "tests/test_integration_data.py",
            "tests/test_e2e_trading.py"
        ]
        
        for file_path in test_files:
            (self.temp_dir / file_path).touch()
    
    def test_complete_organization_workflow(self):
        """测试完整的组织工作流程"""
        system = TestOrganizationSystem(self.temp_dir)
        
        # 执行完整的组织流程
        report = system.organize_all_tests(dry_run=False)
        
        # 验证执行成功
        self.assertEqual(report["summary"]["status"], "成功")
        
        # 验证单元测试结构被创建
        self.assertTrue((self.temp_dir / "tests" / "unit" / "test_main.py").exists())
        self.assertTrue((self.temp_dir / "tests" / "unit" / "core" / "test_engine.py").exists())
        self.assertTrue((self.temp_dir / "tests" / "unit" / "data" / "test_manager.py").exists())
        
        # 验证集成测试目录被创建
        self.assertTrue((self.temp_dir / "tests" / "integration" / "data").exists())
        self.assertTrue((self.temp_dir / "tests" / "integration" / "system").exists())
        
        # 验证端到端测试目录被创建
        self.assertTrue((self.temp_dir / "tests" / "end_to_end" / "user_workflows").exists())
        self.assertTrue((self.temp_dir / "tests" / "end_to_end" / "api_automation").exists())
    
    def test_validation_after_organization(self):
        """测试组织后的验证"""
        system = TestOrganizationSystem(self.temp_dir)
        
        # 先执行组织
        system.organize_all_tests(dry_run=False)
        
        # 验证镜像结构
        mirror_report = system.mirror_tool.validate_mirror_structure()
        
        # 应该没有缺失的测试文件（因为刚刚创建了）
        self.assertEqual(len(mirror_report["missing_test_files"]), 0)
        self.assertGreater(len(mirror_report["valid_mappings"]), 0)


def run_comprehensive_test():
    """运行综合测试"""
    logger.info("开始运行测试文件组织系统综合测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestTestFileClassifier,
        TestUnitTestMirrorTool,
        TestIntegrationTestOrganizer,
        TestEndToEndTestManager,
        TestTestOrganizationSystem,
        TestSystemIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果摘要
    logger.info(f"\n测试结果摘要:")
    logger.info(f"运行测试数: {result.testsRun}")
    logger.info(f"失败数: {len(result.failures)}")
    logger.info(f"错误数: {len(result.errors)}")
    logger.info(f"跳过数: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    if result.failures:
        logger.info(f"\n失败的测试:")
        for test, traceback in result.failures:
            logger.info(f"  - {test}: {traceback}")
    
    if result.errors:
        logger.info(f"\n错误的测试:")
        for test, traceback in result.errors:
            logger.info(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    logger.info(f"\n测试{'通过' if success else '失败'}!")
    
    return success


if __name__ == '__main__':
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)