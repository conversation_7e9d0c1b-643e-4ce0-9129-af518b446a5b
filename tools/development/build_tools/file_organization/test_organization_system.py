#!/usr/bin/env python3
"""
测试文件组织系统

提供测试文件的自动分类、结构镜像和管理功能，确保测试文件按照规范组织。
"""

import os
import re
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestType(Enum):
    """测试类型枚举"""
    UNIT = "unit"
    INTEGRATION = "integration"
    END_TO_END = "end_to_end"
    PERFORMANCE = "performance"
    CHINESE_LOCALIZATION = "chinese_localization"


@dataclass
class TestFileInfo:
    """测试文件信息"""
    file_path: Path
    test_type: TestType
    module_path: Optional[str] = None
    target_directory: Optional[Path] = None


class TestFileClassifier:
    """测试文件自动分类工具"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.tests_root = project_root / "tests"
        
        # 测试类型识别规则 - 按优先级排序，更具体的规则在前
        self.classification_rules = {
            TestType.INTEGRATION: [
                r".*integration.*\.py$",
                r".*test.*integration.*\.py$",
                r"test_.*integration.*\.py$"
            ],
            TestType.END_TO_END: [
                r".*e2e.*\.py$",
                r".*end.*to.*end.*\.py$",
                r".*test.*e2e.*\.py$",
                r"test_.*e2e.*\.py$"
            ],
            TestType.PERFORMANCE: [
                r".*performance.*\.py$",
                r".*test.*performance.*\.py$",
                r"test_.*performance.*\.py$",
                r".*load.*test.*\.py$"
            ],
            TestType.CHINESE_LOCALIZATION: [
                r".*chinese.*\.py$",
                r".*localization.*\.py$",
                r".*test.*chinese.*\.py$"
            ],
            TestType.UNIT: [
                r"test_.*\.py$",
                r".*_test\.py$",
                r".*test.*unit.*\.py$"
            ]
        }
    
    def classify_test_file(self, file_path: Path) -> TestType:
        """
        根据文件名和内容分类测试文件
        
        Args:
            file_path: 测试文件路径
            
        Returns:
            测试类型
        """
        file_name = file_path.name.lower()
        
        # 检查文件名模式 - 按优先级顺序检查
        for test_type, patterns in self.classification_rules.items():
            for pattern in patterns:
                if re.search(pattern, file_name):
                    return test_type
        
        # 检查文件内容
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().lower()
                
            # 基于内容的分类规则
            if any(keyword in content for keyword in ['integration', 'integrationtest']):
                return TestType.INTEGRATION
            elif any(keyword in content for keyword in ['e2e', 'end_to_end', 'selenium', 'playwright']):
                return TestType.END_TO_END
            elif any(keyword in content for keyword in ['performance', 'load_test', 'benchmark']):
                return TestType.PERFORMANCE
            elif any(keyword in content for keyword in ['chinese', 'localization', '中文']):
                return TestType.CHINESE_LOCALIZATION
                
        except Exception as e:
            logger.warning(f"无法读取文件内容进行分类: {file_path}, 错误: {e}")
        
        # 默认为单元测试
        return TestType.UNIT
    
    def get_target_directory(self, test_type: TestType, module_path: Optional[str] = None) -> Path:
        """
        获取测试文件的目标目录
        
        Args:
            test_type: 测试类型
            module_path: 模块路径（用于单元测试）
            
        Returns:
            目标目录路径
        """
        base_dir = self.tests_root / test_type.value
        
        if test_type == TestType.UNIT and module_path:
            return base_dir / module_path
        
        return base_dir
    
    def organize_test_files(self, dry_run: bool = True) -> List[TestFileInfo]:
        """
        组织所有测试文件到正确的目录
        
        Args:
            dry_run: 是否为试运行模式
            
        Returns:
            测试文件信息列表
        """
        test_files = []
        
        # 扫描所有测试文件
        for root, dirs, files in os.walk(self.tests_root):
            for file in files:
                if file.endswith('.py') and file.startswith('test_'):
                    file_path = Path(root) / file
                    test_type = self.classify_test_file(file_path)
                    
                    # 确定模块路径（用于单元测试）
                    module_path = None
                    if test_type == TestType.UNIT:
                        module_path = self._extract_module_path(file_path)
                    
                    target_dir = self.get_target_directory(test_type, module_path)
                    
                    test_info = TestFileInfo(
                        file_path=file_path,
                        test_type=test_type,
                        module_path=module_path,
                        target_directory=target_dir
                    )
                    test_files.append(test_info)
        
        # 执行文件移动（如果不是试运行）
        if not dry_run:
            self._move_test_files(test_files)
        
        return test_files
    
    def _extract_module_path(self, file_path: Path) -> Optional[str]:
        """
        从测试文件路径提取对应的模块路径
        
        Args:
            file_path: 测试文件路径
            
        Returns:
            模块路径
        """
        # 尝试从文件名推断模块路径
        file_name = file_path.stem
        if file_name.startswith('test_'):
            module_name = file_name[5:]  # 移除 'test_' 前缀
            
            # 检查是否存在对应的源文件
            src_root = self.project_root / "src"
            for root, dirs, files in os.walk(src_root):
                for file in files:
                    if file == f"{module_name}.py":
                        rel_path = Path(root).relative_to(src_root)
                        return str(rel_path)
        
        return None
    
    def _move_test_files(self, test_files: List[TestFileInfo]) -> None:
        """
        移动测试文件到目标目录
        
        Args:
            test_files: 测试文件信息列表
        """
        for test_info in test_files:
            target_dir = test_info.target_directory
            target_file = target_dir / test_info.file_path.name
            
            # 如果文件已经在正确位置，跳过
            if test_info.file_path == target_file:
                continue
            
            # 创建目标目录
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # 移动文件
            try:
                shutil.move(str(test_info.file_path), str(target_file))
                logger.info(f"移动测试文件: {test_info.file_path} -> {target_file}")
            except Exception as e:
                logger.error(f"移动文件失败: {test_info.file_path} -> {target_file}, 错误: {e}")


class UnitTestMirrorTool:
    """单元测试结构镜像工具"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.src_root = project_root / "src"
        self.unit_tests_root = project_root / "tests" / "unit"
    
    def mirror_src_structure(self, create_missing: bool = True) -> Dict[str, List[str]]:
        """
        镜像src目录结构到tests/unit目录
        
        Args:
            create_missing: 是否创建缺失的目录和文件
            
        Returns:
            镜像结果报告
        """
        report = {
            "created_directories": [],
            "created_test_files": [],
            "existing_files": [],
            "errors": []
        }
        
        # 遍历src目录
        for root, dirs, files in os.walk(self.src_root):
            # 计算相对路径
            rel_path = Path(root).relative_to(self.src_root)
            target_dir = self.unit_tests_root / rel_path
            
            # 创建对应的测试目录
            if create_missing and not target_dir.exists():
                target_dir.mkdir(parents=True, exist_ok=True)
                report["created_directories"].append(str(rel_path))
                logger.info(f"创建测试目录: {target_dir}")
            
            # 为每个Python文件创建对应的测试文件
            for file in files:
                if file.endswith('.py') and not file.startswith('__'):
                    module_name = file[:-3]  # 移除.py扩展名
                    test_file_name = f"test_{module_name}.py"
                    test_file_path = target_dir / test_file_name
                    
                    if create_missing and not test_file_path.exists():
                        try:
                            self._create_test_file_template(
                                test_file_path, 
                                module_name, 
                                rel_path
                            )
                            report["created_test_files"].append(str(test_file_path.relative_to(self.unit_tests_root)))
                            logger.info(f"创建测试文件: {test_file_path}")
                        except Exception as e:
                            error_msg = f"创建测试文件失败: {test_file_path}, 错误: {e}"
                            report["errors"].append(error_msg)
                            logger.error(error_msg)
                    else:
                        report["existing_files"].append(str(test_file_path.relative_to(self.unit_tests_root)))
        
        return report
    
    def _create_test_file_template(self, test_file_path: Path, module_name: str, module_path: Path) -> None:
        """
        创建测试文件模板
        
        Args:
            test_file_path: 测试文件路径
            module_name: 模块名称
            module_path: 模块路径
        """
        # 构建导入路径
        import_path = "src"
        if str(module_path) != ".":
            import_path += "." + str(module_path).replace("/", ".")
        import_path += f".{module_name}"
        
        template = f'''"""
{module_name}模块的单元测试

测试{module_name}模块的核心功能和边界条件。
"""

import pytest
import unittest
from unittest.mock import Mock, patch, MagicMock

try:
    from {import_path} import *
except ImportError as e:
    pytest.skip(f"无法导入模块 {import_path}: {{e}}", allow_module_level=True)


class Test{module_name.title().replace('_', '')}(unittest.TestCase):
    """
    {module_name}模块测试类
    """
    
    def setUp(self):
        """测试前置设置"""
        pass
    
    def tearDown(self):
        """测试后置清理"""
        pass
    
    def test_placeholder(self):
        """占位测试方法 - 请根据实际模块功能编写具体测试"""
        # TODO: 根据{module_name}模块的实际功能编写测试
        self.assertTrue(True, "占位测试 - 请实现具体的测试逻辑")
    
    # TODO: 添加更多测试方法
    # def test_specific_function(self):
    #     """测试特定功能"""
    #     pass


if __name__ == '__main__':
    unittest.main()
'''
        
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(template)
    
    def validate_mirror_structure(self) -> Dict[str, List[str]]:
        """
        验证镜像结构的完整性
        
        Returns:
            验证结果报告
        """
        report = {
            "missing_directories": [],
            "missing_test_files": [],
            "orphaned_test_files": [],
            "valid_mappings": []
        }
        
        # 检查缺失的目录和文件
        for root, dirs, files in os.walk(self.src_root):
            rel_path = Path(root).relative_to(self.src_root)
            target_dir = self.unit_tests_root / rel_path
            
            if not target_dir.exists():
                report["missing_directories"].append(str(rel_path))
            
            for file in files:
                if file.endswith('.py') and not file.startswith('__'):
                    module_name = file[:-3]
                    test_file_name = f"test_{module_name}.py"
                    test_file_path = target_dir / test_file_name
                    
                    if not test_file_path.exists():
                        report["missing_test_files"].append(str(rel_path / test_file_name))
                    else:
                        report["valid_mappings"].append(str(rel_path / test_file_name))
        
        # 检查孤立的测试文件
        for root, dirs, files in os.walk(self.unit_tests_root):
            rel_path = Path(root).relative_to(self.unit_tests_root)
            src_dir = self.src_root / rel_path
            
            for file in files:
                if file.startswith('test_') and file.endswith('.py'):
                    module_name = file[5:-3]  # 移除test_前缀和.py后缀
                    src_file = src_dir / f"{module_name}.py"
                    
                    if not src_file.exists():
                        report["orphaned_test_files"].append(str(rel_path / file))
        
        return report


class IntegrationTestOrganizer:
    """集成测试组织器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.integration_tests_root = project_root / "tests" / "integration"
        
        # 集成测试分类规则
        self.integration_categories = {
            "data": ["data", "database", "cache", "storage", "adapter"],
            "system": ["system", "core", "engine", "trading"],
            "api": ["api", "web", "rest", "endpoint"],
            "strategy": ["strategy", "backtest", "portfolio"],
            "risk": ["risk", "monitoring", "alert"],
            "external": ["external", "third_party", "integration"]
        }
    
    def organize_integration_tests(self) -> Dict[str, List[str]]:
        """
        组织集成测试文件到相应的子目录
        
        Returns:
            组织结果报告
        """
        report = {
            "organized_files": [],
            "created_directories": [],
            "errors": []
        }
        
        # 确保集成测试根目录存在
        self.integration_tests_root.mkdir(parents=True, exist_ok=True)
        
        # 扫描现有的集成测试文件
        for root, dirs, files in os.walk(self.integration_tests_root):
            for file in files:
                if file.startswith('test_') and file.endswith('.py'):
                    file_path = Path(root) / file
                    category = self._categorize_integration_test(file)
                    
                    if category:
                        target_dir = self.integration_tests_root / category
                        target_file = target_dir / file
                        
                        # 如果文件不在正确位置
                        if file_path != target_file:
                            try:
                                # 创建目标目录
                                if not target_dir.exists():
                                    target_dir.mkdir(parents=True, exist_ok=True)
                                    report["created_directories"].append(category)
                                
                                # 移动文件
                                shutil.move(str(file_path), str(target_file))
                                report["organized_files"].append(f"{file} -> {category}/")
                                logger.info(f"移动集成测试文件: {file} -> {category}/")
                                
                            except Exception as e:
                                error_msg = f"移动集成测试文件失败: {file}, 错误: {e}"
                                report["errors"].append(error_msg)
                                logger.error(error_msg)
        
        return report
    
    def _categorize_integration_test(self, filename: str) -> Optional[str]:
        """
        根据文件名分类集成测试
        
        Args:
            filename: 文件名
            
        Returns:
            分类名称
        """
        filename_lower = filename.lower()
        
        for category, keywords in self.integration_categories.items():
            if any(keyword in filename_lower for keyword in keywords):
                return category
        
        return "system"  # 默认分类
    
    def create_integration_test_structure(self) -> None:
        """创建标准的集成测试目录结构"""
        for category in self.integration_categories.keys():
            category_dir = self.integration_tests_root / category
            category_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建__init__.py文件
            init_file = category_dir / "__init__.py"
            if not init_file.exists():
                with open(init_file, 'w', encoding='utf-8') as f:
                    f.write(f'"""{category}集成测试模块"""\n')
            
            logger.info(f"创建集成测试目录: {category}")


class EndToEndTestManager:
    """端到端测试管理器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.e2e_tests_root = project_root / "tests" / "end_to_end"
        
        # 端到端测试场景分类
        self.e2e_scenarios = {
            "user_workflows": ["user", "workflow", "journey"],
            "api_automation": ["api", "automation", "rest"],
            "system_integration": ["system", "integration", "full"],
            "performance": ["performance", "load", "stress"],
            "cross_platform": ["cross", "platform", "multi"]
        }
    
    def organize_e2e_tests(self) -> Dict[str, List[str]]:
        """
        组织端到端测试文件
        
        Returns:
            组织结果报告
        """
        report = {
            "organized_files": [],
            "created_directories": [],
            "errors": []
        }
        
        # 确保端到端测试根目录存在
        self.e2e_tests_root.mkdir(parents=True, exist_ok=True)
        
        # 扫描现有的端到端测试文件
        for root, dirs, files in os.walk(self.e2e_tests_root):
            for file in files:
                if file.startswith('test_') and file.endswith('.py'):
                    file_path = Path(root) / file
                    scenario = self._categorize_e2e_test(file)
                    
                    if scenario:
                        target_dir = self.e2e_tests_root / scenario
                        target_file = target_dir / file
                        
                        # 如果文件不在正确位置
                        if file_path != target_file:
                            try:
                                # 创建目标目录
                                if not target_dir.exists():
                                    target_dir.mkdir(parents=True, exist_ok=True)
                                    report["created_directories"].append(scenario)
                                
                                # 移动文件
                                shutil.move(str(file_path), str(target_file))
                                report["organized_files"].append(f"{file} -> {scenario}/")
                                logger.info(f"移动端到端测试文件: {file} -> {scenario}/")
                                
                            except Exception as e:
                                error_msg = f"移动端到端测试文件失败: {file}, 错误: {e}"
                                report["errors"].append(error_msg)
                                logger.error(error_msg)
        
        return report
    
    def _categorize_e2e_test(self, filename: str) -> Optional[str]:
        """
        根据文件名分类端到端测试
        
        Args:
            filename: 文件名
            
        Returns:
            场景分类名称
        """
        filename_lower = filename.lower()
        
        for scenario, keywords in self.e2e_scenarios.items():
            if any(keyword in filename_lower for keyword in keywords):
                return scenario
        
        return "user_workflows"  # 默认分类
    
    def create_e2e_test_structure(self) -> None:
        """创建标准的端到端测试目录结构"""
        for scenario in self.e2e_scenarios.keys():
            scenario_dir = self.e2e_tests_root / scenario
            scenario_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建__init__.py文件
            init_file = scenario_dir / "__init__.py"
            if not init_file.exists():
                with open(init_file, 'w', encoding='utf-8') as f:
                    f.write(f'"""{scenario}端到端测试模块"""\n')
            
            logger.info(f"创建端到端测试目录: {scenario}")
    
    def create_e2e_test_template(self, scenario: str, test_name: str) -> Path:
        """
        创建端到端测试模板
        
        Args:
            scenario: 测试场景
            test_name: 测试名称
            
        Returns:
            创建的测试文件路径
        """
        scenario_dir = self.e2e_tests_root / scenario
        scenario_dir.mkdir(parents=True, exist_ok=True)
        
        test_file = scenario_dir / f"test_{test_name}.py"
        
        template = f'''"""
{test_name}端到端测试

测试{test_name}的完整用户流程和系统集成。
"""

import pytest
import asyncio
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


class Test{test_name.title().replace('_', '')}E2E:
    """
    {test_name}端到端测试类
    """
    
    @pytest.fixture(autouse=True)
    def setup_and_teardown(self):
        """测试前置设置和后置清理"""
        # 前置设置
        logger.info(f"开始{test_name}端到端测试...")
        
        yield
        
        # 后置清理
        logger.info(f"完成{test_name}端到端测试清理...")
    
    def test_{test_name}_complete_flow(self):
        """测试{test_name}的完整流程"""
        # TODO: 实现完整的端到端测试流程
        assert True, "端到端测试占位符 - 请实现具体的测试逻辑"
    
    def test_{test_name}_error_handling(self):
        """测试{test_name}的错误处理"""
        # TODO: 测试错误场景和异常处理
        assert True, "错误处理测试占位符 - 请实现具体的测试逻辑"
    
    def test_{test_name}_performance(self):
        """测试{test_name}的性能表现"""
        # TODO: 测试性能指标和响应时间
        assert True, "性能测试占位符 - 请实现具体的测试逻辑"


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
'''
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(template)
        
        logger.info(f"创建端到端测试模板: {test_file}")
        return test_file


class TestOrganizationSystem:
    """测试文件组织系统主类"""
    
    def __init__(self, project_root: Optional[Path] = None):
        if project_root is None:
            project_root = Path.cwd()
        
        self.project_root = project_root
        self.classifier = TestFileClassifier(project_root)
        self.mirror_tool = UnitTestMirrorTool(project_root)
        self.integration_organizer = IntegrationTestOrganizer(project_root)
        self.e2e_manager = EndToEndTestManager(project_root)
    
    def organize_all_tests(self, dry_run: bool = True) -> Dict[str, any]:
        """
        组织所有测试文件
        
        Args:
            dry_run: 是否为试运行模式
            
        Returns:
            完整的组织结果报告
        """
        logger.info("开始组织测试文件系统...")
        
        report = {
            "classification": {},
            "mirror": {},
            "integration": {},
            "e2e": {},
            "summary": {}
        }
        
        try:
            # 1. 分类测试文件
            logger.info("1. 执行测试文件自动分类...")
            test_files = self.classifier.organize_test_files(dry_run=dry_run)
            report["classification"] = {
                "total_files": len(test_files),
                "by_type": {}
            }
            
            for test_type in TestType:
                count = len([f for f in test_files if f.test_type == test_type])
                report["classification"]["by_type"][test_type.value] = count
            
            # 2. 镜像单元测试结构
            logger.info("2. 执行单元测试结构镜像...")
            if not dry_run:
                mirror_report = self.mirror_tool.mirror_src_structure(create_missing=True)
            else:
                mirror_report = self.mirror_tool.validate_mirror_structure()
            report["mirror"] = mirror_report
            
            # 3. 组织集成测试
            logger.info("3. 执行集成测试组织...")
            if not dry_run:
                self.integration_organizer.create_integration_test_structure()
                integration_report = self.integration_organizer.organize_integration_tests()
            else:
                integration_report = {"message": "试运行模式 - 跳过集成测试组织"}
            report["integration"] = integration_report
            
            # 4. 组织端到端测试
            logger.info("4. 执行端到端测试组织...")
            if not dry_run:
                self.e2e_manager.create_e2e_test_structure()
                e2e_report = self.e2e_manager.organize_e2e_tests()
            else:
                e2e_report = {"message": "试运行模式 - 跳过端到端测试组织"}
            report["e2e"] = e2e_report
            
            # 生成摘要
            report["summary"] = {
                "status": "成功",
                "mode": "试运行" if dry_run else "实际执行",
                "total_operations": len(test_files) + len(mirror_report.get("created_test_files", []))
            }
            
            logger.info("测试文件组织系统执行完成!")
            
        except Exception as e:
            error_msg = f"测试文件组织过程中发生错误: {e}"
            logger.error(error_msg)
            report["summary"] = {
                "status": "失败",
                "error": error_msg
            }
        
        return report
    
    def generate_report(self, report: Dict[str, any]) -> str:
        """
        生成可读的报告
        
        Args:
            report: 组织结果报告
            
        Returns:
            格式化的报告字符串
        """
        lines = []
        lines.append("=" * 60)
        lines.append("测试文件组织系统报告")
        lines.append("=" * 60)
        
        # 摘要信息
        summary = report.get("summary", {})
        lines.append(f"执行状态: {summary.get('status', '未知')}")
        lines.append(f"执行模式: {summary.get('mode', '未知')}")
        lines.append(f"总操作数: {summary.get('total_operations', 0)}")
        lines.append("")
        
        # 分类结果
        classification = report.get("classification", {})
        if classification:
            lines.append("测试文件分类结果:")
            lines.append(f"  总文件数: {classification.get('total_files', 0)}")
            by_type = classification.get("by_type", {})
            for test_type, count in by_type.items():
                lines.append(f"  {test_type}: {count} 个文件")
            lines.append("")
        
        # 镜像结果
        mirror = report.get("mirror", {})
        if mirror:
            lines.append("单元测试结构镜像结果:")
            lines.append(f"  创建目录: {len(mirror.get('created_directories', []))}")
            lines.append(f"  创建测试文件: {len(mirror.get('created_test_files', []))}")
            lines.append(f"  现有文件: {len(mirror.get('existing_files', []))}")
            lines.append(f"  错误: {len(mirror.get('errors', []))}")
            lines.append("")
        
        # 集成测试结果
        integration = report.get("integration", {})
        if integration and "message" not in integration:
            lines.append("集成测试组织结果:")
            lines.append(f"  组织文件: {len(integration.get('organized_files', []))}")
            lines.append(f"  创建目录: {len(integration.get('created_directories', []))}")
            lines.append(f"  错误: {len(integration.get('errors', []))}")
            lines.append("")
        
        # 端到端测试结果
        e2e = report.get("e2e", {})
        if e2e and "message" not in e2e:
            lines.append("端到端测试组织结果:")
            lines.append(f"  组织文件: {len(e2e.get('organized_files', []))}")
            lines.append(f"  创建目录: {len(e2e.get('created_directories', []))}")
            lines.append(f"  错误: {len(e2e.get('errors', []))}")
            lines.append("")
        
        lines.append("=" * 60)
        
        return "\n".join(lines)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="测试文件组织系统")
    parser.add_argument("--project-root", type=Path, default=Path.cwd(),
                       help="项目根目录路径")
    parser.add_argument("--dry-run", action="store_true",
                       help="试运行模式，不实际移动文件")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建测试组织系统
    system = TestOrganizationSystem(args.project_root)
    
    # 执行组织
    report = system.organize_all_tests(dry_run=args.dry_run)
    
    # 输出报告
    logger.info(system.generate_report(report))


if __name__ == "__main__":
    main()