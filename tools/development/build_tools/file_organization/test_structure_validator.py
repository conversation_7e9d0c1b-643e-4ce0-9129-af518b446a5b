#!/usr/bin/env python3
"""
测试结构验证工具

验证测试文件组织是否符合项目规范，检查结构完整性和一致性。
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class ValidationIssue:
    """验证问题"""
    severity: str  # "error", "warning", "info"
    category: str  # "structure", "naming", "content", "coverage"
    message: str
    file_path: Optional[Path] = None
    suggestion: Optional[str] = None


@dataclass
class ValidationReport:
    """验证报告"""
    total_files: int
    issues: List[ValidationIssue]
    summary: Dict[str, int]
    
    def add_issue(self, issue: ValidationIssue):
        """添加验证问题"""
        self.issues.append(issue)
        if issue.severity not in self.summary:
            self.summary[issue.severity] = 0
        self.summary[issue.severity] += 1
    
    def has_errors(self) -> bool:
        """是否有错误"""
        return self.summary.get("error", 0) > 0
    
    def get_issues_by_category(self, category: str) -> List[ValidationIssue]:
        """按类别获取问题"""
        return [issue for issue in self.issues if issue.category == category]


class TestStructureValidator:
    """测试结构验证器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.src_root = project_root / "src"
        self.tests_root = project_root / "tests"
        
        # 预期的测试目录结构
        self.expected_test_directories = {
            "unit": "单元测试目录",
            "integration": "集成测试目录", 
            "end_to_end": "端到端测试目录",
            "performance": "性能测试目录",
            "chinese_localization": "中文本地化测试目录"
        }
        
        # 集成测试预期分类
        self.expected_integration_categories = [
            "data", "system", "api", "strategy", "risk", "external"
        ]
        
        # 端到端测试预期场景
        self.expected_e2e_scenarios = [
            "user_workflows", "api_automation", "system_integration", 
            "performance", "cross_platform"
        ]
    
    def validate_all(self) -> ValidationReport:
        """
        执行完整的测试结构验证
        
        Returns:
            验证报告
        """
        logger.info("开始验证测试文件结构...")
        
        report = ValidationReport(
            total_files=0,
            issues=[],
            summary={}
        )
        
        # 1. 验证基础目录结构
        self._validate_basic_structure(report)
        
        # 2. 验证单元测试结构
        self._validate_unit_test_structure(report)
        
        # 3. 验证集成测试结构
        self._validate_integration_test_structure(report)
        
        # 4. 验证端到端测试结构
        self._validate_e2e_test_structure(report)
        
        # 5. 验证测试文件命名规范
        self._validate_test_file_naming(report)
        
        # 6. 验证测试文件内容
        self._validate_test_file_content(report)
        
        # 7. 验证测试覆盖率
        self._validate_test_coverage(report)
        
        logger.info(f"测试结构验证完成，发现 {len(report.issues)} 个问题")
        return report
    
    def _validate_basic_structure(self, report: ValidationReport):
        """验证基础目录结构"""
        logger.info("验证基础目录结构...")
        
        # 检查tests根目录是否存在
        if not self.tests_root.exists():
            report.add_issue(ValidationIssue(
                severity="error",
                category="structure",
                message="tests目录不存在",
                suggestion="创建tests目录"
            ))
            return
        
        # 检查预期的测试子目录
        for dir_name, description in self.expected_test_directories.items():
            dir_path = self.tests_root / dir_name
            if not dir_path.exists():
                report.add_issue(ValidationIssue(
                    severity="warning",
                    category="structure",
                    message=f"缺少{description}: tests/{dir_name}/",
                    suggestion=f"创建tests/{dir_name}/目录"
                ))
            elif not dir_path.is_dir():
                report.add_issue(ValidationIssue(
                    severity="error",
                    category="structure",
                    message=f"tests/{dir_name}不是目录",
                    suggestion=f"删除文件并创建tests/{dir_name}/目录"
                ))
        
        # 检查__init__.py文件
        init_file = self.tests_root / "__init__.py"
        if not init_file.exists():
            report.add_issue(ValidationIssue(
                severity="info",
                category="structure",
                message="tests目录缺少__init__.py文件",
                suggestion="创建tests/__init__.py文件"
            ))
    
    def _validate_unit_test_structure(self, report: ValidationReport):
        """验证单元测试结构"""
        logger.info("验证单元测试结构...")
        
        unit_tests_root = self.tests_root / "unit"
        if not unit_tests_root.exists():
            return
        
        # 检查单元测试是否镜像src结构
        src_modules = self._get_python_modules(self.src_root)
        unit_test_files = self._get_test_files(unit_tests_root)
        
        # 检查缺失的单元测试
        for module_path in src_modules:
            expected_test_path = unit_tests_root / module_path.parent / f"test_{module_path.stem}.py"
            if not expected_test_path.exists():
                report.add_issue(ValidationIssue(
                    severity="warning",
                    category="coverage",
                    message=f"缺少单元测试: {module_path}",
                    suggestion=f"创建测试文件: {expected_test_path.relative_to(self.tests_root)}"
                ))
        
        # 检查孤立的单元测试
        for test_file in unit_test_files:
            if test_file.name.startswith("test_"):
                module_name = test_file.name[5:-3]  # 移除test_前缀和.py后缀
                rel_path = test_file.parent.relative_to(unit_tests_root)
                expected_src_file = self.src_root / rel_path / f"{module_name}.py"
                
                if not expected_src_file.exists():
                    report.add_issue(ValidationIssue(
                        severity="info",
                        category="structure",
                        message=f"孤立的单元测试文件: {test_file.relative_to(self.tests_root)}",
                        file_path=test_file,
                        suggestion=f"检查对应的源文件是否存在: {expected_src_file.relative_to(self.project_root)}"
                    ))
    
    def _validate_integration_test_structure(self, report: ValidationReport):
        """验证集成测试结构"""
        logger.info("验证集成测试结构...")
        
        integration_tests_root = self.tests_root / "integration"
        if not integration_tests_root.exists():
            return
        
        # 检查集成测试分类目录
        existing_categories = set()
        for item in integration_tests_root.iterdir():
            if item.is_dir():
                existing_categories.add(item.name)
        
        # 检查是否有推荐的分类目录
        missing_categories = set(self.expected_integration_categories) - existing_categories
        if missing_categories:
            report.add_issue(ValidationIssue(
                severity="info",
                category="structure",
                message=f"建议创建集成测试分类目录: {', '.join(missing_categories)}",
                suggestion="根据需要创建相应的分类目录"
            ))
        
        # 检查未分类的集成测试文件
        for test_file in integration_tests_root.glob("test_*.py"):
            report.add_issue(ValidationIssue(
                severity="warning",
                category="structure",
                message=f"未分类的集成测试文件: {test_file.name}",
                file_path=test_file,
                suggestion="将测试文件移动到相应的分类目录中"
            ))
    
    def _validate_e2e_test_structure(self, report: ValidationReport):
        """验证端到端测试结构"""
        logger.info("验证端到端测试结构...")
        
        e2e_tests_root = self.tests_root / "end_to_end"
        if not e2e_tests_root.exists():
            return
        
        # 检查端到端测试场景目录
        existing_scenarios = set()
        for item in e2e_tests_root.iterdir():
            if item.is_dir():
                existing_scenarios.add(item.name)
        
        # 检查是否有推荐的场景目录
        missing_scenarios = set(self.expected_e2e_scenarios) - existing_scenarios
        if missing_scenarios:
            report.add_issue(ValidationIssue(
                severity="info",
                category="structure",
                message=f"建议创建端到端测试场景目录: {', '.join(missing_scenarios)}",
                suggestion="根据需要创建相应的场景目录"
            ))
        
        # 检查未分类的端到端测试文件
        for test_file in e2e_tests_root.glob("test_*.py"):
            report.add_issue(ValidationIssue(
                severity="warning",
                category="structure",
                message=f"未分类的端到端测试文件: {test_file.name}",
                file_path=test_file,
                suggestion="将测试文件移动到相应的场景目录中"
            ))
    
    def _validate_test_file_naming(self, report: ValidationReport):
        """验证测试文件命名规范"""
        logger.info("验证测试文件命名规范...")
        
        # 命名规范模式
        naming_patterns = {
            "unit": r"^test_[a-z_]+\.py$",
            "integration": r"^test_[a-z_]+(integration)?\.py$",
            "end_to_end": r"^test_[a-z_]+(e2e|end_to_end)?\.py$",
            "performance": r"^test_[a-z_]+(performance|load)?\.py$"
        }
        
        for test_type, pattern in naming_patterns.items():
            test_dir = self.tests_root / test_type
            if not test_dir.exists():
                continue
            
            for test_file in self._get_test_files(test_dir):
                if not re.match(pattern, test_file.name):
                    report.add_issue(ValidationIssue(
                        severity="warning",
                        category="naming",
                        message=f"测试文件命名不符合规范: {test_file.relative_to(self.tests_root)}",
                        file_path=test_file,
                        suggestion=f"重命名文件以符合模式: {pattern}"
                    ))
    
    def _validate_test_file_content(self, report: ValidationReport):
        """验证测试文件内容"""
        logger.info("验证测试文件内容...")
        
        for test_file in self._get_all_test_files():
            report.total_files += 1
            
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查基本内容要求
                self._check_test_file_content(test_file, content, report)
                
            except Exception as e:
                report.add_issue(ValidationIssue(
                    severity="error",
                    category="content",
                    message=f"无法读取测试文件: {test_file.relative_to(self.tests_root)}",
                    file_path=test_file,
                    suggestion=f"检查文件编码和权限: {e}"
                ))
    
    def _check_test_file_content(self, test_file: Path, content: str, report: ValidationReport):
        """检查测试文件内容"""
        # 检查是否有测试方法
        if not re.search(r'def test_\w+\(', content):
            report.add_issue(ValidationIssue(
                severity="warning",
                category="content",
                message=f"测试文件没有测试方法: {test_file.relative_to(self.tests_root)}",
                file_path=test_file,
                suggestion="添加以test_开头的测试方法"
            ))
        
        # 检查是否有中文注释或文档字符串
        if not re.search(r'[\u4e00-\u9fff]', content):
            report.add_issue(ValidationIssue(
                severity="info",
                category="content",
                message=f"测试文件缺少中文注释: {test_file.relative_to(self.tests_root)}",
                file_path=test_file,
                suggestion="添加中文注释和文档字符串"
            ))
        
        # 检查是否有导入语句
        if not re.search(r'^import |^from .* import', content, re.MULTILINE):
            report.add_issue(ValidationIssue(
                severity="warning",
                category="content",
                message=f"测试文件缺少导入语句: {test_file.relative_to(self.tests_root)}",
                file_path=test_file,
                suggestion="添加必要的导入语句"
            ))
        
        # 检查是否有断言
        if not re.search(r'assert |self\.assert', content):
            report.add_issue(ValidationIssue(
                severity="warning",
                category="content",
                message=f"测试文件缺少断言: {test_file.relative_to(self.tests_root)}",
                file_path=test_file,
                suggestion="在测试方法中添加断言语句"
            ))
    
    def _validate_test_coverage(self, report: ValidationReport):
        """验证测试覆盖率"""
        logger.info("验证测试覆盖率...")
        
        # 统计源文件和测试文件数量
        src_files = list(self._get_python_modules(self.src_root))
        unit_test_files = list(self._get_test_files(self.tests_root / "unit"))
        
        coverage_ratio = len(unit_test_files) / len(src_files) if src_files else 0
        
        if coverage_ratio < 0.5:
            report.add_issue(ValidationIssue(
                severity="warning",
                category="coverage",
                message=f"单元测试覆盖率较低: {coverage_ratio:.1%} ({len(unit_test_files)}/{len(src_files)})",
                suggestion="增加单元测试文件以提高覆盖率"
            ))
        elif coverage_ratio < 0.8:
            report.add_issue(ValidationIssue(
                severity="info",
                category="coverage",
                message=f"单元测试覆盖率中等: {coverage_ratio:.1%} ({len(unit_test_files)}/{len(src_files)})",
                suggestion="考虑增加更多单元测试"
            ))
    
    def _get_python_modules(self, root_dir: Path) -> List[Path]:
        """获取Python模块文件列表"""
        modules = []
        for root, dirs, files in os.walk(root_dir):
            for file in files:
                if file.endswith('.py') and not file.startswith('__'):
                    modules.append(Path(root) / file)
        return modules
    
    def _get_test_files(self, test_dir: Path) -> List[Path]:
        """获取测试文件列表"""
        if not test_dir.exists():
            return []
        
        test_files = []
        for root, dirs, files in os.walk(test_dir):
            for file in files:
                if file.startswith('test_') and file.endswith('.py'):
                    test_files.append(Path(root) / file)
        return test_files
    
    def _get_all_test_files(self) -> List[Path]:
        """获取所有测试文件"""
        all_test_files = []
        for root, dirs, files in os.walk(self.tests_root):
            for file in files:
                if file.startswith('test_') and file.endswith('.py'):
                    all_test_files.append(Path(root) / file)
        return all_test_files
    
    def generate_report(self, report: ValidationReport) -> str:
        """
        生成可读的验证报告
        
        Args:
            report: 验证报告
            
        Returns:
            格式化的报告字符串
        """
        lines = []
        lines.append("=" * 60)
        lines.append("测试结构验证报告")
        lines.append("=" * 60)
        
        # 摘要信息
        lines.append(f"总测试文件数: {report.total_files}")
        lines.append(f"发现问题数: {len(report.issues)}")
        lines.append("")
        
        # 按严重程度统计
        if report.summary:
            lines.append("问题统计:")
            for severity, count in report.summary.items():
                emoji = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}.get(severity, "")
                lines.append(f"  {emoji} {severity.upper()}: {count}")
            lines.append("")
        
        # 按类别分组显示问题
        categories = set(issue.category for issue in report.issues)
        for category in sorted(categories):
            category_issues = report.get_issues_by_category(category)
            if category_issues:
                lines.append(f"{category.upper()}问题 ({len(category_issues)}):")
                for issue in category_issues[:10]:  # 只显示前10个
                    emoji = {"error": "❌", "warning": "⚠️", "info": "ℹ️"}.get(issue.severity, "")
                    lines.append(f"  {emoji} {issue.message}")
                    if issue.suggestion:
                        lines.append(f"     建议: {issue.suggestion}")
                
                if len(category_issues) > 10:
                    lines.append(f"     ... 还有 {len(category_issues) - 10} 个{category}问题")
                lines.append("")
        
        # 总结
        if report.has_errors():
            lines.append("❌ 验证失败 - 发现严重错误，需要修复")
        elif report.summary.get("warning", 0) > 0:
            lines.append("⚠️ 验证通过但有警告 - 建议修复警告问题")
        else:
            lines.append("✅ 验证通过 - 测试结构符合规范")
        
        lines.append("=" * 60)
        
        return "\n".join(lines)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="测试结构验证工具")
    parser.add_argument("--project-root", type=Path, default=Path.cwd(),
                       help="项目根目录路径")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="详细输出")
    parser.add_argument("--output", "-o", type=Path,
                       help="输出报告到文件")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建验证器
    validator = TestStructureValidator(args.project_root)
    
    # 执行验证
    report = validator.validate_all()
    
    # 生成报告
    report_text = validator.generate_report(report)
    
    # 输出报告
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report_text)
        logger.info(f"验证报告已保存到: {args.output}")
    else:
        logger.info(report_text)
    
    # 设置退出码
    if report.has_errors():
        sys.exit(1)


if __name__ == "__main__":
    import sys
    main()