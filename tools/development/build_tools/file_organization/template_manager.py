#!/usr/bin/env python3
"""
配置模板管理器

本模块提供配置模板的创建、管理和使用功能，支持从模板生成配置文件，
模板变量替换，以及模板版本管理等功能。

作者: 量化交易系统开发团队
版本: 1.0.0
"""

import os
import re
import yaml
import json
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class TemplateVariable:
    """模板变量定义"""
    name: str                           # 变量名
    description: str                    # 变量描述
    default_value: Optional[str] = None # 默认值
    is_required: bool = True            # 是否必需
    variable_type: str = "string"       # 变量类型
    validation_pattern: Optional[str] = None  # 验证正则表达式
    allowed_values: Optional[List[str]] = None  # 允许的值列表


@dataclass
class TemplateInfo:
    """模板信息"""
    name: str                           # 模板名称
    description: str                    # 模板描述
    version: str = "1.0.0"             # 模板版本
    author: str = ""                    # 作者
    category: str = "general"           # 模板分类
    tags: Set[str] = field(default_factory=set)  # 模板标签
    variables: List[TemplateVariable] = field(default_factory=list)  # 模板变量
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    file_path: Optional[str] = None     # 模板文件路径


class TemplateManager:
    """配置模板管理器"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化模板管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.templates_dir = self.config_dir / "templates"
        
        # 确保目录存在
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        # 模板缓存
        self._template_cache: Dict[str, TemplateInfo] = {}
        self._load_templates()
        
        logger.info(f"模板管理器初始化完成，模板目录: {self.templates_dir}")
    
    def _load_templates(self) -> None:
        """加载所有模板信息"""
        self._template_cache.clear()
        
        for template_file in self.templates_dir.glob("*.template"):
            template_name = template_file.stem
            
            try:
                template_info = self._parse_template_file(template_file)
                template_info.name = template_name
                template_info.file_path = str(template_file)
                
                stat = template_file.stat()
                template_info.created_at = datetime.fromtimestamp(stat.st_ctime)
                template_info.updated_at = datetime.fromtimestamp(stat.st_mtime)
                
                self._template_cache[template_name] = template_info
                
            except Exception as e:
                logger.warning(f"加载模板失败 {template_name}: {str(e)}")
    
    def _parse_template_file(self, template_file: Path) -> TemplateInfo:
        """解析模板文件，提取模板信息"""
        template_info = TemplateInfo(
            name="",
            description="",
            author=os.getenv('USER', 'unknown')
        )
        
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析模板头部信息
            header_match = re.search(r'^# (.+?) 配置模板\n# 描述: (.+?)\n', content, re.MULTILINE)
            if header_match:
                template_info.description = header_match.group(2).strip()
            
            # 解析模板变量
            variables = self._extract_template_variables(content)
            template_info.variables = variables
            
            # 尝试解析YAML前置信息（如果存在）
            yaml_front_matter = self._extract_yaml_front_matter(content)
            if yaml_front_matter:
                template_info.version = yaml_front_matter.get('version', template_info.version)
                template_info.author = yaml_front_matter.get('author', template_info.author)
                template_info.category = yaml_front_matter.get('category', template_info.category)
                template_info.tags = set(yaml_front_matter.get('tags', []))
                
                if 'description' in yaml_front_matter:
                    template_info.description = yaml_front_matter['description']
        
        except Exception as e:
            logger.warning(f"解析模板文件失败 {template_file}: {str(e)}")
        
        return template_info
    
    def _extract_template_variables(self, content: str) -> List[TemplateVariable]:
        """从模板内容中提取变量"""
        variables = []
        
        # 查找所有 {variable_name} 格式的变量
        variable_pattern = r'\{([^}]+)\}'
        matches = re.findall(variable_pattern, content)
        
        seen_variables = set()
        for match in matches:
            if match not in seen_variables:
                seen_variables.add(match)
                
                # 尝试从注释中提取变量信息
                var_info = self._extract_variable_info(content, match)
                
                variable = TemplateVariable(
                    name=match,
                    description=var_info.get('description', f"模板变量: {match}"),
                    default_value=var_info.get('default'),
                    is_required=var_info.get('required', True),
                    variable_type=var_info.get('type', 'string')
                )
                
                variables.append(variable)
        
        return variables
    
    def _extract_variable_info(self, content: str, variable_name: str) -> Dict[str, Any]:
        """从模板注释中提取变量信息"""
        info = {}
        
        # 查找变量相关的注释
        pattern = rf'#.*{re.escape(variable_name)}.*?:.*?(.+?)(?:\n|$)'
        matches = re.findall(pattern, content, re.IGNORECASE)
        
        for match in matches:
            comment = match.strip()
            
            # 解析注释中的信息
            if '默认:' in comment or 'default:' in comment.lower():
                default_match = re.search(r'(?:默认|default):\s*(.+?)(?:\s|$)', comment, re.IGNORECASE)
                if default_match:
                    info['default'] = default_match.group(1).strip()
            
            if '必需' in comment or 'required' in comment.lower():
                info['required'] = True
            elif '可选' in comment or 'optional' in comment.lower():
                info['required'] = False
        
        return info
    
    def _extract_yaml_front_matter(self, content: str) -> Optional[Dict[str, Any]]:
        """提取YAML前置信息"""
        front_matter_pattern = r'^---\n(.*?)\n---\n'
        match = re.search(front_matter_pattern, content, re.DOTALL)
        
        if match:
            try:
                return yaml.safe_load(match.group(1))
            except yaml.YAMLError:
                pass
        
        return None
    
    def list_templates(self, category: Optional[str] = None, 
                      tags: Optional[List[str]] = None) -> List[TemplateInfo]:
        """
        列出模板
        
        Args:
            category: 按分类过滤
            tags: 按标签过滤
        
        Returns:
            模板信息列表
        """
        templates = list(self._template_cache.values())
        
        if category:
            templates = [t for t in templates if t.category == category]
        
        if tags:
            tag_set = set(tags)
            templates = [t for t in templates if tag_set.intersection(t.tags)]
        
        return sorted(templates, key=lambda t: t.name)
    
    def get_template_info(self, template_name: str) -> Optional[TemplateInfo]:
        """获取模板信息"""
        return self._template_cache.get(template_name)
    
    def template_exists(self, template_name: str) -> bool:
        """检查模板是否存在"""
        return template_name in self._template_cache
    
    def create_template(self, template_name: str, template_content: str, 
                       description: str = "", category: str = "general",
                       tags: List[str] = None, variables: List[TemplateVariable] = None) -> None:
        """
        创建配置模板
        
        Args:
            template_name: 模板名称
            template_content: 模板内容
            description: 模板描述
            category: 模板分类
            tags: 模板标签
            variables: 模板变量定义
        """
        if self.template_exists(template_name):
            raise ValueError(f"模板已存在: {template_name}")
        
        template_file = self.templates_dir / f"{template_name}.template"
        
        # 构建完整的模板内容
        full_content = self._build_template_content(
            template_name, template_content, description, 
            category, tags or [], variables or []
        )
        
        try:
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(full_content)
            
            # 重新加载模板缓存
            self._load_templates()
            
            logger.info(f"成功创建模板: {template_name}")
            
        except Exception as e:
            logger.error(f"创建模板失败 {template_name}: {str(e)}")
            raise
    
    def _build_template_content(self, name: str, content: str, description: str,
                               category: str, tags: List[str], 
                               variables: List[TemplateVariable]) -> str:
        """构建完整的模板内容"""
        # YAML前置信息
        front_matter = {
            'version': '1.0.0',
            'author': os.getenv('USER', 'system'),
            'category': category,
            'description': description,
            'tags': tags,
            'created_at': datetime.now().isoformat()
        }
        
        if variables:
            front_matter['variables'] = [
                {
                    'name': var.name,
                    'description': var.description,
                    'type': var.variable_type,
                    'required': var.is_required,
                    'default': var.default_value
                }
                for var in variables
            ]
        
        # 构建完整内容
        full_content = f"""---
{yaml.dump(front_matter, default_flow_style=False, allow_unicode=True)}---

# {name} 配置模板
# 描述: {description}
# 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# 使用说明: 复制此模板并根据需要修改配置值

{content}
"""
        
        return full_content
    
    def update_template(self, template_name: str, new_content: str = None,
                       description: str = None, category: str = None,
                       tags: List[str] = None) -> None:
        """
        更新模板
        
        Args:
            template_name: 模板名称
            new_content: 新的模板内容
            description: 新的描述
            category: 新的分类
            tags: 新的标签
        """
        if not self.template_exists(template_name):
            raise ValueError(f"模板不存在: {template_name}")
        
        template_file = self.templates_dir / f"{template_name}.template"
        
        try:
            # 备份原模板
            backup_file = template_file.with_suffix(f'.template.backup.{datetime.now().strftime("%Y%m%d_%H%M%S")}')
            shutil.copy2(template_file, backup_file)
            
            # 读取现有内容
            with open(template_file, 'r', encoding='utf-8') as f:
                current_content = f.read()
            
            # 解析现有前置信息
            front_matter = self._extract_yaml_front_matter(current_content) or {}
            
            # 更新前置信息
            if description is not None:
                front_matter['description'] = description
            if category is not None:
                front_matter['category'] = category
            if tags is not None:
                front_matter['tags'] = tags
            
            front_matter['updated_at'] = datetime.now().isoformat()
            front_matter['updated_by'] = os.getenv('USER', 'system')
            
            # 如果提供了新内容，使用新内容；否则保留原内容
            if new_content is not None:
                # 移除原有的前置信息，只保留模板内容
                content_without_front_matter = re.sub(r'^---\n.*?\n---\n', '', current_content, flags=re.DOTALL)
                # 移除原有的头部注释
                content_without_front_matter = re.sub(r'^#.*?\n# 使用说明:.*?\n\n', '', content_without_front_matter, flags=re.DOTALL)
                template_content = new_content
            else:
                # 保留原有内容
                content_without_front_matter = re.sub(r'^---\n.*?\n---\n', '', current_content, flags=re.DOTALL)
                content_without_front_matter = re.sub(r'^#.*?\n# 使用说明:.*?\n\n', '', content_without_front_matter, flags=re.DOTALL)
                template_content = content_without_front_matter
            
            # 重新构建模板
            full_content = self._build_template_content(
                template_name, template_content, 
                front_matter.get('description', ''),
                front_matter.get('category', 'general'),
                front_matter.get('tags', []),
                []  # 变量会从内容中自动提取
            )
            
            # 保存更新后的模板
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(full_content)
            
            # 重新加载模板缓存
            self._load_templates()
            
            logger.info(f"成功更新模板: {template_name}，备份文件: {backup_file}")
            
        except Exception as e:
            logger.error(f"更新模板失败 {template_name}: {str(e)}")
            raise
    
    def delete_template(self, template_name: str, create_backup: bool = True) -> None:
        """
        删除模板
        
        Args:
            template_name: 模板名称
            create_backup: 是否创建备份
        """
        if not self.template_exists(template_name):
            raise ValueError(f"模板不存在: {template_name}")
        
        template_file = self.templates_dir / f"{template_name}.template"
        
        try:
            if create_backup:
                backup_file = template_file.with_suffix(f'.template.deleted.{datetime.now().strftime("%Y%m%d_%H%M%S")}')
                shutil.copy2(template_file, backup_file)
                logger.info(f"模板备份文件: {backup_file}")
            
            template_file.unlink()
            
            # 更新缓存
            if template_name in self._template_cache:
                del self._template_cache[template_name]
            
            logger.info(f"成功删除模板: {template_name}")
            
        except Exception as e:
            logger.error(f"删除模板失败 {template_name}: {str(e)}")
            raise
    
    def generate_config_from_template(self, template_name: str, output_path: str,
                                    variables: Optional[Dict[str, str]] = None,
                                    format: str = 'auto') -> None:
        """
        从模板生成配置文件
        
        Args:
            template_name: 模板名称
            output_path: 输出文件路径
            variables: 模板变量值
            format: 输出格式（auto, yaml, json, env）
        """
        if not self.template_exists(template_name):
            raise ValueError(f"模板不存在: {template_name}")
        
        template_info = self.get_template_info(template_name)
        template_file = Path(template_info.file_path)
        
        try:
            # 读取模板内容
            with open(template_file, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # 移除前置信息，只保留模板内容
            content = re.sub(r'^---\n.*?\n---\n', '', template_content, flags=re.DOTALL)
            
            # 验证必需变量
            missing_vars = self._validate_required_variables(template_info, variables or {})
            if missing_vars:
                raise ValueError(f"缺少必需的模板变量: {', '.join(missing_vars)}")
            
            # 替换变量
            if variables:
                content = self._replace_template_variables(content, variables)
            
            # 确定输出格式
            if format == 'auto':
                output_file = Path(output_path)
                if output_file.suffix.lower() in ['.yaml', '.yml']:
                    format = 'yaml'
                elif output_file.suffix.lower() == '.json':
                    format = 'json'
                elif output_file.suffix.lower() == '.env':
                    format = 'env'
                else:
                    format = 'yaml'  # 默认格式
            
            # 格式转换（如果需要）
            if format in ['yaml', 'json'] and template_info.category == 'env':
                content = self._convert_env_to_structured(content, format)
            
            # 确保输出目录存在
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"成功从模板 {template_name} 生成配置文件: {output_path}")
            
        except Exception as e:
            logger.error(f"从模板生成配置文件失败: {str(e)}")
            raise
    
    def _validate_required_variables(self, template_info: TemplateInfo, 
                                   variables: Dict[str, str]) -> List[str]:
        """验证必需变量"""
        missing_vars = []
        
        for var in template_info.variables:
            if var.is_required and var.name not in variables:
                # 检查是否有默认值
                if var.default_value is None:
                    missing_vars.append(var.name)
        
        return missing_vars
    
    def _replace_template_variables(self, content: str, variables: Dict[str, str]) -> str:
        """替换模板变量"""
        result = content
        
        for var_name, var_value in variables.items():
            # 替换 {variable_name} 格式的变量
            result = result.replace(f"{{{var_name}}}", var_value)
        
        return result
    
    def _convert_env_to_structured(self, env_content: str, format: str) -> str:
        """将环境变量格式转换为结构化格式"""
        config_dict = {}
        
        for line in env_content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"\'')
                
                # 将点分隔的键转换为嵌套字典
                keys = key.lower().split('_')
                current = config_dict
                
                for k in keys[:-1]:
                    if k not in current:
                        current[k] = {}
                    current = current[k]
                
                current[keys[-1]] = value
        
        if format == 'json':
            return json.dumps(config_dict, indent=2, ensure_ascii=False)
        else:
            return yaml.dump(config_dict, default_flow_style=False, allow_unicode=True)
    
    def preview_template(self, template_name: str, 
                        variables: Optional[Dict[str, str]] = None) -> str:
        """
        预览模板生成结果
        
        Args:
            template_name: 模板名称
            variables: 模板变量值
        
        Returns:
            预览内容
        """
        if not self.template_exists(template_name):
            raise ValueError(f"模板不存在: {template_name}")
        
        template_info = self.get_template_info(template_name)
        template_file = Path(template_info.file_path)
        
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # 移除前置信息
            content = re.sub(r'^---\n.*?\n---\n', '', template_content, flags=re.DOTALL)
            
            # 替换变量
            if variables:
                content = self._replace_template_variables(content, variables)
            
            return content
            
        except Exception as e:
            logger.error(f"预览模板失败: {str(e)}")
            raise
    
    def validate_template_variables(self, template_name: str, 
                                  variables: Dict[str, str]) -> List[str]:
        """
        验证模板变量
        
        Args:
            template_name: 模板名称
            variables: 变量值
        
        Returns:
            错误消息列表
        """
        if not self.template_exists(template_name):
            return [f"模板不存在: {template_name}"]
        
        template_info = self.get_template_info(template_name)
        errors = []
        
        # 检查必需变量
        for var in template_info.variables:
            if var.is_required and var.name not in variables:
                if var.default_value is None:
                    errors.append(f"缺少必需变量: {var.name}")
            
            # 验证变量值
            if var.name in variables:
                value = variables[var.name]
                
                # 类型验证
                if var.variable_type == 'int':
                    try:
                        int(value)
                    except ValueError:
                        errors.append(f"变量 {var.name} 必须是整数")
                
                elif var.variable_type == 'float':
                    try:
                        float(value)
                    except ValueError:
                        errors.append(f"变量 {var.name} 必须是浮点数")
                
                elif var.variable_type == 'bool':
                    if value.lower() not in ['true', 'false', '1', '0', 'yes', 'no']:
                        errors.append(f"变量 {var.name} 必须是布尔值")
                
                # 正则验证
                if var.validation_pattern:
                    if not re.match(var.validation_pattern, value):
                        errors.append(f"变量 {var.name} 格式不正确")
                
                # 枚举值验证
                if var.allowed_values:
                    if value not in var.allowed_values:
                        errors.append(f"变量 {var.name} 必须是以下值之一: {', '.join(var.allowed_values)}")
        
        return errors
    
    def get_template_categories(self) -> List[str]:
        """获取所有模板分类"""
        categories = set()
        for template_info in self._template_cache.values():
            categories.add(template_info.category)
        return sorted(list(categories))
    
    def get_template_tags(self) -> List[str]:
        """获取所有模板标签"""
        tags = set()
        for template_info in self._template_cache.values():
            tags.update(template_info.tags)
        return sorted(list(tags))
    
    def export_template(self, template_name: str, output_file: Optional[str] = None) -> str:
        """导出模板"""
        if not self.template_exists(template_name):
            raise ValueError(f"模板不存在: {template_name}")
        
        template_info = self.get_template_info(template_name)
        source_file = Path(template_info.file_path)
        
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"{template_name}_template_{timestamp}.template"
        
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            shutil.copy2(source_file, output_path)
            logger.info(f"模板已导出: {template_name} -> {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"导出模板失败: {str(e)}")
            raise
    
    def import_template(self, template_file: str, template_name: Optional[str] = None,
                       overwrite: bool = False) -> None:
        """导入模板"""
        source_path = Path(template_file)
        if not source_path.exists():
            raise FileNotFoundError(f"模板文件不存在: {template_file}")
        
        if template_name is None:
            template_name = source_path.stem
        
        if self.template_exists(template_name) and not overwrite:
            raise ValueError(f"模板已存在: {template_name}，使用overwrite=True来覆盖")
        
        target_file = self.templates_dir / f"{template_name}.template"
        
        try:
            shutil.copy2(source_path, target_file)
            
            # 重新加载模板缓存
            self._load_templates()
            
            logger.info(f"成功导入模板: {template_file} -> {template_name}")
            
        except Exception as e:
            logger.error(f"导入模板失败: {str(e)}")
            raise


def main():
    """主函数 - 演示模板管理器的使用"""
    logger.info("📄 配置模板管理器演示")
    logger.info("=" * 50)
    
    # 创建模板管理器
    template_manager = TemplateManager()
    
    # 列出所有模板
    templates = template_manager.list_templates()
    logger.info(f"📋 可用模板 ({len(templates)} 个):")
    
    for template_info in templates:
        logger.info(f"  - {template_info.name}: {template_info.description}")
        logger.info(f"    分类: {template_info.category}, 版本: {template_info.version}")
        if template_info.tags:
            logger.info(f"    标签: {', '.join(template_info.tags)}")
        if template_info.variables:
            logger.info(f"    变量: {', '.join([v.name for v in template_info.variables])}")
        logger.info()
    
    # 显示分类和标签统计
    categories = template_manager.get_template_categories()
    tags = template_manager.get_template_tags()
    
    logger.info(f"📂 模板分类: {', '.join(categories)}")
    logger.info(f"🏷️ 模板标签: {', '.join(tags)}")
    
    # 演示模板预览（如果有模板的话）
    if templates:
        first_template = templates[0]
        logger.info(f"\n👀 预览模板: {first_template.name}")
        
        try:
            # 准备示例变量
            sample_variables = {}
            for var in first_template.variables:
                if var.default_value:
                    sample_variables[var.name] = var.default_value
                elif var.variable_type == 'string':
                    sample_variables[var.name] = f"sample_{var.name}"
                elif var.variable_type == 'int':
                    sample_variables[var.name] = "8080"
                elif var.variable_type == 'bool':
                    sample_variables[var.name] = "true"
            
            if sample_variables:
                logger.info(f"使用变量: {sample_variables}")
                preview = template_manager.preview_template(first_template.name, sample_variables)
                logger.info("预览内容:")
                logger.info("-" * 40)
                logger.info(preview[:500] + "..." if len(preview) > 500 else preview)
                logger.info("-" * 40)
            
        except Exception as e:
            logger.info(f"❌ 预览失败: {str(e)}")


if __name__ == "__main__":
    main()