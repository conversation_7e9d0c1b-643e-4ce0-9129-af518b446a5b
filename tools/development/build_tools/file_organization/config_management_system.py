#!/usr/bin/env python3
"""
配置文件管理系统

本模块实现了完整的配置文件管理功能，包括：
- 配置文件统一加载器
- 环境特定配置管理
- 配置模板系统
- 配置验证机制

作者: 量化交易系统开发团队
版本: 1.0.0
"""

import os
import yaml
import json
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from datetime import datetime
import logging
from abc import ABC, abstractmethod

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ConfigurationMetadata:
    """配置文件元数据"""
    name: str                           # 配置名称
    file_path: str                      # 文件路径
    config_type: str                    # 配置类型
    environment: Optional[str] = None   # 环境名称
    version: str = "1.0.0"             # 配置版本
    description: str = ""               # 配置描述
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    dependencies: List[str] = field(default_factory=list)  # 依赖的其他配置


@dataclass
class ValidationRule:
    """配置验证规则"""
    field_path: str                     # 字段路径，如 "database.host"
    rule_type: str                      # 规则类型：required, type, range, regex, custom
    rule_value: Any                     # 规则值
    error_message: str                  # 错误消息
    is_critical: bool = True            # 是否为关键验证


class ConfigurationValidator(ABC):
    """配置验证器抽象基类"""
    
    @abstractmethod
    def validate(self, config_data: Dict[str, Any]) -> List[str]:
        """验证配置数据，返回错误消息列表"""
        pass


class DefaultConfigurationValidator(ConfigurationValidator):
    """默认配置验证器"""
    
    def __init__(self, validation_rules: List[ValidationRule]):
        self.validation_rules = validation_rules
    
    def validate(self, config_data: Dict[str, Any]) -> List[str]:
        """验证配置数据"""
        errors = []
        
        for rule in self.validation_rules:
            try:
                error = self._validate_rule(config_data, rule)
                if error:
                    errors.append(error)
            except Exception as e:
                errors.append(f"验证规则 {rule.field_path} 时发生错误: {str(e)}")
        
        return errors
    
    def _validate_rule(self, config_data: Dict[str, Any], rule: ValidationRule) -> Optional[str]:
        """验证单个规则"""
        field_value = self._get_nested_value(config_data, rule.field_path)
        
        if rule.rule_type == "required":
            if field_value is None:
                return f"{rule.field_path}: {rule.error_message}"
        
        elif rule.rule_type == "type":
            if field_value is not None and not isinstance(field_value, rule.rule_value):
                return f"{rule.field_path}: {rule.error_message}"
        
        elif rule.rule_type == "range":
            if field_value is not None:
                min_val, max_val = rule.rule_value
                if not (min_val <= field_value <= max_val):
                    return f"{rule.field_path}: {rule.error_message}"
        
        elif rule.rule_type == "regex":
            if field_value is not None:
                import re
                if not re.match(rule.rule_value, str(field_value)):
                    return f"{rule.field_path}: {rule.error_message}"
        
        return None
    
    def _get_nested_value(self, data: Dict[str, Any], path: str) -> Any:
        """获取嵌套字典中的值"""
        keys = path.split('.')
        current = data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current


class ConfigurationLoader:
    """配置文件加载器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        self.loaded_configs: Dict[str, Dict[str, Any]] = {}
        self.config_metadata: Dict[str, ConfigurationMetadata] = {}
    
    def load_configuration(self, config_name: str, environment: Optional[str] = None) -> Dict[str, Any]:
        """
        加载指定配置文件
        
        Args:
            config_name: 配置文件名（不含扩展名）
            environment: 环境名称，如果指定则会加载环境特定配置
        
        Returns:
            配置数据字典
        """
        try:
            # 加载基础配置
            base_config = self._load_base_config(config_name)
            
            # 如果指定了环境，加载环境特定配置
            if environment:
                env_config = self._load_environment_config(config_name, environment)
                base_config = self._merge_configs(base_config, env_config)
            
            # 展开环境变量
            base_config = self._expand_environment_variables(base_config)
            
            # 缓存配置
            cache_key = f"{config_name}_{environment or 'default'}"
            self.loaded_configs[cache_key] = base_config
            
            logger.info(f"成功加载配置: {config_name} (环境: {environment or '默认'})")
            return base_config
            
        except Exception as e:
            logger.error(f"加载配置失败 {config_name}: {str(e)}")
            raise
    
    def _load_base_config(self, config_name: str) -> Dict[str, Any]:
        """加载基础配置文件"""
        config_file = self.config_dir / f"{config_name}.yaml"
        
        if not config_file.exists():
            # 尝试JSON格式
            config_file = self.config_dir / f"{config_name}.json"
            if not config_file.exists():
                raise FileNotFoundError(f"配置文件不存在: {config_name}")
        
        return self._load_config_file(config_file)
    
    def _load_environment_config(self, config_name: str, environment: str) -> Dict[str, Any]:
        """加载环境特定配置"""
        env_config_file = self.config_dir / "environments" / f"{environment}.yaml"
        
        if not env_config_file.exists():
            # 尝试特定配置文件
            env_config_file = self.config_dir / "environments" / f"{config_name}_{environment}.yaml"
            if not env_config_file.exists():
                logger.warning(f"环境配置文件不存在: {environment}")
                return {}
        
        return self._load_config_file(env_config_file)
    
    def _load_config_file(self, file_path: Path) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix.lower() == '.json':
                    return json.load(f)
                else:
                    return yaml.safe_load(f) or {}
        except Exception as e:
            logger.error(f"读取配置文件失败 {file_path}: {str(e)}")
            raise
    
    def _merge_configs(self, base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置，override_config会覆盖base_config中的相同键"""
        result = base_config.copy()
        
        for key, value in override_config.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _expand_environment_variables(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """展开配置中的环境变量"""
        import re
        
        def expand_value(value):
            if isinstance(value, str):
                # 支持 ${VAR_NAME:default_value} 和 ${VAR_NAME} 格式
                pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'
                
                def replace_env_var(match):
                    var_name = match.group(1)
                    default_value = match.group(2) if match.group(2) is not None else ""
                    return os.getenv(var_name, default_value)
                
                return re.sub(pattern, replace_env_var, value)
            elif isinstance(value, dict):
                return {k: expand_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [expand_value(item) for item in value]
            
            return value
        
        return expand_value(config)
    
    def reload_configuration(self, config_name: str, environment: Optional[str] = None) -> Dict[str, Any]:
        """重新加载配置"""
        cache_key = f"{config_name}_{environment or 'default'}"
        if cache_key in self.loaded_configs:
            del self.loaded_configs[cache_key]
        
        return self.load_configuration(config_name, environment)
    
    def get_loaded_configurations(self) -> Dict[str, Dict[str, Any]]:
        """获取所有已加载的配置"""
        return self.loaded_configs.copy()


class EnvironmentConfigurationManager:
    """环境特定配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.environments_dir = self.config_dir / "environments"
        self.environments_dir.mkdir(exist_ok=True)
        self.current_environment = os.getenv('TRADING_ENV', 'development')
    
    def create_environment_config(self, environment: str, base_config: Dict[str, Any]) -> None:
        """创建环境特定配置文件"""
        env_file = self.environments_dir / f"{environment}.yaml"
        
        try:
            with open(env_file, 'w', encoding='utf-8') as f:
                yaml.dump(base_config, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"成功创建环境配置: {environment}")
            
        except Exception as e:
            logger.error(f"创建环境配置失败 {environment}: {str(e)}")
            raise
    
    def update_environment_config(self, environment: str, updates: Dict[str, Any]) -> None:
        """更新环境特定配置"""
        env_file = self.environments_dir / f"{environment}.yaml"
        
        if not env_file.exists():
            raise FileNotFoundError(f"环境配置文件不存在: {environment}")
        
        try:
            # 加载现有配置
            with open(env_file, 'r', encoding='utf-8') as f:
                current_config = yaml.safe_load(f) or {}
            
            # 合并更新
            updated_config = self._deep_merge(current_config, updates)
            
            # 保存更新后的配置
            with open(env_file, 'w', encoding='utf-8') as f:
                yaml.dump(updated_config, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"成功更新环境配置: {environment}")
            
        except Exception as e:
            logger.error(f"更新环境配置失败 {environment}: {str(e)}")
            raise
    
    def _deep_merge(self, base: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并字典"""
        result = base.copy()
        
        for key, value in updates.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def list_environments(self) -> List[str]:
        """列出所有可用环境"""
        environments = []
        
        for env_file in self.environments_dir.glob("*.yaml"):
            environments.append(env_file.stem)
        
        return sorted(environments)
    
    def get_current_environment(self) -> str:
        """获取当前环境"""
        return self.current_environment
    
    def set_current_environment(self, environment: str) -> None:
        """设置当前环境"""
        if environment not in self.list_environments():
            raise ValueError(f"环境不存在: {environment}")
        
        self.current_environment = environment
        os.environ['TRADING_ENV'] = environment
        logger.info(f"切换到环境: {environment}")
    
    def validate_environment_config(self, environment: str, validator: ConfigurationValidator) -> List[str]:
        """验证环境配置"""
        env_file = self.environments_dir / f"{environment}.yaml"
        
        if not env_file.exists():
            return [f"环境配置文件不存在: {environment}"]
        
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f) or {}
            
            return validator.validate(config_data)
            
        except Exception as e:
            return [f"验证环境配置时发生错误: {str(e)}"]


class ConfigurationTemplateManager:
    """配置模板管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.templates_dir = self.config_dir / "templates"
        self.templates_dir.mkdir(exist_ok=True)
    
    def create_template(self, template_name: str, template_content: str, description: str = "") -> None:
        """创建配置模板"""
        template_file = self.templates_dir / f"{template_name}.template"
        
        try:
            # 添加模板头部信息
            header = f"""# {template_name} 配置模板
# 描述: {description}
# 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# 使用说明: 复制此模板并根据需要修改配置值

"""
            
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(header + template_content)
            
            logger.info(f"成功创建配置模板: {template_name}")
            
        except Exception as e:
            logger.error(f"创建配置模板失败 {template_name}: {str(e)}")
            raise
    
    def generate_config_from_template(self, template_name: str, output_path: str, 
                                    variables: Optional[Dict[str, str]] = None) -> None:
        """从模板生成配置文件"""
        template_file = self.templates_dir / f"{template_name}.template"
        
        if not template_file.exists():
            raise FileNotFoundError(f"配置模板不存在: {template_name}")
        
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # 替换模板变量
            if variables:
                for var_name, var_value in variables.items():
                    template_content = template_content.replace(f"{{{var_name}}}", var_value)
            
            # 确保输出目录存在
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(template_content)
            
            logger.info(f"成功从模板 {template_name} 生成配置文件: {output_path}")
            
        except Exception as e:
            logger.error(f"从模板生成配置文件失败: {str(e)}")
            raise
    
    def list_templates(self) -> List[str]:
        """列出所有可用模板"""
        templates = []
        
        for template_file in self.templates_dir.glob("*.template"):
            templates.append(template_file.stem)
        
        return sorted(templates)
    
    def update_template(self, template_name: str, new_content: str) -> None:
        """更新配置模板"""
        template_file = self.templates_dir / f"{template_name}.template"
        
        if not template_file.exists():
            raise FileNotFoundError(f"配置模板不存在: {template_name}")
        
        try:
            # 备份原模板
            backup_file = template_file.with_suffix('.template.backup')
            shutil.copy2(template_file, backup_file)
            
            # 更新模板
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            logger.info(f"成功更新配置模板: {template_name}")
            
        except Exception as e:
            logger.error(f"更新配置模板失败 {template_name}: {str(e)}")
            raise
    
    def delete_template(self, template_name: str) -> None:
        """删除配置模板"""
        template_file = self.templates_dir / f"{template_name}.template"
        
        if not template_file.exists():
            raise FileNotFoundError(f"配置模板不存在: {template_name}")
        
        try:
            template_file.unlink()
            logger.info(f"成功删除配置模板: {template_name}")
            
        except Exception as e:
            logger.error(f"删除配置模板失败 {template_name}: {str(e)}")
            raise


class ConfigurationValidationManager:
    """配置验证管理器"""
    
    def __init__(self):
        self.validators: Dict[str, ConfigurationValidator] = {}
        self.validation_rules: Dict[str, List[ValidationRule]] = {}
        self._setup_default_validation_rules()
    
    def _setup_default_validation_rules(self) -> None:
        """设置默认验证规则"""
        # 数据库配置验证规则
        database_rules = [
            ValidationRule("database.host", "required", None, "数据库主机地址不能为空"),
            ValidationRule("database.port", "type", int, "数据库端口必须为整数"),
            ValidationRule("database.port", "range", (1, 65535), "数据库端口必须在1-65535范围内"),
            ValidationRule("database.database", "required", None, "数据库名称不能为空"),
            ValidationRule("database.username", "required", None, "数据库用户名不能为空"),
            ValidationRule("database.pool_size", "type", int, "连接池大小必须为整数"),
            ValidationRule("database.pool_size", "range", (1, 100), "连接池大小必须在1-100范围内"),
        ]
        
        # API配置验证规则
        api_rules = [
            ValidationRule("api.host", "required", None, "API主机地址不能为空"),
            ValidationRule("api.port", "type", int, "API端口必须为整数"),
            ValidationRule("api.port", "range", (1, 65535), "API端口必须在1-65535范围内"),
            ValidationRule("api.jwt_secret", "required", None, "JWT密钥不能为空"),
        ]
        
        # 风险管理配置验证规则
        risk_rules = [
            ValidationRule("risk.max_position_size", "type", (int, float), "最大仓位大小必须为数字"),
            ValidationRule("risk.max_position_size", "range", (0, 1), "最大仓位大小必须在0-1范围内"),
            ValidationRule("risk.max_portfolio_risk", "type", (int, float), "最大投资组合风险必须为数字"),
            ValidationRule("risk.max_portfolio_risk", "range", (0, 1), "最大投资组合风险必须在0-1范围内"),
            ValidationRule("risk.stop_loss_pct", "type", (int, float), "止损百分比必须为数字"),
            ValidationRule("risk.stop_loss_pct", "range", (0, 1), "止损百分比必须在0-1范围内"),
        ]
        
        self.validation_rules["database"] = database_rules
        self.validation_rules["api"] = api_rules
        self.validation_rules["risk"] = risk_rules
        
        # 创建默认验证器
        self.validators["default"] = DefaultConfigurationValidator(
            database_rules + api_rules + risk_rules
        )
    
    def add_validation_rule(self, config_type: str, rule: ValidationRule) -> None:
        """添加验证规则"""
        if config_type not in self.validation_rules:
            self.validation_rules[config_type] = []
        
        self.validation_rules[config_type].append(rule)
        logger.info(f"添加验证规则: {config_type}.{rule.field_path}")
    
    def register_validator(self, name: str, validator: ConfigurationValidator) -> None:
        """注册自定义验证器"""
        self.validators[name] = validator
        logger.info(f"注册配置验证器: {name}")
    
    def validate_configuration(self, config_data: Dict[str, Any], 
                             validator_name: str = "default") -> List[str]:
        """验证配置数据"""
        if validator_name not in self.validators:
            raise ValueError(f"验证器不存在: {validator_name}")
        
        validator = self.validators[validator_name]
        return validator.validate(config_data)
    
    def validate_configuration_file(self, config_file: str, 
                                  validator_name: str = "default") -> List[str]:
        """验证配置文件"""
        config_path = Path(config_file)
        
        if not config_path.exists():
            return [f"配置文件不存在: {config_file}"]
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.json':
                    config_data = json.load(f)
                else:
                    config_data = yaml.safe_load(f) or {}
            
            return self.validate_configuration(config_data, validator_name)
            
        except Exception as e:
            return [f"读取配置文件时发生错误: {str(e)}"]


class ConfigurationManager:
    """配置管理系统主类"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 初始化各个管理器
        self.loader = ConfigurationLoader(config_dir)
        self.env_manager = EnvironmentConfigurationManager(config_dir)
        self.template_manager = ConfigurationTemplateManager(config_dir)
        self.validation_manager = ConfigurationValidationManager()
        
        logger.info(f"配置管理系统初始化完成，配置目录: {self.config_dir}")
    
    def load_config(self, config_name: str, environment: Optional[str] = None, 
                   validate: bool = True) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_name: 配置文件名
            environment: 环境名称
            validate: 是否验证配置
        
        Returns:
            配置数据字典
        """
        # 加载配置
        config_data = self.loader.load_configuration(config_name, environment)
        
        # 验证配置
        if validate:
            errors = self.validation_manager.validate_configuration(config_data)
            if errors:
                error_msg = "配置验证失败:\n" + "\n".join(f"- {error}" for error in errors)
                logger.error(error_msg)
                raise ValueError(error_msg)
        
        return config_data
    
    def create_environment(self, environment: str, base_config: Dict[str, Any]) -> None:
        """创建新环境配置"""
        self.env_manager.create_environment_config(environment, base_config)
    
    def update_environment(self, environment: str, updates: Dict[str, Any]) -> None:
        """更新环境配置"""
        self.env_manager.update_environment_config(environment, updates)
    
    def create_template(self, template_name: str, template_content: str, 
                       description: str = "") -> None:
        """创建配置模板"""
        self.template_manager.create_template(template_name, template_content, description)
    
    def generate_from_template(self, template_name: str, output_path: str, 
                             variables: Optional[Dict[str, str]] = None) -> None:
        """从模板生成配置文件"""
        self.template_manager.generate_config_from_template(template_name, output_path, variables)
    
    def validate_config_file(self, config_file: str) -> List[str]:
        """验证配置文件"""
        return self.validation_manager.validate_configuration_file(config_file)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取配置管理系统状态"""
        return {
            "config_directory": str(self.config_dir),
            "current_environment": self.env_manager.get_current_environment(),
            "available_environments": self.env_manager.list_environments(),
            "available_templates": self.template_manager.list_templates(),
            "loaded_configurations": list(self.loader.get_loaded_configurations().keys()),
            "validation_rules_count": sum(len(rules) for rules in self.validation_manager.validation_rules.values())
        }


def main():
    """主函数 - 演示配置管理系统的使用"""
    logger.info("🔧 配置文件管理系统演示")
    logger.info("=" * 50)
    
    # 初始化配置管理器
    config_manager = ConfigurationManager()
    
    # 显示系统状态
    status = config_manager.get_system_status()
    logger.info(f"📁 配置目录: {status['config_directory']}")
    logger.info(f"🌍 当前环境: {status['current_environment']}")
    logger.info(f"📋 可用环境: {', '.join(status['available_environments'])}")
    logger.info(f"📄 可用模板: {', '.join(status['available_templates'])}")
    logger.info(f"💾 已加载配置: {', '.join(status['loaded_configurations'])}")
    logger.info(f"✅ 验证规则数量: {status['validation_rules_count']}")
    
    try:
        # 加载主配置
        logger.info("\n📖 加载主配置文件...")
        main_config = config_manager.load_config("config", "development")
        logger.info(f"✅ 成功加载配置，包含 {len(main_config)} 个配置项")
        
        # 验证配置文件
        logger.info("\n🔍 验证配置文件...")
        errors = config_manager.validate_config_file("config/config.yaml")
        if errors:
            logger.info("❌ 配置验证发现问题:")
            for error in errors:
                logger.info(f"  - {error}")
        else:
            logger.info("✅ 配置验证通过")
        
    except Exception as e:
        logger.info(f"❌ 操作失败: {str(e)}")


if __name__ == "__main__":
    main()