import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
文件组织监控系统命令行工具

提供监控系统的命令行接口，支持启动监控、检查规范、生成报告等功能。
"""

import argparse
import json
import sys
import time
import signal
from pathlib import Path
from datetime import datetime, timedelta
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tools.file_organization.monitoring_system import (
    FileOrganizationMonitoringSystem,
    DirectoryStructureMonitor,
    FileComplianceChecker,
    StorageMonitor,
    ConfigChangeTracker
)


class MonitoringCLI:
    """监控系统命令行接口"""
    
    def __init__(self):
        self.monitoring_system: Optional[FileOrganizationMonitoringSystem] = None
        self.running = False
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"\n收到信号 {signum}，正在停止监控系统...")
        self.running = False
        if self.monitoring_system:
            self.monitoring_system.stop_monitoring()
        sys.exit(0)
    
    def start_monitoring(self, args):
        """启动监控系统"""
        logger.info("🚀 启动文件组织监控系统...")
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.monitoring_system = FileOrganizationMonitoringSystem(args.project_root)
        self.monitoring_system.start_monitoring()
        
        self.running = True
        
        if args.daemon:
            logger.info("📡 监控系统以守护进程模式运行...")
            try:
                while self.running:
                    time.sleep(60)  # 每分钟检查一次
                    
                    # 定期执行检查
                    if datetime.now().minute % 10 == 0:  # 每10分钟执行一次
                        self.monitoring_system.run_compliance_check()
                        self.monitoring_system.collect_storage_metrics()
                        self.monitoring_system.check_config_changes()
            except KeyboardInterrupt:
                pass
        else:
            logger.info("⏱️  监控系统运行中，按 Ctrl+C 停止...")
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
        
        logger.info("🛑 监控系统已停止")
    
    def check_compliance(self, args):
        """检查文件放置规范"""
        logger.info("🔍 开始文件放置规范检查...")
        
        checker = FileComplianceChecker(args.project_root)
        violations = checker.check_compliance()
        
        if not violations:
            logger.info("✅ 文件放置规范检查通过，未发现违规")
            return
        
        logger.info(f"⚠️  发现 {len(violations)} 个规范违规:")
        
        # 按严重程度分组显示
        high_violations = [v for v in violations if v.severity == "high"]
        medium_violations = [v for v in violations if v.severity == "medium"]
        low_violations = [v for v in violations if v.severity == "low"]
        
        if high_violations:
            logger.info(f"\n🔴 高严重程度违规 ({len(high_violations)} 个):")
            for violation in high_violations:
                logger.info(f"  - {violation.file_path}")
                logger.info(f"    违规类型: {violation.violation_type}")
                logger.info(f"    建议: {violation.expected_location}")
        
        if medium_violations:
            logger.info(f"\n🟡 中等严重程度违规 ({len(medium_violations)} 个):")
            for violation in medium_violations:
                logger.info(f"  - {violation.file_path}")
                logger.info(f"    违规类型: {violation.violation_type}")
                logger.info(f"    建议: {violation.expected_location}")
        
        if low_violations:
            logger.info(f"\n🟢 低严重程度违规 ({len(low_violations)} 个):")
            for violation in low_violations:
                logger.info(f"  - {violation.file_path}")
                logger.info(f"    违规类型: {violation.violation_type}")
                logger.info(f"    建议: {violation.expected_location}")
        
        if args.fix:
            logger.info("\n🔧 自动修复功能暂未实现")
    
    def check_storage(self, args):
        """检查存储空间"""
        logger.info("💾 检查存储空间使用情况...")
        
        monitor = StorageMonitor(args.project_root)
        metrics = monitor.collect_metrics()
        
        logger.info(f"📊 存储空间统计:")
        logger.info(f"  总空间: {self._format_bytes(metrics.total_space)}")
        logger.info(f"  已使用: {self._format_bytes(metrics.used_space)} ({metrics.usage_percentage:.1f}%)")
        logger.info(f"  可用空间: {self._format_bytes(metrics.free_space)}")
        logger.info(f"  文件总数: {metrics.file_count:,}")
        logger.info(f"  目录总数: {metrics.directory_count:,}")
        
        # 显示增长趋势
        if args.trend:
            trend = monitor.get_growth_trend(args.days or 7)
            logger.info(f"\n📈 {args.days or 7}天增长趋势:")
            logger.info(f"  文件数量变化: {trend['file_growth']:+,}")
            logger.info(f"  存储空间变化: {self._format_bytes(trend['space_growth'], signed=True)}")
        
        # 存储使用率告警
        if metrics.usage_percentage > 90:
            logger.info("🚨 警告: 磁盘使用率超过90%，建议清理文件")
        elif metrics.usage_percentage > 80:
            logger.info("⚠️  注意: 磁盘使用率超过80%，请关注存储空间")
    
    def check_config_changes(self, args):
        """检查配置文件变更"""
        logger.info("⚙️  检查配置文件变更...")
        
        tracker = ConfigChangeTracker(args.project_root)
        changes = tracker.check_changes()
        
        if not changes:
            logger.info("✅ 未发现配置文件变更")
            return
        
        logger.info(f"📝 发现 {len(changes)} 个配置文件变更:")
        
        for change in changes:
            change_icon = {
                "created": "➕",
                "modified": "✏️",
                "deleted": "❌"
            }.get(change.change_type, "📝")
            
            logger.info(f"  {change_icon} {change.config_path}")
            logger.info(f"    变更类型: {change.change_type}")
            logger.info(f"    时间: {change.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 显示最近的变更历史
        if args.history:
            hours = args.hours or 24
            recent_changes = tracker.get_recent_changes(hours)
            logger.info(f"\n📅 最近 {hours} 小时的变更历史:")
            
            for change in recent_changes:
                logger.info(f"  {change.timestamp.strftime('%m-%d %H:%M')} - {change.change_type} - {change.config_path}")
    
    def generate_report(self, args):
        """生成监控报告"""
        logger.info("📋 生成监控报告...")
        
        monitoring_system = FileOrganizationMonitoringSystem(args.project_root)
        report = monitoring_system.generate_monitoring_report()
        
        logger.info("📊 监控报告摘要:")
        logger.info(f"  报告时间: {datetime.fromisoformat(report['report_time']).strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"  目录变更: {report['directory_changes']['count']} 个")
        logger.info(f"  规范违规: {report['compliance_violations']['total']} 个")
        logger.info(f"    - 高严重程度: {report['compliance_violations']['high_severity']} 个")
        logger.info(f"    - 中等严重程度: {report['compliance_violations']['medium_severity']} 个")
        logger.info(f"    - 低严重程度: {report['compliance_violations']['low_severity']} 个")
        logger.info(f"  配置变更: {report['config_changes']['count']} 个")
        
        storage = report['storage_metrics']
        logger.info(f"  存储使用率: {storage['usage_percentage']:.1f}%")
        logger.info(f"  文件总数: {storage['file_count']:,}")
        
        trend = report['storage_trend']
        logger.info(f"  7天文件增长: {trend['file_growth']:+,}")
        logger.info(f"  7天空间增长: {self._format_bytes(trend['space_growth'], signed=True)}")
        
        if args.output:
            output_file = Path(args.output)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📄 详细报告已保存到: {output_file}")
        
        logger.info(f"📄 详细报告已保存到: logs/monitoring/monitoring_report.json")
    
    def show_directory_changes(self, args):
        """显示目录结构变更"""
        logger.info("📁 查看目录结构变更...")
        
        monitor = DirectoryStructureMonitor(args.project_root)
        hours = args.hours or 24
        changes = monitor.get_recent_changes(hours)
        
        if not changes:
            logger.info(f"✅ 最近 {hours} 小时内未发现目录结构变更")
            return
        
        logger.info(f"📝 最近 {hours} 小时的目录变更 ({len(changes)} 个):")
        
        for change in changes:
            change_icon = {
                "created": "➕",
                "deleted": "❌",
                "moved": "🔄",
                "modified": "✏️"
            }.get(change.event_type, "📝")
            
            logger.info(f"  {change_icon} {change.path}")
            logger.info(f"    操作: {change.event_type}")
            logger.info(f"    时间: {change.timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
            
            if change.old_path:
                logger.info(f"    原路径: {change.old_path}")
    
    def _format_bytes(self, bytes_value: int, signed: bool = False) -> str:
        """格式化字节数"""
        if bytes_value == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        abs_value = abs(bytes_value)
        unit_index = 0
        
        while abs_value >= 1024 and unit_index < len(units) - 1:
            abs_value /= 1024
            unit_index += 1
        
        sign = "+" if signed and bytes_value > 0 else ""
        sign = "-" if bytes_value < 0 else sign
        
        return f"{sign}{abs_value:.1f} {units[unit_index]}"


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="文件组织监控系统命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s start --daemon                    # 启动守护进程监控
  %(prog)s compliance                        # 检查文件放置规范
  %(prog)s storage --trend --days 7         # 检查存储空间和7天趋势
  %(prog)s config --history --hours 48      # 检查配置变更和48小时历史
  %(prog)s report --output report.json      # 生成监控报告
  %(prog)s directory --hours 12             # 查看12小时内目录变更
        """
    )
    
    parser.add_argument(
        '--project-root',
        default='.',
        help='项目根目录路径 (默认: 当前目录)'
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 启动监控命令
    start_parser = subparsers.add_parser('start', help='启动监控系统')
    start_parser.add_argument(
        '--daemon',
        action='store_true',
        help='以守护进程模式运行'
    )
    
    # 规范检查命令
    compliance_parser = subparsers.add_parser('compliance', help='检查文件放置规范')
    compliance_parser.add_argument(
        '--fix',
        action='store_true',
        help='自动修复违规（暂未实现）'
    )
    
    # 存储检查命令
    storage_parser = subparsers.add_parser('storage', help='检查存储空间')
    storage_parser.add_argument(
        '--trend',
        action='store_true',
        help='显示增长趋势'
    )
    storage_parser.add_argument(
        '--days',
        type=int,
        help='趋势分析天数 (默认: 7)'
    )
    
    # 配置变更检查命令
    config_parser = subparsers.add_parser('config', help='检查配置文件变更')
    config_parser.add_argument(
        '--history',
        action='store_true',
        help='显示变更历史'
    )
    config_parser.add_argument(
        '--hours',
        type=int,
        help='历史查询小时数 (默认: 24)'
    )
    
    # 报告生成命令
    report_parser = subparsers.add_parser('report', help='生成监控报告')
    report_parser.add_argument(
        '--output',
        help='报告输出文件路径'
    )
    
    # 目录变更查看命令
    directory_parser = subparsers.add_parser('directory', help='查看目录结构变更')
    directory_parser.add_argument(
        '--hours',
        type=int,
        help='查询小时数 (默认: 24)'
    )
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cli = MonitoringCLI()
    
    try:
        if args.command == 'start':
            cli.start_monitoring(args)
        elif args.command == 'compliance':
            cli.check_compliance(args)
        elif args.command == 'storage':
            cli.check_storage(args)
        elif args.command == 'config':
            cli.check_config_changes(args)
        elif args.command == 'report':
            cli.generate_report(args)
        elif args.command == 'directory':
            cli.show_directory_changes(args)
    except Exception as e:
        logger.info(f"❌ 执行命令时发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()