#!/usr/bin/env python3
"""
环境配置管理器

本模块提供环境特定配置的管理功能，支持多环境配置切换、
环境配置创建、更新和验证等功能。

作者: 量化交易系统开发团队
版本: 1.0.0
"""

import os
import yaml
import json
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class EnvironmentInfo:
    """环境信息"""
    name: str                           # 环境名称
    description: str                    # 环境描述
    config_file: str                    # 配置文件路径
    is_active: bool = False            # 是否为当前活动环境
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    tags: Set[str] = field(default_factory=set)  # 环境标签


@dataclass
class EnvironmentTemplate:
    """环境模板"""
    name: str                           # 模板名称
    description: str                    # 模板描述
    config_template: Dict[str, Any]     # 配置模板
    required_variables: List[str] = field(default_factory=list)  # 必需变量
    optional_variables: List[str] = field(default_factory=list)  # 可选变量


class EnvironmentManager:
    """环境配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化环境管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.environments_dir = self.config_dir / "environments"
        self.templates_dir = self.config_dir / "templates"
        
        # 确保目录存在
        self.environments_dir.mkdir(parents=True, exist_ok=True)
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        # 当前环境
        self.current_environment = os.getenv('TRADING_ENV', 'development')
        
        # 环境信息缓存
        self._environment_cache: Dict[str, EnvironmentInfo] = {}
        self._load_environment_info()
        
        logger.info(f"环境管理器初始化完成，当前环境: {self.current_environment}")
    
    def _load_environment_info(self) -> None:
        """加载环境信息"""
        self._environment_cache.clear()
        
        for env_file in self.environments_dir.glob("*.yaml"):
            env_name = env_file.stem
            
            try:
                stat = env_file.stat()
                env_info = EnvironmentInfo(
                    name=env_name,
                    description=f"{env_name} 环境配置",
                    config_file=str(env_file),
                    is_active=(env_name == self.current_environment),
                    created_at=datetime.fromtimestamp(stat.st_ctime),
                    updated_at=datetime.fromtimestamp(stat.st_mtime)
                )
                
                # 尝试从配置文件中读取描述和标签
                with open(env_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f) or {}
                    
                    if 'environment_info' in config_data:
                        info = config_data['environment_info']
                        env_info.description = info.get('description', env_info.description)
                        env_info.tags = set(info.get('tags', []))
                
                self._environment_cache[env_name] = env_info
                
            except Exception as e:
                logger.warning(f"加载环境信息失败 {env_name}: {str(e)}")
    
    def list_environments(self) -> List[EnvironmentInfo]:
        """列出所有环境"""
        return list(self._environment_cache.values())
    
    def get_environment_info(self, environment: str) -> Optional[EnvironmentInfo]:
        """获取环境信息"""
        return self._environment_cache.get(environment)
    
    def get_current_environment(self) -> str:
        """获取当前环境"""
        return self.current_environment
    
    def set_current_environment(self, environment: str) -> None:
        """设置当前环境"""
        if not self.environment_exists(environment):
            raise ValueError(f"环境不存在: {environment}")
        
        # 更新环境变量
        old_env = self.current_environment
        self.current_environment = environment
        os.environ['TRADING_ENV'] = environment
        
        # 更新缓存中的活动状态
        if old_env in self._environment_cache:
            self._environment_cache[old_env].is_active = False
        if environment in self._environment_cache:
            self._environment_cache[environment].is_active = True
        
        logger.info(f"环境已切换: {old_env} -> {environment}")
    
    def environment_exists(self, environment: str) -> bool:
        """检查环境是否存在"""
        return environment in self._environment_cache
    
    def create_environment(self, environment: str, config_data: Dict[str, Any], 
                         description: str = "", tags: List[str] = None) -> None:
        """
        创建新环境
        
        Args:
            environment: 环境名称
            config_data: 配置数据
            description: 环境描述
            tags: 环境标签
        """
        if self.environment_exists(environment):
            raise ValueError(f"环境已存在: {environment}")
        
        env_file = self.environments_dir / f"{environment}.yaml"
        
        # 添加环境信息到配置
        full_config = config_data.copy()
        full_config['environment_info'] = {
            'description': description or f"{environment} 环境配置",
            'tags': tags or [],
            'created_at': datetime.now().isoformat(),
            'created_by': os.getenv('USER', 'system')
        }
        
        try:
            with open(env_file, 'w', encoding='utf-8') as f:
                yaml.dump(full_config, f, default_flow_style=False, allow_unicode=True)
            
            # 更新缓存
            self._load_environment_info()
            
            logger.info(f"成功创建环境: {environment}")
            
        except Exception as e:
            logger.error(f"创建环境失败 {environment}: {str(e)}")
            raise
    
    def update_environment(self, environment: str, updates: Dict[str, Any], 
                         merge: bool = True) -> None:
        """
        更新环境配置
        
        Args:
            environment: 环境名称
            updates: 更新数据
            merge: 是否合并更新（True）还是完全替换（False）
        """
        if not self.environment_exists(environment):
            raise ValueError(f"环境不存在: {environment}")
        
        env_file = self.environments_dir / f"{environment}.yaml"
        
        try:
            if merge:
                # 加载现有配置
                with open(env_file, 'r', encoding='utf-8') as f:
                    current_config = yaml.safe_load(f) or {}
                
                # 深度合并
                updated_config = self._deep_merge(current_config, updates)
            else:
                updated_config = updates.copy()
            
            # 更新修改时间
            if 'environment_info' not in updated_config:
                updated_config['environment_info'] = {}
            
            updated_config['environment_info']['updated_at'] = datetime.now().isoformat()
            updated_config['environment_info']['updated_by'] = os.getenv('USER', 'system')
            
            # 保存配置
            with open(env_file, 'w', encoding='utf-8') as f:
                yaml.dump(updated_config, f, default_flow_style=False, allow_unicode=True)
            
            # 更新缓存
            self._load_environment_info()
            
            logger.info(f"成功更新环境: {environment}")
            
        except Exception as e:
            logger.error(f"更新环境失败 {environment}: {str(e)}")
            raise
    
    def delete_environment(self, environment: str, force: bool = False) -> None:
        """
        删除环境
        
        Args:
            environment: 环境名称
            force: 是否强制删除（即使是当前环境）
        """
        if not self.environment_exists(environment):
            raise ValueError(f"环境不存在: {environment}")
        
        if environment == self.current_environment and not force:
            raise ValueError(f"不能删除当前活动环境: {environment}，请先切换到其他环境或使用force=True")
        
        env_file = self.environments_dir / f"{environment}.yaml"
        
        try:
            # 备份配置文件
            backup_file = env_file.with_suffix(f'.yaml.backup.{datetime.now().strftime("%Y%m%d_%H%M%S")}')
            shutil.copy2(env_file, backup_file)
            
            # 删除配置文件
            env_file.unlink()
            
            # 如果删除的是当前环境，切换到development
            if environment == self.current_environment:
                if self.environment_exists('development'):
                    self.set_current_environment('development')
                else:
                    # 找到第一个可用环境
                    available_envs = [name for name in self._environment_cache.keys() if name != environment]
                    if available_envs:
                        self.set_current_environment(available_envs[0])
                    else:
                        self.current_environment = 'development'
                        os.environ['TRADING_ENV'] = 'development'
            
            # 更新缓存
            self._load_environment_info()
            
            logger.info(f"成功删除环境: {environment}，备份文件: {backup_file}")
            
        except Exception as e:
            logger.error(f"删除环境失败 {environment}: {str(e)}")
            raise
    
    def copy_environment(self, source_env: str, target_env: str, 
                        description: str = "") -> None:
        """
        复制环境配置
        
        Args:
            source_env: 源环境名称
            target_env: 目标环境名称
            description: 新环境描述
        """
        if not self.environment_exists(source_env):
            raise ValueError(f"源环境不存在: {source_env}")
        
        if self.environment_exists(target_env):
            raise ValueError(f"目标环境已存在: {target_env}")
        
        # 加载源环境配置
        source_config = self.load_environment_config(source_env)
        
        # 移除环境信息，避免复制创建时间等
        if 'environment_info' in source_config:
            del source_config['environment_info']
        
        # 创建新环境
        self.create_environment(
            target_env, 
            source_config, 
            description or f"从 {source_env} 复制的环境配置"
        )
        
        logger.info(f"成功复制环境: {source_env} -> {target_env}")
    
    def load_environment_config(self, environment: str) -> Dict[str, Any]:
        """加载环境配置"""
        if not self.environment_exists(environment):
            raise ValueError(f"环境不存在: {environment}")
        
        env_file = self.environments_dir / f"{environment}.yaml"
        
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            logger.error(f"加载环境配置失败 {environment}: {str(e)}")
            raise
    
    def export_environment(self, environment: str, output_file: Optional[str] = None, 
                          format: str = 'yaml') -> str:
        """
        导出环境配置
        
        Args:
            environment: 环境名称
            output_file: 输出文件路径
            format: 输出格式（yaml或json）
        
        Returns:
            输出文件路径
        """
        config_data = self.load_environment_config(environment)
        
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"{environment}_config_{timestamp}.{format}"
        
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                if format.lower() == 'json':
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
                else:
                    yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"环境配置已导出: {environment} -> {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"导出环境配置失败: {str(e)}")
            raise
    
    def import_environment(self, environment: str, config_file: str, 
                          overwrite: bool = False) -> None:
        """
        导入环境配置
        
        Args:
            environment: 环境名称
            config_file: 配置文件路径
            overwrite: 是否覆盖已存在的环境
        """
        if self.environment_exists(environment) and not overwrite:
            raise ValueError(f"环境已存在: {environment}，使用overwrite=True来覆盖")
        
        config_path = Path(config_file)
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.json':
                    config_data = json.load(f)
                else:
                    config_data = yaml.safe_load(f) or {}
            
            if overwrite and self.environment_exists(environment):
                self.update_environment(environment, config_data, merge=False)
            else:
                self.create_environment(environment, config_data, f"从 {config_file} 导入")
            
            logger.info(f"成功导入环境配置: {config_file} -> {environment}")
            
        except Exception as e:
            logger.error(f"导入环境配置失败: {str(e)}")
            raise
    
    def validate_environment(self, environment: str, 
                           required_keys: List[str] = None) -> List[str]:
        """
        验证环境配置
        
        Args:
            environment: 环境名称
            required_keys: 必需的配置键列表
        
        Returns:
            错误消息列表
        """
        errors = []
        
        if not self.environment_exists(environment):
            errors.append(f"环境不存在: {environment}")
            return errors
        
        try:
            config_data = self.load_environment_config(environment)
            
            # 检查必需键
            if required_keys:
                for key in required_keys:
                    if not self._has_nested_key(config_data, key):
                        errors.append(f"缺少必需的配置项: {key}")
            
            # 基本结构验证
            if 'environment' not in config_data:
                errors.append("缺少环境标识符")
            elif config_data['environment'] != environment:
                errors.append(f"环境标识符不匹配: 期望 {environment}，实际 {config_data['environment']}")
            
        except Exception as e:
            errors.append(f"验证环境配置时发生错误: {str(e)}")
        
        return errors
    
    def create_environment_template(self, template_name: str, 
                                  base_config: Dict[str, Any],
                                  description: str = "",
                                  required_vars: List[str] = None,
                                  optional_vars: List[str] = None) -> None:
        """创建环境模板"""
        template = EnvironmentTemplate(
            name=template_name,
            description=description or f"{template_name} 环境模板",
            config_template=base_config,
            required_variables=required_vars or [],
            optional_variables=optional_vars or []
        )
        
        template_file = self.templates_dir / f"{template_name}_env.yaml"
        
        template_data = {
            'template_info': {
                'name': template.name,
                'description': template.description,
                'required_variables': template.required_variables,
                'optional_variables': template.optional_variables,
                'created_at': datetime.now().isoformat()
            },
            'config_template': template.config_template
        }
        
        try:
            with open(template_file, 'w', encoding='utf-8') as f:
                yaml.dump(template_data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"成功创建环境模板: {template_name}")
            
        except Exception as e:
            logger.error(f"创建环境模板失败: {str(e)}")
            raise
    
    def create_environment_from_template(self, template_name: str, 
                                       environment: str,
                                       variables: Dict[str, str] = None) -> None:
        """从模板创建环境"""
        template_file = self.templates_dir / f"{template_name}_env.yaml"
        
        if not template_file.exists():
            raise FileNotFoundError(f"环境模板不存在: {template_name}")
        
        try:
            with open(template_file, 'r', encoding='utf-8') as f:
                template_data = yaml.safe_load(f) or {}
            
            template_info = template_data.get('template_info', {})
            config_template = template_data.get('config_template', {})
            
            # 替换变量
            if variables:
                config_template = self._replace_template_variables(config_template, variables)
            
            # 检查必需变量
            required_vars = template_info.get('required_variables', [])
            missing_vars = [var for var in required_vars if not variables or var not in variables]
            if missing_vars:
                raise ValueError(f"缺少必需的模板变量: {', '.join(missing_vars)}")
            
            # 创建环境
            description = f"从模板 {template_name} 创建"
            self.create_environment(environment, config_template, description)
            
            logger.info(f"成功从模板创建环境: {template_name} -> {environment}")
            
        except Exception as e:
            logger.error(f"从模板创建环境失败: {str(e)}")
            raise
    
    def _deep_merge(self, base: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并字典"""
        result = base.copy()
        
        for key, value in updates.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _has_nested_key(self, data: Dict[str, Any], key: str) -> bool:
        """检查嵌套键是否存在"""
        keys = key.split('.')
        current = data
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return False
        
        return True
    
    def _replace_template_variables(self, template: Any, variables: Dict[str, str]) -> Any:
        """替换模板中的变量"""
        if isinstance(template, str):
            for var_name, var_value in variables.items():
                template = template.replace(f"{{{var_name}}}", var_value)
            return template
        elif isinstance(template, dict):
            return {k: self._replace_template_variables(v, variables) for k, v in template.items()}
        elif isinstance(template, list):
            return [self._replace_template_variables(item, variables) for item in template]
        else:
            return template
    
    def get_environment_diff(self, env1: str, env2: str) -> Dict[str, Any]:
        """比较两个环境的配置差异"""
        if not self.environment_exists(env1):
            raise ValueError(f"环境不存在: {env1}")
        if not self.environment_exists(env2):
            raise ValueError(f"环境不存在: {env2}")
        
        config1 = self.load_environment_config(env1)
        config2 = self.load_environment_config(env2)
        
        # 移除环境信息，只比较配置内容
        config1.pop('environment_info', None)
        config2.pop('environment_info', None)
        
        return {
            'added': self._find_added_keys(config1, config2),
            'removed': self._find_removed_keys(config1, config2),
            'modified': self._find_modified_keys(config1, config2)
        }
    
    def _find_added_keys(self, old_config: Dict[str, Any], new_config: Dict[str, Any], 
                        prefix: str = "") -> List[str]:
        """查找新增的配置键"""
        added = []
        
        for key, value in new_config.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if key not in old_config:
                added.append(full_key)
            elif isinstance(value, dict) and isinstance(old_config[key], dict):
                added.extend(self._find_added_keys(old_config[key], value, full_key))
        
        return added
    
    def _find_removed_keys(self, old_config: Dict[str, Any], new_config: Dict[str, Any], 
                          prefix: str = "") -> List[str]:
        """查找删除的配置键"""
        return self._find_added_keys(new_config, old_config, prefix)
    
    def _find_modified_keys(self, old_config: Dict[str, Any], new_config: Dict[str, Any], 
                           prefix: str = "") -> List[str]:
        """查找修改的配置键"""
        modified = []
        
        for key, value in new_config.items():
            if key in old_config:
                full_key = f"{prefix}.{key}" if prefix else key
                old_value = old_config[key]
                
                if isinstance(value, dict) and isinstance(old_value, dict):
                    modified.extend(self._find_modified_keys(old_value, value, full_key))
                elif value != old_value:
                    modified.append(full_key)
        
        return modified


def main():
    """主函数 - 演示环境管理器的使用"""
    logger.info("🌍 环境配置管理器演示")
    logger.info("=" * 50)
    
    # 创建环境管理器
    env_manager = EnvironmentManager()
    
    # 显示当前环境信息
    logger.info(f"📍 当前环境: {env_manager.get_current_environment()}")
    
    # 列出所有环境
    environments = env_manager.list_environments()
    logger.info(f"📋 可用环境 ({len(environments)} 个):")
    for env_info in environments:
        status = "✅ 活动" if env_info.is_active else "⭕ 非活动"
        logger.info(f"  - {env_info.name}: {env_info.description} [{status}]")
        if env_info.tags:
            logger.info(f"    标签: {', '.join(env_info.tags)}")
    
    # 验证当前环境
    current_env = env_manager.get_current_environment()
    if env_manager.environment_exists(current_env):
        logger.info(f"\n🔍 验证环境: {current_env}")
        errors = env_manager.validate_environment(current_env)
        
        if errors:
            logger.info("❌ 环境验证失败:")
            for error in errors:
                logger.info(f"  - {error}")
        else:
            logger.info("✅ 环境验证通过")
    
    # 显示环境配置示例
    try:
        if env_manager.environment_exists('development'):
            logger.info(f"\n📖 开发环境配置示例:")
            dev_config = env_manager.load_environment_config('development')
            
            # 只显示部分配置
            if 'database' in dev_config:
                logger.info(f"  数据库: {dev_config['database'].get('host', 'N/A')}:{dev_config['database'].get('port', 'N/A')}")
            if 'api' in dev_config:
                logger.info(f"  API: {dev_config['api'].get('host', 'N/A')}:{dev_config['api'].get('port', 'N/A')}")
    
    except Exception as e:
        logger.info(f"❌ 加载环境配置失败: {str(e)}")


if __name__ == "__main__":
    main()