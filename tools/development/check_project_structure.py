import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
项目结构检查工具

检查项目目录结构是否符合规范，验证模块组织、文件命名、
导入路径等是否遵循最佳实践。

功能特性：
- 验证目录结构符合规范
- 检查模块导入路径
- 验证文件命名规范
- 检测重复和异常嵌套
- 生成结构报告

作者: 量化交易系统开发团队
版本: 1.0.0
创建日期: 2025-01-15
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import importlib.util


@dataclass
class StructureIssue:
    """结构问题数据类"""
    level: str  # 'error', 'warning', 'info'
    category: str  # 'structure', 'naming', 'imports', 'duplication'
    message: str
    path: str
    suggestions: List[str]


@dataclass
class ProjectStructureReport:
    """项目结构报告"""
    timestamp: str
    issues: List[StructureIssue]
    summary: Dict[str, int]
    recommendations: List[str]


class ProjectStructureChecker:
    """项目结构检查器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.src_path = project_root / "src"
        self.issues: List[StructureIssue] = []
        
        # 标准目录结构规范
        self.standard_structure = {
            "src": {
                "application": ["services", "main.py"],
                "domain": ["models", "context"],
                "infrastructure": ["api_clients", "database"],
                "market": ["data", "indicators", "strategies", "analysis"],
                "trading": ["execution", "orders", "portfolio"],
                "core": ["engine", "monitoring"],
                "common": ["utils", "exceptions.py"],
                "risk": [],
                "backtest": [],
                "config": [],
                "api": [],
                "monitoring": [],
                "performance": [],
                "security": []
            }
        }
    
    def check_structure(self) -> ProjectStructureReport:
        """执行完整的结构检查"""
        logger.info("🔍 开始项目结构检查...")
        
        self._check_directory_structure()
        self._check_module_imports()
        self._check_file_naming()
        self._check_duplications()
        self._check_nested_structures()
        
        return self._generate_report()
    
    def _check_directory_structure(self):
        """检查目录结构"""
        logger.info("  检查目录结构...")
        
        if not self.src_path.exists():
            self.issues.append(StructureIssue(
                level="error",
                category="structure",
                message="缺少src/目录",
                path=str(self.project_root),
                suggestions=["创建src/目录作为源代码根目录"]
            ))
            return
        
        # 检查必需的顶级目录
        required_dirs = ["application", "domain", "infrastructure", "market", "trading", "core", "common"]
        for dir_name in required_dirs:
            dir_path = self.src_path / dir_name
            if not dir_path.exists():
                self.issues.append(StructureIssue(
                    level="warning",
                    category="structure",
                    message=f"建议的目录 {dir_name}/ 不存在",
                    path=str(dir_path),
                    suggestions=[f"创建 {dir_name}/ 目录以改善代码组织"]
                ))
    
    def _check_module_imports(self):
        """检查模块导入路径"""
        logger.info("  检查模块导入...")
        
        for py_file in self.src_path.rglob("*.py"):
            if py_file.name == "__init__.py":
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 检查相对导入问题
                if "from .." in content and content.count("..") > 2:
                    self.issues.append(StructureIssue(
                        level="warning",
                        category="imports",
                        message="过深的相对导入",
                        path=str(py_file),
                        suggestions=["考虑重组模块结构或使用绝对导入"]
                    ))
                
                # 检查循环导入风险
                if "import src." in content:
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if "import src." in line and py_file.stem in line:
                            self.issues.append(StructureIssue(
                                level="error",
                                category="imports",
                                message=f"可能的循环导入: {line.strip()}",
                                path=f"{py_file}:{i}",
                                suggestions=["重构代码避免循环导入"]
                            ))
                            
            except Exception as e:
                self.issues.append(StructureIssue(
                    level="warning",
                    category="imports",
                    message=f"无法读取文件: {e}",
                    path=str(py_file),
                    suggestions=["检查文件编码和权限"]
                ))
    
    def _check_file_naming(self):
        """检查文件命名规范"""
        logger.info("  检查文件命名...")
        
        for py_file in self.src_path.rglob("*.py"):
            filename = py_file.name
            
            # 检查Python命名规范
            if not filename.replace('_', '').replace('.py', '').islower():
                if filename not in ['__init__.py', '__main__.py']:
                    self.issues.append(StructureIssue(
                        level="warning",
                        category="naming",
                        message=f"文件名不符合Python命名规范: {filename}",
                        path=str(py_file),
                        suggestions=["使用小写字母和下划线命名文件"]
                    ))
            
            # 检查常见问题命名
            problematic_names = ['temp.py', 'test.py', 'tmp.py', 'backup.py']
            if filename in problematic_names:
                self.issues.append(StructureIssue(
                    level="warning",
                    category="naming",
                    message=f"可能的临时文件: {filename}",
                    path=str(py_file),
                    suggestions=["使用更具描述性的文件名或移除临时文件"]
                ))
    
    def _check_duplications(self):
        """检查重复模块和文件"""
        logger.info("  检查重复结构...")
        
        # 检查重复的__init__.py内容
        init_files = list(self.src_path.rglob("__init__.py"))
        for init_file in init_files:
            try:
                with open(init_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    
                if len(content) > 100:  # 内容较多的__init__.py
                    # 检查是否有重复的导入模式
                    if content.count("from") > 10:
                        self.issues.append(StructureIssue(
                            level="info",
                            category="structure",
                            message="__init__.py文件内容较多，可能需要重构",
                            path=str(init_file),
                            suggestions=["考虑将部分导入移到子模块中"]
                        ))
            except Exception:
                pass
        
        # 检查功能重复的目录
        dir_names = set()
        duplicate_patterns = [
            ("data", "market/data"),
            ("utils", "common/utils"),
            ("models", "domain/models"),
            ("strategies", "market/strategies")
        ]
        
        for pattern1, pattern2 in duplicate_patterns:
            path1 = self.src_path / pattern1
            path2 = self.src_path / pattern2
            
            if path1.exists() and path2.exists():
                self.issues.append(StructureIssue(
                    level="warning",
                    category="duplication",
                    message=f"发现重复的功能目录: {pattern1}/ 和 {pattern2}/",
                    path=str(self.src_path),
                    suggestions=[f"合并重复目录或创建兼容层"]
                ))
    
    def _check_nested_structures(self):
        """检查异常嵌套结构"""
        logger.info("  检查嵌套结构...")
        
        for dir_path in self.src_path.rglob("*"):
            if dir_path.is_dir():
                dir_name = dir_path.name
                parent_name = dir_path.parent.name
                
                # 检查双层嵌套
                if dir_name == parent_name:
                    self.issues.append(StructureIssue(
                        level="error",
                        category="structure",
                        message=f"检测到异常嵌套: {parent_name}/{dir_name}/",
                        path=str(dir_path),
                        suggestions=["移除重复的嵌套目录"]
                    ))
                
                # 检查过深的嵌套
                relative_path = dir_path.relative_to(self.src_path)
                if len(relative_path.parts) > 4:
                    self.issues.append(StructureIssue(
                        level="warning",
                        category="structure",
                        message=f"目录嵌套过深: {relative_path}",
                        path=str(dir_path),
                        suggestions=["考虑简化目录结构"]
                    ))
    
    def _generate_report(self) -> ProjectStructureReport:
        """生成检查报告"""
        logger.info("  生成报告...")
        
        # 统计问题
        summary = {
            "total": len(self.issues),
            "errors": len([i for i in self.issues if i.level == "error"]),
            "warnings": len([i for i in self.issues if i.level == "warning"]),
            "info": len([i for i in self.issues if i.level == "info"])
        }
        
        # 生成建议
        recommendations = [
            "遵循Clean Architecture分层原则",
            "使用兼容层处理历史导入",
            "定期运行结构检查工具",
            "保持模块职责单一"
        ]
        
        if summary["errors"] > 0:
            recommendations.insert(0, "优先解决错误级别的问题")
        
        return ProjectStructureReport(
            timestamp=datetime.now().isoformat(),
            issues=self.issues,
            summary=summary,
            recommendations=recommendations
        )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="项目结构检查工具")
    parser.add_argument("--project-root", type=Path, 
                       help="项目根目录路径")
    parser.add_argument("--output", "-o", type=Path,
                       help="输出报告文件路径")
    parser.add_argument("--format", choices=["json", "text"], default="text",
                       help="输出格式")
    
    args = parser.parse_args()
    
    # 确定项目根目录
    if args.project_root:
        project_root = args.project_root
    else:
        # 从当前脚本位置推导
        project_root = Path(__file__).parent.parent.parent
    
    if not project_root.exists():
        logger.info(f"❌ 项目根目录不存在: {project_root}")
        sys.exit(1)
    
    # 执行检查
    checker = ProjectStructureChecker(project_root)
    report = checker.check_structure()
    
    # 输出报告
    if args.format == "json":
        output_content = json.dumps(asdict(report), indent=2, ensure_ascii=False)
    else:
        output_content = format_text_report(report)
    
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(output_content)
        logger.info(f"📝 报告已保存到: {args.output}")
    else:
        logger.info(output_content)
    
    # 退出码
    if report.summary["errors"] > 0:
        sys.exit(1)
    elif report.summary["warnings"] > 0:
        sys.exit(2)
    else:
        sys.exit(0)


def format_text_report(report: ProjectStructureReport) -> str:
    """格式化文本报告"""
    lines = [
        "=" * 60,
        "🏗️  项目结构检查报告",
        "=" * 60,
        f"检查时间: {report.timestamp}",
        "",
        "📊 问题统计:",
        f"  总问题数: {report.summary['total']}",
        f"  错误: {report.summary['errors']}",
        f"  警告: {report.summary['warnings']}",
        f"  信息: {report.summary['info']}",
        ""
    ]
    
    if report.issues:
        lines.append("🔍 发现的问题:")
        lines.append("")
        
        for issue in report.issues:
            icon = "❌" if issue.level == "error" else "⚠️" if issue.level == "warning" else "ℹ️"
            lines.append(f"{icon} [{issue.category.upper()}] {issue.message}")
            lines.append(f"   路径: {issue.path}")
            if issue.suggestions:
                lines.append(f"   建议: {'; '.join(issue.suggestions)}")
            lines.append("")
    
    if report.recommendations:
        lines.append("💡 建议:")
        for rec in report.recommendations:
            lines.append(f"  • {rec}")
        lines.append("")
    
    lines.append("=" * 60)
    
    return "\n".join(lines)


if __name__ == "__main__":
    main()
