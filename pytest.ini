[tool:pytest]
minversion = 7.0
addopts = -ra -q --strict-markers --strict-config --import-mode=importlib
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

markers =
    # 测试类型标记
    unit: 单元测试 - 测试单个模块和函数的功能
    integration: 集成测试 - 测试模块间的协作和接口
    e2e: 端到端测试 - 测试完整的业务流程和用户场景
    performance: 性能测试 - 测试系统性能指标和负载能力
    regression: 回归测试 - 防止已修复的问题再次出现
    smoke: 冒烟测试 - 快速验证基本功能是否正常
    
    # 执行特性标记
    slow: 耗时较长的测试（通常>10秒）
    fast: 快速测试（通常<1秒）
    db: 需要数据库的测试
    network: 需要网络连接的测试
    api: API接口测试
    ui: 用户界面测试
    
    # 环境标记
    dev: 开发环境测试
    staging: 测试环境测试
    prod: 生产环境测试
    
    # 组件标记
    backend: 后端相关测试
    frontend: 前端相关测试
    data: 数据处理相关测试
    strategy: 策略相关测试
    risk: 风险管理相关测试
    
    # 特殊标记
    skip_ci: CI环境中跳过的测试
    manual: 需要手动执行的测试
    experimental: 实验性功能测试

filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::FutureWarning

[coverage:run]
source = src
omit = 
    */tests/*
    */test_*
    */__init__.py
    */venv/*
    */backup/*

[coverage:report]
exclude_lines = 
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
    if TYPE_CHECKING:
