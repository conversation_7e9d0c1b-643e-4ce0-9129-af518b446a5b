{"version": 3, "sources": ["../../react-window-infinite-loader/dist/index.esm.js"], "sourcesContent": ["import { PureComponent } from 'react';\n\nfunction isInteger(value) {\n  return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n}\n\nfunction isRangeVisible(_ref) {\n  var lastRenderedStartIndex = _ref.lastRenderedStartIndex,\n      lastRenderedStopIndex = _ref.lastRenderedStopIndex,\n      startIndex = _ref.startIndex,\n      stopIndex = _ref.stopIndex;\n\n  return !(startIndex > lastRenderedStopIndex || stopIndex < lastRenderedStartIndex);\n}\n\nfunction scanForUnloadedRanges(_ref) {\n  var isItemLoaded = _ref.isItemLoaded,\n      itemCount = _ref.itemCount,\n      minimumBatchSize = _ref.minimumBatchSize,\n      startIndex = _ref.startIndex,\n      stopIndex = _ref.stopIndex;\n\n  var unloadedRanges = [];\n\n  var rangeStartIndex = null;\n  var rangeStopIndex = null;\n\n  for (var _index = startIndex; _index <= stopIndex; _index++) {\n    var loaded = isItemLoaded(_index);\n\n    if (!loaded) {\n      rangeStopIndex = _index;\n      if (rangeStartIndex === null) {\n        rangeStartIndex = _index;\n      }\n    } else if (rangeStopIndex !== null) {\n      unloadedRanges.push(rangeStartIndex, rangeStopIndex);\n\n      rangeStartIndex = rangeStopIndex = null;\n    }\n  }\n\n  // If :rangeStopIndex is not null it means we haven't ran out of unloaded rows.\n  // Scan forward to try filling our :minimumBatchSize.\n  if (rangeStopIndex !== null) {\n    var potentialStopIndex = Math.min(Math.max(rangeStopIndex, rangeStartIndex + minimumBatchSize - 1), itemCount - 1);\n\n    for (var _index2 = rangeStopIndex + 1; _index2 <= potentialStopIndex; _index2++) {\n      if (!isItemLoaded(_index2)) {\n        rangeStopIndex = _index2;\n      } else {\n        break;\n      }\n    }\n\n    unloadedRanges.push(rangeStartIndex, rangeStopIndex);\n  }\n\n  // Check to see if our first range ended prematurely.\n  // In this case we should scan backwards to try filling our :minimumBatchSize.\n  if (unloadedRanges.length) {\n    while (unloadedRanges[1] - unloadedRanges[0] + 1 < minimumBatchSize && unloadedRanges[0] > 0) {\n      var _index3 = unloadedRanges[0] - 1;\n\n      if (!isItemLoaded(_index3)) {\n        unloadedRanges[0] = _index3;\n      } else {\n        break;\n      }\n    }\n  }\n\n  return unloadedRanges;\n}\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\nvar inherits = function (subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n};\n\nvar possibleConstructorReturn = function (self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n};\n\nvar InfiniteLoader = function (_PureComponent) {\n  inherits(InfiniteLoader, _PureComponent);\n\n  function InfiniteLoader() {\n    var _ref;\n\n    var _temp, _this, _ret;\n\n    classCallCheck(this, InfiniteLoader);\n\n    for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return _ret = (_temp = (_this = possibleConstructorReturn(this, (_ref = InfiniteLoader.__proto__ || Object.getPrototypeOf(InfiniteLoader)).call.apply(_ref, [this].concat(args))), _this), _this._lastRenderedStartIndex = -1, _this._lastRenderedStopIndex = -1, _this._memoizedUnloadedRanges = [], _this._onItemsRendered = function (_ref2) {\n      var visibleStartIndex = _ref2.visibleStartIndex,\n          visibleStopIndex = _ref2.visibleStopIndex;\n\n      if (process.env.NODE_ENV !== 'production') {\n        if (!isInteger(visibleStartIndex) || !isInteger(visibleStopIndex)) {\n          console.warn('Invalid onItemsRendered signature; please refer to InfiniteLoader documentation.');\n        }\n\n        if (typeof _this.props.loadMoreRows === 'function') {\n          console.warn('InfiniteLoader \"loadMoreRows\" prop has been renamed to \"loadMoreItems\".');\n        }\n      }\n\n      _this._lastRenderedStartIndex = visibleStartIndex;\n      _this._lastRenderedStopIndex = visibleStopIndex;\n\n      _this._ensureRowsLoaded(visibleStartIndex, visibleStopIndex);\n    }, _this._setRef = function (listRef) {\n      _this._listRef = listRef;\n    }, _temp), possibleConstructorReturn(_this, _ret);\n  }\n\n  createClass(InfiniteLoader, [{\n    key: 'resetloadMoreItemsCache',\n    value: function resetloadMoreItemsCache() {\n      var autoReload = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n\n      this._memoizedUnloadedRanges = [];\n\n      if (autoReload) {\n        this._ensureRowsLoaded(this._lastRenderedStartIndex, this._lastRenderedStopIndex);\n      }\n    }\n  }, {\n    key: 'componentDidMount',\n    value: function componentDidMount() {\n      if (process.env.NODE_ENV !== 'production') {\n        if (this._listRef == null) {\n          console.warn('Invalid list ref; please refer to InfiniteLoader documentation.');\n        }\n      }\n    }\n  }, {\n    key: 'render',\n    value: function render() {\n      var children = this.props.children;\n\n\n      return children({\n        onItemsRendered: this._onItemsRendered,\n        ref: this._setRef\n      });\n    }\n  }, {\n    key: '_ensureRowsLoaded',\n    value: function _ensureRowsLoaded(startIndex, stopIndex) {\n      var _props = this.props,\n          isItemLoaded = _props.isItemLoaded,\n          itemCount = _props.itemCount,\n          _props$minimumBatchSi = _props.minimumBatchSize,\n          minimumBatchSize = _props$minimumBatchSi === undefined ? 10 : _props$minimumBatchSi,\n          _props$threshold = _props.threshold,\n          threshold = _props$threshold === undefined ? 15 : _props$threshold;\n\n\n      var unloadedRanges = scanForUnloadedRanges({\n        isItemLoaded: isItemLoaded,\n        itemCount: itemCount,\n        minimumBatchSize: minimumBatchSize,\n        startIndex: Math.max(0, startIndex - threshold),\n        stopIndex: Math.min(itemCount - 1, stopIndex + threshold)\n      });\n\n      // Avoid calling load-rows unless range has changed.\n      // This shouldn't be strictly necessary, but is maybe nice to do.\n      if (this._memoizedUnloadedRanges.length !== unloadedRanges.length || this._memoizedUnloadedRanges.some(function (startOrStop, index) {\n        return unloadedRanges[index] !== startOrStop;\n      })) {\n        this._memoizedUnloadedRanges = unloadedRanges;\n        this._loadUnloadedRanges(unloadedRanges);\n      }\n    }\n  }, {\n    key: '_loadUnloadedRanges',\n    value: function _loadUnloadedRanges(unloadedRanges) {\n      var _this2 = this;\n\n      // loadMoreRows was renamed to loadMoreItems in v1.0.3; will be removed in v2.0\n      var loadMoreItems = this.props.loadMoreItems || this.props.loadMoreRows;\n\n      var _loop = function _loop(i) {\n        var startIndex = unloadedRanges[i];\n        var stopIndex = unloadedRanges[i + 1];\n        var promise = loadMoreItems(startIndex, stopIndex);\n        if (promise != null) {\n          promise.then(function () {\n            // Refresh the visible rows if any of them have just been loaded.\n            // Otherwise they will remain in their unloaded visual state.\n            if (isRangeVisible({\n              lastRenderedStartIndex: _this2._lastRenderedStartIndex,\n              lastRenderedStopIndex: _this2._lastRenderedStopIndex,\n              startIndex: startIndex,\n              stopIndex: stopIndex\n            })) {\n              // Handle an unmount while promises are still in flight.\n              if (_this2._listRef == null) {\n                return;\n              }\n\n              // Resize cached row sizes for VariableSizeList,\n              // otherwise just re-render the list.\n              if (typeof _this2._listRef.resetAfterIndex === 'function') {\n                _this2._listRef.resetAfterIndex(startIndex, true);\n              } else {\n                // HACK reset temporarily cached item styles to force PureComponent to re-render.\n                // This is pretty gross, but I'm okay with it for now.\n                // Don't judge me.\n                if (typeof _this2._listRef._getItemStyleCache === 'function') {\n                  _this2._listRef._getItemStyleCache(-1);\n                }\n                _this2._listRef.forceUpdate();\n              }\n            }\n          });\n        }\n      };\n\n      for (var i = 0; i < unloadedRanges.length; i += 2) {\n        _loop(i);\n      }\n    }\n  }]);\n  return InfiniteLoader;\n}(PureComponent);\n\nexport default InfiniteLoader;\n"], "mappings": ";;;;;;;;AAAA,mBAA8B;AAE9B,SAAS,UAAU,OAAO;AACxB,SAAO,OAAO,UAAU,YAAY,SAAS,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM;AAC/E;AAEA,SAAS,eAAe,MAAM;AAC5B,MAAI,yBAAyB,KAAK,wBAC9B,wBAAwB,KAAK,uBAC7B,aAAa,KAAK,YAClB,YAAY,KAAK;AAErB,SAAO,EAAE,aAAa,yBAAyB,YAAY;AAC7D;AAEA,SAAS,sBAAsB,MAAM;AACnC,MAAI,eAAe,KAAK,cACpB,YAAY,KAAK,WACjB,mBAAmB,KAAK,kBACxB,aAAa,KAAK,YAClB,YAAY,KAAK;AAErB,MAAI,iBAAiB,CAAC;AAEtB,MAAI,kBAAkB;AACtB,MAAI,iBAAiB;AAErB,WAAS,SAAS,YAAY,UAAU,WAAW,UAAU;AAC3D,QAAI,SAAS,aAAa,MAAM;AAEhC,QAAI,CAAC,QAAQ;AACX,uBAAiB;AACjB,UAAI,oBAAoB,MAAM;AAC5B,0BAAkB;AAAA,MACpB;AAAA,IACF,WAAW,mBAAmB,MAAM;AAClC,qBAAe,KAAK,iBAAiB,cAAc;AAEnD,wBAAkB,iBAAiB;AAAA,IACrC;AAAA,EACF;AAIA,MAAI,mBAAmB,MAAM;AAC3B,QAAI,qBAAqB,KAAK,IAAI,KAAK,IAAI,gBAAgB,kBAAkB,mBAAmB,CAAC,GAAG,YAAY,CAAC;AAEjH,aAAS,UAAU,iBAAiB,GAAG,WAAW,oBAAoB,WAAW;AAC/E,UAAI,CAAC,aAAa,OAAO,GAAG;AAC1B,yBAAiB;AAAA,MACnB,OAAO;AACL;AAAA,MACF;AAAA,IACF;AAEA,mBAAe,KAAK,iBAAiB,cAAc;AAAA,EACrD;AAIA,MAAI,eAAe,QAAQ;AACzB,WAAO,eAAe,CAAC,IAAI,eAAe,CAAC,IAAI,IAAI,oBAAoB,eAAe,CAAC,IAAI,GAAG;AAC5F,UAAI,UAAU,eAAe,CAAC,IAAI;AAElC,UAAI,CAAC,aAAa,OAAO,GAAG;AAC1B,uBAAe,CAAC,IAAI;AAAA,MACtB,OAAO;AACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAI,iBAAiB,SAAU,UAAU,aAAa;AACpD,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;AAEA,IAAI,cAAc,WAAY;AAC5B,WAAS,iBAAiB,QAAQ,OAAO;AACvC,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,aAAa,MAAM,CAAC;AACxB,iBAAW,aAAa,WAAW,cAAc;AACjD,iBAAW,eAAe;AAC1B,UAAI,WAAW;AAAY,mBAAW,WAAW;AACjD,aAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,IAC1D;AAAA,EACF;AAEA,SAAO,SAAU,aAAa,YAAY,aAAa;AACrD,QAAI;AAAY,uBAAiB,YAAY,WAAW,UAAU;AAClE,QAAI;AAAa,uBAAiB,aAAa,WAAW;AAC1D,WAAO;AAAA,EACT;AACF,EAAE;AAEF,IAAI,WAAW,SAAU,UAAU,YAAY;AAC7C,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAC3D,UAAM,IAAI,UAAU,6DAA6D,OAAO,UAAU;AAAA,EACpG;AAEA,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW;AAAA,IACrE,aAAa;AAAA,MACX,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,MAAI;AAAY,WAAO,iBAAiB,OAAO,eAAe,UAAU,UAAU,IAAI,SAAS,YAAY;AAC7G;AAEA,IAAI,4BAA4B,SAAU,MAAM,MAAM;AACpD,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,eAAe,2DAA2D;AAAA,EACtF;AAEA,SAAO,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,cAAc,OAAO;AACnF;AAEA,IAAI,iBAAiB,SAAU,gBAAgB;AAC7C,WAASA,iBAAgB,cAAc;AAEvC,WAASA,kBAAiB;AACxB,QAAI;AAEJ,QAAI,OAAO,OAAO;AAElB,mBAAe,MAAMA,eAAc;AAEnC,aAAS,OAAO,UAAU,QAAQ,OAAO,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACnF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AAEA,WAAO,QAAQ,SAAS,QAAQ,0BAA0B,OAAO,OAAOA,gBAAe,aAAa,OAAO,eAAeA,eAAc,GAAG,KAAK,MAAM,MAAM,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,CAAC,GAAG,QAAQ,MAAM,0BAA0B,IAAI,MAAM,yBAAyB,IAAI,MAAM,0BAA0B,CAAC,GAAG,MAAM,mBAAmB,SAAU,OAAO;AAC9U,UAAI,oBAAoB,MAAM,mBAC1B,mBAAmB,MAAM;AAE7B,UAAI,MAAuC;AACzC,YAAI,CAAC,UAAU,iBAAiB,KAAK,CAAC,UAAU,gBAAgB,GAAG;AACjE,kBAAQ,KAAK,kFAAkF;AAAA,QACjG;AAEA,YAAI,OAAO,MAAM,MAAM,iBAAiB,YAAY;AAClD,kBAAQ,KAAK,yEAAyE;AAAA,QACxF;AAAA,MACF;AAEA,YAAM,0BAA0B;AAChC,YAAM,yBAAyB;AAE/B,YAAM,kBAAkB,mBAAmB,gBAAgB;AAAA,IAC7D,GAAG,MAAM,UAAU,SAAU,SAAS;AACpC,YAAM,WAAW;AAAA,IACnB,GAAG,QAAQ,0BAA0B,OAAO,IAAI;AAAA,EAClD;AAEA,cAAYA,iBAAgB,CAAC;AAAA,IAC3B,KAAK;AAAA,IACL,OAAO,SAAS,0BAA0B;AACxC,UAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAErF,WAAK,0BAA0B,CAAC;AAEhC,UAAI,YAAY;AACd,aAAK,kBAAkB,KAAK,yBAAyB,KAAK,sBAAsB;AAAA,MAClF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB;AAClC,UAAI,MAAuC;AACzC,YAAI,KAAK,YAAY,MAAM;AACzB,kBAAQ,KAAK,iEAAiE;AAAA,QAChF;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,UAAI,WAAW,KAAK,MAAM;AAG1B,aAAO,SAAS;AAAA,QACd,iBAAiB,KAAK;AAAA,QACtB,KAAK,KAAK;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,kBAAkB,YAAY,WAAW;AACvD,UAAI,SAAS,KAAK,OACd,eAAe,OAAO,cACtB,YAAY,OAAO,WACnB,wBAAwB,OAAO,kBAC/B,mBAAmB,0BAA0B,SAAY,KAAK,uBAC9D,mBAAmB,OAAO,WAC1B,YAAY,qBAAqB,SAAY,KAAK;AAGtD,UAAI,iBAAiB,sBAAsB;AAAA,QACzC;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAY,KAAK,IAAI,GAAG,aAAa,SAAS;AAAA,QAC9C,WAAW,KAAK,IAAI,YAAY,GAAG,YAAY,SAAS;AAAA,MAC1D,CAAC;AAID,UAAI,KAAK,wBAAwB,WAAW,eAAe,UAAU,KAAK,wBAAwB,KAAK,SAAU,aAAa,OAAO;AACnI,eAAO,eAAe,KAAK,MAAM;AAAA,MACnC,CAAC,GAAG;AACF,aAAK,0BAA0B;AAC/B,aAAK,oBAAoB,cAAc;AAAA,MACzC;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB,gBAAgB;AAClD,UAAI,SAAS;AAGb,UAAI,gBAAgB,KAAK,MAAM,iBAAiB,KAAK,MAAM;AAE3D,UAAI,QAAQ,SAASC,OAAMC,IAAG;AAC5B,YAAI,aAAa,eAAeA,EAAC;AACjC,YAAI,YAAY,eAAeA,KAAI,CAAC;AACpC,YAAI,UAAU,cAAc,YAAY,SAAS;AACjD,YAAI,WAAW,MAAM;AACnB,kBAAQ,KAAK,WAAY;AAGvB,gBAAI,eAAe;AAAA,cACjB,wBAAwB,OAAO;AAAA,cAC/B,uBAAuB,OAAO;AAAA,cAC9B;AAAA,cACA;AAAA,YACF,CAAC,GAAG;AAEF,kBAAI,OAAO,YAAY,MAAM;AAC3B;AAAA,cACF;AAIA,kBAAI,OAAO,OAAO,SAAS,oBAAoB,YAAY;AACzD,uBAAO,SAAS,gBAAgB,YAAY,IAAI;AAAA,cAClD,OAAO;AAIL,oBAAI,OAAO,OAAO,SAAS,uBAAuB,YAAY;AAC5D,yBAAO,SAAS,mBAAmB,EAAE;AAAA,gBACvC;AACA,uBAAO,SAAS,YAAY;AAAA,cAC9B;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAEA,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK,GAAG;AACjD,cAAM,CAAC;AAAA,MACT;AAAA,IACF;AAAA,EACF,CAAC,CAAC;AACF,SAAOF;AACT,EAAE,0BAAa;AAEf,IAAO,oBAAQ;", "names": ["InfiniteLoader", "_loop", "i"]}