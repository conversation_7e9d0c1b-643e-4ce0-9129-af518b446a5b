{"version": 3, "sources": ["../../react-virtualized-auto-sizer/dist/react-virtualized-auto-sizer.esm.js"], "sourcesContent": ["import { Component, createElement } from 'react';\n\n/**\n * Detect Element Resize.\n * https://github.com/sdecima/javascript-detect-element-resize\n * Sebastian Decima\n *\n * Forked from version 0.5.3; includes the following modifications:\n * 1) Guard against unsafe 'window' and 'document' references (to support SSR).\n * 2) Defer initialization code via a top-level function wrapper (to support SSR).\n * 3) Avoid unnecessary reflows by not measuring size for scroll events bubbling from children.\n * 4) Add nonce for style element.\n * 5) Use 'export' statement over 'module.exports' assignment\n **/\n\n// Check `document` and `window` in case of server-side rendering\nlet windowObject;\nif (typeof window !== \"undefined\") {\n  windowObject = window;\n\n  // eslint-disable-next-line no-restricted-globals\n} else if (typeof self !== \"undefined\") {\n  // eslint-disable-next-line no-restricted-globals\n  windowObject = self;\n} else {\n  windowObject = global;\n}\nlet cancelFrame = null;\nlet requestFrame = null;\nconst TIMEOUT_DURATION = 20;\nconst clearTimeoutFn = windowObject.clearTimeout;\nconst setTimeoutFn = windowObject.setTimeout;\nconst cancelAnimationFrameFn = windowObject.cancelAnimationFrame || windowObject.mozCancelAnimationFrame || windowObject.webkitCancelAnimationFrame;\nconst requestAnimationFrameFn = windowObject.requestAnimationFrame || windowObject.mozRequestAnimationFrame || windowObject.webkitRequestAnimationFrame;\nif (cancelAnimationFrameFn == null || requestAnimationFrameFn == null) {\n  // For environments that don't support animation frame,\n  // fallback to a setTimeout based approach.\n  cancelFrame = clearTimeoutFn;\n  requestFrame = function requestAnimationFrameViaSetTimeout(callback) {\n    return setTimeoutFn(callback, TIMEOUT_DURATION);\n  };\n} else {\n  // Counter intuitively, environments that support animation frames can be trickier.\n  // Chrome's \"Throttle non-visible cross-origin iframes\" flag can prevent rAFs from being called.\n  // In this case, we should fallback to a setTimeout() implementation.\n  cancelFrame = function cancelFrame([animationFrameID, timeoutID]) {\n    cancelAnimationFrameFn(animationFrameID);\n    clearTimeoutFn(timeoutID);\n  };\n  requestFrame = function requestAnimationFrameWithSetTimeoutFallback(callback) {\n    const animationFrameID = requestAnimationFrameFn(function animationFrameCallback() {\n      clearTimeoutFn(timeoutID);\n      callback();\n    });\n    const timeoutID = setTimeoutFn(function timeoutCallback() {\n      cancelAnimationFrameFn(animationFrameID);\n      callback();\n    }, TIMEOUT_DURATION);\n    return [animationFrameID, timeoutID];\n  };\n}\nfunction createDetectElementResize(nonce) {\n  let animationKeyframes;\n  let animationName;\n  let animationStartEvent;\n  let animationStyle;\n  let checkTriggers;\n  let resetTriggers;\n  let scrollListener;\n  const attachEvent = typeof document !== \"undefined\" && document.attachEvent;\n  if (!attachEvent) {\n    resetTriggers = function (element) {\n      const triggers = element.__resizeTriggers__,\n        expand = triggers.firstElementChild,\n        contract = triggers.lastElementChild,\n        expandChild = expand.firstElementChild;\n      contract.scrollLeft = contract.scrollWidth;\n      contract.scrollTop = contract.scrollHeight;\n      expandChild.style.width = expand.offsetWidth + 1 + \"px\";\n      expandChild.style.height = expand.offsetHeight + 1 + \"px\";\n      expand.scrollLeft = expand.scrollWidth;\n      expand.scrollTop = expand.scrollHeight;\n    };\n    checkTriggers = function (element) {\n      return element.offsetWidth !== element.__resizeLast__.width || element.offsetHeight !== element.__resizeLast__.height;\n    };\n    scrollListener = function (e) {\n      // Don't measure (which forces) reflow for scrolls that happen inside of children!\n      if (e.target.className && typeof e.target.className.indexOf === \"function\" && e.target.className.indexOf(\"contract-trigger\") < 0 && e.target.className.indexOf(\"expand-trigger\") < 0) {\n        return;\n      }\n      const element = this;\n      resetTriggers(this);\n      if (this.__resizeRAF__) {\n        cancelFrame(this.__resizeRAF__);\n      }\n      this.__resizeRAF__ = requestFrame(function animationFrame() {\n        if (checkTriggers(element)) {\n          element.__resizeLast__.width = element.offsetWidth;\n          element.__resizeLast__.height = element.offsetHeight;\n          element.__resizeListeners__.forEach(function forEachResizeListener(fn) {\n            fn.call(element, e);\n          });\n        }\n      });\n    };\n\n    /* Detect CSS Animations support to detect element display/re-attach */\n    let animation = false;\n    let keyframeprefix = \"\";\n    animationStartEvent = \"animationstart\";\n    const domPrefixes = \"Webkit Moz O ms\".split(\" \");\n    let startEvents = \"webkitAnimationStart animationstart oAnimationStart MSAnimationStart\".split(\" \");\n    let pfx = \"\";\n    {\n      const elm = document.createElement(\"fakeelement\");\n      if (elm.style.animationName !== undefined) {\n        animation = true;\n      }\n      if (animation === false) {\n        for (let i = 0; i < domPrefixes.length; i++) {\n          if (elm.style[domPrefixes[i] + \"AnimationName\"] !== undefined) {\n            pfx = domPrefixes[i];\n            keyframeprefix = \"-\" + pfx.toLowerCase() + \"-\";\n            animationStartEvent = startEvents[i];\n            animation = true;\n            break;\n          }\n        }\n      }\n    }\n    animationName = \"resizeanim\";\n    animationKeyframes = \"@\" + keyframeprefix + \"keyframes \" + animationName + \" { from { opacity: 0; } to { opacity: 0; } } \";\n    animationStyle = keyframeprefix + \"animation: 1ms \" + animationName + \"; \";\n  }\n  const createStyles = function (doc) {\n    if (!doc.getElementById(\"detectElementResize\")) {\n      //opacity:0 works around a chrome bug https://code.google.com/p/chromium/issues/detail?id=286360\n      const css = (animationKeyframes ? animationKeyframes : \"\") + \".resize-triggers { \" + (animationStyle ? animationStyle : \"\") + \"visibility: hidden; opacity: 0; } \" + '.resize-triggers, .resize-triggers > div, .contract-trigger:before { content: \" \"; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',\n        head = doc.head || doc.getElementsByTagName(\"head\")[0],\n        style = doc.createElement(\"style\");\n      style.id = \"detectElementResize\";\n      style.type = \"text/css\";\n      if (nonce != null) {\n        style.setAttribute(\"nonce\", nonce);\n      }\n      if (style.styleSheet) {\n        style.styleSheet.cssText = css;\n      } else {\n        style.appendChild(doc.createTextNode(css));\n      }\n      head.appendChild(style);\n    }\n  };\n  const addResizeListener = function (element, fn) {\n    if (attachEvent) {\n      element.attachEvent(\"onresize\", fn);\n    } else {\n      if (!element.__resizeTriggers__) {\n        const doc = element.ownerDocument;\n        const elementStyle = windowObject.getComputedStyle(element);\n        if (elementStyle && elementStyle.position === \"static\") {\n          element.style.position = \"relative\";\n        }\n        createStyles(doc);\n        element.__resizeLast__ = {};\n        element.__resizeListeners__ = [];\n        (element.__resizeTriggers__ = doc.createElement(\"div\")).className = \"resize-triggers\";\n        const expandTrigger = doc.createElement(\"div\");\n        expandTrigger.className = \"expand-trigger\";\n        expandTrigger.appendChild(doc.createElement(\"div\"));\n        const contractTrigger = doc.createElement(\"div\");\n        contractTrigger.className = \"contract-trigger\";\n        element.__resizeTriggers__.appendChild(expandTrigger);\n        element.__resizeTriggers__.appendChild(contractTrigger);\n        element.appendChild(element.__resizeTriggers__);\n        resetTriggers(element);\n        element.addEventListener(\"scroll\", scrollListener, true);\n\n        /* Listen for a css animation to detect element display/re-attach */\n        if (animationStartEvent) {\n          element.__resizeTriggers__.__animationListener__ = function animationListener(e) {\n            if (e.animationName === animationName) {\n              resetTriggers(element);\n            }\n          };\n          element.__resizeTriggers__.addEventListener(animationStartEvent, element.__resizeTriggers__.__animationListener__);\n        }\n      }\n      element.__resizeListeners__.push(fn);\n    }\n  };\n  const removeResizeListener = function (element, fn) {\n    if (attachEvent) {\n      element.detachEvent(\"onresize\", fn);\n    } else {\n      element.__resizeListeners__.splice(element.__resizeListeners__.indexOf(fn), 1);\n      if (!element.__resizeListeners__.length) {\n        element.removeEventListener(\"scroll\", scrollListener, true);\n        if (element.__resizeTriggers__.__animationListener__) {\n          element.__resizeTriggers__.removeEventListener(animationStartEvent, element.__resizeTriggers__.__animationListener__);\n          element.__resizeTriggers__.__animationListener__ = null;\n        }\n        try {\n          element.__resizeTriggers__ = !element.removeChild(element.__resizeTriggers__);\n        } catch (e) {\n          // Preact compat; see developit/preact-compat/issues/228\n        }\n      }\n    }\n  };\n  return {\n    addResizeListener,\n    removeResizeListener\n  };\n}\n\nclass AutoSizer extends Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      height: this.props.defaultHeight || 0,\n      width: this.props.defaultWidth || 0\n    };\n    this._autoSizer = null;\n    this._detectElementResize = null;\n    this._didLogDeprecationWarning = false;\n    this._parentNode = null;\n    this._resizeObserver = null;\n    this._timeoutId = null;\n    this._onResize = () => {\n      this._timeoutId = null;\n      const {\n        disableHeight,\n        disableWidth,\n        onResize\n      } = this.props;\n      if (this._parentNode) {\n        // Guard against AutoSizer component being removed from the DOM immediately after being added.\n        // This can result in invalid style values which can result in NaN values if we don't handle them.\n        // See issue #150 for more context.\n\n        const style = window.getComputedStyle(this._parentNode) || {};\n        const paddingLeft = parseFloat(style.paddingLeft || \"0\");\n        const paddingRight = parseFloat(style.paddingRight || \"0\");\n        const paddingTop = parseFloat(style.paddingTop || \"0\");\n        const paddingBottom = parseFloat(style.paddingBottom || \"0\");\n        const rect = this._parentNode.getBoundingClientRect();\n        const height = rect.height - paddingTop - paddingBottom;\n        const width = rect.width - paddingLeft - paddingRight;\n        if (!disableHeight && this.state.height !== height || !disableWidth && this.state.width !== width) {\n          this.setState({\n            height,\n            width\n          });\n          const maybeLogDeprecationWarning = () => {\n            if (!this._didLogDeprecationWarning) {\n              this._didLogDeprecationWarning = true;\n              console.warn(\"scaledWidth and scaledHeight parameters have been deprecated; use width and height instead\");\n            }\n          };\n          if (typeof onResize === \"function\") {\n            onResize({\n              height,\n              width,\n              // TODO Remove these params in the next major release\n              get scaledHeight() {\n                maybeLogDeprecationWarning();\n                return height;\n              },\n              get scaledWidth() {\n                maybeLogDeprecationWarning();\n                return width;\n              }\n            });\n          }\n        }\n      }\n    };\n    this._setRef = autoSizer => {\n      this._autoSizer = autoSizer;\n    };\n  }\n  componentDidMount() {\n    const {\n      nonce\n    } = this.props;\n    const parentNode = this._autoSizer ? this._autoSizer.parentNode : null;\n    if (parentNode != null && parentNode.ownerDocument && parentNode.ownerDocument.defaultView && parentNode instanceof parentNode.ownerDocument.defaultView.HTMLElement) {\n      // Delay access of parentNode until mount.\n      // This handles edge-cases where the component has already been unmounted before its ref has been set,\n      // As well as libraries like react-lite which have a slightly different lifecycle.\n      this._parentNode = parentNode;\n\n      // Use ResizeObserver from the same context where parentNode (which we will observe) was defined\n      // Using just global can result into onResize events not being emitted in cases with multiple realms\n      const ResizeObserverInstance = parentNode.ownerDocument.defaultView.ResizeObserver;\n      if (ResizeObserverInstance != null) {\n        this._resizeObserver = new ResizeObserverInstance(() => {\n          // Guard against \"ResizeObserver loop limit exceeded\" error;\n          // could be triggered if the state update causes the ResizeObserver handler to run long.\n          // See https://github.com/bvaughn/react-virtualized-auto-sizer/issues/55\n          this._timeoutId = setTimeout(this._onResize, 0);\n        });\n        this._resizeObserver.observe(parentNode);\n      } else {\n        // Defer requiring resize handler in order to support server-side rendering.\n        // See issue #41\n        this._detectElementResize = createDetectElementResize(nonce);\n        this._detectElementResize.addResizeListener(parentNode, this._onResize);\n      }\n      this._onResize();\n    }\n  }\n  componentWillUnmount() {\n    if (this._parentNode) {\n      if (this._detectElementResize) {\n        this._detectElementResize.removeResizeListener(this._parentNode, this._onResize);\n      }\n      if (this._timeoutId !== null) {\n        clearTimeout(this._timeoutId);\n      }\n      if (this._resizeObserver) {\n        this._resizeObserver.disconnect();\n      }\n    }\n  }\n  render() {\n    const {\n      children,\n      defaultHeight,\n      defaultWidth,\n      disableHeight = false,\n      disableWidth = false,\n      doNotBailOutOnEmptyChildren = false,\n      nonce,\n      onResize,\n      style = {},\n      tagName = \"div\",\n      ...rest\n    } = this.props;\n    const {\n      height,\n      width\n    } = this.state;\n\n    // Outer div should not force width/height since that may prevent containers from shrinking.\n    // Inner component should overflow and use calculated width/height.\n    // See issue #68 for more information.\n    const outerStyle = {\n      overflow: \"visible\"\n    };\n    const childParams = {};\n\n    // Avoid rendering children before the initial measurements have been collected.\n    // At best this would just be wasting cycles.\n    let bailoutOnChildren = false;\n    if (!disableHeight) {\n      if (height === 0) {\n        bailoutOnChildren = true;\n      }\n      outerStyle.height = 0;\n      childParams.height = height;\n\n      // TODO Remove this in the next major release\n      childParams.scaledHeight = height;\n    }\n    if (!disableWidth) {\n      if (width === 0) {\n        bailoutOnChildren = true;\n      }\n      outerStyle.width = 0;\n      childParams.width = width;\n\n      // TODO Remove this in the next major release\n      childParams.scaledWidth = width;\n    }\n    if (doNotBailOutOnEmptyChildren) {\n      bailoutOnChildren = false;\n    }\n    return createElement(tagName, {\n      ref: this._setRef,\n      style: {\n        ...outerStyle,\n        ...style\n      },\n      ...rest\n    }, !bailoutOnChildren && children(childParams));\n  }\n}\n\nfunction isHeightAndWidthProps(props) {\n  return props && props.disableHeight !== true && props.disableWidth !== true;\n}\nfunction isHeightOnlyProps(props) {\n  return props && props.disableHeight !== true && props.disableWidth === true;\n}\nfunction isWidthOnlyProps(props) {\n  return props && props.disableHeight === true && props.disableWidth !== true;\n}\n\nexport { AutoSizer as default, isHeightAndWidthProps, isHeightOnlyProps, isWidthOnlyProps };\n"], "mappings": ";;;;;;;;AAAA,mBAAyC;AAgBzC,IAAI;AACJ,IAAI,OAAO,WAAW,aAAa;AACjC,iBAAe;AAGjB,WAAW,OAAO,SAAS,aAAa;AAEtC,iBAAe;AACjB,OAAO;AACL,iBAAe;AACjB;AACA,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAM,mBAAmB;AACzB,IAAM,iBAAiB,aAAa;AACpC,IAAM,eAAe,aAAa;AAClC,IAAM,yBAAyB,aAAa,wBAAwB,aAAa,2BAA2B,aAAa;AACzH,IAAM,0BAA0B,aAAa,yBAAyB,aAAa,4BAA4B,aAAa;AAC5H,IAAI,0BAA0B,QAAQ,2BAA2B,MAAM;AAGrE,gBAAc;AACd,iBAAe,SAAS,mCAAmC,UAAU;AACnE,WAAO,aAAa,UAAU,gBAAgB;AAAA,EAChD;AACF,OAAO;AAIL,gBAAc,SAASA,aAAY,CAAC,kBAAkB,SAAS,GAAG;AAChE,2BAAuB,gBAAgB;AACvC,mBAAe,SAAS;AAAA,EAC1B;AACA,iBAAe,SAAS,4CAA4C,UAAU;AAC5E,UAAM,mBAAmB,wBAAwB,SAAS,yBAAyB;AACjF,qBAAe,SAAS;AACxB,eAAS;AAAA,IACX,CAAC;AACD,UAAM,YAAY,aAAa,SAAS,kBAAkB;AACxD,6BAAuB,gBAAgB;AACvC,eAAS;AAAA,IACX,GAAG,gBAAgB;AACnB,WAAO,CAAC,kBAAkB,SAAS;AAAA,EACrC;AACF;AACA,SAAS,0BAA0B,OAAO;AACxC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,cAAc,OAAO,aAAa,eAAe,SAAS;AAChE,MAAI,CAAC,aAAa;AAChB,oBAAgB,SAAU,SAAS;AACjC,YAAM,WAAW,QAAQ,oBACvB,SAAS,SAAS,mBAClB,WAAW,SAAS,kBACpB,cAAc,OAAO;AACvB,eAAS,aAAa,SAAS;AAC/B,eAAS,YAAY,SAAS;AAC9B,kBAAY,MAAM,QAAQ,OAAO,cAAc,IAAI;AACnD,kBAAY,MAAM,SAAS,OAAO,eAAe,IAAI;AACrD,aAAO,aAAa,OAAO;AAC3B,aAAO,YAAY,OAAO;AAAA,IAC5B;AACA,oBAAgB,SAAU,SAAS;AACjC,aAAO,QAAQ,gBAAgB,QAAQ,eAAe,SAAS,QAAQ,iBAAiB,QAAQ,eAAe;AAAA,IACjH;AACA,qBAAiB,SAAU,GAAG;AAE5B,UAAI,EAAE,OAAO,aAAa,OAAO,EAAE,OAAO,UAAU,YAAY,cAAc,EAAE,OAAO,UAAU,QAAQ,kBAAkB,IAAI,KAAK,EAAE,OAAO,UAAU,QAAQ,gBAAgB,IAAI,GAAG;AACpL;AAAA,MACF;AACA,YAAM,UAAU;AAChB,oBAAc,IAAI;AAClB,UAAI,KAAK,eAAe;AACtB,oBAAY,KAAK,aAAa;AAAA,MAChC;AACA,WAAK,gBAAgB,aAAa,SAAS,iBAAiB;AAC1D,YAAI,cAAc,OAAO,GAAG;AAC1B,kBAAQ,eAAe,QAAQ,QAAQ;AACvC,kBAAQ,eAAe,SAAS,QAAQ;AACxC,kBAAQ,oBAAoB,QAAQ,SAAS,sBAAsB,IAAI;AACrE,eAAG,KAAK,SAAS,CAAC;AAAA,UACpB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAGA,QAAI,YAAY;AAChB,QAAI,iBAAiB;AACrB,0BAAsB;AACtB,UAAM,cAAc,kBAAkB,MAAM,GAAG;AAC/C,QAAI,cAAc,uEAAuE,MAAM,GAAG;AAClG,QAAI,MAAM;AACV;AACE,YAAM,MAAM,SAAS,cAAc,aAAa;AAChD,UAAI,IAAI,MAAM,kBAAkB,QAAW;AACzC,oBAAY;AAAA,MACd;AACA,UAAI,cAAc,OAAO;AACvB,iBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,cAAI,IAAI,MAAM,YAAY,CAAC,IAAI,eAAe,MAAM,QAAW;AAC7D,kBAAM,YAAY,CAAC;AACnB,6BAAiB,MAAM,IAAI,YAAY,IAAI;AAC3C,kCAAsB,YAAY,CAAC;AACnC,wBAAY;AACZ;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,oBAAgB;AAChB,yBAAqB,MAAM,iBAAiB,eAAe,gBAAgB;AAC3E,qBAAiB,iBAAiB,oBAAoB,gBAAgB;AAAA,EACxE;AACA,QAAM,eAAe,SAAU,KAAK;AAClC,QAAI,CAAC,IAAI,eAAe,qBAAqB,GAAG;AAE9C,YAAM,OAAO,qBAAqB,qBAAqB,MAAM,yBAAyB,iBAAiB,iBAAiB,MAAM,8VAC5H,OAAO,IAAI,QAAQ,IAAI,qBAAqB,MAAM,EAAE,CAAC,GACrD,QAAQ,IAAI,cAAc,OAAO;AACnC,YAAM,KAAK;AACX,YAAM,OAAO;AACb,UAAI,SAAS,MAAM;AACjB,cAAM,aAAa,SAAS,KAAK;AAAA,MACnC;AACA,UAAI,MAAM,YAAY;AACpB,cAAM,WAAW,UAAU;AAAA,MAC7B,OAAO;AACL,cAAM,YAAY,IAAI,eAAe,GAAG,CAAC;AAAA,MAC3C;AACA,WAAK,YAAY,KAAK;AAAA,IACxB;AAAA,EACF;AACA,QAAM,oBAAoB,SAAU,SAAS,IAAI;AAC/C,QAAI,aAAa;AACf,cAAQ,YAAY,YAAY,EAAE;AAAA,IACpC,OAAO;AACL,UAAI,CAAC,QAAQ,oBAAoB;AAC/B,cAAM,MAAM,QAAQ;AACpB,cAAM,eAAe,aAAa,iBAAiB,OAAO;AAC1D,YAAI,gBAAgB,aAAa,aAAa,UAAU;AACtD,kBAAQ,MAAM,WAAW;AAAA,QAC3B;AACA,qBAAa,GAAG;AAChB,gBAAQ,iBAAiB,CAAC;AAC1B,gBAAQ,sBAAsB,CAAC;AAC/B,SAAC,QAAQ,qBAAqB,IAAI,cAAc,KAAK,GAAG,YAAY;AACpE,cAAM,gBAAgB,IAAI,cAAc,KAAK;AAC7C,sBAAc,YAAY;AAC1B,sBAAc,YAAY,IAAI,cAAc,KAAK,CAAC;AAClD,cAAM,kBAAkB,IAAI,cAAc,KAAK;AAC/C,wBAAgB,YAAY;AAC5B,gBAAQ,mBAAmB,YAAY,aAAa;AACpD,gBAAQ,mBAAmB,YAAY,eAAe;AACtD,gBAAQ,YAAY,QAAQ,kBAAkB;AAC9C,sBAAc,OAAO;AACrB,gBAAQ,iBAAiB,UAAU,gBAAgB,IAAI;AAGvD,YAAI,qBAAqB;AACvB,kBAAQ,mBAAmB,wBAAwB,SAAS,kBAAkB,GAAG;AAC/E,gBAAI,EAAE,kBAAkB,eAAe;AACrC,4BAAc,OAAO;AAAA,YACvB;AAAA,UACF;AACA,kBAAQ,mBAAmB,iBAAiB,qBAAqB,QAAQ,mBAAmB,qBAAqB;AAAA,QACnH;AAAA,MACF;AACA,cAAQ,oBAAoB,KAAK,EAAE;AAAA,IACrC;AAAA,EACF;AACA,QAAM,uBAAuB,SAAU,SAAS,IAAI;AAClD,QAAI,aAAa;AACf,cAAQ,YAAY,YAAY,EAAE;AAAA,IACpC,OAAO;AACL,cAAQ,oBAAoB,OAAO,QAAQ,oBAAoB,QAAQ,EAAE,GAAG,CAAC;AAC7E,UAAI,CAAC,QAAQ,oBAAoB,QAAQ;AACvC,gBAAQ,oBAAoB,UAAU,gBAAgB,IAAI;AAC1D,YAAI,QAAQ,mBAAmB,uBAAuB;AACpD,kBAAQ,mBAAmB,oBAAoB,qBAAqB,QAAQ,mBAAmB,qBAAqB;AACpH,kBAAQ,mBAAmB,wBAAwB;AAAA,QACrD;AACA,YAAI;AACF,kBAAQ,qBAAqB,CAAC,QAAQ,YAAY,QAAQ,kBAAkB;AAAA,QAC9E,SAAS,GAAG;AAAA,QAEZ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAN,cAAwB,uBAAU;AAAA,EAChC,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,SAAK,QAAQ;AAAA,MACX,QAAQ,KAAK,MAAM,iBAAiB;AAAA,MACpC,OAAO,KAAK,MAAM,gBAAgB;AAAA,IACpC;AACA,SAAK,aAAa;AAClB,SAAK,uBAAuB;AAC5B,SAAK,4BAA4B;AACjC,SAAK,cAAc;AACnB,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAClB,SAAK,YAAY,MAAM;AACrB,WAAK,aAAa;AAClB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,UAAI,KAAK,aAAa;AAKpB,cAAM,QAAQ,OAAO,iBAAiB,KAAK,WAAW,KAAK,CAAC;AAC5D,cAAM,cAAc,WAAW,MAAM,eAAe,GAAG;AACvD,cAAM,eAAe,WAAW,MAAM,gBAAgB,GAAG;AACzD,cAAM,aAAa,WAAW,MAAM,cAAc,GAAG;AACrD,cAAM,gBAAgB,WAAW,MAAM,iBAAiB,GAAG;AAC3D,cAAM,OAAO,KAAK,YAAY,sBAAsB;AACpD,cAAM,SAAS,KAAK,SAAS,aAAa;AAC1C,cAAM,QAAQ,KAAK,QAAQ,cAAc;AACzC,YAAI,CAAC,iBAAiB,KAAK,MAAM,WAAW,UAAU,CAAC,gBAAgB,KAAK,MAAM,UAAU,OAAO;AACjG,eAAK,SAAS;AAAA,YACZ;AAAA,YACA;AAAA,UACF,CAAC;AACD,gBAAM,6BAA6B,MAAM;AACvC,gBAAI,CAAC,KAAK,2BAA2B;AACnC,mBAAK,4BAA4B;AACjC,sBAAQ,KAAK,4FAA4F;AAAA,YAC3G;AAAA,UACF;AACA,cAAI,OAAO,aAAa,YAAY;AAClC,qBAAS;AAAA,cACP;AAAA,cACA;AAAA;AAAA,cAEA,IAAI,eAAe;AACjB,2CAA2B;AAC3B,uBAAO;AAAA,cACT;AAAA,cACA,IAAI,cAAc;AAChB,2CAA2B;AAC3B,uBAAO;AAAA,cACT;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,UAAU,eAAa;AAC1B,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,KAAK;AACT,UAAM,aAAa,KAAK,aAAa,KAAK,WAAW,aAAa;AAClE,QAAI,cAAc,QAAQ,WAAW,iBAAiB,WAAW,cAAc,eAAe,sBAAsB,WAAW,cAAc,YAAY,aAAa;AAIpK,WAAK,cAAc;AAInB,YAAM,yBAAyB,WAAW,cAAc,YAAY;AACpE,UAAI,0BAA0B,MAAM;AAClC,aAAK,kBAAkB,IAAI,uBAAuB,MAAM;AAItD,eAAK,aAAa,WAAW,KAAK,WAAW,CAAC;AAAA,QAChD,CAAC;AACD,aAAK,gBAAgB,QAAQ,UAAU;AAAA,MACzC,OAAO;AAGL,aAAK,uBAAuB,0BAA0B,KAAK;AAC3D,aAAK,qBAAqB,kBAAkB,YAAY,KAAK,SAAS;AAAA,MACxE;AACA,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,QAAI,KAAK,aAAa;AACpB,UAAI,KAAK,sBAAsB;AAC7B,aAAK,qBAAqB,qBAAqB,KAAK,aAAa,KAAK,SAAS;AAAA,MACjF;AACA,UAAI,KAAK,eAAe,MAAM;AAC5B,qBAAa,KAAK,UAAU;AAAA,MAC9B;AACA,UAAI,KAAK,iBAAiB;AACxB,aAAK,gBAAgB,WAAW;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,8BAA8B;AAAA,MAC9B;AAAA,MACA;AAAA,MACA,QAAQ,CAAC;AAAA,MACT,UAAU;AAAA,MACV,GAAG;AAAA,IACL,IAAI,KAAK;AACT,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AAKT,UAAM,aAAa;AAAA,MACjB,UAAU;AAAA,IACZ;AACA,UAAM,cAAc,CAAC;AAIrB,QAAI,oBAAoB;AACxB,QAAI,CAAC,eAAe;AAClB,UAAI,WAAW,GAAG;AAChB,4BAAoB;AAAA,MACtB;AACA,iBAAW,SAAS;AACpB,kBAAY,SAAS;AAGrB,kBAAY,eAAe;AAAA,IAC7B;AACA,QAAI,CAAC,cAAc;AACjB,UAAI,UAAU,GAAG;AACf,4BAAoB;AAAA,MACtB;AACA,iBAAW,QAAQ;AACnB,kBAAY,QAAQ;AAGpB,kBAAY,cAAc;AAAA,IAC5B;AACA,QAAI,6BAA6B;AAC/B,0BAAoB;AAAA,IACtB;AACA,eAAO,4BAAc,SAAS;AAAA,MAC5B,KAAK,KAAK;AAAA,MACV,OAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MACA,GAAG;AAAA,IACL,GAAG,CAAC,qBAAqB,SAAS,WAAW,CAAC;AAAA,EAChD;AACF;AAEA,SAAS,sBAAsB,OAAO;AACpC,SAAO,SAAS,MAAM,kBAAkB,QAAQ,MAAM,iBAAiB;AACzE;AACA,SAAS,kBAAkB,OAAO;AAChC,SAAO,SAAS,MAAM,kBAAkB,QAAQ,MAAM,iBAAiB;AACzE;AACA,SAAS,iBAAiB,OAAO;AAC/B,SAAO,SAAS,MAAM,kBAAkB,QAAQ,MAAM,iBAAiB;AACzE;", "names": ["cancelFrame"]}