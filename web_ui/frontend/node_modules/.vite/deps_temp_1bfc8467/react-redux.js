import {
  Provider_default,
  ReactReduxContext,
  connect_default,
  createDispatchHook,
  createSelectorHook,
  createStoreHook,
  import_react_dom,
  shallowEqual,
  useDispatch,
  useSelector,
  useStore
} from "./chunk-L5TUDAP5.js";
import "./chunk-WALXKXZM.js";
import "./chunk-SNXB62YR.js";
import "./chunk-WQMOH32Y.js";
import "./chunk-5WWUZCGV.js";
var export_batch = import_react_dom.unstable_batchedUpdates;
export {
  Provider_default as Provider,
  ReactReduxContext,
  export_batch as batch,
  connect_default as connect,
  createDispatchHook,
  createSelectorHook,
  createStoreHook,
  shallowEqual,
  useDispatch,
  useSelector,
  useStore
};
//# sourceMappingURL=react-redux.js.map
