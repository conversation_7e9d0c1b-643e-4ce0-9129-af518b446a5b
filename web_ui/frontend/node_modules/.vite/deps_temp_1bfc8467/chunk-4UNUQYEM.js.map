{"version": 3, "sources": ["../../@reduxjs/toolkit/src/query/core/apiState.ts", "../../@reduxjs/toolkit/src/query/utils/isAbsoluteUrl.ts", "../../@reduxjs/toolkit/src/query/utils/joinUrls.ts", "../../@reduxjs/toolkit/src/query/utils/flatten.ts", "../../@reduxjs/toolkit/src/query/utils/isOnline.ts", "../../@reduxjs/toolkit/src/query/utils/isDocumentVisible.ts", "../../@reduxjs/toolkit/src/query/utils/copyWithStructuralSharing.ts", "../../@reduxjs/toolkit/src/query/fetchBaseQuery.ts", "../../@reduxjs/toolkit/src/query/HandledError.ts", "../../@reduxjs/toolkit/src/query/retry.ts", "../../@reduxjs/toolkit/src/query/core/setupListeners.ts", "../../@reduxjs/toolkit/src/query/core/buildSelectors.ts", "../../@reduxjs/toolkit/src/query/endpointDefinitions.ts", "../../@reduxjs/toolkit/src/query/core/buildSlice.ts", "../../@reduxjs/toolkit/src/query/utils/isNotNullish.ts", "../../@reduxjs/toolkit/src/query/core/buildInitiate.ts", "../../@reduxjs/toolkit/src/query/core/buildThunks.ts", "../../@reduxjs/toolkit/src/query/defaultSerializeQueryArgs.ts", "../../@reduxjs/toolkit/src/query/createApi.ts", "../../@reduxjs/toolkit/src/query/fakeBaseQuery.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/index.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/cacheCollection.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/invalidationByTags.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/polling.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/windowEventHandling.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/cacheLifecycle.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/queryLifecycle.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/devMiddleware.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/batchActions.ts", "../../@reduxjs/toolkit/src/query/tsHelpers.ts", "../../@reduxjs/toolkit/src/query/core/module.ts", "../../@reduxjs/toolkit/src/query/core/index.ts", "../../@reduxjs/toolkit/dist/query/rtk-query.esm.js"], "sourcesContent": ["import type { SerializedError } from '@reduxjs/toolkit'\r\nimport type { BaseQueryError } from '../baseQueryTypes'\r\nimport type {\r\n  QueryDefinition,\r\n  MutationDefinition,\r\n  EndpointDefinitions,\r\n  BaseEndpointDefinition,\r\n  ResultTypeFrom,\r\n  QueryArgFrom,\r\n} from '../endpointDefinitions'\r\nimport type { Id, WithRequiredProp } from '../tsHelpers'\r\n\r\nexport type QueryCacheKey = string & { _type: 'queryCacheKey' }\r\nexport type QuerySubstateIdentifier = { queryCacheKey: QueryCacheKey }\r\nexport type MutationSubstateIdentifier =\r\n  | {\r\n      requestId: string\r\n      fixedCacheKey?: string\r\n    }\r\n  | {\r\n      requestId?: string\r\n      fixedCacheKey: string\r\n    }\r\n\r\nexport type RefetchConfigOptions = {\r\n  refetchOnMountOrArgChange: boolean | number\r\n  refetchOnReconnect: boolean\r\n  refetchOnFocus: boolean\r\n}\r\n\r\n/**\r\n * Strings describing the query state at any given time.\r\n */\r\nexport enum QueryStatus {\r\n  uninitialized = 'uninitialized',\r\n  pending = 'pending',\r\n  fulfilled = 'fulfilled',\r\n  rejected = 'rejected',\r\n}\r\n\r\nexport type RequestStatusFlags =\r\n  | {\r\n      status: QueryStatus.uninitialized\r\n      isUninitialized: true\r\n      isLoading: false\r\n      isSuccess: false\r\n      isError: false\r\n    }\r\n  | {\r\n      status: QueryStatus.pending\r\n      isUninitialized: false\r\n      isLoading: true\r\n      isSuccess: false\r\n      isError: false\r\n    }\r\n  | {\r\n      status: QueryStatus.fulfilled\r\n      isUninitialized: false\r\n      isLoading: false\r\n      isSuccess: true\r\n      isError: false\r\n    }\r\n  | {\r\n      status: QueryStatus.rejected\r\n      isUninitialized: false\r\n      isLoading: false\r\n      isSuccess: false\r\n      isError: true\r\n    }\r\n\r\nexport function getRequestStatusFlags(status: QueryStatus): RequestStatusFlags {\r\n  return {\r\n    status,\r\n    isUninitialized: status === QueryStatus.uninitialized,\r\n    isLoading: status === QueryStatus.pending,\r\n    isSuccess: status === QueryStatus.fulfilled,\r\n    isError: status === QueryStatus.rejected,\r\n  } as any\r\n}\r\n\r\nexport type SubscriptionOptions = {\r\n  /**\r\n   * How frequently to automatically re-fetch data (in milliseconds). Defaults to `0` (off).\r\n   */\r\n  pollingInterval?: number\r\n  /**\r\n   * Defaults to `false`. This setting allows you to control whether RTK Query will try to refetch all subscribed queries after regaining a network connection.\r\n   *\r\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\r\n   *\r\n   * Note: requires [`setupListeners`](./setupListeners) to have been called.\r\n   */\r\n  refetchOnReconnect?: boolean\r\n  /**\r\n   * Defaults to `false`. This setting allows you to control whether RTK Query will try to refetch all subscribed queries after the application window regains focus.\r\n   *\r\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\r\n   *\r\n   * Note: requires [`setupListeners`](./setupListeners) to have been called.\r\n   */\r\n  refetchOnFocus?: boolean\r\n}\r\nexport type Subscribers = { [requestId: string]: SubscriptionOptions }\r\nexport type QueryKeys<Definitions extends EndpointDefinitions> = {\r\n  [K in keyof Definitions]: Definitions[K] extends QueryDefinition<\r\n    any,\r\n    any,\r\n    any,\r\n    any\r\n  >\r\n    ? K\r\n    : never\r\n}[keyof Definitions]\r\nexport type MutationKeys<Definitions extends EndpointDefinitions> = {\r\n  [K in keyof Definitions]: Definitions[K] extends MutationDefinition<\r\n    any,\r\n    any,\r\n    any,\r\n    any\r\n  >\r\n    ? K\r\n    : never\r\n}[keyof Definitions]\r\n\r\ntype BaseQuerySubState<D extends BaseEndpointDefinition<any, any, any>> = {\r\n  /**\r\n   * The argument originally passed into the hook or `initiate` action call\r\n   */\r\n  originalArgs: QueryArgFrom<D>\r\n  /**\r\n   * A unique ID associated with the request\r\n   */\r\n  requestId: string\r\n  /**\r\n   * The received data from the query\r\n   */\r\n  data?: ResultTypeFrom<D>\r\n  /**\r\n   * The received error if applicable\r\n   */\r\n  error?:\r\n    | SerializedError\r\n    | (D extends QueryDefinition<any, infer BaseQuery, any, any>\r\n        ? BaseQueryError<BaseQuery>\r\n        : never)\r\n  /**\r\n   * The name of the endpoint associated with the query\r\n   */\r\n  endpointName: string\r\n  /**\r\n   * Time that the latest query started\r\n   */\r\n  startedTimeStamp: number\r\n  /**\r\n   * Time that the latest query was fulfilled\r\n   */\r\n  fulfilledTimeStamp?: number\r\n}\r\n\r\nexport type QuerySubState<D extends BaseEndpointDefinition<any, any, any>> = Id<\r\n  | ({\r\n      status: QueryStatus.fulfilled\r\n    } & WithRequiredProp<\r\n      BaseQuerySubState<D>,\r\n      'data' | 'fulfilledTimeStamp'\r\n    > & { error: undefined })\r\n  | ({\r\n      status: QueryStatus.pending\r\n    } & BaseQuerySubState<D>)\r\n  | ({\r\n      status: QueryStatus.rejected\r\n    } & WithRequiredProp<BaseQuerySubState<D>, 'error'>)\r\n  | {\r\n      status: QueryStatus.uninitialized\r\n      originalArgs?: undefined\r\n      data?: undefined\r\n      error?: undefined\r\n      requestId?: undefined\r\n      endpointName?: string\r\n      startedTimeStamp?: undefined\r\n      fulfilledTimeStamp?: undefined\r\n    }\r\n>\r\n\r\ntype BaseMutationSubState<D extends BaseEndpointDefinition<any, any, any>> = {\r\n  requestId: string\r\n  data?: ResultTypeFrom<D>\r\n  error?:\r\n    | SerializedError\r\n    | (D extends MutationDefinition<any, infer BaseQuery, any, any>\r\n        ? BaseQueryError<BaseQuery>\r\n        : never)\r\n  endpointName: string\r\n  startedTimeStamp: number\r\n  fulfilledTimeStamp?: number\r\n}\r\n\r\nexport type MutationSubState<D extends BaseEndpointDefinition<any, any, any>> =\r\n  | (({\r\n      status: QueryStatus.fulfilled\r\n    } & WithRequiredProp<\r\n      BaseMutationSubState<D>,\r\n      'data' | 'fulfilledTimeStamp'\r\n    >) & { error: undefined })\r\n  | (({\r\n      status: QueryStatus.pending\r\n    } & BaseMutationSubState<D>) & { data?: undefined })\r\n  | ({\r\n      status: QueryStatus.rejected\r\n    } & WithRequiredProp<BaseMutationSubState<D>, 'error'>)\r\n  | {\r\n      requestId?: undefined\r\n      status: QueryStatus.uninitialized\r\n      data?: undefined\r\n      error?: undefined\r\n      endpointName?: string\r\n      startedTimeStamp?: undefined\r\n      fulfilledTimeStamp?: undefined\r\n    }\r\n\r\nexport type CombinedState<\r\n  D extends EndpointDefinitions,\r\n  E extends string,\r\n  ReducerPath extends string\r\n> = {\r\n  queries: QueryState<D>\r\n  mutations: MutationState<D>\r\n  provided: InvalidationState<E>\r\n  subscriptions: SubscriptionState\r\n  config: ConfigState<ReducerPath>\r\n}\r\n\r\nexport type InvalidationState<TagTypes extends string> = {\r\n  [_ in TagTypes]: {\r\n    [id: string]: Array<QueryCacheKey>\r\n    [id: number]: Array<QueryCacheKey>\r\n  }\r\n}\r\n\r\nexport type QueryState<D extends EndpointDefinitions> = {\r\n  [queryCacheKey: string]: QuerySubState<D[string]> | undefined\r\n}\r\n\r\nexport type SubscriptionState = {\r\n  [queryCacheKey: string]: Subscribers | undefined\r\n}\r\n\r\nexport type ConfigState<ReducerPath> = RefetchConfigOptions & {\r\n  reducerPath: ReducerPath\r\n  online: boolean\r\n  focused: boolean\r\n  middlewareRegistered: boolean | 'conflict'\r\n} & ModifiableConfigState\r\n\r\nexport type ModifiableConfigState = {\r\n  keepUnusedDataFor: number\r\n} & RefetchConfigOptions\r\n\r\nexport type MutationState<D extends EndpointDefinitions> = {\r\n  [requestId: string]: MutationSubState<D[string]> | undefined\r\n}\r\n\r\nexport type RootState<\r\n  Definitions extends EndpointDefinitions,\r\n  TagTypes extends string,\r\n  ReducerPath extends string\r\n> = {\r\n  [P in ReducerPath]: CombinedState<Definitions, TagTypes, P>\r\n}\r\n", "/**\r\n * If either :// or // is present consider it to be an absolute url\r\n *\r\n * @param url string\r\n */\r\n\r\nexport function isAbsoluteUrl(url: string) {\r\n  return new RegExp(`(^|:)//`).test(url)\r\n}\r\n", "import { isAbsoluteUrl } from './isAbsoluteUrl'\r\n\r\nconst withoutTrailingSlash = (url: string) => url.replace(/\\/$/, '')\r\nconst withoutLeadingSlash = (url: string) => url.replace(/^\\//, '')\r\n\r\nexport function joinUrls(\r\n  base: string | undefined,\r\n  url: string | undefined\r\n): string {\r\n  if (!base) {\r\n    return url!\r\n  }\r\n  if (!url) {\r\n    return base\r\n  }\r\n\r\n  if (isAbsoluteUrl(url)) {\r\n    return url\r\n  }\r\n\r\n  const delimiter = base.endsWith('/') || !url.startsWith('?') ? '/' : ''\r\n  base = withoutTrailingSlash(base)\r\n  url = withoutLeadingSlash(url)\r\n\r\n  return `${base}${delimiter}${url}`;\r\n}\r\n", "/**\r\n * Alternative to `Array.flat(1)`\r\n * @param arr An array like [1,2,3,[1,2]]\r\n * @link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/flat\r\n */\r\nexport const flatten = (arr: readonly any[]) => [].concat(...arr)\r\n", "/**\r\n * Assumes a browser is online if `undefined`, otherwise makes a best effort\r\n * @link https://developer.mozilla.org/en-US/docs/Web/API/NavigatorOnLine/onLine\r\n */\r\nexport function isOnline() {\r\n  // We set the default config value in the store, so we'd need to check for this in a SSR env\r\n  return typeof navigator === 'undefined'\r\n    ? true\r\n    : navigator.onLine === undefined\r\n    ? true\r\n    : navigator.onLine\r\n}\r\n", "/**\r\n * Assumes true for a non-browser env, otherwise makes a best effort\r\n * @link https://developer.mozilla.org/en-US/docs/Web/API/Document/visibilityState\r\n */\r\nexport function isDocumentVisible(): boolean {\r\n  // `document` may not exist in non-browser envs (like RN)\r\n  if (typeof document === 'undefined') {\r\n    return true\r\n  }\r\n  // Match true for visible, prerender, undefined\r\n  return document.visibilityState !== 'hidden'\r\n}\r\n", "import { isPlainObject as _iPO } from '@reduxjs/toolkit'\r\n\r\n// remove type guard\r\nconst isPlainObject: (_: any) => boolean = _iPO\r\n\r\nexport function copyWithStructuralSharing<T>(oldObj: any, newObj: T): T\r\nexport function copyWithStructuralSharing(oldObj: any, newObj: any): any {\r\n  if (\r\n    oldObj === newObj ||\r\n    !(\r\n      (isPlainObject(oldObj) && isPlainObject(newObj)) ||\r\n      (Array.isArray(oldObj) && Array.isArray(newObj))\r\n    )\r\n  ) {\r\n    return newObj\r\n  }\r\n  const newKeys = Object.keys(newObj)\r\n  const oldKeys = Object.keys(oldObj)\r\n\r\n  let isSameObject = newKeys.length === oldKeys.length\r\n  const mergeObj: any = Array.isArray(newObj) ? [] : {}\r\n  for (const key of newKeys) {\r\n    mergeObj[key] = copyWithStructuralSharing(oldObj[key], newObj[key])\r\n    if (isSameObject) isSameObject = oldObj[key] === mergeObj[key]\r\n  }\r\n  return isSameObject ? oldObj : mergeObj\r\n}\r\n", "import { joinUrls } from './utils'\r\nimport { isPlainObject } from '@reduxjs/toolkit'\r\nimport type { BaseQueryApi, BaseQueryFn } from './baseQueryTypes'\r\nimport type { MaybePromise, Override } from './tsHelpers'\r\n\r\nexport type ResponseHandler =\r\n  | 'content-type'\r\n  | 'json'\r\n  | 'text'\r\n  | ((response: Response) => Promise<any>)\r\n\r\ntype CustomRequestInit = Override<\r\n  RequestInit,\r\n  {\r\n    headers?:\r\n      | Headers\r\n      | string[][]\r\n      | Record<string, string | undefined>\r\n      | undefined\r\n  }\r\n>\r\n\r\nexport interface FetchArgs extends CustomRequestInit {\r\n  url: string\r\n  params?: Record<string, any>\r\n  body?: any\r\n  responseHandler?: ResponseHandler\r\n  validateStatus?: (response: Response, body: any) => boolean\r\n  /**\r\n   * A number in milliseconds that represents that maximum time a request can take before timing out.\r\n   */\r\n  timeout?: number\r\n}\r\n\r\n/**\r\n * A mini-wrapper that passes arguments straight through to\r\n * {@link [fetch](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API)}.\r\n * Avoids storing `fetch` in a closure, in order to permit mocking/monkey-patching.\r\n */\r\nconst defaultFetchFn: typeof fetch = (...args) => fetch(...args)\r\n\r\nconst defaultValidateStatus = (response: Response) =>\r\n  response.status >= 200 && response.status <= 299\r\n\r\nconst defaultIsJsonContentType = (headers: Headers) =>\r\n  /*applicat*/ /ion\\/(vnd\\.api\\+)?json/.test(headers.get('content-type') || '')\r\n\r\nexport type FetchBaseQueryError =\r\n  | {\r\n      /**\r\n       * * `number`:\r\n       *   HTTP status code\r\n       */\r\n      status: number\r\n      data: unknown\r\n    }\r\n  | {\r\n      /**\r\n       * * `\"FETCH_ERROR\"`:\r\n       *   An error that occurred during execution of `fetch` or the `fetchFn` callback option\r\n       **/\r\n      status: 'FETCH_ERROR'\r\n      data?: undefined\r\n      error: string\r\n    }\r\n  | {\r\n      /**\r\n       * * `\"PARSING_ERROR\"`:\r\n       *   An error happened during parsing.\r\n       *   Most likely a non-JSON-response was returned with the default `responseHandler` \"JSON\",\r\n       *   or an error occurred while executing a custom `responseHandler`.\r\n       **/\r\n      status: 'PARSING_ERROR'\r\n      originalStatus: number\r\n      data: string\r\n      error: string\r\n    }\r\n  | {\r\n      /**\r\n       * * `\"TIMEOUT_ERROR\"`:\r\n       *   Request timed out\r\n       **/\r\n      status: 'TIMEOUT_ERROR'\r\n      data?: undefined\r\n      error: string\r\n    }\r\n  | {\r\n      /**\r\n       * * `\"CUSTOM_ERROR\"`:\r\n       *   A custom error type that you can return from your `queryFn` where another error might not make sense.\r\n       **/\r\n      status: 'CUSTOM_ERROR'\r\n      data?: unknown\r\n      error: string\r\n    }\r\n\r\nfunction stripUndefined(obj: any) {\r\n  if (!isPlainObject(obj)) {\r\n    return obj\r\n  }\r\n  const copy: Record<string, any> = { ...obj }\r\n  for (const [k, v] of Object.entries(copy)) {\r\n    if (v === undefined) delete copy[k]\r\n  }\r\n  return copy\r\n}\r\n\r\nexport type FetchBaseQueryArgs = {\r\n  baseUrl?: string\r\n  prepareHeaders?: (\r\n    headers: Headers,\r\n    api: Pick<\r\n      BaseQueryApi,\r\n      'getState' | 'extra' | 'endpoint' | 'type' | 'forced'\r\n    >\r\n  ) => MaybePromise<Headers | void>\r\n  fetchFn?: (\r\n    input: RequestInfo,\r\n    init?: RequestInit | undefined\r\n  ) => Promise<Response>\r\n  paramsSerializer?: (params: Record<string, any>) => string\r\n  /**\r\n   * By default, we only check for 'application/json' and 'application/vnd.api+json' as the content-types for json. If you need to support another format, you can pass\r\n   * in a predicate function for your given api to get the same automatic stringifying behavior\r\n   * @example\r\n   * ```ts\r\n   * const isJsonContentType = (headers: Headers) => [\"application/vnd.api+json\", \"application/json\", \"application/vnd.hal+json\"].includes(headers.get(\"content-type\")?.trim());\r\n   * ```\r\n   */\r\n  isJsonContentType?: (headers: Headers) => boolean\r\n  /**\r\n   * Defaults to `application/json`;\r\n   */\r\n  jsonContentType?: string\r\n\r\n  /**\r\n   * Custom replacer function used when calling `JSON.stringify()`;\r\n   */\r\n  jsonReplacer?: (this: any, key: string, value: any) => any\r\n} & RequestInit &\r\n  Pick<FetchArgs, 'responseHandler' | 'validateStatus' | 'timeout'>\r\n\r\nexport type FetchBaseQueryMeta = { request: Request; response?: Response }\r\n\r\n/**\r\n * This is a very small wrapper around fetch that aims to simplify requests.\r\n *\r\n * @example\r\n * ```ts\r\n * const baseQuery = fetchBaseQuery({\r\n *   baseUrl: 'https://api.your-really-great-app.com/v1/',\r\n *   prepareHeaders: (headers, { getState }) => {\r\n *     const token = (getState() as RootState).auth.token;\r\n *     // If we have a token set in state, let's assume that we should be passing it.\r\n *     if (token) {\r\n *       headers.set('authorization', `Bearer ${token}`);\r\n *     }\r\n *     return headers;\r\n *   },\r\n * })\r\n * ```\r\n *\r\n * @param {string} baseUrl\r\n * The base URL for an API service.\r\n * Typically in the format of https://example.com/\r\n *\r\n * @param {(headers: Headers, api: { getState: () => unknown; extra: unknown; endpoint: string; type: 'query' | 'mutation'; forced: boolean; }) => Headers} prepareHeaders\r\n * An optional function that can be used to inject headers on requests.\r\n * Provides a Headers object, as well as most of the `BaseQueryApi` (`dispatch` is not available).\r\n * Useful for setting authentication or headers that need to be set conditionally.\r\n *\r\n * @link https://developer.mozilla.org/en-US/docs/Web/API/Headers\r\n *\r\n * @param {(input: RequestInfo, init?: RequestInit | undefined) => Promise<Response>} fetchFn\r\n * Accepts a custom `fetch` function if you do not want to use the default on the window.\r\n * Useful in SSR environments if you need to use a library such as `isomorphic-fetch` or `cross-fetch`\r\n *\r\n * @param {(params: Record<string, unknown>) => string} paramsSerializer\r\n * An optional function that can be used to stringify querystring parameters.\r\n *\r\n * @param {(headers: Headers) => boolean} isJsonContentType\r\n * An optional predicate function to determine if `JSON.stringify()` should be called on the `body` arg of `FetchArgs`\r\n *\r\n * @param {string} jsonContentType Used when automatically setting the content-type header for a request with a jsonifiable body that does not have an explicit content-type header. Defaults to `application/json`.\r\n *\r\n * @param {(this: any, key: string, value: any) => any} jsonReplacer Custom replacer function used when calling `JSON.stringify()`.\r\n *\r\n * @param {number} timeout\r\n * A number in milliseconds that represents the maximum time a request can take before timing out.\r\n */\r\nexport function fetchBaseQuery({\r\n  baseUrl,\r\n  prepareHeaders = (x) => x,\r\n  fetchFn = defaultFetchFn,\r\n  paramsSerializer,\r\n  isJsonContentType = defaultIsJsonContentType,\r\n  jsonContentType = 'application/json',\r\n  jsonReplacer,\r\n  timeout: defaultTimeout,\r\n  responseHandler: globalResponseHandler,\r\n  validateStatus: globalValidateStatus,\r\n  ...baseFetchOptions\r\n}: FetchBaseQueryArgs = {}): BaseQueryFn<\r\n  string | FetchArgs,\r\n  unknown,\r\n  FetchBaseQueryError,\r\n  {},\r\n  FetchBaseQueryMeta\r\n> {\r\n  if (typeof fetch === 'undefined' && fetchFn === defaultFetchFn) {\r\n    console.warn(\r\n      'Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments.'\r\n    )\r\n  }\r\n  return async (arg, api) => {\r\n    const { signal, getState, extra, endpoint, forced, type } = api\r\n    let meta: FetchBaseQueryMeta | undefined\r\n    let {\r\n      url,\r\n      headers = new Headers(baseFetchOptions.headers),\r\n      params = undefined,\r\n      responseHandler = globalResponseHandler ?? ('json' as const),\r\n      validateStatus = globalValidateStatus ?? defaultValidateStatus,\r\n      timeout = defaultTimeout,\r\n      ...rest\r\n    } = typeof arg == 'string' ? { url: arg } : arg\r\n    let config: RequestInit = {\r\n      ...baseFetchOptions,\r\n      signal,\r\n      ...rest,\r\n    }\r\n\r\n    headers = new Headers(stripUndefined(headers))\r\n    config.headers =\r\n      (await prepareHeaders(headers, {\r\n        getState,\r\n        extra,\r\n        endpoint,\r\n        forced,\r\n        type,\r\n      })) || headers\r\n\r\n    // Only set the content-type to json if appropriate. Will not be true for FormData, ArrayBuffer, Blob, etc.\r\n    const isJsonifiable = (body: any) =>\r\n      typeof body === 'object' &&\r\n      (isPlainObject(body) ||\r\n        Array.isArray(body) ||\r\n        typeof body.toJSON === 'function')\r\n\r\n    if (!config.headers.has('content-type') && isJsonifiable(config.body)) {\r\n      config.headers.set('content-type', jsonContentType)\r\n    }\r\n\r\n    if (isJsonifiable(config.body) && isJsonContentType(config.headers)) {\r\n      config.body = JSON.stringify(config.body, jsonReplacer)\r\n    }\r\n\r\n    if (params) {\r\n      const divider = ~url.indexOf('?') ? '&' : '?'\r\n      const query = paramsSerializer\r\n        ? paramsSerializer(params)\r\n        : new URLSearchParams(stripUndefined(params))\r\n      url += divider + query\r\n    }\r\n\r\n    url = joinUrls(baseUrl, url)\r\n\r\n    const request = new Request(url, config)\r\n    const requestClone = new Request(url, config)\r\n    meta = { request: requestClone }\r\n\r\n    let response,\r\n      timedOut = false,\r\n      timeoutId =\r\n        timeout &&\r\n        setTimeout(() => {\r\n          timedOut = true\r\n          api.abort()\r\n        }, timeout)\r\n    try {\r\n      response = await fetchFn(request)\r\n    } catch (e) {\r\n      return {\r\n        error: {\r\n          status: timedOut ? 'TIMEOUT_ERROR' : 'FETCH_ERROR',\r\n          error: String(e),\r\n        },\r\n        meta,\r\n      }\r\n    } finally {\r\n      if (timeoutId) clearTimeout(timeoutId)\r\n    }\r\n    const responseClone = response.clone()\r\n\r\n    meta.response = responseClone\r\n\r\n    let resultData: any\r\n    let responseText: string = ''\r\n    try {\r\n      let handleResponseError\r\n      await Promise.all([\r\n        handleResponse(response, responseHandler).then(\r\n          (r) => (resultData = r),\r\n          (e) => (handleResponseError = e)\r\n        ),\r\n        // see https://github.com/node-fetch/node-fetch/issues/665#issuecomment-538995182\r\n        // we *have* to \"use up\" both streams at the same time or they will stop running in node-fetch scenarios\r\n        responseClone.text().then(\r\n          (r) => (responseText = r),\r\n          () => {}\r\n        ),\r\n      ])\r\n      if (handleResponseError) throw handleResponseError\r\n    } catch (e) {\r\n      return {\r\n        error: {\r\n          status: 'PARSING_ERROR',\r\n          originalStatus: response.status,\r\n          data: responseText,\r\n          error: String(e),\r\n        },\r\n        meta,\r\n      }\r\n    }\r\n\r\n    return validateStatus(response, resultData)\r\n      ? {\r\n          data: resultData,\r\n          meta,\r\n        }\r\n      : {\r\n          error: {\r\n            status: response.status,\r\n            data: resultData,\r\n          },\r\n          meta,\r\n        }\r\n  }\r\n\r\n  async function handleResponse(\r\n    response: Response,\r\n    responseHandler: ResponseHandler\r\n  ) {\r\n    if (typeof responseHandler === 'function') {\r\n      return responseHandler(response)\r\n    }\r\n\r\n    if (responseHandler === 'content-type') {\r\n      responseHandler = isJsonContentType(response.headers) ? 'json' : 'text'\r\n    }\r\n\r\n    if (responseHandler === 'json') {\r\n      const text = await response.text()\r\n      return text.length ? JSON.parse(text) : null\r\n    }\r\n\r\n    return response.text()\r\n  }\r\n}\r\n", "export class HandledError {\r\n  constructor(\r\n    public readonly value: any,\r\n    public readonly meta: any = undefined\r\n  ) {}\r\n}\r\n", "import type {\r\n  BaseQueryApi,\r\n  BaseQueryArg,\r\n  BaseQueryEnhancer,\r\n  BaseQueryExtraOptions,\r\n  BaseQueryFn,\r\n} from './baseQueryTypes'\r\nimport type { FetchBaseQueryError } from './fetchBaseQuery'\r\nimport { HandledError } from './HandledError'\r\n\r\n/**\r\n * Exponential backoff based on the attempt number.\r\n *\r\n * @remarks\r\n * 1. 600ms * random(0.4, 1.4)\r\n * 2. 1200ms * random(0.4, 1.4)\r\n * 3. 2400ms * random(0.4, 1.4)\r\n * 4. 4800ms * random(0.4, 1.4)\r\n * 5. 9600ms * random(0.4, 1.4)\r\n *\r\n * @param attempt - Current attempt\r\n * @param maxRetries - Maximum number of retries\r\n */\r\nasync function defaultBackoff(attempt: number = 0, maxRetries: number = 5) {\r\n  const attempts = Math.min(attempt, maxRetries)\r\n\r\n  const timeout = ~~((Math.random() + 0.4) * (300 << attempts)) // Force a positive int in the case we make this an option\r\n  await new Promise((resolve) =>\r\n    setTimeout((res: any) => resolve(res), timeout)\r\n  )\r\n}\r\n\r\ntype RetryConditionFunction = (\r\n  error: FetchBaseQueryError,\r\n  args: BaseQueryArg<BaseQueryFn>,\r\n  extraArgs: {\r\n    attempt: number\r\n    baseQueryApi: BaseQueryApi\r\n    extraOptions: BaseQueryExtraOptions<BaseQueryFn> & RetryOptions\r\n  }\r\n) => boolean\r\n\r\nexport type RetryOptions = {\r\n  /**\r\n   * Function used to determine delay between retries\r\n   */\r\n  backoff?: (attempt: number, maxRetries: number) => Promise<void>\r\n} & (\r\n  | {\r\n      /**\r\n       * How many times the query will be retried (default: 5)\r\n       */\r\n      maxRetries?: number\r\n      retryCondition?: undefined\r\n    }\r\n  | {\r\n      /**\r\n       * Callback to determine if a retry should be attempted.\r\n       * Return `true` for another retry and `false` to quit trying prematurely.\r\n       */\r\n      retryCondition?: RetryConditionFunction\r\n      maxRetries?: undefined\r\n    }\r\n)\r\n\r\nfunction fail(e: any): never {\r\n  throw Object.assign(new HandledError({ error: e }), {\r\n    throwImmediately: true,\r\n  })\r\n}\r\n\r\nconst EMPTY_OPTIONS = {}\r\n\r\nconst retryWithBackoff: BaseQueryEnhancer<\r\n  unknown,\r\n  RetryOptions,\r\n  RetryOptions | void\r\n> = (baseQuery, defaultOptions) => async (args, api, extraOptions) => {\r\n  // We need to figure out `maxRetries` before we define `defaultRetryCondition.\r\n  // This is probably goofy, but ought to work.\r\n  // Put our defaults in one array, filter out undefineds, grab the last value.\r\n  const possibleMaxRetries: number[] = [\r\n    5,\r\n    ((defaultOptions as any) || EMPTY_OPTIONS).maxRetries,\r\n    ((extraOptions as any) || EMPTY_OPTIONS).maxRetries,\r\n  ].filter(x => x !== undefined)\r\n  const [maxRetries] = possibleMaxRetries.slice(-1)\r\n\r\n  const defaultRetryCondition: RetryConditionFunction = (_, __, { attempt }) =>\r\n    attempt <= maxRetries\r\n\r\n  const options: {\r\n    maxRetries: number\r\n    backoff: typeof defaultBackoff\r\n    retryCondition: typeof defaultRetryCondition\r\n  } = {\r\n    maxRetries,\r\n    backoff: defaultBackoff,\r\n    retryCondition: defaultRetryCondition,\r\n    ...defaultOptions,\r\n    ...extraOptions,\r\n  }\r\n  let retry = 0\r\n\r\n  while (true) {\r\n    try {\r\n      const result = await baseQuery(args, api, extraOptions)\r\n      // baseQueries _should_ return an error property, so we should check for that and throw it to continue retrying\r\n      if (result.error) {\r\n        throw new HandledError(result)\r\n      }\r\n      return result\r\n    } catch (e: any) {\r\n      retry++\r\n\r\n      if (e.throwImmediately) {\r\n        if (e instanceof HandledError) {\r\n          return e.value\r\n        }\r\n\r\n        // We don't know what this is, so we have to rethrow it\r\n        throw e\r\n      }\r\n\r\n      if (\r\n        e instanceof HandledError &&\r\n        !options.retryCondition(e.value.error as FetchBaseQueryError, args, {\r\n          attempt: retry,\r\n          baseQueryApi: api,\r\n          extraOptions,\r\n        })\r\n      ) {\r\n        return e.value\r\n      }\r\n      await options.backoff(retry, options.maxRetries)\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * A utility that can wrap `baseQuery` in the API definition to provide retries with a basic exponential backoff.\r\n *\r\n * @example\r\n *\r\n * ```ts\r\n * // codeblock-meta title=\"Retry every request 5 times by default\"\r\n * import { createApi, fetchBaseQuery, retry } from '@reduxjs/toolkit/query/react'\r\n * interface Post {\r\n *   id: number\r\n *   name: string\r\n * }\r\n * type PostsResponse = Post[]\r\n *\r\n * // maxRetries: 5 is the default, and can be omitted. Shown for documentation purposes.\r\n * const staggeredBaseQuery = retry(fetchBaseQuery({ baseUrl: '/' }), { maxRetries: 5 });\r\n * export const api = createApi({\r\n *   baseQuery: staggeredBaseQuery,\r\n *   endpoints: (build) => ({\r\n *     getPosts: build.query<PostsResponse, void>({\r\n *       query: () => ({ url: 'posts' }),\r\n *     }),\r\n *     getPost: build.query<PostsResponse, string>({\r\n *       query: (id) => ({ url: `post/${id}` }),\r\n *       extraOptions: { maxRetries: 8 }, // You can override the retry behavior on each endpoint\r\n *     }),\r\n *   }),\r\n * });\r\n *\r\n * export const { useGetPostsQuery, useGetPostQuery } = api;\r\n * ```\r\n */\r\nexport const retry = /* @__PURE__ */ Object.assign(retryWithBackoff, { fail })\r\n", "import type {\r\n  ThunkDispatch,\r\n  ActionCreatorWithoutPayload, // Workaround for API-Extractor\r\n} from '@reduxjs/toolkit'\r\nimport { createAction } from '@reduxjs/toolkit'\r\n\r\nexport const onFocus = /* @__PURE__ */ createAction('__rtkq/focused')\r\nexport const onFocusLost = /* @__PURE__ */ createAction('__rtkq/unfocused')\r\nexport const onOnline = /* @__PURE__ */ createAction('__rtkq/online')\r\nexport const onOffline = /* @__PURE__ */ createAction('__rtkq/offline')\r\n\r\nlet initialized = false\r\n\r\n/**\r\n * A utility used to enable `refetchOnMount` and `refetchOnReconnect` behaviors.\r\n * It requires the dispatch method from your store.\r\n * Calling `setupListeners(store.dispatch)` will configure listeners with the recommended defaults,\r\n * but you have the option of providing a callback for more granular control.\r\n *\r\n * @example\r\n * ```ts\r\n * setupListeners(store.dispatch)\r\n * ```\r\n *\r\n * @param dispatch - The dispatch method from your store\r\n * @param customHandler - An optional callback for more granular control over listener behavior\r\n * @returns Return value of the handler.\r\n * The default handler returns an `unsubscribe` method that can be called to remove the listeners.\r\n */\r\nexport function setupListeners(\r\n  dispatch: ThunkDispatch<any, any, any>,\r\n  customHandler?: (\r\n    dispatch: ThunkDispatch<any, any, any>,\r\n    actions: {\r\n      onFocus: typeof onFocus\r\n      onFocusLost: typeof onFocusLost\r\n      onOnline: typeof onOnline\r\n      onOffline: typeof onOffline\r\n    }\r\n  ) => () => void\r\n) {\r\n  function defaultHandler() {\r\n    const handleFocus = () => dispatch(onFocus())\r\n    const handleFocusLost = () => dispatch(onFocusLost())\r\n    const handleOnline = () => dispatch(onOnline())\r\n    const handleOffline = () => dispatch(onOffline())\r\n    const handleVisibilityChange = () => {\r\n      if (window.document.visibilityState === 'visible') {\r\n        handleFocus()\r\n      } else {\r\n        handleFocusLost()\r\n      }\r\n    }\r\n\r\n    if (!initialized) {\r\n      if (typeof window !== 'undefined' && window.addEventListener) {\r\n        // Handle focus events\r\n        window.addEventListener(\r\n          'visibilitychange',\r\n          handleVisibilityChange,\r\n          false\r\n        )\r\n        window.addEventListener('focus', handleFocus, false)\r\n\r\n        // Handle connection events\r\n        window.addEventListener('online', handleOnline, false)\r\n        window.addEventListener('offline', handleOffline, false)\r\n        initialized = true\r\n      }\r\n    }\r\n    const unsubscribe = () => {\r\n      window.removeEventListener('focus', handleFocus)\r\n      window.removeEventListener('visibilitychange', handleVisibilityChange)\r\n      window.removeEventListener('online', handleOnline)\r\n      window.removeEventListener('offline', handleOffline)\r\n      initialized = false\r\n    }\r\n    return unsubscribe\r\n  }\r\n\r\n  return customHandler\r\n    ? customHandler(dispatch, { onFocus, onFocusLost, onOffline, onOnline })\r\n    : defaultHandler()\r\n}\r\n", "import { createNextState, createSelector } from '@reduxjs/toolkit'\r\nimport type {\r\n  MutationSubState,\r\n  QuerySubState,\r\n  RootState as _RootState,\r\n  RequestStatusFlags,\r\n  QueryCacheKey,\r\n} from './apiState'\r\nimport { QueryStatus, getRequestStatusFlags } from './apiState'\r\nimport type {\r\n  EndpointDefinitions,\r\n  QueryDefinition,\r\n  MutationDefinition,\r\n  QueryArgFrom,\r\n  TagTypesFrom,\r\n  ReducerPathFrom,\r\n  TagDescription,\r\n} from '../endpointDefinitions'\r\nimport { expandTagDescription } from '../endpointDefinitions'\r\nimport type { InternalSerializeQueryArgs } from '../defaultSerializeQueryArgs'\r\nimport { getMutationCacheKey } from './buildSlice'\r\nimport { flatten } from '../utils'\r\n\r\nexport type SkipToken = typeof skipToken\r\n/**\r\n * Can be passed into `useQuery`, `useQueryState` or `useQuerySubscription`\r\n * instead of the query argument to get the same effect as if setting\r\n * `skip: true` in the query options.\r\n *\r\n * Useful for scenarios where a query should be skipped when `arg` is `undefined`\r\n * and <PERSON><PERSON> complains about it because `arg` is not allowed to be passed\r\n * in as `undefined`, such as\r\n *\r\n * ```ts\r\n * // codeblock-meta title=\"will error if the query argument is not allowed to be undefined\" no-transpile\r\n * useSomeQuery(arg, { skip: !!arg })\r\n * ```\r\n *\r\n * ```ts\r\n * // codeblock-meta title=\"using skipToken instead\" no-transpile\r\n * useSomeQuery(arg ?? skipToken)\r\n * ```\r\n *\r\n * If passed directly into a query or mutation selector, that selector will always\r\n * return an uninitialized state.\r\n */\r\nexport const skipToken = /* @__PURE__ */ Symbol.for('RTKQ/skipToken')\r\n/** @deprecated renamed to `skipToken` */\r\nexport const skipSelector = skipToken\r\n\r\ndeclare module './module' {\r\n  export interface ApiEndpointQuery<\r\n    Definition extends QueryDefinition<any, any, any, any, any>,\r\n    Definitions extends EndpointDefinitions\r\n  > {\r\n    select: QueryResultSelectorFactory<\r\n      Definition,\r\n      _RootState<\r\n        Definitions,\r\n        TagTypesFrom<Definition>,\r\n        ReducerPathFrom<Definition>\r\n      >\r\n    >\r\n  }\r\n\r\n  export interface ApiEndpointMutation<\r\n    Definition extends MutationDefinition<any, any, any, any, any>,\r\n    Definitions extends EndpointDefinitions\r\n  > {\r\n    select: MutationResultSelectorFactory<\r\n      Definition,\r\n      _RootState<\r\n        Definitions,\r\n        TagTypesFrom<Definition>,\r\n        ReducerPathFrom<Definition>\r\n      >\r\n    >\r\n  }\r\n}\r\n\r\ntype QueryResultSelectorFactory<\r\n  Definition extends QueryDefinition<any, any, any, any>,\r\n  RootState\r\n> = (\r\n  queryArg: QueryArgFrom<Definition> | SkipToken\r\n) => (state: RootState) => QueryResultSelectorResult<Definition>\r\n\r\nexport type QueryResultSelectorResult<\r\n  Definition extends QueryDefinition<any, any, any, any>\r\n> = QuerySubState<Definition> & RequestStatusFlags\r\n\r\ntype MutationResultSelectorFactory<\r\n  Definition extends MutationDefinition<any, any, any, any>,\r\n  RootState\r\n> = (\r\n  requestId:\r\n    | string\r\n    | { requestId: string | undefined; fixedCacheKey: string | undefined }\r\n    | SkipToken\r\n) => (state: RootState) => MutationResultSelectorResult<Definition>\r\n\r\nexport type MutationResultSelectorResult<\r\n  Definition extends MutationDefinition<any, any, any, any>\r\n> = MutationSubState<Definition> & RequestStatusFlags\r\n\r\nconst initialSubState: QuerySubState<any> = {\r\n  status: QueryStatus.uninitialized as const,\r\n}\r\n\r\n// abuse immer to freeze default states\r\nconst defaultQuerySubState = /* @__PURE__ */ createNextState(\r\n  initialSubState,\r\n  () => {}\r\n)\r\nconst defaultMutationSubState = /* @__PURE__ */ createNextState(\r\n  initialSubState as MutationSubState<any>,\r\n  () => {}\r\n)\r\n\r\nexport function buildSelectors<\r\n  Definitions extends EndpointDefinitions,\r\n  ReducerPath extends string\r\n>({\r\n  serializeQueryArgs,\r\n  reducerPath,\r\n}: {\r\n  serializeQueryArgs: InternalSerializeQueryArgs\r\n  reducerPath: ReducerPath\r\n}) {\r\n  type RootState = _RootState<Definitions, string, string>\r\n\r\n  const selectSkippedQuery = (state: RootState) => defaultQuerySubState\r\n  const selectSkippedMutation = (state: RootState) => defaultMutationSubState\r\n\r\n  return { buildQuerySelector, buildMutationSelector, selectInvalidatedBy }\r\n\r\n  function withRequestFlags<T extends { status: QueryStatus }>(\r\n    substate: T\r\n  ): T & RequestStatusFlags {\r\n    return {\r\n      ...substate,\r\n      ...getRequestStatusFlags(substate.status),\r\n    }\r\n  }\r\n\r\n  function selectInternalState(rootState: RootState) {\r\n    const state = rootState[reducerPath]\r\n    if (process.env.NODE_ENV !== 'production') {\r\n      if (!state) {\r\n        if ((selectInternalState as any).triggered) return state\r\n        ;(selectInternalState as any).triggered = true\r\n        console.error(\r\n          `Error: No data found at \\`state.${reducerPath}\\`. Did you forget to add the reducer to the store?`\r\n        )\r\n      }\r\n    }\r\n    return state\r\n  }\r\n\r\n  function buildQuerySelector(\r\n    endpointName: string,\r\n    endpointDefinition: QueryDefinition<any, any, any, any>\r\n  ) {\r\n    return ((queryArgs: any) => {\r\n      const serializedArgs = serializeQueryArgs({\r\n        queryArgs,\r\n        endpointDefinition,\r\n        endpointName,\r\n      })\r\n      const selectQuerySubstate = (state: RootState) =>\r\n        selectInternalState(state)?.queries?.[serializedArgs] ??\r\n        defaultQuerySubState\r\n      const finalSelectQuerySubState =\r\n        queryArgs === skipToken ? selectSkippedQuery : selectQuerySubstate\r\n\r\n      return createSelector(finalSelectQuerySubState, withRequestFlags)\r\n    }) as QueryResultSelectorFactory<any, RootState>\r\n  }\r\n\r\n  function buildMutationSelector() {\r\n    return ((id) => {\r\n      let mutationId: string | typeof skipToken\r\n      if (typeof id === 'object') {\r\n        mutationId = getMutationCacheKey(id) ?? skipToken\r\n      } else {\r\n        mutationId = id\r\n      }\r\n      const selectMutationSubstate = (state: RootState) =>\r\n        selectInternalState(state)?.mutations?.[mutationId as string] ??\r\n        defaultMutationSubState\r\n      const finalSelectMutationSubstate =\r\n        mutationId === skipToken\r\n          ? selectSkippedMutation\r\n          : selectMutationSubstate\r\n\r\n      return createSelector(finalSelectMutationSubstate, withRequestFlags)\r\n    }) as MutationResultSelectorFactory<any, RootState>\r\n  }\r\n\r\n  function selectInvalidatedBy(\r\n    state: RootState,\r\n    tags: ReadonlyArray<TagDescription<string>>\r\n  ): Array<{\r\n    endpointName: string\r\n    originalArgs: any\r\n    queryCacheKey: QueryCacheKey\r\n  }> {\r\n    const apiState = state[reducerPath]\r\n    const toInvalidate = new Set<QueryCacheKey>()\r\n    for (const tag of tags.map(expandTagDescription)) {\r\n      const provided = apiState.provided[tag.type]\r\n      if (!provided) {\r\n        continue\r\n      }\r\n\r\n      let invalidateSubscriptions =\r\n        (tag.id !== undefined\r\n          ? // id given: invalidate all queries that provide this type & id\r\n            provided[tag.id]\r\n          : // no id: invalidate all queries that provide this type\r\n            flatten(Object.values(provided))) ?? []\r\n\r\n      for (const invalidate of invalidateSubscriptions) {\r\n        toInvalidate.add(invalidate)\r\n      }\r\n    }\r\n\r\n    return flatten(\r\n      Array.from(toInvalidate.values()).map((queryCacheKey) => {\r\n        const querySubState = apiState.queries[queryCacheKey]\r\n        return querySubState\r\n          ? [\r\n              {\r\n                queryCacheKey,\r\n                endpointName: querySubState.endpointName!,\r\n                originalArgs: querySubState.originalArgs,\r\n              },\r\n            ]\r\n          : []\r\n      })\r\n    )\r\n  }\r\n}\r\n", "import type { AnyAction, ThunkDispatch } from '@reduxjs/toolkit'\r\nimport type { SerializeQueryArgs } from './defaultSerializeQueryArgs'\r\nimport type { QuerySubState, RootState } from './core/apiState'\r\nimport type {\r\n  BaseQueryExtraOptions,\r\n  BaseQueryFn,\r\n  BaseQueryResult,\r\n  BaseQueryArg,\r\n  BaseQueryApi,\r\n  QueryReturnValue,\r\n  BaseQueryError,\r\n  BaseQueryMeta,\r\n} from './baseQueryTypes'\r\nimport type {\r\n  HasRequiredProps,\r\n  MaybePromise,\r\n  OmitFromUnion,\r\n  CastAny,\r\n  NonUndefined,\r\n  UnwrapPromise,\r\n} from './tsHelpers'\r\nimport type { NEVER } from './fakeBaseQuery'\r\nimport type { Api } from '@reduxjs/toolkit/query'\r\n\r\nconst resultType = /* @__PURE__ */ Symbol()\r\nconst baseQuery = /* @__PURE__ */ Symbol()\r\n\r\ninterface EndpointDefinitionWithQuery<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ResultType\r\n> {\r\n  /**\r\n   * `query` can be a function that returns either a `string` or an `object` which is passed to your `baseQuery`. If you are using [fetchBaseQuery](./fetchBaseQuery), this can return either a `string` or an `object` of properties in `FetchArgs`. If you use your own custom [`baseQuery`](../../rtk-query/usage/customizing-queries), you can customize this behavior to your liking.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"query example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   tagTypes: ['Post'],\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       // highlight-start\r\n   *       query: () => 'posts',\r\n   *       // highlight-end\r\n   *     }),\r\n   *     addPost: build.mutation<Post, Partial<Post>>({\r\n   *      // highlight-start\r\n   *      query: (body) => ({\r\n   *        url: `posts`,\r\n   *        method: 'POST',\r\n   *        body,\r\n   *      }),\r\n   *      // highlight-end\r\n   *      invalidatesTags: [{ type: 'Post', id: 'LIST' }],\r\n   *    }),\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\r\n  query(arg: QueryArg): BaseQueryArg<BaseQuery>\r\n  queryFn?: never\r\n  /**\r\n   * A function to manipulate the data returned by a query or mutation.\r\n   */\r\n  transformResponse?(\r\n    baseQueryReturnValue: BaseQueryResult<BaseQuery>,\r\n    meta: BaseQueryMeta<BaseQuery>,\r\n    arg: QueryArg\r\n  ): ResultType | Promise<ResultType>\r\n  /**\r\n   * A function to manipulate the data returned by a failed query or mutation.\r\n   */\r\n  transformErrorResponse?(\r\n    baseQueryReturnValue: BaseQueryError<BaseQuery>,\r\n    meta: BaseQueryMeta<BaseQuery>,\r\n    arg: QueryArg\r\n  ): unknown\r\n  /**\r\n   * Defaults to `true`.\r\n   *\r\n   * Most apps should leave this setting on. The only time it can be a performance issue\r\n   * is if an API returns extremely large amounts of data (e.g. 10,000 rows per request) and\r\n   * you're unable to paginate it.\r\n   *\r\n   * For details of how this works, please see the below. When it is set to `false`,\r\n   * every request will cause subscribed components to rerender, even when the data has not changed.\r\n   *\r\n   * @see https://redux-toolkit.js.org/api/other-exports#copywithstructuralsharing\r\n   */\r\n  structuralSharing?: boolean\r\n}\r\n\r\ninterface EndpointDefinitionWithQueryFn<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ResultType\r\n> {\r\n  /**\r\n   * Can be used in place of `query` as an inline function that bypasses `baseQuery` completely for the endpoint.\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Basic queryFn example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *     }),\r\n   *     flipCoin: build.query<'heads' | 'tails', void>({\r\n   *       // highlight-start\r\n   *       queryFn(arg, queryApi, extraOptions, baseQuery) {\r\n   *         const randomVal = Math.random()\r\n   *         if (randomVal < 0.45) {\r\n   *           return { data: 'heads' }\r\n   *         }\r\n   *         if (randomVal < 0.9) {\r\n   *           return { data: 'tails' }\r\n   *         }\r\n   *         return { error: { status: 500, statusText: 'Internal Server Error', data: \"Coin landed on it's edge!\" } }\r\n   *       }\r\n   *       // highlight-end\r\n   *     })\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\r\n  queryFn(\r\n    arg: QueryArg,\r\n    api: BaseQueryApi,\r\n    extraOptions: BaseQueryExtraOptions<BaseQuery>,\r\n    baseQuery: (arg: Parameters<BaseQuery>[0]) => ReturnType<BaseQuery>\r\n  ): MaybePromise<QueryReturnValue<ResultType, BaseQueryError<BaseQuery>>>\r\n  query?: never\r\n  transformResponse?: never\r\n  transformErrorResponse?: never\r\n  /**\r\n   * Defaults to `true`.\r\n   *\r\n   * Most apps should leave this setting on. The only time it can be a performance issue\r\n   * is if an API returns extremely large amounts of data (e.g. 10,000 rows per request) and\r\n   * you're unable to paginate it.\r\n   *\r\n   * For details of how this works, please see the below. When it is set to `false`,\r\n   * every request will cause subscribed components to rerender, even when the data has not changed.\r\n   *\r\n   * @see https://redux-toolkit.js.org/api/other-exports#copywithstructuralsharing\r\n   */\r\n  structuralSharing?: boolean\r\n}\r\n\r\nexport interface BaseEndpointTypes<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ResultType\r\n> {\r\n  QueryArg: QueryArg\r\n  BaseQuery: BaseQuery\r\n  ResultType: ResultType\r\n}\r\n\r\nexport type BaseEndpointDefinition<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ResultType\r\n> = (\r\n  | ([CastAny<BaseQueryResult<BaseQuery>, {}>] extends [NEVER]\r\n      ? never\r\n      : EndpointDefinitionWithQuery<QueryArg, BaseQuery, ResultType>)\r\n  | EndpointDefinitionWithQueryFn<QueryArg, BaseQuery, ResultType>\r\n) & {\r\n  /* phantom type */\r\n  [resultType]?: ResultType\r\n  /* phantom type */\r\n  [baseQuery]?: BaseQuery\r\n} & HasRequiredProps<\r\n    BaseQueryExtraOptions<BaseQuery>,\r\n    { extraOptions: BaseQueryExtraOptions<BaseQuery> },\r\n    { extraOptions?: BaseQueryExtraOptions<BaseQuery> }\r\n  >\r\n\r\nexport enum DefinitionType {\r\n  query = 'query',\r\n  mutation = 'mutation',\r\n}\r\n\r\nexport type GetResultDescriptionFn<\r\n  TagTypes extends string,\r\n  ResultType,\r\n  QueryArg,\r\n  ErrorType,\r\n  MetaType\r\n> = (\r\n  result: ResultType | undefined,\r\n  error: ErrorType | undefined,\r\n  arg: QueryArg,\r\n  meta: MetaType\r\n) => ReadonlyArray<TagDescription<TagTypes>>\r\n\r\nexport type FullTagDescription<TagType> = {\r\n  type: TagType\r\n  id?: number | string\r\n}\r\nexport type TagDescription<TagType> = TagType | FullTagDescription<TagType>\r\nexport type ResultDescription<\r\n  TagTypes extends string,\r\n  ResultType,\r\n  QueryArg,\r\n  ErrorType,\r\n  MetaType\r\n> =\r\n  | ReadonlyArray<TagDescription<TagTypes>>\r\n  | GetResultDescriptionFn<TagTypes, ResultType, QueryArg, ErrorType, MetaType>\r\n\r\n/** @deprecated please use `onQueryStarted` instead */\r\nexport interface QueryApi<ReducerPath extends string, Context extends {}> {\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  dispatch: ThunkDispatch<any, any, AnyAction>\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  getState(): RootState<any, any, ReducerPath>\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  extra: unknown\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  requestId: string\r\n  /** @deprecated please use `onQueryStarted` instead */\r\n  context: Context\r\n}\r\n\r\nexport interface QueryTypes<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> extends BaseEndpointTypes<QueryArg, BaseQuery, ResultType> {\r\n  /**\r\n   * The endpoint definition type. To be used with some internal generic types.\r\n   * @example\r\n   * ```ts\r\n   * const useMyWrappedHook: UseQuery<typeof api.endpoints.query.Types.QueryDefinition> = ...\r\n   * ```\r\n   */\r\n  QueryDefinition: QueryDefinition<\r\n    QueryArg,\r\n    BaseQuery,\r\n    TagTypes,\r\n    ResultType,\r\n    ReducerPath\r\n  >\r\n  TagTypes: TagTypes\r\n  ReducerPath: ReducerPath\r\n}\r\n\r\nexport interface QueryExtraOptions<\r\n  TagTypes extends string,\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ReducerPath extends string = string\r\n> {\r\n  type: DefinitionType.query\r\n  /**\r\n   * Used by `query` endpoints. Determines which 'tag' is attached to the cached data returned by the query.\r\n   * Expects an array of tag type strings, an array of objects of tag types with ids, or a function that returns such an array.\r\n   * 1.  `['Post']` - equivalent to `2`\r\n   * 2.  `[{ type: 'Post' }]` - equivalent to `1`\r\n   * 3.  `[{ type: 'Post', id: 1 }]`\r\n   * 4.  `(result, error, arg) => ['Post']` - equivalent to `5`\r\n   * 5.  `(result, error, arg) => [{ type: 'Post' }]` - equivalent to `4`\r\n   * 6.  `(result, error, arg) => [{ type: 'Post', id: 1 }]`\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"providesTags example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   tagTypes: ['Posts'],\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *       // highlight-start\r\n   *       providesTags: (result) =>\r\n   *         result\r\n   *           ? [\r\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\r\n   *               { type: 'Posts', id: 'LIST' },\r\n   *             ]\r\n   *           : [{ type: 'Posts', id: 'LIST' }],\r\n   *       // highlight-end\r\n   *     })\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\r\n  providesTags?: ResultDescription<\r\n    TagTypes,\r\n    ResultType,\r\n    QueryArg,\r\n    BaseQueryError<BaseQuery>,\r\n    BaseQueryMeta<BaseQuery>\r\n  >\r\n  /**\r\n   * Not to be used. A query should not invalidate tags in the cache.\r\n   */\r\n  invalidatesTags?: never\r\n\r\n  /**\r\n   * Can be provided to return a custom cache key value based on the query arguments.\r\n   *\r\n   * This is primarily intended for cases where a non-serializable value is passed as part of the query arg object and should be excluded from the cache key.  It may also be used for cases where an endpoint should only have a single cache entry, such as an infinite loading / pagination implementation.\r\n   *\r\n   * Unlike the `createApi` version which can _only_ return a string, this per-endpoint option can also return an an object, number, or boolean.  If it returns a string, that value will be used as the cache key directly.  If it returns an object / number / boolean, that value will be passed to the built-in `defaultSerializeQueryArgs`.  This simplifies the use case of stripping out args you don't want included in the cache key.\r\n   *\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"serializeQueryArgs : exclude value\"\r\n   *\r\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   *\r\n   * interface MyApiClient {\r\n   *   fetchPost: (id: string) => Promise<Post>\r\n   * }\r\n   *\r\n   * createApi({\r\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *  endpoints: (build) => ({\r\n   *    // Example: an endpoint with an API client passed in as an argument,\r\n   *    // but only the item ID should be used as the cache key\r\n   *    getPost: build.query<Post, { id: string; client: MyApiClient }>({\r\n   *      queryFn: async ({ id, client }) => {\r\n   *        const post = await client.fetchPost(id)\r\n   *        return { data: post }\r\n   *      },\r\n   *      // highlight-start\r\n   *      serializeQueryArgs: ({ queryArgs, endpointDefinition, endpointName }) => {\r\n   *        const { id } = queryArgs\r\n   *        // This can return a string, an object, a number, or a boolean.\r\n   *        // If it returns an object, number or boolean, that value\r\n   *        // will be serialized automatically via `defaultSerializeQueryArgs`\r\n   *        return { id } // omit `client` from the cache key\r\n   *\r\n   *        // Alternately, you can use `defaultSerializeQueryArgs` yourself:\r\n   *        // return defaultSerializeQueryArgs({\r\n   *        //   endpointName,\r\n   *        //   queryArgs: { id },\r\n   *        //   endpointDefinition\r\n   *        // })\r\n   *        // Or  create and return a string yourself:\r\n   *        // return `getPost(${id})`\r\n   *      },\r\n   *      // highlight-end\r\n   *    }),\r\n   *  }),\r\n   *})\r\n   * ```\r\n   */\r\n  serializeQueryArgs?: SerializeQueryArgs<\r\n    QueryArg,\r\n    string | number | boolean | Record<any, any>\r\n  >\r\n\r\n  /**\r\n   * Can be provided to merge an incoming response value into the current cache data.\r\n   * If supplied, no automatic structural sharing will be applied - it's up to\r\n   * you to update the cache appropriately.\r\n   *\r\n   * Since RTKQ normally replaces cache entries with the new response, you will usually\r\n   * need to use this with the `serializeQueryArgs` or `forceRefetch` options to keep\r\n   * an existing cache entry so that it can be updated.\r\n   *\r\n   * Since this is wrapped with Immer, you may either mutate the `currentCacheValue` directly,\r\n   * or return a new value, but _not_ both at once.\r\n   *\r\n   * Will only be called if the existing `currentCacheData` is _not_ `undefined` - on first response,\r\n   * the cache entry will just save the response data directly.\r\n   *\r\n   * Useful if you don't want a new request to completely override the current cache value,\r\n   * maybe because you have manually updated it from another source and don't want those\r\n   * updates to get lost.\r\n   *\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"merge: pagination\"\r\n   *\r\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   *\r\n   * createApi({\r\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *  endpoints: (build) => ({\r\n   *    listItems: build.query<string[], number>({\r\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\r\n   *     // Only have one cache entry because the arg always maps to one string\r\n   *     serializeQueryArgs: ({ endpointName }) => {\r\n   *       return endpointName\r\n   *      },\r\n   *      // Always merge incoming data to the cache entry\r\n   *      merge: (currentCache, newItems) => {\r\n   *        currentCache.push(...newItems)\r\n   *      },\r\n   *      // Refetch when the page arg changes\r\n   *      forceRefetch({ currentArg, previousArg }) {\r\n   *        return currentArg !== previousArg\r\n   *      },\r\n   *    }),\r\n   *  }),\r\n   *})\r\n   * ```\r\n   */\r\n  merge?(\r\n    currentCacheData: ResultType,\r\n    responseData: ResultType,\r\n    otherArgs: {\r\n      arg: QueryArg\r\n      baseQueryMeta: BaseQueryMeta<BaseQuery>\r\n      requestId: string\r\n      fulfilledTimeStamp: number\r\n    }\r\n  ): ResultType | void\r\n\r\n  /**\r\n   * Check to see if the endpoint should force a refetch in cases where it normally wouldn't.\r\n   * This is primarily useful for \"infinite scroll\" / pagination use cases where\r\n   * RTKQ is keeping a single cache entry that is added to over time, in combination\r\n   * with `serializeQueryArgs` returning a fixed cache key and a `merge` callback\r\n   * set to add incoming data to the cache entry each time.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"forceRefresh: pagination\"\r\n   *\r\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   *\r\n   * createApi({\r\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *  endpoints: (build) => ({\r\n   *    listItems: build.query<string[], number>({\r\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\r\n   *     // Only have one cache entry because the arg always maps to one string\r\n   *     serializeQueryArgs: ({ endpointName }) => {\r\n   *       return endpointName\r\n   *      },\r\n   *      // Always merge incoming data to the cache entry\r\n   *      merge: (currentCache, newItems) => {\r\n   *        currentCache.push(...newItems)\r\n   *      },\r\n   *      // Refetch when the page arg changes\r\n   *      forceRefetch({ currentArg, previousArg }) {\r\n   *        return currentArg !== previousArg\r\n   *      },\r\n   *    }),\r\n   *  }),\r\n   *})\r\n   * ```\r\n   */\r\n  forceRefetch?(params: {\r\n    currentArg: QueryArg | undefined\r\n    previousArg: QueryArg | undefined\r\n    state: RootState<any, any, string>\r\n    endpointState?: QuerySubState<any>\r\n  }): boolean\r\n\r\n  /**\r\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\r\n   */\r\n  Types?: QueryTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n}\r\n\r\nexport type QueryDefinition<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType> &\r\n  QueryExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>\r\n\r\nexport interface MutationTypes<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> extends BaseEndpointTypes<QueryArg, BaseQuery, ResultType> {\r\n  /**\r\n   * The endpoint definition type. To be used with some internal generic types.\r\n   * @example\r\n   * ```ts\r\n   * const useMyWrappedHook: UseMutation<typeof api.endpoints.query.Types.MutationDefinition> = ...\r\n   * ```\r\n   */\r\n  MutationDefinition: MutationDefinition<\r\n    QueryArg,\r\n    BaseQuery,\r\n    TagTypes,\r\n    ResultType,\r\n    ReducerPath\r\n  >\r\n  TagTypes: TagTypes\r\n  ReducerPath: ReducerPath\r\n}\r\n\r\nexport interface MutationExtraOptions<\r\n  TagTypes extends string,\r\n  ResultType,\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  ReducerPath extends string = string\r\n> {\r\n  type: DefinitionType.mutation\r\n  /**\r\n   * Used by `mutation` endpoints. Determines which cached data should be either re-fetched or removed from the cache.\r\n   * Expects the same shapes as `providesTags`.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"invalidatesTags example\"\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   tagTypes: ['Posts'],\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *       providesTags: (result) =>\r\n   *         result\r\n   *           ? [\r\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\r\n   *               { type: 'Posts', id: 'LIST' },\r\n   *             ]\r\n   *           : [{ type: 'Posts', id: 'LIST' }],\r\n   *     }),\r\n   *     addPost: build.mutation<Post, Partial<Post>>({\r\n   *       query(body) {\r\n   *         return {\r\n   *           url: `posts`,\r\n   *           method: 'POST',\r\n   *           body,\r\n   *         }\r\n   *       },\r\n   *       // highlight-start\r\n   *       invalidatesTags: [{ type: 'Posts', id: 'LIST' }],\r\n   *       // highlight-end\r\n   *     }),\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\r\n  invalidatesTags?: ResultDescription<\r\n    TagTypes,\r\n    ResultType,\r\n    QueryArg,\r\n    BaseQueryError<BaseQuery>,\r\n    BaseQueryMeta<BaseQuery>\r\n  >\r\n  /**\r\n   * Not to be used. A mutation should not provide tags to the cache.\r\n   */\r\n  providesTags?: never\r\n\r\n  /**\r\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\r\n   */\r\n  Types?: MutationTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n}\r\n\r\nexport type MutationDefinition<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType> &\r\n  MutationExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>\r\n\r\nexport type EndpointDefinition<\r\n  QueryArg,\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ResultType,\r\n  ReducerPath extends string = string\r\n> =\r\n  | QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n  | MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n\r\nexport type EndpointDefinitions = Record<\r\n  string,\r\n  EndpointDefinition<any, any, any, any>\r\n>\r\n\r\nexport function isQueryDefinition(\r\n  e: EndpointDefinition<any, any, any, any>\r\n): e is QueryDefinition<any, any, any, any> {\r\n  return e.type === DefinitionType.query\r\n}\r\n\r\nexport function isMutationDefinition(\r\n  e: EndpointDefinition<any, any, any, any>\r\n): e is MutationDefinition<any, any, any, any> {\r\n  return e.type === DefinitionType.mutation\r\n}\r\n\r\nexport type EndpointBuilder<\r\n  BaseQuery extends BaseQueryFn,\r\n  TagTypes extends string,\r\n  ReducerPath extends string\r\n> = {\r\n  /**\r\n   * An endpoint definition that retrieves data, and may provide tags to the cache.\r\n   *\r\n   * @example\r\n   * ```js\r\n   * // codeblock-meta title=\"Example of all query endpoint options\"\r\n   * const api = createApi({\r\n   *  baseQuery,\r\n   *  endpoints: (build) => ({\r\n   *    getPost: build.query({\r\n   *      query: (id) => ({ url: `post/${id}` }),\r\n   *      // Pick out data and prevent nested properties in a hook or selector\r\n   *      transformResponse: (response) => response.data,\r\n   *      // Pick out error and prevent nested properties in a hook or selector\r\n   *      transformErrorResponse: (response) => response.error,\r\n   *      // `result` is the server response\r\n   *      providesTags: (result, error, id) => [{ type: 'Post', id }],\r\n   *      // trigger side effects or optimistic updates\r\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry, updateCachedData }) {},\r\n   *      // handle subscriptions etc\r\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry, updateCachedData }) {},\r\n   *    }),\r\n   *  }),\r\n   *});\r\n   *```\r\n   */\r\n  query<ResultType, QueryArg>(\r\n    definition: OmitFromUnion<\r\n      QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>,\r\n      'type'\r\n    >\r\n  ): QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n  /**\r\n   * An endpoint definition that alters data on the server or will possibly invalidate the cache.\r\n   *\r\n   * @example\r\n   * ```js\r\n   * // codeblock-meta title=\"Example of all mutation endpoint options\"\r\n   * const api = createApi({\r\n   *   baseQuery,\r\n   *   endpoints: (build) => ({\r\n   *     updatePost: build.mutation({\r\n   *       query: ({ id, ...patch }) => ({ url: `post/${id}`, method: 'PATCH', body: patch }),\r\n   *       // Pick out data and prevent nested properties in a hook or selector\r\n   *       transformResponse: (response) => response.data,\r\n   *       // Pick out error and prevent nested properties in a hook or selector\r\n   *       transformErrorResponse: (response) => response.error,\r\n   *       // `result` is the server response\r\n   *       invalidatesTags: (result, error, id) => [{ type: 'Post', id }],\r\n   *      // trigger side effects or optimistic updates\r\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry }) {},\r\n   *      // handle subscriptions etc\r\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry }) {},\r\n   *     }),\r\n   *   }),\r\n   * });\r\n   * ```\r\n   */\r\n  mutation<ResultType, QueryArg>(\r\n    definition: OmitFromUnion<\r\n      MutationDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        TagTypes,\r\n        ResultType,\r\n        ReducerPath\r\n      >,\r\n      'type'\r\n    >\r\n  ): MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>\r\n}\r\n\r\nexport type AssertTagTypes = <T extends FullTagDescription<string>>(t: T) => T\r\n\r\nexport function calculateProvidedBy<ResultType, QueryArg, ErrorType, MetaType>(\r\n  description:\r\n    | ResultDescription<string, ResultType, QueryArg, ErrorType, MetaType>\r\n    | undefined,\r\n  result: ResultType | undefined,\r\n  error: ErrorType | undefined,\r\n  queryArg: QueryArg,\r\n  meta: MetaType | undefined,\r\n  assertTagTypes: AssertTagTypes\r\n): readonly FullTagDescription<string>[] {\r\n  if (isFunction(description)) {\r\n    return description(\r\n      result as ResultType,\r\n      error as undefined,\r\n      queryArg,\r\n      meta as MetaType\r\n    )\r\n      .map(expandTagDescription)\r\n      .map(assertTagTypes)\r\n  }\r\n  if (Array.isArray(description)) {\r\n    return description.map(expandTagDescription).map(assertTagTypes)\r\n  }\r\n  return []\r\n}\r\n\r\nfunction isFunction<T>(t: T): t is Extract<T, Function> {\r\n  return typeof t === 'function'\r\n}\r\n\r\nexport function expandTagDescription(\r\n  description: TagDescription<string>\r\n): FullTagDescription<string> {\r\n  return typeof description === 'string' ? { type: description } : description\r\n}\r\n\r\nexport type QueryArgFrom<D extends BaseEndpointDefinition<any, any, any>> =\r\n  D extends BaseEndpointDefinition<infer QA, any, any> ? QA : unknown\r\nexport type ResultTypeFrom<D extends BaseEndpointDefinition<any, any, any>> =\r\n  D extends BaseEndpointDefinition<any, any, infer RT> ? RT : unknown\r\n\r\nexport type ReducerPathFrom<\r\n  D extends EndpointDefinition<any, any, any, any, any>\r\n> = D extends EndpointDefinition<any, any, any, any, infer RP> ? RP : unknown\r\n\r\nexport type TagTypesFrom<D extends EndpointDefinition<any, any, any, any>> =\r\n  D extends EndpointDefinition<any, any, infer RP, any> ? RP : unknown\r\n\r\nexport type TagTypesFromApi<T> = T extends Api<any, any, any, infer TagTypes>\r\n  ? TagTypes\r\n  : never\r\n\r\nexport type DefinitionsFromApi<T> = T extends Api<\r\n  any,\r\n  infer Definitions,\r\n  any,\r\n  any\r\n>\r\n  ? Definitions\r\n  : never\r\n\r\nexport type TransformedResponse<\r\n  NewDefinitions extends EndpointDefinitions,\r\n  K,\r\n  ResultType\r\n> = K extends keyof NewDefinitions\r\n  ? NewDefinitions[K]['transformResponse'] extends undefined\r\n    ? ResultType\r\n    : UnwrapPromise<\r\n        ReturnType<NonUndefined<NewDefinitions[K]['transformResponse']>>\r\n      >\r\n  : ResultType\r\n\r\nexport type OverrideResultType<Definition, NewResultType> =\r\n  Definition extends QueryDefinition<\r\n    infer QueryArg,\r\n    infer BaseQuery,\r\n    infer TagTypes,\r\n    any,\r\n    infer ReducerPath\r\n  >\r\n    ? QueryDefinition<QueryArg, BaseQuery, TagTypes, NewResultType, ReducerPath>\r\n    : Definition extends MutationDefinition<\r\n        infer QueryArg,\r\n        infer BaseQuery,\r\n        infer TagTypes,\r\n        any,\r\n        infer ReducerPath\r\n      >\r\n    ? MutationDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        TagTypes,\r\n        NewResultType,\r\n        ReducerPath\r\n      >\r\n    : never\r\n\r\nexport type UpdateDefinitions<\r\n  Definitions extends EndpointDefinitions,\r\n  NewTagTypes extends string,\r\n  NewDefinitions extends EndpointDefinitions\r\n> = {\r\n  [K in keyof Definitions]: Definitions[K] extends QueryDefinition<\r\n    infer QueryArg,\r\n    infer BaseQuery,\r\n    any,\r\n    infer ResultType,\r\n    infer ReducerPath\r\n  >\r\n    ? QueryDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        NewTagTypes,\r\n        TransformedResponse<NewDefinitions, K, ResultType>,\r\n        ReducerPath\r\n      >\r\n    : Definitions[K] extends MutationDefinition<\r\n        infer QueryArg,\r\n        infer BaseQuery,\r\n        any,\r\n        infer ResultType,\r\n        infer ReducerPath\r\n      >\r\n    ? MutationDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        NewTagTypes,\r\n        TransformedResponse<NewDefinitions, K, ResultType>,\r\n        ReducerPath\r\n      >\r\n    : never\r\n}\r\n", "import type { AnyAction, PayloadAction } from '@reduxjs/toolkit'\r\nimport {\r\n  combineReducers,\r\n  createAction,\r\n  createSlice,\r\n  isAnyOf,\r\n  isFulfilled,\r\n  isRejectedWithValue,\r\n  createNextState,\r\n  prepareAutoBatched,\r\n} from '@reduxjs/toolkit'\r\nimport type {\r\n  CombinedState as CombinedQueryState,\r\n  QuerySubstateIdentifier,\r\n  QuerySubState,\r\n  MutationSubstateIdentifier,\r\n  MutationSubState,\r\n  MutationState,\r\n  QueryState,\r\n  InvalidationState,\r\n  Subscribers,\r\n  QueryCacheKey,\r\n  SubscriptionState,\r\n  ConfigState,\r\n} from './apiState'\r\nimport { QueryStatus } from './apiState'\r\nimport type { MutationThunk, QueryThunk, RejectedAction } from './buildThunks'\r\nimport { calculateProvidedByThunk } from './buildThunks'\r\nimport type {\r\n  AssertTagTypes,\r\n  EndpointDefinitions,\r\n  FullTagDescription,\r\n  QueryDefinition,\r\n} from '../endpointDefinitions'\r\nimport type { Patch } from 'immer'\r\nimport { isDraft } from 'immer'\r\nimport { applyPatches, original } from 'immer'\r\nimport { onFocus, onFocusLost, onOffline, onOnline } from './setupListeners'\r\nimport {\r\n  isDocumentVisible,\r\n  isOnline,\r\n  copyWithStructuralSharing,\r\n} from '../utils'\r\nimport type { ApiContext } from '../apiTypes'\r\nimport { isUpsertQuery } from './buildInitiate'\r\n\r\nfunction updateQuerySubstateIfExists(\r\n  state: QueryState<any>,\r\n  queryCacheKey: QueryCacheKey,\r\n  update: (substate: QuerySubState<any>) => void\r\n) {\r\n  const substate = state[queryCacheKey]\r\n  if (substate) {\r\n    update(substate)\r\n  }\r\n}\r\n\r\nexport function getMutationCacheKey(\r\n  id:\r\n    | MutationSubstateIdentifier\r\n    | { requestId: string; arg: { fixedCacheKey?: string | undefined } }\r\n): string\r\nexport function getMutationCacheKey(id: {\r\n  fixedCacheKey?: string\r\n  requestId?: string\r\n}): string | undefined\r\n\r\nexport function getMutationCacheKey(\r\n  id:\r\n    | { fixedCacheKey?: string; requestId?: string }\r\n    | MutationSubstateIdentifier\r\n    | { requestId: string; arg: { fixedCacheKey?: string | undefined } }\r\n): string | undefined {\r\n  return ('arg' in id ? id.arg.fixedCacheKey : id.fixedCacheKey) ?? id.requestId\r\n}\r\n\r\nfunction updateMutationSubstateIfExists(\r\n  state: MutationState<any>,\r\n  id:\r\n    | MutationSubstateIdentifier\r\n    | { requestId: string; arg: { fixedCacheKey?: string | undefined } },\r\n  update: (substate: MutationSubState<any>) => void\r\n) {\r\n  const substate = state[getMutationCacheKey(id)]\r\n  if (substate) {\r\n    update(substate)\r\n  }\r\n}\r\n\r\nconst initialState = {} as any\r\n\r\nexport function buildSlice({\r\n  reducerPath,\r\n  queryThunk,\r\n  mutationThunk,\r\n  context: {\r\n    endpointDefinitions: definitions,\r\n    apiUid,\r\n    extractRehydrationInfo,\r\n    hasRehydrationInfo,\r\n  },\r\n  assertTagType,\r\n  config,\r\n}: {\r\n  reducerPath: string\r\n  queryThunk: QueryThunk\r\n  mutationThunk: MutationThunk\r\n  context: ApiContext<EndpointDefinitions>\r\n  assertTagType: AssertTagTypes\r\n  config: Omit<\r\n    ConfigState<string>,\r\n    'online' | 'focused' | 'middlewareRegistered'\r\n  >\r\n}) {\r\n  const resetApiState = createAction(`${reducerPath}/resetApiState`)\r\n  const querySlice = createSlice({\r\n    name: `${reducerPath}/queries`,\r\n    initialState: initialState as QueryState<any>,\r\n    reducers: {\r\n      removeQueryResult: {\r\n        reducer(\r\n          draft,\r\n          { payload: { queryCacheKey } }: PayloadAction<QuerySubstateIdentifier>\r\n        ) {\r\n          delete draft[queryCacheKey]\r\n        },\r\n        prepare: prepareAutoBatched<QuerySubstateIdentifier>(),\r\n      },\r\n      queryResultPatched: {\r\n        reducer(\r\n          draft,\r\n          {\r\n            payload: { queryCacheKey, patches },\r\n          }: PayloadAction<\r\n            QuerySubstateIdentifier & { patches: readonly Patch[] }\r\n          >\r\n        ) {\r\n          updateQuerySubstateIfExists(draft, queryCacheKey, (substate) => {\r\n            substate.data = applyPatches(substate.data as any, patches.concat())\r\n          })\r\n        },\r\n        prepare: prepareAutoBatched<\r\n          QuerySubstateIdentifier & { patches: readonly Patch[] }\r\n        >(),\r\n      },\r\n    },\r\n    extraReducers(builder) {\r\n      builder\r\n        .addCase(queryThunk.pending, (draft, { meta, meta: { arg } }) => {\r\n          const upserting = isUpsertQuery(arg)\r\n          if (arg.subscribe || upserting) {\r\n            // only initialize substate if we want to subscribe to it\r\n            draft[arg.queryCacheKey] ??= {\r\n              status: QueryStatus.uninitialized,\r\n              endpointName: arg.endpointName,\r\n            }\r\n          }\r\n\r\n          updateQuerySubstateIfExists(draft, arg.queryCacheKey, (substate) => {\r\n            substate.status = QueryStatus.pending\r\n\r\n            substate.requestId =\r\n              upserting && substate.requestId\r\n                ? // for `upsertQuery` **updates**, keep the current `requestId`\r\n                  substate.requestId\r\n                : // for normal queries or `upsertQuery` **inserts** always update the `requestId`\r\n                  meta.requestId\r\n            if (arg.originalArgs !== undefined) {\r\n              substate.originalArgs = arg.originalArgs\r\n            }\r\n            substate.startedTimeStamp = meta.startedTimeStamp\r\n          })\r\n        })\r\n        .addCase(queryThunk.fulfilled, (draft, { meta, payload }) => {\r\n          updateQuerySubstateIfExists(\r\n            draft,\r\n            meta.arg.queryCacheKey,\r\n            (substate) => {\r\n              if (\r\n                substate.requestId !== meta.requestId &&\r\n                !isUpsertQuery(meta.arg)\r\n              )\r\n                return\r\n              const { merge } = definitions[\r\n                meta.arg.endpointName\r\n              ] as QueryDefinition<any, any, any, any>\r\n              substate.status = QueryStatus.fulfilled\r\n\r\n              if (merge) {\r\n                if (substate.data !== undefined) {\r\n                  const { fulfilledTimeStamp, arg, baseQueryMeta, requestId } =\r\n                    meta\r\n                  // There's existing cache data. Let the user merge it in themselves.\r\n                  // We're already inside an Immer-powered reducer, and the user could just mutate `substate.data`\r\n                  // themselves inside of `merge()`. But, they might also want to return a new value.\r\n                  // Try to let Immer figure that part out, save the result, and assign it to `substate.data`.\r\n                  let newData = createNextState(\r\n                    substate.data,\r\n                    (draftSubstateData) => {\r\n                      // As usual with Immer, you can mutate _or_ return inside here, but not both\r\n                      return merge(draftSubstateData, payload, {\r\n                        arg: arg.originalArgs,\r\n                        baseQueryMeta,\r\n                        fulfilledTimeStamp,\r\n                        requestId,\r\n                      })\r\n                    }\r\n                  )\r\n                  substate.data = newData\r\n                } else {\r\n                  // Presumably a fresh request. Just cache the response data.\r\n                  substate.data = payload\r\n                }\r\n              } else {\r\n                // Assign or safely update the cache data.\r\n                substate.data =\r\n                  definitions[meta.arg.endpointName].structuralSharing ?? true\r\n                    ? copyWithStructuralSharing(\r\n                        isDraft(substate.data)\r\n                          ? original(substate.data)\r\n                          : substate.data,\r\n                        payload\r\n                      )\r\n                    : payload\r\n              }\r\n\r\n              delete substate.error\r\n              substate.fulfilledTimeStamp = meta.fulfilledTimeStamp\r\n            }\r\n          )\r\n        })\r\n        .addCase(\r\n          queryThunk.rejected,\r\n          (draft, { meta: { condition, arg, requestId }, error, payload }) => {\r\n            updateQuerySubstateIfExists(\r\n              draft,\r\n              arg.queryCacheKey,\r\n              (substate) => {\r\n                if (condition) {\r\n                  // request was aborted due to condition (another query already running)\r\n                } else {\r\n                  // request failed\r\n                  if (substate.requestId !== requestId) return\r\n                  substate.status = QueryStatus.rejected\r\n                  substate.error = (payload ?? error) as any\r\n                }\r\n              }\r\n            )\r\n          }\r\n        )\r\n        .addMatcher(hasRehydrationInfo, (draft, action) => {\r\n          const { queries } = extractRehydrationInfo(action)!\r\n          for (const [key, entry] of Object.entries(queries)) {\r\n            if (\r\n              // do not rehydrate entries that were currently in flight.\r\n              entry?.status === QueryStatus.fulfilled ||\r\n              entry?.status === QueryStatus.rejected\r\n            ) {\r\n              draft[key] = entry\r\n            }\r\n          }\r\n        })\r\n    },\r\n  })\r\n  const mutationSlice = createSlice({\r\n    name: `${reducerPath}/mutations`,\r\n    initialState: initialState as MutationState<any>,\r\n    reducers: {\r\n      removeMutationResult: {\r\n        reducer(draft, { payload }: PayloadAction<MutationSubstateIdentifier>) {\r\n          const cacheKey = getMutationCacheKey(payload)\r\n          if (cacheKey in draft) {\r\n            delete draft[cacheKey]\r\n          }\r\n        },\r\n        prepare: prepareAutoBatched<MutationSubstateIdentifier>(),\r\n      },\r\n    },\r\n    extraReducers(builder) {\r\n      builder\r\n        .addCase(\r\n          mutationThunk.pending,\r\n          (draft, { meta, meta: { requestId, arg, startedTimeStamp } }) => {\r\n            if (!arg.track) return\r\n\r\n            draft[getMutationCacheKey(meta)] = {\r\n              requestId,\r\n              status: QueryStatus.pending,\r\n              endpointName: arg.endpointName,\r\n              startedTimeStamp,\r\n            }\r\n          }\r\n        )\r\n        .addCase(mutationThunk.fulfilled, (draft, { payload, meta }) => {\r\n          if (!meta.arg.track) return\r\n\r\n          updateMutationSubstateIfExists(draft, meta, (substate) => {\r\n            if (substate.requestId !== meta.requestId) return\r\n            substate.status = QueryStatus.fulfilled\r\n            substate.data = payload\r\n            substate.fulfilledTimeStamp = meta.fulfilledTimeStamp\r\n          })\r\n        })\r\n        .addCase(mutationThunk.rejected, (draft, { payload, error, meta }) => {\r\n          if (!meta.arg.track) return\r\n\r\n          updateMutationSubstateIfExists(draft, meta, (substate) => {\r\n            if (substate.requestId !== meta.requestId) return\r\n\r\n            substate.status = QueryStatus.rejected\r\n            substate.error = (payload ?? error) as any\r\n          })\r\n        })\r\n        .addMatcher(hasRehydrationInfo, (draft, action) => {\r\n          const { mutations } = extractRehydrationInfo(action)!\r\n          for (const [key, entry] of Object.entries(mutations)) {\r\n            if (\r\n              // do not rehydrate entries that were currently in flight.\r\n              (entry?.status === QueryStatus.fulfilled ||\r\n                entry?.status === QueryStatus.rejected) &&\r\n              // only rehydrate endpoints that were persisted using a `fixedCacheKey`\r\n              key !== entry?.requestId\r\n            ) {\r\n              draft[key] = entry\r\n            }\r\n          }\r\n        })\r\n    },\r\n  })\r\n\r\n  const invalidationSlice = createSlice({\r\n    name: `${reducerPath}/invalidation`,\r\n    initialState: initialState as InvalidationState<string>,\r\n    reducers: {\r\n      updateProvidedBy: {\r\n        reducer(\r\n          draft,\r\n          action: PayloadAction<{\r\n            queryCacheKey: QueryCacheKey\r\n            providedTags: readonly FullTagDescription<string>[]\r\n          }>\r\n        ) {\r\n          const { queryCacheKey, providedTags } = action.payload\r\n\r\n          for (const tagTypeSubscriptions of Object.values(draft)) {\r\n            for (const idSubscriptions of Object.values(tagTypeSubscriptions)) {\r\n              const foundAt = idSubscriptions.indexOf(queryCacheKey)\r\n              if (foundAt !== -1) {\r\n                idSubscriptions.splice(foundAt, 1)\r\n              }\r\n            }\r\n          }\r\n\r\n          for (const { type, id } of providedTags) {\r\n            const subscribedQueries = ((draft[type] ??= {})[\r\n              id || '__internal_without_id'\r\n            ] ??= [])\r\n            const alreadySubscribed = subscribedQueries.includes(queryCacheKey)\r\n            if (!alreadySubscribed) {\r\n              subscribedQueries.push(queryCacheKey)\r\n            }\r\n          }\r\n        },\r\n        prepare: prepareAutoBatched<{\r\n          queryCacheKey: QueryCacheKey\r\n          providedTags: readonly FullTagDescription<string>[]\r\n        }>(),\r\n      },\r\n    },\r\n    extraReducers(builder) {\r\n      builder\r\n        .addCase(\r\n          querySlice.actions.removeQueryResult,\r\n          (draft, { payload: { queryCacheKey } }) => {\r\n            for (const tagTypeSubscriptions of Object.values(draft)) {\r\n              for (const idSubscriptions of Object.values(\r\n                tagTypeSubscriptions\r\n              )) {\r\n                const foundAt = idSubscriptions.indexOf(queryCacheKey)\r\n                if (foundAt !== -1) {\r\n                  idSubscriptions.splice(foundAt, 1)\r\n                }\r\n              }\r\n            }\r\n          }\r\n        )\r\n        .addMatcher(hasRehydrationInfo, (draft, action) => {\r\n          const { provided } = extractRehydrationInfo(action)!\r\n          for (const [type, incomingTags] of Object.entries(provided)) {\r\n            for (const [id, cacheKeys] of Object.entries(incomingTags)) {\r\n              const subscribedQueries = ((draft[type] ??= {})[\r\n                id || '__internal_without_id'\r\n              ] ??= [])\r\n              for (const queryCacheKey of cacheKeys) {\r\n                const alreadySubscribed =\r\n                  subscribedQueries.includes(queryCacheKey)\r\n                if (!alreadySubscribed) {\r\n                  subscribedQueries.push(queryCacheKey)\r\n                }\r\n              }\r\n            }\r\n          }\r\n        })\r\n        .addMatcher(\r\n          isAnyOf(isFulfilled(queryThunk), isRejectedWithValue(queryThunk)),\r\n          (draft, action) => {\r\n            const providedTags = calculateProvidedByThunk(\r\n              action,\r\n              'providesTags',\r\n              definitions,\r\n              assertTagType\r\n            )\r\n            const { queryCacheKey } = action.meta.arg\r\n\r\n            invalidationSlice.caseReducers.updateProvidedBy(\r\n              draft,\r\n              invalidationSlice.actions.updateProvidedBy({\r\n                queryCacheKey,\r\n                providedTags,\r\n              })\r\n            )\r\n          }\r\n        )\r\n    },\r\n  })\r\n\r\n  // Dummy slice to generate actions\r\n  const subscriptionSlice = createSlice({\r\n    name: `${reducerPath}/subscriptions`,\r\n    initialState: initialState as SubscriptionState,\r\n    reducers: {\r\n      updateSubscriptionOptions(\r\n        d,\r\n        a: PayloadAction<\r\n          {\r\n            endpointName: string\r\n            requestId: string\r\n            options: Subscribers[number]\r\n          } & QuerySubstateIdentifier\r\n        >\r\n      ) {\r\n        // Dummy\r\n      },\r\n      unsubscribeQueryResult(\r\n        d,\r\n        a: PayloadAction<{ requestId: string } & QuerySubstateIdentifier>\r\n      ) {\r\n        // Dummy\r\n      },\r\n      internal_probeSubscription(\r\n        d,\r\n        a: PayloadAction<{ queryCacheKey: string; requestId: string }>\r\n      ) {\r\n        // dummy\r\n      },\r\n    },\r\n  })\r\n\r\n  const internalSubscriptionsSlice = createSlice({\r\n    name: `${reducerPath}/internalSubscriptions`,\r\n    initialState: initialState as SubscriptionState,\r\n    reducers: {\r\n      subscriptionsUpdated: {\r\n        reducer(state, action: PayloadAction<Patch[]>) {\r\n          return applyPatches(state, action.payload)\r\n        },\r\n        prepare: prepareAutoBatched<Patch[]>(),\r\n      },\r\n    },\r\n  })\r\n\r\n  const configSlice = createSlice({\r\n    name: `${reducerPath}/config`,\r\n    initialState: {\r\n      online: isOnline(),\r\n      focused: isDocumentVisible(),\r\n      middlewareRegistered: false,\r\n      ...config,\r\n    } as ConfigState<string>,\r\n    reducers: {\r\n      middlewareRegistered(state, { payload }: PayloadAction<string>) {\r\n        state.middlewareRegistered =\r\n          state.middlewareRegistered === 'conflict' || apiUid !== payload\r\n            ? 'conflict'\r\n            : true\r\n      },\r\n    },\r\n    extraReducers: (builder) => {\r\n      builder\r\n        .addCase(onOnline, (state) => {\r\n          state.online = true\r\n        })\r\n        .addCase(onOffline, (state) => {\r\n          state.online = false\r\n        })\r\n        .addCase(onFocus, (state) => {\r\n          state.focused = true\r\n        })\r\n        .addCase(onFocusLost, (state) => {\r\n          state.focused = false\r\n        })\r\n        // update the state to be a new object to be picked up as a \"state change\"\r\n        // by redux-persist's `autoMergeLevel2`\r\n        .addMatcher(hasRehydrationInfo, (draft) => ({ ...draft }))\r\n    },\r\n  })\r\n\r\n  const combinedReducer = combineReducers<\r\n    CombinedQueryState<any, string, string>\r\n  >({\r\n    queries: querySlice.reducer,\r\n    mutations: mutationSlice.reducer,\r\n    provided: invalidationSlice.reducer,\r\n    subscriptions: internalSubscriptionsSlice.reducer,\r\n    config: configSlice.reducer,\r\n  })\r\n\r\n  const reducer: typeof combinedReducer = (state, action) =>\r\n    combinedReducer(resetApiState.match(action) ? undefined : state, action)\r\n\r\n  const actions = {\r\n    ...configSlice.actions,\r\n    ...querySlice.actions,\r\n    ...subscriptionSlice.actions,\r\n    ...internalSubscriptionsSlice.actions,\r\n    ...mutationSlice.actions,\r\n    ...invalidationSlice.actions,\r\n    /** @deprecated has been renamed to `removeMutationResult` */\r\n    unsubscribeMutationResult: mutationSlice.actions.removeMutationResult,\r\n    resetApiState,\r\n  }\r\n\r\n  return { reducer, actions }\r\n}\r\nexport type SliceActions = ReturnType<typeof buildSlice>['actions']\r\n", "export function isNotNullish<T>(v: T | null | undefined): v is T {\r\n  return v != null\r\n}\r\n", "import type {\r\n  EndpointDefinitions,\r\n  QueryDefinition,\r\n  MutationDefinition,\r\n  QueryArgFrom,\r\n  ResultTypeFrom,\r\n} from '../endpointDefinitions'\r\nimport { DefinitionType, isQueryDefinition } from '../endpointDefinitions'\r\nimport type { QueryThunk, MutationThunk, QueryThunkArg } from './buildThunks'\r\nimport type { AnyAction, ThunkAction, SerializedError } from '@reduxjs/toolkit'\r\nimport type { SubscriptionOptions, RootState } from './apiState'\r\nimport type { InternalSerializeQueryArgs } from '../defaultSerializeQueryArgs'\r\nimport type { Api, ApiContext } from '../apiTypes'\r\nimport type { ApiEndpointQuery } from './module'\r\nimport type { BaseQueryError, QueryReturnValue } from '../baseQueryTypes'\r\nimport type { QueryResultSelectorResult } from './buildSelectors'\r\nimport type { Dispatch } from 'redux'\r\nimport { isNotNullish } from '../utils/isNotNullish'\r\n\r\ndeclare module './module' {\r\n  export interface ApiEndpointQuery<\r\n    Definition extends QueryDefinition<any, any, any, any, any>,\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    Definitions extends EndpointDefinitions\r\n  > {\r\n    initiate: StartQueryActionCreator<Definition>\r\n  }\r\n\r\n  export interface ApiEndpointMutation<\r\n    Definition extends MutationDefinition<any, any, any, any, any>,\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    Definitions extends EndpointDefinitions\r\n  > {\r\n    initiate: StartMutationActionCreator<Definition>\r\n  }\r\n}\r\n\r\nexport const forceQueryFnSymbol = Symbol('forceQueryFn')\r\nexport const isUpsertQuery = (arg: QueryThunkArg) =>\r\n  typeof arg[forceQueryFnSymbol] === 'function'\r\n\r\nexport interface StartQueryActionCreatorOptions {\r\n  subscribe?: boolean\r\n  forceRefetch?: boolean | number\r\n  subscriptionOptions?: SubscriptionOptions\r\n  [forceQueryFnSymbol]?: () => QueryReturnValue\r\n}\r\n\r\ntype StartQueryActionCreator<\r\n  D extends QueryDefinition<any, any, any, any, any>\r\n> = (\r\n  arg: QueryArgFrom<D>,\r\n  options?: StartQueryActionCreatorOptions\r\n) => ThunkAction<QueryActionCreatorResult<D>, any, any, AnyAction>\r\n\r\nexport type QueryActionCreatorResult<\r\n  D extends QueryDefinition<any, any, any, any>\r\n> = Promise<QueryResultSelectorResult<D>> & {\r\n  arg: QueryArgFrom<D>\r\n  requestId: string\r\n  subscriptionOptions: SubscriptionOptions | undefined\r\n  abort(): void\r\n  unwrap(): Promise<ResultTypeFrom<D>>\r\n  unsubscribe(): void\r\n  refetch(): QueryActionCreatorResult<D>\r\n  updateSubscriptionOptions(options: SubscriptionOptions): void\r\n  queryCacheKey: string\r\n}\r\n\r\ntype StartMutationActionCreator<\r\n  D extends MutationDefinition<any, any, any, any>\r\n> = (\r\n  arg: QueryArgFrom<D>,\r\n  options?: {\r\n    /**\r\n     * If this mutation should be tracked in the store.\r\n     * If you just want to manually trigger this mutation using `dispatch` and don't care about the\r\n     * result, state & potential errors being held in store, you can set this to false.\r\n     * (defaults to `true`)\r\n     */\r\n    track?: boolean\r\n    fixedCacheKey?: string\r\n  }\r\n) => ThunkAction<MutationActionCreatorResult<D>, any, any, AnyAction>\r\n\r\nexport type MutationActionCreatorResult<\r\n  D extends MutationDefinition<any, any, any, any>\r\n> = Promise<\r\n  | { data: ResultTypeFrom<D> }\r\n  | {\r\n      error:\r\n        | Exclude<\r\n            BaseQueryError<\r\n              D extends MutationDefinition<any, infer BaseQuery, any, any>\r\n                ? BaseQuery\r\n                : never\r\n            >,\r\n            undefined\r\n          >\r\n        | SerializedError\r\n    }\r\n> & {\r\n  /** @internal */\r\n  arg: {\r\n    /**\r\n     * The name of the given endpoint for the mutation\r\n     */\r\n    endpointName: string\r\n    /**\r\n     * The original arguments supplied to the mutation call\r\n     */\r\n    originalArgs: QueryArgFrom<D>\r\n    /**\r\n     * Whether the mutation is being tracked in the store.\r\n     */\r\n    track?: boolean\r\n    fixedCacheKey?: string\r\n  }\r\n  /**\r\n   * A unique string generated for the request sequence\r\n   */\r\n  requestId: string\r\n\r\n  /**\r\n   * A method to cancel the mutation promise. Note that this is not intended to prevent the mutation\r\n   * that was fired off from reaching the server, but only to assist in handling the response.\r\n   *\r\n   * Calling `abort()` prior to the promise resolving will force it to reach the error state with\r\n   * the serialized error:\r\n   * `{ name: 'AbortError', message: 'Aborted' }`\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * const [updateUser] = useUpdateUserMutation();\r\n   *\r\n   * useEffect(() => {\r\n   *   const promise = updateUser(id);\r\n   *   promise\r\n   *     .unwrap()\r\n   *     .catch((err) => {\r\n   *       if (err.name === 'AbortError') return;\r\n   *       // else handle the unexpected error\r\n   *     })\r\n   *\r\n   *   return () => {\r\n   *     promise.abort();\r\n   *   }\r\n   * }, [id, updateUser])\r\n   * ```\r\n   */\r\n  abort(): void\r\n  /**\r\n   * Unwraps a mutation call to provide the raw response/error.\r\n   *\r\n   * @remarks\r\n   * If you need to access the error or success payload immediately after a mutation, you can chain .unwrap().\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Using .unwrap\"\r\n   * addPost({ id: 1, name: 'Example' })\r\n   *   .unwrap()\r\n   *   .then((payload) => console.log('fulfilled', payload))\r\n   *   .catch((error) => console.error('rejected', error));\r\n   * ```\r\n   *\r\n   * @example\r\n   * ```ts\r\n   * // codeblock-meta title=\"Using .unwrap with async await\"\r\n   * try {\r\n   *   const payload = await addPost({ id: 1, name: 'Example' }).unwrap();\r\n   *   console.log('fulfilled', payload)\r\n   * } catch (error) {\r\n   *   console.error('rejected', error);\r\n   * }\r\n   * ```\r\n   */\r\n  unwrap(): Promise<ResultTypeFrom<D>>\r\n  /**\r\n   * A method to manually unsubscribe from the mutation call, meaning it will be removed from cache after the usual caching grace period.\r\n   The value returned by the hook will reset to `isUninitialized` afterwards.\r\n   */\r\n  reset(): void\r\n  /** @deprecated has been renamed to `reset` */\r\n  unsubscribe(): void\r\n}\r\n\r\nexport function buildInitiate({\r\n  serializeQueryArgs,\r\n  queryThunk,\r\n  mutationThunk,\r\n  api,\r\n  context,\r\n}: {\r\n  serializeQueryArgs: InternalSerializeQueryArgs\r\n  queryThunk: QueryThunk\r\n  mutationThunk: MutationThunk\r\n  api: Api<any, EndpointDefinitions, any, any>\r\n  context: ApiContext<EndpointDefinitions>\r\n}) {\r\n  const runningQueries: Map<\r\n    Dispatch,\r\n    Record<string, QueryActionCreatorResult<any> | undefined>\r\n  > = new Map()\r\n  const runningMutations: Map<\r\n    Dispatch,\r\n    Record<string, MutationActionCreatorResult<any> | undefined>\r\n  > = new Map()\r\n\r\n  const {\r\n    unsubscribeQueryResult,\r\n    removeMutationResult,\r\n    updateSubscriptionOptions,\r\n  } = api.internalActions\r\n  return {\r\n    buildInitiateQuery,\r\n    buildInitiateMutation,\r\n    getRunningQueryThunk,\r\n    getRunningMutationThunk,\r\n    getRunningQueriesThunk,\r\n    getRunningMutationsThunk,\r\n    getRunningOperationPromises,\r\n    removalWarning,\r\n  }\r\n\r\n  /** @deprecated to be removed in 2.0 */\r\n  function removalWarning(): never {\r\n    throw new Error(\r\n      `This method had to be removed due to a conceptual bug in RTK.\r\n       Please see https://github.com/reduxjs/redux-toolkit/pull/2481 for details.\r\n       See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for new guidance on SSR.`\r\n    )\r\n  }\r\n\r\n  /** @deprecated to be removed in 2.0 */\r\n  function getRunningOperationPromises() {\r\n    if (\r\n      typeof process !== 'undefined' &&\r\n      process.env.NODE_ENV === 'development'\r\n    ) {\r\n      removalWarning()\r\n    } else {\r\n      const extract = <T>(\r\n        v: Map<Dispatch<AnyAction>, Record<string, T | undefined>>\r\n      ) =>\r\n        Array.from(v.values()).flatMap((queriesForStore) =>\r\n          queriesForStore ? Object.values(queriesForStore) : []\r\n        )\r\n      return [...extract(runningQueries), ...extract(runningMutations)].filter(\r\n        isNotNullish\r\n      )\r\n    }\r\n  }\r\n\r\n  function getRunningQueryThunk(endpointName: string, queryArgs: any) {\r\n    return (dispatch: Dispatch) => {\r\n      const endpointDefinition = context.endpointDefinitions[endpointName]\r\n      const queryCacheKey = serializeQueryArgs({\r\n        queryArgs,\r\n        endpointDefinition,\r\n        endpointName,\r\n      })\r\n      return runningQueries.get(dispatch)?.[queryCacheKey] as\r\n        | QueryActionCreatorResult<never>\r\n        | undefined\r\n    }\r\n  }\r\n\r\n  function getRunningMutationThunk(\r\n    /**\r\n     * this is only here to allow TS to infer the result type by input value\r\n     * we could use it to validate the result, but it's probably not necessary\r\n     */\r\n    _endpointName: string,\r\n    fixedCacheKeyOrRequestId: string\r\n  ) {\r\n    return (dispatch: Dispatch) => {\r\n      return runningMutations.get(dispatch)?.[fixedCacheKeyOrRequestId] as\r\n        | MutationActionCreatorResult<never>\r\n        | undefined\r\n    }\r\n  }\r\n\r\n  function getRunningQueriesThunk() {\r\n    return (dispatch: Dispatch) =>\r\n      Object.values(runningQueries.get(dispatch) || {}).filter(isNotNullish)\r\n  }\r\n\r\n  function getRunningMutationsThunk() {\r\n    return (dispatch: Dispatch) =>\r\n      Object.values(runningMutations.get(dispatch) || {}).filter(isNotNullish)\r\n  }\r\n\r\n  function middlewareWarning(dispatch: Dispatch) {\r\n    if (process.env.NODE_ENV !== 'production') {\r\n      if ((middlewareWarning as any).triggered) return\r\n      const registered:\r\n        | ReturnType<typeof api.internalActions.internal_probeSubscription>\r\n        | boolean = dispatch(\r\n        api.internalActions.internal_probeSubscription({\r\n          queryCacheKey: 'DOES_NOT_EXIST',\r\n          requestId: 'DUMMY_REQUEST_ID',\r\n        })\r\n      )\r\n\r\n      ;(middlewareWarning as any).triggered = true\r\n\r\n      // The RTKQ middleware _should_ always return a boolean for `probeSubscription`\r\n      if (typeof registered !== 'boolean') {\r\n        // Otherwise, must not have been added\r\n        throw new Error(\r\n          `Warning: Middleware for RTK-Query API at reducerPath \"${api.reducerPath}\" has not been added to the store.\r\nYou must add the middleware for RTK-Query to function correctly!`\r\n        )\r\n      }\r\n    }\r\n  }\r\n\r\n  function buildInitiateQuery(\r\n    endpointName: string,\r\n    endpointDefinition: QueryDefinition<any, any, any, any>\r\n  ) {\r\n    const queryAction: StartQueryActionCreator<any> =\r\n      (\r\n        arg,\r\n        {\r\n          subscribe = true,\r\n          forceRefetch,\r\n          subscriptionOptions,\r\n          [forceQueryFnSymbol]: forceQueryFn,\r\n        } = {}\r\n      ) =>\r\n      (dispatch, getState) => {\r\n        const queryCacheKey = serializeQueryArgs({\r\n          queryArgs: arg,\r\n          endpointDefinition,\r\n          endpointName,\r\n        })\r\n\r\n        const thunk = queryThunk({\r\n          type: 'query',\r\n          subscribe,\r\n          forceRefetch: forceRefetch,\r\n          subscriptionOptions,\r\n          endpointName,\r\n          originalArgs: arg,\r\n          queryCacheKey,\r\n          [forceQueryFnSymbol]: forceQueryFn,\r\n        })\r\n        const selector = (\r\n          api.endpoints[endpointName] as ApiEndpointQuery<any, any>\r\n        ).select(arg)\r\n\r\n        const thunkResult = dispatch(thunk)\r\n        const stateAfter = selector(getState())\r\n\r\n        middlewareWarning(dispatch)\r\n\r\n        const { requestId, abort } = thunkResult\r\n\r\n        const skippedSynchronously = stateAfter.requestId !== requestId\r\n\r\n        const runningQuery = runningQueries.get(dispatch)?.[queryCacheKey]\r\n        const selectFromState = () => selector(getState())\r\n\r\n        const statePromise: QueryActionCreatorResult<any> = Object.assign(\r\n          forceQueryFn\r\n            ? // a query has been forced (upsertQueryData)\r\n              // -> we want to resolve it once data has been written with the data that will be written\r\n              thunkResult.then(selectFromState)\r\n            : skippedSynchronously && !runningQuery\r\n            ? // a query has been skipped due to a condition and we do not have any currently running query\r\n              // -> we want to resolve it immediately with the current data\r\n              Promise.resolve(stateAfter)\r\n            : // query just started or one is already in flight\r\n              // -> wait for the running query, then resolve with data from after that\r\n              Promise.all([runningQuery, thunkResult]).then(selectFromState),\r\n          {\r\n            arg,\r\n            requestId,\r\n            subscriptionOptions,\r\n            queryCacheKey,\r\n            abort,\r\n            async unwrap() {\r\n              const result = await statePromise\r\n\r\n              if (result.isError) {\r\n                throw result.error\r\n              }\r\n\r\n              return result.data\r\n            },\r\n            refetch: () =>\r\n              dispatch(\r\n                queryAction(arg, { subscribe: false, forceRefetch: true })\r\n              ),\r\n            unsubscribe() {\r\n              if (subscribe)\r\n                dispatch(\r\n                  unsubscribeQueryResult({\r\n                    queryCacheKey,\r\n                    requestId,\r\n                  })\r\n                )\r\n            },\r\n            updateSubscriptionOptions(options: SubscriptionOptions) {\r\n              statePromise.subscriptionOptions = options\r\n              dispatch(\r\n                updateSubscriptionOptions({\r\n                  endpointName,\r\n                  requestId,\r\n                  queryCacheKey,\r\n                  options,\r\n                })\r\n              )\r\n            },\r\n          }\r\n        )\r\n\r\n        if (!runningQuery && !skippedSynchronously && !forceQueryFn) {\r\n          const running = runningQueries.get(dispatch) || {}\r\n          running[queryCacheKey] = statePromise\r\n          runningQueries.set(dispatch, running)\r\n\r\n          statePromise.then(() => {\r\n            delete running[queryCacheKey]\r\n            if (!Object.keys(running).length) {\r\n              runningQueries.delete(dispatch)\r\n            }\r\n          })\r\n        }\r\n\r\n        return statePromise\r\n      }\r\n    return queryAction\r\n  }\r\n\r\n  function buildInitiateMutation(\r\n    endpointName: string\r\n  ): StartMutationActionCreator<any> {\r\n    return (arg, { track = true, fixedCacheKey } = {}) =>\r\n      (dispatch, getState) => {\r\n        const thunk = mutationThunk({\r\n          type: 'mutation',\r\n          endpointName,\r\n          originalArgs: arg,\r\n          track,\r\n          fixedCacheKey,\r\n        })\r\n        const thunkResult = dispatch(thunk)\r\n        middlewareWarning(dispatch)\r\n        const { requestId, abort, unwrap } = thunkResult\r\n        const returnValuePromise = thunkResult\r\n          .unwrap()\r\n          .then((data) => ({ data }))\r\n          .catch((error) => ({ error }))\r\n\r\n        const reset = () => {\r\n          dispatch(removeMutationResult({ requestId, fixedCacheKey }))\r\n        }\r\n\r\n        const ret = Object.assign(returnValuePromise, {\r\n          arg: thunkResult.arg,\r\n          requestId,\r\n          abort,\r\n          unwrap,\r\n          unsubscribe: reset,\r\n          reset,\r\n        })\r\n\r\n        const running = runningMutations.get(dispatch) || {}\r\n        runningMutations.set(dispatch, running)\r\n        running[requestId] = ret\r\n        ret.then(() => {\r\n          delete running[requestId]\r\n          if (!Object.keys(running).length) {\r\n            runningMutations.delete(dispatch)\r\n          }\r\n        })\r\n        if (fixedCacheKey) {\r\n          running[fixedCacheKey] = ret\r\n          ret.then(() => {\r\n            if (running[fixedCacheKey] === ret) {\r\n              delete running[fixedCacheKey]\r\n              if (!Object.keys(running).length) {\r\n                runningMutations.delete(dispatch)\r\n              }\r\n            }\r\n          })\r\n        }\r\n\r\n        return ret\r\n      }\r\n  }\r\n}\r\n", "import type { InternalSerializeQueryArgs } from '../defaultSerializeQueryArgs'\r\nimport type { Api, ApiContext } from '../apiTypes'\r\nimport type {\r\n  BaseQueryFn,\r\n  BaseQueryError,\r\n  QueryReturnValue,\r\n} from '../baseQueryTypes'\r\nimport type { RootState, QueryKeys, QuerySubstateIdentifier } from './apiState'\r\nimport { QueryStatus } from './apiState'\r\nimport type {\r\n  StartQueryActionCreatorOptions,\r\n  QueryActionCreatorResult,\r\n} from './buildInitiate'\r\nimport { forceQueryFnSymbol, isUpsertQuery } from './buildInitiate'\r\nimport type {\r\n  AssertTagTypes,\r\n  EndpointDefinition,\r\n  EndpointDefinitions,\r\n  MutationDefinition,\r\n  QueryArgFrom,\r\n  QueryDefinition,\r\n  ResultTypeFrom,\r\n  FullTagDescription,\r\n} from '../endpointDefinitions'\r\nimport { isQueryDefinition } from '../endpointDefinitions'\r\nimport { calculateProvidedBy } from '../endpointDefinitions'\r\nimport type { AsyncThunkPayloadCreator, Draft } from '@reduxjs/toolkit'\r\nimport {\r\n  isAllOf,\r\n  isFulfilled,\r\n  isPending,\r\n  isRejected,\r\n  isRejectedWithValue,\r\n} from '@reduxjs/toolkit'\r\nimport type { Patch } from 'immer'\r\nimport { isDraftable, produceWithPatches } from 'immer'\r\nimport type {\r\n  AnyAction,\r\n  ThunkAction,\r\n  ThunkDispatch,\r\n  AsyncThunk,\r\n} from '@reduxjs/toolkit'\r\nimport { createAsyncThunk, SHOULD_AUTOBATCH } from '@reduxjs/toolkit'\r\n\r\nimport { HandledError } from '../HandledError'\r\n\r\nimport type { ApiEndpointQuery, PrefetchOptions } from './module'\r\nimport type { UnwrapPromise } from '../tsHelpers'\r\n\r\ndeclare module './module' {\r\n  export interface ApiEndpointQuery<\r\n    Definition extends QueryDefinition<any, any, any, any, any>,\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    Definitions extends EndpointDefinitions\r\n  > extends Matchers<QueryThunk, Definition> {}\r\n\r\n  export interface ApiEndpointMutation<\r\n    Definition extends MutationDefinition<any, any, any, any, any>,\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    Definitions extends EndpointDefinitions\r\n  > extends Matchers<MutationThunk, Definition> {}\r\n}\r\n\r\ntype EndpointThunk<\r\n  Thunk extends QueryThunk | MutationThunk,\r\n  Definition extends EndpointDefinition<any, any, any, any>\r\n> = Definition extends EndpointDefinition<\r\n  infer QueryArg,\r\n  infer BaseQueryFn,\r\n  any,\r\n  infer ResultType\r\n>\r\n  ? Thunk extends AsyncThunk<unknown, infer ATArg, infer ATConfig>\r\n    ? AsyncThunk<\r\n        ResultType,\r\n        ATArg & { originalArgs: QueryArg },\r\n        ATConfig & { rejectValue: BaseQueryError<BaseQueryFn> }\r\n      >\r\n    : never\r\n  : never\r\n\r\nexport type PendingAction<\r\n  Thunk extends QueryThunk | MutationThunk,\r\n  Definition extends EndpointDefinition<any, any, any, any>\r\n> = ReturnType<EndpointThunk<Thunk, Definition>['pending']>\r\n\r\nexport type FulfilledAction<\r\n  Thunk extends QueryThunk | MutationThunk,\r\n  Definition extends EndpointDefinition<any, any, any, any>\r\n> = ReturnType<EndpointThunk<Thunk, Definition>['fulfilled']>\r\n\r\nexport type RejectedAction<\r\n  Thunk extends QueryThunk | MutationThunk,\r\n  Definition extends EndpointDefinition<any, any, any, any>\r\n> = ReturnType<EndpointThunk<Thunk, Definition>['rejected']>\r\n\r\nexport type Matcher<M> = (value: any) => value is M\r\n\r\nexport interface Matchers<\r\n  Thunk extends QueryThunk | MutationThunk,\r\n  Definition extends EndpointDefinition<any, any, any, any>\r\n> {\r\n  matchPending: Matcher<PendingAction<Thunk, Definition>>\r\n  matchFulfilled: Matcher<FulfilledAction<Thunk, Definition>>\r\n  matchRejected: Matcher<RejectedAction<Thunk, Definition>>\r\n}\r\n\r\nexport interface QueryThunkArg\r\n  extends QuerySubstateIdentifier,\r\n    StartQueryActionCreatorOptions {\r\n  type: 'query'\r\n  originalArgs: unknown\r\n  endpointName: string\r\n}\r\n\r\nexport interface MutationThunkArg {\r\n  type: 'mutation'\r\n  originalArgs: unknown\r\n  endpointName: string\r\n  track?: boolean\r\n  fixedCacheKey?: string\r\n}\r\n\r\nexport type ThunkResult = unknown\r\n\r\nexport type ThunkApiMetaConfig = {\r\n  pendingMeta: {\r\n    startedTimeStamp: number\r\n    [SHOULD_AUTOBATCH]: true\r\n  }\r\n  fulfilledMeta: {\r\n    fulfilledTimeStamp: number\r\n    baseQueryMeta: unknown\r\n    [SHOULD_AUTOBATCH]: true\r\n  }\r\n  rejectedMeta: {\r\n    baseQueryMeta: unknown\r\n    [SHOULD_AUTOBATCH]: true\r\n  }\r\n}\r\nexport type QueryThunk = AsyncThunk<\r\n  ThunkResult,\r\n  QueryThunkArg,\r\n  ThunkApiMetaConfig\r\n>\r\nexport type MutationThunk = AsyncThunk<\r\n  ThunkResult,\r\n  MutationThunkArg,\r\n  ThunkApiMetaConfig\r\n>\r\n\r\nfunction defaultTransformResponse(baseQueryReturnValue: unknown) {\r\n  return baseQueryReturnValue\r\n}\r\n\r\nexport type MaybeDrafted<T> = T | Draft<T>\r\nexport type Recipe<T> = (data: MaybeDrafted<T>) => void | MaybeDrafted<T>\r\nexport type UpsertRecipe<T> = (\r\n  data: MaybeDrafted<T> | undefined\r\n) => void | MaybeDrafted<T>\r\n\r\nexport type PatchQueryDataThunk<\r\n  Definitions extends EndpointDefinitions,\r\n  PartialState\r\n> = <EndpointName extends QueryKeys<Definitions>>(\r\n  endpointName: EndpointName,\r\n  args: QueryArgFrom<Definitions[EndpointName]>,\r\n  patches: readonly Patch[],\r\n  updateProvided?: boolean\r\n) => ThunkAction<void, PartialState, any, AnyAction>\r\n\r\nexport type UpdateQueryDataThunk<\r\n  Definitions extends EndpointDefinitions,\r\n  PartialState\r\n> = <EndpointName extends QueryKeys<Definitions>>(\r\n  endpointName: EndpointName,\r\n  args: QueryArgFrom<Definitions[EndpointName]>,\r\n  updateRecipe: Recipe<ResultTypeFrom<Definitions[EndpointName]>>,\r\n  updateProvided?: boolean\r\n) => ThunkAction<PatchCollection, PartialState, any, AnyAction>\r\n\r\nexport type UpsertQueryDataThunk<\r\n  Definitions extends EndpointDefinitions,\r\n  PartialState\r\n> = <EndpointName extends QueryKeys<Definitions>>(\r\n  endpointName: EndpointName,\r\n  args: QueryArgFrom<Definitions[EndpointName]>,\r\n  value: ResultTypeFrom<Definitions[EndpointName]>\r\n) => ThunkAction<\r\n  QueryActionCreatorResult<\r\n    Definitions[EndpointName] extends QueryDefinition<any, any, any, any>\r\n      ? Definitions[EndpointName]\r\n      : never\r\n  >,\r\n  PartialState,\r\n  any,\r\n  AnyAction\r\n>\r\n\r\n/**\r\n * An object returned from dispatching a `api.util.updateQueryData` call.\r\n */\r\nexport type PatchCollection = {\r\n  /**\r\n   * An `immer` Patch describing the cache update.\r\n   */\r\n  patches: Patch[]\r\n  /**\r\n   * An `immer` Patch to revert the cache update.\r\n   */\r\n  inversePatches: Patch[]\r\n  /**\r\n   * A function that will undo the cache update.\r\n   */\r\n  undo: () => void\r\n}\r\n\r\nexport function buildThunks<\r\n  BaseQuery extends BaseQueryFn,\r\n  ReducerPath extends string,\r\n  Definitions extends EndpointDefinitions\r\n>({\r\n  reducerPath,\r\n  baseQuery,\r\n  context: { endpointDefinitions },\r\n  serializeQueryArgs,\r\n  api,\r\n  assertTagType,\r\n}: {\r\n  baseQuery: BaseQuery\r\n  reducerPath: ReducerPath\r\n  context: ApiContext<Definitions>\r\n  serializeQueryArgs: InternalSerializeQueryArgs\r\n  api: Api<BaseQuery, Definitions, ReducerPath, any>\r\n  assertTagType: AssertTagTypes\r\n}) {\r\n  type State = RootState<any, string, ReducerPath>\r\n\r\n  const patchQueryData: PatchQueryDataThunk<EndpointDefinitions, State> =\r\n    (endpointName, args, patches, updateProvided) => (dispatch, getState) => {\r\n      const endpointDefinition = endpointDefinitions[endpointName]\r\n\r\n      const queryCacheKey = serializeQueryArgs({\r\n        queryArgs: args,\r\n        endpointDefinition,\r\n        endpointName,\r\n      })\r\n\r\n      dispatch(\r\n        api.internalActions.queryResultPatched({ queryCacheKey, patches })\r\n      )\r\n\r\n      if (!updateProvided) {\r\n        return\r\n      }\r\n\r\n      const newValue = api.endpoints[endpointName].select(args)(\r\n        // Work around TS 4.1 mismatch\r\n        getState() as RootState<any, any, any>\r\n      )\r\n\r\n      const providedTags = calculateProvidedBy(\r\n        endpointDefinition.providesTags,\r\n        newValue.data,\r\n        undefined,\r\n        args,\r\n        {},\r\n        assertTagType\r\n      )\r\n\r\n      dispatch(\r\n        api.internalActions.updateProvidedBy({ queryCacheKey, providedTags })\r\n      )\r\n    }\r\n\r\n  const updateQueryData: UpdateQueryDataThunk<EndpointDefinitions, State> =\r\n    (endpointName, args, updateRecipe, updateProvided = true) =>\r\n    (dispatch, getState) => {\r\n      const endpointDefinition = api.endpoints[endpointName]\r\n\r\n      const currentState = endpointDefinition.select(args)(\r\n        // Work around TS 4.1 mismatch\r\n        getState() as RootState<any, any, any>\r\n      )\r\n\r\n      let ret: PatchCollection = {\r\n        patches: [],\r\n        inversePatches: [],\r\n        undo: () =>\r\n          dispatch(\r\n            api.util.patchQueryData(\r\n              endpointName,\r\n              args,\r\n              ret.inversePatches,\r\n              updateProvided\r\n            )\r\n          ),\r\n      }\r\n      if (currentState.status === QueryStatus.uninitialized) {\r\n        return ret\r\n      }\r\n      let newValue\r\n      if ('data' in currentState) {\r\n        if (isDraftable(currentState.data)) {\r\n          const [value, patches, inversePatches] = produceWithPatches(\r\n            currentState.data,\r\n            updateRecipe\r\n          )\r\n          ret.patches.push(...patches)\r\n          ret.inversePatches.push(...inversePatches)\r\n          newValue = value\r\n        } else {\r\n          newValue = updateRecipe(currentState.data)\r\n          ret.patches.push({ op: 'replace', path: [], value: newValue })\r\n          ret.inversePatches.push({\r\n            op: 'replace',\r\n            path: [],\r\n            value: currentState.data,\r\n          })\r\n        }\r\n      }\r\n\r\n      dispatch(\r\n        api.util.patchQueryData(endpointName, args, ret.patches, updateProvided)\r\n      )\r\n\r\n      return ret\r\n    }\r\n\r\n  const upsertQueryData: UpsertQueryDataThunk<Definitions, State> =\r\n    (endpointName, args, value) => (dispatch) => {\r\n      return dispatch(\r\n        (\r\n          api.endpoints[endpointName] as ApiEndpointQuery<\r\n            QueryDefinition<any, any, any, any, any>,\r\n            Definitions\r\n          >\r\n        ).initiate(args, {\r\n          subscribe: false,\r\n          forceRefetch: true,\r\n          [forceQueryFnSymbol]: () => ({\r\n            data: value,\r\n          }),\r\n        })\r\n      )\r\n    }\r\n\r\n  const executeEndpoint: AsyncThunkPayloadCreator<\r\n    ThunkResult,\r\n    QueryThunkArg | MutationThunkArg,\r\n    ThunkApiMetaConfig & { state: RootState<any, string, ReducerPath> }\r\n  > = async (\r\n    arg,\r\n    {\r\n      signal,\r\n      abort,\r\n      rejectWithValue,\r\n      fulfillWithValue,\r\n      dispatch,\r\n      getState,\r\n      extra,\r\n    }\r\n  ) => {\r\n    const endpointDefinition = endpointDefinitions[arg.endpointName]\r\n\r\n    try {\r\n      let transformResponse: (\r\n        baseQueryReturnValue: any,\r\n        meta: any,\r\n        arg: any\r\n      ) => any = defaultTransformResponse\r\n      let result: QueryReturnValue\r\n      const baseQueryApi = {\r\n        signal,\r\n        abort,\r\n        dispatch,\r\n        getState,\r\n        extra,\r\n        endpoint: arg.endpointName,\r\n        type: arg.type,\r\n        forced:\r\n          arg.type === 'query' ? isForcedQuery(arg, getState()) : undefined,\r\n      }\r\n\r\n      const forceQueryFn =\r\n        arg.type === 'query' ? arg[forceQueryFnSymbol] : undefined\r\n      if (forceQueryFn) {\r\n        result = forceQueryFn()\r\n      } else if (endpointDefinition.query) {\r\n        result = await baseQuery(\r\n          endpointDefinition.query(arg.originalArgs),\r\n          baseQueryApi,\r\n          endpointDefinition.extraOptions as any\r\n        )\r\n\r\n        if (endpointDefinition.transformResponse) {\r\n          transformResponse = endpointDefinition.transformResponse\r\n        }\r\n      } else {\r\n        result = await endpointDefinition.queryFn(\r\n          arg.originalArgs,\r\n          baseQueryApi,\r\n          endpointDefinition.extraOptions as any,\r\n          (arg) =>\r\n            baseQuery(arg, baseQueryApi, endpointDefinition.extraOptions as any)\r\n        )\r\n      }\r\n      if (\r\n        typeof process !== 'undefined' &&\r\n        process.env.NODE_ENV === 'development'\r\n      ) {\r\n        const what = endpointDefinition.query ? '`baseQuery`' : '`queryFn`'\r\n        let err: undefined | string\r\n        if (!result) {\r\n          err = `${what} did not return anything.`\r\n        } else if (typeof result !== 'object') {\r\n          err = `${what} did not return an object.`\r\n        } else if (result.error && result.data) {\r\n          err = `${what} returned an object containing both \\`error\\` and \\`result\\`.`\r\n        } else if (result.error === undefined && result.data === undefined) {\r\n          err = `${what} returned an object containing neither a valid \\`error\\` and \\`result\\`. At least one of them should not be \\`undefined\\``\r\n        } else {\r\n          for (const key of Object.keys(result)) {\r\n            if (key !== 'error' && key !== 'data' && key !== 'meta') {\r\n              err = `The object returned by ${what} has the unknown property ${key}.`\r\n              break\r\n            }\r\n          }\r\n        }\r\n        if (err) {\r\n          console.error(\r\n            `Error encountered handling the endpoint ${arg.endpointName}.\r\n              ${err}\r\n              It needs to return an object with either the shape \\`{ data: <value> }\\` or \\`{ error: <value> }\\` that may contain an optional \\`meta\\` property.\r\n              Object returned was:`,\r\n            result\r\n          )\r\n        }\r\n      }\r\n\r\n      if (result.error) throw new HandledError(result.error, result.meta)\r\n\r\n      return fulfillWithValue(\r\n        await transformResponse(result.data, result.meta, arg.originalArgs),\r\n        {\r\n          fulfilledTimeStamp: Date.now(),\r\n          baseQueryMeta: result.meta,\r\n          [SHOULD_AUTOBATCH]: true,\r\n        }\r\n      )\r\n    } catch (error) {\r\n      let catchedError = error\r\n      if (catchedError instanceof HandledError) {\r\n        let transformErrorResponse: (\r\n          baseQueryReturnValue: any,\r\n          meta: any,\r\n          arg: any\r\n        ) => any = defaultTransformResponse\r\n\r\n        if (\r\n          endpointDefinition.query &&\r\n          endpointDefinition.transformErrorResponse\r\n        ) {\r\n          transformErrorResponse = endpointDefinition.transformErrorResponse\r\n        }\r\n        try {\r\n          return rejectWithValue(\r\n            await transformErrorResponse(\r\n              catchedError.value,\r\n              catchedError.meta,\r\n              arg.originalArgs\r\n            ),\r\n            { baseQueryMeta: catchedError.meta, [SHOULD_AUTOBATCH]: true }\r\n          )\r\n        } catch (e) {\r\n          catchedError = e\r\n        }\r\n      }\r\n      if (\r\n        typeof process !== 'undefined' &&\r\n        process.env.NODE_ENV !== 'production'\r\n      ) {\r\n        console.error(\r\n          `An unhandled error occurred processing a request for the endpoint \"${arg.endpointName}\".\r\nIn the case of an unhandled error, no tags will be \"provided\" or \"invalidated\".`,\r\n          catchedError\r\n        )\r\n      } else {\r\n        console.error(catchedError)\r\n      }\r\n      throw catchedError\r\n    }\r\n  }\r\n\r\n  function isForcedQuery(\r\n    arg: QueryThunkArg,\r\n    state: RootState<any, string, ReducerPath>\r\n  ) {\r\n    const requestState = state[reducerPath]?.queries?.[arg.queryCacheKey]\r\n    const baseFetchOnMountOrArgChange =\r\n      state[reducerPath]?.config.refetchOnMountOrArgChange\r\n\r\n    const fulfilledVal = requestState?.fulfilledTimeStamp\r\n    const refetchVal =\r\n      arg.forceRefetch ?? (arg.subscribe && baseFetchOnMountOrArgChange)\r\n\r\n    if (refetchVal) {\r\n      // Return if its true or compare the dates because it must be a number\r\n      return (\r\n        refetchVal === true ||\r\n        (Number(new Date()) - Number(fulfilledVal)) / 1000 >= refetchVal\r\n      )\r\n    }\r\n    return false\r\n  }\r\n\r\n  const queryThunk = createAsyncThunk<\r\n    ThunkResult,\r\n    QueryThunkArg,\r\n    ThunkApiMetaConfig & { state: RootState<any, string, ReducerPath> }\r\n  >(`${reducerPath}/executeQuery`, executeEndpoint, {\r\n    getPendingMeta() {\r\n      return { startedTimeStamp: Date.now(), [SHOULD_AUTOBATCH]: true }\r\n    },\r\n    condition(queryThunkArgs, { getState }) {\r\n      const state = getState()\r\n\r\n      const requestState =\r\n        state[reducerPath]?.queries?.[queryThunkArgs.queryCacheKey]\r\n      const fulfilledVal = requestState?.fulfilledTimeStamp\r\n      const currentArg = queryThunkArgs.originalArgs\r\n      const previousArg = requestState?.originalArgs\r\n      const endpointDefinition =\r\n        endpointDefinitions[queryThunkArgs.endpointName]\r\n\r\n      // Order of these checks matters.\r\n      // In order for `upsertQueryData` to successfully run while an existing request is in flight,\r\n      /// we have to check for that first, otherwise `queryThunk` will bail out and not run at all.\r\n      if (isUpsertQuery(queryThunkArgs)) {\r\n        return true\r\n      }\r\n\r\n      // Don't retry a request that's currently in-flight\r\n      if (requestState?.status === 'pending') {\r\n        return false\r\n      }\r\n\r\n      // if this is forced, continue\r\n      if (isForcedQuery(queryThunkArgs, state)) {\r\n        return true\r\n      }\r\n\r\n      if (\r\n        isQueryDefinition(endpointDefinition) &&\r\n        endpointDefinition?.forceRefetch?.({\r\n          currentArg,\r\n          previousArg,\r\n          endpointState: requestState,\r\n          state,\r\n        })\r\n      ) {\r\n        return true\r\n      }\r\n\r\n      // Pull from the cache unless we explicitly force refetch or qualify based on time\r\n      if (fulfilledVal) {\r\n        // Value is cached and we didn't specify to refresh, skip it.\r\n        return false\r\n      }\r\n\r\n      return true\r\n    },\r\n    dispatchConditionRejection: true,\r\n  })\r\n\r\n  const mutationThunk = createAsyncThunk<\r\n    ThunkResult,\r\n    MutationThunkArg,\r\n    ThunkApiMetaConfig & { state: RootState<any, string, ReducerPath> }\r\n  >(`${reducerPath}/executeMutation`, executeEndpoint, {\r\n    getPendingMeta() {\r\n      return { startedTimeStamp: Date.now(), [SHOULD_AUTOBATCH]: true }\r\n    },\r\n  })\r\n\r\n  const hasTheForce = (options: any): options is { force: boolean } =>\r\n    'force' in options\r\n  const hasMaxAge = (\r\n    options: any\r\n  ): options is { ifOlderThan: false | number } => 'ifOlderThan' in options\r\n\r\n  const prefetch =\r\n    <EndpointName extends QueryKeys<Definitions>>(\r\n      endpointName: EndpointName,\r\n      arg: any,\r\n      options: PrefetchOptions\r\n    ): ThunkAction<void, any, any, AnyAction> =>\r\n    (dispatch: ThunkDispatch<any, any, any>, getState: () => any) => {\r\n      const force = hasTheForce(options) && options.force\r\n      const maxAge = hasMaxAge(options) && options.ifOlderThan\r\n\r\n      const queryAction = (force: boolean = true) =>\r\n        (api.endpoints[endpointName] as ApiEndpointQuery<any, any>).initiate(\r\n          arg,\r\n          { forceRefetch: force }\r\n        )\r\n      const latestStateValue = (\r\n        api.endpoints[endpointName] as ApiEndpointQuery<any, any>\r\n      ).select(arg)(getState())\r\n\r\n      if (force) {\r\n        dispatch(queryAction())\r\n      } else if (maxAge) {\r\n        const lastFulfilledTs = latestStateValue?.fulfilledTimeStamp\r\n        if (!lastFulfilledTs) {\r\n          dispatch(queryAction())\r\n          return\r\n        }\r\n        const shouldRetrigger =\r\n          (Number(new Date()) - Number(new Date(lastFulfilledTs))) / 1000 >=\r\n          maxAge\r\n        if (shouldRetrigger) {\r\n          dispatch(queryAction())\r\n        }\r\n      } else {\r\n        // If prefetching with no options, just let it try\r\n        dispatch(queryAction(false))\r\n      }\r\n    }\r\n\r\n  function matchesEndpoint(endpointName: string) {\r\n    return (action: any): action is AnyAction =>\r\n      action?.meta?.arg?.endpointName === endpointName\r\n  }\r\n\r\n  function buildMatchThunkActions<\r\n    Thunk extends\r\n      | AsyncThunk<any, QueryThunkArg, ThunkApiMetaConfig>\r\n      | AsyncThunk<any, MutationThunkArg, ThunkApiMetaConfig>\r\n  >(thunk: Thunk, endpointName: string) {\r\n    return {\r\n      matchPending: isAllOf(isPending(thunk), matchesEndpoint(endpointName)),\r\n      matchFulfilled: isAllOf(\r\n        isFulfilled(thunk),\r\n        matchesEndpoint(endpointName)\r\n      ),\r\n      matchRejected: isAllOf(isRejected(thunk), matchesEndpoint(endpointName)),\r\n    } as Matchers<Thunk, any>\r\n  }\r\n\r\n  return {\r\n    queryThunk,\r\n    mutationThunk,\r\n    prefetch,\r\n    updateQueryData,\r\n    upsertQueryData,\r\n    patchQueryData,\r\n    buildMatchThunkActions,\r\n  }\r\n}\r\n\r\nexport function calculateProvidedByThunk(\r\n  action: UnwrapPromise<\r\n    ReturnType<ReturnType<QueryThunk>> | ReturnType<ReturnType<MutationThunk>>\r\n  >,\r\n  type: 'providesTags' | 'invalidatesTags',\r\n  endpointDefinitions: EndpointDefinitions,\r\n  assertTagType: AssertTagTypes\r\n) {\r\n  return calculateProvidedBy(\r\n    endpointDefinitions[action.meta.arg.endpointName][type],\r\n    isFulfilled(action) ? action.payload : undefined,\r\n    isRejectedWithValue(action) ? action.payload : undefined,\r\n    action.meta.arg.originalArgs,\r\n    'baseQueryMeta' in action.meta ? action.meta.baseQueryMeta : undefined,\r\n    assertTagType\r\n  )\r\n}\r\n", "import type { QueryCacheKey } from './core/apiState'\r\nimport type { EndpointDefinition } from './endpointDefinitions'\r\nimport { isPlainObject } from '@reduxjs/toolkit'\r\n\r\nconst cache: WeakMap<any, string> | undefined = WeakMap\r\n  ? new WeakMap()\r\n  : undefined\r\n\r\nexport const defaultSerializeQueryArgs: SerializeQueryArgs<any> = ({\r\n  endpointName,\r\n  queryArgs,\r\n}) => {\r\n  let serialized = ''\r\n\r\n  const cached = cache?.get(queryArgs)\r\n\r\n  if (typeof cached === 'string') {\r\n    serialized = cached\r\n  } else {\r\n    const stringified = JSON.stringify(queryArgs, (key, value) =>\r\n      isPlainObject(value)\r\n        ? Object.keys(value)\r\n            .sort()\r\n            .reduce<any>((acc, key) => {\r\n              acc[key] = (value as any)[key]\r\n              return acc\r\n            }, {})\r\n        : value\r\n    )\r\n    if (isPlainObject(queryArgs)) {\r\n      cache?.set(queryArgs, stringified)\r\n    }\r\n    serialized = stringified\r\n  }\r\n  // Sort the object keys before stringifying, to prevent useQuery({ a: 1, b: 2 }) having a different cache key than useQuery({ b: 2, a: 1 })\r\n  return `${endpointName}(${serialized})`\r\n}\r\n\r\nexport type SerializeQueryArgs<QueryArgs, ReturnType = string> = (_: {\r\n  queryArgs: QueryArgs\r\n  endpointDefinition: EndpointDefinition<any, any, any, any>\r\n  endpointName: string\r\n}) => ReturnType\r\n\r\nexport type InternalSerializeQueryArgs = (_: {\r\n  queryArgs: any\r\n  endpointDefinition: EndpointDefinition<any, any, any, any>\r\n  endpointName: string\r\n}) => QueryCacheKey\r\n", "import type { <PERSON><PERSON>, <PERSON>pi<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>leName } from './apiTypes'\r\nimport type { CombinedState } from './core/apiState'\r\nimport type { BaseQueryArg, BaseQueryFn } from './baseQueryTypes'\r\nimport type { SerializeQueryArgs } from './defaultSerializeQueryArgs'\r\nimport { defaultSerializeQueryArgs } from './defaultSerializeQueryArgs'\r\nimport type {\r\n  EndpointBuilder,\r\n  EndpointDefinitions,\r\n} from './endpointDefinitions'\r\nimport { DefinitionType, isQueryDefinition } from './endpointDefinitions'\r\nimport { nanoid } from '@reduxjs/toolkit'\r\nimport type { AnyAction } from '@reduxjs/toolkit'\r\nimport type { NoInfer } from './tsHelpers'\r\nimport { defaultMemoize } from 'reselect'\r\n\r\nexport interface CreateApiOptions<\r\n  BaseQuery extends BaseQueryFn,\r\n  Definitions extends EndpointDefinitions,\r\n  ReducerPath extends string = 'api',\r\n  TagTypes extends string = never\r\n> {\r\n  /**\r\n   * The base query used by each endpoint if no `queryFn` option is specified. RTK Query exports a utility called [fetchBaseQuery](./fetchBaseQuery) as a lightweight wrapper around `fetch` for common use-cases. See [Customizing Queries](../../rtk-query/usage/customizing-queries) if `fetchBaseQuery` does not handle your requirements.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query'\r\n   *\r\n   * const api = createApi({\r\n   *   // highlight-start\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   // highlight-end\r\n   *   endpoints: (build) => ({\r\n   *     // ...endpoints\r\n   *   }),\r\n   * })\r\n   * ```\r\n   */\r\n  baseQuery: BaseQuery\r\n  /**\r\n   * An array of string tag type names. Specifying tag types is optional, but you should define them so that they can be used for caching and invalidation. When defining a tag type, you will be able to [provide](../../rtk-query/usage/automated-refetching#providing-tags) them with `providesTags` and [invalidate](../../rtk-query/usage/automated-refetching#invalidating-tags) them with `invalidatesTags` when configuring [endpoints](#endpoints).\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query'\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   // highlight-start\r\n   *   tagTypes: ['Post', 'User'],\r\n   *   // highlight-end\r\n   *   endpoints: (build) => ({\r\n   *     // ...endpoints\r\n   *   }),\r\n   * })\r\n   * ```\r\n   */\r\n  tagTypes?: readonly TagTypes[]\r\n  /**\r\n   * The `reducerPath` is a _unique_ key that your service will be mounted to in your store. If you call `createApi` more than once in your application, you will need to provide a unique value each time. Defaults to `'api'`.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"apis.js\"\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query';\r\n   *\r\n   * const apiOne = createApi({\r\n   *   // highlight-start\r\n   *   reducerPath: 'apiOne',\r\n   *   // highlight-end\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   endpoints: (builder) => ({\r\n   *     // ...endpoints\r\n   *   }),\r\n   * });\r\n   *\r\n   * const apiTwo = createApi({\r\n   *   // highlight-start\r\n   *   reducerPath: 'apiTwo',\r\n   *   // highlight-end\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   endpoints: (builder) => ({\r\n   *     // ...endpoints\r\n   *   }),\r\n   * });\r\n   * ```\r\n   */\r\n  reducerPath?: ReducerPath\r\n  /**\r\n   * Accepts a custom function if you have a need to change the creation of cache keys for any reason.\r\n   */\r\n  serializeQueryArgs?: SerializeQueryArgs<BaseQueryArg<BaseQuery>>\r\n  /**\r\n   * Endpoints are just a set of operations that you want to perform against your server. You define them as an object using the builder syntax. There are two basic endpoint types: [`query`](../../rtk-query/usage/queries) and [`mutation`](../../rtk-query/usage/mutations).\r\n   */\r\n  endpoints(\r\n    build: EndpointBuilder<BaseQuery, TagTypes, ReducerPath>\r\n  ): Definitions\r\n  /**\r\n   * Defaults to `60` _(this value is in seconds)_. This is how long RTK Query will keep your data cached for **after** the last component unsubscribes. For example, if you query an endpoint, then unmount the component, then mount another component that makes the same request within the given time frame, the most recent value will be served from the cache.\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"keepUnusedDataFor example\"\r\n   *\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * interface Post {\r\n   *   id: number\r\n   *   name: string\r\n   * }\r\n   * type PostsResponse = Post[]\r\n   *\r\n   * const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   endpoints: (build) => ({\r\n   *     getPosts: build.query<PostsResponse, void>({\r\n   *       query: () => 'posts',\r\n   *       // highlight-start\r\n   *       keepUnusedDataFor: 5\r\n   *       // highlight-end\r\n   *     })\r\n   *   })\r\n   * })\r\n   * ```\r\n   */\r\n  keepUnusedDataFor?: number\r\n  /**\r\n   * Defaults to `false`. This setting allows you to control whether if a cached result is already available RTK Query will only serve a cached result, or if it should `refetch` when set to `true` or if an adequate amount of time has passed since the last successful query result.\r\n   * - `false` - Will not cause a query to be performed _unless_ it does not exist yet.\r\n   * - `true` - Will always refetch when a new subscriber to a query is added. Behaves the same as calling the `refetch` callback or passing `forceRefetch: true` in the action creator.\r\n   * - `number` - **Value is in seconds**. If a number is provided and there is an existing query in the cache, it will compare the current time vs the last fulfilled timestamp, and only refetch if enough time has elapsed.\r\n   *\r\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\r\n   */\r\n  refetchOnMountOrArgChange?: boolean | number\r\n  /**\r\n   * Defaults to `false`. This setting allows you to control whether RTK Query will try to refetch all subscribed queries after the application window regains focus.\r\n   *\r\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\r\n   *\r\n   * Note: requires [`setupListeners`](./setupListeners) to have been called.\r\n   */\r\n  refetchOnFocus?: boolean\r\n  /**\r\n   * Defaults to `false`. This setting allows you to control whether RTK Query will try to refetch all subscribed queries after regaining a network connection.\r\n   *\r\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\r\n   *\r\n   * Note: requires [`setupListeners`](./setupListeners) to have been called.\r\n   */\r\n  refetchOnReconnect?: boolean\r\n  /**\r\n   * A function that is passed every dispatched action. If this returns something other than `undefined`,\r\n   * that return value will be used to rehydrate fulfilled & errored queries.\r\n   *\r\n   * @example\r\n   *\r\n   * ```ts\r\n   * // codeblock-meta title=\"next-redux-wrapper rehydration example\"\r\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\r\n   * import { HYDRATE } from 'next-redux-wrapper'\r\n   *\r\n   * export const api = createApi({\r\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\r\n   *   // highlight-start\r\n   *   extractRehydrationInfo(action, { reducerPath }) {\r\n   *     if (action.type === HYDRATE) {\r\n   *       return action.payload[reducerPath]\r\n   *     }\r\n   *   },\r\n   *   // highlight-end\r\n   *   endpoints: (build) => ({\r\n   *     // omitted\r\n   *   }),\r\n   * })\r\n   * ```\r\n   */\r\n  extractRehydrationInfo?: (\r\n    action: AnyAction,\r\n    {\r\n      reducerPath,\r\n    }: {\r\n      reducerPath: ReducerPath\r\n    }\r\n  ) =>\r\n    | undefined\r\n    | CombinedState<\r\n        NoInfer<Definitions>,\r\n        NoInfer<TagTypes>,\r\n        NoInfer<ReducerPath>\r\n      >\r\n}\r\n\r\nexport type CreateApi<Modules extends ModuleName> = {\r\n  /**\r\n   * Creates a service to use in your application. Contains only the basic redux logic (the core module).\r\n   *\r\n   * @link https://rtk-query-docs.netlify.app/api/createApi\r\n   */\r\n  <\r\n    BaseQuery extends BaseQueryFn,\r\n    Definitions extends EndpointDefinitions,\r\n    ReducerPath extends string = 'api',\r\n    TagTypes extends string = never\r\n  >(\r\n    options: CreateApiOptions<BaseQuery, Definitions, ReducerPath, TagTypes>\r\n  ): Api<BaseQuery, Definitions, ReducerPath, TagTypes, Modules>\r\n}\r\n\r\n/**\r\n * Builds a `createApi` method based on the provided `modules`.\r\n *\r\n * @link https://rtk-query-docs.netlify.app/concepts/customizing-create-api\r\n *\r\n * @example\r\n * ```ts\r\n * const MyContext = React.createContext<ReactReduxContextValue>(null as any);\r\n * const customCreateApi = buildCreateApi(\r\n *   coreModule(),\r\n *   reactHooksModule({ useDispatch: createDispatchHook(MyContext) })\r\n * );\r\n * ```\r\n *\r\n * @param modules - A variable number of modules that customize how the `createApi` method handles endpoints\r\n * @returns A `createApi` method using the provided `modules`.\r\n */\r\nexport function buildCreateApi<Modules extends [Module<any>, ...Module<any>[]]>(\r\n  ...modules: Modules\r\n): CreateApi<Modules[number]['name']> {\r\n  return function baseCreateApi(options) {\r\n    const extractRehydrationInfo = defaultMemoize((action: AnyAction) =>\r\n      options.extractRehydrationInfo?.(action, {\r\n        reducerPath: (options.reducerPath ?? 'api') as any,\r\n      })\r\n    )\r\n\r\n    const optionsWithDefaults: CreateApiOptions<any, any, any, any> = {\r\n      reducerPath: 'api',\r\n      keepUnusedDataFor: 60,\r\n      refetchOnMountOrArgChange: false,\r\n      refetchOnFocus: false,\r\n      refetchOnReconnect: false,\r\n      ...options,\r\n      extractRehydrationInfo,\r\n      serializeQueryArgs(queryArgsApi) {\r\n        let finalSerializeQueryArgs = defaultSerializeQueryArgs\r\n        if ('serializeQueryArgs' in queryArgsApi.endpointDefinition) {\r\n          const endpointSQA =\r\n            queryArgsApi.endpointDefinition.serializeQueryArgs!\r\n          finalSerializeQueryArgs = (queryArgsApi) => {\r\n            const initialResult = endpointSQA(queryArgsApi)\r\n            if (typeof initialResult === 'string') {\r\n              // If the user function returned a string, use it as-is\r\n              return initialResult\r\n            } else {\r\n              // Assume they returned an object (such as a subset of the original\r\n              // query args) or a primitive, and serialize it ourselves\r\n              return defaultSerializeQueryArgs({\r\n                ...queryArgsApi,\r\n                queryArgs: initialResult,\r\n              })\r\n            }\r\n          }\r\n        } else if (options.serializeQueryArgs) {\r\n          finalSerializeQueryArgs = options.serializeQueryArgs\r\n        }\r\n\r\n        return finalSerializeQueryArgs(queryArgsApi)\r\n      },\r\n      tagTypes: [...(options.tagTypes || [])],\r\n    }\r\n\r\n    const context: ApiContext<EndpointDefinitions> = {\r\n      endpointDefinitions: {},\r\n      batch(fn) {\r\n        // placeholder \"batch\" method to be overridden by plugins, for example with React.unstable_batchedUpdate\r\n        fn()\r\n      },\r\n      apiUid: nanoid(),\r\n      extractRehydrationInfo,\r\n      hasRehydrationInfo: defaultMemoize(\r\n        (action) => extractRehydrationInfo(action) != null\r\n      ),\r\n    }\r\n\r\n    const api = {\r\n      injectEndpoints,\r\n      enhanceEndpoints({ addTagTypes, endpoints }) {\r\n        if (addTagTypes) {\r\n          for (const eT of addTagTypes) {\r\n            if (!optionsWithDefaults.tagTypes!.includes(eT as any)) {\r\n              ;(optionsWithDefaults.tagTypes as any[]).push(eT)\r\n            }\r\n          }\r\n        }\r\n        if (endpoints) {\r\n          for (const [endpointName, partialDefinition] of Object.entries(\r\n            endpoints\r\n          )) {\r\n            if (typeof partialDefinition === 'function') {\r\n              partialDefinition(context.endpointDefinitions[endpointName])\r\n            } else {\r\n              Object.assign(\r\n                context.endpointDefinitions[endpointName] || {},\r\n                partialDefinition\r\n              )\r\n            }\r\n          }\r\n        }\r\n        return api\r\n      },\r\n    } as Api<BaseQueryFn, {}, string, string, Modules[number]['name']>\r\n\r\n    const initializedModules = modules.map((m) =>\r\n      m.init(api as any, optionsWithDefaults as any, context)\r\n    )\r\n\r\n    function injectEndpoints(\r\n      inject: Parameters<typeof api.injectEndpoints>[0]\r\n    ) {\r\n      const evaluatedEndpoints = inject.endpoints({\r\n        query: (x) => ({ ...x, type: DefinitionType.query } as any),\r\n        mutation: (x) => ({ ...x, type: DefinitionType.mutation } as any),\r\n      })\r\n\r\n      for (const [endpointName, definition] of Object.entries(\r\n        evaluatedEndpoints\r\n      )) {\r\n        if (\r\n          !inject.overrideExisting &&\r\n          endpointName in context.endpointDefinitions\r\n        ) {\r\n          if (\r\n            typeof process !== 'undefined' &&\r\n            process.env.NODE_ENV === 'development'\r\n          ) {\r\n            console.error(\r\n              `called \\`injectEndpoints\\` to override already-existing endpointName ${endpointName} without specifying \\`overrideExisting: true\\``\r\n            )\r\n          }\r\n\r\n          continue\r\n        }\r\n\r\n        context.endpointDefinitions[endpointName] = definition\r\n        for (const m of initializedModules) {\r\n          m.injectEndpoint(endpointName, definition)\r\n        }\r\n      }\r\n\r\n      return api as any\r\n    }\r\n\r\n    return api.injectEndpoints({ endpoints: options.endpoints as any })\r\n  }\r\n}\r\n", "import type { BaseQueryFn } from './baseQueryTypes'\r\n\r\nconst _NEVER = /* @__PURE__ */ Symbol()\r\nexport type NEVER = typeof _NEVER\r\n\r\n/**\r\n * Creates a \"fake\" baseQuery to be used if your api *only* uses the `queryFn` definition syntax.\r\n * This also allows you to specify a specific error type to be shared by all your `queryFn` definitions.\r\n */\r\nexport function fakeBaseQuery<ErrorType>(): BaseQueryFn<\r\n  void,\r\n  NEVER,\r\n  ErrorType,\r\n  {}\r\n> {\r\n  return function () {\r\n    throw new Error(\r\n      'When using `fakeBaseQuery`, all queries & mutations must use the `queryFn` definition syntax.'\r\n    )\r\n  }\r\n}\r\n", "import type { AnyAction, Middleware, ThunkDispatch } from '@reduxjs/toolkit'\r\nimport { createAction } from '@reduxjs/toolkit'\r\n\r\nimport type {\r\n  EndpointDefinitions,\r\n  FullTagDescription,\r\n} from '../../endpointDefinitions'\r\nimport type { QueryStatus, QuerySubState, RootState } from '../apiState'\r\nimport type { QueryThunkArg } from '../buildThunks'\r\nimport { buildCacheCollectionHandler } from './cacheCollection'\r\nimport { buildInvalidationByTagsHandler } from './invalidationByTags'\r\nimport { buildPollingHandler } from './polling'\r\nimport type {\r\n  BuildMiddlewareInput,\r\n  InternalHandlerBuilder,\r\n  InternalMiddlewareState,\r\n} from './types'\r\nimport { buildWindowEventHandler } from './windowEventHandling'\r\nimport { buildCacheLifecycleHandler } from './cacheLifecycle'\r\nimport { buildQueryLifecycleHandler } from './queryLifecycle'\r\nimport { buildDevCheckHandler } from './devMiddleware'\r\nimport { buildBatchedActionsHandler } from './batchActions'\r\n\r\nexport function buildMiddleware<\r\n  Definitions extends EndpointDefinitions,\r\n  ReducerPath extends string,\r\n  TagTypes extends string\r\n>(input: BuildMiddlewareInput<Definitions, ReducerPath, TagTypes>) {\r\n  const { reducerPath, queryThunk, api, context } = input\r\n  const { apiUid } = context\r\n\r\n  const actions = {\r\n    invalidateTags: createAction<\r\n      Array<TagTypes | FullTagDescription<TagTypes>>\r\n    >(`${reducerPath}/invalidateTags`),\r\n  }\r\n\r\n  const isThisApiSliceAction = (action: AnyAction) => {\r\n    return (\r\n      !!action &&\r\n      typeof action.type === 'string' &&\r\n      action.type.startsWith(`${reducerPath}/`)\r\n    )\r\n  }\r\n\r\n  const handlerBuilders: InternalHandlerBuilder[] = [\r\n    buildDevCheckHandler,\r\n    buildCacheCollectionHandler,\r\n    buildInvalidationByTagsHandler,\r\n    buildPollingHandler,\r\n    buildCacheLifecycleHandler,\r\n    buildQueryLifecycleHandler,\r\n  ]\r\n\r\n  const middleware: Middleware<\r\n    {},\r\n    RootState<Definitions, string, ReducerPath>,\r\n    ThunkDispatch<any, any, AnyAction>\r\n  > = (mwApi) => {\r\n    let initialized = false\r\n\r\n    let internalState: InternalMiddlewareState = {\r\n      currentSubscriptions: {},\r\n    }\r\n\r\n    const builderArgs = {\r\n      ...(input as any as BuildMiddlewareInput<\r\n        EndpointDefinitions,\r\n        string,\r\n        string\r\n      >),\r\n      internalState,\r\n      refetchQuery,\r\n    }\r\n\r\n    const handlers = handlerBuilders.map((build) => build(builderArgs))\r\n\r\n    const batchedActionsHandler = buildBatchedActionsHandler(builderArgs)\r\n    const windowEventsHandler = buildWindowEventHandler(builderArgs)\r\n\r\n    return (next) => {\r\n      return (action) => {\r\n        if (!initialized) {\r\n          initialized = true\r\n          // dispatch before any other action\r\n          mwApi.dispatch(api.internalActions.middlewareRegistered(apiUid))\r\n        }\r\n\r\n        const mwApiWithNext = { ...mwApi, next }\r\n\r\n        const stateBefore = mwApi.getState()\r\n\r\n        const [actionShouldContinue, hasSubscription] = batchedActionsHandler(\r\n          action,\r\n          mwApiWithNext,\r\n          stateBefore\r\n        )\r\n\r\n        let res: any\r\n\r\n        if (actionShouldContinue) {\r\n          res = next(action)\r\n        } else {\r\n          res = hasSubscription\r\n        }\r\n\r\n        if (!!mwApi.getState()[reducerPath]) {\r\n          // Only run these checks if the middleware is registered okay\r\n\r\n          // This looks for actions that aren't specific to the API slice\r\n          windowEventsHandler(action, mwApiWithNext, stateBefore)\r\n\r\n          if (\r\n            isThisApiSliceAction(action) ||\r\n            context.hasRehydrationInfo(action)\r\n          ) {\r\n            // Only run these additional checks if the actions are part of the API slice,\r\n            // or the action has hydration-related data\r\n            for (let handler of handlers) {\r\n              handler(action, mwApiWithNext, stateBefore)\r\n            }\r\n          }\r\n        }\r\n\r\n        return res\r\n      }\r\n    }\r\n  }\r\n\r\n  return { middleware, actions }\r\n\r\n  function refetchQuery(\r\n    querySubState: Exclude<\r\n      QuerySubState<any>,\r\n      { status: QueryStatus.uninitialized }\r\n    >,\r\n    queryCacheKey: string,\r\n    override: Partial<QueryThunkArg> = {}\r\n  ) {\r\n    return queryThunk({\r\n      type: 'query',\r\n      endpointName: querySubState.endpointName,\r\n      originalArgs: querySubState.originalArgs,\r\n      subscribe: false,\r\n      forceRefetch: true,\r\n      queryCacheKey: queryCacheKey as any,\r\n      ...override,\r\n    })\r\n  }\r\n}\r\n", "import type { BaseQueryFn } from '../../baseQueryTypes'\r\nimport type { QueryDefinition } from '../../endpointDefinitions'\r\nimport type { ConfigState, QueryCacheKey } from '../apiState'\r\nimport type {\r\n  QueryStateMeta,\r\n  SubMiddlewareApi,\r\n  TimeoutId,\r\n  InternalHandlerBuilder,\r\n  ApiMiddlewareInternalHandler,\r\n  InternalMiddlewareState,\r\n} from './types'\r\n\r\nexport type ReferenceCacheCollection = never\r\n\r\nfunction isObjectEmpty(obj: Record<any, any>) {\r\n  // Apparently a for..in loop is faster than `Object.keys()` here:\r\n  // https://stackoverflow.com/a/59787784/62937\r\n  for (let k in obj) {\r\n    // If there is at least one key, it's not empty\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\ndeclare module '../../endpointDefinitions' {\r\n  interface QueryExtraOptions<\r\n    TagTypes extends string,\r\n    ResultType,\r\n    QueryArg,\r\n    BaseQuery extends BaseQueryFn,\r\n    ReducerPath extends string = string\r\n  > {\r\n    /**\r\n     * Overrides the api-wide definition of `keepUnusedDataFor` for this endpoint only. _(This value is in seconds.)_\r\n     *\r\n     * This is how long RTK Query will keep your data cached for **after** the last component unsubscribes. For example, if you query an endpoint, then unmount the component, then mount another component that makes the same request within the given time frame, the most recent value will be served from the cache.\r\n     */\r\n    keepUnusedDataFor?: number\r\n  }\r\n}\r\n\r\n// Per https://developer.mozilla.org/en-US/docs/Web/API/setTimeout#maximum_delay_value , browsers store\r\n// `setTimeout()` timer values in a 32-bit int. If we pass a value in that's larger than that,\r\n// it wraps and ends up executing immediately.\r\n// Our `keepUnusedDataFor` values are in seconds, so adjust the numbers here accordingly.\r\nexport const THIRTY_TWO_BIT_MAX_INT = 2_147_483_647\r\nexport const THIRTY_TWO_BIT_MAX_TIMER_SECONDS = 2_147_483_647 / 1_000 - 1\r\n\r\nexport const buildCacheCollectionHandler: InternalHandlerBuilder = ({\r\n  reducerPath,\r\n  api,\r\n  context,\r\n  internalState,\r\n}) => {\r\n  const { removeQueryResult, unsubscribeQueryResult } = api.internalActions\r\n\r\n  function anySubscriptionsRemainingForKey(queryCacheKey: string) {\r\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey]\r\n    return !!subscriptions && !isObjectEmpty(subscriptions)\r\n  }\r\n\r\n  const currentRemovalTimeouts: QueryStateMeta<TimeoutId> = {}\r\n\r\n  const handler: ApiMiddlewareInternalHandler = (\r\n    action,\r\n    mwApi,\r\n    internalState\r\n  ) => {\r\n    if (unsubscribeQueryResult.match(action)) {\r\n      const state = mwApi.getState()[reducerPath]\r\n      const { queryCacheKey } = action.payload\r\n\r\n      handleUnsubscribe(\r\n        queryCacheKey,\r\n        state.queries[queryCacheKey]?.endpointName,\r\n        mwApi,\r\n        state.config\r\n      )\r\n    }\r\n\r\n    if (api.util.resetApiState.match(action)) {\r\n      for (const [key, timeout] of Object.entries(currentRemovalTimeouts)) {\r\n        if (timeout) clearTimeout(timeout)\r\n        delete currentRemovalTimeouts[key]\r\n      }\r\n    }\r\n\r\n    if (context.hasRehydrationInfo(action)) {\r\n      const state = mwApi.getState()[reducerPath]\r\n      const { queries } = context.extractRehydrationInfo(action)!\r\n      for (const [queryCacheKey, queryState] of Object.entries(queries)) {\r\n        // Gotcha:\r\n        // If rehydrating before the endpoint has been injected,the global `keepUnusedDataFor`\r\n        // will be used instead of the endpoint-specific one.\r\n        handleUnsubscribe(\r\n          queryCacheKey as QueryCacheKey,\r\n          queryState?.endpointName,\r\n          mwApi,\r\n          state.config\r\n        )\r\n      }\r\n    }\r\n  }\r\n\r\n  function handleUnsubscribe(\r\n    queryCacheKey: QueryCacheKey,\r\n    endpointName: string | undefined,\r\n    api: SubMiddlewareApi,\r\n    config: ConfigState<string>\r\n  ) {\r\n    const endpointDefinition = context.endpointDefinitions[\r\n      endpointName!\r\n    ] as QueryDefinition<any, any, any, any>\r\n    const keepUnusedDataFor =\r\n      endpointDefinition?.keepUnusedDataFor ?? config.keepUnusedDataFor\r\n\r\n    if (keepUnusedDataFor === Infinity) {\r\n      // Hey, user said keep this forever!\r\n      return\r\n    }\r\n    // Prevent `setTimeout` timers from overflowing a 32-bit internal int, by\r\n    // clamping the max value to be at most 1000ms less than the 32-bit max.\r\n    // Look, a 24.8-day keepalive ought to be enough for anybody, right? :)\r\n    // Also avoid negative values too.\r\n    const finalKeepUnusedDataFor = Math.max(\r\n      0,\r\n      Math.min(keepUnusedDataFor, THIRTY_TWO_BIT_MAX_TIMER_SECONDS)\r\n    )\r\n\r\n    if (!anySubscriptionsRemainingForKey(queryCacheKey)) {\r\n      const currentTimeout = currentRemovalTimeouts[queryCacheKey]\r\n      if (currentTimeout) {\r\n        clearTimeout(currentTimeout)\r\n      }\r\n      currentRemovalTimeouts[queryCacheKey] = setTimeout(() => {\r\n        if (!anySubscriptionsRemainingForKey(queryCacheKey)) {\r\n          api.dispatch(removeQueryResult({ queryCacheKey }))\r\n        }\r\n        delete currentRemovalTimeouts![queryCacheKey]\r\n      }, finalKeepUnusedDataFor * 1000)\r\n    }\r\n  }\r\n\r\n  return handler\r\n}\r\n", "import { isAnyOf, isFulfilled, isRejectedWithValue } from '@reduxjs/toolkit'\r\n\r\nimport type { FullTagDescription } from '../../endpointDefinitions'\r\nimport { calculateProvidedBy } from '../../endpointDefinitions'\r\nimport type { QueryCacheKey } from '../apiState'\r\nimport { QueryStatus } from '../apiState'\r\nimport { calculateProvidedByThunk } from '../buildThunks'\r\nimport type {\r\n  SubMiddlewareApi,\r\n  InternalHandlerBuilder,\r\n  ApiMiddlewareInternalHandler,\r\n} from './types'\r\n\r\nexport const buildInvalidationByTagsHandler: InternalHandlerBuilder = ({\r\n  reducerPath,\r\n  context,\r\n  context: { endpointDefinitions },\r\n  mutationThunk,\r\n  api,\r\n  assertTagType,\r\n  refetchQuery,\r\n}) => {\r\n  const { removeQueryResult } = api.internalActions\r\n  const isThunkActionWithTags = isAnyOf(\r\n    isFulfilled(mutationThunk),\r\n    isRejectedWithValue(mutationThunk)\r\n  )\r\n\r\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi) => {\r\n    if (isThunkActionWithTags(action)) {\r\n      invalidateTags(\r\n        calculateProvidedByThunk(\r\n          action,\r\n          'invalidatesTags',\r\n          endpointDefinitions,\r\n          assertTagType\r\n        ),\r\n        mwApi\r\n      )\r\n    }\r\n\r\n    if (api.util.invalidateTags.match(action)) {\r\n      invalidateTags(\r\n        calculateProvidedBy(\r\n          action.payload,\r\n          undefined,\r\n          undefined,\r\n          undefined,\r\n          undefined,\r\n          assertTagType\r\n        ),\r\n        mwApi\r\n      )\r\n    }\r\n  }\r\n\r\n  function invalidateTags(\r\n    tags: readonly FullTagDescription<string>[],\r\n    mwApi: SubMiddlewareApi\r\n  ) {\r\n    const rootState = mwApi.getState()\r\n    const state = rootState[reducerPath]\r\n\r\n    const toInvalidate = api.util.selectInvalidatedBy(rootState, tags)\r\n\r\n    context.batch(() => {\r\n      const valuesArray = Array.from(toInvalidate.values())\r\n      for (const { queryCacheKey } of valuesArray) {\r\n        const querySubState = state.queries[queryCacheKey]\r\n        const subscriptionSubState = state.subscriptions[queryCacheKey] ?? {}\r\n\r\n        if (querySubState) {\r\n          if (Object.keys(subscriptionSubState).length === 0) {\r\n            mwApi.dispatch(\r\n              removeQueryResult({\r\n                queryCacheKey: queryCacheKey as QueryCacheKey,\r\n              })\r\n            )\r\n          } else if (querySubState.status !== QueryStatus.uninitialized) {\r\n            mwApi.dispatch(refetchQuery(querySubState, queryCacheKey))\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  return handler\r\n}\r\n", "import type { QuerySubstateIdentifier, Subscribers } from '../apiState'\r\nimport { QueryStatus } from '../apiState'\r\nimport type {\r\n  QueryStateMeta,\r\n  SubMiddlewareApi,\r\n  TimeoutId,\r\n  InternalHandlerBuilder,\r\n  ApiMiddlewareInternalHandler,\r\n  InternalMiddlewareState,\r\n} from './types'\r\n\r\nexport const buildPollingHandler: InternalHandlerBuilder = ({\r\n  reducerPath,\r\n  queryThunk,\r\n  api,\r\n  refetchQuery,\r\n  internalState,\r\n}) => {\r\n  const currentPolls: QueryStateMeta<{\r\n    nextPollTimestamp: number\r\n    timeout?: TimeoutId\r\n    pollingInterval: number\r\n  }> = {}\r\n\r\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi) => {\r\n    if (\r\n      api.internalActions.updateSubscriptionOptions.match(action) ||\r\n      api.internalActions.unsubscribeQueryResult.match(action)\r\n    ) {\r\n      updatePollingInterval(action.payload, mwApi)\r\n    }\r\n\r\n    if (\r\n      queryThunk.pending.match(action) ||\r\n      (queryThunk.rejected.match(action) && action.meta.condition)\r\n    ) {\r\n      updatePollingInterval(action.meta.arg, mwApi)\r\n    }\r\n\r\n    if (\r\n      queryThunk.fulfilled.match(action) ||\r\n      (queryThunk.rejected.match(action) && !action.meta.condition)\r\n    ) {\r\n      startNextPoll(action.meta.arg, mwApi)\r\n    }\r\n\r\n    if (api.util.resetApiState.match(action)) {\r\n      clearPolls()\r\n    }\r\n  }\r\n\r\n  function startNextPoll(\r\n    { queryCacheKey }: QuerySubstateIdentifier,\r\n    api: SubMiddlewareApi\r\n  ) {\r\n    const state = api.getState()[reducerPath]\r\n    const querySubState = state.queries[queryCacheKey]\r\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey]\r\n\r\n    if (!querySubState || querySubState.status === QueryStatus.uninitialized)\r\n      return\r\n\r\n    const lowestPollingInterval = findLowestPollingInterval(subscriptions)\r\n    if (!Number.isFinite(lowestPollingInterval)) return\r\n\r\n    const currentPoll = currentPolls[queryCacheKey]\r\n\r\n    if (currentPoll?.timeout) {\r\n      clearTimeout(currentPoll.timeout)\r\n      currentPoll.timeout = undefined\r\n    }\r\n\r\n    const nextPollTimestamp = Date.now() + lowestPollingInterval\r\n\r\n    const currentInterval: typeof currentPolls[number] = (currentPolls[\r\n      queryCacheKey\r\n    ] = {\r\n      nextPollTimestamp,\r\n      pollingInterval: lowestPollingInterval,\r\n      timeout: setTimeout(() => {\r\n        currentInterval!.timeout = undefined\r\n        api.dispatch(refetchQuery(querySubState, queryCacheKey))\r\n      }, lowestPollingInterval),\r\n    })\r\n  }\r\n\r\n  function updatePollingInterval(\r\n    { queryCacheKey }: QuerySubstateIdentifier,\r\n    api: SubMiddlewareApi\r\n  ) {\r\n    const state = api.getState()[reducerPath]\r\n    const querySubState = state.queries[queryCacheKey]\r\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey]\r\n\r\n    if (!querySubState || querySubState.status === QueryStatus.uninitialized) {\r\n      return\r\n    }\r\n\r\n    const lowestPollingInterval = findLowestPollingInterval(subscriptions)\r\n\r\n    if (!Number.isFinite(lowestPollingInterval)) {\r\n      cleanupPollForKey(queryCacheKey)\r\n      return\r\n    }\r\n\r\n    const currentPoll = currentPolls[queryCacheKey]\r\n    const nextPollTimestamp = Date.now() + lowestPollingInterval\r\n\r\n    if (!currentPoll || nextPollTimestamp < currentPoll.nextPollTimestamp) {\r\n      startNextPoll({ queryCacheKey }, api)\r\n    }\r\n  }\r\n\r\n  function cleanupPollForKey(key: string) {\r\n    const existingPoll = currentPolls[key]\r\n    if (existingPoll?.timeout) {\r\n      clearTimeout(existingPoll.timeout)\r\n    }\r\n    delete currentPolls[key]\r\n  }\r\n\r\n  function clearPolls() {\r\n    for (const key of Object.keys(currentPolls)) {\r\n      cleanupPollForKey(key)\r\n    }\r\n  }\r\n\r\n  function findLowestPollingInterval(subscribers: Subscribers = {}) {\r\n    let lowestPollingInterval = Number.POSITIVE_INFINITY\r\n    for (let key in subscribers) {\r\n      if (!!subscribers[key].pollingInterval) {\r\n        lowestPollingInterval = Math.min(\r\n          subscribers[key].pollingInterval!,\r\n          lowestPollingInterval\r\n        )\r\n      }\r\n    }\r\n\r\n    return lowestPollingInterval\r\n  }\r\n  return handler\r\n}\r\n", "import { QueryStatus } from '../apiState'\r\nimport type { QueryCacheKey } from '../apiState'\r\nimport { onFocus, onOnline } from '../setupListeners'\r\nimport type {\r\n  ApiMiddlewareInternalHandler,\r\n  InternalHandlerBuilder,\r\n  SubMiddlewareApi,\r\n} from './types'\r\n\r\nexport const buildWindowEventHandler: InternalHandlerBuilder = ({\r\n  reducerPath,\r\n  context,\r\n  api,\r\n  refetchQuery,\r\n  internalState,\r\n}) => {\r\n  const { removeQueryResult } = api.internalActions\r\n\r\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi) => {\r\n    if (onFocus.match(action)) {\r\n      refetchValidQueries(mwApi, 'refetchOnFocus')\r\n    }\r\n    if (onOnline.match(action)) {\r\n      refetchValidQueries(mwApi, 'refetchOnReconnect')\r\n    }\r\n  }\r\n\r\n  function refetchValidQueries(\r\n    api: SubMiddlewareApi,\r\n    type: 'refetchOnFocus' | 'refetchOnReconnect'\r\n  ) {\r\n    const state = api.getState()[reducerPath]\r\n    const queries = state.queries\r\n    const subscriptions = internalState.currentSubscriptions\r\n\r\n    context.batch(() => {\r\n      for (const queryCacheKey of Object.keys(subscriptions)) {\r\n        const querySubState = queries[queryCacheKey]\r\n        const subscriptionSubState = subscriptions[queryCacheKey]\r\n\r\n        if (!subscriptionSubState || !querySubState) continue\r\n\r\n        const shouldRefetch =\r\n          Object.values(subscriptionSubState).some(\r\n            (sub) => sub[type] === true\r\n          ) ||\r\n          (Object.values(subscriptionSubState).every(\r\n            (sub) => sub[type] === undefined\r\n          ) &&\r\n            state.config[type])\r\n\r\n        if (shouldRefetch) {\r\n          if (Object.keys(subscriptionSubState).length === 0) {\r\n            api.dispatch(\r\n              removeQueryResult({\r\n                queryCacheKey: queryCacheKey as QueryCacheKey,\r\n              })\r\n            )\r\n          } else if (querySubState.status !== QueryStatus.uninitialized) {\r\n            api.dispatch(refetchQuery(querySubState, queryCacheKey))\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  return handler\r\n}\r\n", "import { isAsyncThunkAction, isFulfilled } from '@reduxjs/toolkit'\r\nimport type { AnyAction } from 'redux'\r\nimport type { ThunkDispatch } from 'redux-thunk'\r\nimport type { BaseQueryFn, BaseQueryMeta } from '../../baseQueryTypes'\r\nimport { DefinitionType } from '../../endpointDefinitions'\r\nimport type { RootState } from '../apiState'\r\nimport type {\r\n  MutationResultSelectorResult,\r\n  QueryResultSelectorResult,\r\n} from '../buildSelectors'\r\nimport { getMutationCacheKey } from '../buildSlice'\r\nimport type { PatchCollection, Recipe } from '../buildThunks'\r\nimport type {\r\n  Api<PERSON><PERSON><PERSON><PERSON>nternalH<PERSON><PERSON>,\r\n  InternalHandlerBuilder,\r\n  PromiseWithKnownReason,\r\n  SubMiddlewareApi,\r\n} from './types'\r\n\r\nexport type ReferenceCacheLifecycle = never\r\n\r\ndeclare module '../../endpointDefinitions' {\r\n  export interface QueryBaseLifecycleApi<\r\n    QueryArg,\r\n    BaseQuery extends BaseQueryFn,\r\n    ResultType,\r\n    ReducerPath extends string = string\r\n  > extends LifecycleApi<ReducerPath> {\r\n    /**\r\n     * Gets the current value of this cache entry.\r\n     */\r\n    getCacheEntry(): QueryResultSelectorResult<\r\n      { type: DefinitionType.query } & BaseEndpointDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        ResultType\r\n      >\r\n    >\r\n    /**\r\n     * Updates the current cache entry value.\r\n     * For documentation see `api.util.updateQueryData`.\r\n     */\r\n    updateCachedData(updateRecipe: Recipe<ResultType>): PatchCollection\r\n  }\r\n\r\n  export interface MutationBaseLifecycleApi<\r\n    QueryArg,\r\n    BaseQuery extends BaseQueryFn,\r\n    ResultType,\r\n    ReducerPath extends string = string\r\n  > extends LifecycleApi<ReducerPath> {\r\n    /**\r\n     * Gets the current value of this cache entry.\r\n     */\r\n    getCacheEntry(): MutationResultSelectorResult<\r\n      { type: DefinitionType.mutation } & BaseEndpointDefinition<\r\n        QueryArg,\r\n        BaseQuery,\r\n        ResultType\r\n      >\r\n    >\r\n  }\r\n\r\n  export interface LifecycleApi<ReducerPath extends string = string> {\r\n    /**\r\n     * The dispatch method for the store\r\n     */\r\n    dispatch: ThunkDispatch<any, any, AnyAction>\r\n    /**\r\n     * A method to get the current state\r\n     */\r\n    getState(): RootState<any, any, ReducerPath>\r\n    /**\r\n     * `extra` as provided as `thunk.extraArgument` to the `configureStore` `getDefaultMiddleware` option.\r\n     */\r\n    extra: unknown\r\n    /**\r\n     * A unique ID generated for the mutation\r\n     */\r\n    requestId: string\r\n  }\r\n\r\n  export interface CacheLifecyclePromises<\r\n    ResultType = unknown,\r\n    MetaType = unknown\r\n  > {\r\n    /**\r\n     * Promise that will resolve with the first value for this cache key.\r\n     * This allows you to `await` until an actual value is in cache.\r\n     *\r\n     * If the cache entry is removed from the cache before any value has ever\r\n     * been resolved, this Promise will reject with\r\n     * `new Error('Promise never resolved before cacheEntryRemoved.')`\r\n     * to prevent memory leaks.\r\n     * You can just re-throw that error (or not handle it at all) -\r\n     * it will be caught outside of `cacheEntryAdded`.\r\n     *\r\n     * If you don't interact with this promise, it will not throw.\r\n     */\r\n    cacheDataLoaded: PromiseWithKnownReason<\r\n      {\r\n        /**\r\n         * The (transformed) query result.\r\n         */\r\n        data: ResultType\r\n        /**\r\n         * The `meta` returned by the `baseQuery`\r\n         */\r\n        meta: MetaType\r\n      },\r\n      typeof neverResolvedError\r\n    >\r\n    /**\r\n     * Promise that allows you to wait for the point in time when the cache entry\r\n     * has been removed from the cache, by not being used/subscribed to any more\r\n     * in the application for too long or by dispatching `api.util.resetApiState`.\r\n     */\r\n    cacheEntryRemoved: Promise<void>\r\n  }\r\n\r\n  export interface QueryCacheLifecycleApi<\r\n    QueryArg,\r\n    BaseQuery extends BaseQueryFn,\r\n    ResultType,\r\n    ReducerPath extends string = string\r\n  > extends QueryBaseLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>,\r\n      CacheLifecyclePromises<ResultType, BaseQueryMeta<BaseQuery>> {}\r\n\r\n  export interface MutationCacheLifecycleApi<\r\n    QueryArg,\r\n    BaseQuery extends BaseQueryFn,\r\n    ResultType,\r\n    ReducerPath extends string = string\r\n  > extends MutationBaseLifecycleApi<\r\n        QueryArg,\r\n        BaseQuery,\r\n        ResultType,\r\n        ReducerPath\r\n      >,\r\n      CacheLifecyclePromises<ResultType, BaseQueryMeta<BaseQuery>> {}\r\n\r\n  interface QueryExtraOptions<\r\n    TagTypes extends string,\r\n    ResultType,\r\n    QueryArg,\r\n    BaseQuery extends BaseQueryFn,\r\n    ReducerPath extends string = string\r\n  > {\r\n    onCacheEntryAdded?(\r\n      arg: QueryArg,\r\n      api: QueryCacheLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>\r\n    ): Promise<void> | void\r\n  }\r\n\r\n  interface MutationExtraOptions<\r\n    TagTypes extends string,\r\n    ResultType,\r\n    QueryArg,\r\n    BaseQuery extends BaseQueryFn,\r\n    ReducerPath extends string = string\r\n  > {\r\n    onCacheEntryAdded?(\r\n      arg: QueryArg,\r\n      api: MutationCacheLifecycleApi<\r\n        QueryArg,\r\n        BaseQuery,\r\n        ResultType,\r\n        ReducerPath\r\n      >\r\n    ): Promise<void> | void\r\n  }\r\n}\r\n\r\nconst neverResolvedError = new Error(\r\n  'Promise never resolved before cacheEntryRemoved.'\r\n) as Error & {\r\n  message: 'Promise never resolved before cacheEntryRemoved.'\r\n}\r\n\r\nexport const buildCacheLifecycleHandler: InternalHandlerBuilder = ({\r\n  api,\r\n  reducerPath,\r\n  context,\r\n  queryThunk,\r\n  mutationThunk,\r\n  internalState,\r\n}) => {\r\n  const isQueryThunk = isAsyncThunkAction(queryThunk)\r\n  const isMutationThunk = isAsyncThunkAction(mutationThunk)\r\n  const isFulfilledThunk = isFulfilled(queryThunk, mutationThunk)\r\n\r\n  type CacheLifecycle = {\r\n    valueResolved?(value: { data: unknown; meta: unknown }): unknown\r\n    cacheEntryRemoved(): void\r\n  }\r\n  const lifecycleMap: Record<string, CacheLifecycle> = {}\r\n\r\n  const handler: ApiMiddlewareInternalHandler = (\r\n    action,\r\n    mwApi,\r\n    stateBefore\r\n  ) => {\r\n    const cacheKey = getCacheKey(action)\r\n\r\n    if (queryThunk.pending.match(action)) {\r\n      const oldState = stateBefore[reducerPath].queries[cacheKey]\r\n      const state = mwApi.getState()[reducerPath].queries[cacheKey]\r\n      if (!oldState && state) {\r\n        handleNewKey(\r\n          action.meta.arg.endpointName,\r\n          action.meta.arg.originalArgs,\r\n          cacheKey,\r\n          mwApi,\r\n          action.meta.requestId\r\n        )\r\n      }\r\n    } else if (mutationThunk.pending.match(action)) {\r\n      const state = mwApi.getState()[reducerPath].mutations[cacheKey]\r\n      if (state) {\r\n        handleNewKey(\r\n          action.meta.arg.endpointName,\r\n          action.meta.arg.originalArgs,\r\n          cacheKey,\r\n          mwApi,\r\n          action.meta.requestId\r\n        )\r\n      }\r\n    } else if (isFulfilledThunk(action)) {\r\n      const lifecycle = lifecycleMap[cacheKey]\r\n      if (lifecycle?.valueResolved) {\r\n        lifecycle.valueResolved({\r\n          data: action.payload,\r\n          meta: action.meta.baseQueryMeta,\r\n        })\r\n        delete lifecycle.valueResolved\r\n      }\r\n    } else if (\r\n      api.internalActions.removeQueryResult.match(action) ||\r\n      api.internalActions.removeMutationResult.match(action)\r\n    ) {\r\n      const lifecycle = lifecycleMap[cacheKey]\r\n      if (lifecycle) {\r\n        delete lifecycleMap[cacheKey]\r\n        lifecycle.cacheEntryRemoved()\r\n      }\r\n    } else if (api.util.resetApiState.match(action)) {\r\n      for (const [cacheKey, lifecycle] of Object.entries(lifecycleMap)) {\r\n        delete lifecycleMap[cacheKey]\r\n        lifecycle.cacheEntryRemoved()\r\n      }\r\n    }\r\n  }\r\n\r\n  function getCacheKey(action: any) {\r\n    if (isQueryThunk(action)) return action.meta.arg.queryCacheKey\r\n    if (isMutationThunk(action)) return action.meta.requestId\r\n    if (api.internalActions.removeQueryResult.match(action))\r\n      return action.payload.queryCacheKey\r\n    if (api.internalActions.removeMutationResult.match(action))\r\n      return getMutationCacheKey(action.payload)\r\n    return ''\r\n  }\r\n\r\n  function handleNewKey(\r\n    endpointName: string,\r\n    originalArgs: any,\r\n    queryCacheKey: string,\r\n    mwApi: SubMiddlewareApi,\r\n    requestId: string\r\n  ) {\r\n    const endpointDefinition = context.endpointDefinitions[endpointName]\r\n    const onCacheEntryAdded = endpointDefinition?.onCacheEntryAdded\r\n    if (!onCacheEntryAdded) return\r\n\r\n    let lifecycle = {} as CacheLifecycle\r\n\r\n    const cacheEntryRemoved = new Promise<void>((resolve) => {\r\n      lifecycle.cacheEntryRemoved = resolve\r\n    })\r\n    const cacheDataLoaded: PromiseWithKnownReason<\r\n      { data: unknown; meta: unknown },\r\n      typeof neverResolvedError\r\n    > = Promise.race([\r\n      new Promise<{ data: unknown; meta: unknown }>((resolve) => {\r\n        lifecycle.valueResolved = resolve\r\n      }),\r\n      cacheEntryRemoved.then(() => {\r\n        throw neverResolvedError\r\n      }),\r\n    ])\r\n    // prevent uncaught promise rejections from happening.\r\n    // if the original promise is used in any way, that will create a new promise that will throw again\r\n    cacheDataLoaded.catch(() => {})\r\n    lifecycleMap[queryCacheKey] = lifecycle\r\n    const selector = (api.endpoints[endpointName] as any).select(\r\n      endpointDefinition.type === DefinitionType.query\r\n        ? originalArgs\r\n        : queryCacheKey\r\n    )\r\n\r\n    const extra = mwApi.dispatch((_, __, extra) => extra)\r\n    const lifecycleApi = {\r\n      ...mwApi,\r\n      getCacheEntry: () => selector(mwApi.getState()),\r\n      requestId,\r\n      extra,\r\n      updateCachedData: (endpointDefinition.type === DefinitionType.query\r\n        ? (updateRecipe: Recipe<any>) =>\r\n            mwApi.dispatch(\r\n              api.util.updateQueryData(\r\n                endpointName as never,\r\n                originalArgs,\r\n                updateRecipe\r\n              )\r\n            )\r\n        : undefined) as any,\r\n\r\n      cacheDataLoaded,\r\n      cacheEntryRemoved,\r\n    }\r\n\r\n    const runningHandler = onCacheEntryAdded(originalArgs, lifecycleApi)\r\n    // if a `neverResolvedError` was thrown, but not handled in the running handler, do not let it leak out further\r\n    Promise.resolve(runningHandler).catch((e) => {\r\n      if (e === neverResolvedError) return\r\n      throw e\r\n    })\r\n  }\r\n\r\n  return handler\r\n}\r\n", "import { isPending, isRejected, isFulfilled } from '@reduxjs/toolkit'\r\nimport type {\r\n  BaseQueryError,\r\n  BaseQueryFn,\r\n  BaseQueryMeta,\r\n} from '../../baseQueryTypes'\r\nimport { DefinitionType } from '../../endpointDefinitions'\r\nimport type { QueryFulfilledRejectionReason } from '../../endpointDefinitions'\r\nimport type { Recipe } from '../buildThunks'\r\nimport type {\r\n  PromiseWithKnownReason,\r\n  PromiseConstructorWithKnownReason,\r\n  InternalHandlerBuilder,\r\n  ApiMiddlewareInternalHandler,\r\n} from './types'\r\n\r\nexport type ReferenceQueryLifecycle = never\r\n\r\ndeclare module '../../endpointDefinitions' {\r\n  export interface QueryLifecyclePromises<\r\n    ResultType,\r\n    BaseQuery extends BaseQueryFn\r\n  > {\r\n    /**\r\n     * Promise that will resolve with the (transformed) query result.\r\n     *\r\n     * If the query fails, this promise will reject with the error.\r\n     *\r\n     * This allows you to `await` for the query to finish.\r\n     *\r\n     * If you don't interact with this promise, it will not throw.\r\n     */\r\n    queryFulfilled: PromiseWithKnownReason<\r\n      {\r\n        /**\r\n         * The (transformed) query result.\r\n         */\r\n        data: ResultType\r\n        /**\r\n         * The `meta` returned by the `baseQuery`\r\n         */\r\n        meta: BaseQueryMeta<BaseQuery>\r\n      },\r\n      QueryFulfilledRejectionReason<BaseQuery>\r\n    >\r\n  }\r\n\r\n  type QueryFulfilledRejectionReason<BaseQuery extends BaseQueryFn> =\r\n    | {\r\n        error: BaseQueryError<BaseQuery>\r\n        /**\r\n         * If this is `false`, that means this error was returned from the `baseQuery` or `queryFn` in a controlled manner.\r\n         */\r\n        isUnhandledError: false\r\n        /**\r\n         * The `meta` returned by the `baseQuery`\r\n         */\r\n        meta: BaseQueryMeta<BaseQuery>\r\n      }\r\n    | {\r\n        error: unknown\r\n        meta?: undefined\r\n        /**\r\n         * If this is `true`, that means that this error is the result of `baseQueryFn`, `queryFn`, `transformResponse` or `transformErrorResponse` throwing an error instead of handling it properly.\r\n         * There can not be made any assumption about the shape of `error`.\r\n         */\r\n        isUnhandledError: true\r\n      }\r\n\r\n  interface QueryExtraOptions<\r\n    TagTypes extends string,\r\n    ResultType,\r\n    QueryArg,\r\n    BaseQuery extends BaseQueryFn,\r\n    ReducerPath extends string = string\r\n  > {\r\n    /**\r\n     * A function that is called when the individual query is started. The function is called with a lifecycle api object containing properties such as `queryFulfilled`, allowing code to be run when a query is started, when it succeeds, and when it fails (i.e. throughout the lifecycle of an individual query/mutation call).\r\n     *\r\n     * Can be used to perform side-effects throughout the lifecycle of the query.\r\n     *\r\n     * @example\r\n     * ```ts\r\n     * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query'\r\n     * import { messageCreated } from './notificationsSlice\r\n     * export interface Post {\r\n     *   id: number\r\n     *   name: string\r\n     * }\r\n     *\r\n     * const api = createApi({\r\n     *   baseQuery: fetchBaseQuery({\r\n     *     baseUrl: '/',\r\n     *   }),\r\n     *   endpoints: (build) => ({\r\n     *     getPost: build.query<Post, number>({\r\n     *       query: (id) => `post/${id}`,\r\n     *       async onQueryStarted(id, { dispatch, queryFulfilled }) {\r\n     *         // `onStart` side-effect\r\n     *         dispatch(messageCreated('Fetching posts...'))\r\n     *         try {\r\n     *           const { data } = await queryFulfilled\r\n     *           // `onSuccess` side-effect\r\n     *           dispatch(messageCreated('Posts received!'))\r\n     *         } catch (err) {\r\n     *           // `onError` side-effect\r\n     *           dispatch(messageCreated('Error fetching posts!'))\r\n     *         }\r\n     *       }\r\n     *     }),\r\n     *   }),\r\n     * })\r\n     * ```\r\n     */\r\n    onQueryStarted?(\r\n      arg: QueryArg,\r\n      api: QueryLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>\r\n    ): Promise<void> | void\r\n  }\r\n\r\n  interface MutationExtraOptions<\r\n    TagTypes extends string,\r\n    ResultType,\r\n    QueryArg,\r\n    BaseQuery extends BaseQueryFn,\r\n    ReducerPath extends string = string\r\n  > {\r\n    /**\r\n     * A function that is called when the individual mutation is started. The function is called with a lifecycle api object containing properties such as `queryFulfilled`, allowing code to be run when a query is started, when it succeeds, and when it fails (i.e. throughout the lifecycle of an individual query/mutation call).\r\n     *\r\n     * Can be used for `optimistic updates`.\r\n     *\r\n     * @example\r\n     *\r\n     * ```ts\r\n     * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query'\r\n     * export interface Post {\r\n     *   id: number\r\n     *   name: string\r\n     * }\r\n     *\r\n     * const api = createApi({\r\n     *   baseQuery: fetchBaseQuery({\r\n     *     baseUrl: '/',\r\n     *   }),\r\n     *   tagTypes: ['Post'],\r\n     *   endpoints: (build) => ({\r\n     *     getPost: build.query<Post, number>({\r\n     *       query: (id) => `post/${id}`,\r\n     *       providesTags: ['Post'],\r\n     *     }),\r\n     *     updatePost: build.mutation<void, Pick<Post, 'id'> & Partial<Post>>({\r\n     *       query: ({ id, ...patch }) => ({\r\n     *         url: `post/${id}`,\r\n     *         method: 'PATCH',\r\n     *         body: patch,\r\n     *       }),\r\n     *       invalidatesTags: ['Post'],\r\n     *       async onQueryStarted({ id, ...patch }, { dispatch, queryFulfilled }) {\r\n     *         const patchResult = dispatch(\r\n     *           api.util.updateQueryData('getPost', id, (draft) => {\r\n     *             Object.assign(draft, patch)\r\n     *           })\r\n     *         )\r\n     *         try {\r\n     *           await queryFulfilled\r\n     *         } catch {\r\n     *           patchResult.undo()\r\n     *         }\r\n     *       },\r\n     *     }),\r\n     *   }),\r\n     * })\r\n     * ```\r\n     */\r\n    onQueryStarted?(\r\n      arg: QueryArg,\r\n      api: MutationLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>\r\n    ): Promise<void> | void\r\n  }\r\n\r\n  export interface QueryLifecycleApi<\r\n    QueryArg,\r\n    BaseQuery extends BaseQueryFn,\r\n    ResultType,\r\n    ReducerPath extends string = string\r\n  > extends QueryBaseLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>,\r\n      QueryLifecyclePromises<ResultType, BaseQuery> {}\r\n\r\n  export interface MutationLifecycleApi<\r\n    QueryArg,\r\n    BaseQuery extends BaseQueryFn,\r\n    ResultType,\r\n    ReducerPath extends string = string\r\n  > extends MutationBaseLifecycleApi<\r\n        QueryArg,\r\n        BaseQuery,\r\n        ResultType,\r\n        ReducerPath\r\n      >,\r\n      QueryLifecyclePromises<ResultType, BaseQuery> {}\r\n}\r\n\r\nexport const buildQueryLifecycleHandler: InternalHandlerBuilder = ({\r\n  api,\r\n  context,\r\n  queryThunk,\r\n  mutationThunk,\r\n}) => {\r\n  const isPendingThunk = isPending(queryThunk, mutationThunk)\r\n  const isRejectedThunk = isRejected(queryThunk, mutationThunk)\r\n  const isFullfilledThunk = isFulfilled(queryThunk, mutationThunk)\r\n\r\n  type CacheLifecycle = {\r\n    resolve(value: { data: unknown; meta: unknown }): unknown\r\n    reject(value: QueryFulfilledRejectionReason<any>): unknown\r\n  }\r\n  const lifecycleMap: Record<string, CacheLifecycle> = {}\r\n\r\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi) => {\r\n    if (isPendingThunk(action)) {\r\n      const {\r\n        requestId,\r\n        arg: { endpointName, originalArgs },\r\n      } = action.meta\r\n      const endpointDefinition = context.endpointDefinitions[endpointName]\r\n      const onQueryStarted = endpointDefinition?.onQueryStarted\r\n      if (onQueryStarted) {\r\n        const lifecycle = {} as CacheLifecycle\r\n        const queryFulfilled =\r\n          new (Promise as PromiseConstructorWithKnownReason)<\r\n            { data: unknown; meta: unknown },\r\n            QueryFulfilledRejectionReason<any>\r\n          >((resolve, reject) => {\r\n            lifecycle.resolve = resolve\r\n            lifecycle.reject = reject\r\n          })\r\n        // prevent uncaught promise rejections from happening.\r\n        // if the original promise is used in any way, that will create a new promise that will throw again\r\n        queryFulfilled.catch(() => {})\r\n        lifecycleMap[requestId] = lifecycle\r\n        const selector = (api.endpoints[endpointName] as any).select(\r\n          endpointDefinition.type === DefinitionType.query\r\n            ? originalArgs\r\n            : requestId\r\n        )\r\n\r\n        const extra = mwApi.dispatch((_, __, extra) => extra)\r\n        const lifecycleApi = {\r\n          ...mwApi,\r\n          getCacheEntry: () => selector(mwApi.getState()),\r\n          requestId,\r\n          extra,\r\n          updateCachedData: (endpointDefinition.type === DefinitionType.query\r\n            ? (updateRecipe: Recipe<any>) =>\r\n                mwApi.dispatch(\r\n                  api.util.updateQueryData(\r\n                    endpointName as never,\r\n                    originalArgs,\r\n                    updateRecipe\r\n                  )\r\n                )\r\n            : undefined) as any,\r\n          queryFulfilled,\r\n        }\r\n        onQueryStarted(originalArgs, lifecycleApi)\r\n      }\r\n    } else if (isFullfilledThunk(action)) {\r\n      const { requestId, baseQueryMeta } = action.meta\r\n      lifecycleMap[requestId]?.resolve({\r\n        data: action.payload,\r\n        meta: baseQueryMeta,\r\n      })\r\n      delete lifecycleMap[requestId]\r\n    } else if (isRejectedThunk(action)) {\r\n      const { requestId, rejectedWithValue, baseQueryMeta } = action.meta\r\n      lifecycleMap[requestId]?.reject({\r\n        error: action.payload ?? action.error,\r\n        isUnhandledError: !rejectedWithValue,\r\n        meta: baseQueryMeta as any,\r\n      })\r\n      delete lifecycleMap[requestId]\r\n    }\r\n  }\r\n\r\n  return handler\r\n}\r\n", "import type { InternalHandlerBuilder } from './types'\r\n\r\nexport const buildDevCheckHandler: InternalHandlerBuilder = ({\r\n  api,\r\n  context: { apiUid },\r\n  reducerPath,\r\n}) => {\r\n  return (action, mwApi) => {\r\n    if (api.util.resetApiState.match(action)) {\r\n      // dispatch after api reset\r\n      mwApi.dispatch(api.internalActions.middlewareRegistered(apiUid))\r\n    }\r\n\r\n    if (\r\n      typeof process !== 'undefined' &&\r\n      process.env.NODE_ENV === 'development'\r\n    ) {\r\n      if (\r\n        api.internalActions.middlewareRegistered.match(action) &&\r\n        action.payload === apiUid &&\r\n        mwApi.getState()[reducerPath]?.config?.middlewareRegistered ===\r\n          'conflict'\r\n      ) {\r\n        console.warn(`There is a mismatch between slice and middleware for the reducerPath \"${reducerPath}\".\r\nYou can only have one api per reducer path, this will lead to crashes in various situations!${\r\n          reducerPath === 'api'\r\n            ? `\r\nIf you have multiple apis, you *have* to specify the reducerPath option when using createApi!`\r\n            : ''\r\n        }`)\r\n      }\r\n    }\r\n  }\r\n}\r\n", "import type { QueryThunk, RejectedAction } from '../buildThunks'\r\nimport type { InternalHandlerBuilder } from './types'\r\nimport type {\r\n  SubscriptionState,\r\n  QuerySubstateIdentifier,\r\n  Subscribers,\r\n} from '../apiState'\r\nimport { produceWithPatches } from 'immer'\r\nimport type { AnyAction } from '@reduxjs/toolkit';\r\nimport { createSlice, PayloadAction } from '@reduxjs/toolkit'\r\n\r\n// Copied from https://github.com/feross/queue-microtask\r\nlet promise: Promise<any>\r\nconst queueMicrotaskShim =\r\n  typeof queueMicrotask === 'function'\r\n    ? queueMicrotask.bind(\r\n        typeof window !== 'undefined'\r\n          ? window\r\n          : typeof global !== 'undefined'\r\n          ? global\r\n          : globalThis\r\n      )\r\n    : // reuse resolved promise, and allocate it lazily\r\n      (cb: () => void) =>\r\n        (promise || (promise = Promise.resolve())).then(cb).catch((err: any) =>\r\n          setTimeout(() => {\r\n            throw err\r\n          }, 0)\r\n        )\r\n\r\nexport const buildBatchedActionsHandler: InternalHandlerBuilder<\r\n  [actionShouldContinue: boolean, subscriptionExists: boolean]\r\n> = ({ api, queryThunk, internalState }) => {\r\n  const subscriptionsPrefix = `${api.reducerPath}/subscriptions`\r\n\r\n  let previousSubscriptions: SubscriptionState =\r\n    null as unknown as SubscriptionState\r\n\r\n  let dispatchQueued = false\r\n\r\n  const { updateSubscriptionOptions, unsubscribeQueryResult } =\r\n    api.internalActions\r\n\r\n  // Actually intentionally mutate the subscriptions state used in the middleware\r\n  // This is done to speed up perf when loading many components\r\n  const actuallyMutateSubscriptions = (\r\n    mutableState: SubscriptionState,\r\n    action: AnyAction\r\n  ) => {\r\n    if (updateSubscriptionOptions.match(action)) {\r\n      const { queryCacheKey, requestId, options } = action.payload\r\n\r\n      if (mutableState?.[queryCacheKey]?.[requestId]) {\r\n        mutableState[queryCacheKey]![requestId] = options\r\n      }\r\n      return true\r\n    }\r\n    if (unsubscribeQueryResult.match(action)) {\r\n      const { queryCacheKey, requestId } = action.payload\r\n      if (mutableState[queryCacheKey]) {\r\n        delete mutableState[queryCacheKey]![requestId]\r\n      }\r\n      return true\r\n    }\r\n    if (api.internalActions.removeQueryResult.match(action)) {\r\n      delete mutableState[action.payload.queryCacheKey]\r\n      return true\r\n    }\r\n    if (queryThunk.pending.match(action)) {\r\n      const {\r\n        meta: { arg, requestId },\r\n      } = action\r\n      if (arg.subscribe) {\r\n        const substate = (mutableState[arg.queryCacheKey] ??= {})\r\n        substate[requestId] =\r\n          arg.subscriptionOptions ?? substate[requestId] ?? {}\r\n\r\n        return true\r\n      }\r\n    }\r\n    if (queryThunk.rejected.match(action)) {\r\n      const {\r\n        meta: { condition, arg, requestId },\r\n      } = action\r\n      if (condition && arg.subscribe) {\r\n        const substate = (mutableState[arg.queryCacheKey] ??= {})\r\n        substate[requestId] =\r\n          arg.subscriptionOptions ?? substate[requestId] ?? {}\r\n\r\n        return true\r\n      }\r\n    }\r\n\r\n    return false\r\n  }\r\n\r\n  return (action, mwApi) => {\r\n    if (!previousSubscriptions) {\r\n      // Initialize it the first time this handler runs\r\n      previousSubscriptions = JSON.parse(\r\n        JSON.stringify(internalState.currentSubscriptions)\r\n      )\r\n    }\r\n\r\n    if (api.util.resetApiState.match(action)) {\r\n      previousSubscriptions = internalState.currentSubscriptions = {}\r\n      return [true, false]\r\n    }\r\n\r\n    // Intercept requests by hooks to see if they're subscribed\r\n    // Necessary because we delay updating store state to the end of the tick\r\n    if (api.internalActions.internal_probeSubscription.match(action)) {\r\n      const { queryCacheKey, requestId } = action.payload\r\n      const hasSubscription =\r\n        !!internalState.currentSubscriptions[queryCacheKey]?.[requestId]\r\n      return [false, hasSubscription]\r\n    }\r\n\r\n    // Update subscription data based on this action\r\n    const didMutate = actuallyMutateSubscriptions(\r\n      internalState.currentSubscriptions,\r\n      action\r\n    )\r\n\r\n    if (didMutate) {\r\n      if (!dispatchQueued) {\r\n        queueMicrotaskShim(() => {\r\n          // Deep clone the current subscription data\r\n          const newSubscriptions: SubscriptionState = JSON.parse(\r\n            JSON.stringify(internalState.currentSubscriptions)\r\n          )\r\n          // Figure out a smaller diff between original and current\r\n          const [, patches] = produceWithPatches(\r\n            previousSubscriptions,\r\n            () => newSubscriptions\r\n          )\r\n\r\n          // Sync the store state for visibility\r\n          mwApi.next(api.internalActions.subscriptionsUpdated(patches))\r\n          // Save the cloned state for later reference\r\n          previousSubscriptions = newSubscriptions\r\n          dispatchQueued = false\r\n        })\r\n        dispatchQueued = true\r\n      }\r\n\r\n      const isSubscriptionSliceAction =\r\n        !!action.type?.startsWith(subscriptionsPrefix)\r\n      const isAdditionalSubscriptionAction =\r\n        queryThunk.rejected.match(action) &&\r\n        action.meta.condition &&\r\n        !!action.meta.arg.subscribe\r\n\r\n      const actionShouldContinue =\r\n        !isSubscriptionSliceAction && !isAdditionalSubscriptionAction\r\n\r\n      return [actionShouldContinue, false]\r\n    }\r\n\r\n    return [true, false]\r\n  }\r\n}\r\n", "export type Id<T> = { [K in keyof T]: T[K] } & {}\r\nexport type WithRequiredProp<T, K extends keyof T> = Omit<T, K> &\r\n  Required<Pick<T, K>>\r\nexport type Override<T1, T2> = T2 extends any ? Omit<T1, keyof T2> & T2 : never\r\nexport function assertCast<T>(v: any): asserts v is T {}\r\n\r\nexport function safeAssign<T extends object>(\r\n  target: T,\r\n  ...args: Array<Partial<NoInfer<T>>>\r\n) {\r\n  Object.assign(target, ...args)\r\n}\r\n\r\n/**\r\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\r\n */\r\nexport type UnionToIntersection<U> = (\r\n  U extends any ? (k: U) => void : never\r\n) extends (k: infer I) => void\r\n  ? I\r\n  : never\r\n\r\nexport type NonOptionalKeys<T> = {\r\n  [K in keyof T]-?: undefined extends T[K] ? never : K\r\n}[keyof T]\r\n\r\nexport type HasRequiredProps<T, True, False> = NonOptionalKeys<T> extends never\r\n  ? False\r\n  : True\r\n\r\nexport type OptionalIfAllPropsOptional<T> = HasRequiredProps<T, T, T | never>\r\n\r\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\r\n\r\nexport type NonUndefined<T> = T extends undefined ? never : T\r\n\r\nexport type UnwrapPromise<T> = T extends PromiseLike<infer V> ? V : T\r\n\r\nexport type MaybePromise<T> = T | PromiseLike<T>\r\n\r\nexport type OmitFromUnion<T, K extends keyof T> = T extends any\r\n  ? Omit<T, K>\r\n  : never\r\n\r\nexport type IsAny<T, True, False = never> = true | false extends (\r\n  T extends never ? true : false\r\n)\r\n  ? True\r\n  : False\r\n\r\nexport type CastAny<T, CastTo> = IsAny<T, CastTo, T>\r\n", "/**\r\n * Note: this file should import all other files for type discovery and declaration merging\r\n */\r\nimport type {\r\n  PatchQueryDataThunk,\r\n  UpdateQueryDataThunk,\r\n  UpsertQueryDataThunk,\r\n} from './buildThunks'\r\nimport { buildThunks } from './buildThunks'\r\nimport type {\r\n  ActionCreatorWithPayload,\r\n  AnyAction,\r\n  Middleware,\r\n  Reducer,\r\n  ThunkAction,\r\n  ThunkDispatch,\r\n} from '@reduxjs/toolkit'\r\nimport type {\r\n  EndpointDefinitions,\r\n  QueryArgFrom,\r\n  QueryDefinition,\r\n  MutationDefinition,\r\n  AssertTagTypes,\r\n  TagDescription,\r\n} from '../endpointDefinitions'\r\nimport { isQueryDefinition, isMutationDefinition } from '../endpointDefinitions'\r\nimport type {\r\n  CombinedState,\r\n  QueryKeys,\r\n  MutationKeys,\r\n  RootState,\r\n} from './apiState'\r\nimport type { Api, Module } from '../apiTypes'\r\nimport { onFocus, onFocusLost, onOnline, onOffline } from './setupListeners'\r\nimport { buildSlice } from './buildSlice'\r\nimport { buildMiddleware } from './buildMiddleware'\r\nimport { buildSelectors } from './buildSelectors'\r\nimport type {\r\n  MutationActionCreatorResult,\r\n  QueryActionCreatorResult,\r\n} from './buildInitiate'\r\nimport { buildInitiate } from './buildInitiate'\r\nimport { assertCast, safeAssign } from '../tsHelpers'\r\nimport type { InternalSerializeQueryArgs } from '../defaultSerializeQueryArgs'\r\nimport type { SliceActions } from './buildSlice'\r\nimport type { BaseQueryFn } from '../baseQueryTypes'\r\n\r\nimport type { ReferenceCacheLifecycle } from './buildMiddleware/cacheLifecycle'\r\nimport type { ReferenceQueryLifecycle } from './buildMiddleware/queryLifecycle'\r\nimport type { ReferenceCacheCollection } from './buildMiddleware/cacheCollection'\r\nimport { enablePatches } from 'immer'\r\n\r\n/**\r\n * `ifOlderThan` - (default: `false` | `number`) - _number is value in seconds_\r\n * - If specified, it will only run the query if the difference between `new Date()` and the last `fulfilledTimeStamp` is greater than the given value\r\n *\r\n * @overloadSummary\r\n * `force`\r\n * - If `force: true`, it will ignore the `ifOlderThan` value if it is set and the query will be run even if it exists in the cache.\r\n */\r\nexport type PrefetchOptions =\r\n  | {\r\n      ifOlderThan?: false | number\r\n    }\r\n  | { force?: boolean }\r\n\r\nexport const coreModuleName = /* @__PURE__ */ Symbol()\r\nexport type CoreModule =\r\n  | typeof coreModuleName\r\n  | ReferenceCacheLifecycle\r\n  | ReferenceQueryLifecycle\r\n  | ReferenceCacheCollection\r\n\r\nexport interface ThunkWithReturnValue<T> extends ThunkAction<T, any, any, AnyAction> {}\r\n\r\ndeclare module '../apiTypes' {\r\n  export interface ApiModules<\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    BaseQuery extends BaseQueryFn,\r\n    Definitions extends EndpointDefinitions,\r\n    ReducerPath extends string,\r\n    TagTypes extends string\r\n  > {\r\n    [coreModuleName]: {\r\n      /**\r\n       * This api's reducer should be mounted at `store[api.reducerPath]`.\r\n       *\r\n       * @example\r\n       * ```ts\r\n       * configureStore({\r\n       *   reducer: {\r\n       *     [api.reducerPath]: api.reducer,\r\n       *   },\r\n       *   middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(api.middleware),\r\n       * })\r\n       * ```\r\n       */\r\n      reducerPath: ReducerPath\r\n      /**\r\n       * Internal actions not part of the public API. Note: These are subject to change at any given time.\r\n       */\r\n      internalActions: InternalActions\r\n      /**\r\n       *  A standard redux reducer that enables core functionality. Make sure it's included in your store.\r\n       *\r\n       * @example\r\n       * ```ts\r\n       * configureStore({\r\n       *   reducer: {\r\n       *     [api.reducerPath]: api.reducer,\r\n       *   },\r\n       *   middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(api.middleware),\r\n       * })\r\n       * ```\r\n       */\r\n      reducer: Reducer<\r\n        CombinedState<Definitions, TagTypes, ReducerPath>,\r\n        AnyAction\r\n      >\r\n      /**\r\n       * This is a standard redux middleware and is responsible for things like polling, garbage collection and a handful of other things. Make sure it's included in your store.\r\n       *\r\n       * @example\r\n       * ```ts\r\n       * configureStore({\r\n       *   reducer: {\r\n       *     [api.reducerPath]: api.reducer,\r\n       *   },\r\n       *   middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(api.middleware),\r\n       * })\r\n       * ```\r\n       */\r\n      middleware: Middleware<\r\n        {},\r\n        RootState<Definitions, string, ReducerPath>,\r\n        ThunkDispatch<any, any, AnyAction>\r\n      >\r\n      /**\r\n       * A collection of utility thunks for various situations.\r\n       */\r\n      util: {\r\n        /**\r\n         * This method had to be removed due to a conceptual bug in RTK.\r\n         *\r\n         * Despite TypeScript errors, it will continue working in the \"buggy\" way it did\r\n         * before in production builds and will be removed in the next major release.\r\n         *\r\n         * Nonetheless, you should immediately replace it with the new recommended approach.\r\n         * See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for new guidance on SSR.\r\n         *\r\n         * Please see https://github.com/reduxjs/redux-toolkit/pull/2481 for details.\r\n         * @deprecated\r\n         */\r\n        getRunningOperationPromises: never // this is now types as `never` to immediately throw TS errors on use, but still allow for a comment\r\n\r\n        /**\r\n         * This method had to be removed due to a conceptual bug in RTK.\r\n         * It has been replaced by `api.util.getRunningQueryThunk` and `api.util.getRunningMutationThunk`.\r\n         * Please see https://github.com/reduxjs/redux-toolkit/pull/2481 for details.\r\n         * @deprecated\r\n         */\r\n        getRunningOperationPromise: never // this is now types as `never` to immediately throw TS errors on use, but still allow for a comment\r\n\r\n        /**\r\n         * A thunk that (if dispatched) will return a specific running query, identified\r\n         * by `endpointName` and `args`.\r\n         * If that query is not running, dispatching the thunk will result in `undefined`.\r\n         *\r\n         * Can be used to await a specific query triggered in any way,\r\n         * including via hook calls or manually dispatching `initiate` actions.\r\n         *\r\n         * See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for details.\r\n         */\r\n        getRunningQueryThunk<EndpointName extends QueryKeys<Definitions>>(\r\n          endpointName: EndpointName,\r\n          args: QueryArgFrom<Definitions[EndpointName]>\r\n        ): ThunkWithReturnValue<\r\n          | QueryActionCreatorResult<\r\n              Definitions[EndpointName] & { type: 'query' }\r\n            >\r\n          | undefined\r\n        >\r\n\r\n        /**\r\n         * A thunk that (if dispatched) will return a specific running mutation, identified\r\n         * by `endpointName` and `fixedCacheKey` or `requestId`.\r\n         * If that mutation is not running, dispatching the thunk will result in `undefined`.\r\n         *\r\n         * Can be used to await a specific mutation triggered in any way,\r\n         * including via hook trigger functions or manually dispatching `initiate` actions.\r\n         *\r\n         * See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for details.\r\n         */\r\n        getRunningMutationThunk<EndpointName extends MutationKeys<Definitions>>(\r\n          endpointName: EndpointName,\r\n          fixedCacheKeyOrRequestId: string\r\n        ): ThunkWithReturnValue<\r\n          | MutationActionCreatorResult<\r\n              Definitions[EndpointName] & { type: 'mutation' }\r\n            >\r\n          | undefined\r\n        >\r\n\r\n        /**\r\n         * A thunk that (if dispatched) will return all running queries.\r\n         *\r\n         * Useful for SSR scenarios to await all running queries triggered in any way,\r\n         * including via hook calls or manually dispatching `initiate` actions.\r\n         *\r\n         * See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for details.\r\n         */\r\n        getRunningQueriesThunk(): ThunkWithReturnValue<\r\n          Array<QueryActionCreatorResult<any>>\r\n        >\r\n\r\n        /**\r\n         * A thunk that (if dispatched) will return all running mutations.\r\n         *\r\n         * Useful for SSR scenarios to await all running mutations triggered in any way,\r\n         * including via hook calls or manually dispatching `initiate` actions.\r\n         *\r\n         * See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for details.\r\n         */\r\n        getRunningMutationsThunk(): ThunkWithReturnValue<\r\n          Array<MutationActionCreatorResult<any>>\r\n        >\r\n\r\n        /**\r\n         * A Redux thunk that can be used to manually trigger pre-fetching of data.\r\n         *\r\n         * The thunk accepts three arguments: the name of the endpoint we are updating (such as `'getPost'`), the appropriate query arg values to construct the desired cache key, and a set of options used to determine if the data actually should be re-fetched based on cache staleness.\r\n         *\r\n         * React Hooks users will most likely never need to use this directly, as the `usePrefetch` hook will dispatch this thunk internally as needed when you call the prefetching function supplied by the hook.\r\n         *\r\n         * @example\r\n         *\r\n         * ```ts no-transpile\r\n         * dispatch(api.util.prefetch('getPosts', undefined, { force: true }))\r\n         * ```\r\n         */\r\n        prefetch<EndpointName extends QueryKeys<Definitions>>(\r\n          endpointName: EndpointName,\r\n          arg: QueryArgFrom<Definitions[EndpointName]>,\r\n          options: PrefetchOptions\r\n        ): ThunkAction<void, any, any, AnyAction>\r\n        /**\r\n         * A Redux thunk action creator that, when dispatched, creates and applies a set of JSON diff/patch objects to the current state. This immediately updates the Redux state with those changes.\r\n         *\r\n         * The thunk action creator accepts three arguments: the name of the endpoint we are updating (such as `'getPost'`), the appropriate query arg values to construct the desired cache key, and an `updateRecipe` callback function. The callback receives an Immer-wrapped `draft` of the current state, and may modify the draft to match the expected results after the mutation completes successfully.\r\n         *\r\n         * The thunk executes _synchronously_, and returns an object containing `{patches: Patch[], inversePatches: Patch[], undo: () => void}`. The `patches` and `inversePatches` are generated using Immer's [`produceWithPatches` method](https://immerjs.github.io/immer/patches).\r\n         *\r\n         * This is typically used as the first step in implementing optimistic updates. The generated `inversePatches` can be used to revert the updates by calling `dispatch(patchQueryData(endpointName, args, inversePatches))`. Alternatively, the `undo` method can be called directly to achieve the same effect.\r\n         *\r\n         * Note that the first two arguments (`endpointName` and `args`) are used to determine which existing cache entry to update. If no existing cache entry is found, the `updateRecipe` callback will not run.\r\n         *\r\n         * @example\r\n         *\r\n         * ```ts\r\n         * const patchCollection = dispatch(\r\n         *   api.util.updateQueryData('getPosts', undefined, (draftPosts) => {\r\n         *     draftPosts.push({ id: 1, name: 'Teddy' })\r\n         *   })\r\n         * )\r\n         * ```\r\n         */\r\n        updateQueryData: UpdateQueryDataThunk<\r\n          Definitions,\r\n          RootState<Definitions, string, ReducerPath>\r\n        >\r\n        /** @deprecated renamed to `updateQueryData` */\r\n        updateQueryResult: UpdateQueryDataThunk<\r\n          Definitions,\r\n          RootState<Definitions, string, ReducerPath>\r\n        >\r\n        /**\r\n         * A Redux thunk action creator that, when dispatched, acts as an artificial API request to upsert a value into the cache.\r\n         *\r\n         * The thunk action creator accepts three arguments: the name of the endpoint we are updating (such as `'getPost'`), the appropriate query arg values to construct the desired cache key, and the data to upsert.\r\n         *\r\n         * If no cache entry for that cache key exists, a cache entry will be created and the data added. If a cache entry already exists, this will _overwrite_ the existing cache entry data.\r\n         *\r\n         * The thunk executes _asynchronously_, and returns a promise that resolves when the store has been updated.\r\n         *\r\n         * If dispatched while an actual request is in progress, both the upsert and request will be handled as soon as they resolve, resulting in a \"last result wins\" update behavior.\r\n         *\r\n         * @example\r\n         *\r\n         * ```ts\r\n         * await dispatch(\r\n         *   api.util.upsertQueryData('getPost', {id: 1}, {id: 1, text: \"Hello!\"})\r\n         * )\r\n         * ```\r\n         */\r\n        upsertQueryData: UpsertQueryDataThunk<\r\n          Definitions,\r\n          RootState<Definitions, string, ReducerPath>\r\n        >\r\n        /**\r\n         * A Redux thunk that applies a JSON diff/patch array to the cached data for a given query result. This immediately updates the Redux state with those changes.\r\n         *\r\n         * The thunk accepts three arguments: the name of the endpoint we are updating (such as `'getPost'`), the appropriate query arg values to construct the desired cache key, and a JSON diff/patch array as produced by Immer's `produceWithPatches`.\r\n         *\r\n         * This is typically used as the second step in implementing optimistic updates. If a request fails, the optimistically-applied changes can be reverted by dispatching `patchQueryData` with the `inversePatches` that were generated by `updateQueryData` earlier.\r\n         *\r\n         * In cases where it is desired to simply revert the previous changes, it may be preferable to call the `undo` method returned from dispatching `updateQueryData` instead.\r\n         *\r\n         * @example\r\n         * ```ts\r\n         * const patchCollection = dispatch(\r\n         *   api.util.updateQueryData('getPosts', undefined, (draftPosts) => {\r\n         *     draftPosts.push({ id: 1, name: 'Teddy' })\r\n         *   })\r\n         * )\r\n         *\r\n         * // later\r\n         * dispatch(\r\n         *   api.util.patchQueryData('getPosts', undefined, patchCollection.inversePatches)\r\n         * )\r\n         *\r\n         * // or\r\n         * patchCollection.undo()\r\n         * ```\r\n         */\r\n        patchQueryData: PatchQueryDataThunk<\r\n          Definitions,\r\n          RootState<Definitions, string, ReducerPath>\r\n        >\r\n        /** @deprecated renamed to `patchQueryData` */\r\n        patchQueryResult: PatchQueryDataThunk<\r\n          Definitions,\r\n          RootState<Definitions, string, ReducerPath>\r\n        >\r\n        /**\r\n         * A Redux action creator that can be dispatched to manually reset the api state completely. This will immediately remove all existing cache entries, and all queries will be considered 'uninitialized'.\r\n         *\r\n         * @example\r\n         *\r\n         * ```ts\r\n         * dispatch(api.util.resetApiState())\r\n         * ```\r\n         */\r\n        resetApiState: SliceActions['resetApiState']\r\n        /**\r\n         * A Redux action creator that can be used to manually invalidate cache tags for [automated re-fetching](../../usage/automated-refetching.mdx).\r\n         *\r\n         * The action creator accepts one argument: the cache tags to be invalidated. It returns an action with those tags as a payload, and the corresponding `invalidateTags` action type for the api.\r\n         *\r\n         * Dispatching the result of this action creator will [invalidate](../../usage/automated-refetching.mdx#invalidating-cache-data) the given tags, causing queries to automatically re-fetch if they are subscribed to cache data that [provides](../../usage/automated-refetching.mdx#providing-cache-data) the corresponding tags.\r\n         *\r\n         * The array of tags provided to the action creator should be in one of the following formats, where `TagType` is equal to a string provided to the [`tagTypes`](../createApi.mdx#tagtypes) property of the api:\r\n         *\r\n         * - `[TagType]`\r\n         * - `[{ type: TagType }]`\r\n         * - `[{ type: TagType, id: number | string }]`\r\n         *\r\n         * @example\r\n         *\r\n         * ```ts\r\n         * dispatch(api.util.invalidateTags(['Post']))\r\n         * dispatch(api.util.invalidateTags([{ type: 'Post', id: 1 }]))\r\n         * dispatch(\r\n         *   api.util.invalidateTags([\r\n         *     { type: 'Post', id: 1 },\r\n         *     { type: 'Post', id: 'LIST' },\r\n         *   ])\r\n         * )\r\n         * ```\r\n         */\r\n        invalidateTags: ActionCreatorWithPayload<\r\n          Array<TagDescription<TagTypes>>,\r\n          string\r\n        >\r\n\r\n        /**\r\n         * A function to select all `{ endpointName, originalArgs, queryCacheKey }` combinations that would be invalidated by a specific set of tags.\r\n         *\r\n         * Can be used for mutations that want to do optimistic updates instead of invalidating a set of tags, but don't know exactly what they need to update.\r\n         */\r\n        selectInvalidatedBy: (\r\n          state: RootState<Definitions, string, ReducerPath>,\r\n          tags: ReadonlyArray<TagDescription<TagTypes>>\r\n        ) => Array<{\r\n          endpointName: string\r\n          originalArgs: any\r\n          queryCacheKey: string\r\n        }>\r\n      }\r\n      /**\r\n       * Endpoints based on the input endpoints provided to `createApi`, containing `select` and `action matchers`.\r\n       */\r\n      endpoints: {\r\n        [K in keyof Definitions]: Definitions[K] extends QueryDefinition<\r\n          any,\r\n          any,\r\n          any,\r\n          any,\r\n          any\r\n        >\r\n          ? ApiEndpointQuery<Definitions[K], Definitions>\r\n          : Definitions[K] extends MutationDefinition<any, any, any, any, any>\r\n          ? ApiEndpointMutation<Definitions[K], Definitions>\r\n          : never\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nexport interface ApiEndpointQuery<\r\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n  Definition extends QueryDefinition<any, any, any, any, any>,\r\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n  Definitions extends EndpointDefinitions\r\n> {\r\n  name: string\r\n  /**\r\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\r\n   */\r\n  Types: NonNullable<Definition['Types']>\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nexport interface ApiEndpointMutation<\r\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n  Definition extends MutationDefinition<any, any, any, any, any>,\r\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n  Definitions extends EndpointDefinitions\r\n> {\r\n  name: string\r\n  /**\r\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\r\n   */\r\n  Types: NonNullable<Definition['Types']>\r\n}\r\n\r\nexport type ListenerActions = {\r\n  /**\r\n   * Will cause the RTK Query middleware to trigger any refetchOnReconnect-related behavior\r\n   * @link https://rtk-query-docs.netlify.app/api/setupListeners\r\n   */\r\n  onOnline: typeof onOnline\r\n  onOffline: typeof onOffline\r\n  /**\r\n   * Will cause the RTK Query middleware to trigger any refetchOnFocus-related behavior\r\n   * @link https://rtk-query-docs.netlify.app/api/setupListeners\r\n   */\r\n  onFocus: typeof onFocus\r\n  onFocusLost: typeof onFocusLost\r\n}\r\n\r\nexport type InternalActions = SliceActions & ListenerActions\r\n\r\n/**\r\n * Creates a module containing the basic redux logic for use with `buildCreateApi`.\r\n *\r\n * @example\r\n * ```ts\r\n * const createBaseApi = buildCreateApi(coreModule());\r\n * ```\r\n */\r\nexport const coreModule = (): Module<CoreModule> => ({\r\n  name: coreModuleName,\r\n  init(\r\n    api,\r\n    {\r\n      baseQuery,\r\n      tagTypes,\r\n      reducerPath,\r\n      serializeQueryArgs,\r\n      keepUnusedDataFor,\r\n      refetchOnMountOrArgChange,\r\n      refetchOnFocus,\r\n      refetchOnReconnect,\r\n    },\r\n    context\r\n  ) {\r\n    enablePatches()\r\n\r\n    assertCast<InternalSerializeQueryArgs>(serializeQueryArgs)\r\n\r\n    const assertTagType: AssertTagTypes = (tag) => {\r\n      if (\r\n        typeof process !== 'undefined' &&\r\n        process.env.NODE_ENV === 'development'\r\n      ) {\r\n        if (!tagTypes.includes(tag.type as any)) {\r\n          console.error(\r\n            `Tag type '${tag.type}' was used, but not specified in \\`tagTypes\\`!`\r\n          )\r\n        }\r\n      }\r\n      return tag\r\n    }\r\n\r\n    Object.assign(api, {\r\n      reducerPath,\r\n      endpoints: {},\r\n      internalActions: {\r\n        onOnline,\r\n        onOffline,\r\n        onFocus,\r\n        onFocusLost,\r\n      },\r\n      util: {},\r\n    })\r\n\r\n    const {\r\n      queryThunk,\r\n      mutationThunk,\r\n      patchQueryData,\r\n      updateQueryData,\r\n      upsertQueryData,\r\n      prefetch,\r\n      buildMatchThunkActions,\r\n    } = buildThunks({\r\n      baseQuery,\r\n      reducerPath,\r\n      context,\r\n      api,\r\n      serializeQueryArgs,\r\n      assertTagType,\r\n    })\r\n\r\n    const { reducer, actions: sliceActions } = buildSlice({\r\n      context,\r\n      queryThunk,\r\n      mutationThunk,\r\n      reducerPath,\r\n      assertTagType,\r\n      config: {\r\n        refetchOnFocus,\r\n        refetchOnReconnect,\r\n        refetchOnMountOrArgChange,\r\n        keepUnusedDataFor,\r\n        reducerPath,\r\n      },\r\n    })\r\n\r\n    safeAssign(api.util, {\r\n      patchQueryData,\r\n      updateQueryData,\r\n      upsertQueryData,\r\n      prefetch,\r\n      resetApiState: sliceActions.resetApiState,\r\n    })\r\n    safeAssign(api.internalActions, sliceActions)\r\n\r\n    const { middleware, actions: middlewareActions } = buildMiddleware({\r\n      reducerPath,\r\n      context,\r\n      queryThunk,\r\n      mutationThunk,\r\n      api,\r\n      assertTagType,\r\n    })\r\n    safeAssign(api.util, middlewareActions)\r\n\r\n    safeAssign(api, { reducer: reducer as any, middleware })\r\n\r\n    const { buildQuerySelector, buildMutationSelector, selectInvalidatedBy } =\r\n      buildSelectors({\r\n        serializeQueryArgs: serializeQueryArgs as any,\r\n        reducerPath,\r\n      })\r\n\r\n    safeAssign(api.util, { selectInvalidatedBy })\r\n\r\n    const {\r\n      buildInitiateQuery,\r\n      buildInitiateMutation,\r\n      getRunningMutationThunk,\r\n      getRunningMutationsThunk,\r\n      getRunningQueriesThunk,\r\n      getRunningQueryThunk,\r\n      getRunningOperationPromises,\r\n      removalWarning,\r\n    } = buildInitiate({\r\n      queryThunk,\r\n      mutationThunk,\r\n      api,\r\n      serializeQueryArgs: serializeQueryArgs as any,\r\n      context,\r\n    })\r\n\r\n    safeAssign(api.util, {\r\n      getRunningOperationPromises: getRunningOperationPromises as any,\r\n      getRunningOperationPromise: removalWarning as any,\r\n      getRunningMutationThunk,\r\n      getRunningMutationsThunk,\r\n      getRunningQueryThunk,\r\n      getRunningQueriesThunk,\r\n    })\r\n\r\n    return {\r\n      name: coreModuleName,\r\n      injectEndpoint(endpointName, definition) {\r\n        const anyApi = api as any as Api<\r\n          any,\r\n          Record<string, any>,\r\n          string,\r\n          string,\r\n          CoreModule\r\n        >\r\n        anyApi.endpoints[endpointName] ??= {} as any\r\n        if (isQueryDefinition(definition)) {\r\n          safeAssign(\r\n            anyApi.endpoints[endpointName],\r\n            {\r\n              name: endpointName,\r\n              select: buildQuerySelector(endpointName, definition),\r\n              initiate: buildInitiateQuery(endpointName, definition),\r\n            },\r\n            buildMatchThunkActions(queryThunk, endpointName)\r\n          )\r\n        } else if (isMutationDefinition(definition)) {\r\n          safeAssign(\r\n            anyApi.endpoints[endpointName],\r\n            {\r\n              name: endpointName,\r\n              select: buildMutationSelector(),\r\n              initiate: buildInitiateMutation(endpointName),\r\n            },\r\n            buildMatchThunkActions(mutationThunk, endpointName)\r\n          )\r\n        }\r\n      },\r\n    }\r\n  },\r\n})\r\n", "import { buildCreate<PERSON><PERSON>, Create<PERSON><PERSON> } from '../createApi'\r\nimport { coreModule, coreModuleName } from './module'\r\n\r\nconst createApi = /* @__PURE__ */ buildCreateApi(coreModule())\r\n\r\nexport { createApi, coreModule, coreModuleName }\r\n", "var __generator = (this && this.__generator) || function (thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n};\r\nvar __spreadArray = (this && this.__spreadArray) || function (to, from) {\r\n    for (var i = 0, il = from.length, j = to.length; i < il; i++, j++)\r\n        to[j] = from[i];\r\n    return to;\r\n};\r\nvar __defProp = Object.defineProperty;\r\nvar __defProps = Object.defineProperties;\r\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\r\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\r\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\r\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\r\nvar __defNormalProp = function (obj, key, value) { return key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value: value }) : obj[key] = value; };\r\nvar __spreadValues = function (a, b) {\r\n    for (var prop in b || (b = {}))\r\n        if (__hasOwnProp.call(b, prop))\r\n            __defNormalProp(a, prop, b[prop]);\r\n    if (__getOwnPropSymbols)\r\n        for (var _j = 0, _k = __getOwnPropSymbols(b); _j < _k.length; _j++) {\r\n            var prop = _k[_j];\r\n            if (__propIsEnum.call(b, prop))\r\n                __defNormalProp(a, prop, b[prop]);\r\n        }\r\n    return a;\r\n};\r\nvar __spreadProps = function (a, b) { return __defProps(a, __getOwnPropDescs(b)); };\r\nvar __objRest = function (source, exclude) {\r\n    var target = {};\r\n    for (var prop in source)\r\n        if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\r\n            target[prop] = source[prop];\r\n    if (source != null && __getOwnPropSymbols)\r\n        for (var _j = 0, _k = __getOwnPropSymbols(source); _j < _k.length; _j++) {\r\n            var prop = _k[_j];\r\n            if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\r\n                target[prop] = source[prop];\r\n        }\r\n    return target;\r\n};\r\nvar __async = function (__this, __arguments, generator) {\r\n    return new Promise(function (resolve, reject) {\r\n        var fulfilled = function (value) {\r\n            try {\r\n                step(generator.next(value));\r\n            }\r\n            catch (e) {\r\n                reject(e);\r\n            }\r\n        };\r\n        var rejected = function (value) {\r\n            try {\r\n                step(generator.throw(value));\r\n            }\r\n            catch (e) {\r\n                reject(e);\r\n            }\r\n        };\r\n        var step = function (x) { return x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected); };\r\n        step((generator = generator.apply(__this, __arguments)).next());\r\n    });\r\n};\r\n// src/query/core/apiState.ts\r\nvar QueryStatus;\r\n(function (QueryStatus2) {\r\n    QueryStatus2[\"uninitialized\"] = \"uninitialized\";\r\n    QueryStatus2[\"pending\"] = \"pending\";\r\n    QueryStatus2[\"fulfilled\"] = \"fulfilled\";\r\n    QueryStatus2[\"rejected\"] = \"rejected\";\r\n})(QueryStatus || (QueryStatus = {}));\r\nfunction getRequestStatusFlags(status) {\r\n    return {\r\n        status: status,\r\n        isUninitialized: status === QueryStatus.uninitialized,\r\n        isLoading: status === QueryStatus.pending,\r\n        isSuccess: status === QueryStatus.fulfilled,\r\n        isError: status === QueryStatus.rejected\r\n    };\r\n}\r\n// src/query/utils/isAbsoluteUrl.ts\r\nfunction isAbsoluteUrl(url) {\r\n    return new RegExp(\"(^|:)//\").test(url);\r\n}\r\n// src/query/utils/joinUrls.ts\r\nvar withoutTrailingSlash = function (url) { return url.replace(/\\/$/, \"\"); };\r\nvar withoutLeadingSlash = function (url) { return url.replace(/^\\//, \"\"); };\r\nfunction joinUrls(base, url) {\r\n    if (!base) {\r\n        return url;\r\n    }\r\n    if (!url) {\r\n        return base;\r\n    }\r\n    if (isAbsoluteUrl(url)) {\r\n        return url;\r\n    }\r\n    var delimiter = base.endsWith(\"/\") || !url.startsWith(\"?\") ? \"/\" : \"\";\r\n    base = withoutTrailingSlash(base);\r\n    url = withoutLeadingSlash(url);\r\n    return \"\" + base + delimiter + url;\r\n}\r\n// src/query/utils/flatten.ts\r\nvar flatten = function (arr) { return [].concat.apply([], arr); };\r\n// src/query/utils/isOnline.ts\r\nfunction isOnline() {\r\n    return typeof navigator === \"undefined\" ? true : navigator.onLine === void 0 ? true : navigator.onLine;\r\n}\r\n// src/query/utils/isDocumentVisible.ts\r\nfunction isDocumentVisible() {\r\n    if (typeof document === \"undefined\") {\r\n        return true;\r\n    }\r\n    return document.visibilityState !== \"hidden\";\r\n}\r\n// src/query/utils/copyWithStructuralSharing.ts\r\nimport { isPlainObject as _iPO } from \"@reduxjs/toolkit\";\r\nvar isPlainObject = _iPO;\r\nfunction copyWithStructuralSharing(oldObj, newObj) {\r\n    if (oldObj === newObj || !(isPlainObject(oldObj) && isPlainObject(newObj) || Array.isArray(oldObj) && Array.isArray(newObj))) {\r\n        return newObj;\r\n    }\r\n    var newKeys = Object.keys(newObj);\r\n    var oldKeys = Object.keys(oldObj);\r\n    var isSameObject = newKeys.length === oldKeys.length;\r\n    var mergeObj = Array.isArray(newObj) ? [] : {};\r\n    for (var _j = 0, newKeys_1 = newKeys; _j < newKeys_1.length; _j++) {\r\n        var key = newKeys_1[_j];\r\n        mergeObj[key] = copyWithStructuralSharing(oldObj[key], newObj[key]);\r\n        if (isSameObject)\r\n            isSameObject = oldObj[key] === mergeObj[key];\r\n    }\r\n    return isSameObject ? oldObj : mergeObj;\r\n}\r\n// src/query/fetchBaseQuery.ts\r\nimport { isPlainObject as isPlainObject2 } from \"@reduxjs/toolkit\";\r\nvar defaultFetchFn = function () {\r\n    var args = [];\r\n    for (var _j = 0; _j < arguments.length; _j++) {\r\n        args[_j] = arguments[_j];\r\n    }\r\n    return fetch.apply(void 0, args);\r\n};\r\nvar defaultValidateStatus = function (response) { return response.status >= 200 && response.status <= 299; };\r\nvar defaultIsJsonContentType = function (headers) { return /ion\\/(vnd\\.api\\+)?json/.test(headers.get(\"content-type\") || \"\"); };\r\nfunction stripUndefined(obj) {\r\n    if (!isPlainObject2(obj)) {\r\n        return obj;\r\n    }\r\n    var copy = __spreadValues({}, obj);\r\n    for (var _j = 0, _k = Object.entries(copy); _j < _k.length; _j++) {\r\n        var _l = _k[_j], k = _l[0], v = _l[1];\r\n        if (v === void 0)\r\n            delete copy[k];\r\n    }\r\n    return copy;\r\n}\r\nfunction fetchBaseQuery(_a) {\r\n    var _this = this;\r\n    if (_a === void 0) { _a = {}; }\r\n    var _b = _a, baseUrl = _b.baseUrl, _j = _b.prepareHeaders, prepareHeaders = _j === void 0 ? function (x) { return x; } : _j, _k = _b.fetchFn, fetchFn = _k === void 0 ? defaultFetchFn : _k, paramsSerializer = _b.paramsSerializer, _l = _b.isJsonContentType, isJsonContentType = _l === void 0 ? defaultIsJsonContentType : _l, _m = _b.jsonContentType, jsonContentType = _m === void 0 ? \"application/json\" : _m, jsonReplacer = _b.jsonReplacer, defaultTimeout = _b.timeout, globalResponseHandler = _b.responseHandler, globalValidateStatus = _b.validateStatus, baseFetchOptions = __objRest(_b, [\r\n        \"baseUrl\",\r\n        \"prepareHeaders\",\r\n        \"fetchFn\",\r\n        \"paramsSerializer\",\r\n        \"isJsonContentType\",\r\n        \"jsonContentType\",\r\n        \"jsonReplacer\",\r\n        \"timeout\",\r\n        \"responseHandler\",\r\n        \"validateStatus\"\r\n    ]);\r\n    if (typeof fetch === \"undefined\" && fetchFn === defaultFetchFn) {\r\n        console.warn(\"Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments.\");\r\n    }\r\n    return function (arg, api) { return __async(_this, null, function () {\r\n        var signal, getState, extra, endpoint, forced, type, meta, _a2, url, _j, headers, _k, params, _l, responseHandler, _m, validateStatus, _o, timeout, rest, config, _p, isJsonifiable, divider, query, request, requestClone, response, timedOut, timeoutId, e_1, responseClone, resultData, responseText, handleResponseError_1, e_2;\r\n        return __generator(this, function (_q) {\r\n            switch (_q.label) {\r\n                case 0:\r\n                    signal = api.signal, getState = api.getState, extra = api.extra, endpoint = api.endpoint, forced = api.forced, type = api.type;\r\n                    _a2 = typeof arg == \"string\" ? { url: arg } : arg, url = _a2.url, _j = _a2.headers, headers = _j === void 0 ? new Headers(baseFetchOptions.headers) : _j, _k = _a2.params, params = _k === void 0 ? void 0 : _k, _l = _a2.responseHandler, responseHandler = _l === void 0 ? globalResponseHandler != null ? globalResponseHandler : \"json\" : _l, _m = _a2.validateStatus, validateStatus = _m === void 0 ? globalValidateStatus != null ? globalValidateStatus : defaultValidateStatus : _m, _o = _a2.timeout, timeout = _o === void 0 ? defaultTimeout : _o, rest = __objRest(_a2, [\r\n                        \"url\",\r\n                        \"headers\",\r\n                        \"params\",\r\n                        \"responseHandler\",\r\n                        \"validateStatus\",\r\n                        \"timeout\"\r\n                    ]);\r\n                    config = __spreadValues(__spreadProps(__spreadValues({}, baseFetchOptions), {\r\n                        signal: signal\r\n                    }), rest);\r\n                    headers = new Headers(stripUndefined(headers));\r\n                    _p = config;\r\n                    return [4 /*yield*/, prepareHeaders(headers, {\r\n                            getState: getState,\r\n                            extra: extra,\r\n                            endpoint: endpoint,\r\n                            forced: forced,\r\n                            type: type\r\n                        })];\r\n                case 1:\r\n                    _p.headers = (_q.sent()) || headers;\r\n                    isJsonifiable = function (body) { return typeof body === \"object\" && (isPlainObject2(body) || Array.isArray(body) || typeof body.toJSON === \"function\"); };\r\n                    if (!config.headers.has(\"content-type\") && isJsonifiable(config.body)) {\r\n                        config.headers.set(\"content-type\", jsonContentType);\r\n                    }\r\n                    if (isJsonifiable(config.body) && isJsonContentType(config.headers)) {\r\n                        config.body = JSON.stringify(config.body, jsonReplacer);\r\n                    }\r\n                    if (params) {\r\n                        divider = ~url.indexOf(\"?\") ? \"&\" : \"?\";\r\n                        query = paramsSerializer ? paramsSerializer(params) : new URLSearchParams(stripUndefined(params));\r\n                        url += divider + query;\r\n                    }\r\n                    url = joinUrls(baseUrl, url);\r\n                    request = new Request(url, config);\r\n                    requestClone = new Request(url, config);\r\n                    meta = { request: requestClone };\r\n                    timedOut = false, timeoutId = timeout && setTimeout(function () {\r\n                        timedOut = true;\r\n                        api.abort();\r\n                    }, timeout);\r\n                    _q.label = 2;\r\n                case 2:\r\n                    _q.trys.push([2, 4, 5, 6]);\r\n                    return [4 /*yield*/, fetchFn(request)];\r\n                case 3:\r\n                    response = _q.sent();\r\n                    return [3 /*break*/, 6];\r\n                case 4:\r\n                    e_1 = _q.sent();\r\n                    return [2 /*return*/, {\r\n                            error: {\r\n                                status: timedOut ? \"TIMEOUT_ERROR\" : \"FETCH_ERROR\",\r\n                                error: String(e_1)\r\n                            },\r\n                            meta: meta\r\n                        }];\r\n                case 5:\r\n                    if (timeoutId)\r\n                        clearTimeout(timeoutId);\r\n                    return [7 /*endfinally*/];\r\n                case 6:\r\n                    responseClone = response.clone();\r\n                    meta.response = responseClone;\r\n                    responseText = \"\";\r\n                    _q.label = 7;\r\n                case 7:\r\n                    _q.trys.push([7, 9, , 10]);\r\n                    return [4 /*yield*/, Promise.all([\r\n                            handleResponse(response, responseHandler).then(function (r) { return resultData = r; }, function (e) { return handleResponseError_1 = e; }),\r\n                            responseClone.text().then(function (r) { return responseText = r; }, function () {\r\n                            })\r\n                        ])];\r\n                case 8:\r\n                    _q.sent();\r\n                    if (handleResponseError_1)\r\n                        throw handleResponseError_1;\r\n                    return [3 /*break*/, 10];\r\n                case 9:\r\n                    e_2 = _q.sent();\r\n                    return [2 /*return*/, {\r\n                            error: {\r\n                                status: \"PARSING_ERROR\",\r\n                                originalStatus: response.status,\r\n                                data: responseText,\r\n                                error: String(e_2)\r\n                            },\r\n                            meta: meta\r\n                        }];\r\n                case 10: return [2 /*return*/, validateStatus(response, resultData) ? {\r\n                        data: resultData,\r\n                        meta: meta\r\n                    } : {\r\n                        error: {\r\n                            status: response.status,\r\n                            data: resultData\r\n                        },\r\n                        meta: meta\r\n                    }];\r\n            }\r\n        });\r\n    }); };\r\n    function handleResponse(response, responseHandler) {\r\n        return __async(this, null, function () {\r\n            var text;\r\n            return __generator(this, function (_j) {\r\n                switch (_j.label) {\r\n                    case 0:\r\n                        if (typeof responseHandler === \"function\") {\r\n                            return [2 /*return*/, responseHandler(response)];\r\n                        }\r\n                        if (responseHandler === \"content-type\") {\r\n                            responseHandler = isJsonContentType(response.headers) ? \"json\" : \"text\";\r\n                        }\r\n                        if (!(responseHandler === \"json\")) return [3 /*break*/, 2];\r\n                        return [4 /*yield*/, response.text()];\r\n                    case 1:\r\n                        text = _j.sent();\r\n                        return [2 /*return*/, text.length ? JSON.parse(text) : null];\r\n                    case 2: return [2 /*return*/, response.text()];\r\n                }\r\n            });\r\n        });\r\n    }\r\n}\r\n// src/query/HandledError.ts\r\nvar HandledError = /** @class */ (function () {\r\n    function HandledError(value, meta) {\r\n        if (meta === void 0) { meta = void 0; }\r\n        this.value = value;\r\n        this.meta = meta;\r\n    }\r\n    return HandledError;\r\n}());\r\n// src/query/retry.ts\r\nfunction defaultBackoff(attempt, maxRetries) {\r\n    if (attempt === void 0) { attempt = 0; }\r\n    if (maxRetries === void 0) { maxRetries = 5; }\r\n    return __async(this, null, function () {\r\n        var attempts, timeout;\r\n        return __generator(this, function (_j) {\r\n            switch (_j.label) {\r\n                case 0:\r\n                    attempts = Math.min(attempt, maxRetries);\r\n                    timeout = ~~((Math.random() + 0.4) * (300 << attempts));\r\n                    return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(function (res) { return resolve(res); }, timeout); })];\r\n                case 1:\r\n                    _j.sent();\r\n                    return [2 /*return*/];\r\n            }\r\n        });\r\n    });\r\n}\r\nfunction fail(e) {\r\n    throw Object.assign(new HandledError({ error: e }), {\r\n        throwImmediately: true\r\n    });\r\n}\r\nvar EMPTY_OPTIONS = {};\r\nvar retryWithBackoff = function (baseQuery, defaultOptions) { return function (args, api, extraOptions) { return __async(void 0, null, function () {\r\n    var possibleMaxRetries, maxRetries, defaultRetryCondition, options, retry2, result, e_3;\r\n    return __generator(this, function (_j) {\r\n        switch (_j.label) {\r\n            case 0:\r\n                possibleMaxRetries = [\r\n                    5,\r\n                    (defaultOptions || EMPTY_OPTIONS).maxRetries,\r\n                    (extraOptions || EMPTY_OPTIONS).maxRetries\r\n                ].filter(function (x) { return x !== void 0; });\r\n                maxRetries = possibleMaxRetries.slice(-1)[0];\r\n                defaultRetryCondition = function (_, __, _j) {\r\n                    var attempt = _j.attempt;\r\n                    return attempt <= maxRetries;\r\n                };\r\n                options = __spreadValues(__spreadValues({\r\n                    maxRetries: maxRetries,\r\n                    backoff: defaultBackoff,\r\n                    retryCondition: defaultRetryCondition\r\n                }, defaultOptions), extraOptions);\r\n                retry2 = 0;\r\n                _j.label = 1;\r\n            case 1:\r\n                if (!true) return [3 /*break*/, 7];\r\n                _j.label = 2;\r\n            case 2:\r\n                _j.trys.push([2, 4, , 6]);\r\n                return [4 /*yield*/, baseQuery(args, api, extraOptions)];\r\n            case 3:\r\n                result = _j.sent();\r\n                if (result.error) {\r\n                    throw new HandledError(result);\r\n                }\r\n                return [2 /*return*/, result];\r\n            case 4:\r\n                e_3 = _j.sent();\r\n                retry2++;\r\n                if (e_3.throwImmediately) {\r\n                    if (e_3 instanceof HandledError) {\r\n                        return [2 /*return*/, e_3.value];\r\n                    }\r\n                    throw e_3;\r\n                }\r\n                if (e_3 instanceof HandledError && !options.retryCondition(e_3.value.error, args, {\r\n                    attempt: retry2,\r\n                    baseQueryApi: api,\r\n                    extraOptions: extraOptions\r\n                })) {\r\n                    return [2 /*return*/, e_3.value];\r\n                }\r\n                return [4 /*yield*/, options.backoff(retry2, options.maxRetries)];\r\n            case 5:\r\n                _j.sent();\r\n                return [3 /*break*/, 6];\r\n            case 6: return [3 /*break*/, 1];\r\n            case 7: return [2 /*return*/];\r\n        }\r\n    });\r\n}); }; };\r\nvar retry = /* @__PURE__ */ Object.assign(retryWithBackoff, { fail: fail });\r\n// src/query/core/setupListeners.ts\r\nimport { createAction } from \"@reduxjs/toolkit\";\r\nvar onFocus = /* @__PURE__ */ createAction(\"__rtkq/focused\");\r\nvar onFocusLost = /* @__PURE__ */ createAction(\"__rtkq/unfocused\");\r\nvar onOnline = /* @__PURE__ */ createAction(\"__rtkq/online\");\r\nvar onOffline = /* @__PURE__ */ createAction(\"__rtkq/offline\");\r\nvar initialized = false;\r\nfunction setupListeners(dispatch, customHandler) {\r\n    function defaultHandler() {\r\n        var handleFocus = function () { return dispatch(onFocus()); };\r\n        var handleFocusLost = function () { return dispatch(onFocusLost()); };\r\n        var handleOnline = function () { return dispatch(onOnline()); };\r\n        var handleOffline = function () { return dispatch(onOffline()); };\r\n        var handleVisibilityChange = function () {\r\n            if (window.document.visibilityState === \"visible\") {\r\n                handleFocus();\r\n            }\r\n            else {\r\n                handleFocusLost();\r\n            }\r\n        };\r\n        if (!initialized) {\r\n            if (typeof window !== \"undefined\" && window.addEventListener) {\r\n                window.addEventListener(\"visibilitychange\", handleVisibilityChange, false);\r\n                window.addEventListener(\"focus\", handleFocus, false);\r\n                window.addEventListener(\"online\", handleOnline, false);\r\n                window.addEventListener(\"offline\", handleOffline, false);\r\n                initialized = true;\r\n            }\r\n        }\r\n        var unsubscribe = function () {\r\n            window.removeEventListener(\"focus\", handleFocus);\r\n            window.removeEventListener(\"visibilitychange\", handleVisibilityChange);\r\n            window.removeEventListener(\"online\", handleOnline);\r\n            window.removeEventListener(\"offline\", handleOffline);\r\n            initialized = false;\r\n        };\r\n        return unsubscribe;\r\n    }\r\n    return customHandler ? customHandler(dispatch, { onFocus: onFocus, onFocusLost: onFocusLost, onOffline: onOffline, onOnline: onOnline }) : defaultHandler();\r\n}\r\n// src/query/core/buildSelectors.ts\r\nimport { createNextState as createNextState2, createSelector } from \"@reduxjs/toolkit\";\r\n// src/query/endpointDefinitions.ts\r\nvar DefinitionType;\r\n(function (DefinitionType2) {\r\n    DefinitionType2[\"query\"] = \"query\";\r\n    DefinitionType2[\"mutation\"] = \"mutation\";\r\n})(DefinitionType || (DefinitionType = {}));\r\nfunction isQueryDefinition(e) {\r\n    return e.type === DefinitionType.query;\r\n}\r\nfunction isMutationDefinition(e) {\r\n    return e.type === DefinitionType.mutation;\r\n}\r\nfunction calculateProvidedBy(description, result, error, queryArg, meta, assertTagTypes) {\r\n    if (isFunction(description)) {\r\n        return description(result, error, queryArg, meta).map(expandTagDescription).map(assertTagTypes);\r\n    }\r\n    if (Array.isArray(description)) {\r\n        return description.map(expandTagDescription).map(assertTagTypes);\r\n    }\r\n    return [];\r\n}\r\nfunction isFunction(t) {\r\n    return typeof t === \"function\";\r\n}\r\nfunction expandTagDescription(description) {\r\n    return typeof description === \"string\" ? { type: description } : description;\r\n}\r\n// src/query/core/buildSlice.ts\r\nimport { combineReducers, createAction as createAction2, createSlice, isAnyOf, isFulfilled as isFulfilled2, isRejectedWithValue as isRejectedWithValue2, createNextState, prepareAutoBatched } from \"@reduxjs/toolkit\";\r\n// src/query/utils/isNotNullish.ts\r\nfunction isNotNullish(v) {\r\n    return v != null;\r\n}\r\n// src/query/core/buildInitiate.ts\r\nvar forceQueryFnSymbol = Symbol(\"forceQueryFn\");\r\nvar isUpsertQuery = function (arg) { return typeof arg[forceQueryFnSymbol] === \"function\"; };\r\nfunction buildInitiate(_j) {\r\n    var serializeQueryArgs = _j.serializeQueryArgs, queryThunk = _j.queryThunk, mutationThunk = _j.mutationThunk, api = _j.api, context = _j.context;\r\n    var runningQueries = new Map();\r\n    var runningMutations = new Map();\r\n    var _k = api.internalActions, unsubscribeQueryResult = _k.unsubscribeQueryResult, removeMutationResult = _k.removeMutationResult, updateSubscriptionOptions = _k.updateSubscriptionOptions;\r\n    return {\r\n        buildInitiateQuery: buildInitiateQuery,\r\n        buildInitiateMutation: buildInitiateMutation,\r\n        getRunningQueryThunk: getRunningQueryThunk,\r\n        getRunningMutationThunk: getRunningMutationThunk,\r\n        getRunningQueriesThunk: getRunningQueriesThunk,\r\n        getRunningMutationsThunk: getRunningMutationsThunk,\r\n        getRunningOperationPromises: getRunningOperationPromises,\r\n        removalWarning: removalWarning\r\n    };\r\n    function removalWarning() {\r\n        throw new Error(\"This method had to be removed due to a conceptual bug in RTK.\\n       Please see https://github.com/reduxjs/redux-toolkit/pull/2481 for details.\\n       See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for new guidance on SSR.\");\r\n    }\r\n    function getRunningOperationPromises() {\r\n        if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\r\n            removalWarning();\r\n        }\r\n        else {\r\n            var extract = function (v) { return Array.from(v.values()).flatMap(function (queriesForStore) { return queriesForStore ? Object.values(queriesForStore) : []; }); };\r\n            return __spreadArray(__spreadArray([], extract(runningQueries)), extract(runningMutations)).filter(isNotNullish);\r\n        }\r\n    }\r\n    function getRunningQueryThunk(endpointName, queryArgs) {\r\n        return function (dispatch) {\r\n            var _a;\r\n            var endpointDefinition = context.endpointDefinitions[endpointName];\r\n            var queryCacheKey = serializeQueryArgs({\r\n                queryArgs: queryArgs,\r\n                endpointDefinition: endpointDefinition,\r\n                endpointName: endpointName\r\n            });\r\n            return (_a = runningQueries.get(dispatch)) == null ? void 0 : _a[queryCacheKey];\r\n        };\r\n    }\r\n    function getRunningMutationThunk(_endpointName, fixedCacheKeyOrRequestId) {\r\n        return function (dispatch) {\r\n            var _a;\r\n            return (_a = runningMutations.get(dispatch)) == null ? void 0 : _a[fixedCacheKeyOrRequestId];\r\n        };\r\n    }\r\n    function getRunningQueriesThunk() {\r\n        return function (dispatch) { return Object.values(runningQueries.get(dispatch) || {}).filter(isNotNullish); };\r\n    }\r\n    function getRunningMutationsThunk() {\r\n        return function (dispatch) { return Object.values(runningMutations.get(dispatch) || {}).filter(isNotNullish); };\r\n    }\r\n    function middlewareWarning(dispatch) {\r\n        if (process.env.NODE_ENV !== \"production\") {\r\n            if (middlewareWarning.triggered)\r\n                return;\r\n            var registered = dispatch(api.internalActions.internal_probeSubscription({\r\n                queryCacheKey: \"DOES_NOT_EXIST\",\r\n                requestId: \"DUMMY_REQUEST_ID\"\r\n            }));\r\n            middlewareWarning.triggered = true;\r\n            if (typeof registered !== \"boolean\") {\r\n                throw new Error(\"Warning: Middleware for RTK-Query API at reducerPath \\\"\" + api.reducerPath + \"\\\" has not been added to the store.\\nYou must add the middleware for RTK-Query to function correctly!\");\r\n            }\r\n        }\r\n    }\r\n    function buildInitiateQuery(endpointName, endpointDefinition) {\r\n        var queryAction = function (arg, _j) {\r\n            var _k = _j === void 0 ? {} : _j, _l = _k.subscribe, subscribe = _l === void 0 ? true : _l, forceRefetch = _k.forceRefetch, subscriptionOptions = _k.subscriptionOptions, _m = forceQueryFnSymbol, forceQueryFn = _k[_m];\r\n            return function (dispatch, getState) {\r\n                var _j;\r\n                var _a;\r\n                var queryCacheKey = serializeQueryArgs({\r\n                    queryArgs: arg,\r\n                    endpointDefinition: endpointDefinition,\r\n                    endpointName: endpointName\r\n                });\r\n                var thunk = queryThunk((_j = {\r\n                        type: \"query\",\r\n                        subscribe: subscribe,\r\n                        forceRefetch: forceRefetch,\r\n                        subscriptionOptions: subscriptionOptions,\r\n                        endpointName: endpointName,\r\n                        originalArgs: arg,\r\n                        queryCacheKey: queryCacheKey\r\n                    },\r\n                    _j[forceQueryFnSymbol] = forceQueryFn,\r\n                    _j));\r\n                var selector = api.endpoints[endpointName].select(arg);\r\n                var thunkResult = dispatch(thunk);\r\n                var stateAfter = selector(getState());\r\n                middlewareWarning(dispatch);\r\n                var requestId = thunkResult.requestId, abort = thunkResult.abort;\r\n                var skippedSynchronously = stateAfter.requestId !== requestId;\r\n                var runningQuery = (_a = runningQueries.get(dispatch)) == null ? void 0 : _a[queryCacheKey];\r\n                var selectFromState = function () { return selector(getState()); };\r\n                var statePromise = Object.assign(forceQueryFn ? thunkResult.then(selectFromState) : skippedSynchronously && !runningQuery ? Promise.resolve(stateAfter) : Promise.all([runningQuery, thunkResult]).then(selectFromState), {\r\n                    arg: arg,\r\n                    requestId: requestId,\r\n                    subscriptionOptions: subscriptionOptions,\r\n                    queryCacheKey: queryCacheKey,\r\n                    abort: abort,\r\n                    unwrap: function () {\r\n                        return __async(this, null, function () {\r\n                            var result;\r\n                            return __generator(this, function (_j) {\r\n                                switch (_j.label) {\r\n                                    case 0: return [4 /*yield*/, statePromise];\r\n                                    case 1:\r\n                                        result = _j.sent();\r\n                                        if (result.isError) {\r\n                                            throw result.error;\r\n                                        }\r\n                                        return [2 /*return*/, result.data];\r\n                                }\r\n                            });\r\n                        });\r\n                    },\r\n                    refetch: function () { return dispatch(queryAction(arg, { subscribe: false, forceRefetch: true })); },\r\n                    unsubscribe: function () {\r\n                        if (subscribe)\r\n                            dispatch(unsubscribeQueryResult({\r\n                                queryCacheKey: queryCacheKey,\r\n                                requestId: requestId\r\n                            }));\r\n                    },\r\n                    updateSubscriptionOptions: function (options) {\r\n                        statePromise.subscriptionOptions = options;\r\n                        dispatch(updateSubscriptionOptions({\r\n                            endpointName: endpointName,\r\n                            requestId: requestId,\r\n                            queryCacheKey: queryCacheKey,\r\n                            options: options\r\n                        }));\r\n                    }\r\n                });\r\n                if (!runningQuery && !skippedSynchronously && !forceQueryFn) {\r\n                    var running_1 = runningQueries.get(dispatch) || {};\r\n                    running_1[queryCacheKey] = statePromise;\r\n                    runningQueries.set(dispatch, running_1);\r\n                    statePromise.then(function () {\r\n                        delete running_1[queryCacheKey];\r\n                        if (!Object.keys(running_1).length) {\r\n                            runningQueries.delete(dispatch);\r\n                        }\r\n                    });\r\n                }\r\n                return statePromise;\r\n            };\r\n        };\r\n        return queryAction;\r\n    }\r\n    function buildInitiateMutation(endpointName) {\r\n        return function (arg, _j) {\r\n            var _k = _j === void 0 ? {} : _j, _l = _k.track, track = _l === void 0 ? true : _l, fixedCacheKey = _k.fixedCacheKey;\r\n            return function (dispatch, getState) {\r\n                var thunk = mutationThunk({\r\n                    type: \"mutation\",\r\n                    endpointName: endpointName,\r\n                    originalArgs: arg,\r\n                    track: track,\r\n                    fixedCacheKey: fixedCacheKey\r\n                });\r\n                var thunkResult = dispatch(thunk);\r\n                middlewareWarning(dispatch);\r\n                var requestId = thunkResult.requestId, abort = thunkResult.abort, unwrap = thunkResult.unwrap;\r\n                var returnValuePromise = thunkResult.unwrap().then(function (data) { return ({ data: data }); }).catch(function (error) { return ({ error: error }); });\r\n                var reset = function () {\r\n                    dispatch(removeMutationResult({ requestId: requestId, fixedCacheKey: fixedCacheKey }));\r\n                };\r\n                var ret = Object.assign(returnValuePromise, {\r\n                    arg: thunkResult.arg,\r\n                    requestId: requestId,\r\n                    abort: abort,\r\n                    unwrap: unwrap,\r\n                    unsubscribe: reset,\r\n                    reset: reset\r\n                });\r\n                var running = runningMutations.get(dispatch) || {};\r\n                runningMutations.set(dispatch, running);\r\n                running[requestId] = ret;\r\n                ret.then(function () {\r\n                    delete running[requestId];\r\n                    if (!Object.keys(running).length) {\r\n                        runningMutations.delete(dispatch);\r\n                    }\r\n                });\r\n                if (fixedCacheKey) {\r\n                    running[fixedCacheKey] = ret;\r\n                    ret.then(function () {\r\n                        if (running[fixedCacheKey] === ret) {\r\n                            delete running[fixedCacheKey];\r\n                            if (!Object.keys(running).length) {\r\n                                runningMutations.delete(dispatch);\r\n                            }\r\n                        }\r\n                    });\r\n                }\r\n                return ret;\r\n            };\r\n        };\r\n    }\r\n}\r\n// src/query/core/buildThunks.ts\r\nimport { isAllOf, isFulfilled, isPending, isRejected, isRejectedWithValue } from \"@reduxjs/toolkit\";\r\nimport { isDraftable, produceWithPatches } from \"immer\";\r\nimport { createAsyncThunk, SHOULD_AUTOBATCH } from \"@reduxjs/toolkit\";\r\nfunction defaultTransformResponse(baseQueryReturnValue) {\r\n    return baseQueryReturnValue;\r\n}\r\nfunction buildThunks(_j) {\r\n    var _this = this;\r\n    var reducerPath = _j.reducerPath, baseQuery = _j.baseQuery, endpointDefinitions = _j.context.endpointDefinitions, serializeQueryArgs = _j.serializeQueryArgs, api = _j.api, assertTagType = _j.assertTagType;\r\n    var patchQueryData = function (endpointName, args, patches, updateProvided) { return function (dispatch, getState) {\r\n        var endpointDefinition = endpointDefinitions[endpointName];\r\n        var queryCacheKey = serializeQueryArgs({\r\n            queryArgs: args,\r\n            endpointDefinition: endpointDefinition,\r\n            endpointName: endpointName\r\n        });\r\n        dispatch(api.internalActions.queryResultPatched({ queryCacheKey: queryCacheKey, patches: patches }));\r\n        if (!updateProvided) {\r\n            return;\r\n        }\r\n        var newValue = api.endpoints[endpointName].select(args)(getState());\r\n        var providedTags = calculateProvidedBy(endpointDefinition.providesTags, newValue.data, void 0, args, {}, assertTagType);\r\n        dispatch(api.internalActions.updateProvidedBy({ queryCacheKey: queryCacheKey, providedTags: providedTags }));\r\n    }; };\r\n    var updateQueryData = function (endpointName, args, updateRecipe, updateProvided) {\r\n        if (updateProvided === void 0) { updateProvided = true; }\r\n        return function (dispatch, getState) {\r\n            var _j, _k;\r\n            var endpointDefinition = api.endpoints[endpointName];\r\n            var currentState = endpointDefinition.select(args)(getState());\r\n            var ret = {\r\n                patches: [],\r\n                inversePatches: [],\r\n                undo: function () { return dispatch(api.util.patchQueryData(endpointName, args, ret.inversePatches, updateProvided)); }\r\n            };\r\n            if (currentState.status === QueryStatus.uninitialized) {\r\n                return ret;\r\n            }\r\n            var newValue;\r\n            if (\"data\" in currentState) {\r\n                if (isDraftable(currentState.data)) {\r\n                    var _l = produceWithPatches(currentState.data, updateRecipe), value = _l[0], patches = _l[1], inversePatches = _l[2];\r\n                    (_j = ret.patches).push.apply(_j, patches);\r\n                    (_k = ret.inversePatches).push.apply(_k, inversePatches);\r\n                    newValue = value;\r\n                }\r\n                else {\r\n                    newValue = updateRecipe(currentState.data);\r\n                    ret.patches.push({ op: \"replace\", path: [], value: newValue });\r\n                    ret.inversePatches.push({\r\n                        op: \"replace\",\r\n                        path: [],\r\n                        value: currentState.data\r\n                    });\r\n                }\r\n            }\r\n            dispatch(api.util.patchQueryData(endpointName, args, ret.patches, updateProvided));\r\n            return ret;\r\n        };\r\n    };\r\n    var upsertQueryData = function (endpointName, args, value) { return function (dispatch) {\r\n        var _j;\r\n        return dispatch(api.endpoints[endpointName].initiate(args, (_j = {\r\n                subscribe: false,\r\n                forceRefetch: true\r\n            },\r\n            _j[forceQueryFnSymbol] = function () { return ({\r\n                data: value\r\n            }); },\r\n            _j)));\r\n    }; };\r\n    var executeEndpoint = function (_0, _1) { return __async(_this, [_0, _1], function (arg, _j) {\r\n        var endpointDefinition, transformResponse, result, baseQueryApi_1, forceQueryFn, what, err, _k, _l, key, _m, error_1, catchedError, transformErrorResponse, _o, e_4;\r\n        var _p, _q;\r\n        var signal = _j.signal, abort = _j.abort, rejectWithValue = _j.rejectWithValue, fulfillWithValue = _j.fulfillWithValue, dispatch = _j.dispatch, getState = _j.getState, extra = _j.extra;\r\n        return __generator(this, function (_r) {\r\n            switch (_r.label) {\r\n                case 0:\r\n                    endpointDefinition = endpointDefinitions[arg.endpointName];\r\n                    _r.label = 1;\r\n                case 1:\r\n                    _r.trys.push([1, 8, , 13]);\r\n                    transformResponse = defaultTransformResponse;\r\n                    result = void 0;\r\n                    baseQueryApi_1 = {\r\n                        signal: signal,\r\n                        abort: abort,\r\n                        dispatch: dispatch,\r\n                        getState: getState,\r\n                        extra: extra,\r\n                        endpoint: arg.endpointName,\r\n                        type: arg.type,\r\n                        forced: arg.type === \"query\" ? isForcedQuery(arg, getState()) : void 0\r\n                    };\r\n                    forceQueryFn = arg.type === \"query\" ? arg[forceQueryFnSymbol] : void 0;\r\n                    if (!forceQueryFn) return [3 /*break*/, 2];\r\n                    result = forceQueryFn();\r\n                    return [3 /*break*/, 6];\r\n                case 2:\r\n                    if (!endpointDefinition.query) return [3 /*break*/, 4];\r\n                    return [4 /*yield*/, baseQuery(endpointDefinition.query(arg.originalArgs), baseQueryApi_1, endpointDefinition.extraOptions)];\r\n                case 3:\r\n                    result = _r.sent();\r\n                    if (endpointDefinition.transformResponse) {\r\n                        transformResponse = endpointDefinition.transformResponse;\r\n                    }\r\n                    return [3 /*break*/, 6];\r\n                case 4: return [4 /*yield*/, endpointDefinition.queryFn(arg.originalArgs, baseQueryApi_1, endpointDefinition.extraOptions, function (arg2) { return baseQuery(arg2, baseQueryApi_1, endpointDefinition.extraOptions); })];\r\n                case 5:\r\n                    result = _r.sent();\r\n                    _r.label = 6;\r\n                case 6:\r\n                    if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\r\n                        what = endpointDefinition.query ? \"`baseQuery`\" : \"`queryFn`\";\r\n                        err = void 0;\r\n                        if (!result) {\r\n                            err = what + \" did not return anything.\";\r\n                        }\r\n                        else if (typeof result !== \"object\") {\r\n                            err = what + \" did not return an object.\";\r\n                        }\r\n                        else if (result.error && result.data) {\r\n                            err = what + \" returned an object containing both `error` and `result`.\";\r\n                        }\r\n                        else if (result.error === void 0 && result.data === void 0) {\r\n                            err = what + \" returned an object containing neither a valid `error` and `result`. At least one of them should not be `undefined`\";\r\n                        }\r\n                        else {\r\n                            for (_k = 0, _l = Object.keys(result); _k < _l.length; _k++) {\r\n                                key = _l[_k];\r\n                                if (key !== \"error\" && key !== \"data\" && key !== \"meta\") {\r\n                                    err = \"The object returned by \" + what + \" has the unknown property \" + key + \".\";\r\n                                    break;\r\n                                }\r\n                            }\r\n                        }\r\n                        if (err) {\r\n                            console.error(\"Error encountered handling the endpoint \" + arg.endpointName + \".\\n              \" + err + \"\\n              It needs to return an object with either the shape `{ data: <value> }` or `{ error: <value> }` that may contain an optional `meta` property.\\n              Object returned was:\", result);\r\n                        }\r\n                    }\r\n                    if (result.error)\r\n                        throw new HandledError(result.error, result.meta);\r\n                    _m = fulfillWithValue;\r\n                    return [4 /*yield*/, transformResponse(result.data, result.meta, arg.originalArgs)];\r\n                case 7: return [2 /*return*/, _m.apply(void 0, [_r.sent(), (_p = {\r\n                                fulfilledTimeStamp: Date.now(),\r\n                                baseQueryMeta: result.meta\r\n                            },\r\n                            _p[SHOULD_AUTOBATCH] = true,\r\n                            _p)])];\r\n                case 8:\r\n                    error_1 = _r.sent();\r\n                    catchedError = error_1;\r\n                    if (!(catchedError instanceof HandledError)) return [3 /*break*/, 12];\r\n                    transformErrorResponse = defaultTransformResponse;\r\n                    if (endpointDefinition.query && endpointDefinition.transformErrorResponse) {\r\n                        transformErrorResponse = endpointDefinition.transformErrorResponse;\r\n                    }\r\n                    _r.label = 9;\r\n                case 9:\r\n                    _r.trys.push([9, 11, , 12]);\r\n                    _o = rejectWithValue;\r\n                    return [4 /*yield*/, transformErrorResponse(catchedError.value, catchedError.meta, arg.originalArgs)];\r\n                case 10: return [2 /*return*/, _o.apply(void 0, [_r.sent(), (_q = { baseQueryMeta: catchedError.meta }, _q[SHOULD_AUTOBATCH] = true, _q)])];\r\n                case 11:\r\n                    e_4 = _r.sent();\r\n                    catchedError = e_4;\r\n                    return [3 /*break*/, 12];\r\n                case 12:\r\n                    if (typeof process !== \"undefined\" && process.env.NODE_ENV !== \"production\") {\r\n                        console.error(\"An unhandled error occurred processing a request for the endpoint \\\"\" + arg.endpointName + \"\\\".\\nIn the case of an unhandled error, no tags will be \\\"provided\\\" or \\\"invalidated\\\".\", catchedError);\r\n                    }\r\n                    else {\r\n                        console.error(catchedError);\r\n                    }\r\n                    throw catchedError;\r\n                case 13: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); };\r\n    function isForcedQuery(arg, state) {\r\n        var _a, _b, _c, _d;\r\n        var requestState = (_b = (_a = state[reducerPath]) == null ? void 0 : _a.queries) == null ? void 0 : _b[arg.queryCacheKey];\r\n        var baseFetchOnMountOrArgChange = (_c = state[reducerPath]) == null ? void 0 : _c.config.refetchOnMountOrArgChange;\r\n        var fulfilledVal = requestState == null ? void 0 : requestState.fulfilledTimeStamp;\r\n        var refetchVal = (_d = arg.forceRefetch) != null ? _d : arg.subscribe && baseFetchOnMountOrArgChange;\r\n        if (refetchVal) {\r\n            return refetchVal === true || (Number(new Date()) - Number(fulfilledVal)) / 1e3 >= refetchVal;\r\n        }\r\n        return false;\r\n    }\r\n    var queryThunk = createAsyncThunk(reducerPath + \"/executeQuery\", executeEndpoint, {\r\n        getPendingMeta: function () {\r\n            var _j;\r\n            return _j = { startedTimeStamp: Date.now() }, _j[SHOULD_AUTOBATCH] = true, _j;\r\n        },\r\n        condition: function (queryThunkArgs, _j) {\r\n            var getState = _j.getState;\r\n            var _a, _b, _c;\r\n            var state = getState();\r\n            var requestState = (_b = (_a = state[reducerPath]) == null ? void 0 : _a.queries) == null ? void 0 : _b[queryThunkArgs.queryCacheKey];\r\n            var fulfilledVal = requestState == null ? void 0 : requestState.fulfilledTimeStamp;\r\n            var currentArg = queryThunkArgs.originalArgs;\r\n            var previousArg = requestState == null ? void 0 : requestState.originalArgs;\r\n            var endpointDefinition = endpointDefinitions[queryThunkArgs.endpointName];\r\n            if (isUpsertQuery(queryThunkArgs)) {\r\n                return true;\r\n            }\r\n            if ((requestState == null ? void 0 : requestState.status) === \"pending\") {\r\n                return false;\r\n            }\r\n            if (isForcedQuery(queryThunkArgs, state)) {\r\n                return true;\r\n            }\r\n            if (isQueryDefinition(endpointDefinition) && ((_c = endpointDefinition == null ? void 0 : endpointDefinition.forceRefetch) == null ? void 0 : _c.call(endpointDefinition, {\r\n                currentArg: currentArg,\r\n                previousArg: previousArg,\r\n                endpointState: requestState,\r\n                state: state\r\n            }))) {\r\n                return true;\r\n            }\r\n            if (fulfilledVal) {\r\n                return false;\r\n            }\r\n            return true;\r\n        },\r\n        dispatchConditionRejection: true\r\n    });\r\n    var mutationThunk = createAsyncThunk(reducerPath + \"/executeMutation\", executeEndpoint, {\r\n        getPendingMeta: function () {\r\n            var _j;\r\n            return _j = { startedTimeStamp: Date.now() }, _j[SHOULD_AUTOBATCH] = true, _j;\r\n        }\r\n    });\r\n    var hasTheForce = function (options) { return \"force\" in options; };\r\n    var hasMaxAge = function (options) { return \"ifOlderThan\" in options; };\r\n    var prefetch = function (endpointName, arg, options) { return function (dispatch, getState) {\r\n        var force = hasTheForce(options) && options.force;\r\n        var maxAge = hasMaxAge(options) && options.ifOlderThan;\r\n        var queryAction = function (force2) {\r\n            if (force2 === void 0) { force2 = true; }\r\n            return api.endpoints[endpointName].initiate(arg, { forceRefetch: force2 });\r\n        };\r\n        var latestStateValue = api.endpoints[endpointName].select(arg)(getState());\r\n        if (force) {\r\n            dispatch(queryAction());\r\n        }\r\n        else if (maxAge) {\r\n            var lastFulfilledTs = latestStateValue == null ? void 0 : latestStateValue.fulfilledTimeStamp;\r\n            if (!lastFulfilledTs) {\r\n                dispatch(queryAction());\r\n                return;\r\n            }\r\n            var shouldRetrigger = (Number(new Date()) - Number(new Date(lastFulfilledTs))) / 1e3 >= maxAge;\r\n            if (shouldRetrigger) {\r\n                dispatch(queryAction());\r\n            }\r\n        }\r\n        else {\r\n            dispatch(queryAction(false));\r\n        }\r\n    }; };\r\n    function matchesEndpoint(endpointName) {\r\n        return function (action) {\r\n            var _a, _b;\r\n            return ((_b = (_a = action == null ? void 0 : action.meta) == null ? void 0 : _a.arg) == null ? void 0 : _b.endpointName) === endpointName;\r\n        };\r\n    }\r\n    function buildMatchThunkActions(thunk, endpointName) {\r\n        return {\r\n            matchPending: isAllOf(isPending(thunk), matchesEndpoint(endpointName)),\r\n            matchFulfilled: isAllOf(isFulfilled(thunk), matchesEndpoint(endpointName)),\r\n            matchRejected: isAllOf(isRejected(thunk), matchesEndpoint(endpointName))\r\n        };\r\n    }\r\n    return {\r\n        queryThunk: queryThunk,\r\n        mutationThunk: mutationThunk,\r\n        prefetch: prefetch,\r\n        updateQueryData: updateQueryData,\r\n        upsertQueryData: upsertQueryData,\r\n        patchQueryData: patchQueryData,\r\n        buildMatchThunkActions: buildMatchThunkActions\r\n    };\r\n}\r\nfunction calculateProvidedByThunk(action, type, endpointDefinitions, assertTagType) {\r\n    return calculateProvidedBy(endpointDefinitions[action.meta.arg.endpointName][type], isFulfilled(action) ? action.payload : void 0, isRejectedWithValue(action) ? action.payload : void 0, action.meta.arg.originalArgs, \"baseQueryMeta\" in action.meta ? action.meta.baseQueryMeta : void 0, assertTagType);\r\n}\r\n// src/query/core/buildSlice.ts\r\nimport { isDraft } from \"immer\";\r\nimport { applyPatches, original } from \"immer\";\r\nfunction updateQuerySubstateIfExists(state, queryCacheKey, update) {\r\n    var substate = state[queryCacheKey];\r\n    if (substate) {\r\n        update(substate);\r\n    }\r\n}\r\nfunction getMutationCacheKey(id) {\r\n    var _a;\r\n    return (_a = \"arg\" in id ? id.arg.fixedCacheKey : id.fixedCacheKey) != null ? _a : id.requestId;\r\n}\r\nfunction updateMutationSubstateIfExists(state, id, update) {\r\n    var substate = state[getMutationCacheKey(id)];\r\n    if (substate) {\r\n        update(substate);\r\n    }\r\n}\r\nvar initialState = {};\r\nfunction buildSlice(_j) {\r\n    var reducerPath = _j.reducerPath, queryThunk = _j.queryThunk, mutationThunk = _j.mutationThunk, _k = _j.context, definitions = _k.endpointDefinitions, apiUid = _k.apiUid, extractRehydrationInfo = _k.extractRehydrationInfo, hasRehydrationInfo = _k.hasRehydrationInfo, assertTagType = _j.assertTagType, config = _j.config;\r\n    var resetApiState = createAction2(reducerPath + \"/resetApiState\");\r\n    var querySlice = createSlice({\r\n        name: reducerPath + \"/queries\",\r\n        initialState: initialState,\r\n        reducers: {\r\n            removeQueryResult: {\r\n                reducer: function (draft, _j) {\r\n                    var queryCacheKey = _j.payload.queryCacheKey;\r\n                    delete draft[queryCacheKey];\r\n                },\r\n                prepare: prepareAutoBatched()\r\n            },\r\n            queryResultPatched: {\r\n                reducer: function (draft, _j) {\r\n                    var _k = _j.payload, queryCacheKey = _k.queryCacheKey, patches = _k.patches;\r\n                    updateQuerySubstateIfExists(draft, queryCacheKey, function (substate) {\r\n                        substate.data = applyPatches(substate.data, patches.concat());\r\n                    });\r\n                },\r\n                prepare: prepareAutoBatched()\r\n            }\r\n        },\r\n        extraReducers: function (builder) {\r\n            builder.addCase(queryThunk.pending, function (draft, _j) {\r\n                var meta = _j.meta, arg = _j.meta.arg;\r\n                var _a, _b;\r\n                var upserting = isUpsertQuery(arg);\r\n                if (arg.subscribe || upserting) {\r\n                    (_b = draft[_a = arg.queryCacheKey]) != null ? _b : draft[_a] = {\r\n                        status: QueryStatus.uninitialized,\r\n                        endpointName: arg.endpointName\r\n                    };\r\n                }\r\n                updateQuerySubstateIfExists(draft, arg.queryCacheKey, function (substate) {\r\n                    substate.status = QueryStatus.pending;\r\n                    substate.requestId = upserting && substate.requestId ? substate.requestId : meta.requestId;\r\n                    if (arg.originalArgs !== void 0) {\r\n                        substate.originalArgs = arg.originalArgs;\r\n                    }\r\n                    substate.startedTimeStamp = meta.startedTimeStamp;\r\n                });\r\n            }).addCase(queryThunk.fulfilled, function (draft, _j) {\r\n                var meta = _j.meta, payload = _j.payload;\r\n                updateQuerySubstateIfExists(draft, meta.arg.queryCacheKey, function (substate) {\r\n                    var _a;\r\n                    if (substate.requestId !== meta.requestId && !isUpsertQuery(meta.arg))\r\n                        return;\r\n                    var merge = definitions[meta.arg.endpointName].merge;\r\n                    substate.status = QueryStatus.fulfilled;\r\n                    if (merge) {\r\n                        if (substate.data !== void 0) {\r\n                            var fulfilledTimeStamp_1 = meta.fulfilledTimeStamp, arg_1 = meta.arg, baseQueryMeta_1 = meta.baseQueryMeta, requestId_1 = meta.requestId;\r\n                            var newData = createNextState(substate.data, function (draftSubstateData) {\r\n                                return merge(draftSubstateData, payload, {\r\n                                    arg: arg_1.originalArgs,\r\n                                    baseQueryMeta: baseQueryMeta_1,\r\n                                    fulfilledTimeStamp: fulfilledTimeStamp_1,\r\n                                    requestId: requestId_1\r\n                                });\r\n                            });\r\n                            substate.data = newData;\r\n                        }\r\n                        else {\r\n                            substate.data = payload;\r\n                        }\r\n                    }\r\n                    else {\r\n                        substate.data = ((_a = definitions[meta.arg.endpointName].structuralSharing) != null ? _a : true) ? copyWithStructuralSharing(isDraft(substate.data) ? original(substate.data) : substate.data, payload) : payload;\r\n                    }\r\n                    delete substate.error;\r\n                    substate.fulfilledTimeStamp = meta.fulfilledTimeStamp;\r\n                });\r\n            }).addCase(queryThunk.rejected, function (draft, _j) {\r\n                var _k = _j.meta, condition = _k.condition, arg = _k.arg, requestId = _k.requestId, error = _j.error, payload = _j.payload;\r\n                updateQuerySubstateIfExists(draft, arg.queryCacheKey, function (substate) {\r\n                    if (condition) {\r\n                    }\r\n                    else {\r\n                        if (substate.requestId !== requestId)\r\n                            return;\r\n                        substate.status = QueryStatus.rejected;\r\n                        substate.error = payload != null ? payload : error;\r\n                    }\r\n                });\r\n            }).addMatcher(hasRehydrationInfo, function (draft, action) {\r\n                var queries = extractRehydrationInfo(action).queries;\r\n                for (var _j = 0, _k = Object.entries(queries); _j < _k.length; _j++) {\r\n                    var _l = _k[_j], key = _l[0], entry = _l[1];\r\n                    if ((entry == null ? void 0 : entry.status) === QueryStatus.fulfilled || (entry == null ? void 0 : entry.status) === QueryStatus.rejected) {\r\n                        draft[key] = entry;\r\n                    }\r\n                }\r\n            });\r\n        }\r\n    });\r\n    var mutationSlice = createSlice({\r\n        name: reducerPath + \"/mutations\",\r\n        initialState: initialState,\r\n        reducers: {\r\n            removeMutationResult: {\r\n                reducer: function (draft, _j) {\r\n                    var payload = _j.payload;\r\n                    var cacheKey = getMutationCacheKey(payload);\r\n                    if (cacheKey in draft) {\r\n                        delete draft[cacheKey];\r\n                    }\r\n                },\r\n                prepare: prepareAutoBatched()\r\n            }\r\n        },\r\n        extraReducers: function (builder) {\r\n            builder.addCase(mutationThunk.pending, function (draft, _j) {\r\n                var meta = _j.meta, _k = _j.meta, requestId = _k.requestId, arg = _k.arg, startedTimeStamp = _k.startedTimeStamp;\r\n                if (!arg.track)\r\n                    return;\r\n                draft[getMutationCacheKey(meta)] = {\r\n                    requestId: requestId,\r\n                    status: QueryStatus.pending,\r\n                    endpointName: arg.endpointName,\r\n                    startedTimeStamp: startedTimeStamp\r\n                };\r\n            }).addCase(mutationThunk.fulfilled, function (draft, _j) {\r\n                var payload = _j.payload, meta = _j.meta;\r\n                if (!meta.arg.track)\r\n                    return;\r\n                updateMutationSubstateIfExists(draft, meta, function (substate) {\r\n                    if (substate.requestId !== meta.requestId)\r\n                        return;\r\n                    substate.status = QueryStatus.fulfilled;\r\n                    substate.data = payload;\r\n                    substate.fulfilledTimeStamp = meta.fulfilledTimeStamp;\r\n                });\r\n            }).addCase(mutationThunk.rejected, function (draft, _j) {\r\n                var payload = _j.payload, error = _j.error, meta = _j.meta;\r\n                if (!meta.arg.track)\r\n                    return;\r\n                updateMutationSubstateIfExists(draft, meta, function (substate) {\r\n                    if (substate.requestId !== meta.requestId)\r\n                        return;\r\n                    substate.status = QueryStatus.rejected;\r\n                    substate.error = payload != null ? payload : error;\r\n                });\r\n            }).addMatcher(hasRehydrationInfo, function (draft, action) {\r\n                var mutations = extractRehydrationInfo(action).mutations;\r\n                for (var _j = 0, _k = Object.entries(mutations); _j < _k.length; _j++) {\r\n                    var _l = _k[_j], key = _l[0], entry = _l[1];\r\n                    if (((entry == null ? void 0 : entry.status) === QueryStatus.fulfilled || (entry == null ? void 0 : entry.status) === QueryStatus.rejected) && key !== (entry == null ? void 0 : entry.requestId)) {\r\n                        draft[key] = entry;\r\n                    }\r\n                }\r\n            });\r\n        }\r\n    });\r\n    var invalidationSlice = createSlice({\r\n        name: reducerPath + \"/invalidation\",\r\n        initialState: initialState,\r\n        reducers: {\r\n            updateProvidedBy: {\r\n                reducer: function (draft, action) {\r\n                    var _a, _b, _c, _d;\r\n                    var _j = action.payload, queryCacheKey = _j.queryCacheKey, providedTags = _j.providedTags;\r\n                    for (var _k = 0, _l = Object.values(draft); _k < _l.length; _k++) {\r\n                        var tagTypeSubscriptions = _l[_k];\r\n                        for (var _m = 0, _o = Object.values(tagTypeSubscriptions); _m < _o.length; _m++) {\r\n                            var idSubscriptions = _o[_m];\r\n                            var foundAt = idSubscriptions.indexOf(queryCacheKey);\r\n                            if (foundAt !== -1) {\r\n                                idSubscriptions.splice(foundAt, 1);\r\n                            }\r\n                        }\r\n                    }\r\n                    for (var _p = 0, providedTags_1 = providedTags; _p < providedTags_1.length; _p++) {\r\n                        var _q = providedTags_1[_p], type = _q.type, id = _q.id;\r\n                        var subscribedQueries = (_d = (_b = (_a = draft[type]) != null ? _a : draft[type] = {})[_c = id || \"__internal_without_id\"]) != null ? _d : _b[_c] = [];\r\n                        var alreadySubscribed = subscribedQueries.includes(queryCacheKey);\r\n                        if (!alreadySubscribed) {\r\n                            subscribedQueries.push(queryCacheKey);\r\n                        }\r\n                    }\r\n                },\r\n                prepare: prepareAutoBatched()\r\n            }\r\n        },\r\n        extraReducers: function (builder) {\r\n            builder.addCase(querySlice.actions.removeQueryResult, function (draft, _j) {\r\n                var queryCacheKey = _j.payload.queryCacheKey;\r\n                for (var _k = 0, _l = Object.values(draft); _k < _l.length; _k++) {\r\n                    var tagTypeSubscriptions = _l[_k];\r\n                    for (var _m = 0, _o = Object.values(tagTypeSubscriptions); _m < _o.length; _m++) {\r\n                        var idSubscriptions = _o[_m];\r\n                        var foundAt = idSubscriptions.indexOf(queryCacheKey);\r\n                        if (foundAt !== -1) {\r\n                            idSubscriptions.splice(foundAt, 1);\r\n                        }\r\n                    }\r\n                }\r\n            }).addMatcher(hasRehydrationInfo, function (draft, action) {\r\n                var _a, _b, _c, _d;\r\n                var provided = extractRehydrationInfo(action).provided;\r\n                for (var _j = 0, _k = Object.entries(provided); _j < _k.length; _j++) {\r\n                    var _l = _k[_j], type = _l[0], incomingTags = _l[1];\r\n                    for (var _m = 0, _o = Object.entries(incomingTags); _m < _o.length; _m++) {\r\n                        var _p = _o[_m], id = _p[0], cacheKeys = _p[1];\r\n                        var subscribedQueries = (_d = (_b = (_a = draft[type]) != null ? _a : draft[type] = {})[_c = id || \"__internal_without_id\"]) != null ? _d : _b[_c] = [];\r\n                        for (var _q = 0, cacheKeys_1 = cacheKeys; _q < cacheKeys_1.length; _q++) {\r\n                            var queryCacheKey = cacheKeys_1[_q];\r\n                            var alreadySubscribed = subscribedQueries.includes(queryCacheKey);\r\n                            if (!alreadySubscribed) {\r\n                                subscribedQueries.push(queryCacheKey);\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }).addMatcher(isAnyOf(isFulfilled2(queryThunk), isRejectedWithValue2(queryThunk)), function (draft, action) {\r\n                var providedTags = calculateProvidedByThunk(action, \"providesTags\", definitions, assertTagType);\r\n                var queryCacheKey = action.meta.arg.queryCacheKey;\r\n                invalidationSlice.caseReducers.updateProvidedBy(draft, invalidationSlice.actions.updateProvidedBy({\r\n                    queryCacheKey: queryCacheKey,\r\n                    providedTags: providedTags\r\n                }));\r\n            });\r\n        }\r\n    });\r\n    var subscriptionSlice = createSlice({\r\n        name: reducerPath + \"/subscriptions\",\r\n        initialState: initialState,\r\n        reducers: {\r\n            updateSubscriptionOptions: function (d, a) {\r\n            },\r\n            unsubscribeQueryResult: function (d, a) {\r\n            },\r\n            internal_probeSubscription: function (d, a) {\r\n            }\r\n        }\r\n    });\r\n    var internalSubscriptionsSlice = createSlice({\r\n        name: reducerPath + \"/internalSubscriptions\",\r\n        initialState: initialState,\r\n        reducers: {\r\n            subscriptionsUpdated: {\r\n                reducer: function (state, action) {\r\n                    return applyPatches(state, action.payload);\r\n                },\r\n                prepare: prepareAutoBatched()\r\n            }\r\n        }\r\n    });\r\n    var configSlice = createSlice({\r\n        name: reducerPath + \"/config\",\r\n        initialState: __spreadValues({\r\n            online: isOnline(),\r\n            focused: isDocumentVisible(),\r\n            middlewareRegistered: false\r\n        }, config),\r\n        reducers: {\r\n            middlewareRegistered: function (state, _j) {\r\n                var payload = _j.payload;\r\n                state.middlewareRegistered = state.middlewareRegistered === \"conflict\" || apiUid !== payload ? \"conflict\" : true;\r\n            }\r\n        },\r\n        extraReducers: function (builder) {\r\n            builder.addCase(onOnline, function (state) {\r\n                state.online = true;\r\n            }).addCase(onOffline, function (state) {\r\n                state.online = false;\r\n            }).addCase(onFocus, function (state) {\r\n                state.focused = true;\r\n            }).addCase(onFocusLost, function (state) {\r\n                state.focused = false;\r\n            }).addMatcher(hasRehydrationInfo, function (draft) { return __spreadValues({}, draft); });\r\n        }\r\n    });\r\n    var combinedReducer = combineReducers({\r\n        queries: querySlice.reducer,\r\n        mutations: mutationSlice.reducer,\r\n        provided: invalidationSlice.reducer,\r\n        subscriptions: internalSubscriptionsSlice.reducer,\r\n        config: configSlice.reducer\r\n    });\r\n    var reducer = function (state, action) { return combinedReducer(resetApiState.match(action) ? void 0 : state, action); };\r\n    var actions = __spreadProps(__spreadValues(__spreadValues(__spreadValues(__spreadValues(__spreadValues(__spreadValues({}, configSlice.actions), querySlice.actions), subscriptionSlice.actions), internalSubscriptionsSlice.actions), mutationSlice.actions), invalidationSlice.actions), {\r\n        unsubscribeMutationResult: mutationSlice.actions.removeMutationResult,\r\n        resetApiState: resetApiState\r\n    });\r\n    return { reducer: reducer, actions: actions };\r\n}\r\n// src/query/core/buildSelectors.ts\r\nvar skipToken = /* @__PURE__ */ Symbol.for(\"RTKQ/skipToken\");\r\nvar skipSelector = skipToken;\r\nvar initialSubState = {\r\n    status: QueryStatus.uninitialized\r\n};\r\nvar defaultQuerySubState = /* @__PURE__ */ createNextState2(initialSubState, function () {\r\n});\r\nvar defaultMutationSubState = /* @__PURE__ */ createNextState2(initialSubState, function () {\r\n});\r\nfunction buildSelectors(_j) {\r\n    var serializeQueryArgs = _j.serializeQueryArgs, reducerPath = _j.reducerPath;\r\n    var selectSkippedQuery = function (state) { return defaultQuerySubState; };\r\n    var selectSkippedMutation = function (state) { return defaultMutationSubState; };\r\n    return { buildQuerySelector: buildQuerySelector, buildMutationSelector: buildMutationSelector, selectInvalidatedBy: selectInvalidatedBy };\r\n    function withRequestFlags(substate) {\r\n        return __spreadValues(__spreadValues({}, substate), getRequestStatusFlags(substate.status));\r\n    }\r\n    function selectInternalState(rootState) {\r\n        var state = rootState[reducerPath];\r\n        if (process.env.NODE_ENV !== \"production\") {\r\n            if (!state) {\r\n                if (selectInternalState.triggered)\r\n                    return state;\r\n                selectInternalState.triggered = true;\r\n                console.error(\"Error: No data found at `state.\" + reducerPath + \"`. Did you forget to add the reducer to the store?\");\r\n            }\r\n        }\r\n        return state;\r\n    }\r\n    function buildQuerySelector(endpointName, endpointDefinition) {\r\n        return function (queryArgs) {\r\n            var serializedArgs = serializeQueryArgs({\r\n                queryArgs: queryArgs,\r\n                endpointDefinition: endpointDefinition,\r\n                endpointName: endpointName\r\n            });\r\n            var selectQuerySubstate = function (state) {\r\n                var _a, _b, _c;\r\n                return (_c = (_b = (_a = selectInternalState(state)) == null ? void 0 : _a.queries) == null ? void 0 : _b[serializedArgs]) != null ? _c : defaultQuerySubState;\r\n            };\r\n            var finalSelectQuerySubState = queryArgs === skipToken ? selectSkippedQuery : selectQuerySubstate;\r\n            return createSelector(finalSelectQuerySubState, withRequestFlags);\r\n        };\r\n    }\r\n    function buildMutationSelector() {\r\n        return function (id) {\r\n            var _a;\r\n            var mutationId;\r\n            if (typeof id === \"object\") {\r\n                mutationId = (_a = getMutationCacheKey(id)) != null ? _a : skipToken;\r\n            }\r\n            else {\r\n                mutationId = id;\r\n            }\r\n            var selectMutationSubstate = function (state) {\r\n                var _a2, _b, _c;\r\n                return (_c = (_b = (_a2 = selectInternalState(state)) == null ? void 0 : _a2.mutations) == null ? void 0 : _b[mutationId]) != null ? _c : defaultMutationSubState;\r\n            };\r\n            var finalSelectMutationSubstate = mutationId === skipToken ? selectSkippedMutation : selectMutationSubstate;\r\n            return createSelector(finalSelectMutationSubstate, withRequestFlags);\r\n        };\r\n    }\r\n    function selectInvalidatedBy(state, tags) {\r\n        var _a;\r\n        var apiState = state[reducerPath];\r\n        var toInvalidate = new Set();\r\n        for (var _j = 0, _k = tags.map(expandTagDescription); _j < _k.length; _j++) {\r\n            var tag = _k[_j];\r\n            var provided = apiState.provided[tag.type];\r\n            if (!provided) {\r\n                continue;\r\n            }\r\n            var invalidateSubscriptions = (_a = tag.id !== void 0 ? provided[tag.id] : flatten(Object.values(provided))) != null ? _a : [];\r\n            for (var _l = 0, invalidateSubscriptions_1 = invalidateSubscriptions; _l < invalidateSubscriptions_1.length; _l++) {\r\n                var invalidate = invalidateSubscriptions_1[_l];\r\n                toInvalidate.add(invalidate);\r\n            }\r\n        }\r\n        return flatten(Array.from(toInvalidate.values()).map(function (queryCacheKey) {\r\n            var querySubState = apiState.queries[queryCacheKey];\r\n            return querySubState ? [\r\n                {\r\n                    queryCacheKey: queryCacheKey,\r\n                    endpointName: querySubState.endpointName,\r\n                    originalArgs: querySubState.originalArgs\r\n                }\r\n            ] : [];\r\n        }));\r\n    }\r\n}\r\n// src/query/defaultSerializeQueryArgs.ts\r\nimport { isPlainObject as isPlainObject3 } from \"@reduxjs/toolkit\";\r\nvar cache = WeakMap ? new WeakMap() : void 0;\r\nvar defaultSerializeQueryArgs = function (_j) {\r\n    var endpointName = _j.endpointName, queryArgs = _j.queryArgs;\r\n    var serialized = \"\";\r\n    var cached = cache == null ? void 0 : cache.get(queryArgs);\r\n    if (typeof cached === \"string\") {\r\n        serialized = cached;\r\n    }\r\n    else {\r\n        var stringified = JSON.stringify(queryArgs, function (key, value) { return isPlainObject3(value) ? Object.keys(value).sort().reduce(function (acc, key2) {\r\n            acc[key2] = value[key2];\r\n            return acc;\r\n        }, {}) : value; });\r\n        if (isPlainObject3(queryArgs)) {\r\n            cache == null ? void 0 : cache.set(queryArgs, stringified);\r\n        }\r\n        serialized = stringified;\r\n    }\r\n    return endpointName + \"(\" + serialized + \")\";\r\n};\r\n// src/query/createApi.ts\r\nimport { nanoid } from \"@reduxjs/toolkit\";\r\nimport { defaultMemoize } from \"reselect\";\r\nfunction buildCreateApi() {\r\n    var modules = [];\r\n    for (var _j = 0; _j < arguments.length; _j++) {\r\n        modules[_j] = arguments[_j];\r\n    }\r\n    return function baseCreateApi(options) {\r\n        var extractRehydrationInfo = defaultMemoize(function (action) {\r\n            var _a, _b;\r\n            return (_b = options.extractRehydrationInfo) == null ? void 0 : _b.call(options, action, {\r\n                reducerPath: (_a = options.reducerPath) != null ? _a : \"api\"\r\n            });\r\n        });\r\n        var optionsWithDefaults = __spreadProps(__spreadValues({\r\n            reducerPath: \"api\",\r\n            keepUnusedDataFor: 60,\r\n            refetchOnMountOrArgChange: false,\r\n            refetchOnFocus: false,\r\n            refetchOnReconnect: false\r\n        }, options), {\r\n            extractRehydrationInfo: extractRehydrationInfo,\r\n            serializeQueryArgs: function (queryArgsApi) {\r\n                var finalSerializeQueryArgs = defaultSerializeQueryArgs;\r\n                if (\"serializeQueryArgs\" in queryArgsApi.endpointDefinition) {\r\n                    var endpointSQA_1 = queryArgsApi.endpointDefinition.serializeQueryArgs;\r\n                    finalSerializeQueryArgs = function (queryArgsApi2) {\r\n                        var initialResult = endpointSQA_1(queryArgsApi2);\r\n                        if (typeof initialResult === \"string\") {\r\n                            return initialResult;\r\n                        }\r\n                        else {\r\n                            return defaultSerializeQueryArgs(__spreadProps(__spreadValues({}, queryArgsApi2), {\r\n                                queryArgs: initialResult\r\n                            }));\r\n                        }\r\n                    };\r\n                }\r\n                else if (options.serializeQueryArgs) {\r\n                    finalSerializeQueryArgs = options.serializeQueryArgs;\r\n                }\r\n                return finalSerializeQueryArgs(queryArgsApi);\r\n            },\r\n            tagTypes: __spreadArray([], options.tagTypes || [])\r\n        });\r\n        var context = {\r\n            endpointDefinitions: {},\r\n            batch: function (fn) {\r\n                fn();\r\n            },\r\n            apiUid: nanoid(),\r\n            extractRehydrationInfo: extractRehydrationInfo,\r\n            hasRehydrationInfo: defaultMemoize(function (action) { return extractRehydrationInfo(action) != null; })\r\n        };\r\n        var api = {\r\n            injectEndpoints: injectEndpoints,\r\n            enhanceEndpoints: function (_j) {\r\n                var addTagTypes = _j.addTagTypes, endpoints = _j.endpoints;\r\n                if (addTagTypes) {\r\n                    for (var _k = 0, addTagTypes_1 = addTagTypes; _k < addTagTypes_1.length; _k++) {\r\n                        var eT = addTagTypes_1[_k];\r\n                        if (!optionsWithDefaults.tagTypes.includes(eT)) {\r\n                            ;\r\n                            optionsWithDefaults.tagTypes.push(eT);\r\n                        }\r\n                    }\r\n                }\r\n                if (endpoints) {\r\n                    for (var _l = 0, _m = Object.entries(endpoints); _l < _m.length; _l++) {\r\n                        var _o = _m[_l], endpointName = _o[0], partialDefinition = _o[1];\r\n                        if (typeof partialDefinition === \"function\") {\r\n                            partialDefinition(context.endpointDefinitions[endpointName]);\r\n                        }\r\n                        else {\r\n                            Object.assign(context.endpointDefinitions[endpointName] || {}, partialDefinition);\r\n                        }\r\n                    }\r\n                }\r\n                return api;\r\n            }\r\n        };\r\n        var initializedModules = modules.map(function (m) { return m.init(api, optionsWithDefaults, context); });\r\n        function injectEndpoints(inject) {\r\n            var evaluatedEndpoints = inject.endpoints({\r\n                query: function (x) { return __spreadProps(__spreadValues({}, x), { type: DefinitionType.query }); },\r\n                mutation: function (x) { return __spreadProps(__spreadValues({}, x), { type: DefinitionType.mutation }); }\r\n            });\r\n            for (var _j = 0, _k = Object.entries(evaluatedEndpoints); _j < _k.length; _j++) {\r\n                var _l = _k[_j], endpointName = _l[0], definition = _l[1];\r\n                if (!inject.overrideExisting && endpointName in context.endpointDefinitions) {\r\n                    if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\r\n                        console.error(\"called `injectEndpoints` to override already-existing endpointName \" + endpointName + \" without specifying `overrideExisting: true`\");\r\n                    }\r\n                    continue;\r\n                }\r\n                context.endpointDefinitions[endpointName] = definition;\r\n                for (var _m = 0, initializedModules_1 = initializedModules; _m < initializedModules_1.length; _m++) {\r\n                    var m = initializedModules_1[_m];\r\n                    m.injectEndpoint(endpointName, definition);\r\n                }\r\n            }\r\n            return api;\r\n        }\r\n        return api.injectEndpoints({ endpoints: options.endpoints });\r\n    };\r\n}\r\n// src/query/fakeBaseQuery.ts\r\nfunction fakeBaseQuery() {\r\n    return function () {\r\n        throw new Error(\"When using `fakeBaseQuery`, all queries & mutations must use the `queryFn` definition syntax.\");\r\n    };\r\n}\r\n// src/query/core/buildMiddleware/index.ts\r\nimport { createAction as createAction3 } from \"@reduxjs/toolkit\";\r\n// src/query/core/buildMiddleware/cacheCollection.ts\r\nfunction isObjectEmpty(obj) {\r\n    for (var k in obj) {\r\n        return false;\r\n    }\r\n    return true;\r\n}\r\nvar THIRTY_TWO_BIT_MAX_TIMER_SECONDS = 2147483647 / 1e3 - 1;\r\nvar buildCacheCollectionHandler = function (_j) {\r\n    var reducerPath = _j.reducerPath, api = _j.api, context = _j.context, internalState = _j.internalState;\r\n    var _k = api.internalActions, removeQueryResult = _k.removeQueryResult, unsubscribeQueryResult = _k.unsubscribeQueryResult;\r\n    function anySubscriptionsRemainingForKey(queryCacheKey) {\r\n        var subscriptions = internalState.currentSubscriptions[queryCacheKey];\r\n        return !!subscriptions && !isObjectEmpty(subscriptions);\r\n    }\r\n    var currentRemovalTimeouts = {};\r\n    var handler = function (action, mwApi, internalState2) {\r\n        var _a;\r\n        if (unsubscribeQueryResult.match(action)) {\r\n            var state = mwApi.getState()[reducerPath];\r\n            var queryCacheKey = action.payload.queryCacheKey;\r\n            handleUnsubscribe(queryCacheKey, (_a = state.queries[queryCacheKey]) == null ? void 0 : _a.endpointName, mwApi, state.config);\r\n        }\r\n        if (api.util.resetApiState.match(action)) {\r\n            for (var _j = 0, _k = Object.entries(currentRemovalTimeouts); _j < _k.length; _j++) {\r\n                var _l = _k[_j], key = _l[0], timeout = _l[1];\r\n                if (timeout)\r\n                    clearTimeout(timeout);\r\n                delete currentRemovalTimeouts[key];\r\n            }\r\n        }\r\n        if (context.hasRehydrationInfo(action)) {\r\n            var state = mwApi.getState()[reducerPath];\r\n            var queries = context.extractRehydrationInfo(action).queries;\r\n            for (var _m = 0, _o = Object.entries(queries); _m < _o.length; _m++) {\r\n                var _p = _o[_m], queryCacheKey = _p[0], queryState = _p[1];\r\n                handleUnsubscribe(queryCacheKey, queryState == null ? void 0 : queryState.endpointName, mwApi, state.config);\r\n            }\r\n        }\r\n    };\r\n    function handleUnsubscribe(queryCacheKey, endpointName, api2, config) {\r\n        var _a;\r\n        var endpointDefinition = context.endpointDefinitions[endpointName];\r\n        var keepUnusedDataFor = (_a = endpointDefinition == null ? void 0 : endpointDefinition.keepUnusedDataFor) != null ? _a : config.keepUnusedDataFor;\r\n        if (keepUnusedDataFor === Infinity) {\r\n            return;\r\n        }\r\n        var finalKeepUnusedDataFor = Math.max(0, Math.min(keepUnusedDataFor, THIRTY_TWO_BIT_MAX_TIMER_SECONDS));\r\n        if (!anySubscriptionsRemainingForKey(queryCacheKey)) {\r\n            var currentTimeout = currentRemovalTimeouts[queryCacheKey];\r\n            if (currentTimeout) {\r\n                clearTimeout(currentTimeout);\r\n            }\r\n            currentRemovalTimeouts[queryCacheKey] = setTimeout(function () {\r\n                if (!anySubscriptionsRemainingForKey(queryCacheKey)) {\r\n                    api2.dispatch(removeQueryResult({ queryCacheKey: queryCacheKey }));\r\n                }\r\n                delete currentRemovalTimeouts[queryCacheKey];\r\n            }, finalKeepUnusedDataFor * 1e3);\r\n        }\r\n    }\r\n    return handler;\r\n};\r\n// src/query/core/buildMiddleware/invalidationByTags.ts\r\nimport { isAnyOf as isAnyOf2, isFulfilled as isFulfilled3, isRejectedWithValue as isRejectedWithValue3 } from \"@reduxjs/toolkit\";\r\nvar buildInvalidationByTagsHandler = function (_j) {\r\n    var reducerPath = _j.reducerPath, context = _j.context, endpointDefinitions = _j.context.endpointDefinitions, mutationThunk = _j.mutationThunk, api = _j.api, assertTagType = _j.assertTagType, refetchQuery = _j.refetchQuery;\r\n    var removeQueryResult = api.internalActions.removeQueryResult;\r\n    var isThunkActionWithTags = isAnyOf2(isFulfilled3(mutationThunk), isRejectedWithValue3(mutationThunk));\r\n    var handler = function (action, mwApi) {\r\n        if (isThunkActionWithTags(action)) {\r\n            invalidateTags(calculateProvidedByThunk(action, \"invalidatesTags\", endpointDefinitions, assertTagType), mwApi);\r\n        }\r\n        if (api.util.invalidateTags.match(action)) {\r\n            invalidateTags(calculateProvidedBy(action.payload, void 0, void 0, void 0, void 0, assertTagType), mwApi);\r\n        }\r\n    };\r\n    function invalidateTags(tags, mwApi) {\r\n        var rootState = mwApi.getState();\r\n        var state = rootState[reducerPath];\r\n        var toInvalidate = api.util.selectInvalidatedBy(rootState, tags);\r\n        context.batch(function () {\r\n            var _a;\r\n            var valuesArray = Array.from(toInvalidate.values());\r\n            for (var _j = 0, valuesArray_1 = valuesArray; _j < valuesArray_1.length; _j++) {\r\n                var queryCacheKey = valuesArray_1[_j].queryCacheKey;\r\n                var querySubState = state.queries[queryCacheKey];\r\n                var subscriptionSubState = (_a = state.subscriptions[queryCacheKey]) != null ? _a : {};\r\n                if (querySubState) {\r\n                    if (Object.keys(subscriptionSubState).length === 0) {\r\n                        mwApi.dispatch(removeQueryResult({\r\n                            queryCacheKey: queryCacheKey\r\n                        }));\r\n                    }\r\n                    else if (querySubState.status !== QueryStatus.uninitialized) {\r\n                        mwApi.dispatch(refetchQuery(querySubState, queryCacheKey));\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n    return handler;\r\n};\r\n// src/query/core/buildMiddleware/polling.ts\r\nvar buildPollingHandler = function (_j) {\r\n    var reducerPath = _j.reducerPath, queryThunk = _j.queryThunk, api = _j.api, refetchQuery = _j.refetchQuery, internalState = _j.internalState;\r\n    var currentPolls = {};\r\n    var handler = function (action, mwApi) {\r\n        if (api.internalActions.updateSubscriptionOptions.match(action) || api.internalActions.unsubscribeQueryResult.match(action)) {\r\n            updatePollingInterval(action.payload, mwApi);\r\n        }\r\n        if (queryThunk.pending.match(action) || queryThunk.rejected.match(action) && action.meta.condition) {\r\n            updatePollingInterval(action.meta.arg, mwApi);\r\n        }\r\n        if (queryThunk.fulfilled.match(action) || queryThunk.rejected.match(action) && !action.meta.condition) {\r\n            startNextPoll(action.meta.arg, mwApi);\r\n        }\r\n        if (api.util.resetApiState.match(action)) {\r\n            clearPolls();\r\n        }\r\n    };\r\n    function startNextPoll(_j, api2) {\r\n        var queryCacheKey = _j.queryCacheKey;\r\n        var state = api2.getState()[reducerPath];\r\n        var querySubState = state.queries[queryCacheKey];\r\n        var subscriptions = internalState.currentSubscriptions[queryCacheKey];\r\n        if (!querySubState || querySubState.status === QueryStatus.uninitialized)\r\n            return;\r\n        var lowestPollingInterval = findLowestPollingInterval(subscriptions);\r\n        if (!Number.isFinite(lowestPollingInterval))\r\n            return;\r\n        var currentPoll = currentPolls[queryCacheKey];\r\n        if (currentPoll == null ? void 0 : currentPoll.timeout) {\r\n            clearTimeout(currentPoll.timeout);\r\n            currentPoll.timeout = void 0;\r\n        }\r\n        var nextPollTimestamp = Date.now() + lowestPollingInterval;\r\n        var currentInterval = currentPolls[queryCacheKey] = {\r\n            nextPollTimestamp: nextPollTimestamp,\r\n            pollingInterval: lowestPollingInterval,\r\n            timeout: setTimeout(function () {\r\n                currentInterval.timeout = void 0;\r\n                api2.dispatch(refetchQuery(querySubState, queryCacheKey));\r\n            }, lowestPollingInterval)\r\n        };\r\n    }\r\n    function updatePollingInterval(_j, api2) {\r\n        var queryCacheKey = _j.queryCacheKey;\r\n        var state = api2.getState()[reducerPath];\r\n        var querySubState = state.queries[queryCacheKey];\r\n        var subscriptions = internalState.currentSubscriptions[queryCacheKey];\r\n        if (!querySubState || querySubState.status === QueryStatus.uninitialized) {\r\n            return;\r\n        }\r\n        var lowestPollingInterval = findLowestPollingInterval(subscriptions);\r\n        if (!Number.isFinite(lowestPollingInterval)) {\r\n            cleanupPollForKey(queryCacheKey);\r\n            return;\r\n        }\r\n        var currentPoll = currentPolls[queryCacheKey];\r\n        var nextPollTimestamp = Date.now() + lowestPollingInterval;\r\n        if (!currentPoll || nextPollTimestamp < currentPoll.nextPollTimestamp) {\r\n            startNextPoll({ queryCacheKey: queryCacheKey }, api2);\r\n        }\r\n    }\r\n    function cleanupPollForKey(key) {\r\n        var existingPoll = currentPolls[key];\r\n        if (existingPoll == null ? void 0 : existingPoll.timeout) {\r\n            clearTimeout(existingPoll.timeout);\r\n        }\r\n        delete currentPolls[key];\r\n    }\r\n    function clearPolls() {\r\n        for (var _j = 0, _k = Object.keys(currentPolls); _j < _k.length; _j++) {\r\n            var key = _k[_j];\r\n            cleanupPollForKey(key);\r\n        }\r\n    }\r\n    function findLowestPollingInterval(subscribers) {\r\n        if (subscribers === void 0) { subscribers = {}; }\r\n        var lowestPollingInterval = Number.POSITIVE_INFINITY;\r\n        for (var key in subscribers) {\r\n            if (!!subscribers[key].pollingInterval) {\r\n                lowestPollingInterval = Math.min(subscribers[key].pollingInterval, lowestPollingInterval);\r\n            }\r\n        }\r\n        return lowestPollingInterval;\r\n    }\r\n    return handler;\r\n};\r\n// src/query/core/buildMiddleware/windowEventHandling.ts\r\nvar buildWindowEventHandler = function (_j) {\r\n    var reducerPath = _j.reducerPath, context = _j.context, api = _j.api, refetchQuery = _j.refetchQuery, internalState = _j.internalState;\r\n    var removeQueryResult = api.internalActions.removeQueryResult;\r\n    var handler = function (action, mwApi) {\r\n        if (onFocus.match(action)) {\r\n            refetchValidQueries(mwApi, \"refetchOnFocus\");\r\n        }\r\n        if (onOnline.match(action)) {\r\n            refetchValidQueries(mwApi, \"refetchOnReconnect\");\r\n        }\r\n    };\r\n    function refetchValidQueries(api2, type) {\r\n        var state = api2.getState()[reducerPath];\r\n        var queries = state.queries;\r\n        var subscriptions = internalState.currentSubscriptions;\r\n        context.batch(function () {\r\n            for (var _j = 0, _k = Object.keys(subscriptions); _j < _k.length; _j++) {\r\n                var queryCacheKey = _k[_j];\r\n                var querySubState = queries[queryCacheKey];\r\n                var subscriptionSubState = subscriptions[queryCacheKey];\r\n                if (!subscriptionSubState || !querySubState)\r\n                    continue;\r\n                var shouldRefetch = Object.values(subscriptionSubState).some(function (sub) { return sub[type] === true; }) || Object.values(subscriptionSubState).every(function (sub) { return sub[type] === void 0; }) && state.config[type];\r\n                if (shouldRefetch) {\r\n                    if (Object.keys(subscriptionSubState).length === 0) {\r\n                        api2.dispatch(removeQueryResult({\r\n                            queryCacheKey: queryCacheKey\r\n                        }));\r\n                    }\r\n                    else if (querySubState.status !== QueryStatus.uninitialized) {\r\n                        api2.dispatch(refetchQuery(querySubState, queryCacheKey));\r\n                    }\r\n                }\r\n            }\r\n        });\r\n    }\r\n    return handler;\r\n};\r\n// src/query/core/buildMiddleware/cacheLifecycle.ts\r\nimport { isAsyncThunkAction, isFulfilled as isFulfilled4 } from \"@reduxjs/toolkit\";\r\nvar neverResolvedError = new Error(\"Promise never resolved before cacheEntryRemoved.\");\r\nvar buildCacheLifecycleHandler = function (_j) {\r\n    var api = _j.api, reducerPath = _j.reducerPath, context = _j.context, queryThunk = _j.queryThunk, mutationThunk = _j.mutationThunk, internalState = _j.internalState;\r\n    var isQueryThunk = isAsyncThunkAction(queryThunk);\r\n    var isMutationThunk = isAsyncThunkAction(mutationThunk);\r\n    var isFulfilledThunk = isFulfilled4(queryThunk, mutationThunk);\r\n    var lifecycleMap = {};\r\n    var handler = function (action, mwApi, stateBefore) {\r\n        var cacheKey = getCacheKey(action);\r\n        if (queryThunk.pending.match(action)) {\r\n            var oldState = stateBefore[reducerPath].queries[cacheKey];\r\n            var state = mwApi.getState()[reducerPath].queries[cacheKey];\r\n            if (!oldState && state) {\r\n                handleNewKey(action.meta.arg.endpointName, action.meta.arg.originalArgs, cacheKey, mwApi, action.meta.requestId);\r\n            }\r\n        }\r\n        else if (mutationThunk.pending.match(action)) {\r\n            var state = mwApi.getState()[reducerPath].mutations[cacheKey];\r\n            if (state) {\r\n                handleNewKey(action.meta.arg.endpointName, action.meta.arg.originalArgs, cacheKey, mwApi, action.meta.requestId);\r\n            }\r\n        }\r\n        else if (isFulfilledThunk(action)) {\r\n            var lifecycle = lifecycleMap[cacheKey];\r\n            if (lifecycle == null ? void 0 : lifecycle.valueResolved) {\r\n                lifecycle.valueResolved({\r\n                    data: action.payload,\r\n                    meta: action.meta.baseQueryMeta\r\n                });\r\n                delete lifecycle.valueResolved;\r\n            }\r\n        }\r\n        else if (api.internalActions.removeQueryResult.match(action) || api.internalActions.removeMutationResult.match(action)) {\r\n            var lifecycle = lifecycleMap[cacheKey];\r\n            if (lifecycle) {\r\n                delete lifecycleMap[cacheKey];\r\n                lifecycle.cacheEntryRemoved();\r\n            }\r\n        }\r\n        else if (api.util.resetApiState.match(action)) {\r\n            for (var _j = 0, _k = Object.entries(lifecycleMap); _j < _k.length; _j++) {\r\n                var _l = _k[_j], cacheKey2 = _l[0], lifecycle = _l[1];\r\n                delete lifecycleMap[cacheKey2];\r\n                lifecycle.cacheEntryRemoved();\r\n            }\r\n        }\r\n    };\r\n    function getCacheKey(action) {\r\n        if (isQueryThunk(action))\r\n            return action.meta.arg.queryCacheKey;\r\n        if (isMutationThunk(action))\r\n            return action.meta.requestId;\r\n        if (api.internalActions.removeQueryResult.match(action))\r\n            return action.payload.queryCacheKey;\r\n        if (api.internalActions.removeMutationResult.match(action))\r\n            return getMutationCacheKey(action.payload);\r\n        return \"\";\r\n    }\r\n    function handleNewKey(endpointName, originalArgs, queryCacheKey, mwApi, requestId) {\r\n        var endpointDefinition = context.endpointDefinitions[endpointName];\r\n        var onCacheEntryAdded = endpointDefinition == null ? void 0 : endpointDefinition.onCacheEntryAdded;\r\n        if (!onCacheEntryAdded)\r\n            return;\r\n        var lifecycle = {};\r\n        var cacheEntryRemoved = new Promise(function (resolve) {\r\n            lifecycle.cacheEntryRemoved = resolve;\r\n        });\r\n        var cacheDataLoaded = Promise.race([\r\n            new Promise(function (resolve) {\r\n                lifecycle.valueResolved = resolve;\r\n            }),\r\n            cacheEntryRemoved.then(function () {\r\n                throw neverResolvedError;\r\n            })\r\n        ]);\r\n        cacheDataLoaded.catch(function () {\r\n        });\r\n        lifecycleMap[queryCacheKey] = lifecycle;\r\n        var selector = api.endpoints[endpointName].select(endpointDefinition.type === DefinitionType.query ? originalArgs : queryCacheKey);\r\n        var extra = mwApi.dispatch(function (_, __, extra2) { return extra2; });\r\n        var lifecycleApi = __spreadProps(__spreadValues({}, mwApi), {\r\n            getCacheEntry: function () { return selector(mwApi.getState()); },\r\n            requestId: requestId,\r\n            extra: extra,\r\n            updateCachedData: endpointDefinition.type === DefinitionType.query ? function (updateRecipe) { return mwApi.dispatch(api.util.updateQueryData(endpointName, originalArgs, updateRecipe)); } : void 0,\r\n            cacheDataLoaded: cacheDataLoaded,\r\n            cacheEntryRemoved: cacheEntryRemoved\r\n        });\r\n        var runningHandler = onCacheEntryAdded(originalArgs, lifecycleApi);\r\n        Promise.resolve(runningHandler).catch(function (e) {\r\n            if (e === neverResolvedError)\r\n                return;\r\n            throw e;\r\n        });\r\n    }\r\n    return handler;\r\n};\r\n// src/query/core/buildMiddleware/queryLifecycle.ts\r\nimport { isPending as isPending2, isRejected as isRejected2, isFulfilled as isFulfilled5 } from \"@reduxjs/toolkit\";\r\nvar buildQueryLifecycleHandler = function (_j) {\r\n    var api = _j.api, context = _j.context, queryThunk = _j.queryThunk, mutationThunk = _j.mutationThunk;\r\n    var isPendingThunk = isPending2(queryThunk, mutationThunk);\r\n    var isRejectedThunk = isRejected2(queryThunk, mutationThunk);\r\n    var isFullfilledThunk = isFulfilled5(queryThunk, mutationThunk);\r\n    var lifecycleMap = {};\r\n    var handler = function (action, mwApi) {\r\n        var _a, _b, _c;\r\n        if (isPendingThunk(action)) {\r\n            var _j = action.meta, requestId = _j.requestId, _k = _j.arg, endpointName_1 = _k.endpointName, originalArgs_1 = _k.originalArgs;\r\n            var endpointDefinition = context.endpointDefinitions[endpointName_1];\r\n            var onQueryStarted = endpointDefinition == null ? void 0 : endpointDefinition.onQueryStarted;\r\n            if (onQueryStarted) {\r\n                var lifecycle_1 = {};\r\n                var queryFulfilled = new Promise(function (resolve, reject) {\r\n                    lifecycle_1.resolve = resolve;\r\n                    lifecycle_1.reject = reject;\r\n                });\r\n                queryFulfilled.catch(function () {\r\n                });\r\n                lifecycleMap[requestId] = lifecycle_1;\r\n                var selector_1 = api.endpoints[endpointName_1].select(endpointDefinition.type === DefinitionType.query ? originalArgs_1 : requestId);\r\n                var extra = mwApi.dispatch(function (_, __, extra2) { return extra2; });\r\n                var lifecycleApi = __spreadProps(__spreadValues({}, mwApi), {\r\n                    getCacheEntry: function () { return selector_1(mwApi.getState()); },\r\n                    requestId: requestId,\r\n                    extra: extra,\r\n                    updateCachedData: endpointDefinition.type === DefinitionType.query ? function (updateRecipe) { return mwApi.dispatch(api.util.updateQueryData(endpointName_1, originalArgs_1, updateRecipe)); } : void 0,\r\n                    queryFulfilled: queryFulfilled\r\n                });\r\n                onQueryStarted(originalArgs_1, lifecycleApi);\r\n            }\r\n        }\r\n        else if (isFullfilledThunk(action)) {\r\n            var _l = action.meta, requestId = _l.requestId, baseQueryMeta = _l.baseQueryMeta;\r\n            (_a = lifecycleMap[requestId]) == null ? void 0 : _a.resolve({\r\n                data: action.payload,\r\n                meta: baseQueryMeta\r\n            });\r\n            delete lifecycleMap[requestId];\r\n        }\r\n        else if (isRejectedThunk(action)) {\r\n            var _m = action.meta, requestId = _m.requestId, rejectedWithValue = _m.rejectedWithValue, baseQueryMeta = _m.baseQueryMeta;\r\n            (_c = lifecycleMap[requestId]) == null ? void 0 : _c.reject({\r\n                error: (_b = action.payload) != null ? _b : action.error,\r\n                isUnhandledError: !rejectedWithValue,\r\n                meta: baseQueryMeta\r\n            });\r\n            delete lifecycleMap[requestId];\r\n        }\r\n    };\r\n    return handler;\r\n};\r\n// src/query/core/buildMiddleware/devMiddleware.ts\r\nvar buildDevCheckHandler = function (_j) {\r\n    var api = _j.api, apiUid = _j.context.apiUid, reducerPath = _j.reducerPath;\r\n    return function (action, mwApi) {\r\n        var _a, _b;\r\n        if (api.util.resetApiState.match(action)) {\r\n            mwApi.dispatch(api.internalActions.middlewareRegistered(apiUid));\r\n        }\r\n        if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\r\n            if (api.internalActions.middlewareRegistered.match(action) && action.payload === apiUid && ((_b = (_a = mwApi.getState()[reducerPath]) == null ? void 0 : _a.config) == null ? void 0 : _b.middlewareRegistered) === \"conflict\") {\r\n                console.warn(\"There is a mismatch between slice and middleware for the reducerPath \\\"\" + reducerPath + \"\\\".\\nYou can only have one api per reducer path, this will lead to crashes in various situations!\" + (reducerPath === \"api\" ? \"\\nIf you have multiple apis, you *have* to specify the reducerPath option when using createApi!\" : \"\"));\r\n            }\r\n        }\r\n    };\r\n};\r\n// src/query/core/buildMiddleware/batchActions.ts\r\nimport { produceWithPatches as produceWithPatches2 } from \"immer\";\r\nvar promise;\r\nvar queueMicrotaskShim = typeof queueMicrotask === \"function\" ? queueMicrotask.bind(typeof window !== \"undefined\" ? window : typeof global !== \"undefined\" ? global : globalThis) : function (cb) { return (promise || (promise = Promise.resolve())).then(cb).catch(function (err) { return setTimeout(function () {\r\n    throw err;\r\n}, 0); }); };\r\nvar buildBatchedActionsHandler = function (_j) {\r\n    var api = _j.api, queryThunk = _j.queryThunk, internalState = _j.internalState;\r\n    var subscriptionsPrefix = api.reducerPath + \"/subscriptions\";\r\n    var previousSubscriptions = null;\r\n    var dispatchQueued = false;\r\n    var _k = api.internalActions, updateSubscriptionOptions = _k.updateSubscriptionOptions, unsubscribeQueryResult = _k.unsubscribeQueryResult;\r\n    var actuallyMutateSubscriptions = function (mutableState, action) {\r\n        var _a, _b, _c, _d, _e, _f, _g, _h, _i;\r\n        if (updateSubscriptionOptions.match(action)) {\r\n            var _j = action.payload, queryCacheKey = _j.queryCacheKey, requestId = _j.requestId, options = _j.options;\r\n            if ((_a = mutableState == null ? void 0 : mutableState[queryCacheKey]) == null ? void 0 : _a[requestId]) {\r\n                mutableState[queryCacheKey][requestId] = options;\r\n            }\r\n            return true;\r\n        }\r\n        if (unsubscribeQueryResult.match(action)) {\r\n            var _k = action.payload, queryCacheKey = _k.queryCacheKey, requestId = _k.requestId;\r\n            if (mutableState[queryCacheKey]) {\r\n                delete mutableState[queryCacheKey][requestId];\r\n            }\r\n            return true;\r\n        }\r\n        if (api.internalActions.removeQueryResult.match(action)) {\r\n            delete mutableState[action.payload.queryCacheKey];\r\n            return true;\r\n        }\r\n        if (queryThunk.pending.match(action)) {\r\n            var _l = action.meta, arg = _l.arg, requestId = _l.requestId;\r\n            if (arg.subscribe) {\r\n                var substate = (_c = mutableState[_b = arg.queryCacheKey]) != null ? _c : mutableState[_b] = {};\r\n                substate[requestId] = (_e = (_d = arg.subscriptionOptions) != null ? _d : substate[requestId]) != null ? _e : {};\r\n                return true;\r\n            }\r\n        }\r\n        if (queryThunk.rejected.match(action)) {\r\n            var _m = action.meta, condition = _m.condition, arg = _m.arg, requestId = _m.requestId;\r\n            if (condition && arg.subscribe) {\r\n                var substate = (_g = mutableState[_f = arg.queryCacheKey]) != null ? _g : mutableState[_f] = {};\r\n                substate[requestId] = (_i = (_h = arg.subscriptionOptions) != null ? _h : substate[requestId]) != null ? _i : {};\r\n                return true;\r\n            }\r\n        }\r\n        return false;\r\n    };\r\n    return function (action, mwApi) {\r\n        var _a, _b;\r\n        if (!previousSubscriptions) {\r\n            previousSubscriptions = JSON.parse(JSON.stringify(internalState.currentSubscriptions));\r\n        }\r\n        if (api.util.resetApiState.match(action)) {\r\n            previousSubscriptions = internalState.currentSubscriptions = {};\r\n            return [true, false];\r\n        }\r\n        if (api.internalActions.internal_probeSubscription.match(action)) {\r\n            var _j = action.payload, queryCacheKey = _j.queryCacheKey, requestId = _j.requestId;\r\n            var hasSubscription = !!((_a = internalState.currentSubscriptions[queryCacheKey]) == null ? void 0 : _a[requestId]);\r\n            return [false, hasSubscription];\r\n        }\r\n        var didMutate = actuallyMutateSubscriptions(internalState.currentSubscriptions, action);\r\n        if (didMutate) {\r\n            if (!dispatchQueued) {\r\n                queueMicrotaskShim(function () {\r\n                    var newSubscriptions = JSON.parse(JSON.stringify(internalState.currentSubscriptions));\r\n                    var _j = produceWithPatches2(previousSubscriptions, function () { return newSubscriptions; }), patches = _j[1];\r\n                    mwApi.next(api.internalActions.subscriptionsUpdated(patches));\r\n                    previousSubscriptions = newSubscriptions;\r\n                    dispatchQueued = false;\r\n                });\r\n                dispatchQueued = true;\r\n            }\r\n            var isSubscriptionSliceAction = !!((_b = action.type) == null ? void 0 : _b.startsWith(subscriptionsPrefix));\r\n            var isAdditionalSubscriptionAction = queryThunk.rejected.match(action) && action.meta.condition && !!action.meta.arg.subscribe;\r\n            var actionShouldContinue = !isSubscriptionSliceAction && !isAdditionalSubscriptionAction;\r\n            return [actionShouldContinue, false];\r\n        }\r\n        return [true, false];\r\n    };\r\n};\r\n// src/query/core/buildMiddleware/index.ts\r\nfunction buildMiddleware(input) {\r\n    var reducerPath = input.reducerPath, queryThunk = input.queryThunk, api = input.api, context = input.context;\r\n    var apiUid = context.apiUid;\r\n    var actions = {\r\n        invalidateTags: createAction3(reducerPath + \"/invalidateTags\")\r\n    };\r\n    var isThisApiSliceAction = function (action) {\r\n        return !!action && typeof action.type === \"string\" && action.type.startsWith(reducerPath + \"/\");\r\n    };\r\n    var handlerBuilders = [\r\n        buildDevCheckHandler,\r\n        buildCacheCollectionHandler,\r\n        buildInvalidationByTagsHandler,\r\n        buildPollingHandler,\r\n        buildCacheLifecycleHandler,\r\n        buildQueryLifecycleHandler\r\n    ];\r\n    var middleware = function (mwApi) {\r\n        var initialized2 = false;\r\n        var internalState = {\r\n            currentSubscriptions: {}\r\n        };\r\n        var builderArgs = __spreadProps(__spreadValues({}, input), {\r\n            internalState: internalState,\r\n            refetchQuery: refetchQuery\r\n        });\r\n        var handlers = handlerBuilders.map(function (build) { return build(builderArgs); });\r\n        var batchedActionsHandler = buildBatchedActionsHandler(builderArgs);\r\n        var windowEventsHandler = buildWindowEventHandler(builderArgs);\r\n        return function (next) {\r\n            return function (action) {\r\n                if (!initialized2) {\r\n                    initialized2 = true;\r\n                    mwApi.dispatch(api.internalActions.middlewareRegistered(apiUid));\r\n                }\r\n                var mwApiWithNext = __spreadProps(__spreadValues({}, mwApi), { next: next });\r\n                var stateBefore = mwApi.getState();\r\n                var _j = batchedActionsHandler(action, mwApiWithNext, stateBefore), actionShouldContinue = _j[0], hasSubscription = _j[1];\r\n                var res;\r\n                if (actionShouldContinue) {\r\n                    res = next(action);\r\n                }\r\n                else {\r\n                    res = hasSubscription;\r\n                }\r\n                if (!!mwApi.getState()[reducerPath]) {\r\n                    windowEventsHandler(action, mwApiWithNext, stateBefore);\r\n                    if (isThisApiSliceAction(action) || context.hasRehydrationInfo(action)) {\r\n                        for (var _k = 0, handlers_1 = handlers; _k < handlers_1.length; _k++) {\r\n                            var handler = handlers_1[_k];\r\n                            handler(action, mwApiWithNext, stateBefore);\r\n                        }\r\n                    }\r\n                }\r\n                return res;\r\n            };\r\n        };\r\n    };\r\n    return { middleware: middleware, actions: actions };\r\n    function refetchQuery(querySubState, queryCacheKey, override) {\r\n        if (override === void 0) { override = {}; }\r\n        return queryThunk(__spreadValues({\r\n            type: \"query\",\r\n            endpointName: querySubState.endpointName,\r\n            originalArgs: querySubState.originalArgs,\r\n            subscribe: false,\r\n            forceRefetch: true,\r\n            queryCacheKey: queryCacheKey\r\n        }, override));\r\n    }\r\n}\r\n// src/query/tsHelpers.ts\r\nfunction assertCast(v) {\r\n}\r\nfunction safeAssign(target) {\r\n    var args = [];\r\n    for (var _j = 1; _j < arguments.length; _j++) {\r\n        args[_j - 1] = arguments[_j];\r\n    }\r\n    Object.assign.apply(Object, __spreadArray([target], args));\r\n}\r\n// src/query/core/module.ts\r\nimport { enablePatches } from \"immer\";\r\nvar coreModuleName = /* @__PURE__ */ Symbol();\r\nvar coreModule = function () { return ({\r\n    name: coreModuleName,\r\n    init: function (api, _j, context) {\r\n        var baseQuery = _j.baseQuery, tagTypes = _j.tagTypes, reducerPath = _j.reducerPath, serializeQueryArgs = _j.serializeQueryArgs, keepUnusedDataFor = _j.keepUnusedDataFor, refetchOnMountOrArgChange = _j.refetchOnMountOrArgChange, refetchOnFocus = _j.refetchOnFocus, refetchOnReconnect = _j.refetchOnReconnect;\r\n        enablePatches();\r\n        assertCast(serializeQueryArgs);\r\n        var assertTagType = function (tag) {\r\n            if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\r\n                if (!tagTypes.includes(tag.type)) {\r\n                    console.error(\"Tag type '\" + tag.type + \"' was used, but not specified in `tagTypes`!\");\r\n                }\r\n            }\r\n            return tag;\r\n        };\r\n        Object.assign(api, {\r\n            reducerPath: reducerPath,\r\n            endpoints: {},\r\n            internalActions: {\r\n                onOnline: onOnline,\r\n                onOffline: onOffline,\r\n                onFocus: onFocus,\r\n                onFocusLost: onFocusLost\r\n            },\r\n            util: {}\r\n        });\r\n        var _k = buildThunks({\r\n            baseQuery: baseQuery,\r\n            reducerPath: reducerPath,\r\n            context: context,\r\n            api: api,\r\n            serializeQueryArgs: serializeQueryArgs,\r\n            assertTagType: assertTagType\r\n        }), queryThunk = _k.queryThunk, mutationThunk = _k.mutationThunk, patchQueryData = _k.patchQueryData, updateQueryData = _k.updateQueryData, upsertQueryData = _k.upsertQueryData, prefetch = _k.prefetch, buildMatchThunkActions = _k.buildMatchThunkActions;\r\n        var _l = buildSlice({\r\n            context: context,\r\n            queryThunk: queryThunk,\r\n            mutationThunk: mutationThunk,\r\n            reducerPath: reducerPath,\r\n            assertTagType: assertTagType,\r\n            config: {\r\n                refetchOnFocus: refetchOnFocus,\r\n                refetchOnReconnect: refetchOnReconnect,\r\n                refetchOnMountOrArgChange: refetchOnMountOrArgChange,\r\n                keepUnusedDataFor: keepUnusedDataFor,\r\n                reducerPath: reducerPath\r\n            }\r\n        }), reducer = _l.reducer, sliceActions = _l.actions;\r\n        safeAssign(api.util, {\r\n            patchQueryData: patchQueryData,\r\n            updateQueryData: updateQueryData,\r\n            upsertQueryData: upsertQueryData,\r\n            prefetch: prefetch,\r\n            resetApiState: sliceActions.resetApiState\r\n        });\r\n        safeAssign(api.internalActions, sliceActions);\r\n        var _m = buildMiddleware({\r\n            reducerPath: reducerPath,\r\n            context: context,\r\n            queryThunk: queryThunk,\r\n            mutationThunk: mutationThunk,\r\n            api: api,\r\n            assertTagType: assertTagType\r\n        }), middleware = _m.middleware, middlewareActions = _m.actions;\r\n        safeAssign(api.util, middlewareActions);\r\n        safeAssign(api, { reducer: reducer, middleware: middleware });\r\n        var _o = buildSelectors({\r\n            serializeQueryArgs: serializeQueryArgs,\r\n            reducerPath: reducerPath\r\n        }), buildQuerySelector = _o.buildQuerySelector, buildMutationSelector = _o.buildMutationSelector, selectInvalidatedBy = _o.selectInvalidatedBy;\r\n        safeAssign(api.util, { selectInvalidatedBy: selectInvalidatedBy });\r\n        var _p = buildInitiate({\r\n            queryThunk: queryThunk,\r\n            mutationThunk: mutationThunk,\r\n            api: api,\r\n            serializeQueryArgs: serializeQueryArgs,\r\n            context: context\r\n        }), buildInitiateQuery = _p.buildInitiateQuery, buildInitiateMutation = _p.buildInitiateMutation, getRunningMutationThunk = _p.getRunningMutationThunk, getRunningMutationsThunk = _p.getRunningMutationsThunk, getRunningQueriesThunk = _p.getRunningQueriesThunk, getRunningQueryThunk = _p.getRunningQueryThunk, getRunningOperationPromises = _p.getRunningOperationPromises, removalWarning = _p.removalWarning;\r\n        safeAssign(api.util, {\r\n            getRunningOperationPromises: getRunningOperationPromises,\r\n            getRunningOperationPromise: removalWarning,\r\n            getRunningMutationThunk: getRunningMutationThunk,\r\n            getRunningMutationsThunk: getRunningMutationsThunk,\r\n            getRunningQueryThunk: getRunningQueryThunk,\r\n            getRunningQueriesThunk: getRunningQueriesThunk\r\n        });\r\n        return {\r\n            name: coreModuleName,\r\n            injectEndpoint: function (endpointName, definition) {\r\n                var _a, _b;\r\n                var anyApi = api;\r\n                (_b = (_a = anyApi.endpoints)[endpointName]) != null ? _b : _a[endpointName] = {};\r\n                if (isQueryDefinition(definition)) {\r\n                    safeAssign(anyApi.endpoints[endpointName], {\r\n                        name: endpointName,\r\n                        select: buildQuerySelector(endpointName, definition),\r\n                        initiate: buildInitiateQuery(endpointName, definition)\r\n                    }, buildMatchThunkActions(queryThunk, endpointName));\r\n                }\r\n                else if (isMutationDefinition(definition)) {\r\n                    safeAssign(anyApi.endpoints[endpointName], {\r\n                        name: endpointName,\r\n                        select: buildMutationSelector(),\r\n                        initiate: buildInitiateMutation(endpointName)\r\n                    }, buildMatchThunkActions(mutationThunk, endpointName));\r\n                }\r\n            }\r\n        };\r\n    }\r\n}); };\r\n// src/query/core/index.ts\r\nvar createApi = /* @__PURE__ */ buildCreateApi(coreModule());\r\nexport { QueryStatus, buildCreateApi, copyWithStructuralSharing, coreModule, coreModuleName, createApi, defaultSerializeQueryArgs, fakeBaseQuery, fetchBaseQuery, retry, setupListeners, skipSelector, skipToken };\r\n//# sourceMappingURL=rtk-query.esm.js.map"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCO,IAAK;CAAL,SAAK,cAAA;AACV,eAAA,eAAA,IAAgB;AAChB,eAAA,SAAA,IAAU;AACV,eAAA,WAAA,IAAY;AACZ,eAAA,UAAA,IAAW;AAAA,GAJD,gBAAA,cAAA,CAAA,EAAA;AAqCL,SAAA,sBAA+B,QAAA;AACpC,SAAO;IACL;IACA,iBAAiB,WAAW,YAAY;IACxC,WAAW,WAAW,YAAY;IAClC,WAAW,WAAW,YAAY;IAClC,SAAS,WAAW,YAAY;;AAAA;ACtE7B,SAAA,cAAuB,KAAA;AAC5B,SAAO,IAAI,OAAO,SAAA,EAAW,KAAK,GAAA;AAAA;ACLpC,IAAM,uBAAuB,SAAC,KAAA;AAAgB,SAAA,IAAI,QAAQ,OAAO,EAAA;AAAnB;AAC9C,IAAM,sBAAsB,SAAC,KAAA;AAAgB,SAAA,IAAI,QAAQ,OAAO,EAAA;AAAnB;AAEtC,SAAA,SACL,MACA,KAAA;AAEA,MAAI,CAAC,MAAM;AACT,WAAO;;AAET,MAAI,CAAC,KAAK;AACR,WAAO;;AAGT,MAAI,cAAc,GAAA,GAAM;AACtB,WAAO;;AAGT,MAAM,YAAY,KAAK,SAAS,GAAA,KAAQ,CAAC,IAAI,WAAW,GAAA,IAAO,MAAM;AACrE,SAAO,qBAAqB,IAAA;AAC5B,QAAM,oBAAoB,GAAA;AAE1B,SAAO,KAAG,OAAO,YAAY;AAAA;ACnBxB,IAAM,UAAU,SAAC,KAAA;AAAwB,SAAA,CAAA,EAAG,OAAA,MAAH,CAAA,GAAa,GAAA;AAAb;ACDzC,SAAA,WAAA;AAEL,SAAO,OAAO,cAAc,cACxB,OACA,UAAU,WAAW,SACrB,OACA,UAAU;AAAA;ACNT,SAAA,oBAAA;AAEL,MAAI,OAAO,aAAa,aAAa;AACnC,WAAO;;AAGT,SAAO,SAAS,oBAAoB;AAAA;ACPtC,IAAMA,iBAAqC;AAGpC,SAAA,0BAAmC,QAAa,QAAA;AACrD,MACE,WAAW,UACX,EACGA,eAAc,MAAA,KAAWA,eAAc,MAAA,KACvC,MAAM,QAAQ,MAAA,KAAW,MAAM,QAAQ,MAAA,IAE1C;AACA,WAAO;;AAET,MAAM,UAAU,OAAO,KAAK,MAAA;AAC5B,MAAM,UAAU,OAAO,KAAK,MAAA;AAE5B,MAAI,eAAe,QAAQ,WAAW,QAAQ;AAC9C,MAAM,WAAgB,MAAM,QAAQ,MAAA,IAAU,CAAA,IAAK,CAAA;AACnD,WAAkB,KAAA,GAAA,YAAA,SAAA,KAAA,UAAA,QAAA,MAAS;AAA3B,QAAW,MAAA,UAAA,EAAA;AACT,aAAS,GAAA,IAAO,0BAA0B,OAAO,GAAA,GAAM,OAAO,GAAA,CAAA;AAC9D,QAAI;AAAc,qBAAe,OAAO,GAAA,MAAS,SAAS,GAAA;;AAE5D,SAAO,eAAe,SAAS;AAAA;ACcjC,IAAM,iBAA+B,WAAA;AAAA,MAAA,OAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAI;AAAJ,SAAA,EAAA,IAAA,UAAA,EAAA;;AAAa,SAAA,MAAA,MAAA,QAAS,IAAA;AAAT;AAElD,IAAM,wBAAwB,SAAC,UAAA;AAC7B,SAAA,SAAS,UAAU,OAAO,SAAS,UAAU;AAA7C;AAEF,IAAM,2BAA2B,SAAC,SAAA;AACnB,SAAA,yBAAyB,KAAK,QAAQ,IAAI,cAAA,KAAmB,EAAA;AAA7D;AAmDf,SAAA,eAAwB,KAAA;AACtB,MAAI,CAAC,cAAc,GAAA,GAAM;AACvB,WAAO;;AAET,MAAM,OAA4B,eAAA,CAAA,GAAK,GAAA;AACvC,WAAqB,KAAA,GAAA,KAAA,OAAO,QAAQ,IAAA,GAAf,KAAA,GAAA,QAAA,MAAsB;AAAhC,QAAA,KAAA,GAAA,EAAA,GAAC,IAAA,GAAA,CAAA,GAAG,IAAA,GAAA,CAAA;AACb,QAAI,MAAM;AAAW,aAAO,KAAK,CAAA;;AAEnC,SAAO;AAAA;AAsFF,SAAA,eAAwB,IAYP;AAZjB,MAAA,QAAA;AAAwB,MAAA,OAAA,QAAA;AAAA,SAAA,CAAA;EAYP;AAZO,MAAA,KAAA,IAC7B,UAD6B,GAAA,SAE7B,KAF6B,GAAA,gBAE7B,iBAAA,OAAA,SAAiB,SAAC,GAAA;AAAM,WAAA;EAAA,IAAA,IACxB,KAH6B,GAAA,SAG7B,UAAA,OAAA,SAAU,iBAAA,IACV,mBAJ6B,GAAA,kBAK7B,KAL6B,GAAA,mBAK7B,oBAAA,OAAA,SAAoB,2BAAA,IACpB,KAN6B,GAAA,iBAM7B,kBAAA,OAAA,SAAkB,qBAAA,IAClB,eAP6B,GAAA,cAQpB,iBARoB,GAAA,SASZ,wBATY,GAAA,iBAUb,uBAVa,GAAA,gBAW1B,mBAAA,UAX0B,IAW1B;IAVH;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;GAAA;AASA,MAAI,OAAO,UAAU,eAAe,YAAY,gBAAgB;AAC9D,YAAQ,KACN,2HAAA;;AAGJ,SAAO,SAAO,KAAK,KAAA;AAAQ,WAAA,QAAA,OAAA,MAAA,WAAA;;;;;AACjB,qBAAoD,IAAA,QAA5C,WAA4C,IAAA,UAAlC,QAAkC,IAAA,OAA3B,WAA2B,IAAA,UAAjB,SAAiB,IAAA,QAAT,OAAS,IAAA;AAUxD,kBAAA,OAAO,OAAO,WAAW,EAAE,KAAK,IAAA,IAAQ,KAP1C,MAOE,IAAA,KANFC,MAME,IAAA,SANF,UAAAA,QAAA,SAAU,IAAI,QAAQ,iBAAiB,OAAA,IAAAA,KACvCC,MAKE,IAAA,QALF,SAAAA,QAAA,SAAS,SAAAA,KACTC,MAIE,IAAA,iBAJF,kBAAAA,QAAA,SAAkB,yBAAA,OAAA,wBAA0B,SAAAA,KAC5CC,MAGE,IAAA,gBAHF,iBAAAA,QAAA,SAAiB,wBAAA,OAAA,uBAAwB,wBAAAA,KACzC,KAEE,IAAA,SAFF,UAAA,OAAA,SAAU,iBAAA,IACP,OAAA,UACD,KADC;cANH;cACA;cACA;cACA;cACA;cACA;aAAA;AAGE,qBAAsB,eAAA,cAAA,eAAA,CAAA,GACrB,gBAAA,GADqB;cAExB;aAAA,GACG,IAAA;AAGL,sBAAU,IAAI,QAAQ,eAAe,OAAA,CAAA;AACrC,iBAAA;AACG,mBAAA,CAAA,GAAM,eAAe,SAAS;cAC7B;cACA;cACA;cACA;cACA;aAAA,CAAA;;AANJ,eAAO,UACJ,GAAA,KAAA,KAMM;AAGH,4BAAgB,SAAC,MAAA;AACrB,qBAAA,OAAO,SAAS,aACf,cAAc,IAAA,KACb,MAAM,QAAQ,IAAA,KACd,OAAO,KAAK,WAAW;YAHzB;AAKF,gBAAI,CAAC,OAAO,QAAQ,IAAI,cAAA,KAAmB,cAAc,OAAO,IAAA,GAAO;AACrE,qBAAO,QAAQ,IAAI,gBAAgB,eAAA;;AAGrC,gBAAI,cAAc,OAAO,IAAA,KAAS,kBAAkB,OAAO,OAAA,GAAU;AACnE,qBAAO,OAAO,KAAK,UAAU,OAAO,MAAM,YAAA;;AAG5C,gBAAI,QAAQ;AACJ,wBAAU,CAAC,IAAI,QAAQ,GAAA,IAAO,MAAM;AACpC,sBAAQ,mBACV,iBAAiB,MAAA,IACjB,IAAI,gBAAgB,eAAe,MAAA,CAAA;AACvC,qBAAO,UAAU;;AAGnB,kBAAM,SAAS,SAAS,GAAA;AAElB,sBAAU,IAAI,QAAQ,KAAK,MAAA;AAC3B,2BAAe,IAAI,QAAQ,KAAK,MAAA;AACtC,mBAAO,EAAE,SAAS,aAAA;AAGhB,uBAAW,OACX,YACE,WACA,WAAW,WAAA;AACT,yBAAW;AACX,kBAAI,MAAA;YAAA,GACH,OAAA;;;;AAEM,mBAAA,CAAA,GAAM,QAAQ,OAAA,CAAA;;AAAzB,uBAAW,GAAA,KAAA;;;;AAEX,mBAAA,CAAA,GAAO;cACL,OAAO;gBACL,QAAQ,WAAW,kBAAkB;gBACrC,OAAO,OAAO,GAAA;;cAEhB;aAAA;;AAGF,gBAAI;AAAW,2BAAa,SAAA;;;;;;AAExB,4BAAgB,SAAS,MAAA;AAE/B,iBAAK,WAAW;AAGZ,2BAAuB;;;;AAGzB,mBAAA,CAAA,GAAM,QAAQ,IAAI;cAChB,eAAe,UAAU,eAAA,EAAiB,KACxC,SAACC,IAAA;AAAO,uBAAA,aAAaA;cAAb,GACR,SAACC,IAAA;AAAO,uBAAA,wBAAsBA;cAAtB,CAAsB;cAIhC,cAAc,KAAA,EAAO,KACnB,SAACD,IAAA;AAAO,uBAAA,eAAeA;cAAf,GACR,WAAA;cAAM,CAAA;aAAA,CAAA;;AATV,eAAA,KAAA;AAYA,gBAAI;AAAqB,oBAAM;;;;AAE/B,mBAAA,CAAA,GAAO;cACL,OAAO;gBACL,QAAQ;gBACR,gBAAgB,SAAS;gBACzB,MAAM;gBACN,OAAO,OAAO,GAAA;;cAEhB;aAAA;;AAIJ,mBAAA,CAAA,GAAO,eAAe,UAAU,UAAA,IAC5B;cACE,MAAM;cACN;gBAEF;cACE,OAAO;gBACL,QAAQ,SAAS;gBACjB,MAAM;;cAER;aAAA;;;KAAA;EAzHmB;AA6H3B,WAAA,eACE,UACA,iBAAA;AACA,WAAA,QAAA,MAAA,MAAA,WAAA;;;;;AACA,gBAAI,OAAO,oBAAoB,YAAY;AACzC,qBAAA,CAAA,GAAO,gBAAgB,QAAA,CAAA;;AAGzB,gBAAI,oBAAoB,gBAAgB;AACtC,gCAAkB,kBAAkB,SAAS,OAAA,IAAW,SAAS;;kBAG/D,oBAAoB;AAApB,qBAAA,CAAA,GAAA,CAAA;AACW,mBAAA,CAAA,GAAM,SAAS,KAAA,CAAA;;AAAtB,mBAAOJ,IAAA,KAAA;AACb,mBAAA,CAAA,GAAO,KAAK,SAAS,KAAK,MAAM,IAAA,IAAQ,IAAA;;AAG1C,mBAAA,CAAA,GAAO,SAAS,KAAA,CAAA;;;KAAA;EAAA;AAAA;ACpWb,IAAA;;EAAA,WAAA;AACL,aAAAM,cACkB,OACA,MAAY;AAAZ,UAAA,SAAA,QAAA;AAAA,eAAA;MAAY;AADZ,WAAA,QAAA;AACA,WAAA,OAAA;IAAA;AAAA,WAAAA;EAAA,EAAA;;ACoBpB,SAAA,eAA8B,SAAqB,YAAqB;AAA1C,MAAA,YAAA,QAAA;AAAA,cAAA;EAAkB;AAAG,MAAA,eAAA,QAAA;AAAA,iBAAA;EAAqB;AAAG,SAAA,QAAA,MAAA,MAAA,WAAA;;;;;AACnE,qBAAW,KAAK,IAAI,SAAS,UAAA;AAE7B,oBAAU,CAAC,GAAG,KAAK,OAAA,IAAW,QAAQ,OAAO;AACnD,iBAAA,CAAA,GAAM,IAAI,QAAQ,SAAC,SAAA;AACjB,mBAAA,WAAW,SAAC,KAAA;AAAa,qBAAA,QAAQ,GAAA;YAAR,GAAc,OAAA;UAAvC,CAAuC,CAAA;;AADzC,aAAA,KAAA;;;;;;;GACyC;AAAA;AAqC3C,SAAA,KAAcD,IAAA;AACZ,QAAM,OAAO,OAAO,IAAI,aAAa,EAAE,OAAOA,GAAA,CAAA,GAAM;IAClD,kBAAkB;GAAA;AAAA;AAItB,IAAM,gBAAgB,CAAA;AAEtB,IAAM,mBAIF,SAAC,WAAW,gBAAA;AAAmB,SAAA,SAAO,MAAM,KAAK,cAAA;AAAiB,WAAA,QAAA,QAAA,MAAA,WAAA;;;;;AAI9D,iCAA+B;cACnC;eACE,kBAA0B,eAAe;eACzC,gBAAwB,eAAe;cACzC,OAAO,SAAA,GAAA;AAAK,qBAAA,MAAM;YAAN,CAAM;AACb,yBAAc,mBAAmB,MAAM,EAAA,EAAA,CAAA;AAExC,oCAAgD,SAAC,GAAG,IAAIL,KAAE;kBAAA,UAAAA,IAAA;AAC9D,qBAAA,WAAW;YAAX;AAEI,sBAIF,eAAA,eAAA;cACF;cACA,SAAS;cACT,gBAAgB;eACb,cAAA,GACA,YAAA;AAED,qBAAQ;;;;AAEL,qBAAA,CAAA,GAAA,CAAA;;;;AAEY,mBAAA,CAAA,GAAM,UAAU,MAAM,KAAK,YAAA,CAAA;;AAApC,qBAAS,GAAA,KAAA;AAEf,gBAAI,OAAO,OAAO;AAChB,oBAAM,IAAI,aAAa,MAAA;;AAEzB,mBAAA,CAAA,GAAO,MAAA;;;AAEP;AAEA,gBAAI,IAAE,kBAAkB;AACtB,kBAAI,eAAa,cAAc;AAC7B,uBAAA,CAAA,GAAO,IAAE,KAAA;;AAIX,oBAAM;;AAGR,gBACE,eAAa,gBACb,CAAC,QAAQ,eAAe,IAAE,MAAM,OAA8B,MAAM;cAClE,SAAS;cACT,cAAc;cACd;aAAA,GAEF;AACA,qBAAA,CAAA,GAAO,IAAE,KAAA;;AAEX,mBAAA,CAAA,GAAM,QAAQ,QAAQ,QAAO,QAAQ,UAAA,CAAA;;AAArC,eAAA,KAAA;;;;;;;;;;;KAAqC;EAzD2B;AAAnC;AA8F5B,IAAM,QAAwB,OAAO,OAAO,kBAAkB,EAAE,KAAA,CAAA;ACrKhE,IAAM,UAA0B,aAAa,gBAAA;AAC7C,IAAM,cAA8B,aAAa,kBAAA;AACjD,IAAM,WAA2B,aAAa,eAAA;AAC9C,IAAM,YAA4B,aAAa,gBAAA;AAEtD,IAAI,cAAc;AAkBX,SAAA,eACL,UACA,eAAA;AAUA,WAAA,iBAAA;AACE,QAAM,cAAc,WAAA;AAAM,aAAA,SAAS,QAAA,CAAA;IAAT;AAC1B,QAAM,kBAAkB,WAAA;AAAM,aAAA,SAAS,YAAA,CAAA;IAAT;AAC9B,QAAM,eAAe,WAAA;AAAM,aAAA,SAAS,SAAA,CAAA;IAAT;AAC3B,QAAM,gBAAgB,WAAA;AAAM,aAAA,SAAS,UAAA,CAAA;IAAT;AAC5B,QAAM,yBAAyB,WAAA;AAC7B,UAAI,OAAO,SAAS,oBAAoB,WAAW;AACjD,oBAAA;aACK;AACL,wBAAA;;IAAA;AAIJ,QAAI,CAAC,aAAa;AAChB,UAAI,OAAO,WAAW,eAAe,OAAO,kBAAkB;AAE5D,eAAO,iBACL,oBACA,wBACA,KAAA;AAEF,eAAO,iBAAiB,SAAS,aAAa,KAAA;AAG9C,eAAO,iBAAiB,UAAU,cAAc,KAAA;AAChD,eAAO,iBAAiB,WAAW,eAAe,KAAA;AAClD,sBAAc;;;AAGlB,QAAM,cAAc,WAAA;AAClB,aAAO,oBAAoB,SAAS,WAAA;AACpC,aAAO,oBAAoB,oBAAoB,sBAAA;AAC/C,aAAO,oBAAoB,UAAU,YAAA;AACrC,aAAO,oBAAoB,WAAW,aAAA;AACtC,oBAAc;IAAA;AAEhB,WAAO;EAAA;AAGT,SAAO,gBACH,cAAc,UAAU,EAAE,SAAS,aAAa,WAAW,SAAA,CAAA,IAC3D,eAAA;AAAA;AEsHC,IAAK;CAAL,SAAK,iBAAA;AACV,kBAAA,OAAA,IAAQ;AACR,kBAAA,UAAA,IAAW;AAAA,GAFD,mBAAA,iBAAA,CAAA,EAAA;AAybL,SAAA,kBACLK,IAAA;AAEA,SAAOA,GAAE,SAAS,eAAe;AAAA;AAG5B,SAAA,qBACLA,IAAA;AAEA,SAAOA,GAAE,SAAS,eAAe;AAAA;AAkF5B,SAAA,oBACL,aAGA,QACA,OACA,UACA,MACA,gBAAA;AAEA,MAAI,WAAW,WAAA,GAAc;AAC3B,WAAO,YACL,QACA,OACA,UACA,IAAA,EAEC,IAAI,oBAAA,EACJ,IAAI,cAAA;;AAET,MAAI,MAAM,QAAQ,WAAA,GAAc;AAC9B,WAAO,YAAY,IAAI,oBAAA,EAAsB,IAAI,cAAA;;AAEnD,SAAO,CAAA;AAAA;AAGT,SAAA,WAAuBE,IAAA;AACrB,SAAO,OAAOA,OAAM;AAAA;AAGf,SAAA,qBACL,aAAA;AAEA,SAAO,OAAO,gBAAgB,WAAW,EAAE,MAAM,YAAA,IAAgB;AAAA;AE7vB5D,SAAA,aAAyB,GAAA;AAC9B,SAAO,KAAK;AAAA;ACoCP,IAAM,qBAAqB,OAAO,cAAA;AAClC,IAAM,gBAAgB,SAAC,KAAA;AAC5B,SAAA,OAAO,IAAI,kBAAA,MAAwB;AAAnC;AAoJK,SAAA,cAAuB,IAK5B;MAJA,qBAAA,GAAA,oBACA,aAAA,GAAA,YACA,gBAAA,GAAA,eACA,MAAA,GAAA,KACA,UAAA,GAAA;AAQA,MAAM,iBAGF,oBAAI,IAAA;AACR,MAAM,mBAGF,oBAAI,IAAA;AAEF,MAAA,KAIF,IAAI,iBAHN,yBAAA,GAAA,wBACA,uBAAA,GAAA,sBACA,4BAAA,GAAA;AAEF,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;AAIF,WAAA,iBAAA;AACE,UAAM,IAAI,MACR,0PAAA;EAAA;AAOJ,WAAA,8BAAA;AACE,QACE,OAAO,YAAY,eACnB,MACA;AACA,qBAAA;WACK;AACL,UAAM,UAAU,SACd,GAAA;AAEA,eAAA,MAAM,KAAK,EAAE,OAAA,CAAA,EAAU,QAAQ,SAAC,iBAAA;AAC9B,iBAAA,kBAAkB,OAAO,OAAO,eAAA,IAAmB,CAAA;QAAnD,CAAmD;MADrD;AAGF,aAAO,cAAA,cAAA,CAAA,GAAI,QAAQ,cAAA,CAAA,GAAoB,QAAQ,gBAAA,CAAA,EAAmB,OAChE,YAAA;;EAAA;AAKN,WAAA,qBAA8B,cAAsB,WAAA;AAClD,WAAO,SAAC,UAAA;AA/PZ,UAAA;AAgQM,UAAM,qBAAqB,QAAQ,oBAAoB,YAAA;AACvD,UAAM,gBAAgB,mBAAmB;QACvC;QACA;QACA;OAAA;AAEF,cAAO,KAAA,eAAe,IAAI,QAAA,MAAnB,OAAA,SAAA,GAA+B,aAAA;IAAA;EAAA;AAM1C,WAAA,wBAKE,eACA,0BAAA;AAEA,WAAO,SAAC,UAAA;AApRZ,UAAA;AAqRM,cAAO,KAAA,iBAAiB,IAAI,QAAA,MAArB,OAAA,SAAA,GAAiC,wBAAA;IAAA;EAAA;AAM5C,WAAA,yBAAA;AACE,WAAO,SAAC,UAAA;AACN,aAAA,OAAO,OAAO,eAAe,IAAI,QAAA,KAAa,CAAA,CAAA,EAAI,OAAO,YAAA;IAAzD;EAAyD;AAG7D,WAAA,2BAAA;AACE,WAAO,SAAC,UAAA;AACN,aAAA,OAAO,OAAO,iBAAiB,IAAI,QAAA,KAAa,CAAA,CAAA,EAAI,OAAO,YAAA;IAA3D;EAA2D;AAG/D,WAAA,kBAA2B,UAAA;AACzB,QAAI,MAAuC;AACzC,UAAK,kBAA0B;AAAW;AAC1C,UAAM,aAEQ,SACZ,IAAI,gBAAgB,2BAA2B;QAC7C,eAAe;QACf,WAAW;OAAA,CAAA;AAIb,wBAA0B,YAAY;AAGxC,UAAI,OAAO,eAAe,WAAW;AAEnC,cAAM,IAAI,MACR,2DAAyD,IAAI,cAAA,sGAAA;;;EAAA;AAOrE,WAAA,mBACE,cACA,oBAAA;AAEA,QAAM,cACJ,SACE,KACAP,KAKI;UALJC,MAAAD,QAAA,SAKI,CAAA,IAAAA,KAJF,KAAAC,IAAA,WAAA,YAAA,OAAA,SAAY,OAAA,IACZ,eAAAA,IAAA,cACA,sBAAAA,IAAA,qBAAA,KACC,oBAAqB,eAAAA,IAAA,EAAA;AAG1B,aAAA,SAAC,UAAU,UAAA;;AA5UjB,YAAA;AA6UQ,YAAM,gBAAgB,mBAAmB;UACvC,WAAW;UACX;UACA;SAAA;AAGF,YAAM,QAAQ,YAAAD,MAAA;UACZ,MAAM;UACN;UACA;UACA;UACA;UACA,cAAc;UACd;WAAAA,IACC,kBAAA,IAAqB;AAExB,YAAM,WACJ,IAAI,UAAU,YAAA,EACd,OAAO,GAAA;AAET,YAAM,cAAc,SAAS,KAAA;AAC7B,YAAM,aAAa,SAAS,SAAA,CAAA;AAE5B,0BAAkB,QAAA;AAEV,YAAA,YAAqB,YAAA,WAAV,QAAU,YAAA;AAE7B,YAAM,uBAAuB,WAAW,cAAc;AAEtD,YAAM,gBAAe,KAAA,eAAe,IAAI,QAAA,MAAnB,OAAA,SAAA,GAA+B,aAAA;AACpD,YAAM,kBAAkB,WAAA;AAAM,iBAAA,SAAS,SAAA,CAAA;QAAT;AAE9B,YAAM,eAA8C,OAAO,OACzD,eAGI,YAAY,KAAK,eAAA,IACjB,wBAAwB,CAAC,eAGzB,QAAQ,QAAQ,UAAA,IAGhB,QAAQ,IAAI,CAAC,cAAc,WAAA,CAAA,EAAc,KAAK,eAAA,GAClD;UACE;UACA;UACA;UACA;UACA;UACM,QAAA,WAAA;AAAS,mBAAA,QAAA,MAAA,MAAA,WAAA;;;;;AACE,2BAAA,CAAA,GAAM,YAAA;;AAAf,6BAASA,IAAA,KAAA;AAEf,wBAAI,OAAO,SAAS;AAClB,4BAAM,OAAO;;AAGf,2BAAA,CAAA,GAAO,OAAO,IAAA;;;aAAA;UAAA;UAEhB,SAAS,WAAA;AACP,mBAAA,SACE,YAAY,KAAK,EAAE,WAAW,OAAO,cAAc,KAAA,CAAA,CAAA;UADrD;UAGF,aAAA,WAAA;AACE,gBAAI;AACF,uBACE,uBAAuB;gBACrB;gBACA;eAAA,CAAA;UAAA;UAIR,2BAAA,SAA0B,SAAA;AACxB,yBAAa,sBAAsB;AACnC,qBACE,0BAA0B;cACxB;cACA;cACA;cACA;aAAA,CAAA;UAAA;SAAA;AAOV,YAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,cAAc;AAC3D,cAAM,YAAU,eAAe,IAAI,QAAA,KAAa,CAAA;AAChD,oBAAQ,aAAA,IAAiB;AACzB,yBAAe,IAAI,UAAU,SAAA;AAE7B,uBAAa,KAAK,WAAA;AAChB,mBAAO,UAAQ,aAAA;AACf,gBAAI,CAAC,OAAO,KAAK,SAAA,EAAS,QAAQ;AAChC,6BAAe,OAAO,QAAA;;UAAA,CAAA;;AAK5B,eAAO;MAAA;IApGT;AAsGF,WAAO;EAAA;AAGT,WAAA,sBACE,cAAA;AAEA,WAAO,SAAC,KAAKA,KAAkC;UAAlCC,MAAAD,QAAA,SAAkC,CAAA,IAAAA,KAAhC,KAAAC,IAAA,OAAA,QAAA,OAAA,SAAQ,OAAA,IAAM,gBAAAA,IAAA;AAC3B,aAAA,SAAC,UAAU,UAAA;AACT,YAAM,QAAQ,cAAc;UAC1B,MAAM;UACN;UACA,cAAc;UACd;UACA;SAAA;AAEF,YAAM,cAAc,SAAS,KAAA;AAC7B,0BAAkB,QAAA;AACV,YAAA,YAA6B,YAAA,WAAlB,QAAkB,YAAA,OAAX,SAAW,YAAA;AACrC,YAAM,qBAAqB,YACxB,OAAA,EACA,KAAK,SAAC,MAAA;AAAU,iBAAA,EAAE,KAAA;QAAF,CAAE,EAClB,MAAM,SAAC,OAAA;AAAW,iBAAA,EAAE,MAAA;QAAF,CAAE;AAEvB,YAAM,QAAQ,WAAA;AACZ,mBAAS,qBAAqB,EAAE,WAAW,cAAA,CAAA,CAAA;QAAA;AAG7C,YAAM,MAAM,OAAO,OAAO,oBAAoB;UAC5C,KAAK,YAAY;UACjB;UACA;UACA;UACA,aAAa;UACb;SAAA;AAGF,YAAM,UAAU,iBAAiB,IAAI,QAAA,KAAa,CAAA;AAClD,yBAAiB,IAAI,UAAU,OAAA;AAC/B,gBAAQ,SAAA,IAAa;AACrB,YAAI,KAAK,WAAA;AACP,iBAAO,QAAQ,SAAA;AACf,cAAI,CAAC,OAAO,KAAK,OAAA,EAAS,QAAQ;AAChC,6BAAiB,OAAO,QAAA;;QAAA,CAAA;AAG5B,YAAI,eAAe;AACjB,kBAAQ,aAAA,IAAiB;AACzB,cAAI,KAAK,WAAA;AACP,gBAAI,QAAQ,aAAA,MAAmB,KAAK;AAClC,qBAAO,QAAQ,aAAA;AACf,kBAAI,CAAC,OAAO,KAAK,OAAA,EAAS,QAAQ;AAChC,iCAAiB,OAAO,QAAA;;;UAAA,CAAA;;AAMhC,eAAO;MAAA;IAlDT;EAkDS;AAAA;ACpVf,SAAA,yBAAkC,sBAAA;AAChC,SAAO;AAAA;AAiEF,SAAA,YAIL,IAMA;AAVK,MAAA,QAAA;MAKL,cAAA,GAAA,aACA,YAAA,GAAA,WACW,sBAAA,GAAA,QAAA,qBACX,qBAAA,GAAA,oBACA,MAAA,GAAA,KACA,gBAAA,GAAA;AAWA,MAAM,iBACJ,SAAC,cAAc,MAAM,SAAS,gBAAA;AAAmB,WAAA,SAAC,UAAU,UAAA;AAC1D,UAAM,qBAAqB,oBAAoB,YAAA;AAE/C,UAAM,gBAAgB,mBAAmB;QACvC,WAAW;QACX;QACA;OAAA;AAGF,eACE,IAAI,gBAAgB,mBAAmB,EAAE,eAAe,QAAA,CAAA,CAAA;AAG1D,UAAI,CAAC,gBAAgB;AACnB;;AAGF,UAAM,WAAW,IAAI,UAAU,YAAA,EAAc,OAAO,IAAA,EAElD,SAAA,CAAA;AAGF,UAAM,eAAe,oBACnB,mBAAmB,cACnB,SAAS,MACT,QACA,MACA,CAAA,GACA,aAAA;AAGF,eACE,IAAI,gBAAgB,iBAAiB,EAAE,eAAe,aAAA,CAAA,CAAA;IAAA;EAhCT;AAoCnD,MAAM,kBACJ,SAAC,cAAc,MAAM,cAAc,gBAAiB;AAAjB,QAAA,mBAAA,QAAA;AAAA,uBAAA;IAAiB;AACpD,WAAA,SAAC,UAAU,UAAA;;AACT,UAAM,qBAAqB,IAAI,UAAU,YAAA;AAEzC,UAAM,eAAe,mBAAmB,OAAO,IAAA,EAE7C,SAAA,CAAA;AAGF,UAAI,MAAuB;QACzB,SAAS,CAAA;QACT,gBAAgB,CAAA;QAChB,MAAM,WAAA;AACJ,iBAAA,SACE,IAAI,KAAK,eACP,cACA,MACA,IAAI,gBACJ,cAAA,CAAA;QALJ;;AASJ,UAAI,aAAa,WAAW,YAAY,eAAe;AACrD,eAAO;;AAET,UAAI;AACJ,UAAI,UAAU,cAAc;AAC1B,YAAI,EAAY,aAAa,IAAA,GAAO;AAC5B,cAAA,KAAmC,GACvC,aAAa,MACb,YAAA,GAFK,QAAA,GAAA,CAAA,GAAO,UAAA,GAAA,CAAA,GAAS,iBAAA,GAAA,CAAA;AAIvB,WAAAD,MAAA,IAAI,SAAQ,KAAA,MAAAA,KAAQ,OAAA;AACpB,WAAA,KAAA,IAAI,gBAAe,KAAA,MAAA,IAAQ,cAAA;AAC3B,qBAAW;eACN;AACL,qBAAW,aAAa,aAAa,IAAA;AACrC,cAAI,QAAQ,KAAK,EAAE,IAAI,WAAW,MAAM,CAAA,GAAI,OAAO,SAAA,CAAA;AACnD,cAAI,eAAe,KAAK;YACtB,IAAI;YACJ,MAAM,CAAA;YACN,OAAO,aAAa;WAAA;;;AAK1B,eACE,IAAI,KAAK,eAAe,cAAc,MAAM,IAAI,SAAS,cAAA,CAAA;AAG3D,aAAO;IAAA;EAjDT;AAoDF,MAAM,kBACJ,SAAC,cAAc,MAAM,OAAA;AAAU,WAAA,SAAC,UAAA;;AAC9B,aAAO,SAEH,IAAI,UAAU,YAAA,EAId,SAAS,OAAAA,MAAA;QACT,WAAW;QACX,cAAc;SAAAA,IACb,kBAAA,IAAqB,WAAA;AAAO,eAAA;UAC3B,MAAM;;MADqB,QACrB;IAAA;EAXiB;AAiBjC,MAAM,kBAIF,SACF,IACA,IAAA;AASG,WAAA,QAAA,OAAA,CAVH,IACA,EAAA,GASG,SAVH,KACAA,KAOE;;;UANA,SAAAA,IAAA,QACA,QAAAA,IAAA,OACA,kBAAAA,IAAA,iBACA,mBAAAA,IAAA,kBACA,WAAAA,IAAA,UACA,WAAAA,IAAA,UACA,QAAAA,IAAA;;;;AAGI,iCAAqB,oBAAoB,IAAI,YAAA;;;;AAG7C,gCAIO;AACP,qBAAA;AACE,6BAAe;cACnB;cACA;cACA;cACA;cACA;cACA,UAAU,IAAI;cACd,MAAM,IAAI;cACV,QACE,IAAI,SAAS,UAAU,cAAc,KAAK,SAAA,CAAA,IAAc;;AAGtD,2BACJ,IAAI,SAAS,UAAU,IAAI,kBAAA,IAAsB;iBAC/C;AAAA,qBAAA,CAAA,GAAA,CAAA;AACF,qBAAS,aAAA;;;iBACA,mBAAmB;AAAnB,qBAAA,CAAA,GAAA,CAAA;AACA,mBAAA,CAAA,GAAM,UACb,mBAAmB,MAAM,IAAI,YAAA,GAC7B,gBACA,mBAAmB,YAAA,CAAA;;AAHrB,qBAAS,GAAA,KAAA;AAMT,gBAAI,mBAAmB,mBAAmB;AACxC,kCAAoB,mBAAmB;;;;AAGhC,mBAAA,CAAA,GAAM,mBAAmB,QAChC,IAAI,cACJ,gBACA,mBAAmB,cACnB,SAAC,MAAA;AACC,qBAAA,UAAU,MAAK,gBAAc,mBAAmB,YAAA;YAAhD,CAAgD,CAAA;;AALpD,qBAAS,GAAA,KAAA;;;AAQX,gBACE,OAAO,YAAY,eACnB,MACA;AACM,qBAAO,mBAAmB,QAAQ,gBAAgB;AACpD,oBAAA;AACJ,kBAAI,CAAC,QAAQ;AACX,sBAAS,OAAA;yBACA,OAAO,WAAW,UAAU;AACrC,sBAAS,OAAA;yBACA,OAAO,SAAS,OAAO,MAAM;AACtC,sBAAS,OAAA;yBACA,OAAO,UAAU,UAAa,OAAO,SAAS,QAAW;AAClE,sBAAS,OAAA;qBACJ;AACL,qBAAA,KAAA,GAAkB,KAAA,OAAO,KAAK,MAAA,GAAZ,KAAA,GAAA,QAAA,MAAqB;AAA5B,wBAAA,GAAA,EAAA;AACT,sBAAI,QAAQ,WAAW,QAAQ,UAAU,QAAQ,QAAQ;AACvD,0BAAM,4BAA0B,OAAA,+BAAiC,MAAA;AACjE;;;;AAIN,kBAAI,KAAK;AACP,wBAAQ,MACN,6CAA2C,IAAI,eAAA,sBAC3C,MAAA,oMAGJ,MAAA;;;AAKN,gBAAI,OAAO;AAAO,oBAAM,IAAI,aAAa,OAAO,OAAO,OAAO,IAAA;AAEvD,iBAAA;AACL,mBAAA,CAAA,GAAM,kBAAkB,OAAO,MAAM,OAAO,MAAM,IAAI,YAAA,CAAA;;AADxD,mBAAA,CAAA,GAAO,GAAA,MAAA,QAAA,CACL,GAAA,KAAA,IAAsD,KAAA;cAEpD,oBAAoB,KAAK,IAAA;cACzB,eAAe,OAAO;eAAA,GACrB,gBAAA,IAAmB,WAAA;;;AAIpB,2BAAe;kBACf,wBAAwB;AAAxB,qBAAA,CAAA,GAAA,EAAA;AACE,qCAIO;AAEX,gBACE,mBAAmB,SACnB,mBAAmB,wBACnB;AACA,uCAAyB,mBAAmB;;;;;AAGrC,iBAAA;AACL,mBAAA,CAAA,GAAM,uBACJ,aAAa,OACb,aAAa,MACb,IAAI,YAAA,CAAA;;AAJR,mBAAA,CAAA,GAAO,GAAA,MAAA,QAAA,CACL,GAAA,KAAA,IAGM,KAAA,EAEJ,eAAe,aAAa,KAAA,GAAA,GAAO,gBAAA,IAAmB,MAAA,GAAA,CAAA,CAAA;;;AAG1D,2BAAe;;;AAGnB,gBACE,OAAO,YAAY,eACnB,MACA;AACA,sBAAQ,MACN,wEAAsE,IAAI,eAAA,uFAE1E,YAAA;mBAEG;AACL,sBAAQ,MAAM,YAAA;;AAEhB,kBAAM;;;;;;;;KAAA;EAhIL;AAoIL,WAAA,cACE,KACA,OAAA;AAhfJ,QAAA,IAAA,IAAA,IAAA;AAkfI,QAAM,gBAAe,MAAA,KAAA,MAAM,WAAA,MAAN,OAAA,SAAA,GAAoB,YAApB,OAAA,SAAA,GAA8B,IAAI,aAAA;AACvD,QAAM,+BACJ,KAAA,MAAM,WAAA,MAAN,OAAA,SAAA,GAAoB,OAAO;AAE7B,QAAM,eAAe,gBAAA,OAAA,SAAA,aAAc;AACnC,QAAM,cACJ,KAAA,IAAI,iBAAJ,OAAA,KAAqB,IAAI,aAAa;AAExC,QAAI,YAAY;AAEd,aACE,eAAe,SACd,OAAO,oBAAI,KAAA,CAAA,IAAU,OAAO,YAAA,KAAiB,OAAQ;;AAG1D,WAAO;EAAA;AAGT,MAAM,aAAa,iBAId,cAAA,iBAA4B,iBAAiB;IAChD,gBAAA,WAAA;;AACE,aAAAA,MAAA,EAAS,kBAAkB,KAAK,IAAA,EAAA,GAAAA,IAAQ,gBAAA,IAAmB,MAAAA;IAAA;IAE7D,WAAA,SAAU,gBAAgBA,KAAE;UAAA,WAAAA,IAAA;AA5gBhC,UAAA,IAAA,IAAA;AA6gBM,UAAM,QAAQ,SAAA;AAEd,UAAM,gBACJ,MAAA,KAAA,MAAM,WAAA,MAAN,OAAA,SAAA,GAAoB,YAApB,OAAA,SAAA,GAA8B,eAAe,aAAA;AAC/C,UAAM,eAAe,gBAAA,OAAA,SAAA,aAAc;AACnC,UAAM,aAAa,eAAe;AAClC,UAAM,cAAc,gBAAA,OAAA,SAAA,aAAc;AAClC,UAAM,qBACJ,oBAAoB,eAAe,YAAA;AAKrC,UAAI,cAAc,cAAA,GAAiB;AACjC,eAAO;;AAIT,WAAI,gBAAA,OAAA,SAAA,aAAc,YAAW,WAAW;AACtC,eAAO;;AAIT,UAAI,cAAc,gBAAgB,KAAA,GAAQ;AACxC,eAAO;;AAGT,UACE,kBAAkB,kBAAA,OAClB,KAAA,sBAAA,OAAA,SAAA,mBAAoB,iBAApB,OAAA,SAAA,GAAA,KAAA,oBAAmC;QACjC;QACA;QACA,eAAe;QACf;OAAA,IAEF;AACA,eAAO;;AAIT,UAAI,cAAc;AAEhB,eAAO;;AAGT,aAAO;IAAA;IAET,4BAA4B;GAAA;AAG9B,MAAM,gBAAgB,iBAIjB,cAAA,oBAA+B,iBAAiB;IACnD,gBAAA,WAAA;;AACE,aAAAA,MAAA,EAAS,kBAAkB,KAAK,IAAA,EAAA,GAAAA,IAAQ,gBAAA,IAAmB,MAAAA;IAAA;GAAA;AAI/D,MAAM,cAAc,SAAC,SAAA;AACnB,WAAA,WAAW;EAAX;AACF,MAAM,YAAY,SAChB,SAAA;AAC+C,WAAA,iBAAiB;EAAjB;AAEjD,MAAM,WACJ,SACE,cACA,KACA,SAAA;AAEF,WAAA,SAAC,UAAwC,UAAA;AACvC,UAAM,QAAQ,YAAY,OAAA,KAAY,QAAQ;AAC9C,UAAM,SAAS,UAAU,OAAA,KAAY,QAAQ;AAE7C,UAAM,cAAc,SAAC,QAAiB;AAAjB,YAAA,WAAA,QAAA;AAAA,mBAAA;QAAiB;AACnC,eAAA,IAAI,UAAU,YAAA,EAA6C,SAC1D,KACA,EAAE,cAAc,OAAA,CAAA;MAFjB;AAIH,UAAM,mBACJ,IAAI,UAAU,YAAA,EACd,OAAO,GAAA,EAAK,SAAA,CAAA;AAEd,UAAI,OAAO;AACT,iBAAS,YAAA,CAAA;iBACA,QAAQ;AACjB,YAAM,kBAAkB,oBAAA,OAAA,SAAA,iBAAkB;AAC1C,YAAI,CAAC,iBAAiB;AACpB,mBAAS,YAAA,CAAA;AACT;;AAEF,YAAM,mBACH,OAAO,oBAAI,KAAA,CAAA,IAAU,OAAO,IAAI,KAAK,eAAA,CAAA,KAAqB,OAC3D;AACF,YAAI,iBAAiB;AACnB,mBAAS,YAAA,CAAA;;aAEN;AAEL,iBAAS,YAAY,KAAA,CAAA;;IAAA;EA7BzB;AAiCF,WAAA,gBAAyB,cAAA;AACvB,WAAO,SAAC,QAAA;AAvnBZ,UAAA,IAAA;AAwnBM,eAAA,MAAA,KAAA,UAAA,OAAA,SAAA,OAAQ,SAAR,OAAA,SAAA,GAAc,QAAd,OAAA,SAAA,GAAmB,kBAAiB;IAAA;EAAA;AAGxC,WAAA,uBAIE,OAAc,cAAA;AACd,WAAO;MACL,cAAc,QAAQ,UAAU,KAAA,GAAQ,gBAAgB,YAAA,CAAA;MACxD,gBAAgB,QACd,YAAY,KAAA,GACZ,gBAAgB,YAAA,CAAA;MAElB,eAAe,QAAQ,WAAW,KAAA,GAAQ,gBAAgB,YAAA,CAAA;;EAAA;AAI9D,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;;AAAA;AAIG,SAAA,yBACL,QAGA,MACA,qBACA,eAAA;AAEA,SAAO,oBACL,oBAAoB,OAAO,KAAK,IAAI,YAAA,EAAc,IAAA,GAClD,YAAY,MAAA,IAAU,OAAO,UAAU,QACvC,oBAAoB,MAAA,IAAU,OAAO,UAAU,QAC/C,OAAO,KAAK,IAAI,cAChB,mBAAmB,OAAO,OAAO,OAAO,KAAK,gBAAgB,QAC7D,aAAA;AAAA;AHrnBJ,SAAA,4BACE,OACA,eACA,QAAA;AAEA,MAAM,WAAW,MAAM,aAAA;AACvB,MAAI,UAAU;AACZ,WAAO,QAAA;;AAAA;AAcJ,SAAA,oBACL,IAAA;AApEF,MAAA;AAyEE,UAAQ,KAAA,SAAS,KAAK,GAAG,IAAI,gBAAgB,GAAG,kBAAxC,OAAA,KAA0D,GAAG;AAAA;AAGvE,SAAA,+BACE,OACA,IAGA,QAAA;AAEA,MAAM,WAAW,MAAM,oBAAoB,EAAA,CAAA;AAC3C,MAAI,UAAU;AACZ,WAAO,QAAA;;AAAA;AAIX,IAAM,eAAe,CAAA;AAEd,SAAA,WAAoB,IAWzB;MAVA,cAAA,GAAA,aACA,aAAA,GAAA,YACA,gBAAA,GAAA,eACA,KAAA,GAAA,SACuB,cAAA,GAAA,qBACrB,SAAA,GAAA,QACA,yBAAA,GAAA,wBACA,qBAAA,GAAA,oBAEF,gBAAA,GAAA,eACA,SAAA,GAAA;AAYA,MAAM,gBAAgB,aAAgB,cAAA,gBAAA;AACtC,MAAM,aAAa,YAAY;IAC7B,MAAS,cAAA;IACT;IACA,UAAU;MACR,mBAAmB;QACjB,SAAA,SACE,OACAA,KAAa;cAAA,gBAAAA,IAAA,QAAA;AAEb,iBAAO,MAAM,aAAA;QAAA;QAEf,SAAS,mBAAA;;MAEX,oBAAoB;QAClB,SAAA,SACE,OACAA,KAC4B;cAA1BC,MAAAD,IAAA,SAAW,gBAAAC,IAAA,eAAe,UAAAA,IAAA;AAK5B,sCAA4B,OAAO,eAAe,SAAC,UAAA;AACjD,qBAAS,OAAO,GAAa,SAAS,MAAa,QAAQ,OAAA,CAAA;UAAA,CAAA;QAAA;QAG/D,SAAS,mBAAA;;;IAKb,eAAA,SAAc,SAAA;AACZ,cACG,QAAQ,WAAW,SAAS,SAAC,OAAOD,KAAgB;YAAd,OAAAA,IAAA,MAAc,MAAAA,IAAA,KAAA;AApJ7D,YAAA,IAAA;AAqJU,YAAM,YAAY,cAAc,GAAA;AAChC,YAAI,IAAI,aAAa,WAAW;AAE9B,WAAA,KAAA,MAAA,KAAM,IAAI,aAAA,MAAV,OAAA,KAAA,MAAA,EAAA,IAA6B;YAC3B,QAAQ,YAAY;YACpB,cAAc,IAAI;;;AAItB,oCAA4B,OAAO,IAAI,eAAe,SAAC,UAAA;AACrD,mBAAS,SAAS,YAAY;AAE9B,mBAAS,YACP,aAAa,SAAS,YAElB,SAAS,YAET,KAAK;AACX,cAAI,IAAI,iBAAiB,QAAW;AAClC,qBAAS,eAAe,IAAI;;AAE9B,mBAAS,mBAAmB,KAAK;QAAA,CAAA;MAAA,CAAA,EAGpC,QAAQ,WAAW,WAAW,SAAC,OAAOA,KAAQ;YAAN,OAAAA,IAAA,MAAM,UAAAA,IAAA;AAC7C,oCACE,OACA,KAAK,IAAI,eACT,SAAC,UAAA;AAjLb,cAAA;AAkLc,cACE,SAAS,cAAc,KAAK,aAC5B,CAAC,cAAc,KAAK,GAAA;AAEpB;AACM,cAAA,QAAU,YAChB,KAAK,IAAI,YAAA,EAAA;AAEX,mBAAS,SAAS,YAAY;AAE9B,cAAI,OAAO;AACT,gBAAI,SAAS,SAAS,QAAW;AACvB,kBAAA,uBACN,KAAA,oBAD0B,QAC1B,KAAA,KAD+B,kBAC/B,KAAA,eAD8C,cAC9C,KAAA;AAKF,kBAAI,UAAU,kBACZ,SAAS,MACT,SAAC,mBAAA;AAEC,uBAAO,MAAM,mBAAmB,SAAS;kBACvC,KAAK,MAAI;kBACT,eAAA;kBACA,oBAAA;kBACA,WAAA;iBAAA;cAAA,CAAA;AAIN,uBAAS,OAAO;mBACX;AAEL,uBAAS,OAAO;;iBAEb;AAEL,qBAAS,SACP,KAAA,YAAY,KAAK,IAAI,YAAA,EAAc,sBAAnC,OAAA,KAAwD,QACpD,0BACE,EAAQ,SAAS,IAAA,IACb,EAAS,SAAS,IAAA,IAClB,SAAS,MACb,OAAA,IAEF;;AAGR,iBAAO,SAAS;AAChB,mBAAS,qBAAqB,KAAK;QAAA,CAAA;MAAA,CAAA,EAIxC,QACC,WAAW,UACX,SAAC,OAAOA,KAA8C;YAA5CC,MAAAD,IAAA,MAAQ,YAAAC,IAAA,WAAW,MAAAA,IAAA,KAAK,YAAAA,IAAA,WAAa,QAAAD,IAAA,OAAO,UAAAA,IAAA;AACpD,oCACE,OACA,IAAI,eACJ,SAAC,UAAA;AACC,cAAI,WAAW;iBAER;AAEL,gBAAI,SAAS,cAAc;AAAW;AACtC,qBAAS,SAAS,YAAY;AAC9B,qBAAS,QAAS,WAAA,OAAA,UAAW;;QAAA,CAAA;MAAA,CAAA,EAMtC,WAAW,oBAAoB,SAAC,OAAO,QAAA;AAC9B,YAAA,UAAY,uBAAuB,MAAA,EAAA;AAC3C,iBAA2BA,MAAA,GAAAC,MAAA,OAAO,QAAQ,OAAA,GAAfD,MAAAC,IAAA,QAAAD,OAAyB;AAAzC,cAAA,KAAAC,IAAAD,GAAA,GAAC,MAAA,GAAA,CAAA,GAAK,QAAA,GAAA,CAAA;AACf,eAEE,SAAA,OAAA,SAAA,MAAO,YAAW,YAAY,cAC9B,SAAA,OAAA,SAAA,MAAO,YAAW,YAAY,UAC9B;AACA,kBAAM,GAAA,IAAO;;;MAAA,CAAA;IAAA;GAAA;AAMzB,MAAM,gBAAgB,YAAY;IAChC,MAAS,cAAA;IACT;IACA,UAAU;MACR,sBAAsB;QACpB,SAAA,SAAQ,OAAOA,KAAE;cAAA,UAAAA,IAAA;AACf,cAAM,WAAW,oBAAoB,OAAA;AACrC,cAAI,YAAY,OAAO;AACrB,mBAAO,MAAM,QAAA;;QAAA;QAGjB,SAAS,mBAAA;;;IAGb,eAAA,SAAc,SAAA;AACZ,cACG,QACC,cAAc,SACd,SAAC,OAAOA,KAAgC;YAA9B,OAAAA,IAAA,MAAMC,MAAAD,IAAA,MAAQ,YAAAC,IAAA,WAAW,MAAAA,IAAA,KAAK,mBAAAA,IAAA;AACtC,YAAI,CAAC,IAAI;AAAO;AAEhB,cAAM,oBAAoB,IAAA,CAAA,IAAS;UACjC;UACA,QAAQ,YAAY;UACpB,cAAc,IAAI;UAClB;;MAAA,CAAA,EAIL,QAAQ,cAAc,WAAW,SAAC,OAAOD,KAAW;YAAT,UAAAA,IAAA,SAAS,OAAAA,IAAA;AACnD,YAAI,CAAC,KAAK,IAAI;AAAO;AAErB,uCAA+B,OAAO,MAAM,SAAC,UAAA;AAC3C,cAAI,SAAS,cAAc,KAAK;AAAW;AAC3C,mBAAS,SAAS,YAAY;AAC9B,mBAAS,OAAO;AAChB,mBAAS,qBAAqB,KAAK;QAAA,CAAA;MAAA,CAAA,EAGtC,QAAQ,cAAc,UAAU,SAAC,OAAOA,KAAkB;YAAhB,UAAAA,IAAA,SAAS,QAAAA,IAAA,OAAO,OAAAA,IAAA;AACzD,YAAI,CAAC,KAAK,IAAI;AAAO;AAErB,uCAA+B,OAAO,MAAM,SAAC,UAAA;AAC3C,cAAI,SAAS,cAAc,KAAK;AAAW;AAE3C,mBAAS,SAAS,YAAY;AAC9B,mBAAS,QAAS,WAAA,OAAA,UAAW;QAAA,CAAA;MAAA,CAAA,EAGhC,WAAW,oBAAoB,SAAC,OAAO,QAAA;AAC9B,YAAA,YAAc,uBAAuB,MAAA,EAAA;AAC7C,iBAA2BA,MAAA,GAAAC,MAAA,OAAO,QAAQ,SAAA,GAAfD,MAAAC,IAAA,QAAAD,OAA2B;AAA3C,cAAA,KAAAC,IAAAD,GAAA,GAAC,MAAA,GAAA,CAAA,GAAK,QAAA,GAAA,CAAA;AACf,gBAEG,SAAA,OAAA,SAAA,MAAO,YAAW,YAAY,cAC7B,SAAA,OAAA,SAAA,MAAO,YAAW,YAAY,aAEhC,SAAQ,SAAA,OAAA,SAAA,MAAO,YACf;AACA,kBAAM,GAAA,IAAO;;;MAAA,CAAA;IAAA;GAAA;AAOzB,MAAM,oBAAoB,YAAY;IACpC,MAAS,cAAA;IACT;IACA,UAAU;MACR,kBAAkB;QAChB,SAAA,SACE,OACA,QAAA;AAjVV,cAAA,IAAA,IAAA,IAAA;AAsVgB,cAAAA,MAAkC,OAAO,SAAvC,gBAAAA,IAAA,eAAe,eAAAA,IAAA;AAEvB,mBAAmCC,MAAA,GAAA,KAAA,OAAO,OAAO,KAAA,GAAdA,MAAA,GAAA,QAAAA,OAAsB;AAAzD,gBAAW,uBAAA,GAAAA,GAAA;AACT,qBAA8B,KAAA,GAAA,KAAA,OAAO,OAAO,oBAAA,GAAd,KAAA,GAAA,QAAA,MAAqC;AAAnE,kBAAW,kBAAA,GAAA,EAAA;AACT,kBAAM,UAAU,gBAAgB,QAAQ,aAAA;AACxC,kBAAI,YAAY,IAAI;AAClB,gCAAgB,OAAO,SAAS,CAAA;;;;AAKtC,mBAA2B,KAAA,GAAA,iBAAA,cAAA,KAAA,eAAA,QAAA,MAAc;AAA9B,gBAAA,KAAA,eAAA,EAAA,GAAE,OAAA,GAAA,MAAM,KAAA,GAAA;AACjB,gBAAM,qBAAsB,MAAA,MAAA,KAAA,MAAA,IAAA,MAAA,OAAA,KAAA,MAAA,IAAA,IAAgB,CAAA,GAAhB,KAC1B,MAAM,uBAAA,MADoB,OAAA,KAAA,GAAA,EAAA,IAEtB,CAAA;AACN,gBAAM,oBAAoB,kBAAkB,SAAS,aAAA;AACrD,gBAAI,CAAC,mBAAmB;AACtB,gCAAkB,KAAK,aAAA;;;QAAA;QAI7B,SAAS,mBAAA;;;IAMb,eAAA,SAAc,SAAA;AACZ,cACG,QACC,WAAW,QAAQ,mBACnB,SAAC,OAAOD,KAAa;YAAA,gBAAAA,IAAA,QAAA;AACnB,iBAAmCC,MAAA,GAAA,KAAA,OAAO,OAAO,KAAA,GAAdA,MAAA,GAAA,QAAAA,OAAsB;AAAzD,cAAW,uBAAA,GAAAA,GAAA;AACT,mBAA8B,KAAA,GAAA,KAAA,OAAO,OACnC,oBAAA,GAD4B,KAAA,GAAA,QAAA,MAE3B;AAFH,gBAAW,kBAAA,GAAA,EAAA;AAGT,gBAAM,UAAU,gBAAgB,QAAQ,aAAA;AACxC,gBAAI,YAAY,IAAI;AAClB,8BAAgB,OAAO,SAAS,CAAA;;;;MAAA,CAAA,EAMzC,WAAW,oBAAoB,SAAC,OAAO,QAAA;AAlYhD,YAAA,IAAA,IAAA,IAAA;AAmYkB,YAAA,WAAa,uBAAuB,MAAA,EAAA;AAC5C,iBAAmCD,MAAA,GAAAC,MAAA,OAAO,QAAQ,QAAA,GAAfD,MAAAC,IAAA,QAAAD,OAA0B;AAAlD,cAAA,KAAAC,IAAAD,GAAA,GAAC,OAAA,GAAA,CAAA,GAAM,eAAA,GAAA,CAAA;AAChB,mBAA8B,KAAA,GAAA,KAAA,OAAO,QAAQ,YAAA,GAAf,KAAA,GAAA,QAAA,MAA8B;AAAjD,gBAAA,KAAA,GAAA,EAAA,GAAC,KAAA,GAAA,CAAA,GAAI,YAAA,GAAA,CAAA;AACd,gBAAM,qBAAsB,MAAA,MAAA,KAAA,MAAA,IAAA,MAAA,OAAA,KAAA,MAAA,IAAA,IAAgB,CAAA,GAAhB,KAC1B,MAAM,uBAAA,MADoB,OAAA,KAAA,GAAA,EAAA,IAEtB,CAAA;AACN,qBAA4B,KAAA,GAAA,cAAA,WAAA,KAAA,YAAA,QAAA,MAAW;AAAvC,kBAAW,gBAAA,YAAA,EAAA;AACT,kBAAM,oBACJ,kBAAkB,SAAS,aAAA;AAC7B,kBAAI,CAAC,mBAAmB;AACtB,kCAAkB,KAAK,aAAA;;;;;MAAA,CAAA,EAMhC,WACC,QAAQ,YAAY,UAAA,GAAa,oBAAoB,UAAA,CAAA,GACrD,SAAC,OAAO,QAAA;AACN,YAAM,eAAe,yBACnB,QACA,gBACA,aACA,aAAA;AAEM,YAAA,gBAAkB,OAAO,KAAK,IAAA;AAEtC,0BAAkB,aAAa,iBAC7B,OACA,kBAAkB,QAAQ,iBAAiB;UACzC;UACA;SAAA,CAAA;MAAA,CAAA;IAAA;GAAA;AASd,MAAM,oBAAoB,YAAY;IACpC,MAAS,cAAA;IACT;IACA,UAAU;MACR,2BAAA,SACE,GACA,GAAA;MAOA;MAGF,wBAAA,SACE,GACA,GAAA;MACA;MAGF,4BAAA,SACE,GACA,GAAA;MACA;;GAAA;AAMN,MAAM,6BAA6B,YAAY;IAC7C,MAAS,cAAA;IACT;IACA,UAAU;MACR,sBAAsB;QACpB,SAAA,SAAQ,OAAO,QAAA;AACb,iBAAO,GAAa,OAAO,OAAO,OAAA;QAAA;QAEpC,SAAS,mBAAA;;;GAAA;AAKf,MAAM,cAAc,YAAY;IAC9B,MAAS,cAAA;IACT,cAAc,eAAA;MACZ,QAAQ,SAAA;MACR,SAAS,kBAAA;MACT,sBAAsB;OACnB,MAAA;IAEL,UAAU;MACR,sBAAA,SAAqB,OAAOA,KAAE;YAAA,UAAAA,IAAA;AAC5B,cAAM,uBACJ,MAAM,yBAAyB,cAAc,WAAW,UACpD,aACA;MAAA;;IAGV,eAAe,SAAC,SAAA;AACd,cACG,QAAQ,UAAU,SAAC,OAAA;AAClB,cAAM,SAAS;MAAA,CAAA,EAEhB,QAAQ,WAAW,SAAC,OAAA;AACnB,cAAM,SAAS;MAAA,CAAA,EAEhB,QAAQ,SAAS,SAAC,OAAA;AACjB,cAAM,UAAU;MAAA,CAAA,EAEjB,QAAQ,aAAa,SAAC,OAAA;AACrB,cAAM,UAAU;MAAA,CAAA,EAIjB,WAAW,oBAAoB,SAAC,OAAA;AAAW,eAAA,eAAA,CAAA,GAAK,KAAA;MAAL,CAAK;IAAA;GAAA;AAIvD,MAAM,kBAAkB,gBAEtB;IACA,SAAS,WAAW;IACpB,WAAW,cAAc;IACzB,UAAU,kBAAkB;IAC5B,eAAe,2BAA2B;IAC1C,QAAQ,YAAY;GAAA;AAGtB,MAAM,UAAkC,SAAC,OAAO,QAAA;AAC9C,WAAA,gBAAgB,cAAc,MAAM,MAAA,IAAU,SAAY,OAAO,MAAA;EAAjE;AAEF,MAAM,UAAU,cAAA,eAAA,eAAA,eAAA,eAAA,eAAA,eAAA,CAAA,GACX,YAAY,OAAA,GACZ,WAAW,OAAA,GACX,kBAAkB,OAAA,GAClB,2BAA2B,OAAA,GAC3B,cAAc,OAAA,GACd,kBAAkB,OAAA,GANP;IAQd,2BAA2B,cAAc,QAAQ;IACjD;GAAA;AAGF,SAAO,EAAE,SAAS,QAAA;AAAA;AFteb,IAAM,YAA4B,OAAO,IAAI,gBAAA;AAE7C,IAAM,eAAe;AAyD5B,IAAM,kBAAsC;EAC1C,QAAQ,YAAY;;AAItB,IAAM,uBAAuC,kBAC3C,iBACA,WAAA;AAAM,CAAA;AAER,IAAM,0BAA0C,kBAC9C,iBACA,WAAA;AAAM,CAAA;AAGD,SAAA,eAGL,IAEA;MADA,qBAAA,GAAA,oBACA,cAAA,GAAA;AAOA,MAAM,qBAAqB,SAAC,OAAA;AAAqB,WAAA;EAAA;AACjD,MAAM,wBAAwB,SAAC,OAAA;AAAqB,WAAA;EAAA;AAEpD,SAAO,EAAE,oBAAoB,uBAAuB,oBAAA;AAEpD,WAAA,iBACE,UAAA;AAEA,WAAO,eAAA,eAAA,CAAA,GACF,QAAA,GACA,sBAAsB,SAAS,MAAA,CAAA;EAAA;AAItC,WAAA,oBAA6B,WAAA;AAC3B,QAAM,QAAQ,UAAU,WAAA;AACxB,QAAI,MAAuC;AACzC,UAAI,CAAC,OAAO;AACV,YAAK,oBAA4B;AAAW,iBAAO;AACjD,4BAA4B,YAAY;AAC1C,gBAAQ,MACN,oCAAmC,cAAA,oDAAA;;;AAIzC,WAAO;EAAA;AAGT,WAAA,mBACE,cACA,oBAAA;AAEA,WAAQ,SAAC,WAAA;AACP,UAAM,iBAAiB,mBAAmB;QACxC;QACA;QACA;OAAA;AAEF,UAAM,sBAAsB,SAAC,OAAA;AAzKnC,YAAA,IAAA,IAAA;AA0KQ,gBAAA,MAAA,MAAA,KAAA,oBAAoB,KAAA,MAApB,OAAA,SAAA,GAA4B,YAA5B,OAAA,SAAA,GAAsC,cAAA,MAAtC,OAAA,KACA;MAAA;AACF,UAAM,2BACJ,cAAc,YAAY,qBAAqB;AAEjD,aAAO,eAAe,0BAA0B,gBAAA;IAAA;EAAA;AAIpD,WAAA,wBAAA;AACE,WAAQ,SAAC,IAAA;AApLb,UAAA;AAqLM,UAAI;AACJ,UAAI,OAAO,OAAO,UAAU;AAC1B,sBAAa,KAAA,oBAAoB,EAAA,MAApB,OAAA,KAA2B;aACnC;AACL,qBAAa;;AAEf,UAAM,yBAAyB,SAAC,OAAA;AA3LtC,YAAA,KAAA,IAAA;AA4LQ,gBAAA,MAAA,MAAA,MAAA,oBAAoB,KAAA,MAApB,OAAA,SAAA,IAA4B,cAA5B,OAAA,SAAA,GAAwC,UAAA,MAAxC,OAAA,KACA;MAAA;AACF,UAAM,8BACJ,eAAe,YACX,wBACA;AAEN,aAAO,eAAe,6BAA6B,gBAAA;IAAA;EAAA;AAIvD,WAAA,oBACE,OACA,MAAA;AAzMJ,QAAA;AA+MI,QAAM,WAAW,MAAM,WAAA;AACvB,QAAM,eAAe,oBAAI,IAAA;AACzB,aAAkBA,MAAA,GAAA,KAAA,KAAK,IAAI,oBAAA,GAATA,MAAA,GAAA,QAAAA,OAAgC;AAAlD,UAAW,MAAA,GAAAA,GAAA;AACT,UAAM,WAAW,SAAS,SAAS,IAAI,IAAA;AACvC,UAAI,CAAC,UAAU;AACb;;AAGF,UAAI,2BACD,KAAA,IAAI,OAAO,SAER,SAAS,IAAI,EAAA,IAEb,QAAQ,OAAO,OAAO,QAAA,CAAA,MAJzB,OAAA,KAIwC,CAAA;AAE3C,eAAyB,KAAA,GAAA,4BAAA,yBAAA,KAAA,0BAAA,QAAA,MAAyB;AAAlD,YAAW,aAAA,0BAAA,EAAA;AACT,qBAAa,IAAI,UAAA;;;AAIrB,WAAO,QACL,MAAM,KAAK,aAAa,OAAA,CAAA,EAAU,IAAI,SAAC,eAAA;AACrC,UAAM,gBAAgB,SAAS,QAAQ,aAAA;AACvC,aAAO,gBACH;QACE;UACE;UACA,cAAc,cAAc;UAC5B,cAAc,cAAc;;UAGhC,CAAA;IAAA,CAAA,CAAA;EAAA;AAAA;AM1OZ,IAAM,QAA0C,UAC5C,oBAAI,QAAA,IACJ;AAEG,IAAM,4BAAqD,SAAC,IAEjE;MADA,eAAA,GAAA,cACA,YAAA,GAAA;AAEA,MAAI,aAAa;AAEjB,MAAM,SAAS,SAAA,OAAA,SAAA,MAAO,IAAI,SAAA;AAE1B,MAAI,OAAO,WAAW,UAAU;AAC9B,iBAAa;SACR;AACL,QAAM,cAAc,KAAK,UAAU,WAAW,SAAC,KAAK,OAAA;AAClD,aAAA,cAAc,KAAA,IACV,OAAO,KAAK,KAAA,EACT,KAAA,EACA,OAAY,SAAC,KAAK,MAAA;AACjB,YAAI,IAAA,IAAQ,MAAc,IAAA;AAC1B,eAAO;MAAA,GACN,CAAA,CAAA,IACL;IAPJ,CAOI;AAEN,QAAI,cAAc,SAAA,GAAY;AAC5B,eAAA,OAAA,SAAA,MAAO,IAAI,WAAW,WAAA;;AAExB,iBAAa;;AAGf,SAAU,eAAA,MAAgB,aAAA;AAAA;ACiMrB,SAAA,iBAAA;AAAA,MAAA,UAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MACF;AADE,YAAA,EAAA,IAAA,UAAA,EAAA;;AAGL,SAAO,SAAA,cAAuB,SAAA;AAC5B,QAAM,yBAAyB,eAAe,SAAC,QAAA;AAxOnD,UAAA,IAAA;AAyOM,cAAA,KAAA,QAAQ,2BAAR,OAAA,SAAA,GAAA,KAAA,SAAiC,QAAQ;QACvC,cAAc,KAAA,QAAQ,gBAAR,OAAA,KAAuB;OAAA;IAAA,CAAA;AAIzC,QAAM,sBAA4D,cAAA,eAAA;MAChE,aAAa;MACb,mBAAmB;MACnB,2BAA2B;MAC3B,gBAAgB;MAChB,oBAAoB;OACjB,OAAA,GAN6D;MAOhE;MACA,oBAAA,SAAmB,cAAA;AACjB,YAAI,0BAA0B;AAC9B,YAAI,wBAAwB,aAAa,oBAAoB;AAC3D,cAAM,gBACJ,aAAa,mBAAmB;AAClC,oCAA0B,SAAC,eAAA;AACzB,gBAAM,gBAAgB,cAAY,aAAA;AAClC,gBAAI,OAAO,kBAAkB,UAAU;AAErC,qBAAO;mBACF;AAGL,qBAAO,0BAA0B,cAAA,eAAA,CAAA,GAC5B,aAAA,GAD4B;gBAE/B,WAAW;eAAA,CAAA;;UAAA;mBAIR,QAAQ,oBAAoB;AACrC,oCAA0B,QAAQ;;AAGpC,eAAO,wBAAwB,YAAA;MAAA;MAEjC,UAAA,cAAA,CAAA,GAAe,QAAQ,YAAY,CAAA,CAAA;KAAA;AAGrC,QAAM,UAA2C;MAC/C,qBAAqB,CAAA;MACrB,OAAA,SAAM,IAAA;AAEJ,WAAA;MAAA;MAEF,QAAQ,OAAA;MACR;MACA,oBAAoB,eAClB,SAAC,QAAA;AAAW,eAAA,uBAAuB,MAAA,KAAW;MAAlC,CAAkC;;AAIlD,QAAM,MAAM;MACV;MACA,kBAAA,SAAiBA,KAAe;YAAb,cAAAA,IAAA,aAAa,YAAAA,IAAA;AAC9B,YAAI,aAAa;AACf,mBAAiB,KAAA,GAAA,gBAAA,aAAA,KAAA,cAAA,QAAA,MAAa;AAA9B,gBAAW,KAAA,cAAA,EAAA;AACT,gBAAI,CAAC,oBAAoB,SAAU,SAAS,EAAA,GAAY;AACtD;AAAE,kCAAoB,SAAmB,KAAK,EAAA;;;;AAIpD,YAAI,WAAW;AACb,mBAAgD,KAAA,GAAA,KAAA,OAAO,QACrD,SAAA,GAD8C,KAAA,GAAA,QAAA,MAE7C;AAFQ,gBAAA,KAAA,GAAA,EAAA,GAAC,eAAA,GAAA,CAAA,GAAc,oBAAA,GAAA,CAAA;AAGxB,gBAAI,OAAO,sBAAsB,YAAY;AAC3C,gCAAkB,QAAQ,oBAAoB,YAAA,CAAA;mBACzC;AACL,qBAAO,OACL,QAAQ,oBAAoB,YAAA,KAAiB,CAAA,GAC7C,iBAAA;;;;AAKR,eAAO;MAAA;;AAIX,QAAM,qBAAqB,QAAQ,IAAI,SAAC,GAAA;AACtC,aAAA,EAAE,KAAK,KAAY,qBAA4B,OAAA;IAA/C,CAA+C;AAGjD,aAAA,gBACE,QAAA;AAEA,UAAM,qBAAqB,OAAO,UAAU;QAC1C,OAAO,SAAC,GAAA;AAAO,iBAAA,cAAA,eAAA,CAAA,GAAK,CAAA,GAAL,EAAQ,MAAM,eAAe,MAAA,CAAA;QAA7B;QACf,UAAU,SAAC,GAAA;AAAO,iBAAA,cAAA,eAAA,CAAA,GAAK,CAAA,GAAL,EAAQ,MAAM,eAAe,SAAA,CAAA;QAA7B;OAA6B;AAGjD,eAAyCA,MAAA,GAAA,KAAA,OAAO,QAC9C,kBAAA,GADuCA,MAAA,GAAA,QAAAA,OAEtC;AAFQ,YAAA,KAAA,GAAAA,GAAA,GAAC,eAAA,GAAA,CAAA,GAAc,aAAA,GAAA,CAAA;AAGxB,YACE,CAAC,OAAO,oBACR,gBAAgB,QAAQ,qBACxB;AACA,cACE,OAAO,YAAY,eACnB,MACA;AACA,oBAAQ,MACN,wEAAwE,eAAA,8CAAA;;AAI5E;;AAGF,gBAAQ,oBAAoB,YAAA,IAAgB;AAC5C,iBAAgB,KAAA,GAAA,uBAAA,oBAAA,KAAA,qBAAA,QAAA,MAAoB;AAApC,cAAW,IAAA,qBAAA,EAAA;AACT,YAAE,eAAe,cAAc,UAAA;;;AAInC,aAAO;IAAA;AAGT,WAAO,IAAI,gBAAgB,EAAE,WAAW,QAAQ,UAAA,CAAA;EAAA;AAAA;AC1V7C,SAAA,gBAAA;AAML,SAAO,WAAA;AACL,UAAM,IAAI,MACR,+FAAA;EAAA;AAAA;AEHN,SAAA,cAAuB,KAAA;AAGrB,WAAS,KAAK,KAAK;AAEjB,WAAO;;AAET,SAAO;AAAA;AAyBF,IAAM,mCAAmC,aAAgB,MAAQ;AAEjE,IAAM,8BAAsD,SAAC,IAIlE;MAHA,cAAA,GAAA,aACA,MAAA,GAAA,KACA,UAAA,GAAA,SACA,gBAAA,GAAA;AAEM,MAAA,KAAgD,IAAI,iBAAlD,oBAAA,GAAA,mBAAmB,yBAAA,GAAA;AAE3B,WAAA,gCAAyC,eAAA;AACvC,QAAM,gBAAgB,cAAc,qBAAqB,aAAA;AACzD,WAAO,CAAC,CAAC,iBAAiB,CAAC,cAAc,aAAA;EAAA;AAG3C,MAAM,yBAAoD,CAAA;AAE1D,MAAM,UAAwC,SAC5C,QACA,OACA,gBAAA;AAlEJ,QAAA;AAoEI,QAAI,uBAAuB,MAAM,MAAA,GAAS;AACxC,UAAM,QAAQ,MAAM,SAAA,EAAW,WAAA;AACvB,UAAA,gBAAkB,OAAO,QAAA;AAEjC,wBACE,gBACA,KAAA,MAAM,QAAQ,aAAA,MAAd,OAAA,SAAA,GAA8B,cAC9B,OACA,MAAM,MAAA;;AAIV,QAAI,IAAI,KAAK,cAAc,MAAM,MAAA,GAAS;AACxC,eAA6BA,MAAA,GAAAC,MAAA,OAAO,QAAQ,sBAAA,GAAfD,MAAAC,IAAA,QAAAD,OAAwC;AAA1D,YAAA,KAAAC,IAAAD,GAAA,GAAC,MAAA,GAAA,CAAA,GAAK,UAAA,GAAA,CAAA;AACf,YAAI;AAAS,uBAAa,OAAA;AAC1B,eAAO,uBAAuB,GAAA;;;AAIlC,QAAI,QAAQ,mBAAmB,MAAA,GAAS;AACtC,UAAM,QAAQ,MAAM,SAAA,EAAW,WAAA;AACvB,UAAA,UAAY,QAAQ,uBAAuB,MAAA,EAAA;AACnD,eAA0C,KAAA,GAAA,KAAA,OAAO,QAAQ,OAAA,GAAf,KAAA,GAAA,QAAA,MAAyB;AAAxD,YAAA,KAAA,GAAA,EAAA,GAAC,gBAAA,GAAA,CAAA,GAAe,aAAA,GAAA,CAAA;AAIzB,0BACE,eACA,cAAA,OAAA,SAAA,WAAY,cACZ,OACA,MAAM,MAAA;;;EAAA;AAMd,WAAA,kBACE,eACA,cACA,MACA,QAAA;AA5GJ,QAAA;AA8GI,QAAM,qBAAqB,QAAQ,oBACjC,YAAA;AAEF,QAAM,qBACJ,KAAA,sBAAA,OAAA,SAAA,mBAAoB,sBAApB,OAAA,KAAyC,OAAO;AAElD,QAAI,sBAAsB,UAAU;AAElC;;AAMF,QAAM,yBAAyB,KAAK,IAClC,GACA,KAAK,IAAI,mBAAmB,gCAAA,CAAA;AAG9B,QAAI,CAAC,gCAAgC,aAAA,GAAgB;AACnD,UAAM,iBAAiB,uBAAuB,aAAA;AAC9C,UAAI,gBAAgB;AAClB,qBAAa,cAAA;;AAEf,6BAAuB,aAAA,IAAiB,WAAW,WAAA;AACjD,YAAI,CAAC,gCAAgC,aAAA,GAAgB;AACnD,eAAI,SAAS,kBAAkB,EAAE,cAAA,CAAA,CAAA;;AAEnC,eAAO,uBAAwB,aAAA;MAAA,GAC9B,yBAAyB,GAAA;;EAAA;AAIhC,SAAO;AAAA;AClIF,IAAM,iCAAyD,SAAC,IAOrE;MANA,cAAA,GAAA,aACA,UAAA,GAAA,SACW,sBAAA,GAAA,QAAA,qBACX,gBAAA,GAAA,eACA,MAAA,GAAA,KACA,gBAAA,GAAA,eACA,eAAA,GAAA;AAEQ,MAAA,oBAAsB,IAAI,gBAAA;AAClC,MAAM,wBAAwB,QAC5B,YAAY,aAAA,GACZ,oBAAoB,aAAA,CAAA;AAGtB,MAAM,UAAwC,SAAC,QAAQ,OAAA;AACrD,QAAI,sBAAsB,MAAA,GAAS;AACjC,qBACE,yBACE,QACA,mBACA,qBACA,aAAA,GAEF,KAAA;;AAIJ,QAAI,IAAI,KAAK,eAAe,MAAM,MAAA,GAAS;AACzC,qBACE,oBACE,OAAO,SACP,QACA,QACA,QACA,QACA,aAAA,GAEF,KAAA;;EAAA;AAKN,WAAA,eACE,MACA,OAAA;AAEA,QAAM,YAAY,MAAM,SAAA;AACxB,QAAM,QAAQ,UAAU,WAAA;AAExB,QAAM,eAAe,IAAI,KAAK,oBAAoB,WAAW,IAAA;AAE7D,YAAQ,MAAM,WAAA;AAjElB,UAAA;AAkEM,UAAM,cAAc,MAAM,KAAK,aAAa,OAAA,CAAA;AAC5C,eAAgCA,MAAA,GAAA,gBAAA,aAAAA,MAAA,cAAA,QAAAA,OAAa;AAAhC,YAAA,gBAAA,cAAAA,GAAA,EAAA;AACX,YAAM,gBAAgB,MAAM,QAAQ,aAAA;AACpC,YAAM,wBAAuB,KAAA,MAAM,cAAc,aAAA,MAApB,OAAA,KAAsC,CAAA;AAEnE,YAAI,eAAe;AACjB,cAAI,OAAO,KAAK,oBAAA,EAAsB,WAAW,GAAG;AAClD,kBAAM,SACJ,kBAAkB;cAChB;aAAA,CAAA;qBAGK,cAAc,WAAW,YAAY,eAAe;AAC7D,kBAAM,SAAS,aAAa,eAAe,aAAA,CAAA;;;;IAAA,CAAA;EAAA;AAOrD,SAAO;AAAA;AC3EF,IAAM,sBAA8C,SAAC,IAK1D;MAJA,cAAA,GAAA,aACA,aAAA,GAAA,YACA,MAAA,GAAA,KACA,eAAA,GAAA,cACA,gBAAA,GAAA;AAEA,MAAM,eAID,CAAA;AAEL,MAAM,UAAwC,SAAC,QAAQ,OAAA;AACrD,QACE,IAAI,gBAAgB,0BAA0B,MAAM,MAAA,KACpD,IAAI,gBAAgB,uBAAuB,MAAM,MAAA,GACjD;AACA,4BAAsB,OAAO,SAAS,KAAA;;AAGxC,QACE,WAAW,QAAQ,MAAM,MAAA,KACxB,WAAW,SAAS,MAAM,MAAA,KAAW,OAAO,KAAK,WAClD;AACA,4BAAsB,OAAO,KAAK,KAAK,KAAA;;AAGzC,QACE,WAAW,UAAU,MAAM,MAAA,KAC1B,WAAW,SAAS,MAAM,MAAA,KAAW,CAAC,OAAO,KAAK,WACnD;AACA,oBAAc,OAAO,KAAK,KAAK,KAAA;;AAGjC,QAAI,IAAI,KAAK,cAAc,MAAM,MAAA,GAAS;AACxC,iBAAA;;EAAA;AAIJ,WAAA,cACEA,KACA,MAAA;QADE,gBAAAA,IAAA;AAGF,QAAM,QAAQ,KAAI,SAAA,EAAW,WAAA;AAC7B,QAAM,gBAAgB,MAAM,QAAQ,aAAA;AACpC,QAAM,gBAAgB,cAAc,qBAAqB,aAAA;AAEzD,QAAI,CAAC,iBAAiB,cAAc,WAAW,YAAY;AACzD;AAEF,QAAM,wBAAwB,0BAA0B,aAAA;AACxD,QAAI,CAAC,OAAO,SAAS,qBAAA;AAAwB;AAE7C,QAAM,cAAc,aAAa,aAAA;AAEjC,QAAI,eAAA,OAAA,SAAA,YAAa,SAAS;AACxB,mBAAa,YAAY,OAAA;AACzB,kBAAY,UAAU;;AAGxB,QAAM,oBAAoB,KAAK,IAAA,IAAQ;AAEvC,QAAM,kBAAgD,aACpD,aAAA,IACE;MACF;MACA,iBAAiB;MACjB,SAAS,WAAW,WAAA;AAClB,wBAAiB,UAAU;AAC3B,aAAI,SAAS,aAAa,eAAe,aAAA,CAAA;MAAA,GACxC,qBAAA;;EAAA;AAIP,WAAA,sBACEA,KACA,MAAA;QADE,gBAAAA,IAAA;AAGF,QAAM,QAAQ,KAAI,SAAA,EAAW,WAAA;AAC7B,QAAM,gBAAgB,MAAM,QAAQ,aAAA;AACpC,QAAM,gBAAgB,cAAc,qBAAqB,aAAA;AAEzD,QAAI,CAAC,iBAAiB,cAAc,WAAW,YAAY,eAAe;AACxE;;AAGF,QAAM,wBAAwB,0BAA0B,aAAA;AAExD,QAAI,CAAC,OAAO,SAAS,qBAAA,GAAwB;AAC3C,wBAAkB,aAAA;AAClB;;AAGF,QAAM,cAAc,aAAa,aAAA;AACjC,QAAM,oBAAoB,KAAK,IAAA,IAAQ;AAEvC,QAAI,CAAC,eAAe,oBAAoB,YAAY,mBAAmB;AACrE,oBAAc,EAAE,cAAA,GAAiB,IAAA;;EAAA;AAIrC,WAAA,kBAA2B,KAAA;AACzB,QAAM,eAAe,aAAa,GAAA;AAClC,QAAI,gBAAA,OAAA,SAAA,aAAc,SAAS;AACzB,mBAAa,aAAa,OAAA;;AAE5B,WAAO,aAAa,GAAA;EAAA;AAGtB,WAAA,aAAA;AACE,aAAkBA,MAAA,GAAA,KAAA,OAAO,KAAK,YAAA,GAAZA,MAAA,GAAA,QAAAA,OAA2B;AAA7C,UAAW,MAAA,GAAAA,GAAA;AACT,wBAAkB,GAAA;;EAAA;AAItB,WAAA,0BAAmC,aAA2B;AAA3B,QAAA,gBAAA,QAAA;AAAA,oBAAA,CAAA;IAA2B;AAC5D,QAAI,wBAAwB,OAAO;AACnC,aAAS,OAAO,aAAa;AAC3B,UAAI,CAAC,CAAC,YAAY,GAAA,EAAK,iBAAiB;AACtC,gCAAwB,KAAK,IAC3B,YAAY,GAAA,EAAK,iBACjB,qBAAA;;;AAKN,WAAO;EAAA;AAET,SAAO;AAAA;ACnIF,IAAM,0BAAkD,SAAC,IAK9D;MAJA,cAAA,GAAA,aACA,UAAA,GAAA,SACA,MAAA,GAAA,KACA,eAAA,GAAA,cACA,gBAAA,GAAA;AAEQ,MAAA,oBAAsB,IAAI,gBAAA;AAElC,MAAM,UAAwC,SAAC,QAAQ,OAAA;AACrD,QAAI,QAAQ,MAAM,MAAA,GAAS;AACzB,0BAAoB,OAAO,gBAAA;;AAE7B,QAAI,SAAS,MAAM,MAAA,GAAS;AAC1B,0BAAoB,OAAO,oBAAA;;EAAA;AAI/B,WAAA,oBACE,MACA,MAAA;AAEA,QAAM,QAAQ,KAAI,SAAA,EAAW,WAAA;AAC7B,QAAM,UAAU,MAAM;AACtB,QAAM,gBAAgB,cAAc;AAEpC,YAAQ,MAAM,WAAA;AACZ,eAA4BA,MAAA,GAAA,KAAA,OAAO,KAAK,aAAA,GAAZA,MAAA,GAAA,QAAAA,OAA4B;AAAxD,YAAW,gBAAA,GAAAA,GAAA;AACT,YAAM,gBAAgB,QAAQ,aAAA;AAC9B,YAAM,uBAAuB,cAAc,aAAA;AAE3C,YAAI,CAAC,wBAAwB,CAAC;AAAe;AAE7C,YAAM,gBACJ,OAAO,OAAO,oBAAA,EAAsB,KAClC,SAAC,KAAA;AAAQ,iBAAA,IAAI,IAAA,MAAU;QAAd,CAAc,KAExB,OAAO,OAAO,oBAAA,EAAsB,MACnC,SAAC,KAAA;AAAQ,iBAAA,IAAI,IAAA,MAAU;QAAd,CAAc,KAEvB,MAAM,OAAO,IAAA;AAEjB,YAAI,eAAe;AACjB,cAAI,OAAO,KAAK,oBAAA,EAAsB,WAAW,GAAG;AAClD,iBAAI,SACF,kBAAkB;cAChB;aAAA,CAAA;qBAGK,cAAc,WAAW,YAAY,eAAe;AAC7D,iBAAI,SAAS,aAAa,eAAe,aAAA,CAAA;;;;IAAA,CAAA;EAAA;AAOnD,SAAO;AAAA;AC2GT,IAAM,qBAAqB,IAAI,MAC7B,kDAAA;AAKK,IAAM,6BAAqD,SAAC,IAMjE;MALA,MAAA,GAAA,KACA,cAAA,GAAA,aACA,UAAA,GAAA,SACA,aAAA,GAAA,YACA,gBAAA,GAAA,eACA,gBAAA,GAAA;AAEA,MAAM,eAAe,mBAAmB,UAAA;AACxC,MAAM,kBAAkB,mBAAmB,aAAA;AAC3C,MAAM,mBAAmB,YAAY,YAAY,aAAA;AAMjD,MAAM,eAA+C,CAAA;AAErD,MAAM,UAAwC,SAC5C,QACA,OACA,aAAA;AAEA,QAAM,WAAW,YAAY,MAAA;AAE7B,QAAI,WAAW,QAAQ,MAAM,MAAA,GAAS;AACpC,UAAM,WAAW,YAAY,WAAA,EAAa,QAAQ,QAAA;AAClD,UAAM,QAAQ,MAAM,SAAA,EAAW,WAAA,EAAa,QAAQ,QAAA;AACpD,UAAI,CAAC,YAAY,OAAO;AACtB,qBACE,OAAO,KAAK,IAAI,cAChB,OAAO,KAAK,IAAI,cAChB,UACA,OACA,OAAO,KAAK,SAAA;;eAGP,cAAc,QAAQ,MAAM,MAAA,GAAS;AAC9C,UAAM,QAAQ,MAAM,SAAA,EAAW,WAAA,EAAa,UAAU,QAAA;AACtD,UAAI,OAAO;AACT,qBACE,OAAO,KAAK,IAAI,cAChB,OAAO,KAAK,IAAI,cAChB,UACA,OACA,OAAO,KAAK,SAAA;;eAGP,iBAAiB,MAAA,GAAS;AACnC,UAAM,YAAY,aAAa,QAAA;AAC/B,UAAI,aAAA,OAAA,SAAA,UAAW,eAAe;AAC5B,kBAAU,cAAc;UACtB,MAAM,OAAO;UACb,MAAM,OAAO,KAAK;SAAA;AAEpB,eAAO,UAAU;;eAGnB,IAAI,gBAAgB,kBAAkB,MAAM,MAAA,KAC5C,IAAI,gBAAgB,qBAAqB,MAAM,MAAA,GAC/C;AACA,UAAM,YAAY,aAAa,QAAA;AAC/B,UAAI,WAAW;AACb,eAAO,aAAa,QAAA;AACpB,kBAAU,kBAAA;;eAEH,IAAI,KAAK,cAAc,MAAM,MAAA,GAAS;AAC/C,eAAoCA,MAAA,GAAA,KAAA,OAAO,QAAQ,YAAA,GAAfA,MAAA,GAAA,QAAAA,OAA8B;AAAvD,YAAA,KAAA,GAAAA,GAAA,GAAC,YAAA,GAAA,CAAA,GAAU,YAAA,GAAA,CAAA;AACpB,eAAO,aAAa,SAAA;AACpB,kBAAU,kBAAA;;;EAAA;AAKhB,WAAA,YAAqB,QAAA;AACnB,QAAI,aAAa,MAAA;AAAS,aAAO,OAAO,KAAK,IAAI;AACjD,QAAI,gBAAgB,MAAA;AAAS,aAAO,OAAO,KAAK;AAChD,QAAI,IAAI,gBAAgB,kBAAkB,MAAM,MAAA;AAC9C,aAAO,OAAO,QAAQ;AACxB,QAAI,IAAI,gBAAgB,qBAAqB,MAAM,MAAA;AACjD,aAAO,oBAAoB,OAAO,OAAA;AACpC,WAAO;EAAA;AAGT,WAAA,aACE,cACA,cACA,eACA,OACA,WAAA;AAEA,QAAM,qBAAqB,QAAQ,oBAAoB,YAAA;AACvD,QAAM,oBAAoB,sBAAA,OAAA,SAAA,mBAAoB;AAC9C,QAAI,CAAC;AAAmB;AAExB,QAAI,YAAY,CAAA;AAEhB,QAAM,oBAAoB,IAAI,QAAc,SAAC,SAAA;AAC3C,gBAAU,oBAAoB;IAAA,CAAA;AAEhC,QAAM,kBAGF,QAAQ,KAAK;MACf,IAAI,QAA0C,SAAC,SAAA;AAC7C,kBAAU,gBAAgB;MAAA,CAAA;MAE5B,kBAAkB,KAAK,WAAA;AACrB,cAAM;MAAA,CAAA;KAAA;AAKV,oBAAgB,MAAM,WAAA;IAAM,CAAA;AAC5B,iBAAa,aAAA,IAAiB;AAC9B,QAAM,WAAY,IAAI,UAAU,YAAA,EAAsB,OACpD,mBAAmB,SAAS,eAAe,QACvC,eACA,aAAA;AAGN,QAAM,QAAQ,MAAM,SAAS,SAAC,GAAG,IAAI,QAAA;AAAU,aAAA;IAAA,CAAA;AAC/C,QAAM,eAAe,cAAA,eAAA,CAAA,GAChB,KAAA,GADgB;MAEnB,eAAe,WAAA;AAAM,eAAA,SAAS,MAAM,SAAA,CAAA;MAAf;MACrB;MACA;MACA,kBAAmB,mBAAmB,SAAS,eAAe,QAC1D,SAAC,cAAA;AACC,eAAA,MAAM,SACJ,IAAI,KAAK,gBACP,cACA,cACA,YAAA,CAAA;MAJJ,IAOF;MAEJ;MACA;KAAA;AAGF,QAAM,iBAAiB,kBAAkB,cAAc,YAAA;AAEvD,YAAQ,QAAQ,cAAA,EAAgB,MAAM,SAACK,IAAA;AACrC,UAAIA,OAAM;AAAoB;AAC9B,YAAMA;IAAA,CAAA;EAAA;AAIV,SAAO;AAAA;AC9HF,IAAM,6BAAqD,SAAC,IAIjE;MAHA,MAAA,GAAA,KACA,UAAA,GAAA,SACA,aAAA,GAAA,YACA,gBAAA,GAAA;AAEA,MAAM,iBAAiB,UAAU,YAAY,aAAA;AAC7C,MAAM,kBAAkB,WAAW,YAAY,aAAA;AAC/C,MAAM,oBAAoB,YAAY,YAAY,aAAA;AAMlD,MAAM,eAA+C,CAAA;AAErD,MAAM,UAAwC,SAAC,QAAQ,OAAA;AA3NzD,QAAA,IAAA,IAAA;AA4NI,QAAI,eAAe,MAAA,GAAS;AACpB,UAAAL,MAGF,OAAO,MAFT,YAAAA,IAAA,WACA,KAAAA,IAAA,KAAO,iBAAA,GAAA,cAAc,iBAAA,GAAA;AAEvB,UAAM,qBAAqB,QAAQ,oBAAoB,cAAA;AACvD,UAAM,iBAAiB,sBAAA,OAAA,SAAA,mBAAoB;AAC3C,UAAI,gBAAgB;AAClB,YAAM,cAAY,CAAA;AAClB,YAAM,iBACJ,IAAK,QAGH,SAAC,SAAS,QAAA;AACV,sBAAU,UAAU;AACpB,sBAAU,SAAS;QAAA,CAAA;AAIvB,uBAAe,MAAM,WAAA;QAAM,CAAA;AAC3B,qBAAa,SAAA,IAAa;AAC1B,YAAM,aAAY,IAAI,UAAU,cAAA,EAAsB,OACpD,mBAAmB,SAAS,eAAe,QACvC,iBACA,SAAA;AAGN,YAAM,QAAQ,MAAM,SAAS,SAAC,GAAG,IAAI,QAAA;AAAU,iBAAA;QAAA,CAAA;AAC/C,YAAM,eAAe,cAAA,eAAA,CAAA,GAChB,KAAA,GADgB;UAEnB,eAAe,WAAA;AAAM,mBAAA,WAAS,MAAM,SAAA,CAAA;UAAf;UACrB;UACA;UACA,kBAAmB,mBAAmB,SAAS,eAAe,QAC1D,SAAC,cAAA;AACC,mBAAA,MAAM,SACJ,IAAI,KAAK,gBACP,gBACA,gBACA,YAAA,CAAA;UAJJ,IAOF;UACJ;SAAA;AAEF,uBAAe,gBAAc,YAAA;;eAEtB,kBAAkB,MAAA,GAAS;AAC9B,UAAA,KAA+B,OAAO,MAApC,YAAA,GAAA,WAAW,gBAAA,GAAA;AACnB,OAAA,KAAA,aAAa,SAAA,MAAb,OAAA,SAAA,GAAyB,QAAQ;QAC/B,MAAM,OAAO;QACb,MAAM;OAAA;AAER,aAAO,aAAa,SAAA;eACX,gBAAgB,MAAA,GAAS;AAC5B,UAAA,KAAkD,OAAO,MAAvD,YAAA,GAAA,WAAW,oBAAA,GAAA,mBAAmB,gBAAA,GAAA;AACtC,OAAA,KAAA,aAAa,SAAA,MAAb,OAAA,SAAA,GAAyB,OAAO;QAC9B,QAAO,KAAA,OAAO,YAAP,OAAA,KAAkB,OAAO;QAChC,kBAAkB,CAAC;QACnB,MAAM;OAAA;AAER,aAAO,aAAa,SAAA;;EAAA;AAIxB,SAAO;AAAA;AC3RF,IAAM,uBAA+C,SAAC,IAG3D;MAFA,MAAA,GAAA,KACW,SAAA,GAAA,QAAA,QACX,cAAA,GAAA;AAEA,SAAO,SAAC,QAAQ,OAAA;AAPlB,QAAA,IAAA;AAQI,QAAI,IAAI,KAAK,cAAc,MAAM,MAAA,GAAS;AAExC,YAAM,SAAS,IAAI,gBAAgB,qBAAqB,MAAA,CAAA;;AAG1D,QACE,OAAO,YAAY,eACnB,MACA;AACA,UACE,IAAI,gBAAgB,qBAAqB,MAAM,MAAA,KAC/C,OAAO,YAAY,YACnB,MAAA,KAAA,MAAM,SAAA,EAAW,WAAA,MAAjB,OAAA,SAAA,GAA+B,WAA/B,OAAA,SAAA,GAAuC,0BACrC,YACF;AACA,gBAAQ,KAAK,2EAAyE,cAAA,sGAEpF,gBAAgB,QACZ,oGAEA,GAAA;;;EAAA;AAAA;AChBd,IAAI;AACJ,IAAM,qBACJ,OAAO,mBAAmB,aACtB,eAAe,KACb,OAAO,WAAW,cACd,SACA,OAAO,WAAW,cAClB,SACA,UAAA,IAGN,SAAC,IAAA;AACE,UAAA,YAAY,UAAU,QAAQ,QAAA,IAAY,KAAK,EAAA,EAAI,MAAM,SAAC,KAAA;AACzD,WAAA,WAAW,WAAA;AACT,YAAM;IAAA,GACL,CAAA;EAFH,CAEG;AAHJ;AAMF,IAAM,6BAET,SAAC,IAAmB;MAAjB,MAAA,GAAA,KAAK,aAAA,GAAA,YAAY,gBAAA,GAAA;AACtB,MAAM,sBAAyB,IAAI,cAAA;AAEnC,MAAI,wBACF;AAEF,MAAI,iBAAiB;AAEf,MAAA,KACJ,IAAI,iBADE,4BAAA,GAAA,2BAA2B,yBAAA,GAAA;AAKnC,MAAM,8BAA8B,SAClC,cACA,QAAA;AA/CJ,QAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;AAiDI,QAAI,0BAA0B,MAAM,MAAA,GAAS;AACrC,UAAAA,MAAwC,OAAO,SAA7C,gBAAAA,IAAA,eAAe,YAAAA,IAAA,WAAW,UAAAA,IAAA;AAElC,WAAI,KAAA,gBAAA,OAAA,SAAA,aAAe,aAAA,MAAf,OAAA,SAAA,GAAgC,SAAA,GAAY;AAC9C,qBAAa,aAAA,EAAgB,SAAA,IAAa;;AAE5C,aAAO;;AAET,QAAI,uBAAuB,MAAM,MAAA,GAAS;AAClC,UAAAC,MAA+B,OAAO,SAApC,gBAAAA,IAAA,eAAe,YAAAA,IAAA;AACvB,UAAI,aAAa,aAAA,GAAgB;AAC/B,eAAO,aAAa,aAAA,EAAgB,SAAA;;AAEtC,aAAO;;AAET,QAAI,IAAI,gBAAgB,kBAAkB,MAAM,MAAA,GAAS;AACvD,aAAO,aAAa,OAAO,QAAQ,aAAA;AACnC,aAAO;;AAET,QAAI,WAAW,QAAQ,MAAM,MAAA,GAAS;AAElC,UAAA,KACE,OAAA,MADM,MAAA,GAAA,KAAK,YAAA,GAAA;AAEf,UAAI,IAAI,WAAW;AACjB,YAAM,YAAY,KAAA,aAAA,KAAa,IAAI,aAAA,MAAjB,OAAA,KAAA,aAAA,EAAA,IAAoC,CAAA;AACtD,iBAAS,SAAA,KACP,MAAA,KAAA,IAAI,wBAAJ,OAAA,KAA2B,SAAS,SAAA,MAApC,OAAA,KAAkD,CAAA;AAEpD,eAAO;;;AAGX,QAAI,WAAW,SAAS,MAAM,MAAA,GAAS;AAEnC,UAAA,KACE,OAAA,MADM,YAAA,GAAA,WAAW,MAAA,GAAA,KAAK,YAAA,GAAA;AAE1B,UAAI,aAAa,IAAI,WAAW;AAC9B,YAAM,YAAY,KAAA,aAAA,KAAa,IAAI,aAAA,MAAjB,OAAA,KAAA,aAAA,EAAA,IAAoC,CAAA;AACtD,iBAAS,SAAA,KACP,MAAA,KAAA,IAAI,wBAAJ,OAAA,KAA2B,SAAS,SAAA,MAApC,OAAA,KAAkD,CAAA;AAEpD,eAAO;;;AAIX,WAAO;EAAA;AAGT,SAAO,SAAC,QAAQ,OAAA;AAhGlB,QAAA,IAAA;AAiGI,QAAI,CAAC,uBAAuB;AAE1B,8BAAwB,KAAK,MAC3B,KAAK,UAAU,cAAc,oBAAA,CAAA;;AAIjC,QAAI,IAAI,KAAK,cAAc,MAAM,MAAA,GAAS;AACxC,8BAAwB,cAAc,uBAAuB,CAAA;AAC7D,aAAO,CAAC,MAAM,KAAA;;AAKhB,QAAI,IAAI,gBAAgB,2BAA2B,MAAM,MAAA,GAAS;AAC1D,UAAAD,MAA+B,OAAO,SAApC,gBAAAA,IAAA,eAAe,YAAAA,IAAA;AACvB,UAAM,kBACJ,CAAC,GAAC,KAAA,cAAc,qBAAqB,aAAA,MAAnC,OAAA,SAAA,GAAoD,SAAA;AACxD,aAAO,CAAC,OAAO,eAAA;;AAIjB,QAAM,YAAY,4BAChB,cAAc,sBACd,MAAA;AAGF,QAAI,WAAW;AACb,UAAI,CAAC,gBAAgB;AACnB,2BAAmB,WAAA;AAEjB,cAAM,mBAAsC,KAAK,MAC/C,KAAK,UAAU,cAAc,oBAAA,CAAA;AAGzB,cAAAA,MAAc,GAClB,uBACA,WAAA;AAAM,mBAAA;UAAA,CAAA,GAFC,UAAAA,IAAA,CAAA;AAMT,gBAAM,KAAK,IAAI,gBAAgB,qBAAqB,OAAA,CAAA;AAEpD,kCAAwB;AACxB,2BAAiB;QAAA,CAAA;AAEnB,yBAAiB;;AAGnB,UAAM,4BACJ,CAAC,GAAC,KAAA,OAAO,SAAP,OAAA,SAAA,GAAa,WAAW,mBAAA;AAC5B,UAAM,iCACJ,WAAW,SAAS,MAAM,MAAA,KAC1B,OAAO,KAAK,aACZ,CAAC,CAAC,OAAO,KAAK,IAAI;AAEpB,UAAM,uBACJ,CAAC,6BAA6B,CAAC;AAEjC,aAAO,CAAC,sBAAsB,KAAA;;AAGhC,WAAO,CAAC,MAAM,KAAA;EAAA;AAAA;ARxIX,SAAA,gBAIL,OAAA;AACQ,MAAA,cAA0C,MAAA,aAA7B,aAA6B,MAAA,YAAjB,MAAiB,MAAA,KAAZ,UAAY,MAAA;AAC1C,MAAA,SAAW,QAAA;AAEnB,MAAM,UAAU;IACd,gBAAgB,aAEX,cAAA,iBAAA;;AAGP,MAAM,uBAAuB,SAAC,QAAA;AAC5B,WACE,CAAC,CAAC,UACF,OAAO,OAAO,SAAS,YACvB,OAAO,KAAK,WAAc,cAAA,GAAA;EAAA;AAI9B,MAAM,kBAA4C;IAChD;IACA;IACA;IACA;IACA;IACA;;AAGF,MAAM,aAIF,SAAC,OAAA;AACH,QAAI,eAAc;AAElB,QAAI,gBAAyC;MAC3C,sBAAsB,CAAA;;AAGxB,QAAM,cAAc,cAAA,eAAA,CAAA,GACd,KAAA,GADc;MAMlB;MACA;KAAA;AAGF,QAAM,WAAW,gBAAgB,IAAI,SAAC,OAAA;AAAU,aAAA,MAAM,WAAA;IAAN,CAAM;AAEtD,QAAM,wBAAwB,2BAA2B,WAAA;AACzD,QAAM,sBAAsB,wBAAwB,WAAA;AAEpD,WAAO,SAAC,MAAA;AACN,aAAO,SAAC,QAAA;AACN,YAAI,CAAC,cAAa;AAChB,yBAAc;AAEd,gBAAM,SAAS,IAAI,gBAAgB,qBAAqB,MAAA,CAAA;;AAG1D,YAAM,gBAAgB,cAAA,eAAA,CAAA,GAAK,KAAA,GAAL,EAAY,KAAA,CAAA;AAElC,YAAM,cAAc,MAAM,SAAA;AAEpB,YAAA,KAA0C,sBAC9C,QACA,eACA,WAAA,GAHK,uBAAA,GAAA,CAAA,GAAsB,kBAAA,GAAA,CAAA;AAM7B,YAAI;AAEJ,YAAI,sBAAsB;AACxB,gBAAM,KAAK,MAAA;eACN;AACL,gBAAM;;AAGR,YAAI,CAAC,CAAC,MAAM,SAAA,EAAW,WAAA,GAAc;AAInC,8BAAoB,QAAQ,eAAe,WAAA;AAE3C,cACE,qBAAqB,MAAA,KACrB,QAAQ,mBAAmB,MAAA,GAC3B;AAGA,qBAAoB,KAAA,GAAA,aAAA,UAAA,KAAA,WAAA,QAAA,MAAU;AAA9B,kBAAS,UAAA,WAAA,EAAA;AACP,sBAAQ,QAAQ,eAAe,WAAA;;;;AAKrC,eAAO;MAAA;IAAA;EAAA;AAKb,SAAO,EAAE,YAAY,QAAA;AAErB,WAAA,aACE,eAIA,eACA,UAAmC;AAAnC,QAAA,aAAA,QAAA;AAAA,iBAAA,CAAA;IAAmC;AAEnC,WAAO,WAAW,eAAA;MAChB,MAAM;MACN,cAAc,cAAc;MAC5B,cAAc,cAAc;MAC5B,WAAW;MACX,cAAc;MACd;OACG,QAAA,CAAA;EAAA;AAAA;AS9IF,SAAA,WAAuB,GAAA;AAAwB;AAE/C,SAAA,WACL,QAAA;AAAA,MAAA,OAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MACG;AADH,SAAA,KAAA,CAAA,IAAA,UAAA,EAAA;;AAGA,SAAO,OAAA,MAAP,QAAA,cAAA,CAAc,MAAA,GAAW,IAAA,CAAA;AAAA;ACwDpB,IAAM,iBAAiC,OAAA;AA0YvC,IAAM,aAAa,WAAA;AAA2B,SAAA;IACnD,MAAM;IACN,MAAA,SACE,KACA,IAUA,SAAA;UATE,YAAA,GAAA,WACA,WAAA,GAAA,UACA,cAAA,GAAA,aACA,qBAAA,GAAA,oBACA,oBAAA,GAAA,mBACA,4BAAA,GAAA,2BACA,iBAAA,GAAA,gBACA,qBAAA,GAAA;AAIF,QAAA;AAEA,iBAAuC,kBAAA;AAEvC,UAAM,gBAAgC,SAAC,KAAA;AACrC,YACE,OAAO,YAAY,eACnB,MACA;AACA,cAAI,CAAC,SAAS,SAAS,IAAI,IAAA,GAAc;AACvC,oBAAQ,MACN,eAAa,IAAI,OAAA,8CAAA;;;AAIvB,eAAO;MAAA;AAGT,aAAO,OAAO,KAAK;QACjB;QACA,WAAW,CAAA;QACX,iBAAiB;UACf;UACA;UACA;UACA;;QAEF,MAAM,CAAA;OAAA;AAGF,UAAA,KAQF,YAAY;QACd;QACA;QACA;QACA;QACA;QACA;OAAA,GAbA,aAAA,GAAA,YACA,gBAAA,GAAA,eACA,iBAAA,GAAA,gBACA,kBAAA,GAAA,iBACA,kBAAA,GAAA,iBACA,WAAA,GAAA,UACA,yBAAA,GAAA;AAUI,UAAA,KAAqC,WAAW;QACpD;QACA;QACA;QACA;QACA;QACA,QAAQ;UACN;UACA;UACA;UACA;UACA;;OAAA,GAXI,UAAA,GAAA,SAAkB,eAAA,GAAA;AAe1B,iBAAW,IAAI,MAAM;QACnB;QACA;QACA;QACA;QACA,eAAe,aAAa;OAAA;AAE9B,iBAAW,IAAI,iBAAiB,YAAA;AAE1B,UAAA,KAA6C,gBAAgB;QACjE;QACA;QACA;QACA;QACA;QACA;OAAA,GANM,aAAA,GAAA,YAAqB,oBAAA,GAAA;AAQ7B,iBAAW,IAAI,MAAM,iBAAA;AAErB,iBAAW,KAAK,EAAE,SAAyB,WAAA,CAAA;AAErC,UAAA,KACJ,eAAe;QACb;QACA;OAAA,GAHI,qBAAA,GAAA,oBAAoB,wBAAA,GAAA,uBAAuB,sBAAA,GAAA;AAMnD,iBAAW,IAAI,MAAM,EAAE,oBAAA,CAAA;AAEjB,UAAA,KASF,cAAc;QAChB;QACA;QACA;QACA;QACA;OAAA,GAbA,qBAAA,GAAA,oBACA,wBAAA,GAAA,uBACA,0BAAA,GAAA,yBACA,2BAAA,GAAA,0BACA,yBAAA,GAAA,wBACA,uBAAA,GAAA,sBACA,8BAAA,GAAA,6BACA,iBAAA,GAAA;AASF,iBAAW,IAAI,MAAM;QACnB;QACA,4BAA4B;QAC5B;QACA;QACA;QACA;OAAA;AAGF,aAAO;QACL,MAAM;QACN,gBAAA,SAAe,cAAc,YAAA;AAnlBnC,cAAA,IAAA;AAolBQ,cAAM,SAAS;AAOf,WAAA,MAAA,KAAA,OAAO,WAAP,YAAA,MAAA,OAAA,KAAA,GAAA,YAAA,IAAmC,CAAA;AACnC,cAAI,kBAAkB,UAAA,GAAa;AACjC,uBACE,OAAO,UAAU,YAAA,GACjB;cACE,MAAM;cACN,QAAQ,mBAAmB,cAAc,UAAA;cACzC,UAAU,mBAAmB,cAAc,UAAA;eAE7C,uBAAuB,YAAY,YAAA,CAAA;qBAE5B,qBAAqB,UAAA,GAAa;AAC3C,uBACE,OAAO,UAAU,YAAA,GACjB;cACE,MAAM;cACN,QAAQ,sBAAA;cACR,UAAU,sBAAsB,YAAA;eAElC,uBAAuB,eAAe,YAAA,CAAA;;QAAA;;IAAA;;AAlKG;ACzcrD,IAAM,YAA4B,eAAe,WAAA,CAAA;", "names": ["isPlainObject", "_j", "_k", "_l", "_m", "r", "e", "HandledError", "t"]}