{"version": 3, "sources": ["../../scroll/index.js", "../../scrollparent/scrollparent.js", "../../react-innertext/index.js", "../../deepmerge/dist/cjs.js", "../../prop-types/node_modules/react-is/cjs/react-is.development.js", "../../prop-types/node_modules/react-is/index.js", "../../object-assign/index.js", "../../prop-types/lib/ReactPropTypesSecret.js", "../../prop-types/lib/has.js", "../../prop-types/checkPropTypes.js", "../../prop-types/factoryWithTypeCheckers.js", "../../prop-types/index.js", "../../react-joyride/src/literals/index.ts", "../../react-joyride/src/components/index.tsx", "../../react-joyride/src/modules/dom.ts", "../../react-joyride/src/modules/helpers.tsx", "../../react-joyride/src/modules/step.ts", "../../react-joyride/src/defaults.ts", "../../react-joyride/src/styles.ts", "../../react-joyride/src/modules/store.ts", "../../react-joyride/src/components/Overlay.tsx", "../../react-joyride/src/components/Spotlight.tsx", "../../react-joyride/src/components/Portal.tsx", "../../react-joyride/src/components/Step.tsx", "../../react-joyride/src/modules/scope.ts", "../../react-joyride/src/components/Beacon.tsx", "../../react-joyride/src/components/Tooltip/index.tsx", "../../react-joyride/src/components/Tooltip/Container.tsx", "../../react-joyride/src/components/Tooltip/CloseButton.tsx", "../../@gilbarbara/deep-equal/src/helpers.ts", "../../@gilbarbara/deep-equal/src/index.ts", "../../is-lite/src/helpers.ts", "../../is-lite/src/index.ts", "../../tree-changes/src/index.ts", "../../tree-changes/src/helpers.ts", "../../react-floater/es/index.js", "../../popper.js/src/utils/isBrowser.js", "../../popper.js/src/utils/debounce.js", "../../popper.js/src/utils/isFunction.js", "../../popper.js/src/utils/getStyleComputedProperty.js", "../../popper.js/src/utils/getParentNode.js", "../../popper.js/src/utils/getScrollParent.js", "../../popper.js/src/utils/getReferenceNode.js", "../../popper.js/src/utils/isIE.js", "../../popper.js/src/utils/getOffsetParent.js", "../../popper.js/src/utils/isOffsetContainer.js", "../../popper.js/src/utils/getRoot.js", "../../popper.js/src/utils/findCommonOffsetParent.js", "../../popper.js/src/utils/getScroll.js", "../../popper.js/src/utils/includeScroll.js", "../../popper.js/src/utils/getBordersSize.js", "../../popper.js/src/utils/getWindowSizes.js", "../../popper.js/src/utils/getClientRect.js", "../../popper.js/src/utils/getBoundingClientRect.js", "../../popper.js/src/utils/getOffsetRectRelativeToArbitraryNode.js", "../../popper.js/src/utils/getViewportOffsetRectRelativeToArtbitraryNode.js", "../../popper.js/src/utils/isFixed.js", "../../popper.js/src/utils/getFixedPositionOffsetParent.js", "../../popper.js/src/utils/getBoundaries.js", "../../popper.js/src/utils/computeAutoPlacement.js", "../../popper.js/src/utils/getReferenceOffsets.js", "../../popper.js/src/utils/getOuterSizes.js", "../../popper.js/src/utils/getOppositePlacement.js", "../../popper.js/src/utils/getPopperOffsets.js", "../../popper.js/src/utils/find.js", "../../popper.js/src/utils/findIndex.js", "../../popper.js/src/utils/runModifiers.js", "../../popper.js/src/methods/update.js", "../../popper.js/src/utils/isModifierEnabled.js", "../../popper.js/src/utils/getSupportedPropertyName.js", "../../popper.js/src/methods/destroy.js", "../../popper.js/src/utils/getWindow.js", "../../popper.js/src/utils/setupEventListeners.js", "../../popper.js/src/methods/enableEventListeners.js", "../../popper.js/src/utils/removeEventListeners.js", "../../popper.js/src/methods/disableEventListeners.js", "../../popper.js/src/utils/isNumeric.js", "../../popper.js/src/utils/setStyles.js", "../../popper.js/src/utils/setAttributes.js", "../../popper.js/src/modifiers/applyStyle.js", "../../popper.js/src/utils/getRoundedOffsets.js", "../../popper.js/src/modifiers/computeStyle.js", "../../popper.js/src/utils/isModifierRequired.js", "../../popper.js/src/modifiers/arrow.js", "../../popper.js/src/utils/getOppositeVariation.js", "../../popper.js/src/methods/placements.js", "../../popper.js/src/utils/clockwise.js", "../../popper.js/src/modifiers/flip.js", "../../popper.js/src/modifiers/keepTogether.js", "../../popper.js/src/modifiers/offset.js", "../../popper.js/src/modifiers/preventOverflow.js", "../../popper.js/src/modifiers/shift.js", "../../popper.js/src/modifiers/hide.js", "../../popper.js/src/modifiers/inner.js", "../../popper.js/src/modifiers/index.js", "../../popper.js/src/methods/defaults.js", "../../popper.js/src/index.js", "../../react-floater/node_modules/is-lite/src/index.ts", "../../react-floater/node_modules/@gilbarbara/deep-equal/src/helpers.ts", "../../react-floater/node_modules/@gilbarbara/deep-equal/src/index.ts", "../../react-floater/node_modules/tree-changes/src/helpers.ts", "../../react-floater/node_modules/tree-changes/src/index.ts"], "sourcesContent": ["var E_NOSCROLL = new Error('Element already at target scroll position')\nvar E_CANCELLED = new Error('Scroll cancelled')\nvar min = Math.min\nvar ms = Date.now\n\nmodule.exports = {\n  left: make('scrollLeft'),\n  top: make('scrollTop')\n}\n\nfunction make (prop) {\n  return function scroll (el, to, opts, cb) {\n    opts = opts || {}\n\n    if (typeof opts == 'function') cb = opts, opts = {}\n    if (typeof cb != 'function') cb = noop\n\n    var start = ms()\n    var from = el[prop]\n    var ease = opts.ease || inOutSine\n    var duration = !isNaN(opts.duration) ? +opts.duration : 350\n    var cancelled = false\n\n    return from === to ?\n      cb(E_NOSCROLL, el[prop]) :\n      requestAnimationFrame(animate), cancel\n\n    function cancel () {\n      cancelled = true\n    }\n\n    function animate (timestamp) {\n      if (cancelled) return cb(E_CANCELLED, el[prop])\n\n      var now = ms()\n      var time = min(1, ((now - start) / duration))\n      var eased = ease(time)\n\n      el[prop] = (eased * (to - from)) + from\n\n      time < 1 ?\n        requestAnimationFrame(animate) :\n        requestAnimationFrame(function () {\n          cb(null, el[prop])\n        })\n    }\n  }\n}\n\nfunction inOutSine (n) {\n  return 0.5 * (1 - Math.cos(Math.PI * n))\n}\n\nfunction noop () {}\n", "(function (root, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define([], factory);\n  } else if (typeof module === \"object\" && module.exports) {\n    module.exports = factory();\n  } else {\n    root.Scrollparent = factory();\n  }\n}(this, function () {\n  function isScrolling(node) {\n    var overflow = getComputedStyle(node, null).getPropertyValue(\"overflow\");\n\n    return overflow.indexOf(\"scroll\") > -1 || overflow.indexOf(\"auto\") > - 1;\n  }\n\n  function scrollParent(node) {\n    if (!(node instanceof HTMLElement || node instanceof SVGElement)) {\n      return undefined;\n    }\n\n    var current = node.parentNode;\n    while (current.parentNode) {\n      if (isScrolling(current)) {\n        return current;\n      }\n\n      current = current.parentNode;\n    }\n\n    return document.scrollingElement || document.documentElement;\n  }\n\n  return scrollParent;\n}));", "\"use strict\";\nvar hasProps = function (jsx) {\n    return Object.prototype.hasOwnProperty.call(jsx, 'props');\n};\nvar reduceJsxToString = function (previous, current) {\n    return previous + innerText(current);\n};\nvar innerText = function (jsx) {\n    if (jsx === null ||\n        typeof jsx === 'boolean' ||\n        typeof jsx === 'undefined') {\n        return '';\n    }\n    if (typeof jsx === 'number') {\n        return jsx.toString();\n    }\n    if (typeof jsx === 'string') {\n        return jsx;\n    }\n    if (Array.isArray(jsx)) {\n        return jsx.reduce(reduceJsxToString, '');\n    }\n    if (hasProps(jsx) &&\n        Object.prototype.hasOwnProperty.call(jsx.props, 'children')) {\n        return innerText(jsx.props.children);\n    }\n    return '';\n};\ninnerText.default = innerText;\nmodule.exports = innerText;\n", "'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction getMergeFunction(key, options) {\n\tif (!options.customMerge) {\n\t\treturn deepmerge\n\t}\n\tvar customMerge = options.customMerge(key);\n\treturn typeof customMerge === 'function' ? customMerge : deepmerge\n}\n\nfunction getEnumerableOwnPropertySymbols(target) {\n\treturn Object.getOwnPropertySymbols\n\t\t? Object.getOwnPropertySymbols(target).filter(function(symbol) {\n\t\t\treturn Object.propertyIsEnumerable.call(target, symbol)\n\t\t})\n\t\t: []\n}\n\nfunction getKeys(target) {\n\treturn Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))\n}\n\nfunction propertyIsOnObject(object, property) {\n\ttry {\n\t\treturn property in object\n\t} catch(_) {\n\t\treturn false\n\t}\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n\treturn propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n\t\t&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n\t\t\t&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tgetKeys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tgetKeys(source).forEach(function(key) {\n\t\tif (propertyIsUnsafe(target, key)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n\t\t\tdestination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\t// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n\t// implementations can use it. The caller may not replace it.\n\toptions.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n", "/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "export const ACTIONS = {\n  INIT: 'init',\n  START: 'start',\n  STOP: 'stop',\n  RESET: 'reset',\n  PREV: 'prev',\n  NEXT: 'next',\n  GO: 'go',\n  CLOSE: 'close',\n  SKIP: 'skip',\n  UPDATE: 'update',\n} as const;\n\nexport const EVENTS = {\n  TOUR_START: 'tour:start',\n  STEP_BEFORE: 'step:before',\n  BEACON: 'beacon',\n  TOOLTIP: 'tooltip',\n  STEP_AFTER: 'step:after',\n  TOUR_END: 'tour:end',\n  TOUR_STATUS: 'tour:status',\n  TARGET_NOT_FOUND: 'error:target_not_found',\n  ERROR: 'error',\n} as const;\n\nexport const LIFECYCLE = {\n  INIT: 'init',\n  READY: 'ready',\n  BEACON: 'beacon',\n  TOOLTIP: 'tooltip',\n  COMPLETE: 'complete',\n  ERROR: 'error',\n} as const;\n\nexport const ORIGIN = {\n  BUTTON_CLOSE: 'button_close',\n  BUTTON_PRIMARY: 'button_primary',\n  KEYBOARD: 'keyboard',\n  OVERLAY: 'overlay',\n} as const;\n\nexport const STATUS = {\n  IDLE: 'idle',\n  READY: 'ready',\n  WAITING: 'waiting',\n  RUNNING: 'running',\n  PAUSED: 'paused',\n  SKIPPED: 'skipped',\n  FINISHED: 'finished',\n  ERROR: 'error',\n} as const;\n", "import * as React from 'react';\nimport { ReactNode } from 'react';\nimport isEqual from '@gilbarbara/deep-equal';\nimport is from 'is-lite';\nimport treeChanges from 'tree-changes';\n\nimport {\n  canUseDOM,\n  getElement,\n  getScrollParent,\n  getScrollTo,\n  hasCustomScrollParent,\n  scrollTo,\n} from '~/modules/dom';\nimport { log, shouldScroll } from '~/modules/helpers';\nimport { getMergedStep, validateSteps } from '~/modules/step';\nimport createStore from '~/modules/store';\n\nimport { ACTIONS, EVENTS, LIFECYCLE, STATUS } from '~/literals';\n\nimport Overlay from '~/components/Overlay';\nimport Portal from '~/components/Portal';\n\nimport { defaultProps } from '~/defaults';\nimport { Actions, CallBackProps, Props, State, Status, StoreHelpers } from '~/types';\n\nimport Step from './Step';\n\nclass Joyride extends React.Component<Props, State> {\n  private readonly helpers: StoreHelpers;\n  private readonly store: ReturnType<typeof createStore>;\n\n  static defaultProps = defaultProps;\n\n  constructor(props: Props) {\n    super(props);\n\n    const { debug, getHelpers, run = true, stepIndex } = props;\n\n    this.store = createStore({\n      ...props,\n      controlled: run && is.number(stepIndex),\n    });\n    this.helpers = this.store.getHelpers();\n\n    const { addListener } = this.store;\n\n    log({\n      title: 'init',\n      data: [\n        { key: 'props', value: this.props },\n        { key: 'state', value: this.state },\n      ],\n      debug,\n    });\n\n    // Sync the store to this component's state.\n    addListener(this.syncState);\n\n    if (getHelpers) {\n      getHelpers(this.helpers);\n    }\n\n    this.state = this.store.getState();\n  }\n\n  componentDidMount() {\n    if (!canUseDOM()) {\n      return;\n    }\n\n    const { debug, disableCloseOnEsc, run, steps } = this.props;\n    const { start } = this.store;\n\n    if (validateSteps(steps, debug) && run) {\n      start();\n    }\n\n    if (!disableCloseOnEsc) {\n      document.body.addEventListener('keydown', this.handleKeyboard, { passive: true });\n    }\n  }\n\n  componentDidUpdate(previousProps: Props, previousState: State) {\n    if (!canUseDOM()) {\n      return;\n    }\n\n    const { action, controlled, index, status } = this.state;\n    const { debug, run, stepIndex, steps } = this.props;\n    const { stepIndex: previousStepIndex, steps: previousSteps } = previousProps;\n    const { reset, setSteps, start, stop, update } = this.store;\n    const { changed: changedProps } = treeChanges(previousProps, this.props);\n    const { changed, changedFrom } = treeChanges(previousState, this.state);\n    const step = getMergedStep(this.props, steps[index]);\n\n    const stepsChanged = !isEqual(previousSteps, steps);\n    const stepIndexChanged = is.number(stepIndex) && changedProps('stepIndex');\n    const target = getElement(step.target);\n\n    if (stepsChanged) {\n      if (validateSteps(steps, debug)) {\n        setSteps(steps);\n      } else {\n        // eslint-disable-next-line no-console\n        console.warn('Steps are not valid', steps);\n      }\n    }\n\n    if (changedProps('run')) {\n      if (run) {\n        start(stepIndex);\n      } else {\n        stop();\n      }\n    }\n\n    if (stepIndexChanged) {\n      let nextAction: Actions =\n        is.number(previousStepIndex) && previousStepIndex < stepIndex ? ACTIONS.NEXT : ACTIONS.PREV;\n\n      if (action === ACTIONS.STOP) {\n        nextAction = ACTIONS.START;\n      }\n\n      if (!([STATUS.FINISHED, STATUS.SKIPPED] as Array<Status>).includes(status)) {\n        update({\n          action: action === ACTIONS.CLOSE ? ACTIONS.CLOSE : nextAction,\n          index: stepIndex,\n          lifecycle: LIFECYCLE.INIT,\n        });\n      }\n    }\n\n    // Update the index if the first step is not found\n    if (!controlled && status === STATUS.RUNNING && index === 0 && !target) {\n      this.store.update({ index: index + 1 });\n      this.callback({\n        ...this.state,\n        type: EVENTS.TARGET_NOT_FOUND,\n        step,\n      });\n    }\n\n    const callbackData = {\n      ...this.state,\n      index,\n      step,\n    };\n    const isAfterAction = changed('action', [\n      ACTIONS.NEXT,\n      ACTIONS.PREV,\n      ACTIONS.SKIP,\n      ACTIONS.CLOSE,\n    ]);\n\n    if (isAfterAction && changed('status', STATUS.PAUSED)) {\n      const previousStep = getMergedStep(this.props, steps[previousState.index]);\n\n      this.callback({\n        ...callbackData,\n        index: previousState.index,\n        lifecycle: LIFECYCLE.COMPLETE,\n        step: previousStep,\n        type: EVENTS.STEP_AFTER,\n      });\n    }\n\n    if (changed('status', [STATUS.FINISHED, STATUS.SKIPPED])) {\n      const previousStep = getMergedStep(this.props, steps[previousState.index]);\n\n      if (!controlled) {\n        this.callback({\n          ...callbackData,\n          index: previousState.index,\n          lifecycle: LIFECYCLE.COMPLETE,\n          step: previousStep,\n          type: EVENTS.STEP_AFTER,\n        });\n      }\n\n      this.callback({\n        ...callbackData,\n        type: EVENTS.TOUR_END,\n        // Return the last step when the tour is finished\n        step: previousStep,\n        index: previousState.index,\n      });\n      reset();\n    } else if (changedFrom('status', [STATUS.IDLE, STATUS.READY], STATUS.RUNNING)) {\n      this.callback({\n        ...callbackData,\n        type: EVENTS.TOUR_START,\n      });\n    } else if (changed('status') || changed('action', ACTIONS.RESET)) {\n      this.callback({\n        ...callbackData,\n        type: EVENTS.TOUR_STATUS,\n      });\n    }\n\n    this.scrollToStep(previousState);\n  }\n\n  componentWillUnmount() {\n    const { disableCloseOnEsc } = this.props;\n\n    if (!disableCloseOnEsc) {\n      document.body.removeEventListener('keydown', this.handleKeyboard);\n    }\n  }\n\n  /**\n   * Trigger the callback.\n   */\n  callback = (data: CallBackProps) => {\n    const { callback } = this.props;\n\n    if (is.function(callback)) {\n      callback(data);\n    }\n  };\n\n  /**\n   * Keydown event listener\n   */\n  handleKeyboard = (event: KeyboardEvent) => {\n    const { index, lifecycle } = this.state;\n    const { steps } = this.props;\n    const step = steps[index];\n\n    if (lifecycle === LIFECYCLE.TOOLTIP) {\n      if (event.code === 'Escape' && step && !step.disableCloseOnEsc) {\n        this.store.close('keyboard');\n      }\n    }\n  };\n\n  handleClickOverlay = () => {\n    const { index } = this.state;\n    const { steps } = this.props;\n\n    const step = getMergedStep(this.props, steps[index]);\n\n    if (!step.disableOverlayClose) {\n      this.helpers.close('overlay');\n    }\n  };\n\n  /**\n   * Sync the store with the component's state\n   */\n  syncState = (state: State) => {\n    this.setState(state);\n  };\n\n  scrollToStep(previousState: State) {\n    const { index, lifecycle, status } = this.state;\n    const {\n      debug,\n      disableScrollParentFix = false,\n      scrollDuration,\n      scrollOffset = 20,\n      scrollToFirstStep = false,\n      steps,\n    } = this.props;\n    const step = getMergedStep(this.props, steps[index]);\n\n    const target = getElement(step.target);\n    const shouldScrollToStep = shouldScroll({\n      isFirstStep: index === 0,\n      lifecycle,\n      previousLifecycle: previousState.lifecycle,\n      scrollToFirstStep,\n      step,\n      target,\n    });\n\n    if (status === STATUS.RUNNING && shouldScrollToStep) {\n      const hasCustomScroll = hasCustomScrollParent(target, disableScrollParentFix);\n      const scrollParent = getScrollParent(target, disableScrollParentFix);\n      let scrollY = Math.floor(getScrollTo(target, scrollOffset, disableScrollParentFix)) || 0;\n\n      log({\n        title: 'scrollToStep',\n        data: [\n          { key: 'index', value: index },\n          { key: 'lifecycle', value: lifecycle },\n          { key: 'status', value: status },\n        ],\n        debug,\n      });\n\n      const beaconPopper = this.store.getPopper('beacon');\n      const tooltipPopper = this.store.getPopper('tooltip');\n\n      if (lifecycle === LIFECYCLE.BEACON && beaconPopper) {\n        const { offsets, placement } = beaconPopper;\n\n        if (!['bottom'].includes(placement) && !hasCustomScroll) {\n          scrollY = Math.floor(offsets.popper.top - scrollOffset);\n        }\n      } else if (lifecycle === LIFECYCLE.TOOLTIP && tooltipPopper) {\n        const { flipped, offsets, placement } = tooltipPopper;\n\n        if (['top', 'right', 'left'].includes(placement) && !flipped && !hasCustomScroll) {\n          scrollY = Math.floor(offsets.popper.top - scrollOffset);\n        } else {\n          scrollY -= step.spotlightPadding;\n        }\n      }\n\n      scrollY = scrollY >= 0 ? scrollY : 0;\n\n      if (status === STATUS.RUNNING) {\n        scrollTo(scrollY, { element: scrollParent as Element, duration: scrollDuration }).then(\n          () => {\n            setTimeout(() => {\n              this.store.getPopper('tooltip')?.instance.update();\n            }, 10);\n          },\n        );\n      }\n    }\n  }\n\n  render() {\n    if (!canUseDOM()) {\n      return null;\n    }\n\n    const { index, lifecycle, status } = this.state;\n    const {\n      continuous = false,\n      debug = false,\n      nonce,\n      scrollToFirstStep = false,\n      steps,\n    } = this.props;\n    const isRunning = status === STATUS.RUNNING;\n    const content: Record<string, ReactNode> = {};\n\n    if (isRunning && steps[index]) {\n      const step = getMergedStep(this.props, steps[index]);\n\n      content.step = (\n        <Step\n          {...this.state}\n          callback={this.callback}\n          continuous={continuous}\n          debug={debug}\n          helpers={this.helpers}\n          nonce={nonce}\n          shouldScroll={!step.disableScrolling && (index !== 0 || scrollToFirstStep)}\n          step={step}\n          store={this.store}\n        />\n      );\n\n      content.overlay = (\n        <Portal id=\"react-joyride-portal\">\n          <Overlay\n            {...step}\n            continuous={continuous}\n            debug={debug}\n            lifecycle={lifecycle}\n            onClickOverlay={this.handleClickOverlay}\n          />\n        </Portal>\n      );\n    }\n\n    return (\n      <div className=\"react-joyride\">\n        {content.step}\n        {content.overlay}\n      </div>\n    );\n  }\n}\n\nexport default Joyride;\n", "import scroll from 'scroll';\nimport scrollParent from 'scrollparent';\n\nexport function canUseDOM() {\n  return !!(typeof window !== 'undefined' && window.document?.createElement);\n}\n\n/**\n * Find the bounding client rect\n */\nexport function getClientRect(element: HTMLElement | null) {\n  if (!element) {\n    return null;\n  }\n\n  return element.getBoundingClientRect();\n}\n\n/**\n * Helper function to get the browser-normalized \"document height\"\n */\nexport function getDocumentHeight(median = false): number {\n  const { body, documentElement } = document;\n\n  if (!body || !documentElement) {\n    return 0;\n  }\n\n  if (median) {\n    const heights = [\n      body.scrollHeight,\n      body.offsetHeight,\n      documentElement.clientHeight,\n      documentElement.scrollHeight,\n      documentElement.offsetHeight,\n    ].sort((a, b) => a - b);\n    const middle = Math.floor(heights.length / 2);\n\n    if (heights.length % 2 === 0) {\n      return (heights[middle - 1] + heights[middle]) / 2;\n    }\n\n    return heights[middle];\n  }\n\n  return Math.max(\n    body.scrollHeight,\n    body.offsetHeight,\n    documentElement.clientHeight,\n    documentElement.scrollHeight,\n    documentElement.offsetHeight,\n  );\n}\n\n/**\n * Find and return the target DOM element based on a step's 'target'.\n */\nexport function getElement(element: string | HTMLElement): HTMLElement | null {\n  if (typeof element === 'string') {\n    try {\n      return document.querySelector(element);\n    } catch (error: any) {\n      if (process.env.NODE_ENV !== 'production') {\n        // eslint-disable-next-line no-console\n        console.error(error);\n      }\n\n      return null;\n    }\n  }\n\n  return element;\n}\n\n/**\n *  Get computed style property\n */\nexport function getStyleComputedProperty(el: HTMLElement): CSSStyleDeclaration | null {\n  if (!el || el.nodeType !== 1) {\n    return null;\n  }\n\n  return getComputedStyle(el);\n}\n\n/**\n * Get scroll parent with fix\n */\nexport function getScrollParent(\n  element: HTMLElement | null,\n  skipFix: boolean,\n  forListener?: boolean,\n) {\n  if (!element) {\n    return scrollDocument();\n  }\n\n  const parent = scrollParent(element) as HTMLElement;\n\n  if (parent) {\n    if (parent.isSameNode(scrollDocument())) {\n      if (forListener) {\n        return document;\n      }\n\n      return scrollDocument();\n    }\n\n    const hasScrolling = parent.scrollHeight > parent.offsetHeight;\n\n    if (!hasScrolling && !skipFix) {\n      parent.style.overflow = 'initial';\n\n      return scrollDocument();\n    }\n  }\n\n  return parent;\n}\n\n/**\n * Check if the element has custom scroll parent\n */\nexport function hasCustomScrollParent(element: HTMLElement | null, skipFix: boolean): boolean {\n  if (!element) {\n    return false;\n  }\n\n  const parent = getScrollParent(element, skipFix);\n\n  return parent ? !parent.isSameNode(scrollDocument()) : false;\n}\n\n/**\n * Check if the element has custom offset parent\n */\nexport function hasCustomOffsetParent(element: HTMLElement): boolean {\n  return element.offsetParent !== document.body;\n}\n\n/**\n * Check if an element has fixed/sticky position\n */\nexport function hasPosition(el: HTMLElement | Node | null, type: string = 'fixed'): boolean {\n  if (!el || !(el instanceof HTMLElement)) {\n    return false;\n  }\n\n  const { nodeName } = el;\n  const styles = getStyleComputedProperty(el);\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n\n  if (styles && styles.position === type) {\n    return true;\n  }\n\n  if (!el.parentNode) {\n    return false;\n  }\n\n  return hasPosition(el.parentNode, type);\n}\n\n/**\n * Check if the element is visible\n */\nexport function isElementVisible(element: HTMLElement): element is HTMLElement {\n  if (!element) {\n    return false;\n  }\n\n  let parentElement: HTMLElement | null = element;\n\n  while (parentElement) {\n    if (parentElement === document.body) {\n      break;\n    }\n\n    if (parentElement instanceof HTMLElement) {\n      const { display, visibility } = getComputedStyle(parentElement);\n\n      if (display === 'none' || visibility === 'hidden') {\n        return false;\n      }\n    }\n\n    parentElement = parentElement.parentElement ?? null;\n  }\n\n  return true;\n}\n\n/**\n * Find and return the target DOM element based on a step's 'target'.\n */\nexport function getElementPosition(\n  element: HTMLElement | null,\n  offset: number,\n  skipFix: boolean,\n): number {\n  const elementRect = getClientRect(element);\n  const parent = getScrollParent(element, skipFix);\n  const hasScrollParent = hasCustomScrollParent(element, skipFix);\n  const isFixedTarget = hasPosition(element);\n  let parentTop = 0;\n  let top = elementRect?.top ?? 0;\n\n  if (hasScrollParent && isFixedTarget) {\n    const offsetTop = element?.offsetTop ?? 0;\n    const parentScrollTop = (parent as HTMLElement)?.scrollTop ?? 0;\n\n    top = offsetTop - parentScrollTop;\n  } else if (parent instanceof HTMLElement) {\n    parentTop = parent.scrollTop;\n\n    if (!hasScrollParent && !hasPosition(element)) {\n      top += parentTop;\n    }\n\n    if (!parent.isSameNode(scrollDocument())) {\n      top += scrollDocument().scrollTop;\n    }\n  }\n\n  return Math.floor(top - offset);\n}\n\n/**\n * Get the scrollTop position\n */\nexport function getScrollTo(element: HTMLElement | null, offset: number, skipFix: boolean): number {\n  if (!element) {\n    return 0;\n  }\n\n  const { offsetTop = 0, scrollTop = 0 } = scrollParent(element) ?? {};\n  let top = element.getBoundingClientRect().top + scrollTop;\n\n  if (!!offsetTop && (hasCustomScrollParent(element, skipFix) || hasCustomOffsetParent(element))) {\n    top -= offsetTop;\n  }\n\n  const output = Math.floor(top - offset);\n\n  return output < 0 ? 0 : output;\n}\n\nexport function scrollDocument(): Element | HTMLElement {\n  return document.scrollingElement ?? document.documentElement;\n}\n\n/**\n * Scroll to position\n */\nexport function scrollTo(\n  value: number,\n  options: { duration?: number; element: Element | HTMLElement },\n): Promise<void> {\n  const { duration, element } = options;\n\n  return new Promise((resolve, reject) => {\n    const { scrollTop } = element;\n\n    const limit = value > scrollTop ? value - scrollTop : scrollTop - value;\n\n    scroll.top(element as HTMLElement, value, { duration: limit < 100 ? 50 : duration }, error => {\n      if (error && error.message !== 'Element already at target scroll position') {\n        return reject(error);\n      }\n\n      return resolve();\n    });\n  });\n}\n", "import { cloneElement, FC, isValidElement, ReactElement, ReactNode } from 'react';\nimport { createPortal } from 'react-dom';\nimport innerText from 'react-innertext';\nimport is from 'is-lite';\n\nimport { LIFECYCLE } from '~/literals';\n\nimport { AnyObject, Lifecycle, NarrowPlainObject, Step } from '~/types';\n\nimport { hasPosition } from './dom';\n\ninterface GetReactNodeTextOptions {\n  defaultValue?: any;\n  step?: number;\n  steps?: number;\n}\n\ninterface LogOptions {\n  /** The data to be logged */\n  data: any;\n  /** display the log */\n  debug?: boolean;\n  /** The title the logger was called from */\n  title: string;\n  /** If true, the message will be a warning */\n  warn?: boolean;\n}\n\ninterface ShouldScrollOptions {\n  isFirstStep: boolean;\n  lifecycle: Lifecycle;\n  previousLifecycle: Lifecycle;\n  scrollToFirstStep: boolean;\n  step: Step;\n  target: HTMLElement | null;\n}\n\nexport const isReact16 = createPortal !== undefined;\n\n/**\n * Get the current browser\n */\nexport function getBrowser(userAgent: string = navigator.userAgent): string {\n  let browser = userAgent;\n\n  if (typeof window === 'undefined') {\n    browser = 'node';\n  }\n  // @ts-expect-error IE support\n  else if (document.documentMode) {\n    browser = 'ie';\n  } else if (/Edge/.test(userAgent)) {\n    browser = 'edge';\n  }\n  // @ts-expect-error Opera 8.0+\n  else if (Boolean(window.opera) || userAgent.includes(' OPR/')) {\n    browser = 'opera';\n  }\n  // @ts-expect-error Firefox 1.0+\n  else if (typeof window.InstallTrigger !== 'undefined') {\n    browser = 'firefox';\n  }\n  // @ts-expect-error Chrome 1+\n  else if (window.chrome) {\n    browser = 'chrome';\n  }\n  // Safari (and Chrome iOS, Firefox iOS)\n  else if (/(Version\\/([\\d._]+).*Safari|CriOS|FxiOS| Mobile\\/)/.test(userAgent)) {\n    browser = 'safari';\n  }\n\n  return browser;\n}\n\n/**\n * Get Object type\n */\nexport function getObjectType(value: unknown): string {\n  return Object.prototype.toString.call(value).slice(8, -1).toLowerCase();\n}\n\nexport function getReactNodeText(input: ReactNode, options: GetReactNodeTextOptions = {}): string {\n  const { defaultValue, step, steps } = options;\n  let text = innerText(input);\n\n  if (!text) {\n    if (\n      isValidElement(input) &&\n      !Object.values(input.props).length &&\n      getObjectType(input.type) === 'function'\n    ) {\n      const component = (input.type as FC)({});\n\n      text = getReactNodeText(component, options);\n    } else {\n      text = innerText(defaultValue);\n    }\n  } else if ((text.includes('{step}') || text.includes('{steps}')) && step && steps) {\n    text = text.replace('{step}', step.toString()).replace('{steps}', steps.toString());\n  }\n\n  return text;\n}\n\nexport function hasValidKeys(object: Record<string, unknown>, keys?: Array<string>): boolean {\n  if (!is.plainObject(object) || !is.array(keys)) {\n    return false;\n  }\n\n  return Object.keys(object).every(d => keys.includes(d));\n}\n\n/**\n * Convert hex to RGB\n */\nexport function hexToRGB(hex: string): Array<number> {\n  const shorthandRegex = /^#?([\\da-f])([\\da-f])([\\da-f])$/i;\n  const properHex = hex.replace(shorthandRegex, (_m, r, g, b) => r + r + g + g + b + b);\n\n  const result = /^#?([\\da-f]{2})([\\da-f]{2})([\\da-f]{2})$/i.exec(properHex);\n\n  return result ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)] : [];\n}\n\n/**\n * Decide if the step shouldn't skip the beacon\n * @param {Object} step\n *\n * @returns {boolean}\n */\nexport function hideBeacon(step: Step): boolean {\n  return step.disableBeacon || step.placement === 'center';\n}\n\n/**\n * Detect legacy browsers\n *\n * @returns {boolean}\n */\nexport function isLegacy(): boolean {\n  return !['chrome', 'safari', 'firefox', 'opera'].includes(getBrowser());\n}\n\n/**\n * Log method calls if debug is enabled\n */\nexport function log({ data, debug = false, title, warn = false }: LogOptions) {\n  /* eslint-disable no-console */\n  const logFn = warn ? console.warn || console.error : console.log;\n\n  if (debug) {\n    if (title && data) {\n      console.groupCollapsed(\n        `%creact-joyride: ${title}`,\n        'color: #ff0044; font-weight: bold; font-size: 12px;',\n      );\n\n      if (Array.isArray(data)) {\n        data.forEach(d => {\n          if (is.plainObject(d) && d.key) {\n            logFn.apply(console, [d.key, d.value]);\n          } else {\n            logFn.apply(console, [d]);\n          }\n        });\n      } else {\n        logFn.apply(console, [data]);\n      }\n\n      console.groupEnd();\n    } else {\n      console.error('Missing title or data props');\n    }\n  }\n  /* eslint-enable */\n}\n\n/**\n * A function that does nothing.\n */\nexport function noop() {\n  return undefined;\n}\n\n/**\n * Type-safe Object.keys()\n */\nexport function objectKeys<T extends AnyObject>(input: T) {\n  return Object.keys(input) as Array<keyof T>;\n}\n\n/**\n * Remove properties from an object\n */\nexport function omit<T extends Record<string, any>, K extends keyof T>(\n  input: NarrowPlainObject<T>,\n  ...filter: K[]\n) {\n  if (!is.plainObject(input)) {\n    throw new TypeError('Expected an object');\n  }\n\n  const output: any = {};\n\n  for (const key in input) {\n    /* istanbul ignore else */\n    if ({}.hasOwnProperty.call(input, key)) {\n      if (!filter.includes(key as unknown as K)) {\n        output[key] = input[key];\n      }\n    }\n  }\n\n  return output as Omit<T, K>;\n}\n\n/**\n * Select properties from an object\n */\nexport function pick<T extends Record<string, any>, K extends keyof T>(\n  input: NarrowPlainObject<T>,\n  ...filter: K[]\n) {\n  if (!is.plainObject(input)) {\n    throw new TypeError('Expected an object');\n  }\n\n  if (!filter.length) {\n    return input;\n  }\n\n  const output: any = {};\n\n  for (const key in input) {\n    /* istanbul ignore else */\n    if ({}.hasOwnProperty.call(input, key)) {\n      if (filter.includes(key as unknown as K)) {\n        output[key] = input[key];\n      }\n    }\n  }\n\n  return output as Pick<T, K>;\n}\n\nexport function replaceLocaleContent(input: ReactNode, step: number, steps: number): ReactNode {\n  const replacer = (text: string) =>\n    text.replace('{step}', String(step)).replace('{steps}', String(steps));\n\n  if (getObjectType(input) === 'string') {\n    return replacer(input as string);\n  }\n\n  if (!isValidElement(input)) {\n    return input;\n  }\n\n  const { children } = input.props;\n\n  if (getObjectType(children) === 'string' && children.includes('{step}')) {\n    return cloneElement(input as ReactElement, {\n      children: replacer(children),\n    });\n  }\n\n  if (Array.isArray(children)) {\n    return cloneElement(input as ReactElement, {\n      children: children.map((child: ReactNode) => {\n        if (typeof child === 'string') {\n          return replacer(child);\n        }\n\n        return replaceLocaleContent(child, step, steps);\n      }),\n    });\n  }\n\n  if (getObjectType(input.type) === 'function' && !Object.values(input.props).length) {\n    const component = (input.type as FC)({});\n\n    return replaceLocaleContent(component, step, steps);\n  }\n\n  return input;\n}\n\nexport function shouldScroll(options: ShouldScrollOptions): boolean {\n  const { isFirstStep, lifecycle, previousLifecycle, scrollToFirstStep, step, target } = options;\n\n  return (\n    !step.disableScrolling &&\n    (!isFirstStep || scrollToFirstStep || lifecycle === LIFECYCLE.TOOLTIP) &&\n    step.placement !== 'center' &&\n    (!step.isFixed || !hasPosition(target)) && // fixed steps don't need to scroll\n    previousLifecycle !== lifecycle &&\n    ([LIFECYCLE.BEACON, LIFECYCLE.TOOLTIP] as Array<Lifecycle>).includes(lifecycle)\n  );\n}\n\n/**\n * Block execution\n */\nexport function sleep(seconds = 1) {\n  return new Promise(resolve => {\n    setTimeout(resolve, seconds * 1000);\n  });\n}\n", "import { Props as FloaterProps } from 'react-floater';\nimport deepmerge from 'deepmerge';\nimport is from 'is-lite';\nimport { SetRequired } from 'type-fest';\n\nimport { defaultFloaterProps, defaultLocale, defaultStep } from '~/defaults';\nimport getStyles from '~/styles';\nimport { Props, Step, StepMerged } from '~/types';\n\nimport { getElement, hasCustomScrollParent } from './dom';\nimport { log, omit, pick } from './helpers';\n\nfunction getTourProps(props: Props) {\n  return pick(\n    props,\n    'beaconComponent',\n    'disableCloseOnEsc',\n    'disableOverlay',\n    'disableOverlayClose',\n    'disableScrolling',\n    'disableScrollParentFix',\n    'floaterProps',\n    'hideBackButton',\n    'hideCloseButton',\n    'locale',\n    'showProgress',\n    'showSkipButton',\n    'spotlightClicks',\n    'spotlightPadding',\n    'styles',\n    'tooltipComponent',\n  );\n}\n\nexport function getMergedStep(props: Props, currentStep?: Step): StepMerged {\n  const step = currentStep ?? {};\n  const mergedStep = deepmerge.all([defaultStep, getTourProps(props), step], {\n    isMergeableObject: is.plainObject,\n  }) as StepMerged;\n\n  const mergedStyles = getStyles(props, mergedStep);\n  const scrollParent = hasCustomScrollParent(\n    getElement(mergedStep.target),\n    mergedStep.disableScrollParentFix,\n  );\n  const floaterProps = deepmerge.all([\n    defaultFloaterProps,\n    props.floaterProps ?? {},\n    mergedStep.floaterProps ?? {},\n  ]) as SetRequired<FloaterProps, 'options' | 'wrapperOptions'>;\n\n  // Set react-floater props\n  floaterProps.offset = mergedStep.offset;\n  floaterProps.styles = deepmerge(floaterProps.styles ?? {}, mergedStyles.floaterStyles);\n\n  floaterProps.offset += props.spotlightPadding ?? mergedStep.spotlightPadding ?? 0;\n\n  if (mergedStep.placementBeacon && floaterProps.wrapperOptions) {\n    floaterProps.wrapperOptions.placement = mergedStep.placementBeacon;\n  }\n\n  if (scrollParent && floaterProps.options.preventOverflow) {\n    floaterProps.options.preventOverflow.boundariesElement = 'window';\n  }\n\n  return {\n    ...mergedStep,\n    locale: deepmerge.all([defaultLocale, props.locale ?? {}, mergedStep.locale || {}]),\n    floaterProps,\n    styles: omit(mergedStyles, 'floaterStyles'),\n  };\n}\n\n/**\n * Validate if a step is valid\n */\nexport function validateStep(step: Step, debug: boolean = false): boolean {\n  if (!is.plainObject(step)) {\n    log({\n      title: 'validateStep',\n      data: 'step must be an object',\n      warn: true,\n      debug,\n    });\n\n    return false;\n  }\n\n  if (!step.target) {\n    log({\n      title: 'validateStep',\n      data: 'target is missing from the step',\n      warn: true,\n      debug,\n    });\n\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Validate if steps are valid\n */\nexport function validateSteps(steps: Array<Step>, debug: boolean = false): boolean {\n  if (!is.array(steps)) {\n    log({\n      title: 'validateSteps',\n      data: 'steps must be an array',\n      warn: true,\n      debug,\n    });\n\n    return false;\n  }\n\n  return steps.every(d => validateStep(d, debug));\n}\n", "import { noop } from '~/modules/helpers';\n\nimport { FloaterProps, Locale, Props, Step } from '~/types';\n\nexport const defaultFloaterProps: FloaterProps = {\n  options: {\n    preventOverflow: {\n      boundariesElement: 'scrollParent',\n    },\n  },\n  wrapperOptions: {\n    offset: -18,\n    position: true,\n  },\n};\n\nexport const defaultLocale: Locale = {\n  back: 'Back',\n  close: 'Close',\n  last: 'Last',\n  next: 'Next',\n  nextLabelWithProgress: 'Next (Step {step} of {steps})',\n  open: 'Open the dialog',\n  skip: 'Skip',\n};\n\nexport const defaultStep = {\n  event: 'click',\n  placement: 'bottom',\n  offset: 10,\n  disableBeacon: false,\n  disableCloseOnEsc: false,\n  disableOverlay: false,\n  disableOverlayClose: false,\n  disableScrollParentFix: false,\n  disableScrolling: false,\n  hideBackButton: false,\n  hideCloseButton: false,\n  hideFooter: false,\n  isFixed: false,\n  locale: defaultLocale,\n  showProgress: false,\n  showSkipButton: false,\n  spotlightClicks: false,\n  spotlightPadding: 10,\n} satisfies Omit<Step, 'content' | 'target'>;\n\nexport const defaultProps = {\n  continuous: false,\n  debug: false,\n  disableCloseOnEsc: false,\n  disableOverlay: false,\n  disableOverlayClose: false,\n  disableScrolling: false,\n  disableScrollParentFix: false,\n  getHelpers: noop(),\n  hideBackButton: false,\n  run: true,\n  scrollOffset: 20,\n  scrollDuration: 300,\n  scrollToFirstStep: false,\n  showSkipButton: false,\n  showProgress: false,\n  spotlightClicks: false,\n  spotlightPadding: 10,\n  steps: [],\n} satisfies Props;\n", "import deepmerge from 'deepmerge';\n\nimport { hexToRGB } from './modules/helpers';\nimport { Props, StepMerged, StylesOptions, StylesWithFloaterStyles } from './types';\n\nconst defaultOptions = {\n  arrowColor: '#fff',\n  backgroundColor: '#fff',\n  beaconSize: 36,\n  overlayColor: 'rgba(0, 0, 0, 0.5)',\n  primaryColor: '#f04',\n  spotlightShadow: '0 0 15px rgba(0, 0, 0, 0.5)',\n  textColor: '#333',\n  width: 380,\n  zIndex: 100,\n} satisfies StylesOptions;\n\nconst buttonBase = {\n  backgroundColor: 'transparent',\n  border: 0,\n  borderRadius: 0,\n  color: '#555',\n  cursor: 'pointer',\n  fontSize: 16,\n  lineHeight: 1,\n  padding: 8,\n  WebkitAppearance: 'none',\n};\n\nconst spotlight = {\n  borderRadius: 4,\n  position: 'absolute',\n};\n\nexport default function getStyles(props: Props, step: StepMerged) {\n  const { floaterProps, styles } = props;\n  const mergedFloaterProps = deepmerge(step.floaterProps ?? {}, floaterProps ?? {});\n  const mergedStyles = deepmerge(styles ?? {}, step.styles ?? {});\n  const options = deepmerge(defaultOptions, mergedStyles.options || {}) satisfies StylesOptions;\n  const hideBeacon = step.placement === 'center' || step.disableBeacon;\n  let { width } = options;\n\n  if (window.innerWidth > 480) {\n    width = 380;\n  }\n\n  if ('width' in options) {\n    width =\n      typeof options.width === 'number' && window.innerWidth < options.width\n        ? window.innerWidth - 30\n        : options.width;\n  }\n\n  const overlay = {\n    bottom: 0,\n    left: 0,\n    overflow: 'hidden',\n    position: 'absolute',\n    right: 0,\n    top: 0,\n    zIndex: options.zIndex,\n  };\n\n  const defaultStyles = {\n    beacon: {\n      ...buttonBase,\n      display: hideBeacon ? 'none' : 'inline-block',\n      height: options.beaconSize,\n      position: 'relative',\n      width: options.beaconSize,\n      zIndex: options.zIndex,\n    },\n    beaconInner: {\n      animation: 'joyride-beacon-inner 1.2s infinite ease-in-out',\n      backgroundColor: options.primaryColor,\n      borderRadius: '50%',\n      display: 'block',\n      height: '50%',\n      left: '50%',\n      opacity: 0.7,\n      position: 'absolute',\n      top: '50%',\n      transform: 'translate(-50%, -50%)',\n      width: '50%',\n    },\n    beaconOuter: {\n      animation: 'joyride-beacon-outer 1.2s infinite ease-in-out',\n      backgroundColor: `rgba(${hexToRGB(options.primaryColor).join(',')}, 0.2)`,\n      border: `2px solid ${options.primaryColor}`,\n      borderRadius: '50%',\n      boxSizing: 'border-box',\n      display: 'block',\n      height: '100%',\n      left: 0,\n      opacity: 0.9,\n      position: 'absolute',\n      top: 0,\n      transformOrigin: 'center',\n      width: '100%',\n    },\n    tooltip: {\n      backgroundColor: options.backgroundColor,\n      borderRadius: 5,\n      boxSizing: 'border-box',\n      color: options.textColor,\n      fontSize: 16,\n      maxWidth: '100%',\n      padding: 15,\n      position: 'relative',\n      width,\n    },\n    tooltipContainer: {\n      lineHeight: 1.4,\n      textAlign: 'center',\n    },\n    tooltipTitle: {\n      fontSize: 18,\n      margin: 0,\n    },\n    tooltipContent: {\n      padding: '20px 10px',\n    },\n    tooltipFooter: {\n      alignItems: 'center',\n      display: 'flex',\n      justifyContent: 'flex-end',\n      marginTop: 15,\n    },\n    tooltipFooterSpacer: {\n      flex: 1,\n    },\n    buttonNext: {\n      ...buttonBase,\n      backgroundColor: options.primaryColor,\n      borderRadius: 4,\n      color: '#fff',\n    },\n    buttonBack: {\n      ...buttonBase,\n      color: options.primaryColor,\n      marginLeft: 'auto',\n      marginRight: 5,\n    },\n    buttonClose: {\n      ...buttonBase,\n      color: options.textColor,\n      height: 14,\n      padding: 15,\n      position: 'absolute',\n      right: 0,\n      top: 0,\n      width: 14,\n    },\n    buttonSkip: {\n      ...buttonBase,\n      color: options.textColor,\n      fontSize: 14,\n    },\n    overlay: {\n      ...overlay,\n      backgroundColor: options.overlayColor,\n      mixBlendMode: 'hard-light',\n    },\n    overlayLegacy: {\n      ...overlay,\n    },\n    overlayLegacyCenter: {\n      ...overlay,\n      backgroundColor: options.overlayColor,\n    },\n    spotlight: {\n      ...spotlight,\n      backgroundColor: 'gray',\n    },\n    spotlightLegacy: {\n      ...spotlight,\n      boxShadow: `0 0 0 9999px ${options.overlayColor}, ${options.spotlightShadow}`,\n    },\n    floaterStyles: {\n      arrow: {\n        color: mergedFloaterProps?.styles?.arrow?.color ?? options.arrowColor,\n      },\n      options: {\n        zIndex: options.zIndex + 100,\n      },\n    },\n    options,\n  };\n\n  return deepmerge(defaultStyles, mergedStyles) as StylesWithFloaterStyles;\n}\n", "import { Props as FloaterProps } from 'react-floater';\nimport is from 'is-lite';\n\nimport { ACTIONS, LIFECYCLE, STATUS } from '~/literals';\n\nimport { Origin, State, Status, Step, StoreHelpers, StoreOptions } from '~/types';\n\nimport { hasValidKeys, objectKeys, omit } from './helpers';\n\ntype StateWithContinuous = State & { continuous: boolean };\ntype Listener = (state: State) => void;\ntype PopperData = Parameters<NonNullable<FloaterProps['getPopper']>>[0];\n\nconst defaultState: State = {\n  action: 'init',\n  controlled: false,\n  index: 0,\n  lifecycle: LIFECYCLE.INIT,\n  origin: null,\n  size: 0,\n  status: STATUS.IDLE,\n};\nconst validKeys = objectKeys(omit(defaultState, 'controlled', 'size'));\n\nclass Store {\n  private beaconPopper: PopperData | null;\n  private tooltipPopper: PopperData | null;\n  private data: Map<string, any> = new Map();\n  private listener: Listener | null;\n  private store: Map<string, any> = new Map();\n\n  constructor(options?: StoreOptions) {\n    const { continuous = false, stepIndex, steps = [] } = options ?? {};\n\n    this.setState(\n      {\n        action: ACTIONS.INIT,\n        controlled: is.number(stepIndex),\n        continuous,\n        index: is.number(stepIndex) ? stepIndex : 0,\n        lifecycle: LIFECYCLE.INIT,\n        origin: null,\n        status: steps.length ? STATUS.READY : STATUS.IDLE,\n      },\n      true,\n    );\n\n    this.beaconPopper = null;\n    this.tooltipPopper = null;\n    this.listener = null;\n    this.setSteps(steps);\n  }\n\n  public getState(): State {\n    if (!this.store.size) {\n      return { ...defaultState };\n    }\n\n    return {\n      action: this.store.get('action') || '',\n      controlled: this.store.get('controlled') || false,\n      index: parseInt(this.store.get('index'), 10),\n      lifecycle: this.store.get('lifecycle') || '',\n      origin: this.store.get('origin') || null,\n      size: this.store.get('size') || 0,\n      status: (this.store.get('status') as Status) || '',\n    };\n  }\n\n  private getNextState(state: Partial<State>, force: boolean = false): State {\n    const { action, controlled, index, size, status } = this.getState();\n    const newIndex = is.number(state.index) ? state.index : index;\n    const nextIndex = controlled && !force ? index : Math.min(Math.max(newIndex, 0), size);\n\n    return {\n      action: state.action ?? action,\n      controlled,\n      index: nextIndex,\n      lifecycle: state.lifecycle ?? LIFECYCLE.INIT,\n      origin: state.origin ?? null,\n      size: state.size ?? size,\n      status: nextIndex === size ? STATUS.FINISHED : (state.status ?? status),\n    };\n  }\n\n  private getSteps(): Array<Step> {\n    const steps = this.data.get('steps');\n\n    return Array.isArray(steps) ? steps : [];\n  }\n\n  private hasUpdatedState(oldState: State): boolean {\n    const before = JSON.stringify(oldState);\n    const after = JSON.stringify(this.getState());\n\n    return before !== after;\n  }\n\n  private setState(nextState: Partial<StateWithContinuous>, initial: boolean = false) {\n    const state = this.getState();\n\n    const {\n      action,\n      index,\n      lifecycle,\n      origin = null,\n      size,\n      status,\n    } = {\n      ...state,\n      ...nextState,\n    };\n\n    this.store.set('action', action);\n    this.store.set('index', index);\n    this.store.set('lifecycle', lifecycle);\n    this.store.set('origin', origin);\n    this.store.set('size', size);\n    this.store.set('status', status);\n\n    if (initial) {\n      this.store.set('controlled', nextState.controlled);\n      this.store.set('continuous', nextState.continuous);\n    }\n\n    if (this.listener && this.hasUpdatedState(state)) {\n      this.listener(this.getState());\n    }\n  }\n\n  public addListener = (listener: Listener) => {\n    this.listener = listener;\n  };\n\n  public setSteps = (steps: Array<Step>) => {\n    const { size, status } = this.getState();\n    const state = {\n      size: steps.length,\n      status,\n    };\n\n    this.data.set('steps', steps);\n\n    if (status === STATUS.WAITING && !size && steps.length) {\n      state.status = STATUS.RUNNING;\n    }\n\n    this.setState(state);\n  };\n\n  public getHelpers(): StoreHelpers {\n    return {\n      close: this.close,\n      go: this.go,\n      info: this.info,\n      next: this.next,\n      open: this.open,\n      prev: this.prev,\n      reset: this.reset,\n      skip: this.skip,\n    };\n  }\n\n  public getPopper = (name: 'beacon' | 'tooltip'): PopperData | null => {\n    if (name === 'beacon') {\n      return this.beaconPopper;\n    }\n\n    return this.tooltipPopper;\n  };\n\n  public setPopper = (name: 'beacon' | 'tooltip', popper: PopperData) => {\n    if (name === 'beacon') {\n      this.beaconPopper = popper;\n    } else {\n      this.tooltipPopper = popper;\n    }\n  };\n\n  public cleanupPoppers = () => {\n    this.beaconPopper = null;\n    this.tooltipPopper = null;\n  };\n\n  public close = (origin: Origin | null = null) => {\n    const { index, status } = this.getState();\n\n    if (status !== STATUS.RUNNING) {\n      return;\n    }\n\n    this.setState({\n      ...this.getNextState({ action: ACTIONS.CLOSE, index: index + 1, origin }),\n    });\n  };\n\n  public go = (nextIndex: number) => {\n    const { controlled, status } = this.getState();\n\n    if (controlled || status !== STATUS.RUNNING) {\n      return;\n    }\n\n    const step = this.getSteps()[nextIndex];\n\n    this.setState({\n      ...this.getNextState({ action: ACTIONS.GO, index: nextIndex }),\n      status: step ? status : STATUS.FINISHED,\n    });\n  };\n\n  public info = (): State => this.getState();\n\n  public next = () => {\n    const { index, status } = this.getState();\n\n    if (status !== STATUS.RUNNING) {\n      return;\n    }\n\n    this.setState(this.getNextState({ action: ACTIONS.NEXT, index: index + 1 }));\n  };\n\n  public open = () => {\n    const { status } = this.getState();\n\n    if (status !== STATUS.RUNNING) {\n      return;\n    }\n\n    this.setState({\n      ...this.getNextState({ action: ACTIONS.UPDATE, lifecycle: LIFECYCLE.TOOLTIP }),\n    });\n  };\n\n  public prev = () => {\n    const { index, status } = this.getState();\n\n    if (status !== STATUS.RUNNING) {\n      return;\n    }\n\n    this.setState({\n      ...this.getNextState({ action: ACTIONS.PREV, index: index - 1 }),\n    });\n  };\n\n  public reset = (restart = false) => {\n    const { controlled } = this.getState();\n\n    if (controlled) {\n      return;\n    }\n\n    this.setState({\n      ...this.getNextState({ action: ACTIONS.RESET, index: 0 }),\n      status: restart ? STATUS.RUNNING : STATUS.READY,\n    });\n  };\n\n  public skip = () => {\n    const { status } = this.getState();\n\n    if (status !== STATUS.RUNNING) {\n      return;\n    }\n\n    this.setState({\n      action: ACTIONS.SKIP,\n      lifecycle: LIFECYCLE.INIT,\n      status: STATUS.SKIPPED,\n    });\n  };\n\n  public start = (nextIndex?: number) => {\n    const { index, size } = this.getState();\n\n    this.setState({\n      ...this.getNextState(\n        {\n          action: ACTIONS.START,\n          index: is.number(nextIndex) ? nextIndex : index,\n        },\n        true,\n      ),\n      status: size ? STATUS.RUNNING : STATUS.WAITING,\n    });\n  };\n\n  public stop = (advance = false) => {\n    const { index, status } = this.getState();\n\n    if (([STATUS.FINISHED, STATUS.SKIPPED] as Array<Status>).includes(status)) {\n      return;\n    }\n\n    this.setState({\n      ...this.getNextState({ action: ACTIONS.STOP, index: index + (advance ? 1 : 0) }),\n      status: STATUS.PAUSED,\n    });\n  };\n\n  public update = (state: Partial<State>) => {\n    if (!hasValidKeys(state, validKeys)) {\n      throw new Error(`State is not valid. Valid keys: ${validKeys.join(', ')}`);\n    }\n\n    this.setState({\n      ...this.getNextState(\n        {\n          ...this.getState(),\n          ...state,\n          action: state.action ?? ACTIONS.UPDATE,\n          origin: state.origin ?? null,\n        },\n        true,\n      ),\n    });\n  };\n}\n\nexport type StoreInstance = ReturnType<typeof createStore>;\n\nexport default function createStore(options?: StoreOptions) {\n  return new Store(options);\n}\n", "import * as React from 'react';\nimport treeChanges from 'tree-changes';\n\nimport {\n  getClientRect,\n  getDocumentHeight,\n  getElement,\n  getElementPosition,\n  getScrollParent,\n  hasCustomScrollParent,\n  hasPosition,\n} from '~/modules/dom';\nimport { getBrowser, isLegacy, log } from '~/modules/helpers';\n\nimport { LIFECYCLE } from '~/literals';\n\nimport { Lifecycle, OverlayProps } from '~/types';\n\nimport Spotlight from './Spotlight';\n\ninterface State {\n  isScrolling: boolean;\n  mouseOverSpotlight: boolean;\n  showSpotlight: boolean;\n}\n\ninterface SpotlightStyles extends React.CSSProperties {\n  height: number;\n  left: number;\n  top: number;\n  width: number;\n}\n\nexport default class JoyrideOverlay extends React.Component<OverlayProps, State> {\n  isActive = false;\n  resizeTimeout?: number;\n  scrollTimeout?: number;\n  scrollParent?: Document | Element;\n  state = {\n    isScrolling: false,\n    mouseOverSpotlight: false,\n    showSpotlight: true,\n  };\n\n  componentDidMount() {\n    const { debug, disableScrolling, disableScrollParentFix = false, target } = this.props;\n    const element = getElement(target);\n\n    this.scrollParent = getScrollParent(element ?? document.body, disableScrollParentFix, true);\n    this.isActive = true;\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!disableScrolling && hasCustomScrollParent(element, true)) {\n        log({\n          title: 'step has a custom scroll parent and can cause trouble with scrolling',\n          data: [{ key: 'parent', value: this.scrollParent }],\n          debug,\n        });\n      }\n    }\n\n    window.addEventListener('resize', this.handleResize);\n  }\n\n  componentDidUpdate(previousProps: OverlayProps) {\n    const { disableScrollParentFix, lifecycle, spotlightClicks, target } = this.props;\n    const { changed } = treeChanges(previousProps, this.props);\n\n    if (changed('target') || changed('disableScrollParentFix')) {\n      const element = getElement(target);\n\n      this.scrollParent = getScrollParent(element ?? document.body, disableScrollParentFix, true);\n    }\n\n    if (changed('lifecycle', LIFECYCLE.TOOLTIP)) {\n      this.scrollParent?.addEventListener('scroll', this.handleScroll, { passive: true });\n\n      setTimeout(() => {\n        const { isScrolling } = this.state;\n\n        if (!isScrolling) {\n          this.updateState({ showSpotlight: true });\n        }\n      }, 100);\n    }\n\n    if (changed('spotlightClicks') || changed('disableOverlay') || changed('lifecycle')) {\n      if (spotlightClicks && lifecycle === LIFECYCLE.TOOLTIP) {\n        window.addEventListener('mousemove', this.handleMouseMove, false);\n      } else if (lifecycle !== LIFECYCLE.TOOLTIP) {\n        window.removeEventListener('mousemove', this.handleMouseMove);\n      }\n    }\n  }\n\n  componentWillUnmount() {\n    this.isActive = false;\n\n    window.removeEventListener('mousemove', this.handleMouseMove);\n    window.removeEventListener('resize', this.handleResize);\n\n    clearTimeout(this.resizeTimeout);\n    clearTimeout(this.scrollTimeout);\n    this.scrollParent?.removeEventListener('scroll', this.handleScroll);\n  }\n\n  hideSpotlight = () => {\n    const { continuous, disableOverlay, lifecycle } = this.props;\n    const hiddenLifecycles = [\n      LIFECYCLE.INIT,\n      LIFECYCLE.BEACON,\n      LIFECYCLE.COMPLETE,\n      LIFECYCLE.ERROR,\n    ] as Lifecycle[];\n\n    return (\n      disableOverlay ||\n      (continuous ? hiddenLifecycles.includes(lifecycle) : lifecycle !== LIFECYCLE.TOOLTIP)\n    );\n  };\n\n  get overlayStyles() {\n    const { mouseOverSpotlight } = this.state;\n    const { disableOverlayClose, placement, styles } = this.props;\n\n    let baseStyles = styles.overlay;\n\n    if (isLegacy()) {\n      baseStyles = placement === 'center' ? styles.overlayLegacyCenter : styles.overlayLegacy;\n    }\n\n    return {\n      cursor: disableOverlayClose ? 'default' : 'pointer',\n      height: getDocumentHeight(),\n      pointerEvents: mouseOverSpotlight ? 'none' : 'auto',\n      ...baseStyles,\n    } as React.CSSProperties;\n  }\n\n  get spotlightStyles(): SpotlightStyles {\n    const { showSpotlight } = this.state;\n    const {\n      disableScrollParentFix = false,\n      spotlightClicks,\n      spotlightPadding = 0,\n      styles,\n      target,\n    } = this.props;\n    const element = getElement(target);\n    const elementRect = getClientRect(element);\n    const isFixedTarget = hasPosition(element);\n    const top = getElementPosition(element, spotlightPadding, disableScrollParentFix);\n\n    return {\n      ...(isLegacy() ? styles.spotlightLegacy : styles.spotlight),\n      height: Math.round((elementRect?.height ?? 0) + spotlightPadding * 2),\n      left: Math.round((elementRect?.left ?? 0) - spotlightPadding),\n      opacity: showSpotlight ? 1 : 0,\n      pointerEvents: spotlightClicks ? 'none' : 'auto',\n      position: isFixedTarget ? 'fixed' : 'absolute',\n      top,\n      transition: 'opacity 0.2s',\n      width: Math.round((elementRect?.width ?? 0) + spotlightPadding * 2),\n    } satisfies React.CSSProperties;\n  }\n\n  handleMouseMove = (event: MouseEvent) => {\n    const { mouseOverSpotlight } = this.state;\n    const { height, left, position, top, width } = this.spotlightStyles;\n\n    const offsetY = position === 'fixed' ? event.clientY : event.pageY;\n    const offsetX = position === 'fixed' ? event.clientX : event.pageX;\n    const inSpotlightHeight = offsetY >= top && offsetY <= top + height;\n    const inSpotlightWidth = offsetX >= left && offsetX <= left + width;\n    const inSpotlight = inSpotlightWidth && inSpotlightHeight;\n\n    if (inSpotlight !== mouseOverSpotlight) {\n      this.updateState({ mouseOverSpotlight: inSpotlight });\n    }\n  };\n\n  handleScroll = () => {\n    const { target } = this.props;\n    const element = getElement(target);\n\n    if (this.scrollParent !== document) {\n      const { isScrolling } = this.state;\n\n      if (!isScrolling) {\n        this.updateState({ isScrolling: true, showSpotlight: false });\n      }\n\n      clearTimeout(this.scrollTimeout);\n\n      this.scrollTimeout = window.setTimeout(() => {\n        this.updateState({ isScrolling: false, showSpotlight: true });\n      }, 50);\n    } else if (hasPosition(element, 'sticky')) {\n      this.updateState({});\n    }\n  };\n\n  handleResize = () => {\n    clearTimeout(this.resizeTimeout);\n\n    this.resizeTimeout = window.setTimeout(() => {\n      if (!this.isActive) {\n        return;\n      }\n\n      this.forceUpdate();\n    }, 100);\n  };\n\n  updateState(state: Partial<State>) {\n    if (!this.isActive) {\n      return;\n    }\n\n    this.setState(previousState => ({ ...previousState, ...state }));\n  }\n\n  render() {\n    const { showSpotlight } = this.state;\n    const { onClickOverlay, placement } = this.props;\n    const { hideSpotlight, overlayStyles, spotlightStyles } = this;\n\n    if (hideSpotlight()) {\n      return null;\n    }\n\n    let spotlight = placement !== 'center' && showSpotlight && (\n      <Spotlight styles={spotlightStyles} />\n    );\n\n    // Hack for Safari bug with mix-blend-mode with z-index\n    if (getBrowser() === 'safari') {\n      const { mixBlendMode, zIndex, ...safariOverlay } = overlayStyles;\n\n      spotlight = <div style={{ ...safariOverlay }}>{spotlight}</div>;\n      delete overlayStyles.backgroundColor;\n    }\n\n    return (\n      <div\n        className=\"react-joyride__overlay\"\n        data-test-id=\"overlay\"\n        onClick={onClickOverlay}\n        role=\"presentation\"\n        style={overlayStyles}\n      >\n        {spotlight}\n      </div>\n    );\n  }\n}\n", "import * as React from 'react';\n\ninterface Props {\n  styles: React.CSSProperties;\n}\n\nfunction JoyrideSpotlight({ styles }: Props) {\n  return (\n    <div\n      key=\"JoyrideSpotlight\"\n      className=\"react-joyride__spotlight\"\n      data-test-id=\"spotlight\"\n      style={styles}\n    />\n  );\n}\n\nexport default JoyrideSpotlight;\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\n\nimport { canUseDOM } from '~/modules/dom';\nimport { isReact16 } from '~/modules/helpers';\n\ninterface Props {\n  children: React.ReactElement;\n  id: string;\n}\n\nexport default class JoyridePortal extends React.Component<Props> {\n  node: HTMLElement | null = null;\n\n  componentDidMount() {\n    const { id } = this.props;\n\n    if (!canUseDOM()) {\n      return;\n    }\n\n    this.node = document.createElement('div');\n    this.node.id = id;\n\n    document.body.appendChild(this.node);\n\n    if (!isReact16) {\n      this.renderReact15();\n    }\n  }\n\n  componentDidUpdate() {\n    if (!canUseDOM()) {\n      return;\n    }\n\n    if (!isReact16) {\n      this.renderReact15();\n    }\n  }\n\n  componentWillUnmount() {\n    if (!canUseDOM() || !this.node) {\n      return;\n    }\n\n    if (!isReact16) {\n      // eslint-disable-next-line react/no-deprecated\n      ReactDOM.unmountComponentAtNode(this.node);\n    }\n\n    if (this.node.parentNode === document.body) {\n      document.body.removeChild(this.node);\n      this.node = null;\n    }\n  }\n\n  renderReact15() {\n    if (!canUseDOM()) {\n      return;\n    }\n\n    const { children } = this.props;\n\n    if (this.node) {\n      ReactDOM.unstable_renderSubtreeIntoContainer(this, children, this.node);\n    }\n  }\n\n  renderReact16() {\n    if (!canUseDOM() || !isReact16) {\n      return null;\n    }\n\n    const { children } = this.props;\n\n    if (!this.node) {\n      return null;\n    }\n\n    return ReactDOM.createPortal(children, this.node);\n  }\n\n  render() {\n    if (!isReact16) {\n      return null;\n    }\n\n    return this.renderReact16();\n  }\n}\n", "import * as React from 'react';\nimport Floater, { Props as FloaterP<PERSON>, RenderProps } from 'react-floater';\nimport is from 'is-lite';\nimport treeChanges from 'tree-changes';\n\nimport { getElement, isElementVisible } from '~/modules/dom';\nimport { hideBeacon, log } from '~/modules/helpers';\nimport Scope from '~/modules/scope';\nimport { validateStep } from '~/modules/step';\n\nimport { ACTIONS, EVENTS, LIFECYCLE, STATUS } from '~/literals';\n\nimport { StepProps } from '~/types';\n\nimport Beacon from './Beacon';\nimport Tooltip from './Tooltip/index';\n\nexport default class JoyrideStep extends React.Component<StepProps> {\n  scope: Scope | null = null;\n  tooltip: HTMLElement | null = null;\n\n  componentDidMount() {\n    const { debug, index } = this.props;\n\n    log({\n      title: `step:${index}`,\n      data: [{ key: 'props', value: this.props }],\n      debug,\n    });\n  }\n\n  componentDidUpdate(previousProps: StepProps) {\n    const {\n      action,\n      callback,\n      continuous,\n      controlled,\n      debug,\n      helpers,\n      index,\n      lifecycle,\n      shouldScroll,\n      status,\n      step,\n      store,\n    } = this.props;\n    const { changed, changedFrom } = treeChanges(previousProps, this.props);\n    const state = helpers.info();\n\n    const skipBeacon =\n      continuous && action !== ACTIONS.CLOSE && (index > 0 || action === ACTIONS.PREV);\n    const hasStoreChanged =\n      changed('action') || changed('index') || changed('lifecycle') || changed('status');\n    const isInitial = changedFrom('lifecycle', [LIFECYCLE.TOOLTIP, LIFECYCLE.INIT], LIFECYCLE.INIT);\n    const isAfterAction = changed('action', [\n      ACTIONS.NEXT,\n      ACTIONS.PREV,\n      ACTIONS.SKIP,\n      ACTIONS.CLOSE,\n    ]);\n    const isControlled = controlled && index === previousProps.index;\n\n    if (isAfterAction && (isInitial || isControlled)) {\n      callback({\n        ...state,\n        index: previousProps.index,\n        lifecycle: LIFECYCLE.COMPLETE,\n        step: previousProps.step,\n        type: EVENTS.STEP_AFTER,\n      });\n    }\n\n    if (\n      step.placement === 'center' &&\n      status === STATUS.RUNNING &&\n      changed('index') &&\n      action !== ACTIONS.START &&\n      lifecycle === LIFECYCLE.INIT\n    ) {\n      store.update({ lifecycle: LIFECYCLE.READY });\n    }\n\n    if (hasStoreChanged) {\n      const element = getElement(step.target);\n      const elementExists = !!element;\n      const hasRenderedTarget = elementExists && isElementVisible(element);\n\n      if (hasRenderedTarget) {\n        if (\n          changedFrom('status', STATUS.READY, STATUS.RUNNING) ||\n          changedFrom('lifecycle', LIFECYCLE.INIT, LIFECYCLE.READY)\n        ) {\n          callback({\n            ...state,\n            step,\n            type: EVENTS.STEP_BEFORE,\n          });\n        }\n      } else {\n        // eslint-disable-next-line no-console\n        console.warn(elementExists ? 'Target not visible' : 'Target not mounted', step);\n        callback({\n          ...state,\n          type: EVENTS.TARGET_NOT_FOUND,\n          step,\n        });\n\n        if (!controlled) {\n          store.update({ index: index + (action === ACTIONS.PREV ? -1 : 1) });\n        }\n      }\n    }\n\n    if (changedFrom('lifecycle', LIFECYCLE.INIT, LIFECYCLE.READY)) {\n      store.update({\n        lifecycle: hideBeacon(step) || skipBeacon ? LIFECYCLE.TOOLTIP : LIFECYCLE.BEACON,\n      });\n    }\n\n    if (changed('index')) {\n      log({\n        title: `step:${lifecycle}`,\n        data: [{ key: 'props', value: this.props }],\n        debug,\n      });\n    }\n\n    if (changed('lifecycle', LIFECYCLE.BEACON)) {\n      callback({\n        ...state,\n        step,\n        type: EVENTS.BEACON,\n      });\n    }\n\n    if (changed('lifecycle', LIFECYCLE.TOOLTIP)) {\n      callback({\n        ...state,\n        step,\n        type: EVENTS.TOOLTIP,\n      });\n\n      if (shouldScroll && this.tooltip) {\n        this.scope = new Scope(this.tooltip, { selector: '[data-action=primary]' });\n        this.scope.setFocus();\n      }\n    }\n\n    if (changedFrom('lifecycle', [LIFECYCLE.TOOLTIP, LIFECYCLE.INIT], LIFECYCLE.INIT)) {\n      this.scope?.removeScope();\n      store.cleanupPoppers();\n    }\n  }\n\n  componentWillUnmount() {\n    this.scope?.removeScope();\n  }\n\n  /**\n   * Beacon click/hover event listener\n   */\n  handleClickHoverBeacon = (event: React.MouseEvent<HTMLElement>) => {\n    const { step, store } = this.props;\n\n    if (event.type === 'mouseenter' && step.event !== 'hover') {\n      return;\n    }\n\n    store.update({ lifecycle: LIFECYCLE.TOOLTIP });\n  };\n\n  setTooltipRef = (element: HTMLElement) => {\n    this.tooltip = element;\n  };\n\n  setPopper: FloaterProps['getPopper'] = (popper, type) => {\n    const { action, lifecycle, step, store } = this.props;\n\n    if (type === 'wrapper') {\n      store.setPopper('beacon', popper);\n    } else {\n      store.setPopper('tooltip', popper);\n    }\n\n    if (\n      store.getPopper('beacon') &&\n      (store.getPopper('tooltip') || step.placement === 'center') &&\n      lifecycle === LIFECYCLE.INIT\n    ) {\n      store.update({\n        action,\n        lifecycle: LIFECYCLE.READY,\n      });\n    }\n\n    if (step.floaterProps?.getPopper) {\n      step.floaterProps.getPopper(popper, type);\n    }\n  };\n\n  get open() {\n    const { lifecycle, step } = this.props;\n\n    return hideBeacon(step) || lifecycle === LIFECYCLE.TOOLTIP;\n  }\n\n  renderTooltip = (renderProps: RenderProps) => {\n    const { continuous, helpers, index, size, step } = this.props;\n\n    return (\n      <Tooltip\n        continuous={continuous}\n        helpers={helpers}\n        index={index}\n        isLastStep={index + 1 === size}\n        setTooltipRef={this.setTooltipRef}\n        size={size}\n        step={step}\n        {...renderProps}\n      />\n    );\n  };\n\n  render() {\n    const { continuous, debug, index, nonce, shouldScroll, size, step } = this.props;\n    const target = getElement(step.target);\n\n    if (!validateStep(step) || !is.domElement(target)) {\n      return null;\n    }\n\n    return (\n      <div key={`JoyrideStep-${index}`} className=\"react-joyride__step\">\n        <Floater\n          {...step.floaterProps}\n          component={this.renderTooltip}\n          debug={debug}\n          getPopper={this.setPopper}\n          id={`react-joyride-step-${index}`}\n          open={this.open}\n          placement={step.placement}\n          target={step.target}\n        >\n          <Beacon\n            beaconComponent={step.beaconComponent}\n            continuous={continuous}\n            index={index}\n            isLastStep={index + 1 === size}\n            locale={step.locale}\n            nonce={nonce}\n            onClickOrHover={this.handleClickHoverBeacon}\n            shouldFocus={shouldScroll}\n            size={size}\n            step={step}\n            styles={step.styles}\n          />\n        </Floater>\n      </div>\n    );\n  }\n}\n", "interface ScopeOptions {\n  code?: string;\n  selector: string | null;\n}\n\nexport default class Scope {\n  element: HTMLElement;\n  options: ScopeOptions;\n\n  constructor(element: HTMLElement, options: ScopeOptions) {\n    if (!(element instanceof HTMLElement)) {\n      throw new TypeError('Invalid parameter: element must be an HTMLElement');\n    }\n\n    this.element = element;\n    this.options = options;\n\n    window.addEventListener('keydown', this.handleKeyDown, false);\n\n    this.setFocus();\n  }\n\n  canBeTabbed = (element: HTMLElement): boolean => {\n    const { tabIndex } = element;\n\n    if (tabIndex === null || tabIndex < 0) {\n      return false;\n    }\n\n    return this.canHaveFocus(element);\n  };\n\n  canHaveFocus = (element: HTMLElement): boolean => {\n    const validTabNodes = /input|select|textarea|button|object/;\n    const nodeName = element.nodeName.toLowerCase();\n\n    const isValid =\n      (validTabNodes.test(nodeName) && !element.getAttribute('disabled')) ||\n      (nodeName === 'a' && !!element.getAttribute('href'));\n\n    return isValid && this.isVisible(element);\n  };\n\n  findValidTabElements = (): Array<HTMLElement> =>\n    [].slice.call(this.element.querySelectorAll('*'), 0).filter(this.canBeTabbed);\n\n  handleKeyDown = (event: KeyboardEvent) => {\n    const { code = 'Tab' } = this.options;\n\n    if (event.code === code) {\n      this.interceptTab(event);\n    }\n  };\n\n  interceptTab = (event: KeyboardEvent) => {\n    event.preventDefault();\n    const elements = this.findValidTabElements();\n    const { shiftKey } = event;\n\n    if (!elements.length) {\n      return;\n    }\n\n    let x = document.activeElement ? elements.indexOf(document.activeElement as HTMLElement) : 0;\n\n    if (x === -1 || (!shiftKey && x + 1 === elements.length)) {\n      x = 0;\n    } else if (shiftKey && x === 0) {\n      x = elements.length - 1;\n    } else {\n      x += shiftKey ? -1 : 1;\n    }\n\n    elements[x].focus();\n  };\n\n  // eslint-disable-next-line class-methods-use-this\n  isHidden = (element: HTMLElement) => {\n    const noSize = element.offsetWidth <= 0 && element.offsetHeight <= 0;\n    const style = window.getComputedStyle(element);\n\n    if (noSize && !element.innerHTML) {\n      return true;\n    }\n\n    return (\n      (noSize && style.getPropertyValue('overflow') !== 'visible') ||\n      style.getPropertyValue('display') === 'none'\n    );\n  };\n\n  isVisible = (element: HTMLElement): boolean => {\n    let parentElement: HTMLElement | null = element;\n\n    while (parentElement) {\n      if (parentElement instanceof HTMLElement) {\n        if (parentElement === document.body) {\n          break;\n        }\n\n        if (this.isHidden(parentElement)) {\n          return false;\n        }\n\n        parentElement = parentElement.parentNode as HTMLElement;\n      }\n    }\n\n    return true;\n  };\n\n  removeScope = () => {\n    window.removeEventListener('keydown', this.handleKeyDown);\n  };\n\n  checkFocus = (target: HTMLElement) => {\n    if (document.activeElement !== target) {\n      target.focus();\n      window.requestAnimationFrame(() => this.checkFocus(target));\n    }\n  };\n\n  setFocus = () => {\n    const { selector } = this.options;\n\n    if (!selector) {\n      return;\n    }\n\n    const target = this.element.querySelector(selector);\n\n    if (target) {\n      window.requestAnimationFrame(() => this.checkFocus(target as HTMLElement));\n    }\n  };\n}\n", "import * as React from 'react';\nimport is from 'is-lite';\n\nimport { getReactNodeText } from '~/modules/helpers';\n\nimport { BeaconProps } from '~/types';\n\nexport default class JoyrideBeacon extends React.Component<BeaconProps> {\n  private beacon: HTMLElement | null = null;\n\n  constructor(props: BeaconProps) {\n    super(props);\n\n    if (props.beaconComponent) {\n      return;\n    }\n\n    const head = document.head || document.getElementsByTagName('head')[0];\n    const style = document.createElement('style');\n\n    style.id = 'joyride-beacon-animation';\n\n    if (props.nonce) {\n      style.setAttribute('nonce', props.nonce);\n    }\n\n    const css = `\n        @keyframes joyride-beacon-inner {\n          20% {\n            opacity: 0.9;\n          }\n        \n          90% {\n            opacity: 0.7;\n          }\n        }\n        \n        @keyframes joyride-beacon-outer {\n          0% {\n            transform: scale(1);\n          }\n        \n          45% {\n            opacity: 0.7;\n            transform: scale(0.75);\n          }\n        \n          100% {\n            opacity: 0.9;\n            transform: scale(1);\n          }\n        }\n      `;\n\n    style.appendChild(document.createTextNode(css));\n\n    head.appendChild(style);\n  }\n\n  componentDidMount() {\n    const { shouldFocus } = this.props;\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!is.domElement(this.beacon)) {\n        console.warn('beacon is not a valid DOM element'); // eslint-disable-line no-console\n      }\n    }\n\n    setTimeout(() => {\n      if (is.domElement(this.beacon) && shouldFocus) {\n        this.beacon.focus();\n      }\n    }, 0);\n  }\n\n  componentWillUnmount() {\n    const style = document.getElementById('joyride-beacon-animation');\n\n    if (style?.parentNode) {\n      style.parentNode.removeChild(style);\n    }\n  }\n\n  setBeaconRef = (c: HTMLElement | null) => {\n    this.beacon = c;\n  };\n\n  render() {\n    const {\n      beaconComponent,\n      continuous,\n      index,\n      isLastStep,\n      locale,\n      onClickOrHover,\n      size,\n      step,\n      styles,\n    } = this.props;\n    const title = getReactNodeText(locale.open);\n    const sharedProps = {\n      'aria-label': title,\n      onClick: onClickOrHover,\n      onMouseEnter: onClickOrHover,\n      ref: this.setBeaconRef,\n      title,\n    };\n    let component;\n\n    if (beaconComponent) {\n      const BeaconComponent = beaconComponent;\n\n      component = (\n        <BeaconComponent\n          continuous={continuous}\n          index={index}\n          isLastStep={isLastStep}\n          size={size}\n          step={step}\n          {...sharedProps}\n        />\n      );\n    } else {\n      component = (\n        <button\n          key=\"JoyrideBeacon\"\n          className=\"react-joyride__beacon\"\n          data-test-id=\"button-beacon\"\n          style={styles.beacon}\n          type=\"button\"\n          {...sharedProps}\n        >\n          <span style={styles.beaconInner} />\n          <span style={styles.beaconOuter} />\n        </button>\n      );\n    }\n\n    return component;\n  }\n}\n", "import * as React from 'react';\n\nimport { getReactNodeText, replaceLocaleContent } from '~/modules/helpers';\n\nimport { TooltipProps } from '~/types';\n\nimport Container from './Container';\n\nexport default class JoyrideTooltip extends React.Component<TooltipProps> {\n  handleClickBack = (event: React.MouseEvent<HTMLElement>) => {\n    event.preventDefault();\n    const { helpers } = this.props;\n\n    helpers.prev();\n  };\n\n  handleClickClose = (event: React.MouseEvent<HTMLElement>) => {\n    event.preventDefault();\n    const { helpers } = this.props;\n\n    helpers.close('button_close');\n  };\n\n  handleClickPrimary = (event: React.MouseEvent<HTMLElement>) => {\n    event.preventDefault();\n    const { continuous, helpers } = this.props;\n\n    if (!continuous) {\n      helpers.close('button_primary');\n\n      return;\n    }\n\n    helpers.next();\n  };\n\n  handleClickSkip = (event: React.MouseEvent<HTMLElement>) => {\n    event.preventDefault();\n    const { helpers } = this.props;\n\n    helpers.skip();\n  };\n\n  getElementsProps = () => {\n    const { continuous, index, isLastStep, setTooltipRef, size, step } = this.props;\n    const { back, close, last, next, nextLabelWithProgress, skip } = step.locale;\n\n    const backText = getReactNodeText(back);\n    const closeText = getReactNodeText(close);\n    const lastText = getReactNodeText(last);\n    const nextText = getReactNodeText(next);\n    const skipText = getReactNodeText(skip);\n\n    let primary = close;\n    let primaryText = closeText;\n\n    if (continuous) {\n      primary = next;\n      primaryText = nextText;\n\n      if (step.showProgress && !isLastStep) {\n        const labelWithProgress = getReactNodeText(nextLabelWithProgress, {\n          step: index + 1,\n          steps: size,\n        });\n\n        primary = replaceLocaleContent(nextLabelWithProgress, index + 1, size);\n        primaryText = labelWithProgress;\n      }\n\n      if (isLastStep) {\n        primary = last;\n        primaryText = lastText;\n      }\n    }\n\n    return {\n      backProps: {\n        'aria-label': backText,\n        children: back,\n        'data-action': 'back',\n        onClick: this.handleClickBack,\n        role: 'button',\n        title: backText,\n      },\n      closeProps: {\n        'aria-label': closeText,\n        children: close,\n        'data-action': 'close',\n        onClick: this.handleClickClose,\n        role: 'button',\n        title: closeText,\n      },\n      primaryProps: {\n        'aria-label': primaryText,\n        children: primary,\n        'data-action': 'primary',\n        onClick: this.handleClickPrimary,\n        role: 'button',\n        title: primaryText,\n      },\n      skipProps: {\n        'aria-label': skipText,\n        children: skip,\n        'data-action': 'skip',\n        onClick: this.handleClickSkip,\n        role: 'button',\n        title: skipText,\n      },\n      tooltipProps: {\n        'aria-modal': true,\n        ref: setTooltipRef,\n        role: 'alertdialog',\n      },\n    };\n  };\n\n  render() {\n    const { continuous, index, isLastStep, setTooltipRef, size, step } = this.props;\n    const { beaconComponent, tooltipComponent, ...cleanStep } = step;\n    let component;\n\n    if (tooltipComponent) {\n      const renderProps = {\n        ...this.getElementsProps(),\n        continuous,\n        index,\n        isLastStep,\n        size,\n        step: cleanStep,\n        setTooltipRef,\n      };\n\n      const TooltipComponent = tooltipComponent;\n\n      component = <TooltipComponent {...renderProps} />;\n    } else {\n      component = (\n        <Container\n          {...this.getElementsProps()}\n          continuous={continuous}\n          index={index}\n          isLastStep={isLastStep}\n          size={size}\n          step={step}\n        />\n      );\n    }\n\n    return component;\n  }\n}\n", "import * as React from 'react';\n\nimport { getReactNodeText } from '~/modules/helpers';\n\nimport { TooltipRenderProps } from '~/types';\n\nimport CloseButton from './CloseButton';\n\nfunction JoyrideTooltipContainer(props: TooltipRenderProps) {\n  const { backProps, closeProps, index, isLastStep, primaryProps, skipProps, step, tooltipProps } =\n    props;\n  const { content, hideBackButton, hideClose<PERSON><PERSON>on, hideFooter, showSkipButton, styles, title } =\n    step;\n  const output: Record<string, React.ReactNode> = {};\n\n  output.primary = (\n    <button\n      data-test-id=\"button-primary\"\n      style={styles.buttonNext}\n      type=\"button\"\n      {...primaryProps}\n    />\n  );\n\n  if (showSkipButton && !isLastStep) {\n    output.skip = (\n      <button\n        aria-live=\"off\"\n        data-test-id=\"button-skip\"\n        style={styles.buttonSkip}\n        type=\"button\"\n        {...skipProps}\n      />\n    );\n  }\n\n  if (!hideBackButton && index > 0) {\n    output.back = (\n      <button data-test-id=\"button-back\" style={styles.buttonBack} type=\"button\" {...backProps} />\n    );\n  }\n\n  output.close = !hideCloseButton && (\n    <CloseButton data-test-id=\"button-close\" styles={styles.buttonClose} {...closeProps} />\n  );\n\n  return (\n    <div\n      key=\"JoyrideTooltip\"\n      aria-label={getReactNodeText(title ?? content)}\n      className=\"react-joyride__tooltip\"\n      style={styles.tooltip}\n      {...tooltipProps}\n    >\n      <div style={styles.tooltipContainer}>\n        {title && (\n          <h1 aria-label={getReactNodeText(title)} style={styles.tooltipTitle}>\n            {title}\n          </h1>\n        )}\n        <div style={styles.tooltipContent}>{content}</div>\n      </div>\n      {!hideFooter && (\n        <div style={styles.tooltipFooter}>\n          <div style={styles.tooltipFooterSpacer}>{output.skip}</div>\n          {output.back}\n          {output.primary}\n        </div>\n      )}\n      {output.close}\n    </div>\n  );\n}\n\nexport default JoyrideTooltipContainer;\n", "import React, { CSSProperties } from 'react';\n\ninterface Props {\n  styles: CSSProperties;\n}\n\nfunction JoyrideTooltipCloseButton({ styles, ...props }: Props) {\n  const { color, height, width, ...style } = styles;\n\n  return (\n    <button style={style} type=\"button\" {...props}>\n      <svg\n        height={typeof height === 'number' ? `${height}px` : height}\n        preserveAspectRatio=\"xMidYMid\"\n        version=\"1.1\"\n        viewBox=\"0 0 18 18\"\n        width={typeof width === 'number' ? `${width}px` : width}\n        xmlns=\"http://www.w3.org/2000/svg\"\n      >\n        <g>\n          <path\n            d=\"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z\"\n            fill={color}\n          />\n        </g>\n      </svg>\n    </button>\n  );\n}\n\nexport default JoyrideTooltipCloseButton;\n", "import { AnyObject, Primitive } from './types';\n\n/**\n * Checks if the value is of a specified type.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isOfType<T extends Primitive | Function>(type: string) {\n  // eslint-disable-next-line valid-typeof\n  return (value: unknown): value is T => typeof value === type;\n}\n\n/**\n * Checks if the value is a JavaScript function.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport const isFunction = isOfType<Function>('function');\n\n/**\n * Check if the value is null.\n */\nexport const isNull = (value: unknown): value is null => {\n  return value === null;\n};\n\n/**\n * Checks if the input is a regular expression.\n */\nexport const isRegex = (value: unknown): value is RegExp => {\n  return Object.prototype.toString.call(value).slice(8, -1) === 'RegExp';\n};\n\n/**\n * Checks if the value is an object.\n */\nexport const isObject = (value: unknown): value is AnyObject => {\n  return !isUndefined(value) && !isNull(value) && (isFunction(value) || typeof value === 'object');\n};\n\n/**\n * Checks if the value is undefined.\n */\nexport const isUndefined = isOfType<undefined>('undefined');\n", "import { isObject, isRegex } from './helpers';\n\n/**\n * Check if arrays are equal.\n */\nfunction equalArray(left: unknown[], right: unknown[]) {\n  const { length } = left;\n\n  if (length !== right.length) {\n    return false;\n  }\n\n  for (let index = length; index-- !== 0; ) {\n    if (!equal(left[index], right[index])) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Check if array buffers are equal.\n */\nfunction equalArrayBuffer(left: ArrayBufferView, right: ArrayBufferView) {\n  if (left.byteLength !== right.byteLength) {\n    return false;\n  }\n\n  const view1 = new DataView(left.buffer);\n  const view2 = new DataView(right.buffer);\n\n  let index = left.byteLength;\n\n  while (index--) {\n    if (view1.getUint8(index) !== view2.getUint8(index)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Check if maps are equal.\n */\nfunction equalMap(left: Map<unknown, unknown>, right: Map<unknown, unknown>) {\n  if (left.size !== right.size) {\n    return false;\n  }\n\n  for (const index of left.entries()) {\n    if (!right.has(index[0])) {\n      return false;\n    }\n  }\n\n  for (const index of left.entries()) {\n    if (!equal(index[1], right.get(index[0]))) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Check if sets are equal.\n */\nfunction equalSet(left: Set<unknown>, right: Set<unknown>) {\n  if (left.size !== right.size) {\n    return false;\n  }\n\n  for (const index of left.entries()) {\n    if (!right.has(index[0])) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Checks if two values are equal.\n */\nexport default function equal(left: unknown, right: unknown) {\n  if (left === right) {\n    return true;\n  }\n\n  if (left && isObject(left) && right && isObject(right)) {\n    if (left.constructor !== right.constructor) {\n      return false;\n    }\n\n    if (Array.isArray(left) && Array.isArray(right)) {\n      return equalArray(left, right);\n    }\n\n    if (left instanceof Map && right instanceof Map) {\n      return equalMap(left, right);\n    }\n\n    if (left instanceof Set && right instanceof Set) {\n      return equalSet(left, right);\n    }\n\n    if (ArrayBuffer.isView(left) && ArrayBuffer.isView(right)) {\n      return equalArrayBuffer(left, right);\n    }\n\n    if (isRegex(left) && isRegex(right)) {\n      return left.source === right.source && left.flags === right.flags;\n    }\n\n    if (left.valueOf !== Object.prototype.valueOf) {\n      return left.valueOf() === right.valueOf();\n    }\n\n    if (left.toString !== Object.prototype.toString) {\n      return left.toString() === right.toString();\n    }\n\n    const leftKeys = Object.keys(left);\n    const rightKeys = Object.keys(right);\n\n    if (leftKeys.length !== rightKeys.length) {\n      return false;\n    }\n\n    for (let index = leftKeys.length; index-- !== 0; ) {\n      if (!Object.prototype.hasOwnProperty.call(right, leftKeys[index])) {\n        return false;\n      }\n    }\n\n    for (let index = leftKeys.length; index-- !== 0; ) {\n      const key = leftKeys[index];\n\n      if (key === '_owner' && left.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner.\n        //  _owner contains circular references\n        // and is not needed when comparing the actual elements (and not their owners)\n        // eslint-disable-next-line no-continue\n        continue;\n      }\n\n      if (!equal(left[key], right[key])) {\n        return false;\n      }\n    }\n\n    return true;\n  }\n\n  if (Number.isNaN(left) && Number.isNaN(right)) {\n    return true;\n  }\n\n  return left === right;\n}\n", "/* eslint-disable @typescript-eslint/ban-types */\nimport type { ObjectTypes, Primitive, PrimitiveTypes } from './types';\n\nexport const objectTypes = [\n  'Array',\n  'ArrayBuffer',\n  'AsyncFunction',\n  'AsyncGenerator',\n  'AsyncGeneratorFunction',\n  'Date',\n  'Error',\n  'Function',\n  'Generator',\n  'GeneratorFunction',\n  'HTMLElement',\n  'Map',\n  'Object',\n  'Promise',\n  'RegExp',\n  'Set',\n  'WeakMap',\n  'WeakSet',\n] as const;\n\nexport const primitiveTypes = [\n  'bigint',\n  'boolean',\n  'null',\n  'number',\n  'string',\n  'symbol',\n  'undefined',\n] as const;\n\nexport function getObjectType(value: unknown): ObjectTypes | undefined {\n  const objectTypeName = Object.prototype.toString.call(value).slice(8, -1);\n\n  if (/HTML\\w+Element/.test(objectTypeName)) {\n    return 'HTMLElement';\n  }\n\n  if (isObjectType(objectTypeName)) {\n    return objectTypeName;\n  }\n\n  return undefined;\n}\n\nexport function isObjectOfType<T>(type: string) {\n  return (value: unknown): value is T => getObjectType(value) === type;\n}\n\nexport function isObjectType(name: unknown): name is ObjectTypes {\n  return objectTypes.includes(name as ObjectTypes);\n}\n\nexport function isOfType<T extends Primitive | Function>(type: string) {\n  // eslint-disable-next-line valid-typeof\n  return (value: unknown): value is T => typeof value === type;\n}\n\nexport function isPrimitiveType(name: unknown): name is PrimitiveTypes {\n  return primitiveTypes.includes(name as PrimitiveTypes);\n}\n", "/* eslint-disable @typescript-eslint/ban-types */\nimport { getObjectType, isObjectOfType, isOfType, isPrimitiveType } from './helpers';\nimport type { Class, PlainObject, Primitive, TypeName } from './types';\n\nconst DOM_PROPERTIES_TO_CHECK: Array<keyof HTMLElement> = [\n  'innerHTML',\n  'ownerDocument',\n  'style',\n  'attributes',\n  'nodeValue',\n];\n\nfunction is(value: unknown): TypeName {\n  if (value === null) {\n    return 'null';\n  }\n\n  switch (typeof value) {\n    case 'bigint':\n      return 'bigint';\n    case 'boolean':\n      return 'boolean';\n    case 'number':\n      return 'number';\n    case 'string':\n      return 'string';\n    case 'symbol':\n      return 'symbol';\n    case 'undefined':\n      return 'undefined';\n    default:\n  }\n\n  if (is.array(value)) {\n    return 'Array';\n  }\n\n  if (is.plainFunction(value)) {\n    return 'Function';\n  }\n\n  const tagType = getObjectType(value);\n\n  if (tagType) {\n    return tagType;\n  }\n  /* c8 ignore next 3 */\n\n  return 'Object';\n}\n\nis.array = Array.isArray;\n\nis.arrayOf = (target: unknown[], predicate: (v: unknown) => boolean): boolean => {\n  if (!is.array(target) && !is.function(predicate)) {\n    return false;\n  }\n\n  return target.every(d => predicate(d));\n};\n\nis.asyncGeneratorFunction = (value: unknown): value is (...arguments_: any[]) => Promise<unknown> =>\n  getObjectType(value) === 'AsyncGeneratorFunction';\n\nis.asyncFunction = isObjectOfType<Function>('AsyncFunction');\n\nis.bigint = isOfType<bigint>('bigint');\n\nis.boolean = (value: unknown): value is boolean => {\n  return value === true || value === false;\n};\n\nis.date = isObjectOfType<Date>('Date');\n\nis.defined = (value: unknown): boolean => !is.undefined(value);\n\nis.domElement = (value: unknown): value is HTMLElement => {\n  return (\n    is.object(value) &&\n    !is.plainObject(value) &&\n    (value as HTMLElement).nodeType === 1 &&\n    is.string((value as HTMLElement).nodeName) &&\n    DOM_PROPERTIES_TO_CHECK.every(property => property in (value as HTMLElement))\n  );\n};\n\nis.empty = (value: unknown): boolean => {\n  return (\n    (is.string(value) && value.length === 0) ||\n    (is.array(value) && value.length === 0) ||\n    (is.object(value) && !is.map(value) && !is.set(value) && Object.keys(value).length === 0) ||\n    (is.set(value) && value.size === 0) ||\n    (is.map(value) && value.size === 0)\n  );\n};\n\nis.error = isObjectOfType<Error>('Error');\n\nis.function = isOfType<Function>('function');\n\nis.generator = (value: unknown): value is Generator => {\n  return (\n    is.iterable(value) &&\n    is.function((value as IterableIterator<unknown>).next) &&\n    is.function((value as IterableIterator<unknown>).throw)\n  );\n};\n\nis.generatorFunction = isObjectOfType<GeneratorFunction>('GeneratorFunction');\n\nis.instanceOf = <T>(instance: unknown, class_: Class<T>): instance is T => {\n  if (!instance || !(class_ as Class<T>)) {\n    return false;\n  }\n\n  return Object.getPrototypeOf(instance) === class_.prototype;\n};\n\nis.iterable = (value: unknown): value is IterableIterator<unknown> => {\n  return (\n    !is.nullOrUndefined(value) && is.function((value as IterableIterator<unknown>)[Symbol.iterator])\n  );\n};\n\nis.map = isObjectOfType<Map<unknown, unknown>>('Map');\n\nis.nan = (value: unknown): boolean => {\n  return Number.isNaN(value as number);\n};\n\nis.null = (value: unknown): value is null => {\n  return value === null;\n};\n\nis.nullOrUndefined = (value: unknown): value is null | undefined => {\n  return is.null(value) || is.undefined(value);\n};\n\nis.number = (value: unknown): value is number => {\n  return isOfType<number>('number')(value) && !is.nan(value);\n};\n\nis.numericString = (value: unknown): value is string => {\n  return is.string(value) && (value as string).length > 0 && !Number.isNaN(Number(value));\n};\n\nis.object = (value: unknown): value is object => {\n  return !is.nullOrUndefined(value) && (is.function(value) || typeof value === 'object');\n};\n\nis.oneOf = (target: unknown[], value: any): boolean => {\n  if (!is.array(target)) {\n    return false;\n  }\n\n  // eslint-disable-next-line unicorn/prefer-includes\n  return target.indexOf(value) > -1;\n};\n\nis.plainFunction = isObjectOfType<Function>('Function');\n\nis.plainObject = (value: unknown): value is PlainObject => {\n  if (getObjectType(value) !== 'Object') {\n    return false;\n  }\n\n  const prototype = Object.getPrototypeOf(value);\n\n  return prototype === null || prototype === Object.getPrototypeOf({});\n};\n\nis.primitive = (value: unknown): value is Primitive =>\n  is.null(value) || isPrimitiveType(typeof value);\n\nis.promise = isObjectOfType<Promise<unknown>>('Promise');\n\nis.propertyOf = (\n  target: PlainObject,\n  key: string,\n  predicate?: (v: unknown) => boolean,\n): boolean => {\n  if (!is.object(target) || !key) {\n    return false;\n  }\n\n  const value = target[key];\n\n  if (is.function(predicate)) {\n    return predicate(value);\n  }\n\n  return is.defined(value);\n};\n\nis.regexp = isObjectOfType<RegExp>('RegExp');\n\nis.set = isObjectOfType<Set<PlainObject>>('Set');\n\nis.string = isOfType<string>('string');\n\nis.symbol = isOfType<symbol>('symbol');\n\nis.undefined = isOfType<undefined>('undefined');\n\nis.weakMap = isObjectOfType<WeakMap<PlainObject, unknown>>('WeakMap');\n\nis.weakSet = isObjectOfType<WeakSet<PlainObject>>('WeakSet');\n\nexport default is;\n", "import equal from '@gilbarbara/deep-equal';\nimport is from 'is-lite';\n\nimport { compareNumbers, compareValues, getIterables, includesOrEqualsTo, nested } from './helpers';\nimport { Data, KeyType, TreeChanges, Value } from './types';\n\nexport default function treeChanges<P extends Data, D extends Data, K = KeyType<P, D>>(\n  previousData: P,\n  data: D,\n): TreeChanges<K> {\n  if ([previousData, data].some(is.nullOrUndefined)) {\n    throw new Error('Missing required parameters');\n  }\n\n  if (![previousData, data].every(d => is.plainObject(d) || is.array(d))) {\n    throw new Error('Expected plain objects or array');\n  }\n\n  const added = (key?: K, value?: Value): boolean => {\n    try {\n      return compareValues<K>(previousData, data, { key, type: 'added', value });\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const changed = (key?: K | string, actual?: Value, previous?: Value): boolean => {\n    try {\n      const left = nested(previousData, key);\n      const right = nested(data, key);\n      const hasActual = is.defined(actual);\n      const hasPrevious = is.defined(previous);\n\n      if (hasActual || hasPrevious) {\n        const leftComparator = hasPrevious\n          ? includesOrEqualsTo(previous, left)\n          : !includesOrEqualsTo(actual, left);\n        const rightComparator = includesOrEqualsTo(actual, right);\n\n        return leftComparator && rightComparator;\n      }\n\n      if ([left, right].every(is.array) || [left, right].every(is.plainObject)) {\n        return !equal(left, right);\n      }\n\n      return left !== right;\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const changedFrom = (key: K | string, previous: Value, actual?: Value): boolean => {\n    if (!is.defined(key)) {\n      return false;\n    }\n\n    try {\n      const left = nested(previousData, key);\n      const right = nested(data, key);\n      const hasActual = is.defined(actual);\n\n      return (\n        includesOrEqualsTo(previous, left) &&\n        (hasActual ? includesOrEqualsTo(actual, right) : !hasActual)\n      );\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const decreased = (key: K, actual?: Value, previous?: Value): boolean => {\n    if (!is.defined(key)) {\n      return false;\n    }\n\n    try {\n      return compareNumbers<K>(previousData, data, { key, actual, previous, type: 'decreased' });\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const emptied = (key?: K): boolean => {\n    try {\n      const [left, right] = getIterables(previousData, data, { key });\n\n      return !!left.length && !right.length;\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const filled = (key?: K): boolean => {\n    try {\n      const [left, right] = getIterables(previousData, data, { key });\n\n      return !left.length && !!right.length;\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const increased = (key: K, actual?: Value, previous?: Value): boolean => {\n    if (!is.defined(key)) {\n      return false;\n    }\n\n    try {\n      return compareNumbers<K>(previousData, data, { key, actual, previous, type: 'increased' });\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const removed = (key?: K, value?: Value): boolean => {\n    try {\n      return compareValues<K>(previousData, data, { key, type: 'removed', value });\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  return { added, changed, changedFrom, decreased, emptied, filled, increased, removed };\n}\n\nexport type { Data, KeyType, TreeChanges, Value } from './types';\n", "import equal from '@gilbarbara/deep-equal';\nimport is from 'is-lite';\n\nimport { CompareValuesOptions, Data, Key, Options, ValidTypes, Value } from './types';\n\nexport function canHaveLength(...arguments_: any): boolean {\n  return arguments_.every((d: unknown) => is.string(d) || is.array(d) || is.plainObject(d));\n}\n\nexport function checkEquality(left: Data, right: Data, value: Value) {\n  if (!isSameType(left, right)) {\n    return false;\n  }\n\n  if ([left, right].every(is.array)) {\n    return !left.some(hasValue(value)) && right.some(hasValue(value));\n  }\n\n  /* istanbul ignore else */\n  if ([left, right].every(is.plainObject)) {\n    return (\n      !Object.entries(left).some(hasEntry(value)) && Object.entries(right).some(hasEntry(value))\n    );\n  }\n\n  return right === value;\n}\n\nexport function compareNumbers<K = Key>(\n  previousData: Data,\n  data: Data,\n  options: Options<K>,\n): boolean {\n  const { actual, key, previous, type } = options;\n  const left = nested(previousData, key);\n  const right = nested(data, key);\n\n  let changed =\n    [left, right].every(is.number) && (type === 'increased' ? left < right : left > right);\n\n  if (!is.undefined(actual)) {\n    changed = changed && right === actual;\n  }\n\n  if (!is.undefined(previous)) {\n    changed = changed && left === previous;\n  }\n\n  return changed;\n}\n\nexport function compareValues<K = Key>(\n  previousData: Data,\n  data: Data,\n  options: CompareValuesOptions<K>,\n) {\n  const { key, type, value } = options;\n\n  const left = nested(previousData, key);\n  const right = nested(data, key);\n  const primary = type === 'added' ? left : right;\n  const secondary = type === 'added' ? right : left;\n\n  if (!is.nullOrUndefined(value)) {\n    if (is.defined(primary)) {\n      // check if nested data matches\n      if (is.array(primary) || is.plainObject(primary)) {\n        return checkEquality(primary, secondary, value);\n      }\n    } else {\n      return equal(secondary, value);\n    }\n\n    return false;\n  }\n\n  if ([left, right].every(is.array)) {\n    return !secondary.every(isEqualPredicate(primary));\n  }\n\n  if ([left, right].every(is.plainObject)) {\n    return hasExtraKeys(Object.keys(primary), Object.keys(secondary));\n  }\n\n  return (\n    ![left, right].every(d => is.primitive(d) && is.defined(d)) &&\n    (type === 'added'\n      ? !is.defined(left) && is.defined(right)\n      : is.defined(left) && !is.defined(right))\n  );\n}\n\nexport function getIterables<K = Key>(previousData: Data, data: Data, { key }: Options<K> = {}) {\n  let left = nested(previousData, key);\n  let right = nested(data, key);\n\n  if (!isSameType(left, right)) {\n    throw new TypeError('Inputs have different types');\n  }\n\n  if (!canHaveLength(left, right)) {\n    throw new TypeError(\"Inputs don't have length\");\n  }\n\n  if ([left, right].every(is.plainObject)) {\n    left = Object.keys(left);\n    right = Object.keys(right);\n  }\n\n  return [left, right];\n}\n\nexport function hasEntry(input: Value) {\n  return ([key, value]: [string, Value]) => {\n    if (is.array(input)) {\n      return (\n        equal(input, value) ||\n        input.some(d => equal(d, value) || (is.array(value) && isEqualPredicate(value)(d)))\n      );\n    }\n\n    /* istanbul ignore else */\n    if (is.plainObject(input) && input[key]) {\n      return !!input[key] && equal(input[key], value);\n    }\n\n    return equal(input, value);\n  };\n}\n\nexport function hasExtraKeys(left: string[], right: string[]): boolean {\n  return right.some(d => !left.includes(d));\n}\n\nexport function hasValue(input: Value) {\n  return (value: Value) => {\n    if (is.array(input)) {\n      return input.some(d => equal(d, value) || (is.array(value) && isEqualPredicate(value)(d)));\n    }\n\n    return equal(input, value);\n  };\n}\n\nexport function includesOrEqualsTo<T>(previousValue: T | T[], value: T): boolean {\n  return is.array(previousValue)\n    ? previousValue.some(d => equal(d, value))\n    : equal(previousValue, value);\n}\n\nexport function isEqualPredicate(data: unknown[]) {\n  return (value: unknown) => data.some(d => equal(d, value));\n}\n\nexport function isSameType(...arguments_: ValidTypes[]): boolean {\n  return (\n    arguments_.every(is.array) ||\n    arguments_.every(is.number) ||\n    arguments_.every(is.plainObject) ||\n    arguments_.every(is.string)\n  );\n}\n\nexport function nested<T extends Data, K = Key>(data: T, property?: K) {\n  /* istanbul ignore else */\n  if (is.plainObject(data) || is.array(data)) {\n    /* istanbul ignore else */\n    if (is.string(property)) {\n      const props: Array<any> = property.split('.');\n\n      return props.reduce((acc, d) => acc && acc[d], data);\n    }\n\n    /* istanbul ignore else */\n    if (is.number(property)) {\n      return data[property];\n    }\n\n    return data;\n  }\n\n  return data;\n}\n", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport Popper from 'popper.js';\nimport deepmerge from 'deepmerge';\nimport is from 'is-lite';\nimport treeChanges from 'tree-changes';\nimport ReactDOM from 'react-dom';\n\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n\nvar DEFAULTS = {flip:{padding:20},preventOverflow:{padding:10}};\n\nvar VALIDATOR_ARG_ERROR_MESSAGE='The typeValidator argument must be a function '+'with the signature function(props, propName, componentName).';var MESSAGE_ARG_ERROR_MESSAGE='The error message is optional, but must be a string if provided.';function propIsRequired(condition,props,propName,componentName){if(typeof condition==='boolean'){return condition;}if(typeof condition==='function'){return condition(props,propName,componentName);}if(Boolean(condition)===true){return Boolean(condition);}return false;}function propExists(props,propName){return Object.hasOwnProperty.call(props,propName);}function missingPropError(props,propName,componentName,message){if(message){return new Error(message);}return new Error(\"Required \".concat(props[propName],\" `\").concat(propName,\"` was not specified in `\").concat(componentName,\"`.\"));}function guardAgainstInvalidArgTypes(typeValidator,message){if(typeof typeValidator!=='function'){throw new TypeError(VALIDATOR_ARG_ERROR_MESSAGE);}if(Boolean(message)&&typeof message!=='string'){throw new TypeError(MESSAGE_ARG_ERROR_MESSAGE);}}function isRequiredIf(typeValidator,condition,message){guardAgainstInvalidArgTypes(typeValidator,message);return function(props,propName,componentName){for(var _len=arguments.length,rest=new Array(_len>3?_len-3:0),_key=3;_key<_len;_key++){rest[_key-3]=arguments[_key];}if(propIsRequired(condition,props,propName,componentName)){if(propExists(props,propName)){return typeValidator.apply(void 0,[props,propName,componentName].concat(rest));}return missingPropError(props,propName,componentName,message);}// Is not required, so just run typeValidator.\nreturn typeValidator.apply(void 0,[props,propName,componentName].concat(rest));};}\n\nvar STATUS = {INIT:'init',IDLE:'idle',OPENING:'opening',OPEN:'open',CLOSING:'closing',ERROR:'error'};\n\nvar isReact16=ReactDOM.createPortal!==undefined;function canUseDOM(){return !!(typeof window!=='undefined'&&window.document&&window.document.createElement);}function isMobile(){return 'ontouchstart'in window&&/Mobi/.test(navigator.userAgent);}/**\n * Log method calls if debug is enabled\n *\n * @private\n * @param {Object}       arg\n * @param {string}       arg.title    - The title the logger was called from\n * @param {Object|Array} [arg.data]   - The data to be logged\n * @param {boolean}      [arg.warn]  - If true, the message will be a warning\n * @param {boolean}      [arg.debug] - Nothing will be logged unless debug is true\n */function log(_ref){var title=_ref.title,data=_ref.data,_ref$warn=_ref.warn,warn=_ref$warn===void 0?false:_ref$warn,_ref$debug=_ref.debug,debug=_ref$debug===void 0?false:_ref$debug;/* eslint-disable no-console */var logFn=warn?console.warn||console.error:console.log;if(debug&&title&&data){console.groupCollapsed(\"%creact-floater: \".concat(title),'color: #9b00ff; font-weight: bold; font-size: 12px;');if(Array.isArray(data)){data.forEach(function(d){if(is.plainObject(d)&&d.key){logFn.apply(console,[d.key,d.value]);}else {logFn.apply(console,[d]);}});}else {logFn.apply(console,[data]);}console.groupEnd();}/* eslint-enable */}function on(element,event,cb){var capture=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;element.addEventListener(event,cb,capture);}function off(element,event,cb){var capture=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;element.removeEventListener(event,cb,capture);}function once(element,event,cb){var capture=arguments.length>3&&arguments[3]!==undefined?arguments[3]:false;var _nextCB;// eslint-disable-next-line prefer-const\n_nextCB=function nextCB(e){cb(e);off(element,event,_nextCB);};on(element,event,_nextCB,capture);}function noop(){}\n\nvar ReactFloaterPortal=/*#__PURE__*/function(_React$Component){_inherits(ReactFloaterPortal,_React$Component);var _super=_createSuper(ReactFloaterPortal);function ReactFloaterPortal(){_classCallCheck(this,ReactFloaterPortal);return _super.apply(this,arguments);}_createClass(ReactFloaterPortal,[{key:\"componentDidMount\",value:function componentDidMount(){if(!canUseDOM())return;if(!this.node){this.appendNode();}if(!isReact16){this.renderPortal();}}},{key:\"componentDidUpdate\",value:function componentDidUpdate(){if(!canUseDOM())return;if(!isReact16){this.renderPortal();}}},{key:\"componentWillUnmount\",value:function componentWillUnmount(){if(!canUseDOM()||!this.node)return;if(!isReact16){ReactDOM.unmountComponentAtNode(this.node);}if(this.node&&this.node.parentNode===document.body){document.body.removeChild(this.node);this.node=undefined;}}},{key:\"appendNode\",value:function appendNode(){var _this$props=this.props,id=_this$props.id,zIndex=_this$props.zIndex;if(!this.node){this.node=document.createElement('div');/* istanbul ignore else */if(id){this.node.id=id;}if(zIndex){this.node.style.zIndex=zIndex;}document.body.appendChild(this.node);}}},{key:\"renderPortal\",value:function renderPortal(){if(!canUseDOM())return null;var _this$props2=this.props,children=_this$props2.children,setRef=_this$props2.setRef;if(!this.node){this.appendNode();}/* istanbul ignore else */if(isReact16){return/*#__PURE__*/ReactDOM.createPortal(children,this.node);}var portal=ReactDOM.unstable_renderSubtreeIntoContainer(this,children.length>1?/*#__PURE__*/React.createElement(\"div\",null,children):children[0],this.node);setRef(portal);return null;}},{key:\"renderReact16\",value:function renderReact16(){var _this$props3=this.props,hasChildren=_this$props3.hasChildren,placement=_this$props3.placement,target=_this$props3.target;if(!hasChildren){if(target||placement==='center'){return this.renderPortal();}return null;}return this.renderPortal();}},{key:\"render\",value:function render(){if(!isReact16){return null;}return this.renderReact16();}}]);return ReactFloaterPortal;}(React.Component);_defineProperty(ReactFloaterPortal,\"propTypes\",{children:PropTypes.oneOfType([PropTypes.element,PropTypes.array]),hasChildren:PropTypes.bool,id:PropTypes.oneOfType([PropTypes.string,PropTypes.number]),placement:PropTypes.string,setRef:PropTypes.func.isRequired,target:PropTypes.oneOfType([PropTypes.object,PropTypes.string]),zIndex:PropTypes.number});\n\nvar FloaterArrow=/*#__PURE__*/function(_React$Component){_inherits(FloaterArrow,_React$Component);var _super=_createSuper(FloaterArrow);function FloaterArrow(){_classCallCheck(this,FloaterArrow);return _super.apply(this,arguments);}_createClass(FloaterArrow,[{key:\"parentStyle\",get:function get(){var _this$props=this.props,placement=_this$props.placement,styles=_this$props.styles;var length=styles.arrow.length;var arrow={pointerEvents:'none',position:'absolute',width:'100%'};/* istanbul ignore else */if(placement.startsWith('top')){arrow.bottom=0;arrow.left=0;arrow.right=0;arrow.height=length;}else if(placement.startsWith('bottom')){arrow.left=0;arrow.right=0;arrow.top=0;arrow.height=length;}else if(placement.startsWith('left')){arrow.right=0;arrow.top=0;arrow.bottom=0;}else if(placement.startsWith('right')){arrow.left=0;arrow.top=0;}return arrow;}},{key:\"render\",value:function render(){var _this$props2=this.props,placement=_this$props2.placement,setArrowRef=_this$props2.setArrowRef,styles=_this$props2.styles;var _styles$arrow=styles.arrow,color=_styles$arrow.color,display=_styles$arrow.display,length=_styles$arrow.length,margin=_styles$arrow.margin,position=_styles$arrow.position,spread=_styles$arrow.spread;var arrowStyles={display:display,position:position};var points;var x=spread;var y=length;/* istanbul ignore else */if(placement.startsWith('top')){points=\"0,0 \".concat(x/2,\",\").concat(y,\" \").concat(x,\",0\");arrowStyles.bottom=0;arrowStyles.marginLeft=margin;arrowStyles.marginRight=margin;}else if(placement.startsWith('bottom')){points=\"\".concat(x,\",\").concat(y,\" \").concat(x/2,\",0 0,\").concat(y);arrowStyles.top=0;arrowStyles.marginLeft=margin;arrowStyles.marginRight=margin;}else if(placement.startsWith('left')){y=spread;x=length;points=\"0,0 \".concat(x,\",\").concat(y/2,\" 0,\").concat(y);arrowStyles.right=0;arrowStyles.marginTop=margin;arrowStyles.marginBottom=margin;}else if(placement.startsWith('right')){y=spread;x=length;points=\"\".concat(x,\",\").concat(y,\" \").concat(x,\",0 0,\").concat(y/2);arrowStyles.left=0;arrowStyles.marginTop=margin;arrowStyles.marginBottom=margin;}return/*#__PURE__*/React.createElement(\"div\",{className:\"__floater__arrow\",style:this.parentStyle},/*#__PURE__*/React.createElement(\"span\",{ref:setArrowRef,style:arrowStyles},/*#__PURE__*/React.createElement(\"svg\",{width:x,height:y,version:\"1.1\",xmlns:\"http://www.w3.org/2000/svg\"},/*#__PURE__*/React.createElement(\"polygon\",{points:points,fill:color}))));}}]);return FloaterArrow;}(React.Component);_defineProperty(FloaterArrow,\"propTypes\",{placement:PropTypes.string.isRequired,setArrowRef:PropTypes.func.isRequired,styles:PropTypes.object.isRequired});\n\nvar _excluded$1=[\"color\",\"height\",\"width\"];function FloaterCloseBtn(_ref){var handleClick=_ref.handleClick,styles=_ref.styles;var color=styles.color,height=styles.height,width=styles.width,style=_objectWithoutProperties(styles,_excluded$1);return/*#__PURE__*/React.createElement(\"button\",{\"aria-label\":\"close\",onClick:handleClick,style:style,type:\"button\"},/*#__PURE__*/React.createElement(\"svg\",{width:\"\".concat(width,\"px\"),height:\"\".concat(height,\"px\"),viewBox:\"0 0 18 18\",version:\"1.1\",xmlns:\"http://www.w3.org/2000/svg\",preserveAspectRatio:\"xMidYMid\"},/*#__PURE__*/React.createElement(\"g\",null,/*#__PURE__*/React.createElement(\"path\",{d:\"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z\",fill:color}))));}FloaterCloseBtn.propTypes={handleClick:PropTypes.func.isRequired,styles:PropTypes.object.isRequired};\n\nfunction FloaterContainer(_ref){var content=_ref.content,footer=_ref.footer,handleClick=_ref.handleClick,open=_ref.open,positionWrapper=_ref.positionWrapper,showCloseButton=_ref.showCloseButton,title=_ref.title,styles=_ref.styles;var output={content:/*#__PURE__*/React.isValidElement(content)?content:/*#__PURE__*/React.createElement(\"div\",{className:\"__floater__content\",style:styles.content},content)};if(title){output.title=/*#__PURE__*/React.isValidElement(title)?title:/*#__PURE__*/React.createElement(\"div\",{className:\"__floater__title\",style:styles.title},title);}if(footer){output.footer=/*#__PURE__*/React.isValidElement(footer)?footer:/*#__PURE__*/React.createElement(\"div\",{className:\"__floater__footer\",style:styles.footer},footer);}if((showCloseButton||positionWrapper)&&!is[\"boolean\"](open)){output.close=/*#__PURE__*/React.createElement(FloaterCloseBtn,{styles:styles.close,handleClick:handleClick});}return/*#__PURE__*/React.createElement(\"div\",{className:\"__floater__container\",style:styles.container},output.close,output.title,output.content,output.footer);}FloaterContainer.propTypes={content:PropTypes.node.isRequired,footer:PropTypes.node,handleClick:PropTypes.func.isRequired,open:PropTypes.bool,positionWrapper:PropTypes.bool.isRequired,showCloseButton:PropTypes.bool.isRequired,styles:PropTypes.object.isRequired,title:PropTypes.node};\n\nvar Floater=/*#__PURE__*/function(_React$Component){_inherits(Floater,_React$Component);var _super=_createSuper(Floater);function Floater(){_classCallCheck(this,Floater);return _super.apply(this,arguments);}_createClass(Floater,[{key:\"style\",get:function get(){var _this$props=this.props,disableAnimation=_this$props.disableAnimation,component=_this$props.component,placement=_this$props.placement,hideArrow=_this$props.hideArrow,status=_this$props.status,styles=_this$props.styles;var length=styles.arrow.length,floater=styles.floater,floaterCentered=styles.floaterCentered,floaterClosing=styles.floaterClosing,floaterOpening=styles.floaterOpening,floaterWithAnimation=styles.floaterWithAnimation,floaterWithComponent=styles.floaterWithComponent;var element={};if(!hideArrow){if(placement.startsWith('top')){element.padding=\"0 0 \".concat(length,\"px\");}else if(placement.startsWith('bottom')){element.padding=\"\".concat(length,\"px 0 0\");}else if(placement.startsWith('left')){element.padding=\"0 \".concat(length,\"px 0 0\");}else if(placement.startsWith('right')){element.padding=\"0 0 0 \".concat(length,\"px\");}}if([STATUS.OPENING,STATUS.OPEN].indexOf(status)!==-1){element=_objectSpread2(_objectSpread2({},element),floaterOpening);}if(status===STATUS.CLOSING){element=_objectSpread2(_objectSpread2({},element),floaterClosing);}if(status===STATUS.OPEN&&!disableAnimation){element=_objectSpread2(_objectSpread2({},element),floaterWithAnimation);}if(placement==='center'){element=_objectSpread2(_objectSpread2({},element),floaterCentered);}if(component){element=_objectSpread2(_objectSpread2({},element),floaterWithComponent);}return _objectSpread2(_objectSpread2({},floater),element);}},{key:\"render\",value:function render(){var _this$props2=this.props,component=_this$props2.component,closeFn=_this$props2.handleClick,hideArrow=_this$props2.hideArrow,setFloaterRef=_this$props2.setFloaterRef,status=_this$props2.status;var output={};var classes=['__floater'];if(component){if(/*#__PURE__*/React.isValidElement(component)){output.content=/*#__PURE__*/React.cloneElement(component,{closeFn:closeFn});}else {output.content=component({closeFn:closeFn});}}else {output.content=/*#__PURE__*/React.createElement(FloaterContainer,this.props);}if(status===STATUS.OPEN){classes.push('__floater__open');}if(!hideArrow){output.arrow=/*#__PURE__*/React.createElement(FloaterArrow,this.props);}return/*#__PURE__*/React.createElement(\"div\",{ref:setFloaterRef,className:classes.join(' '),style:this.style},/*#__PURE__*/React.createElement(\"div\",{className:\"__floater__body\"},output.content,output.arrow));}}]);return Floater;}(React.Component);_defineProperty(Floater,\"propTypes\",{component:PropTypes.oneOfType([PropTypes.func,PropTypes.element]),content:PropTypes.node,disableAnimation:PropTypes.bool.isRequired,footer:PropTypes.node,handleClick:PropTypes.func.isRequired,hideArrow:PropTypes.bool.isRequired,open:PropTypes.bool,placement:PropTypes.string.isRequired,positionWrapper:PropTypes.bool.isRequired,setArrowRef:PropTypes.func.isRequired,setFloaterRef:PropTypes.func.isRequired,showCloseButton:PropTypes.bool,status:PropTypes.string.isRequired,styles:PropTypes.object.isRequired,title:PropTypes.node});\n\nvar ReactFloaterWrapper=/*#__PURE__*/function(_React$Component){_inherits(ReactFloaterWrapper,_React$Component);var _super=_createSuper(ReactFloaterWrapper);function ReactFloaterWrapper(){_classCallCheck(this,ReactFloaterWrapper);return _super.apply(this,arguments);}_createClass(ReactFloaterWrapper,[{key:\"render\",value:function render(){var _this$props=this.props,children=_this$props.children,handleClick=_this$props.handleClick,handleMouseEnter=_this$props.handleMouseEnter,handleMouseLeave=_this$props.handleMouseLeave,setChildRef=_this$props.setChildRef,setWrapperRef=_this$props.setWrapperRef,style=_this$props.style,styles=_this$props.styles;var element;/* istanbul ignore else */if(children){if(React.Children.count(children)===1){if(!/*#__PURE__*/React.isValidElement(children)){element=/*#__PURE__*/React.createElement(\"span\",null,children);}else {var refProp=is[\"function\"](children.type)?'innerRef':'ref';element=/*#__PURE__*/React.cloneElement(React.Children.only(children),_defineProperty({},refProp,setChildRef));}}else {element=children;}}if(!element){return null;}return/*#__PURE__*/React.createElement(\"span\",{ref:setWrapperRef,style:_objectSpread2(_objectSpread2({},styles),style),onClick:handleClick,onMouseEnter:handleMouseEnter,onMouseLeave:handleMouseLeave},element);}}]);return ReactFloaterWrapper;}(React.Component);_defineProperty(ReactFloaterWrapper,\"propTypes\",{children:PropTypes.node,handleClick:PropTypes.func.isRequired,handleMouseEnter:PropTypes.func.isRequired,handleMouseLeave:PropTypes.func.isRequired,setChildRef:PropTypes.func.isRequired,setWrapperRef:PropTypes.func.isRequired,style:PropTypes.object,styles:PropTypes.object.isRequired});\n\nvar defaultOptions={zIndex:100};function getStyles(styles){var options=deepmerge(defaultOptions,styles.options||{});return {wrapper:{cursor:'help',display:'inline-flex',flexDirection:'column',zIndex:options.zIndex},wrapperPosition:{left:-1000,position:'absolute',top:-1000,visibility:'hidden'},floater:{display:'inline-block',filter:'drop-shadow(0 0 3px rgba(0, 0, 0, 0.3))',maxWidth:300,opacity:0,position:'relative',transition:'opacity 0.3s',visibility:'hidden',zIndex:options.zIndex},floaterOpening:{opacity:1,visibility:'visible'},floaterWithAnimation:{opacity:1,transition:'opacity 0.3s, transform 0.2s',visibility:'visible'},floaterWithComponent:{maxWidth:'100%'},floaterClosing:{opacity:0,visibility:'visible'},floaterCentered:{left:'50%',position:'fixed',top:'50%',transform:'translate(-50%, -50%)'},container:{backgroundColor:'#fff',color:'#666',minHeight:60,minWidth:200,padding:20,position:'relative',zIndex:10},title:{borderBottom:'1px solid #555',color:'#555',fontSize:18,marginBottom:5,paddingBottom:6,paddingRight:18},content:{fontSize:15},close:{backgroundColor:'transparent',border:0,borderRadius:0,color:'#555',fontSize:0,height:15,outline:'none',padding:10,position:'absolute',right:0,top:0,width:15,WebkitAppearance:'none'},footer:{borderTop:'1px solid #ccc',fontSize:13,marginTop:10,paddingTop:5},arrow:{color:'#fff',display:'inline-flex',length:16,margin:8,position:'absolute',spread:32},options:options};}\n\nvar _excluded=[\"arrow\",\"flip\",\"offset\"];var POSITIONING_PROPS=['position','top','right','bottom','left'];var ReactFloater=/*#__PURE__*/function(_React$Component){_inherits(ReactFloater,_React$Component);var _super=_createSuper(ReactFloater);function ReactFloater(props){var _this;_classCallCheck(this,ReactFloater);_this=_super.call(this,props);/* istanbul ignore else */_defineProperty(_assertThisInitialized(_this),\"setArrowRef\",function(ref){_this.arrowRef=ref;});_defineProperty(_assertThisInitialized(_this),\"setChildRef\",function(ref){_this.childRef=ref;});_defineProperty(_assertThisInitialized(_this),\"setFloaterRef\",function(ref){_this.floaterRef=ref;});_defineProperty(_assertThisInitialized(_this),\"setWrapperRef\",function(ref){_this.wrapperRef=ref;});_defineProperty(_assertThisInitialized(_this),\"handleTransitionEnd\",function(){var status=_this.state.status;var callback=_this.props.callback;/* istanbul ignore else */if(_this.wrapperPopper){_this.wrapperPopper.instance.update();}_this.setState({status:status===STATUS.OPENING?STATUS.OPEN:STATUS.IDLE},function(){var newStatus=_this.state.status;callback(newStatus===STATUS.OPEN?'open':'close',_this.props);});});_defineProperty(_assertThisInitialized(_this),\"handleClick\",function(){var _this$props=_this.props,event=_this$props.event,open=_this$props.open;if(is[\"boolean\"](open))return;var _this$state=_this.state,positionWrapper=_this$state.positionWrapper,status=_this$state.status;/* istanbul ignore else */if(_this.event==='click'||_this.event==='hover'&&positionWrapper){log({title:'click',data:[{event:event,status:status===STATUS.OPEN?'closing':'opening'}],debug:_this.debug});_this.toggle();}});_defineProperty(_assertThisInitialized(_this),\"handleMouseEnter\",function(){var _this$props2=_this.props,event=_this$props2.event,open=_this$props2.open;if(is[\"boolean\"](open)||isMobile())return;var status=_this.state.status;/* istanbul ignore else */if(_this.event==='hover'&&status===STATUS.IDLE){log({title:'mouseEnter',data:[{key:'originalEvent',value:event}],debug:_this.debug});clearTimeout(_this.eventDelayTimeout);_this.toggle();}});_defineProperty(_assertThisInitialized(_this),\"handleMouseLeave\",function(){var _this$props3=_this.props,event=_this$props3.event,eventDelay=_this$props3.eventDelay,open=_this$props3.open;if(is[\"boolean\"](open)||isMobile())return;var _this$state2=_this.state,status=_this$state2.status,positionWrapper=_this$state2.positionWrapper;/* istanbul ignore else */if(_this.event==='hover'){log({title:'mouseLeave',data:[{key:'originalEvent',value:event}],debug:_this.debug});if(!eventDelay){_this.toggle(STATUS.IDLE);}else if([STATUS.OPENING,STATUS.OPEN].indexOf(status)!==-1&&!positionWrapper&&!_this.eventDelayTimeout){_this.eventDelayTimeout=setTimeout(function(){delete _this.eventDelayTimeout;_this.toggle();},eventDelay*1000);}}});_this.state={currentPlacement:props.placement,needsUpdate:false,positionWrapper:props.wrapperOptions.position&&!!props.target,status:STATUS.INIT,statusWrapper:STATUS.INIT};_this._isMounted=false;_this.hasMounted=false;if(canUseDOM()){window.addEventListener('load',function(){if(_this.popper){_this.popper.instance.update();}if(_this.wrapperPopper){_this.wrapperPopper.instance.update();}});}return _this;}_createClass(ReactFloater,[{key:\"componentDidMount\",value:function componentDidMount(){if(!canUseDOM())return;var positionWrapper=this.state.positionWrapper;var _this$props5=this.props,children=_this$props5.children,open=_this$props5.open,target=_this$props5.target;this._isMounted=true;log({title:'init',data:{hasChildren:!!children,hasTarget:!!target,isControlled:is[\"boolean\"](open),positionWrapper:positionWrapper,target:this.target,floater:this.floaterRef},debug:this.debug});if(!this.hasMounted){this.initPopper();this.hasMounted=true;}if(!children&&target&&!is[\"boolean\"](open));}},{key:\"componentDidUpdate\",value:function componentDidUpdate(prevProps,prevState){if(!canUseDOM())return;var _this$props6=this.props,autoOpen=_this$props6.autoOpen,open=_this$props6.open,target=_this$props6.target,wrapperOptions=_this$props6.wrapperOptions;var _treeChanges=treeChanges(prevState,this.state),changedFrom=_treeChanges.changedFrom,changed=_treeChanges.changed;if(prevProps.open!==open){var forceStatus;// always follow `open` in controlled mode\nif(is[\"boolean\"](open)){forceStatus=open?STATUS.OPENING:STATUS.CLOSING;}this.toggle(forceStatus);}if(prevProps.wrapperOptions.position!==wrapperOptions.position||prevProps.target!==target){this.changeWrapperPosition(this.props);}if(changed('status',STATUS.IDLE)&&open){this.toggle(STATUS.OPEN);}else if(changedFrom('status',STATUS.INIT,STATUS.IDLE)&&autoOpen){this.toggle(STATUS.OPEN);}if(this.popper&&changed('status',STATUS.OPENING)){this.popper.instance.update();}if(this.floaterRef&&(changed('status',STATUS.OPENING)||changed('status',STATUS.CLOSING))){once(this.floaterRef,'transitionend',this.handleTransitionEnd);}if(changed('needsUpdate',true)){this.rebuildPopper();}}},{key:\"componentWillUnmount\",value:function componentWillUnmount(){if(!canUseDOM())return;this._isMounted=false;if(this.popper){this.popper.instance.destroy();}if(this.wrapperPopper){this.wrapperPopper.instance.destroy();}}},{key:\"initPopper\",value:function initPopper(){var _this2=this;var target=arguments.length>0&&arguments[0]!==undefined?arguments[0]:this.target;var positionWrapper=this.state.positionWrapper;var _this$props7=this.props,disableFlip=_this$props7.disableFlip,getPopper=_this$props7.getPopper,hideArrow=_this$props7.hideArrow,offset=_this$props7.offset,placement=_this$props7.placement,wrapperOptions=_this$props7.wrapperOptions;var flipBehavior=placement==='top'||placement==='bottom'?'flip':['right','bottom-end','top-end','left','top-start','bottom-start'];/* istanbul ignore else */if(placement==='center'){this.setState({status:STATUS.IDLE});}else if(target&&this.floaterRef){var _this$options=this.options,arrow=_this$options.arrow,flip=_this$options.flip,offsetOptions=_this$options.offset,rest=_objectWithoutProperties(_this$options,_excluded);new Popper(target,this.floaterRef,{placement:placement,modifiers:_objectSpread2({arrow:_objectSpread2({enabled:!hideArrow,element:this.arrowRef},arrow),flip:_objectSpread2({enabled:!disableFlip,behavior:flipBehavior},flip),offset:_objectSpread2({offset:\"0, \".concat(offset,\"px\")},offsetOptions)},rest),onCreate:function onCreate(data){var _this2$floaterRef;_this2.popper=data;if(!((_this2$floaterRef=_this2.floaterRef)!==null&&_this2$floaterRef!==void 0&&_this2$floaterRef.isConnected)){_this2.setState({needsUpdate:true});return;}getPopper(data,'floater');if(_this2._isMounted){_this2.setState({currentPlacement:data.placement,status:STATUS.IDLE});}if(placement!==data.placement){setTimeout(function(){data.instance.update();},1);}},onUpdate:function onUpdate(data){_this2.popper=data;var currentPlacement=_this2.state.currentPlacement;if(_this2._isMounted&&data.placement!==currentPlacement){_this2.setState({currentPlacement:data.placement});}}});}if(positionWrapper){var wrapperOffset=!is.undefined(wrapperOptions.offset)?wrapperOptions.offset:0;new Popper(this.target,this.wrapperRef,{placement:wrapperOptions.placement||placement,modifiers:{arrow:{enabled:false},offset:{offset:\"0, \".concat(wrapperOffset,\"px\")},flip:{enabled:false}},onCreate:function onCreate(data){_this2.wrapperPopper=data;if(_this2._isMounted){_this2.setState({statusWrapper:STATUS.IDLE});}getPopper(data,'wrapper');if(placement!==data.placement){setTimeout(function(){data.instance.update();},1);}}});}}},{key:\"rebuildPopper\",value:function rebuildPopper(){var _this3=this;this.floaterRefInterval=setInterval(function(){var _this3$floaterRef;if((_this3$floaterRef=_this3.floaterRef)!==null&&_this3$floaterRef!==void 0&&_this3$floaterRef.isConnected){clearInterval(_this3.floaterRefInterval);_this3.setState({needsUpdate:false});_this3.initPopper();}},50);}},{key:\"changeWrapperPosition\",value:function changeWrapperPosition(_ref){var target=_ref.target,wrapperOptions=_ref.wrapperOptions;this.setState({positionWrapper:wrapperOptions.position&&!!target});}},{key:\"toggle\",value:function toggle(forceStatus){var status=this.state.status;var nextStatus=status===STATUS.OPEN?STATUS.CLOSING:STATUS.OPENING;if(!is.undefined(forceStatus)){nextStatus=forceStatus;}this.setState({status:nextStatus});}},{key:\"debug\",get:function get(){var debug=this.props.debug;return debug||canUseDOM()&&'ReactFloaterDebug'in window&&!!window.ReactFloaterDebug;}},{key:\"event\",get:function get(){var _this$props8=this.props,disableHoverToClick=_this$props8.disableHoverToClick,event=_this$props8.event;if(event==='hover'&&isMobile()&&!disableHoverToClick){return 'click';}return event;}},{key:\"options\",get:function get(){var options=this.props.options;return deepmerge(DEFAULTS,options||{});}},{key:\"styles\",get:function get(){var _this4=this;var _this$state3=this.state,status=_this$state3.status,positionWrapper=_this$state3.positionWrapper,statusWrapper=_this$state3.statusWrapper;var styles=this.props.styles;var nextStyles=deepmerge(getStyles(styles),styles);if(positionWrapper){var wrapperStyles;if(!([STATUS.IDLE].indexOf(status)!==-1)||!([STATUS.IDLE].indexOf(statusWrapper)!==-1)){wrapperStyles=nextStyles.wrapperPosition;}else {wrapperStyles=this.wrapperPopper.styles;}nextStyles.wrapper=_objectSpread2(_objectSpread2({},nextStyles.wrapper),wrapperStyles);}/* istanbul ignore else */if(this.target){var targetStyles=window.getComputedStyle(this.target);/* istanbul ignore else */if(this.wrapperStyles){nextStyles.wrapper=_objectSpread2(_objectSpread2({},nextStyles.wrapper),this.wrapperStyles);}else if(!(['relative','static'].indexOf(targetStyles.position)!==-1)){this.wrapperStyles={};if(!positionWrapper){POSITIONING_PROPS.forEach(function(d){_this4.wrapperStyles[d]=targetStyles[d];});nextStyles.wrapper=_objectSpread2(_objectSpread2({},nextStyles.wrapper),this.wrapperStyles);this.target.style.position='relative';this.target.style.top='auto';this.target.style.right='auto';this.target.style.bottom='auto';this.target.style.left='auto';}}}return nextStyles;}},{key:\"target\",get:function get(){if(!canUseDOM())return null;var target=this.props.target;if(target){if(is.domElement(target)){return target;}return document.querySelector(target);}return this.childRef||this.wrapperRef;}},{key:\"render\",value:function render(){var _this$state4=this.state,currentPlacement=_this$state4.currentPlacement,positionWrapper=_this$state4.positionWrapper,status=_this$state4.status;var _this$props9=this.props,children=_this$props9.children,component=_this$props9.component,content=_this$props9.content,disableAnimation=_this$props9.disableAnimation,footer=_this$props9.footer,hideArrow=_this$props9.hideArrow,id=_this$props9.id,open=_this$props9.open,showCloseButton=_this$props9.showCloseButton,style=_this$props9.style,target=_this$props9.target,title=_this$props9.title;var wrapper=/*#__PURE__*/React.createElement(ReactFloaterWrapper,{handleClick:this.handleClick,handleMouseEnter:this.handleMouseEnter,handleMouseLeave:this.handleMouseLeave,setChildRef:this.setChildRef,setWrapperRef:this.setWrapperRef,style:style,styles:this.styles.wrapper},children);var output={};if(positionWrapper){output.wrapperInPortal=wrapper;}else {output.wrapperAsChildren=wrapper;}return/*#__PURE__*/React.createElement(\"span\",null,/*#__PURE__*/React.createElement(ReactFloaterPortal,{hasChildren:!!children,id:id,placement:currentPlacement,setRef:this.setFloaterRef,target:target,zIndex:this.styles.options.zIndex},/*#__PURE__*/React.createElement(Floater,{component:component,content:content,disableAnimation:disableAnimation,footer:footer,handleClick:this.handleClick,hideArrow:hideArrow||currentPlacement==='center',open:open,placement:currentPlacement,positionWrapper:positionWrapper,setArrowRef:this.setArrowRef,setFloaterRef:this.setFloaterRef,showCloseButton:showCloseButton,status:status,styles:this.styles,title:title}),output.wrapperInPortal),output.wrapperAsChildren);}}]);return ReactFloater;}(React.Component);_defineProperty(ReactFloater,\"propTypes\",{autoOpen:PropTypes.bool,callback:PropTypes.func,children:PropTypes.node,component:isRequiredIf(PropTypes.oneOfType([PropTypes.func,PropTypes.element]),function(props){return !props.content;}),content:isRequiredIf(PropTypes.node,function(props){return !props.component;}),debug:PropTypes.bool,disableAnimation:PropTypes.bool,disableFlip:PropTypes.bool,disableHoverToClick:PropTypes.bool,event:PropTypes.oneOf(['hover','click']),eventDelay:PropTypes.number,footer:PropTypes.node,getPopper:PropTypes.func,hideArrow:PropTypes.bool,id:PropTypes.oneOfType([PropTypes.string,PropTypes.number]),offset:PropTypes.number,open:PropTypes.bool,options:PropTypes.object,placement:PropTypes.oneOf(['top','top-start','top-end','bottom','bottom-start','bottom-end','left','left-start','left-end','right','right-start','right-end','auto','center']),showCloseButton:PropTypes.bool,style:PropTypes.object,styles:PropTypes.object,target:PropTypes.oneOfType([PropTypes.object,PropTypes.string]),title:PropTypes.node,wrapperOptions:PropTypes.shape({offset:PropTypes.number,placement:PropTypes.oneOf(['top','top-start','top-end','bottom','bottom-start','bottom-end','left','left-start','left-end','right','right-start','right-end','auto']),position:PropTypes.bool})});_defineProperty(ReactFloater,\"defaultProps\",{autoOpen:false,callback:noop,debug:false,disableAnimation:false,disableFlip:false,disableHoverToClick:false,event:'click',eventDelay:0.4,getPopper:noop,hideArrow:false,offset:15,placement:'bottom',showCloseButton:false,styles:{},target:null,wrapperOptions:{position:false}});\n\nexport { ReactFloater as default };\n", "export default typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\n", "import isBrowser from './isBrowser';\n\nconst timeoutDuration = (function(){\n  const longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (let i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}());\n\nexport function microtaskDebounce(fn) {\n  let called = false\n  return () => {\n    if (called) {\n      return\n    }\n    called = true\n    window.Promise.resolve().then(() => {\n      called = false\n      fn()\n    })\n  }\n}\n\nexport function taskDebounce(fn) {\n  let scheduled = false;\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(() => {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nconst supportsMicroTasks = isBrowser && window.Promise\n\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nexport default (supportsMicroTasks\n  ? microtaskDebounce\n  : taskDebounce);\n", "/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nexport default function isFunction(functionToCheck) {\n  const getType = {};\n  return (\n    functionToCheck &&\n    getType.toString.call(functionToCheck) === '[object Function]'\n  );\n}\n", "/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nexport default function getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const window = element.ownerDocument.defaultView;\n  const css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n", "/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nexport default function getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nexport default function getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body\n    case '#document':\n      return element.body\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getStyleComputedProperty(element);\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n", "/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nexport default function getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\n", "import isBrowser from './isBrowser';\n\nconst isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nconst isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nexport default function isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nexport default function getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  const noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  let offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (\n    ['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 &&\n    getStyleComputedProperty(offsetParent, 'position') === 'static'\n  ) {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n", "import getOffsetParent from './getOffsetParent';\n\nexport default function isOffsetContainer(element) {\n  const { nodeName } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return (\n    nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element\n  );\n}\n", "/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nexport default function getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n", "import isOffsetContainer from './isOffsetContainer';\nimport getRoot from './getRoot';\nimport getOffsetParent from './getOffsetParent';\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nexport default function findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order =\n    element1.compareDocumentPosition(element2) &\n    Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  const { commonAncestorContainer } = range;\n\n  // Both nodes are inside #document\n  if (\n    (element1 !== commonAncestorContainer &&\n      element2 !== commonAncestorContainer) ||\n    start.contains(end)\n  ) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n", "/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nexport default function getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n", "import getScroll from './getScroll';\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nexport default function includeScroll(rect, element, subtract = false) {\n  const scrollTop = getScroll(element, 'top');\n  const scrollLeft = getScroll(element, 'left');\n  const modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n", "/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nexport default function getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return (\n    parseFloat(styles[`border${sideA}Width`]) +\n    parseFloat(styles[`border${sideB}Width`])\n  );\n}\n", "import isIE from './isIE';\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(\n    body[`offset${axis}`],\n    body[`scroll${axis}`],\n    html[`client${axis}`],\n    html[`offset${axis}`],\n    html[`scroll${axis}`],\n    isIE(10)\n      ? (parseInt(html[`offset${axis}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Top' : 'Left'}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Bottom' : 'Right'}`]))\n    : 0 \n  );\n}\n\nexport default function getWindowSizes(document) {\n  const body = document.body;\n  const html = document.documentElement;\n  const computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle),\n  };\n}\n", "/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nexport default function getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height,\n  };\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getBordersSize from './getBordersSize';\nimport getWindowSizes from './getWindowSizes';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\nimport isIE from './isIE';\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nexport default function getBoundingClientRect(element) {\n  let rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      const scrollTop = getScroll(element, 'top');\n      const scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    }\n    else {\n      rect = element.getBoundingClientRect();\n    }\n  }\n  catch(e){}\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  const width =\n    sizes.width || element.clientWidth || result.width;\n  const height =\n    sizes.height || element.clientHeight || result.height;\n\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport includeScroll from './includeScroll';\nimport getScrollParent from './getScrollParent';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport runIsIE from './isIE';\nimport getClientRect from './getClientRect';\n\nexport default function getOffsetRectRelativeToArbitraryNode(children, parent, fixedPosition = false) {\n  const isIE10 = runIsIE(10);\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = parseFloat(styles.borderTopWidth);\n  const borderLeftWidth = parseFloat(styles.borderLeftWidth);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if(fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  let offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height,\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    const marginTop = parseFloat(styles.marginTop);\n    const marginLeft = parseFloat(styles.marginLeft);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (\n    isIE10 && !fixedPosition\n      ? parent.contains(scrollParent)\n      : parent === scrollParent && scrollParent.nodeName !== 'BODY'\n  ) {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n", "import getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\n\nexport default function getViewportOffsetRectRelativeToArtbitraryNode(element, excludeScroll = false) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  const scrollTop = !excludeScroll ? getScroll(html) : 0;\n  const scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  const offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width,\n    height,\n  };\n\n  return getClientRect(offset);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {<PERSON><PERSON><PERSON>} answer to \"isFixed?\"\n */\nexport default function isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  const parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nexport default function getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n   if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  let el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n\n}\n", "import getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getReferenceNode from './getReferenceNode';\nimport findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getViewportOffsetRectRelativeToArtbitraryNode from './getViewportOffsetRectRelativeToArtbitraryNode';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nexport default function getBoundaries(\n  popper,\n  reference,\n  padding,\n  boundariesElement,\n  fixedPosition = false\n) {\n  // NOTE: 1 DOM access here\n\n  let boundaries = { top: 0, left: 0 };\n  const offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport' ) {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  }\n\n  else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(\n      boundariesNode,\n      offsetParent,\n      fixedPosition\n    );\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const { height, width } = getWindowSizes(popper.ownerDocument);\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  const isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0; \n  boundaries.top += isPaddingNumber ? padding : padding.top || 0; \n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0; \n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0; \n\n  return boundaries;\n}\n", "import getBoundaries from '../utils/getBoundaries';\n\nfunction getArea({ width, height }) {\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeAutoPlacement(\n  placement,\n  refRect,\n  popper,\n  reference,\n  boundariesElement,\n  padding = 0\n) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(\n    popper,\n    reference,\n    padding,\n    boundariesElement\n  );\n\n  const rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top,\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height,\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom,\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height,\n    },\n  };\n\n  const sortedAreas = Object.keys(rects)\n    .map(key => ({\n      key,\n      ...rects[key],\n      area: getArea(rects[key]),\n    }))\n    .sort((a, b) => b.area - a.area);\n\n  const filteredAreas = sortedAreas.filter(\n    ({ width, height }) =>\n      width >= popper.clientWidth && height >= popper.clientHeight\n  );\n\n  const computedPlacement = filteredAreas.length > 0\n    ? filteredAreas[0].key\n    : sortedAreas[0].key;\n\n  const variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n", "import findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\nimport getReferenceNode from './getReferenceNode';\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nexport default function getReferenceOffsets(state, popper, reference, fixedPosition = null) {\n  const commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n", "/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nexport default function getOuterSizes(element) {\n  const window = element.ownerDocument.defaultView;\n  const styles = window.getComputedStyle(element);\n  const x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  const y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  const result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x,\n  };\n  return result;\n}\n", "/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nexport default function getOppositePlacement(placement) {\n  const hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n", "import getOuterSizes from './getOuterSizes';\nimport getOppositePlacement from './getOppositePlacement';\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nexport default function getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  const popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  const popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height,\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] =\n    referenceOffsets[mainSide] +\n    referenceOffsets[measurement] / 2 -\n    popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] =\n      referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] =\n      referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n", "/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n", "import find from './find';\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(cur => cur[prop] === value);\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  const match = find(arr, obj => obj[prop] === value);\n  return arr.indexOf(match);\n}\n", "import isFunction from './isFunction';\nimport findIndex from './findIndex';\nimport getClientRect from '../utils/getClientRect';\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nexport default function runModifiers(modifiers, data, ends) {\n  const modifiersToRun = ends === undefined\n    ? modifiers\n    : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(modifier => {\n    if (modifier['function']) { // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    const fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n", "import computeAutoPlacement from '../utils/computeAutoPlacement';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nexport default function update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  let data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {},\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(\n    this.state,\n    this.popper,\n    this.reference,\n    this.options.positionFixed\n  );\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(\n    this.options.placement,\n    data.offsets.reference,\n    this.popper,\n    this.reference,\n    this.options.modifiers.flip.boundariesElement,\n    this.options.modifiers.flip.padding\n  );\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(\n    this.popper,\n    data.offsets.reference,\n    data.placement\n  );\n\n  data.offsets.popper.position = this.options.positionFixed\n    ? 'fixed'\n    : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n", "/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nexport default function isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(\n    ({ name, enabled }) => enabled && name === modifierName\n  );\n}\n", "/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nexport default function getSupportedPropertyName(property) {\n  const prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  const upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (let i = 0; i < prefixes.length; i++) {\n    const prefix = prefixes[i];\n    const toCheck = prefix ? `${prefix}${upperProp}` : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n", "import isModifierEnabled from '../utils/isModifierEnabled';\nimport getSupportedPropertyName from '../utils/getSupportedPropertyName';\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nexport default function destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicitly asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n", "/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nexport default function getWindow(element) {\n  const ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n", "import getScrollParent from './getScrollParent';\nimport getWindow from './getWindow';\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  const isBody = scrollParent.nodeName === 'BODY';\n  const target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(\n      getScrollParent(target.parentNode),\n      event,\n      callback,\n      scrollParents\n    );\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function setupEventListeners(\n  reference,\n  options,\n  state,\n  updateBound\n) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  const scrollElement = getScrollParent(reference);\n  attachToScrollParents(\n    scrollElement,\n    'scroll',\n    state.updateBound,\n    state.scrollParents\n  );\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n", "import setupEventListeners from '../utils/setupEventListeners';\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nexport default function enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(\n      this.reference,\n      this.options,\n      this.state,\n      this.scheduleUpdate\n    );\n  }\n}\n", "import getWindow from './getWindow';\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(target => {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n", "import removeEventListeners from '../utils/removeEventListeners';\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nexport default function disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n", "/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nexport default function isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n", "import isNumeric from './isNumeric';\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setStyles(element, styles) {\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (\n      ['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !==\n        -1 &&\n      isNumeric(styles[prop])\n    ) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n", "/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function(prop) {\n    const value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n", "import setStyles from '../utils/setStyles';\nimport setAttributes from '../utils/setAttributes';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport computeAutoPlacement from '../utils/computeAutoPlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nexport default function applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nexport function applyStyleOnLoad(\n  reference,\n  popper,\n  options,\n  modifierOptions,\n  state\n) {\n  // compute reference element offsets\n  const referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  const placement = computeAutoPlacement(\n    options.placement,\n    referenceOffsets,\n    popper,\n    reference,\n    options.modifiers.flip.boundariesElement,\n    options.modifiers.flip.padding\n  );\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: options.positionFixed ? 'fixed' : 'absolute' });\n\n  return options;\n}\n", "/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nexport default function getRoundedOffsets(data, shouldRound) {\n  const { popper, reference } = data.offsets;\n  const { round, floor } = Math;\n  const noRound = v => v;\n  \n  const referenceWidth = round(reference.width);\n  const popperWidth = round(popper.width);\n  \n  const isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  const isVariation = data.placement.indexOf('-') !== -1;\n  const sameWidthParity = referenceWidth % 2 === popperWidth % 2;\n  const bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1;\n\n  const horizontalToInteger = !shouldRound\n    ? noRound\n    : isVertical || isVariation || sameWidthParity\n    ? round\n    : floor;\n  const verticalToInteger = !shouldRound ? noRound : round;\n\n  return {\n    left: horizontalToInteger(\n      bothOddWidth && !isVariation && shouldRound\n        ? popper.left - 1\n        : popper.left\n    ),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right),\n  };\n}\n", "import getSupportedPropertyName from '../utils/getSupportedPropertyName';\nimport find from '../utils/find';\nimport getOffsetParent from '../utils/getOffsetParent';\nimport getBoundingClientRect from '../utils/getBoundingClientRect';\nimport getRoundedOffsets from '../utils/getRoundedOffsets';\nimport isBrowser from '../utils/isBrowser';\n\nconst isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeStyle(data, options) {\n  const { x, y } = options;\n  const { popper } = data.offsets;\n\n  // Remove this legacy support in Popper.js v2\n  const legacyGpuAccelerationOption = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'applyStyle'\n  ).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn(\n      'WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!'\n    );\n  }\n  const gpuAcceleration =\n    legacyGpuAccelerationOption !== undefined\n      ? legacyGpuAccelerationOption\n      : options.gpuAcceleration;\n\n  const offsetParent = getOffsetParent(data.instance.popper);\n  const offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  const styles = {\n    position: popper.position,\n  };\n\n  const offsets = getRoundedOffsets(\n    data,\n    window.devicePixelRatio < 2 || !isFirefox\n  );\n\n  const sideA = x === 'bottom' ? 'top' : 'bottom';\n  const sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  const prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  let left, top;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = `translate3d(${left}px, ${top}px, 0)`;\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    const invertTop = sideA === 'bottom' ? -1 : 1;\n    const invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = `${sideA}, ${sideB}`;\n  }\n\n  // Attributes\n  const attributes = {\n    'x-placement': data.placement,\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = { ...attributes, ...data.attributes };\n  data.styles = { ...styles, ...data.styles };\n  data.arrowStyles = { ...data.offsets.arrow, ...data.arrowStyles };\n\n  return data;\n}\n", "import find from './find';\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nexport default function isModifierRequired(\n  modifiers,\n  requestingName,\n  requestedName\n) {\n  const requesting = find(modifiers, ({ name }) => name === requestingName);\n\n  const isRequired =\n    !!requesting &&\n    modifiers.some(modifier => {\n      return (\n        modifier.name === requestedName &&\n        modifier.enabled &&\n        modifier.order < requesting.order\n      );\n    });\n\n  if (!isRequired) {\n    const requesting = `\\`${requestingName}\\``;\n    const requested = `\\`${requestedName}\\``;\n    console.warn(\n      `${requested} modifier is required by ${requesting} modifier in order to work, be sure to include it before ${requesting}!`\n    );\n  }\n  return isRequired;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOuterSizes from '../utils/getOuterSizes';\nimport isModifierRequired from '../utils/isModifierRequired';\nimport getStyleComputedProperty from '../utils/getStyleComputedProperty';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function arrow(data, options) {\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  let arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn(\n        'WARNING: `arrow.element` must be child of its popper element!'\n      );\n      return data;\n    }\n  }\n\n  const placement = data.placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  const len = isVertical ? 'height' : 'width';\n  const sideCapitalized = isVertical ? 'Top' : 'Left';\n  const side = sideCapitalized.toLowerCase();\n  const altSide = isVertical ? 'left' : 'top';\n  const opSide = isVertical ? 'bottom' : 'right';\n  const arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -=\n      popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] +=\n      reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  const center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  const css = getStyleComputedProperty(data.instance.popper);\n  const popperMarginSide = parseFloat(css[`margin${sideCapitalized}`]);\n  const popperBorderSide = parseFloat(css[`border${sideCapitalized}Width`]);\n  let sideValue =\n    center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = {\n    [side]: Math.round(sideValue),\n    [altSide]: '', // make sure to unset any eventual altSide value from the DOM node\n  };\n\n  return data;\n}\n", "/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nexport default function getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n", "/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nexport default [\n  'auto-start',\n  'auto',\n  'auto-end',\n  'top-start',\n  'top',\n  'top-end',\n  'right-start',\n  'right',\n  'right-end',\n  'bottom-end',\n  'bottom',\n  'bottom-start',\n  'left-end',\n  'left',\n  'left-start',\n];\n", "import placements from '../methods/placements';\n\n// Get rid of `auto` `auto-start` and `auto-end`\nconst validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nexport default function clockwise(placement, counter = false) {\n  const index = validPlacements.indexOf(placement);\n  const arr = validPlacements\n    .slice(index + 1)\n    .concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n", "import getOppositePlacement from '../utils/getOppositePlacement';\nimport getOppositeVariation from '../utils/getOppositeVariation';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\nimport getBoundaries from '../utils/getBoundaries';\nimport isModifierEnabled from '../utils/isModifierEnabled';\nimport clockwise from '../utils/clockwise';\n\nconst BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise',\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    options.boundariesElement,\n    data.positionFixed\n  );\n\n  let placement = data.placement.split('-')[0];\n  let placementOpposite = getOppositePlacement(placement);\n  let variation = data.placement.split('-')[1] || '';\n\n  let flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach((step, index) => {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    const popperOffsets = data.offsets.popper;\n    const refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    const floor = Math.floor;\n    const overlapsRef =\n      (placement === 'left' &&\n        floor(popperOffsets.right) > floor(refOffsets.left)) ||\n      (placement === 'right' &&\n        floor(popperOffsets.left) < floor(refOffsets.right)) ||\n      (placement === 'top' &&\n        floor(popperOffsets.bottom) > floor(refOffsets.top)) ||\n      (placement === 'bottom' &&\n        floor(popperOffsets.top) < floor(refOffsets.bottom));\n\n    const overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    const overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    const overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    const overflowsBottom =\n      floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    const overflowsBoundaries =\n      (placement === 'left' && overflowsLeft) ||\n      (placement === 'right' && overflowsRight) ||\n      (placement === 'top' && overflowsTop) ||\n      (placement === 'bottom' && overflowsBottom);\n\n    // flip the variation if required\n    const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n\n    // flips variation if reference element overflows boundaries\n    const flippedVariationByRef =\n      !!options.flipVariations &&\n      ((isVertical && variation === 'start' && overflowsLeft) ||\n        (isVertical && variation === 'end' && overflowsRight) ||\n        (!isVertical && variation === 'start' && overflowsTop) ||\n        (!isVertical && variation === 'end' && overflowsBottom));\n\n    // flips variation if popper content overflows boundaries\n    const flippedVariationByContent =\n      !!options.flipVariationsByContent &&\n      ((isVertical && variation === 'start' && overflowsRight) ||\n        (isVertical && variation === 'end' && overflowsLeft) ||\n        (!isVertical && variation === 'start' && overflowsBottom) ||\n        (!isVertical && variation === 'end' && overflowsTop));\n\n    const flippedVariation = flippedVariationByRef || flippedVariationByContent;\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = {\n        ...data.offsets.popper,\n        ...getPopperOffsets(\n          data.instance.popper,\n          data.offsets.reference,\n          data.placement\n        ),\n      };\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function keepTogether(data) {\n  const { popper, reference } = data.offsets;\n  const placement = data.placement.split('-')[0];\n  const floor = Math.floor;\n  const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  const side = isVertical ? 'right' : 'bottom';\n  const opSide = isVertical ? 'left' : 'top';\n  const measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] =\n      floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n", "import isNumeric from '../utils/isNumeric';\nimport getClientRect from '../utils/getClientRect';\nimport find from '../utils/find';\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nexport function toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  const split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  const value = +split[1];\n  const unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    let element;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    const rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    let size;\n    if (unit === 'vh') {\n      size = Math.max(\n        document.documentElement.clientHeight,\n        window.innerHeight || 0\n      );\n    } else {\n      size = Math.max(\n        document.documentElement.clientWidth,\n        window.innerWidth || 0\n      );\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nexport function parseOffset(\n  offset,\n  popperOffsets,\n  referenceOffsets,\n  basePlacement\n) {\n  const offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  const useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  const fragments = offset.split(/(\\+|\\-)/).map(frag => frag.trim());\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  const divider = fragments.indexOf(\n    find(fragments, frag => frag.search(/,|\\s/) !== -1)\n  );\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn(\n      'Offsets separated by white space(s) are deprecated, use a comma (,) instead.'\n    );\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  const splitRegex = /\\s*,\\s*|\\s+/;\n  let ops = divider !== -1\n    ? [\n        fragments\n          .slice(0, divider)\n          .concat([fragments[divider].split(splitRegex)[0]]),\n        [fragments[divider].split(splitRegex)[1]].concat(\n          fragments.slice(divider + 1)\n        ),\n      ]\n    : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map((op, index) => {\n    // Most of the units rely on the orientation of the popper\n    const measurement = (index === 1 ? !useHeight : useHeight)\n      ? 'height'\n      : 'width';\n    let mergeWithPrevious = false;\n    return (\n      op\n        // This aggregates any `+` or `-` sign that aren't considered operators\n        // e.g.: 10 + +5 => [10, +, +5]\n        .reduce((a, b) => {\n          if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n            a[a.length - 1] = b;\n            mergeWithPrevious = true;\n            return a;\n          } else if (mergeWithPrevious) {\n            a[a.length - 1] += b;\n            mergeWithPrevious = false;\n            return a;\n          } else {\n            return a.concat(b);\n          }\n        }, [])\n        // Here we convert the string values into number values (in px)\n        .map(str => toValue(str, measurement, popperOffsets, referenceOffsets))\n    );\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach((op, index) => {\n    op.forEach((frag, index2) => {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nexport default function offset(data, { offset }) {\n  const { placement, offsets: { popper, reference } } = data;\n  const basePlacement = placement.split('-')[0];\n\n  let offsets;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n", "import getOffsetParent from '../utils/getOffsetParent';\nimport getBoundaries from '../utils/getBoundaries';\nimport getSupportedPropertyName from '../utils/getSupportedPropertyName';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function preventOverflow(data, options) {\n  let boundariesElement =\n    options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  const transformProp = getSupportedPropertyName('transform');\n  const popperStyles = data.instance.popper.style; // assignment to help minification\n  const { top, left, [transformProp]: transform } = popperStyles;\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    boundariesElement,\n    data.positionFixed\n  );\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n\n  options.boundaries = boundaries;\n\n  const order = options.priority;\n  let popper = data.offsets.popper;\n\n  const check = {\n    primary(placement) {\n      let value = popper[placement];\n      if (\n        popper[placement] < boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return { [placement]: value };\n    },\n    secondary(placement) {\n      const mainSide = placement === 'right' ? 'left' : 'top';\n      let value = popper[mainSide];\n      if (\n        popper[placement] > boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.min(\n          popper[mainSide],\n          boundaries[placement] -\n            (placement === 'right' ? popper.width : popper.height)\n        );\n      }\n      return { [mainSide]: value };\n    },\n  };\n\n  order.forEach(placement => {\n    const side =\n      ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = { ...popper, ...check[side](placement) };\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function shift(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    const { reference, popper } = data.offsets;\n    const isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    const side = isVertical ? 'left' : 'top';\n    const measurement = isVertical ? 'width' : 'height';\n\n    const shiftOffsets = {\n      start: { [side]: reference[side] },\n      end: {\n        [side]: reference[side] + reference[measurement] - popper[measurement],\n      },\n    };\n\n    data.offsets.popper = { ...popper, ...shiftOffsets[shiftvariation] };\n  }\n\n  return data;\n}\n", "import isModifierRequired from '../utils/isModifierRequired';\nimport find from '../utils/find';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  const refRect = data.offsets.reference;\n  const bound = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'preventOverflow'\n  ).boundaries;\n\n  if (\n    refRect.bottom < bound.top ||\n    refRect.left > bound.right ||\n    refRect.top > bound.bottom ||\n    refRect.right < bound.left\n  ) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOppositePlacement from '../utils/getOppositePlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function inner(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  const subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] =\n    reference[basePlacement] -\n    (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n", "import applyStyle, { applyStyleOnLoad } from './applyStyle';\nimport computeStyle from './computeStyle';\nimport arrow from './arrow';\nimport flip from './flip';\nimport keepTogether from './keepTogether';\nimport offset from './offset';\nimport preventOverflow from './preventOverflow';\nimport shift from './shift';\nimport hide from './hide';\nimport inner from './inner';\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nexport default {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift,\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0,\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent',\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether,\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]',\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport',\n    /**\n     * @prop {Boolean} flipVariations=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the reference element overlaps its boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariations: false,\n    /**\n     * @prop {Boolean} flipVariationsByContent=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the popper element overlaps its reference boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariationsByContent: false,\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner,\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide,\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right',\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined,\n  },\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n", "import modifiers from '../modifiers/index';\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nexport default {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: () => {},\n\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: () => {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers,\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n", "// Utils\nimport debounce from './utils/debounce';\nimport isFunction from './utils/isFunction';\n\n// Methods\nimport update from './methods/update';\nimport destroy from './methods/destroy';\nimport enableEventListeners from './methods/enableEventListeners';\nimport disableEventListeners from './methods/disableEventListeners';\nimport Defaults from './methods/defaults';\nimport placements from './methods/placements';\n\nexport default class Popper {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {Element|referenceObject} reference - The reference element used to position the popper\n   * @param {Element} popper - The HTML / XML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  constructor(reference, popper, options = {}) {\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = { ...Popper.Defaults, ...options };\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: [],\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys({\n      ...Popper.Defaults.modifiers,\n      ...options.modifiers,\n    }).forEach(name => {\n      this.options.modifiers[name] = {\n        // If it's a built-in modifier, use it as base\n        ...(Popper.Defaults.modifiers[name] || {}),\n        // If there are custom options, override and merge with default ones\n        ...(options.modifiers ? options.modifiers[name] : {}),\n      };\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers)\n      .map(name => ({\n        name,\n        ...this.options.modifiers[name],\n      }))\n      // sort the modifiers by order\n      .sort((a, b) => a.order - b.order);\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(modifierOptions => {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(\n          this.reference,\n          this.popper,\n          this.options,\n          modifierOptions,\n          this.state\n        );\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    const eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n  update() {\n    return update.call(this);\n  }\n  destroy() {\n    return destroy.call(this);\n  }\n  enableEventListeners() {\n    return enableEventListeners.call(this);\n  }\n  disableEventListeners() {\n    return disableEventListeners.call(this);\n  }\n\n  /**\n   * Schedules an update. It will run on the next UI update available.\n   * @method scheduleUpdate\n   * @memberof Popper\n   */\n  scheduleUpdate = () => requestAnimationFrame(this.update);\n\n  /**\n   * Collection of utilities useful when writing custom modifiers.\n   * Starting from version 1.7, this method is available only if you\n   * include `popper-utils.js` before `popper.js`.\n   *\n   * **DEPRECATION**: This way to access PopperUtils is deprecated\n   * and will be removed in v2! Use the PopperUtils module directly instead.\n   * Due to the high instability of the methods contained in Utils, we can't\n   * guarantee them to follow semver. Use them at your own risk!\n   * @static\n   * @private\n   * @type {Object}\n   * @deprecated since version 1.8\n   * @member Utils\n   * @memberof Popper\n   */\n  static Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\n\n  static placements = placements;\n\n  static Defaults = Defaults;\n}\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n", "import { Class, PlainObject, Primitive } from './types';\n\nconst DOM_PROPERTIES_TO_CHECK: Array<keyof HTMLElement> = [\n  'innerHTML',\n  'ownerDocument',\n  'style',\n  'attributes',\n  'nodeValue',\n];\n\nconst objectTypes = [\n  'Array',\n  'ArrayBuffer',\n  'AsyncFunction',\n  'AsyncGenerator',\n  'AsyncGeneratorFunction',\n  'Date',\n  'Error',\n  'Function',\n  'Generator',\n  'GeneratorFunction',\n  'HTMLElement',\n  'Map',\n  'Object',\n  'Promise',\n  'RegExp',\n  'Set',\n  'WeakMap',\n  'WeakSet',\n] as const;\n\nconst primitiveTypes = [\n  'bigint',\n  'boolean',\n  'null',\n  'number',\n  'string',\n  'symbol',\n  'undefined',\n] as const;\n\nexport type ObjectTypes = typeof objectTypes[number];\n\nexport type PrimitiveTypes = typeof primitiveTypes[number];\n\nexport type TypeName = ObjectTypes | PrimitiveTypes;\n\nexport function getObjectType(value: unknown): ObjectTypes | undefined {\n  const objectTypeName = Object.prototype.toString.call(value).slice(8, -1);\n\n  if (/HTML\\w+Element/.test(objectTypeName)) {\n    return 'HTMLElement';\n  }\n\n  if (isObjectType(objectTypeName)) {\n    return objectTypeName;\n  }\n\n  return undefined;\n}\n\nfunction isObjectOfType<T>(type: string) {\n  return (value: unknown): value is T => getObjectType(value) === type;\n}\n\nfunction isObjectType(name: unknown): name is ObjectTypes {\n  return objectTypes.includes(name as ObjectTypes);\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isOfType<T extends Primitive | Function>(type: string) {\n  return (value: unknown): value is T => typeof value === type;\n}\n\nfunction isPrimitiveType(name: unknown): name is PrimitiveTypes {\n  return primitiveTypes.includes(name as PrimitiveTypes);\n}\n\nfunction is(value: unknown): TypeName {\n  if (value === null) {\n    return 'null';\n  }\n\n  switch (typeof value) {\n    case 'bigint':\n      return 'bigint';\n    case 'boolean':\n      return 'boolean';\n    case 'number':\n      return 'number';\n    case 'string':\n      return 'string';\n    case 'symbol':\n      return 'symbol';\n    case 'undefined':\n      return 'undefined';\n    default:\n  }\n\n  if (is.array(value)) {\n    return 'Array';\n  }\n\n  if (is.plainFunction(value)) {\n    return 'Function';\n  }\n\n  const tagType = getObjectType(value);\n\n  /* istanbul ignore else */\n  if (tagType) {\n    return tagType;\n  }\n\n  /* istanbul ignore next */\n  return 'Object';\n}\n\nis.array = Array.isArray;\n\nis.arrayOf = (target: unknown[], predicate: (v: unknown) => boolean): boolean => {\n  if (!is.array(target) && !is.function(predicate)) {\n    return false;\n  }\n\n  return target.every(d => predicate(d));\n};\n\nis.asyncGeneratorFunction = (value: unknown): value is (...arguments_: any[]) => Promise<unknown> =>\n  getObjectType(value) === 'AsyncGeneratorFunction';\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nis.asyncFunction = isObjectOfType<Function>('AsyncFunction');\n\nis.bigint = isOfType<bigint>('bigint');\n\nis.boolean = (value: unknown): value is boolean => {\n  return value === true || value === false;\n};\n\nis.date = isObjectOfType<Date>('Date');\n\nis.defined = (value: unknown): boolean => !is.undefined(value);\n\nis.domElement = (value: unknown): value is HTMLElement => {\n  return (\n    is.object(value) &&\n    !is.plainObject(value) &&\n    (value as HTMLElement).nodeType === 1 &&\n    is.string((value as HTMLElement).nodeName) &&\n    DOM_PROPERTIES_TO_CHECK.every(property => property in (value as HTMLElement))\n  );\n};\n\nis.empty = (value: unknown): boolean => {\n  return (\n    (is.string(value) && value.length === 0) ||\n    (is.array(value) && value.length === 0) ||\n    (is.object(value) && !is.map(value) && !is.set(value) && Object.keys(value).length === 0) ||\n    (is.set(value) && value.size === 0) ||\n    (is.map(value) && value.size === 0)\n  );\n};\n\nis.error = isObjectOfType<Error>('Error');\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nis.function = isOfType<Function>('function');\n\nis.generator = (value: unknown): value is Generator => {\n  return (\n    is.iterable(value) &&\n    is.function((value as IterableIterator<unknown>).next) &&\n    is.function((value as IterableIterator<unknown>).throw)\n  );\n};\n\nis.generatorFunction = isObjectOfType<GeneratorFunction>('GeneratorFunction');\n\nis.instanceOf = <T>(instance: unknown, class_: Class<T>): instance is T => {\n  if (!instance || !(class_ as Class<T>)) {\n    return false;\n  }\n\n  return Object.getPrototypeOf(instance) === class_.prototype;\n};\n\nis.iterable = (value: unknown): value is IterableIterator<unknown> => {\n  return (\n    !is.nullOrUndefined(value) && is.function((value as IterableIterator<unknown>)[Symbol.iterator])\n  );\n};\n\nis.map = isObjectOfType<Map<unknown, unknown>>('Map');\n\nis.nan = (value: unknown): boolean => {\n  return Number.isNaN(value as number);\n};\n\nis.null = (value: unknown): value is null => {\n  return value === null;\n};\n\nis.nullOrUndefined = (value: unknown): value is null | undefined => {\n  return is.null(value) || is.undefined(value);\n};\n\nis.number = (value: unknown): value is number => {\n  return isOfType<number>('number')(value) && !is.nan(value);\n};\n\nis.numericString = (value: unknown): value is string => {\n  return is.string(value) && (value as string).length > 0 && !Number.isNaN(Number(value));\n};\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nis.object = (value: unknown): value is object => {\n  return !is.nullOrUndefined(value) && (is.function(value) || typeof value === 'object');\n};\n\nis.oneOf = (target: unknown[], value: any): boolean => {\n  if (!is.array(target)) {\n    return false;\n  }\n\n  // eslint-disable-next-line unicorn/prefer-includes\n  return target.indexOf(value) > -1;\n};\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nis.plainFunction = isObjectOfType<Function>('Function');\n\nis.plainObject = (value: unknown): value is PlainObject => {\n  if (getObjectType(value) !== 'Object') {\n    return false;\n  }\n\n  const prototype = Object.getPrototypeOf(value);\n\n  return prototype === null || prototype === Object.getPrototypeOf({});\n};\n\nis.primitive = (value: unknown): value is Primitive =>\n  is.null(value) || isPrimitiveType(typeof value);\n\nis.promise = isObjectOfType<Promise<unknown>>('Promise');\n\nis.propertyOf = (\n  target: PlainObject,\n  key: string,\n  predicate?: (v: unknown) => boolean,\n): boolean => {\n  if (!is.object(target) || !key) {\n    return false;\n  }\n\n  const value = target[key];\n\n  if (is.function(predicate)) {\n    return predicate(value);\n  }\n\n  return is.defined(value);\n};\n\nis.regexp = isObjectOfType<RegExp>('RegExp');\n\nis.set = isObjectOfType<Set<PlainObject>>('Set');\n\nis.string = isOfType<string>('string');\n\nis.symbol = isOfType<symbol>('symbol');\n\nis.undefined = isOfType<undefined>('undefined');\n\nis.weakMap = isObjectOfType<WeakMap<PlainObject, unknown>>('WeakMap');\n\nis.weakSet = isObjectOfType<WeakSet<PlainObject>>('WeakSet');\n\nexport * from './types';\n\nexport default is;\n", "import { AnyObject, Primitive } from './types';\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isOfType<T extends Primitive | Function>(type: string) {\n  return (value: unknown): value is T => typeof value === type;\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport const isFunction = isOfType<Function>('function');\n\nexport const isNull = (value: unknown): value is null => {\n  return value === null;\n};\n\nexport const isRegex = (value: unknown): value is RegExp => {\n  return Object.prototype.toString.call(value).slice(8, -1) === 'RegExp';\n};\n\nexport const isObject = (value: unknown): value is AnyObject => {\n  return !isUndefined(value) && !isNull(value) && (isFunction(value) || typeof value === 'object');\n};\n\nexport const isUndefined = isOfType<undefined>('undefined');\n", "import { isObject, isRegex } from './helpers';\n\nfunction equalArray(left: unknown[], right: unknown[]) {\n  const { length } = left;\n\n  if (length !== right.length) {\n    return false;\n  }\n\n  for (let index = length; index-- !== 0; ) {\n    if (!equal(left[index], right[index])) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction equalArrayBuffer(left: ArrayBufferView, right: ArrayBufferView) {\n  if (left.byteLength !== right.byteLength) {\n    return false;\n  }\n\n  const view1 = new DataView(left.buffer);\n  const view2 = new DataView(right.buffer);\n\n  let index = left.byteLength;\n\n  while (index--) {\n    if (view1.getUint8(index) !== view2.getUint8(index)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction equalMap(left: Map<unknown, unknown>, right: Map<unknown, unknown>) {\n  if (left.size !== right.size) {\n    return false;\n  }\n\n  for (const index of left.entries()) {\n    if (!right.has(index[0])) {\n      return false;\n    }\n  }\n\n  for (const index of left.entries()) {\n    if (!equal(index[1], right.get(index[0]))) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction equalSet(left: Set<unknown>, right: Set<unknown>) {\n  if (left.size !== right.size) {\n    return false;\n  }\n\n  for (const index of left.entries()) {\n    if (!right.has(index[0])) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nexport default function equal(left: unknown, right: unknown) {\n  if (left === right) {\n    return true;\n  }\n\n  if (left && isObject(left) && right && isObject(right)) {\n    if (left.constructor !== right.constructor) {\n      return false;\n    }\n\n    if (Array.isArray(left) && Array.isArray(right)) {\n      return equalArray(left, right);\n    }\n\n    if (left instanceof Map && right instanceof Map) {\n      return equalMap(left, right);\n    }\n\n    if (left instanceof Set && right instanceof Set) {\n      return equalSet(left, right);\n    }\n\n    if (ArrayBuffer.isView(left) && ArrayBuffer.isView(right)) {\n      return equalArrayBuffer(left, right);\n    }\n\n    if (isRegex(left) && isRegex(right)) {\n      return left.source === right.source && left.flags === right.flags;\n    }\n\n    if (left.valueOf !== Object.prototype.valueOf) {\n      return left.valueOf() === right.valueOf();\n    }\n\n    if (left.toString !== Object.prototype.toString) {\n      return left.toString() === right.toString();\n    }\n\n    const leftKeys = Object.keys(left);\n    const rightKeys = Object.keys(right);\n\n    if (leftKeys.length !== rightKeys.length) {\n      return false;\n    }\n\n    for (let index = leftKeys.length; index-- !== 0; ) {\n      if (!Object.prototype.hasOwnProperty.call(right, leftKeys[index])) {\n        return false;\n      }\n    }\n\n    for (let index = leftKeys.length; index-- !== 0; ) {\n      const key = leftKeys[index];\n\n      if (key === '_owner' && left.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner.\n        //  _owner contains circular references\n        // and is not needed when comparing the actual elements (and not their owners)\n        // eslint-disable-next-line no-continue\n        continue;\n      }\n\n      if (!equal(left[key], right[key])) {\n        return false;\n      }\n    }\n\n    return true;\n  }\n\n  if (Number.isNaN(left) && Number.isNaN(right)) {\n    return true;\n  }\n\n  return left === right;\n}\n", "import equal from '@gilbarbara/deep-equal';\nimport is from 'is-lite';\n\nimport { CompareValuesOptions, Data, Key, Options, ValidTypes, Value } from './types';\n\nexport function canHaveLength(...arguments_: any): boolean {\n  return arguments_.every((d: unknown) => is.string(d) || is.array(d) || is.plainObject(d));\n}\n\nexport function checkEquality(left: Data, right: Data, value: Value) {\n  if (!isSameType(left, right)) {\n    return false;\n  }\n\n  if ([left, right].every(is.array)) {\n    return !left.some(hasValue(value)) && right.some(hasValue(value));\n  }\n\n  /* istanbul ignore else */\n  if ([left, right].every(is.plainObject)) {\n    return (\n      !Object.entries(left).some(hasEntry(value)) && Object.entries(right).some(hasEntry(value))\n    );\n  }\n\n  return right === value;\n}\n\nexport function compareNumbers<K = Key>(\n  previousData: Data,\n  data: Data,\n  options: Options<K>,\n): boolean {\n  const { actual, key, previous, type } = options;\n  const left = nested(previousData, key);\n  const right = nested(data, key);\n\n  let changed =\n    [left, right].every(is.number) && (type === 'increased' ? left < right : left > right);\n\n  if (!is.undefined(actual)) {\n    changed = changed && right === actual;\n  }\n\n  if (!is.undefined(previous)) {\n    changed = changed && left === previous;\n  }\n\n  return changed;\n}\n\nexport function compareValues<K = Key>(\n  previousData: Data,\n  data: Data,\n  options: CompareValuesOptions<K>,\n) {\n  const { key, type, value } = options;\n\n  const left = nested(previousData, key);\n  const right = nested(data, key);\n  const primary = type === 'added' ? left : right;\n  const secondary = type === 'added' ? right : left;\n\n  // console.log({ primary, secondary });\n\n  if (!is.nullOrUndefined(value)) {\n    if (is.defined(primary)) {\n      // check if nested data matches\n      if (is.array(primary) || is.plainObject(primary)) {\n        return checkEquality(primary, secondary, value);\n      }\n    } else {\n      return equal(secondary, value);\n    }\n\n    return false;\n  }\n\n  if ([left, right].every(is.array)) {\n    return !secondary.every(isEqualPredicate(primary));\n  }\n\n  if ([left, right].every(is.plainObject)) {\n    return hasExtraKeys(Object.keys(primary), Object.keys(secondary));\n  }\n\n  return (\n    ![left, right].every(d => is.primitive(d) && is.defined(d)) &&\n    (type === 'added'\n      ? !is.defined(left) && is.defined(right)\n      : is.defined(left) && !is.defined(right))\n  );\n}\n\nexport function getIterables<K = Key>(previousData: Data, data: Data, { key }: Options<K> = {}) {\n  let left = nested(previousData, key);\n  let right = nested(data, key);\n\n  if (!isSameType(left, right)) {\n    throw new TypeError('Inputs have different types');\n  }\n\n  if (!canHaveLength(left, right)) {\n    throw new TypeError(\"Inputs don't have length\");\n  }\n\n  if ([left, right].every(is.plainObject)) {\n    left = Object.keys(left);\n    right = Object.keys(right);\n  }\n\n  return [left, right];\n}\n\nexport function hasEntry(input: Value) {\n  return ([key, value]: [string, Value]) => {\n    if (is.array(input)) {\n      return (\n        equal(input, value) ||\n        input.some(d => equal(d, value) || (is.array(value) && isEqualPredicate(value)(d)))\n      );\n    }\n\n    /* istanbul ignore else */\n    if (is.plainObject(input) && input[key]) {\n      return !!input[key] && equal(input[key], value);\n    }\n\n    return equal(input, value);\n  };\n}\n\nexport function hasExtraKeys(left: string[], right: string[]): boolean {\n  return right.some(d => !left.includes(d));\n}\n\nexport function hasValue(input: Value) {\n  return (value: Value) => {\n    if (is.array(input)) {\n      return input.some(d => equal(d, value) || (is.array(value) && isEqualPredicate(value)(d)));\n    }\n\n    return equal(input, value);\n  };\n}\n\nexport function includesOrEqualsTo<T>(previousValue: T | T[], value: T): boolean {\n  return is.array(previousValue)\n    ? previousValue.some(d => equal(d, value))\n    : equal(previousValue, value);\n}\n\nexport function isEqualPredicate(data: unknown[]) {\n  return (value: unknown) => data.some(d => equal(d, value));\n}\n\nexport function isSameType(...arguments_: ValidTypes[]): boolean {\n  return (\n    arguments_.every(is.array) ||\n    arguments_.every(is.number) ||\n    arguments_.every(is.plainObject) ||\n    arguments_.every(is.string)\n  );\n}\n\nexport function nested<T extends Data, K = Key>(data: T, property?: K) {\n  /* istanbul ignore else */\n  if (is.plainObject(data) || is.array(data)) {\n    /* istanbul ignore else */\n    if (is.string(property)) {\n      const props: Array<any> = property.split('.');\n\n      return props.reduce((acc, d) => acc && acc[d], data);\n    }\n\n    /* istanbul ignore else */\n    if (is.number(property)) {\n      return data[property];\n    }\n\n    return data;\n  }\n\n  return data;\n}\n", "import equal from '@gilbarbara/deep-equal';\nimport is from 'is-lite';\n\nimport { compareNumbers, compareValues, getIterables, includesOrEqualsTo, nested } from './helpers';\nimport { Data, KeyType, TreeChanges, Value } from './types';\n\nexport default function treeChanges<P extends Data, D extends Data, K = KeyType<P, D>>(\n  previousData: P,\n  data: D,\n): TreeChanges<K> {\n  if ([previousData, data].some(is.nullOrUndefined)) {\n    throw new Error('Missing required parameters');\n  }\n\n  if (![previousData, data].every(d => is.plainObject(d) || is.array(d))) {\n    throw new Error('Expected plain objects or array');\n  }\n\n  const added = (key?: K, value?: Value): boolean => {\n    try {\n      return compareValues<K>(previousData, data, { key, type: 'added', value });\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const changed = (key?: K | string, actual?: Value, previous?: Value): boolean => {\n    try {\n      const left = nested(previousData, key);\n      const right = nested(data, key);\n      const hasActual = is.defined(actual);\n      const hasPrevious = is.defined(previous);\n\n      if (hasActual || hasPrevious) {\n        const leftComparator = hasPrevious\n          ? includesOrEqualsTo(previous, left)\n          : !includesOrEqualsTo(actual, left);\n        const rightComparator = includesOrEqualsTo(actual, right);\n\n        return leftComparator && rightComparator;\n      }\n\n      if ([left, right].every(is.array) || [left, right].every(is.plainObject)) {\n        return !equal(left, right);\n      }\n\n      return left !== right;\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const changedFrom = (key: K | string, previous: Value, actual?: Value): boolean => {\n    if (!is.defined(key)) {\n      return false;\n    }\n\n    try {\n      const left = nested(previousData, key);\n      const right = nested(data, key);\n      const hasActual = is.defined(actual);\n\n      return (\n        includesOrEqualsTo(previous, left) &&\n        (hasActual ? includesOrEqualsTo(actual, right) : !hasActual)\n      );\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  /**\n   * @deprecated\n   * Use \"changed\" instead\n   */\n  const changedTo = (key: K | string, actual: Value): boolean => {\n    if (!is.defined(key)) {\n      return false;\n    }\n\n    /* istanbul ignore next */\n    if (process.env.NODE_ENV === 'development') {\n      // eslint-disable-next-line no-console\n      console.warn('`changedTo` is deprecated! Replace it with `change`');\n    }\n\n    return changed(key, actual);\n  };\n\n  const decreased = (key: K, actual?: Value, previous?: Value): boolean => {\n    if (!is.defined(key)) {\n      return false;\n    }\n\n    try {\n      return compareNumbers<K>(previousData, data, { key, actual, previous, type: 'decreased' });\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const emptied = (key?: K): boolean => {\n    try {\n      const [left, right] = getIterables(previousData, data, { key });\n\n      return !!left.length && !right.length;\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const filled = (key?: K): boolean => {\n    try {\n      const [left, right] = getIterables(previousData, data, { key });\n\n      return !left.length && !!right.length;\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const increased = (key: K, actual?: Value, previous?: Value): boolean => {\n    if (!is.defined(key)) {\n      return false;\n    }\n\n    try {\n      return compareNumbers<K>(previousData, data, { key, actual, previous, type: 'increased' });\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  const removed = (key?: K, value?: Value): boolean => {\n    try {\n      return compareValues<K>(previousData, data, { key, type: 'removed', value });\n    } catch {\n      /* istanbul ignore next */\n      return false;\n    }\n  };\n\n  return { added, changed, changedFrom, changedTo, decreased, emptied, filled, increased, removed };\n}\n\nexport * from './types';\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,aAAa,IAAI,MAAM,2CAA2C;AACtE,QAAI,cAAc,IAAI,MAAM,kBAAkB;AAC9C,QAAI,MAAM,KAAK;AACf,QAAI,KAAK,KAAK;AAEd,WAAO,UAAU;AAAA,MACf,MAAM,KAAK,YAAY;AAAA,MACvB,KAAK,KAAK,WAAW;AAAA,IACvB;AAEA,aAAS,KAAM,MAAM;AACnB,aAAO,SAASA,QAAQ,IAAI,IAAI,MAAM,IAAI;AACxC,eAAO,QAAQ,CAAC;AAEhB,YAAI,OAAO,QAAQ;AAAY,eAAK,MAAM,OAAO,CAAC;AAClD,YAAI,OAAO,MAAM;AAAY,eAAKC;AAElC,YAAI,QAAQ,GAAG;AACf,YAAI,OAAO,GAAG,IAAI;AAClB,YAAI,OAAO,KAAK,QAAQ;AACxB,YAAI,WAAW,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,KAAK,WAAW;AACxD,YAAI,YAAY;AAEhB,eAAO,SAAS,KACd,GAAG,YAAY,GAAG,IAAI,CAAC,IACvB,sBAAsB,OAAO,GAAG;AAElC,iBAAS,SAAU;AACjB,sBAAY;AAAA,QACd;AAEA,iBAAS,QAAS,WAAW;AAC3B,cAAI;AAAW,mBAAO,GAAG,aAAa,GAAG,IAAI,CAAC;AAE9C,cAAI,MAAM,GAAG;AACb,cAAI,OAAO,IAAI,IAAK,MAAM,SAAS,QAAS;AAC5C,cAAI,QAAQ,KAAK,IAAI;AAErB,aAAG,IAAI,IAAK,SAAS,KAAK,QAAS;AAEnC,iBAAO,IACL,sBAAsB,OAAO,IAC7B,sBAAsB,WAAY;AAChC,eAAG,MAAM,GAAG,IAAI,CAAC;AAAA,UACnB,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAEA,aAAS,UAAW,GAAG;AACrB,aAAO,OAAO,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC;AAAA,IACxC;AAEA,aAASA,QAAQ;AAAA,IAAC;AAAA;AAAA;;;ACrDlB;AAAA;AAAA,KAAC,SAAU,MAAM,SAAS;AACxB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAC9C,eAAO,CAAC,GAAG,OAAO;AAAA,MACpB,WAAW,OAAO,WAAW,YAAY,OAAO,SAAS;AACvD,eAAO,UAAU,QAAQ;AAAA,MAC3B,OAAO;AACL,aAAK,eAAe,QAAQ;AAAA,MAC9B;AAAA,IACF,GAAE,SAAM,WAAY;AAClB,eAAS,YAAY,MAAM;AACzB,YAAI,WAAW,iBAAiB,MAAM,IAAI,EAAE,iBAAiB,UAAU;AAEvE,eAAO,SAAS,QAAQ,QAAQ,IAAI,MAAM,SAAS,QAAQ,MAAM,IAAI;AAAA,MACvE;AAEA,eAASC,cAAa,MAAM;AAC1B,YAAI,EAAE,gBAAgB,eAAe,gBAAgB,aAAa;AAChE,iBAAO;AAAA,QACT;AAEA,YAAI,UAAU,KAAK;AACnB,eAAO,QAAQ,YAAY;AACzB,cAAI,YAAY,OAAO,GAAG;AACxB,mBAAO;AAAA,UACT;AAEA,oBAAU,QAAQ;AAAA,QACpB;AAEA,eAAO,SAAS,oBAAoB,SAAS;AAAA,MAC/C;AAEA,aAAOA;AAAA,IACT,CAAC;AAAA;AAAA;;;ACjCD;AAAA;AAAA;AACA,QAAI,WAAW,SAAU,KAAK;AAC1B,aAAO,OAAO,UAAU,eAAe,KAAK,KAAK,OAAO;AAAA,IAC5D;AACA,QAAI,oBAAoB,SAAU,UAAU,SAAS;AACjD,aAAO,WAAWC,WAAU,OAAO;AAAA,IACvC;AACA,QAAIA,aAAY,SAAU,KAAK;AAC3B,UAAI,QAAQ,QACR,OAAO,QAAQ,aACf,OAAO,QAAQ,aAAa;AAC5B,eAAO;AAAA,MACX;AACA,UAAI,OAAO,QAAQ,UAAU;AACzB,eAAO,IAAI,SAAS;AAAA,MACxB;AACA,UAAI,OAAO,QAAQ,UAAU;AACzB,eAAO;AAAA,MACX;AACA,UAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,eAAO,IAAI,OAAO,mBAAmB,EAAE;AAAA,MAC3C;AACA,UAAI,SAAS,GAAG,KACZ,OAAO,UAAU,eAAe,KAAK,IAAI,OAAO,UAAU,GAAG;AAC7D,eAAOA,WAAU,IAAI,MAAM,QAAQ;AAAA,MACvC;AACA,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAUA;AACpB,WAAO,UAAUA;AAAA;AAAA;;;AC7BjB;AAAA;AAAA;AAEA,QAAI,oBAAoB,SAASC,mBAAkB,OAAO;AACzD,aAAO,gBAAgB,KAAK,KACxB,CAAC,UAAU,KAAK;AAAA,IACrB;AAEA,aAAS,gBAAgB,OAAO;AAC/B,aAAO,CAAC,CAAC,SAAS,OAAO,UAAU;AAAA,IACpC;AAEA,aAAS,UAAU,OAAO;AACzB,UAAI,cAAc,OAAO,UAAU,SAAS,KAAK,KAAK;AAEtD,aAAO,gBAAgB,qBACnB,gBAAgB,mBAChB,eAAe,KAAK;AAAA,IACzB;AAGA,QAAI,eAAe,OAAO,WAAW,cAAc,OAAO;AAC1D,QAAI,qBAAqB,eAAe,OAAO,IAAI,eAAe,IAAI;AAEtE,aAAS,eAAe,OAAO;AAC9B,aAAO,MAAM,aAAa;AAAA,IAC3B;AAEA,aAAS,YAAY,KAAK;AACzB,aAAO,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;AAAA,IACnC;AAEA,aAAS,8BAA8B,OAAO,SAAS;AACtD,aAAQ,QAAQ,UAAU,SAAS,QAAQ,kBAAkB,KAAK,IAC/DC,WAAU,YAAY,KAAK,GAAG,OAAO,OAAO,IAC5C;AAAA,IACJ;AAEA,aAAS,kBAAkB,QAAQ,QAAQ,SAAS;AACnD,aAAO,OAAO,OAAO,MAAM,EAAE,IAAI,SAAS,SAAS;AAClD,eAAO,8BAA8B,SAAS,OAAO;AAAA,MACtD,CAAC;AAAA,IACF;AAEA,aAAS,iBAAiB,KAAK,SAAS;AACvC,UAAI,CAAC,QAAQ,aAAa;AACzB,eAAOA;AAAA,MACR;AACA,UAAI,cAAc,QAAQ,YAAY,GAAG;AACzC,aAAO,OAAO,gBAAgB,aAAa,cAAcA;AAAA,IAC1D;AAEA,aAAS,gCAAgC,QAAQ;AAChD,aAAO,OAAO,wBACX,OAAO,sBAAsB,MAAM,EAAE,OAAO,SAAS,QAAQ;AAC9D,eAAO,OAAO,qBAAqB,KAAK,QAAQ,MAAM;AAAA,MACvD,CAAC,IACC,CAAC;AAAA,IACL;AAEA,aAAS,QAAQ,QAAQ;AACxB,aAAO,OAAO,KAAK,MAAM,EAAE,OAAO,gCAAgC,MAAM,CAAC;AAAA,IAC1E;AAEA,aAAS,mBAAmB,QAAQ,UAAU;AAC7C,UAAI;AACH,eAAO,YAAY;AAAA,MACpB,SAAQ,GAAG;AACV,eAAO;AAAA,MACR;AAAA,IACD;AAGA,aAAS,iBAAiB,QAAQ,KAAK;AACtC,aAAO,mBAAmB,QAAQ,GAAG,KACjC,EAAE,OAAO,eAAe,KAAK,QAAQ,GAAG,KACvC,OAAO,qBAAqB,KAAK,QAAQ,GAAG;AAAA,IAClD;AAEA,aAAS,YAAY,QAAQ,QAAQ,SAAS;AAC7C,UAAI,cAAc,CAAC;AACnB,UAAI,QAAQ,kBAAkB,MAAM,GAAG;AACtC,gBAAQ,MAAM,EAAE,QAAQ,SAAS,KAAK;AACrC,sBAAY,GAAG,IAAI,8BAA8B,OAAO,GAAG,GAAG,OAAO;AAAA,QACtE,CAAC;AAAA,MACF;AACA,cAAQ,MAAM,EAAE,QAAQ,SAAS,KAAK;AACrC,YAAI,iBAAiB,QAAQ,GAAG,GAAG;AAClC;AAAA,QACD;AAEA,YAAI,mBAAmB,QAAQ,GAAG,KAAK,QAAQ,kBAAkB,OAAO,GAAG,CAAC,GAAG;AAC9E,sBAAY,GAAG,IAAI,iBAAiB,KAAK,OAAO,EAAE,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,OAAO;AAAA,QACpF,OAAO;AACN,sBAAY,GAAG,IAAI,8BAA8B,OAAO,GAAG,GAAG,OAAO;AAAA,QACtE;AAAA,MACD,CAAC;AACD,aAAO;AAAA,IACR;AAEA,aAASA,WAAU,QAAQ,QAAQ,SAAS;AAC3C,gBAAU,WAAW,CAAC;AACtB,cAAQ,aAAa,QAAQ,cAAc;AAC3C,cAAQ,oBAAoB,QAAQ,qBAAqB;AAGzD,cAAQ,gCAAgC;AAExC,UAAI,gBAAgB,MAAM,QAAQ,MAAM;AACxC,UAAI,gBAAgB,MAAM,QAAQ,MAAM;AACxC,UAAI,4BAA4B,kBAAkB;AAElD,UAAI,CAAC,2BAA2B;AAC/B,eAAO,8BAA8B,QAAQ,OAAO;AAAA,MACrD,WAAW,eAAe;AACzB,eAAO,QAAQ,WAAW,QAAQ,QAAQ,OAAO;AAAA,MAClD,OAAO;AACN,eAAO,YAAY,QAAQ,QAAQ,OAAO;AAAA,MAC3C;AAAA,IACD;AAEA,IAAAA,WAAU,MAAM,SAAS,aAAa,OAAO,SAAS;AACrD,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC1B,cAAM,IAAI,MAAM,mCAAmC;AAAA,MACpD;AAEA,aAAO,MAAM,OAAO,SAAS,MAAM,MAAM;AACxC,eAAOA,WAAU,MAAM,MAAM,OAAO;AAAA,MACrC,GAAG,CAAC,CAAC;AAAA,IACN;AAEA,QAAI,cAAcA;AAElB,WAAO,UAAU;AAAA;AAAA;;;ACpIjB;AAAA;AAAA;AAaA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAIA,YAAI,YAAY,OAAO,WAAW,cAAc,OAAO;AACvD,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AACnE,YAAI,oBAAoB,YAAY,OAAO,IAAI,cAAc,IAAI;AACjE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AAGnE,YAAI,wBAAwB,YAAY,OAAO,IAAI,kBAAkB,IAAI;AACzE,YAAI,6BAA6B,YAAY,OAAO,IAAI,uBAAuB,IAAI;AACnF,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,2BAA2B,YAAY,OAAO,IAAI,qBAAqB,IAAI;AAC/E,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAC/D,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,uBAAuB,YAAY,OAAO,IAAI,iBAAiB,IAAI;AACvE,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAE/D,iBAAS,mBAAmB,MAAM;AAChC,iBAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AAAA,UACnD,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,OAAO,SAAS,YAAY,SAAS,SAAS,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,wBAAwB,KAAK,aAAa,oBAAoB,KAAK,aAAa;AAAA,QACplB;AAEA,iBAAS,OAAO,QAAQ;AACtB,cAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,gBAAI,WAAW,OAAO;AAEtB,oBAAQ,UAAU;AAAA,cAChB,KAAK;AACH,oBAAI,OAAO,OAAO;AAElB,wBAAQ,MAAM;AAAA,kBACZ,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AACH,2BAAO;AAAA,kBAET;AACE,wBAAI,eAAe,QAAQ,KAAK;AAEhC,4BAAQ,cAAc;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AACH,+BAAO;AAAA,sBAET;AACE,+BAAO;AAAA,oBACX;AAAA,gBAEJ;AAAA,cAEF,KAAK;AACH,uBAAO;AAAA,YACX;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY;AAChB,YAAI,iBAAiB;AACrB,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAI,UAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,sCAAsC;AAE1C,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,CAAC,qCAAqC;AACxC,oDAAsC;AAEtC,sBAAQ,MAAM,EAAE,+KAAyL;AAAA,YAC3M;AAAA,UACF;AAEA,iBAAO,iBAAiB,MAAM,KAAK,OAAO,MAAM,MAAM;AAAA,QACxD;AACA,iBAAS,iBAAiB,QAAQ;AAChC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,UAAU,QAAQ;AACzB,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC9E;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,SAAS,QAAQ;AACxB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AAEA,gBAAQ,YAAY;AACpB,gBAAQ,iBAAiB;AACzB,gBAAQ,kBAAkB;AAC1B,gBAAQ,kBAAkB;AAC1B,gBAAQ,UAAU;AAClB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AACf,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,cAAc;AACtB,gBAAQ,mBAAmB;AAC3B,gBAAQ,oBAAoB;AAC5B,gBAAQ,oBAAoB;AAC5B,gBAAQ,YAAY;AACpB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,qBAAqB;AAC7B,gBAAQ,SAAS;AAAA,MACf,GAAG;AAAA,IACL;AAAA;AAAA;;;ACpLA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAQA,QAAI,wBAAwB,OAAO;AACnC,QAAI,iBAAiB,OAAO,UAAU;AACtC,QAAI,mBAAmB,OAAO,UAAU;AAExC,aAAS,SAAS,KAAK;AACtB,UAAI,QAAQ,QAAQ,QAAQ,QAAW;AACtC,cAAM,IAAI,UAAU,uDAAuD;AAAA,MAC5E;AAEA,aAAO,OAAO,GAAG;AAAA,IAClB;AAEA,aAAS,kBAAkB;AAC1B,UAAI;AACH,YAAI,CAAC,OAAO,QAAQ;AACnB,iBAAO;AAAA,QACR;AAKA,YAAI,QAAQ,IAAI,OAAO,KAAK;AAC5B,cAAM,CAAC,IAAI;AACX,YAAI,OAAO,oBAAoB,KAAK,EAAE,CAAC,MAAM,KAAK;AACjD,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,gBAAM,MAAM,OAAO,aAAa,CAAC,CAAC,IAAI;AAAA,QACvC;AACA,YAAI,SAAS,OAAO,oBAAoB,KAAK,EAAE,IAAI,SAAU,GAAG;AAC/D,iBAAO,MAAM,CAAC;AAAA,QACf,CAAC;AACD,YAAI,OAAO,KAAK,EAAE,MAAM,cAAc;AACrC,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,+BAAuB,MAAM,EAAE,EAAE,QAAQ,SAAU,QAAQ;AAC1D,gBAAM,MAAM,IAAI;AAAA,QACjB,CAAC;AACD,YAAI,OAAO,KAAK,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAC/C,wBAAwB;AACzB,iBAAO;AAAA,QACR;AAEA,eAAO;AAAA,MACR,SAAS,KAAK;AAEb,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,UAAU,gBAAgB,IAAI,OAAO,SAAS,SAAU,QAAQ,QAAQ;AAC9E,UAAI;AACJ,UAAI,KAAK,SAAS,MAAM;AACxB,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,eAAO,OAAO,UAAU,CAAC,CAAC;AAE1B,iBAAS,OAAO,MAAM;AACrB,cAAI,eAAe,KAAK,MAAM,GAAG,GAAG;AACnC,eAAG,GAAG,IAAI,KAAK,GAAG;AAAA,UACnB;AAAA,QACD;AAEA,YAAI,uBAAuB;AAC1B,oBAAU,sBAAsB,IAAI;AACpC,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,gBAAI,iBAAiB,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG;AAC5C,iBAAG,QAAQ,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;AAAA,YACjC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzFA;AAAA;AAAA;AASA,QAAI,uBAAuB;AAE3B,WAAO,UAAU;AAAA;AAAA;;;ACXjB;AAAA;AAAA,WAAO,UAAU,SAAS,KAAK,KAAK,OAAO,UAAU,cAAc;AAAA;AAAA;;;ACAnE;AAAA;AAAA;AASA,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACrC,6BAAuB;AACvB,2BAAqB,CAAC;AACtB,YAAM;AAEV,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAO;AAAA,MACrB;AAAA,IACF;AAhBM;AACA;AACA;AA2BN,aAAS,eAAe,WAAW,QAAQ,UAAU,eAAe,UAAU;AAC5E,UAAI,MAAuC;AACzC,iBAAS,gBAAgB,WAAW;AAClC,cAAI,IAAI,WAAW,YAAY,GAAG;AAChC,gBAAI;AAIJ,gBAAI;AAGF,kBAAI,OAAO,UAAU,YAAY,MAAM,YAAY;AACjD,oBAAI,MAAM;AAAA,mBACP,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,+FACC,OAAO,UAAU,YAAY,IAAI;AAAA,gBAEpH;AACA,oBAAI,OAAO;AACX,sBAAM;AAAA,cACR;AACA,sBAAQ,UAAU,YAAY,EAAE,QAAQ,cAAc,eAAe,UAAU,MAAM,oBAAoB;AAAA,YAC3G,SAAS,IAAI;AACX,sBAAQ;AAAA,YACV;AACA,gBAAI,SAAS,EAAE,iBAAiB,QAAQ;AACtC;AAAA,iBACG,iBAAiB,iBAAiB,6BACnC,WAAW,OAAO,eAAe,6FAC6B,OAAO,QAAQ;AAAA,cAI/E;AAAA,YACF;AACA,gBAAI,iBAAiB,SAAS,EAAE,MAAM,WAAW,qBAAqB;AAGpE,iCAAmB,MAAM,OAAO,IAAI;AAEpC,kBAAI,QAAQ,WAAW,SAAS,IAAI;AAEpC;AAAA,gBACE,YAAY,WAAW,YAAY,MAAM,WAAW,SAAS,OAAO,QAAQ;AAAA,cAC9E;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,mBAAe,oBAAoB,WAAW;AAC5C,UAAI,MAAuC;AACzC,6BAAqB,CAAC;AAAA,MACxB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtGjB;AAAA;AAAA;AASA,QAAI,UAAU;AACd,QAAI,SAAS;AAEb,QAAI,uBAAuB;AAC3B,QAAI,MAAM;AACV,QAAI,iBAAiB;AAErB,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACzC,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AAAA,IACF;AAEA,aAAS,+BAA+B;AACtC,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,SAASC,iBAAgB,qBAAqB;AAE7D,UAAI,kBAAkB,OAAO,WAAW,cAAc,OAAO;AAC7D,UAAI,uBAAuB;AAgB3B,eAAS,cAAc,eAAe;AACpC,YAAI,aAAa,kBAAkB,mBAAmB,cAAc,eAAe,KAAK,cAAc,oBAAoB;AAC1H,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT;AAAA,MACF;AAiDA,UAAI,YAAY;AAIhB,UAAI,iBAAiB;AAAA,QACnB,OAAO,2BAA2B,OAAO;AAAA,QACzC,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,MAAM,2BAA2B,SAAS;AAAA,QAC1C,MAAM,2BAA2B,UAAU;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAE3C,KAAK,qBAAqB;AAAA,QAC1B,SAAS;AAAA,QACT,SAAS,yBAAyB;AAAA,QAClC,aAAa,6BAA6B;AAAA,QAC1C,YAAY;AAAA,QACZ,MAAM,kBAAkB;AAAA,QACxB,UAAU;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAOA,eAASC,IAAG,GAAG,GAAG;AAEhB,YAAI,MAAM,GAAG;AAGX,iBAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,QAClC,OAAO;AAEL,iBAAO,MAAM,KAAK,MAAM;AAAA,QAC1B;AAAA,MACF;AAUA,eAAS,cAAc,SAAS,MAAM;AACpC,aAAK,UAAU;AACf,aAAK,OAAO,QAAQ,OAAO,SAAS,WAAW,OAAM,CAAC;AACtD,aAAK,QAAQ;AAAA,MACf;AAEA,oBAAc,YAAY,MAAM;AAEhC,eAAS,2BAA2B,UAAU;AAC5C,YAAI,MAAuC;AACzC,cAAI,0BAA0B,CAAC;AAC/B,cAAI,6BAA6B;AAAA,QACnC;AACA,iBAAS,UAAU,YAAY,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAC7F,0BAAgB,iBAAiB;AACjC,yBAAe,gBAAgB;AAE/B,cAAI,WAAW,sBAAsB;AACnC,gBAAI,qBAAqB;AAEvB,kBAAI,MAAM,IAAI;AAAA,gBACZ;AAAA,cAGF;AACA,kBAAI,OAAO;AACX,oBAAM;AAAA,YACR,WAAoD,OAAO,YAAY,aAAa;AAElF,kBAAI,WAAW,gBAAgB,MAAM;AACrC,kBACE,CAAC,wBAAwB,QAAQ;AAAA,cAEjC,6BAA6B,GAC7B;AACA;AAAA,kBACE,6EACuB,eAAe,gBAAgB,gBAAgB;AAAA,gBAIxE;AACA,wCAAwB,QAAQ,IAAI;AACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,gBAAI,YAAY;AACd,kBAAI,MAAM,QAAQ,MAAM,MAAM;AAC5B,uBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,8BAA8B,SAAS,gBAAgB,8BAA8B;AAAA,cAC1J;AACA,qBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,iCAAiC,MAAM,gBAAgB,mCAAmC;AAAA,YAC/J;AACA,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,OAAO,UAAU,eAAe,UAAU,YAAY;AAAA,UACxE;AAAA,QACF;AAEA,YAAI,mBAAmB,UAAU,KAAK,MAAM,KAAK;AACjD,yBAAiB,aAAa,UAAU,KAAK,MAAM,IAAI;AAEvD,eAAO;AAAA,MACT;AAEA,eAAS,2BAA2B,cAAc;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAChF,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,cAAc;AAI7B,gBAAI,cAAc,eAAe,SAAS;AAE1C,mBAAO,IAAI;AAAA,cACT,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,cAAc,oBAAoB,gBAAgB,mBAAmB,MAAM,eAAe;AAAA,cAC9J,EAAC,aAA0B;AAAA,YAC7B;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB;AAC9B,eAAO,2BAA2B,4BAA4B;AAAA,MAChE;AAEA,eAAS,yBAAyB,aAAa;AAC7C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,iDAAiD;AAAA,UAC/I;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAI,QAAQ,YAAY,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,KAAK,oBAAoB;AACjH,gBAAI,iBAAiB,OAAO;AAC1B,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,2BAA2B;AAClC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAACD,gBAAe,SAAS,GAAG;AAC9B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,qCAAqC;AAAA,UACnL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,+BAA+B;AACtC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,QAAQ,mBAAmB,SAAS,GAAG;AAC1C,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,0CAA0C;AAAA,UACxL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,eAAe;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,EAAE,MAAM,QAAQ,aAAa,gBAAgB;AAC/C,gBAAI,oBAAoB,cAAc,QAAQ;AAC9C,gBAAI,kBAAkB,aAAa,MAAM,QAAQ,CAAC;AAClD,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,kBAAkB,oBAAoB,gBAAgB,mBAAmB,kBAAkB,oBAAoB,KAAK;AAAA,UACnN;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,gBAAgB;AAC7C,YAAI,CAAC,MAAM,QAAQ,cAAc,GAAG;AAClC,cAAI,MAAuC;AACzC,gBAAI,UAAU,SAAS,GAAG;AACxB;AAAA,gBACE,iEAAiE,UAAU,SAAS;AAAA,cAEtF;AAAA,YACF,OAAO;AACL,2BAAa,wDAAwD;AAAA,YACvE;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,mBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,gBAAIC,IAAG,WAAW,eAAe,CAAC,CAAC,GAAG;AACpC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,cAAI,eAAe,KAAK,UAAU,gBAAgB,SAAS,SAAS,KAAK,OAAO;AAC9E,gBAAI,OAAO,eAAe,KAAK;AAC/B,gBAAI,SAAS,UAAU;AACrB,qBAAO,OAAO,KAAK;AAAA,YACrB;AACA,mBAAO;AAAA,UACT,CAAC;AACD,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,iBAAiB,OAAO,SAAS,IAAI,QAAQ,kBAAkB,gBAAgB,wBAAwB,eAAe,IAAI;AAAA,QACnM;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,aAAa;AAC9C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,kDAAkD;AAAA,UAChJ;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,yBAAyB;AAAA,UACvK;AACA,mBAAS,OAAO,WAAW;AACzB,gBAAI,IAAI,WAAW,GAAG,GAAG;AACvB,kBAAI,QAAQ,YAAY,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC/G,kBAAI,iBAAiB,OAAO;AAC1B,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB,qBAAqB;AACnD,YAAI,CAAC,MAAM,QAAQ,mBAAmB,GAAG;AACvC,iBAAwC,aAAa,wEAAwE,IAAI;AACjI,iBAAO;AAAA,QACT;AAEA,iBAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,cAAI,UAAU,oBAAoB,CAAC;AACnC,cAAI,OAAO,YAAY,YAAY;AACjC;AAAA,cACE,gGACc,yBAAyB,OAAO,IAAI,eAAe,IAAI;AAAA,YACvE;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,gBAAgB,CAAC;AACrB,mBAASC,KAAI,GAAGA,KAAI,oBAAoB,QAAQA,MAAK;AACnD,gBAAIC,WAAU,oBAAoBD,EAAC;AACnC,gBAAI,gBAAgBC,SAAQ,OAAO,UAAU,eAAe,UAAU,cAAc,oBAAoB;AACxG,gBAAI,iBAAiB,MAAM;AACzB,qBAAO;AAAA,YACT;AACA,gBAAI,cAAc,QAAQ,IAAI,cAAc,MAAM,cAAc,GAAG;AACjE,4BAAc,KAAK,cAAc,KAAK,YAAY;AAAA,YACpD;AAAA,UACF;AACA,cAAI,uBAAwB,cAAc,SAAS,IAAK,6BAA6B,cAAc,KAAK,IAAI,IAAI,MAAK;AACrH,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,MAAM,uBAAuB,IAAI;AAAA,QACpJ;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,oBAAoB;AAC3B,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,CAAC,OAAO,MAAM,QAAQ,CAAC,GAAG;AAC5B,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,2BAA2B;AAAA,UAC9I;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,eAAe,UAAU,cAAc,KAAK,MAAM;AAC/E,eAAO,IAAI;AAAA,WACR,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,MAAM,MAAM,+FACX,OAAO;AAAA,QAC1F;AAAA,MACF;AAEA,eAAS,uBAAuB,YAAY;AAC1C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,OAAO,YAAY;AAC1B,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,OAAO,YAAY,YAAY;AACjC,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,6BAA6B,YAAY;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AAEA,cAAI,UAAU,OAAO,CAAC,GAAG,MAAM,QAAQ,GAAG,UAAU;AACpD,mBAAS,OAAO,SAAS;AACvB,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,IAAI,YAAY,GAAG,KAAK,OAAO,YAAY,YAAY;AACzD,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,CAAC,SAAS;AACZ,qBAAO,IAAI;AAAA,gBACT,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,oBAAoB,gBAAgB,qBACjF,KAAK,UAAU,MAAM,QAAQ,GAAG,MAAM,IAAI,IAC7D,mBAAmB,KAAK,UAAU,OAAO,KAAK,UAAU,GAAG,MAAM,IAAI;AAAA,cACvE;AAAA,YACF;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,OAAO,WAAW;AACzB,gBAAQ,OAAO,WAAW;AAAA,UACxB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,mBAAO,CAAC;AAAA,UACV,KAAK;AACH,gBAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,qBAAO,UAAU,MAAM,MAAM;AAAA,YAC/B;AACA,gBAAI,cAAc,QAAQH,gBAAe,SAAS,GAAG;AACnD,qBAAO;AAAA,YACT;AAEA,gBAAI,aAAa,cAAc,SAAS;AACxC,gBAAI,YAAY;AACd,kBAAI,WAAW,WAAW,KAAK,SAAS;AACxC,kBAAI;AACJ,kBAAI,eAAe,UAAU,SAAS;AACpC,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF,OAAO;AAEL,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,QAAQ,KAAK;AACjB,sBAAI,OAAO;AACT,wBAAI,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG;AACrB,6BAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,YACT;AAEA,mBAAO;AAAA,UACT;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAEA,eAAS,SAAS,UAAU,WAAW;AAErC,YAAI,aAAa,UAAU;AACzB,iBAAO;AAAA,QACT;AAGA,YAAI,CAAC,WAAW;AACd,iBAAO;AAAA,QACT;AAGA,YAAI,UAAU,eAAe,MAAM,UAAU;AAC3C,iBAAO;AAAA,QACT;AAGA,YAAI,OAAO,WAAW,cAAc,qBAAqB,QAAQ;AAC/D,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAGA,eAAS,YAAY,WAAW;AAC9B,YAAI,WAAW,OAAO;AACtB,YAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,iBAAO;AAAA,QACT;AACA,YAAI,qBAAqB,QAAQ;AAI/B,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,UAAU,SAAS,GAAG;AACjC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAIA,eAAS,eAAe,WAAW;AACjC,YAAI,OAAO,cAAc,eAAe,cAAc,MAAM;AAC1D,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,WAAW,YAAY,SAAS;AACpC,YAAI,aAAa,UAAU;AACzB,cAAI,qBAAqB,MAAM;AAC7B,mBAAO;AAAA,UACT,WAAW,qBAAqB,QAAQ;AACtC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAIA,eAAS,yBAAyB,OAAO;AACvC,YAAI,OAAO,eAAe,KAAK;AAC/B,gBAAQ,MAAM;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,QAAQ;AAAA,UACjB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,OAAO;AAAA,UAChB;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAGA,eAAS,aAAa,WAAW;AAC/B,YAAI,CAAC,UAAU,eAAe,CAAC,UAAU,YAAY,MAAM;AACzD,iBAAO;AAAA,QACT;AACA,eAAO,UAAU,YAAY;AAAA,MAC/B;AAEA,qBAAe,iBAAiB;AAChC,qBAAe,oBAAoB,eAAe;AAClD,qBAAe,YAAY;AAE3B,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjmBA;AAAA;AAOA,QAAI,MAAuC;AACrC,gBAAU;AAIV,4BAAsB;AAC1B,aAAO,UAAU,kCAAqC,QAAQ,WAAW,mBAAmB;AAAA,IAC9F,OAAO;AAGL,aAAO,UAAU,KAAsC;AAAA,IACzD;AAVM;AAIA;AAAA;AAAA;;;AEZN,aAAuB;;;AgBMvB,SAAS,SAAyC,MAAc;AAE9D,SAAO,CAAC,UAA+B,OAAO,UAAU;AAC1D;AAMO,IAAM,aAAa,SAAmB,UAAU;AAKhD,IAAM,SAAS,CAAC,UAAkC;AACvD,SAAO,UAAU;AACnB;AAKO,IAAM,UAAU,CAAC,UAAoC;AAC1D,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,MAAM;AAChE;AAKO,IAAM,WAAW,CAAC,UAAuC;AAC9D,SAAO,CAAC,YAAY,KAAK,KAAK,CAAC,OAAO,KAAK,MAAM,WAAW,KAAK,KAAK,OAAO,UAAU;AACzF;AAKO,IAAM,cAAc,SAAoB,WAAW;ACpC1D,SAAS,WAAW,MAAiB,OAAkB;AACrD,QAAM,EAAE,OAAO,IAAI;AAEnB,MAAI,WAAW,MAAM,QAAQ;AAC3B,WAAO;EACT;AAEA,WAAS,QAAQ,QAAQ,YAAY,KAAK;AACxC,QAAI,CAAC,MAAM,KAAK,KAAK,GAAG,MAAM,KAAK,CAAC,GAAG;AACrC,aAAO;IACT;EACF;AAEA,SAAO;AACT;AAKA,SAAS,iBAAiB,MAAuB,OAAwB;AACvE,MAAI,KAAK,eAAe,MAAM,YAAY;AACxC,WAAO;EACT;AAEA,QAAM,QAAQ,IAAI,SAAS,KAAK,MAAM;AACtC,QAAM,QAAQ,IAAI,SAAS,MAAM,MAAM;AAEvC,MAAI,QAAQ,KAAK;AAEjB,SAAO,SAAS;AACd,QAAI,MAAM,SAAS,KAAK,MAAM,MAAM,SAAS,KAAK,GAAG;AACnD,aAAO;IACT;EACF;AAEA,SAAO;AACT;AAKA,SAAS,SAAS,MAA6B,OAA8B;AAC3E,MAAI,KAAK,SAAS,MAAM,MAAM;AAC5B,WAAO;EACT;AAEA,aAAW,SAAS,KAAK,QAAQ,GAAG;AAClC,QAAI,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,GAAG;AACxB,aAAO;IACT;EACF;AAEA,aAAW,SAAS,KAAK,QAAQ,GAAG;AAClC,QAAI,CAAC,MAAM,MAAM,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG;AACzC,aAAO;IACT;EACF;AAEA,SAAO;AACT;AAKA,SAAS,SAAS,MAAoB,OAAqB;AACzD,MAAI,KAAK,SAAS,MAAM,MAAM;AAC5B,WAAO;EACT;AAEA,aAAW,SAAS,KAAK,QAAQ,GAAG;AAClC,QAAI,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,GAAG;AACxB,aAAO;IACT;EACF;AAEA,SAAO;AACT;AAKe,SAAR,MAAuB,MAAe,OAAgB;AAC3D,MAAI,SAAS,OAAO;AAClB,WAAO;EACT;AAEA,MAAI,QAAQ,SAAS,IAAI,KAAK,SAAS,SAAS,KAAK,GAAG;AACtD,QAAI,KAAK,gBAAgB,MAAM,aAAa;AAC1C,aAAO;IACT;AAEA,QAAI,MAAM,QAAQ,IAAI,KAAK,MAAM,QAAQ,KAAK,GAAG;AAC/C,aAAO,WAAW,MAAM,KAAK;IAC/B;AAEA,QAAI,gBAAgB,OAAO,iBAAiB,KAAK;AAC/C,aAAO,SAAS,MAAM,KAAK;IAC7B;AAEA,QAAI,gBAAgB,OAAO,iBAAiB,KAAK;AAC/C,aAAO,SAAS,MAAM,KAAK;IAC7B;AAEA,QAAI,YAAY,OAAO,IAAI,KAAK,YAAY,OAAO,KAAK,GAAG;AACzD,aAAO,iBAAiB,MAAM,KAAK;IACrC;AAEA,QAAI,QAAQ,IAAI,KAAK,QAAQ,KAAK,GAAG;AACnC,aAAO,KAAK,WAAW,MAAM,UAAU,KAAK,UAAU,MAAM;IAC9D;AAEA,QAAI,KAAK,YAAY,OAAO,UAAU,SAAS;AAC7C,aAAO,KAAK,QAAQ,MAAM,MAAM,QAAQ;IAC1C;AAEA,QAAI,KAAK,aAAa,OAAO,UAAU,UAAU;AAC/C,aAAO,KAAK,SAAS,MAAM,MAAM,SAAS;IAC5C;AAEA,UAAM,WAAW,OAAO,KAAK,IAAI;AACjC,UAAM,YAAY,OAAO,KAAK,KAAK;AAEnC,QAAI,SAAS,WAAW,UAAU,QAAQ;AACxC,aAAO;IACT;AAEA,aAAS,QAAQ,SAAS,QAAQ,YAAY,KAAK;AACjD,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,OAAO,SAAS,KAAK,CAAC,GAAG;AACjE,eAAO;MACT;IACF;AAEA,aAAS,QAAQ,SAAS,QAAQ,YAAY,KAAK;AACjD,YAAM,MAAM,SAAS,KAAK;AAE1B,UAAI,QAAQ,YAAY,KAAK,UAAU;AAKrC;MACF;AAEA,UAAI,CAAC,MAAM,KAAK,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG;AACjC,eAAO;MACT;IACF;AAEA,WAAO;EACT;AAEA,MAAI,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM,KAAK,GAAG;AAC7C,WAAO;EACT;AAEA,SAAO,SAAS;AAClB;;;AC9JO,IAAM,cAAc;EACzB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AAEO,IAAM,iBAAiB;EAC5B;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AAEO,SAAS,cAAc,OAAyC;AACrE,QAAM,iBAAiB,OAAO,UAAU,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE;AAExE,MAAI,iBAAiB,KAAK,cAAc,GAAG;AACzC,WAAO;EACT;AAEA,MAAI,aAAa,cAAc,GAAG;AAChC,WAAO;EACT;AAEA,SAAO;AACT;AAEO,SAAS,eAAkB,MAAc;AAC9C,SAAO,CAAC,UAA+B,cAAc,KAAK,MAAM;AAClE;AAEO,SAAS,aAAa,MAAoC;AAC/D,SAAO,YAAY,SAAS,IAAmB;AACjD;AAEO,SAASI,UAAyC,MAAc;AAErE,SAAO,CAAC,UAA+B,OAAO,UAAU;AAC1D;AAEO,SAAS,gBAAgB,MAAuC;AACrE,SAAO,eAAe,SAAS,IAAsB;AACvD;AC3DA,IAAM,0BAAoD;EACxD;EACA;EACA;EACA;EACA;AACF;AAEA,SAAS,GAAG,OAA0B;AACpC,MAAI,UAAU,MAAM;AAClB,WAAO;EACT;AAEA,UAAQ,OAAO,OAAO;IACpB,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;EACF;AAEA,MAAI,GAAG,MAAM,KAAK,GAAG;AACnB,WAAO;EACT;AAEA,MAAI,GAAG,cAAc,KAAK,GAAG;AAC3B,WAAO;EACT;AAEA,QAAM,UAAU,cAAc,KAAK;AAEnC,MAAI,SAAS;AACX,WAAO;EACT;AAGA,SAAO;AACT;AAEA,GAAG,QAAQ,MAAM;AAEjB,GAAG,UAAU,CAAC,QAAmB,cAAgD;AAC/E,MAAI,CAAC,GAAG,MAAM,MAAM,KAAK,CAAC,GAAG,SAAS,SAAS,GAAG;AAChD,WAAO;EACT;AAEA,SAAO,OAAO,MAAM,CAAA,MAAK,UAAU,CAAC,CAAC;AACvC;AAEA,GAAG,yBAAyB,CAAC,UAC3B,cAAc,KAAK,MAAM;AAE3B,GAAG,gBAAgB,eAAyB,eAAe;AAE3D,GAAG,SAASA,UAAiB,QAAQ;AAErC,GAAG,UAAU,CAAC,UAAqC;AACjD,SAAO,UAAU,QAAQ,UAAU;AACrC;AAEA,GAAG,OAAO,eAAqB,MAAM;AAErC,GAAG,UAAU,CAAC,UAA4B,CAAC,GAAG,UAAU,KAAK;AAE7D,GAAG,aAAa,CAAC,UAAyC;AACxD,SACE,GAAG,OAAO,KAAK,KACf,CAAC,GAAG,YAAY,KAAK,KACpB,MAAsB,aAAa,KACpC,GAAG,OAAQ,MAAsB,QAAQ,KACzC,wBAAwB,MAAM,CAAA,aAAY,YAAa,KAAqB;AAEhF;AAEA,GAAG,QAAQ,CAAC,UAA4B;AACtC,SACG,GAAG,OAAO,KAAK,KAAK,MAAM,WAAW,KACrC,GAAG,MAAM,KAAK,KAAK,MAAM,WAAW,KACpC,GAAG,OAAO,KAAK,KAAK,CAAC,GAAG,IAAI,KAAK,KAAK,CAAC,GAAG,IAAI,KAAK,KAAK,OAAO,KAAK,KAAK,EAAE,WAAW,KACtF,GAAG,IAAI,KAAK,KAAK,MAAM,SAAS,KAChC,GAAG,IAAI,KAAK,KAAK,MAAM,SAAS;AAErC;AAEA,GAAG,QAAQ,eAAsB,OAAO;AAExC,GAAG,WAAWA,UAAmB,UAAU;AAE3C,GAAG,YAAY,CAAC,UAAuC;AACrD,SACE,GAAG,SAAS,KAAK,KACjB,GAAG,SAAU,MAAoC,IAAI,KACrD,GAAG,SAAU,MAAoC,KAAK;AAE1D;AAEA,GAAG,oBAAoB,eAAkC,mBAAmB;AAE5E,GAAG,aAAa,CAAI,UAAmB,WAAoC;AACzE,MAAI,CAAC,YAAY,CAAE,QAAqB;AACtC,WAAO;EACT;AAEA,SAAO,OAAO,eAAe,QAAQ,MAAM,OAAO;AACpD;AAEA,GAAG,WAAW,CAAC,UAAuD;AACpE,SACE,CAAC,GAAG,gBAAgB,KAAK,KAAK,GAAG,SAAU,MAAoC,OAAO,QAAQ,CAAC;AAEnG;AAEA,GAAG,MAAM,eAAsC,KAAK;AAEpD,GAAG,MAAM,CAAC,UAA4B;AACpC,SAAO,OAAO,MAAM,KAAe;AACrC;AAEA,GAAG,OAAO,CAAC,UAAkC;AAC3C,SAAO,UAAU;AACnB;AAEA,GAAG,kBAAkB,CAAC,UAA8C;AAClE,SAAO,GAAG,KAAK,KAAK,KAAK,GAAG,UAAU,KAAK;AAC7C;AAEA,GAAG,SAAS,CAAC,UAAoC;AAC/C,SAAOA,UAAiB,QAAQ,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,KAAK;AAC3D;AAEA,GAAG,gBAAgB,CAAC,UAAoC;AACtD,SAAO,GAAG,OAAO,KAAK,KAAM,MAAiB,SAAS,KAAK,CAAC,OAAO,MAAM,OAAO,KAAK,CAAC;AACxF;AAEA,GAAG,SAAS,CAAC,UAAoC;AAC/C,SAAO,CAAC,GAAG,gBAAgB,KAAK,MAAM,GAAG,SAAS,KAAK,KAAK,OAAO,UAAU;AAC/E;AAEA,GAAG,QAAQ,CAAC,QAAmB,UAAwB;AACrD,MAAI,CAAC,GAAG,MAAM,MAAM,GAAG;AACrB,WAAO;EACT;AAGA,SAAO,OAAO,QAAQ,KAAK,IAAI;AACjC;AAEA,GAAG,gBAAgB,eAAyB,UAAU;AAEtD,GAAG,cAAc,CAAC,UAAyC;AACzD,MAAI,cAAc,KAAK,MAAM,UAAU;AACrC,WAAO;EACT;AAEA,QAAM,YAAY,OAAO,eAAe,KAAK;AAE7C,SAAO,cAAc,QAAQ,cAAc,OAAO,eAAe,CAAC,CAAC;AACrE;AAEA,GAAG,YAAY,CAAC,UACd,GAAG,KAAK,KAAK,KAAK,gBAAgB,OAAO,KAAK;AAEhD,GAAG,UAAU,eAAiC,SAAS;AAEvD,GAAG,aAAa,CACd,QACA,KACA,cACY;AACZ,MAAI,CAAC,GAAG,OAAO,MAAM,KAAK,CAAC,KAAK;AAC9B,WAAO;EACT;AAEA,QAAM,QAAQ,OAAO,GAAG;AAExB,MAAI,GAAG,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,KAAK;EACxB;AAEA,SAAO,GAAG,QAAQ,KAAK;AACzB;AAEA,GAAG,SAAS,eAAuB,QAAQ;AAE3C,GAAG,MAAM,eAAiC,KAAK;AAE/C,GAAG,SAASA,UAAiB,QAAQ;AAErC,GAAG,SAASA,UAAiB,QAAQ;AAErC,GAAG,YAAYA,UAAoB,WAAW;AAE9C,GAAG,UAAU,eAA8C,SAAS;AAEpE,GAAG,UAAU,eAAqC,SAAS;AAE3D,IAAO,cAAQ;;;AE3MR,SAAS,iBAAiB,YAA0B;AACzD,SAAO,WAAW,MAAM,CAAC,MAAe,YAAG,OAAO,CAAC,KAAK,YAAG,MAAM,CAAC,KAAK,YAAG,YAAY,CAAC,CAAC;AAC1F;AAEO,SAAS,cAAc,MAAY,OAAa,OAAc;AACnE,MAAI,CAAC,WAAW,MAAM,KAAK,GAAG;AAC5B,WAAO;EACT;AAEA,MAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,KAAK,GAAG;AACjC,WAAO,CAAC,KAAK,KAAK,SAAS,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,KAAK,CAAC;EAClE;AAGA,MAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,WAAW,GAAG;AACvC,WACE,CAAC,OAAO,QAAQ,IAAI,EAAE,KAAK,SAAS,KAAK,CAAC,KAAK,OAAO,QAAQ,KAAK,EAAE,KAAK,SAAS,KAAK,CAAC;EAE7F;AAEA,SAAO,UAAU;AACnB;AAEO,SAAS,eACd,cACA,MACA,SACS;AACT,QAAM,EAAE,QAAQ,KAAK,UAAU,KAAK,IAAI;AACxC,QAAM,OAAO,OAAO,cAAc,GAAG;AACrC,QAAM,QAAQ,OAAO,MAAM,GAAG;AAE9B,MAAI,UACF,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,MAAM,MAAM,SAAS,cAAc,OAAO,QAAQ,OAAO;AAElF,MAAI,CAAC,YAAG,UAAU,MAAM,GAAG;AACzB,cAAU,WAAW,UAAU;EACjC;AAEA,MAAI,CAAC,YAAG,UAAU,QAAQ,GAAG;AAC3B,cAAU,WAAW,SAAS;EAChC;AAEA,SAAO;AACT;AAEO,SAAS,cACd,cACA,MACA,SACA;AACA,QAAM,EAAE,KAAK,MAAM,MAAM,IAAI;AAE7B,QAAM,OAAO,OAAO,cAAc,GAAG;AACrC,QAAM,QAAQ,OAAO,MAAM,GAAG;AAC9B,QAAM,UAAU,SAAS,UAAU,OAAO;AAC1C,QAAM,YAAY,SAAS,UAAU,QAAQ;AAE7C,MAAI,CAAC,YAAG,gBAAgB,KAAK,GAAG;AAC9B,QAAI,YAAG,QAAQ,OAAO,GAAG;AAEvB,UAAI,YAAG,MAAM,OAAO,KAAK,YAAG,YAAY,OAAO,GAAG;AAChD,eAAO,cAAc,SAAS,WAAW,KAAK;MAChD;IACF,OAAO;AACL,aAAO,MAAM,WAAW,KAAK;IAC/B;AAEA,WAAO;EACT;AAEA,MAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,KAAK,GAAG;AACjC,WAAO,CAAC,UAAU,MAAM,iBAAiB,OAAO,CAAC;EACnD;AAEA,MAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,WAAW,GAAG;AACvC,WAAO,aAAa,OAAO,KAAK,OAAO,GAAG,OAAO,KAAK,SAAS,CAAC;EAClE;AAEA,SACE,CAAC,CAAC,MAAM,KAAK,EAAE,MAAM,CAAA,MAAK,YAAG,UAAU,CAAC,KAAK,YAAG,QAAQ,CAAC,CAAC,MACzD,SAAS,UACN,CAAC,YAAG,QAAQ,IAAI,KAAK,YAAG,QAAQ,KAAK,IACrC,YAAG,QAAQ,IAAI,KAAK,CAAC,YAAG,QAAQ,KAAK;AAE7C;AAEO,SAAS,aAAsB,cAAoB,MAAY,EAAE,IAAI,IAAgB,CAAC,GAAG;AAC9F,MAAI,OAAO,OAAO,cAAc,GAAG;AACnC,MAAI,QAAQ,OAAO,MAAM,GAAG;AAE5B,MAAI,CAAC,WAAW,MAAM,KAAK,GAAG;AAC5B,UAAM,IAAI,UAAU,6BAA6B;EACnD;AAEA,MAAI,CAAC,cAAc,MAAM,KAAK,GAAG;AAC/B,UAAM,IAAI,UAAU,0BAA0B;EAChD;AAEA,MAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,WAAW,GAAG;AACvC,WAAO,OAAO,KAAK,IAAI;AACvB,YAAQ,OAAO,KAAK,KAAK;EAC3B;AAEA,SAAO,CAAC,MAAM,KAAK;AACrB;AAEO,SAAS,SAAS,OAAc;AACrC,SAAO,CAAC,CAAC,KAAK,KAAK,MAAuB;AACxC,QAAI,YAAG,MAAM,KAAK,GAAG;AACnB,aACE,MAAM,OAAO,KAAK,KAClB,MAAM,KAAK,CAAA,MAAK,MAAM,GAAG,KAAK,KAAM,YAAG,MAAM,KAAK,KAAK,iBAAiB,KAAK,EAAE,CAAC,CAAE;IAEtF;AAGA,QAAI,YAAG,YAAY,KAAK,KAAK,MAAM,GAAG,GAAG;AACvC,aAAO,CAAC,CAAC,MAAM,GAAG,KAAK,MAAM,MAAM,GAAG,GAAG,KAAK;IAChD;AAEA,WAAO,MAAM,OAAO,KAAK;EAC3B;AACF;AAEO,SAAS,aAAa,MAAgB,OAA0B;AACrE,SAAO,MAAM,KAAK,CAAA,MAAK,CAAC,KAAK,SAAS,CAAC,CAAC;AAC1C;AAEO,SAAS,SAAS,OAAc;AACrC,SAAO,CAAC,UAAiB;AACvB,QAAI,YAAG,MAAM,KAAK,GAAG;AACnB,aAAO,MAAM,KAAK,CAAA,MAAK,MAAM,GAAG,KAAK,KAAM,YAAG,MAAM,KAAK,KAAK,iBAAiB,KAAK,EAAE,CAAC,CAAE;IAC3F;AAEA,WAAO,MAAM,OAAO,KAAK;EAC3B;AACF;AAEO,SAAS,mBAAsB,eAAwB,OAAmB;AAC/E,SAAO,YAAG,MAAM,aAAa,IACzB,cAAc,KAAK,CAAA,MAAK,MAAM,GAAG,KAAK,CAAC,IACvC,MAAM,eAAe,KAAK;AAChC;AAEO,SAAS,iBAAiB,MAAiB;AAChD,SAAO,CAAC,UAAmB,KAAK,KAAK,CAAA,MAAK,MAAM,GAAG,KAAK,CAAC;AAC3D;AAEO,SAAS,cAAc,YAAmC;AAC/D,SACE,WAAW,MAAM,YAAG,KAAK,KACzB,WAAW,MAAM,YAAG,MAAM,KAC1B,WAAW,MAAM,YAAG,WAAW,KAC/B,WAAW,MAAM,YAAG,MAAM;AAE9B;AAEO,SAAS,OAAgC,MAAS,UAAc;AAErE,MAAI,YAAG,YAAY,IAAI,KAAK,YAAG,MAAM,IAAI,GAAG;AAE1C,QAAI,YAAG,OAAO,QAAQ,GAAG;AACvB,YAAM,QAAoB,SAAS,MAAM,GAAG;AAE5C,aAAO,MAAM,OAAO,CAAC,KAAK,MAAM,OAAO,IAAI,CAAC,GAAG,IAAI;IACrD;AAGA,QAAI,YAAG,OAAO,QAAQ,GAAG;AACvB,aAAO,KAAK,QAAQ;IACtB;AAEA,WAAO;EACT;AAEA,SAAO;AACT;ADhLe,SAAR,YACL,cACA,MACgB;AAChB,MAAI,CAAC,cAAc,IAAI,EAAE,KAAKC,YAAG,eAAe,GAAG;AACjD,UAAM,IAAI,MAAM,6BAA6B;EAC/C;AAEA,MAAI,CAAC,CAAC,cAAc,IAAI,EAAE,MAAM,CAAA,MAAKA,YAAG,YAAY,CAAC,KAAKA,YAAG,MAAM,CAAC,CAAC,GAAG;AACtE,UAAM,IAAI,MAAM,iCAAiC;EACnD;AAEA,QAAM,QAAQ,CAAC,KAAS,UAA2B;AACjD,QAAI;AACF,aAAO,cAAiB,cAAc,MAAM,EAAE,KAAK,MAAM,SAAS,MAAM,CAAC;IAC3E,QAAQ;AAEN,aAAO;IACT;EACF;AAEA,QAAM,UAAU,CAAC,KAAkB,QAAgB,aAA8B;AAC/E,QAAI;AACF,YAAM,OAAO,OAAO,cAAc,GAAG;AACrC,YAAM,QAAQ,OAAO,MAAM,GAAG;AAC9B,YAAM,YAAYA,YAAG,QAAQ,MAAM;AACnC,YAAM,cAAcA,YAAG,QAAQ,QAAQ;AAEvC,UAAI,aAAa,aAAa;AAC5B,cAAM,iBAAiB,cACnB,mBAAmB,UAAU,IAAI,IACjC,CAAC,mBAAmB,QAAQ,IAAI;AACpC,cAAM,kBAAkB,mBAAmB,QAAQ,KAAK;AAExD,eAAO,kBAAkB;MAC3B;AAEA,UAAI,CAAC,MAAM,KAAK,EAAE,MAAMA,YAAG,KAAK,KAAK,CAAC,MAAM,KAAK,EAAE,MAAMA,YAAG,WAAW,GAAG;AACxE,eAAO,CAACC,MAAM,MAAM,KAAK;MAC3B;AAEA,aAAO,SAAS;IAClB,QAAQ;AAEN,aAAO;IACT;EACF;AAEA,QAAM,cAAc,CAAC,KAAiB,UAAiB,WAA4B;AACjF,QAAI,CAACD,YAAG,QAAQ,GAAG,GAAG;AACpB,aAAO;IACT;AAEA,QAAI;AACF,YAAM,OAAO,OAAO,cAAc,GAAG;AACrC,YAAM,QAAQ,OAAO,MAAM,GAAG;AAC9B,YAAM,YAAYA,YAAG,QAAQ,MAAM;AAEnC,aACE,mBAAmB,UAAU,IAAI,MAChC,YAAY,mBAAmB,QAAQ,KAAK,IAAI,CAAC;IAEtD,QAAQ;AAEN,aAAO;IACT;EACF;AAEA,QAAM,YAAY,CAAC,KAAQ,QAAgB,aAA8B;AACvE,QAAI,CAACA,YAAG,QAAQ,GAAG,GAAG;AACpB,aAAO;IACT;AAEA,QAAI;AACF,aAAO,eAAkB,cAAc,MAAM,EAAE,KAAK,QAAQ,UAAU,MAAM,YAAY,CAAC;IAC3F,QAAQ;AAEN,aAAO;IACT;EACF;AAEA,QAAM,UAAU,CAAC,QAAqB;AACpC,QAAI;AACF,YAAM,CAAC,MAAM,KAAK,IAAI,aAAa,cAAc,MAAM,EAAE,IAAI,CAAC;AAE9D,aAAO,CAAC,CAAC,KAAK,UAAU,CAAC,MAAM;IACjC,QAAQ;AAEN,aAAO;IACT;EACF;AAEA,QAAM,SAAS,CAAC,QAAqB;AACnC,QAAI;AACF,YAAM,CAAC,MAAM,KAAK,IAAI,aAAa,cAAc,MAAM,EAAE,IAAI,CAAC;AAE9D,aAAO,CAAC,KAAK,UAAU,CAAC,CAAC,MAAM;IACjC,QAAQ;AAEN,aAAO;IACT;EACF;AAEA,QAAM,YAAY,CAAC,KAAQ,QAAgB,aAA8B;AACvE,QAAI,CAACA,YAAG,QAAQ,GAAG,GAAG;AACpB,aAAO;IACT;AAEA,QAAI;AACF,aAAO,eAAkB,cAAc,MAAM,EAAE,KAAK,QAAQ,UAAU,MAAM,YAAY,CAAC;IAC3F,QAAQ;AAEN,aAAO;IACT;EACF;AAEA,QAAM,UAAU,CAAC,KAAS,UAA2B;AACnD,QAAI;AACF,aAAO,cAAiB,cAAc,MAAM,EAAE,KAAK,MAAM,WAAW,MAAM,CAAC;IAC7E,QAAQ;AAEN,aAAO;IACT;EACF;AAEA,SAAO,EAAE,OAAO,SAAS,aAAa,WAAW,SAAS,QAAQ,WAAW,QAAQ;AACvF;;;AnBpIA,oBAAmB;AACnB,0BAAyB;ACDzB,IAAAE,gBAA0E;AAC1E,IAAAC,oBAA6B;AAC7B,6BAAsB;ACDtB,IAAAC,oBAAsB;AEDtB,IAAAC,oBAAsB;AEAtB,aAAuB;ACAvB,IAAAC,SAAuB;ACAvB,IAAAC,UAAuB;AACvB,IAAAC,YAA0B;ACD1B,aAAuB;;;AYAvB,mBAAkB;AAClB,wBAAsB;;;ACDtB,IAAA,YAAe,OAAOC,WAAW,eAAe,OAAOC,aAAa,eAAe,OAAOC,cAAc;ACExG,IAAMC,kBAAmB,WAAU;MAC3BC,wBAAwB,CAAC,QAAQ,WAAW,SAApB;WACrBC,IAAI,GAAGA,IAAID,sBAAsBE,QAAQD,KAAK,GAAG;QACpDE,aAAaL,UAAUM,UAAUC,QAAQL,sBAAsBC,CAAtB,CAA5B,KAAyD,GAAG;aACpE;;;SAGJ;EAPgB;AAUlB,SAASK,kBAAkBC,IAAI;MAChCC,SAAS;SACN,WAAM;QACPA,QAAQ;;;aAGH;WACFC,QAAQC,QAAf,EAAyBC,KAAK,WAAM;eACzB;;KADX;;;AAOG,SAASC,aAAaL,IAAI;MAC3BM,YAAY;SACT,WAAM;QACP,CAACA,WAAW;kBACF;iBACD,WAAM;oBACH;;SAEXd,eAHH;;;;AAQN,IAAMe,qBAAqBX,aAAaP,OAAOa;AAY/C,IAAA,WAAgBK,qBACZR,oBACAM;AC9CW,SAASG,YAAWC,iBAAiB;MAC5CC,UAAU,CAAA;SAEdD,mBACAC,QAAQC,SAASC,KAAKH,eAAtB,MAA2C;;ACJhC,SAASI,yBAAyBC,SAASC,UAAU;MAC9DD,QAAQE,aAAa,GAAG;WACnB,CAAA;;MAGH3B,UAASyB,QAAQG,cAAcC;MAC/BC,MAAM9B,QAAO+B,iBAAiBN,SAAS,IAAjC;SACLC,WAAWI,IAAIJ,QAAJ,IAAgBI;;ACPrB,SAASE,cAAcP,SAAS;MACzCA,QAAQQ,aAAa,QAAQ;WACxBR;;SAEFA,QAAQS,cAAcT,QAAQU;;ACDxB,SAASC,gBAAgBX,SAAS;MAE3C,CAACA,SAAS;WACLxB,SAASoC;;UAGVZ,QAAQQ,UAAhB;SACO;SACA;aACIR,QAAQG,cAAcS;SAC1B;aACIZ,QAAQY;;8BAIwBb,yBAAyBC,OAAzB,GAAnCa,WAfuC,sBAevCA,UAAUC,YAf6B,sBAe7BA,WAAWC,YAfkB,sBAelBA;MACzB,wBAAwBC,KAAKH,WAAWE,YAAYD,SAApD,GAAgE;WAC3Dd;;SAGFW,gBAAgBJ,cAAcP,OAAd,CAAhB;;ACvBM,SAASiB,iBAAiBC,WAAW;SAC3CA,aAAaA,UAAUC,gBAAgBD,UAAUC,gBAAgBD;;ACN1E,IAAME,SAAStC,aAAa,CAAC,EAAEP,OAAO8C,wBAAwB7C,SAAS8C;AACvE,IAAMC,SAASzC,aAAa,UAAUkC,KAAKvC,UAAUM,SAAzB;AASb,SAASyC,KAAKC,SAAS;MAChCA,YAAY,IAAI;WACXL;;MAELK,YAAY,IAAI;WACXF;;SAEFH,UAAUG;;ACVJ,SAASG,gBAAgB1B,SAAS;MAC3C,CAACA,SAAS;WACLxB,SAASmD;;MAGZC,iBAAiBJ,KAAK,EAAL,IAAWhD,SAASoC,OAAO;MAG9CiB,eAAe7B,QAAQ6B,gBAAgB;SAEpCA,iBAAiBD,kBAAkB5B,QAAQ8B,oBAAoB;oBACpD9B,UAAUA,QAAQ8B,oBAAoBD;;MAGlDrB,WAAWqB,gBAAgBA,aAAarB;MAE1C,CAACA,YAAYA,aAAa,UAAUA,aAAa,QAAQ;WACpDR,UAAUA,QAAQG,cAAcwB,kBAAkBnD,SAASmD;;MAMlE,CAAC,MAAM,MAAM,OAAb,EAAsB3C,QAAQ6C,aAAarB,QAA3C,MAAyD,MACzDT,yBAAyB8B,cAAc,UAAvC,MAAuD,UACvD;WACOH,gBAAgBG,YAAhB;;SAGFA;;ACpCM,SAASE,kBAAkB/B,SAAS;MACzCQ,WAAaR,QAAbQ;MACJA,aAAa,QAAQ;WAChB;;SAGPA,aAAa,UAAUkB,gBAAgB1B,QAAQgC,iBAAxB,MAA+ChC;;ACD3D,SAASiC,QAAQC,MAAM;MAChCA,KAAKzB,eAAe,MAAM;WACrBwB,QAAQC,KAAKzB,UAAb;;SAGFyB;;ACAM,SAASC,uBAAuBC,UAAUC,UAAU;MAE7D,CAACD,YAAY,CAACA,SAASlC,YAAY,CAACmC,YAAY,CAACA,SAASnC,UAAU;WAC/D1B,SAASmD;;MAIZW,QACJF,SAASG,wBAAwBF,QAAjC,IACAG,KAAKC;MACDC,QAAQJ,QAAQF,WAAWC;MAC3BM,MAAML,QAAQD,WAAWD;MAGzBQ,QAAQpE,SAASqE,YAAT;QACRC,SAASJ,OAAO,CAAtB;QACMK,OAAOJ,KAAK,CAAlB;MACQK,0BAA4BJ,MAA5BI;MAILZ,aAAaY,2BACZX,aAAaW,2BACfN,MAAMO,SAASN,GAAf,GACA;QACIZ,kBAAkBiB,uBAAlB,GAA4C;aACvCA;;WAGFtB,gBAAgBsB,uBAAhB;;MAIHE,eAAejB,QAAQG,QAAR;MACjBc,aAAaxC,MAAM;WACdyB,uBAAuBe,aAAaxC,MAAM2B,QAA1C;SACF;WACEF,uBAAuBC,UAAUH,QAAQI,QAAR,EAAkB3B,IAAnD;;;ACzCI,SAASyC,UAAUnD,SAAuB;MAAdoD,OAAc,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAP;MAC1CC,YAAYD,SAAS,QAAQ,cAAc;MAC3C5C,WAAWR,QAAQQ;MAErBA,aAAa,UAAUA,aAAa,QAAQ;QACxC8C,OAAOtD,QAAQG,cAAcwB;QAC7B4B,mBAAmBvD,QAAQG,cAAcoD,oBAAoBD;WAC5DC,iBAAiBF,SAAjB;;SAGFrD,QAAQqD,SAAR;;ACPM,SAASG,cAAcC,MAAMzD,SAA2B;MAAlB0D,WAAkB,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAP;MACxDC,YAAYR,UAAUnD,SAAS,KAAnB;MACZ4D,aAAaT,UAAUnD,SAAS,MAAnB;MACb6D,WAAWH,WAAW,KAAK;OAC5BI,OAAOH,YAAYE;OACnBE,UAAUJ,YAAYE;OACtBG,QAAQJ,aAAaC;OACrBI,SAASL,aAAaC;SACpBJ;;ACTM,SAASS,eAAeC,QAAQC,MAAM;MAC7CC,QAAQD,SAAS,MAAM,SAAS;MAChCE,QAAQD,UAAU,SAAS,UAAU;SAGzCE,WAAWJ,OAAAA,WAAgBE,QAAhB,OAAA,CAAX,IACAE,WAAWJ,OAAAA,WAAgBG,QAAhB,OAAA,CAAX;;ACdJ,SAASE,QAAQJ,MAAMxD,MAAM0C,MAAMmB,eAAe;SACzCC,KAAKC,IACV/D,KAAAA,WAAcwD,IAAd,GACAxD,KAAAA,WAAcwD,IAAd,GACAd,KAAAA,WAAcc,IAAd,GACAd,KAAAA,WAAcc,IAAd,GACAd,KAAAA,WAAcc,IAAd,GACA5C,KAAK,EAAL,IACKoD,SAAStB,KAAAA,WAAcc,IAAd,CAAT,IACHQ,SAASH,cAAAA,YAAuBL,SAAS,WAAW,QAAQ,OAAnD,CAAT,IACAQ,SAASH,cAAAA,YAAuBL,SAAS,WAAW,WAAW,QAAtD,CAAT,IACA,CAVG;;AAcM,SAASS,eAAerG,WAAU;MACzCoC,OAAOpC,UAASoC;MAChB0C,OAAO9E,UAASmD;MAChB8C,gBAAgBjD,KAAK,EAAL,KAAYlB,iBAAiBgD,IAAjB;SAE3B;YACGkB,QAAQ,UAAU5D,MAAM0C,MAAMmB,aAA9B;WACDD,QAAQ,SAAS5D,MAAM0C,MAAMmB,aAA7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjBI,SAASK,cAAcC,SAAS;sBAExCA,SADL;WAESA,QAAQf,OAAOe,QAAQC;YACtBD,QAAQjB,MAAMiB,QAAQE;;;ACGnB,SAASC,sBAAsBlF,SAAS;MACjDyD,OAAO,CAAA;MAKP;QACEjC,KAAK,EAAL,GAAU;aACLxB,QAAQkF,sBAAR;UACDvB,YAAYR,UAAUnD,SAAS,KAAnB;UACZ4D,aAAaT,UAAUnD,SAAS,MAAnB;WACd8D,OAAOH;WACPK,QAAQJ;WACRG,UAAUJ;WACVM,SAASL;WAEX;aACI5D,QAAQkF,sBAAR;;WAGLC,GAAE;EAAA;MAEFC,SAAS;UACP3B,KAAKO;SACNP,KAAKK;WACHL,KAAKQ,QAAQR,KAAKO;YACjBP,KAAKM,SAASN,KAAKK;;MAIvBuB,QAAQrF,QAAQQ,aAAa,SAASqE,eAAe7E,QAAQG,aAAvB,IAAwC,CAAA;MAC9E6E,QACJK,MAAML,SAAShF,QAAQsF,eAAeF,OAAOJ;MACzCC,SACJI,MAAMJ,UAAUjF,QAAQuF,gBAAgBH,OAAOH;MAE7CO,iBAAiBxF,QAAQyF,cAAcT;MACvCU,gBAAgB1F,QAAQ2F,eAAeV;MAIvCO,kBAAkBE,eAAe;QAC7BvB,SAASpE,yBAAyBC,OAAzB;sBACGkE,eAAeC,QAAQ,GAAvB;qBACDD,eAAeC,QAAQ,GAAvB;WAEVa,SAASQ;WACTP,UAAUS;;SAGZZ,cAAcM,MAAd;;ACzDM,SAASQ,qCAAqCC,UAAUC,QAA+B;MAAvBC,gBAAuB,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAP;MACvFxE,UAASyE,KAAQ,EAAR;MACTC,SAASH,OAAOtF,aAAa;MAC7B0F,eAAehB,sBAAsBW,QAAtB;MACfM,aAAajB,sBAAsBY,MAAtB;MACbM,gBAAezF,gBAAgBkF,QAAhB;MAEf1B,SAASpE,yBAAyB+F,MAAzB;MACTO,iBAAiB9B,WAAWJ,OAAOkC,cAAlB;MACjBC,kBAAkB/B,WAAWJ,OAAOmC,eAAlB;MAGrBP,iBAAiBE,QAAQ;eACfnC,MAAMY,KAAKC,IAAIwB,WAAWrC,KAAK,CAAzB;eACNE,OAAOU,KAAKC,IAAIwB,WAAWnC,MAAM,CAA1B;;MAEhBe,UAAUD,cAAc;SACrBoB,aAAapC,MAAMqC,WAAWrC,MAAMuC;UACnCH,aAAalC,OAAOmC,WAAWnC,OAAOsC;WACrCJ,aAAalB;YACZkB,aAAajB;GAJT;UAMNsB,YAAY;UACZC,aAAa;MAMjB,CAACjF,WAAU0E,QAAQ;QACfM,YAAYhC,WAAWJ,OAAOoC,SAAlB;QACZC,aAAajC,WAAWJ,OAAOqC,UAAlB;YAEX1C,OAAOuC,iBAAiBE;YACxBxC,UAAUsC,iBAAiBE;YAC3BvC,QAAQsC,kBAAkBE;YAC1BvC,SAASqC,kBAAkBE;YAG3BD,YAAYA;YACZC,aAAaA;;MAIrBjF,WAAU,CAACwE,gBACPD,OAAO7C,SAASmD,aAAhB,IACAN,WAAWM,iBAAgBA,cAAa5F,aAAa,QACzD;cACUgD,cAAcuB,SAASe,MAAvB;;SAGLf;;ACtDM,SAAS0B,8CAA8CzG,SAAgC;MAAvB0G,gBAAuB,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAP;MACvFpD,OAAOtD,QAAQG,cAAcwB;MAC7BgF,iBAAiBf,qCAAqC5F,SAASsD,IAA9C;MACjB0B,QAAQN,KAAKC,IAAIrB,KAAKgC,aAAa/G,OAAOqI,cAAc,CAAhD;MACR3B,SAASP,KAAKC,IAAIrB,KAAKiC,cAAchH,OAAOsI,eAAe,CAAlD;MAETlD,YAAY,CAAC+C,gBAAgBvD,UAAUG,IAAV,IAAkB;MAC/CM,aAAa,CAAC8C,gBAAgBvD,UAAUG,MAAM,MAAhB,IAA0B;MAExDwD,UAAS;SACRnD,YAAYgD,eAAe7C,MAAM6C,eAAeJ;UAC/C3C,aAAa+C,eAAe3C,OAAO2C,eAAeH;;;;SAKnD1B,cAAcgC,OAAd;;ACTM,SAASC,QAAQ/G,SAAS;MACjCQ,WAAWR,QAAQQ;MACrBA,aAAa,UAAUA,aAAa,QAAQ;WACvC;;MAELT,yBAAyBC,SAAS,UAAlC,MAAkD,SAAS;WACtD;;MAEHS,aAAaF,cAAcP,OAAd;MACf,CAACS,YAAY;WACR;;SAEFsG,QAAQtG,UAAR;;ACbM,SAASuG,6BAA6BhH,SAAS;MAEvD,CAACA,WAAW,CAACA,QAAQiH,iBAAiBzF,KAAAA,GAAQ;WAC1ChD,SAASmD;;MAEduF,KAAKlH,QAAQiH;SACVC,MAAMnH,yBAAyBmH,IAAI,WAA7B,MAA8C,QAAQ;SAC5DA,GAAGD;;SAEHC,MAAM1I,SAASmD;;ACET,SAASwF,cACtBC,QACAlG,WACAmG,SACAC,mBAEA;MADAvB,gBACA,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IADgB;MAIZwB,aAAa,EAAEzD,KAAK,GAAGE,MAAM,EAAhB;MACXnC,eAAekE,gBAAgBiB,6BAA6BI,MAA7B,IAAuCjF,uBAAuBiF,QAAQnG,iBAAiBC,SAAjB,CAA/B;MAGxEoG,sBAAsB,YAAa;iBACxBb,8CAA8C5E,cAAckE,aAA5D;SAGV;QAECyB,iBAAAA;QACAF,sBAAsB,gBAAgB;uBACvB3G,gBAAgBJ,cAAcW,SAAd,CAAhB;UACbsG,eAAehH,aAAa,QAAQ;yBACrB4G,OAAOjH,cAAcwB;;eAE/B2F,sBAAsB,UAAU;uBACxBF,OAAOjH,cAAcwB;WACjC;uBACY2F;;QAGbvC,UAAUa,qCACd4B,gBACA3F,cACAkE,aAHc;QAOZyB,eAAehH,aAAa,UAAU,CAACuG,QAAQlF,YAAR,GAAuB;4BACtCgD,eAAeuC,OAAOjH,aAAtB,GAAlB8E,SADwD,gBACxDA,QAAQD,QADgD,gBAChDA;iBACLlB,OAAOiB,QAAQjB,MAAMiB,QAAQwB;iBAC7BxC,SAASkB,SAASF,QAAQjB;iBAC1BE,QAAQe,QAAQf,OAAOe,QAAQyB;iBAC/BvC,QAAQe,QAAQD,QAAQf;WAC9B;mBAEQe;;;YAKPsC,WAAW;MACfI,kBAAkB,OAAOJ,YAAY;aAChCrD,QAAQyD,kBAAkBJ,UAAUA,QAAQrD,QAAQ;aACpDF,OAAO2D,kBAAkBJ,UAAUA,QAAQvD,OAAO;aAClDG,SAASwD,kBAAkBJ,UAAUA,QAAQpD,SAAS;aACtDF,UAAU0D,kBAAkBJ,UAAUA,QAAQtD,UAAU;SAE5DwD;;AC7ET,SAASG,QAAT,MAAoC;MAAjB1C,QAAiB,KAAjBA,OAAOC,SAAU,KAAVA;SACjBD,QAAQC;;AAYF,SAAS0C,qBACtBC,WACAC,SACAT,QACAlG,WACAoG,mBAEA;MADAD,UACA,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IADU;MAENO,UAAU5I,QAAQ,MAAlB,MAA8B,IAAI;WAC7B4I;;MAGHL,aAAaJ,cACjBC,QACAlG,WACAmG,SACAC,iBAJiB;MAObQ,QAAQ;SACP;aACIP,WAAWvC;cACV6C,QAAQ/D,MAAMyD,WAAWzD;;WAE5B;aACEyD,WAAWtD,QAAQ4D,QAAQ5D;cAC1BsD,WAAWtC;;YAEb;aACCsC,WAAWvC;cACVuC,WAAWxD,SAAS8D,QAAQ9D;;UAEhC;aACG8D,QAAQ7D,OAAOuD,WAAWvD;cACzBuD,WAAWtC;;;MAIjB8C,cAAcC,OAAOC,KAAKH,KAAZ,EACjBI,IAAI,SAAA,KAAA;;;OAEAJ,MAAMK,GAAN,GAFA;YAGGT,QAAQI,MAAMK,GAAN,CAAR;;GAJU,EAMjBC,KAAK,SAACC,GAAGC,GAAJ;WAAUA,EAAEC,OAAOF,EAAEE;GANT;MAQdC,gBAAgBT,YAAYU,OAChC,SAAA,OAAA;QAAGzD,QAAH,MAAGA,OAAOC,SAAV,MAAUA;WACRD,SAASoC,OAAO9B,eAAeL,UAAUmC,OAAO7B;GAF9B;MAKhBmD,oBAAoBF,cAAc3J,SAAS,IAC7C2J,cAAc,CAAd,EAAiBL,MACjBJ,YAAY,CAAZ,EAAeI;MAEbQ,YAAYf,UAAUgB,MAAM,GAAhB,EAAqB,CAArB;SAEXF,qBAAqBC,YAAAA,MAAgBA,YAAc;;ACzD7C,SAASE,oBAAoBC,OAAO1B,QAAQlG,WAAiC;MAAtB6E,gBAAsB,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAN;MAC9EgD,qBAAqBhD,gBAAgBiB,6BAA6BI,MAA7B,IAAuCjF,uBAAuBiF,QAAQnG,iBAAiBC,SAAjB,CAA/B;SAC3E0E,qCAAqC1E,WAAW6H,oBAAoBhD,aAApE;;ACVM,SAASiD,cAAchJ,SAAS;MACvCzB,UAASyB,QAAQG,cAAcC;MAC/B+D,SAAS5F,QAAO+B,iBAAiBN,OAAxB;MACTiJ,IAAI1E,WAAWJ,OAAOoC,aAAa,CAA/B,IAAoChC,WAAWJ,OAAO+E,gBAAgB,CAAlC;MACxCC,IAAI5E,WAAWJ,OAAOqC,cAAc,CAAhC,IAAqCjC,WAAWJ,OAAOiF,eAAe,CAAjC;MACzChE,SAAS;WACNpF,QAAQyF,cAAc0D;YACrBnJ,QAAQ2F,eAAesD;;SAE1B7D;;ACTM,SAASiE,qBAAqBzB,WAAW;MAChD0B,OAAO,EAAEtF,MAAM,SAASC,OAAO,QAAQF,QAAQ,OAAOD,KAAK,SAApD;SACN8D,UAAU2B,QAAQ,0BAA0B,SAAA,SAAA;WAAWD,KAAKE,OAAL;GAAvD;;ACIM,SAASC,iBAAiBrC,QAAQsC,kBAAkB9B,WAAW;cAChEA,UAAUgB,MAAM,GAAhB,EAAqB,CAArB;MAGNe,aAAaX,cAAc5B,MAAd;MAGbwC,gBAAgB;WACbD,WAAW3E;YACV2E,WAAW1E;;MAIf4E,UAAU,CAAC,SAAS,MAAV,EAAkB7K,QAAQ4I,SAA1B,MAAyC;MACnDkC,WAAWD,UAAU,QAAQ;MAC7BE,gBAAgBF,UAAU,SAAS;MACnCG,cAAcH,UAAU,WAAW;MACnCI,uBAAuB,CAACJ,UAAU,WAAW;gBAErCC,QAAd,IACEJ,iBAAiBI,QAAjB,IACAJ,iBAAiBM,WAAjB,IAAgC,IAChCL,WAAWK,WAAX,IAA0B;MACxBpC,cAAcmC,eAAe;kBACjBA,aAAd,IACEL,iBAAiBK,aAAjB,IAAkCJ,WAAWM,oBAAX;SAC/B;kBACSF,aAAd,IACEL,iBAAiBL,qBAAqBU,aAArB,CAAjB;;SAGGH;;ACnCM,SAASM,KAAKC,KAAKC,OAAO;MAEnCC,MAAMC,UAAUJ,MAAM;WACjBC,IAAID,KAAKE,KAAT;;SAIFD,IAAI1B,OAAO2B,KAAX,EAAkB,CAAlB;;ACLM,SAASG,UAAUJ,KAAKK,MAAMC,OAAO;MAE9CJ,MAAMC,UAAUC,WAAW;WACtBJ,IAAII,UAAU,SAAA,KAAA;aAAOG,IAAIF,IAAJ,MAAcC;KAAnC;;MAIHE,QAAQT,KAAKC,KAAK,SAAA,KAAA;WAAOS,IAAIJ,IAAJ,MAAcC;GAA/B;SACPN,IAAInL,QAAQ2L,KAAZ;;ACLM,SAASE,aAAaC,YAAWC,MAAMC,MAAM;MACpDC,iBAAiBD,SAASE,SAC5BJ,aACAA,WAAUK,MAAM,GAAGZ,UAAUO,YAAW,QAAQE,IAA7B,CAAnB;iBAEWI,QAAQ,SAAA,UAAY;QAC7BvH,SAAS,UAAT,GAAsB;cAChBwH,KAAK,uDAAb;;QAEInM,KAAK2E,SAAS,UAAT,KAAwBA,SAAS3E;QACxC2E,SAASyH,WAAW5L,YAAWR,EAAX,GAAgB;WAIjC6F,QAAQqC,SAAStC,cAAciG,KAAKhG,QAAQqC,MAA3B;WACjBrC,QAAQ7D,YAAY4D,cAAciG,KAAKhG,QAAQ7D,SAA3B;aAElBhC,GAAG6L,MAAMlH,QAAT;;GAZX;SAgBOkH;;ACvBM,SAASQ,SAAS;MAE3B,KAAKzC,MAAM0C,aAAa;;;MAIxBT,OAAO;cACC;YACF,CAAA;iBACK,CAAA;gBACD,CAAA;aACH;aACA,CAAA;;OAINhG,QAAQ7D,YAAY2H,oBACvB,KAAKC,OACL,KAAK1B,QACL,KAAKlG,WACL,KAAKuK,QAAQC,aAJU;OAUpB9D,YAAYD,qBACf,KAAK8D,QAAQ7D,WACbmD,KAAKhG,QAAQ7D,WACb,KAAKkG,QACL,KAAKlG,WACL,KAAKuK,QAAQX,UAAUa,KAAKrE,mBAC5B,KAAKmE,QAAQX,UAAUa,KAAKtE,OANb;OAUZuE,oBAAoBb,KAAKnD;OAEzB8D,gBAAgB,KAAKD,QAAQC;OAG7B3G,QAAQqC,SAASqC,iBACpB,KAAKrC,QACL2D,KAAKhG,QAAQ7D,WACb6J,KAAKnD,SAHe;OAMjB7C,QAAQqC,OAAOyE,WAAW,KAAKJ,QAAQC,gBACxC,UACA;SAGGb,aAAa,KAAKC,WAAWC,IAA7B;MAIH,CAAC,KAAKjC,MAAMgD,WAAW;SACpBhD,MAAMgD,YAAY;SAClBL,QAAQM,SAAShB,IAAtB;SACK;SACAU,QAAQO,SAASjB,IAAtB;;;AClEW,SAASkB,kBAAkBnB,YAAWoB,cAAc;SAC1DpB,WAAUqB,KACf,SAAA,MAAA;QAAGC,OAAH,KAAGA,MAAMd,UAAT,KAASA;WAAcA,WAAWc,SAASF;GADtC;;ACAM,SAASG,yBAAyBpM,UAAU;MACnDqM,WAAW,CAAC,OAAO,MAAM,UAAU,OAAO,GAA/B;MACXC,YAAYtM,SAASuM,OAAO,CAAhB,EAAmBC,YAAnB,IAAmCxM,SAASkL,MAAM,CAAf;WAE5CvM,IAAI,GAAGA,IAAI0N,SAASzN,QAAQD,KAAK;QAClC8N,SAASJ,SAAS1N,CAAT;QACT+N,UAAUD,SAAAA,KAAYA,SAASH,YAActM;QAC/C,OAAOzB,SAASoC,KAAKgM,MAAMD,OAApB,MAAiC,aAAa;aAChDA;;;SAGJ;;ACVM,SAASE,UAAU;OAC3B/D,MAAM0C,cAAc;MAGrBS,kBAAkB,KAAKnB,WAAW,YAAlC,GAAiD;SAC9C1D,OAAO0F,gBAAgB,aAA5B;SACK1F,OAAOwF,MAAMf,WAAW;SACxBzE,OAAOwF,MAAM9I,MAAM;SACnBsD,OAAOwF,MAAM5I,OAAO;SACpBoD,OAAOwF,MAAM3I,QAAQ;SACrBmD,OAAOwF,MAAM7I,SAAS;SACtBqD,OAAOwF,MAAMG,aAAa;SAC1B3F,OAAOwF,MAAMP,yBAAyB,WAAzB,CAAlB,IAA2D;;OAGxDW,sBAAL;MAII,KAAKvB,QAAQwB,iBAAiB;SAC3B7F,OAAO3G,WAAWyM,YAAY,KAAK9F,MAAxC;;SAEK;;ACzBM,SAAS+F,UAAUnN,SAAS;MACnCG,gBAAgBH,QAAQG;SACvBA,gBAAgBA,cAAcC,cAAc7B;;ACJrD,SAAS6O,sBAAsBhH,eAAciH,OAAOC,UAAUC,eAAe;MACrEC,SAASpH,cAAa5F,aAAa;MACnCiN,SAASD,SAASpH,cAAajG,cAAcC,cAAcgG;SAC1DsH,iBAAiBL,OAAOC,UAAU,EAAEK,SAAS,KAAX,CAAzC;MAEI,CAACH,QAAQ;0BAET7M,gBAAgB8M,OAAOhN,UAAvB,GACA4M,OACAC,UACAC,aAJF;;gBAOYK,KAAKH,MAAnB;;AASa,SAASI,oBACtB3M,WACAuK,SACA3C,OACAgF,aACA;QAEMA,cAAcA;YACV5M,SAAV,EAAqBwM,iBAAiB,UAAU5E,MAAMgF,aAAa,EAAEH,SAAS,KAAX,CAAnE;MAGMI,gBAAgBpN,gBAAgBO,SAAhB;wBAEpB6M,eACA,UACAjF,MAAMgF,aACNhF,MAAMyE,aAJR;QAMMQ,gBAAgBA;QAChBC,gBAAgB;SAEflF;;ACtCM,SAASmF,uBAAuB;MACzC,CAAC,KAAKnF,MAAMkF,eAAe;SACxBlF,QAAQ+E,oBACX,KAAK3M,WACL,KAAKuK,SACL,KAAK3C,OACL,KAAKoF,cAJM;;;ACFF,SAASC,qBAAqBjN,WAAW4H,OAAO;YAEnD5H,SAAV,EAAqBkN,oBAAoB,UAAUtF,MAAMgF,WAAzD;QAGMP,cAAcnC,QAAQ,SAAA,QAAU;WAC7BgD,oBAAoB,UAAUtF,MAAMgF,WAA3C;GADF;QAKMA,cAAc;QACdP,gBAAgB,CAAA;QAChBQ,gBAAgB;QAChBC,gBAAgB;SACflF;;ACbM,SAASkE,wBAAwB;MAC1C,KAAKlE,MAAMkF,eAAe;yBACP,KAAKE,cAA1B;SACKpF,QAAQqF,qBAAqB,KAAKjN,WAAW,KAAK4H,KAA1C;;;ACLF,SAASuF,UAAUC,GAAG;SAC5BA,MAAM,MAAM,CAACC,MAAMhK,WAAW+J,CAAX,CAAN,KAAwBE,SAASF,CAAT;;ACE/B,SAASG,UAAUzO,SAASmE,QAAQ;SAC1C8D,KAAK9D,MAAZ,EAAoBiH,QAAQ,SAAA,MAAQ;QAC9BsD,OAAO;QAGT,CAAC,SAAS,UAAU,OAAO,SAAS,UAAU,MAA9C,EAAsD1P,QAAQwL,IAA9D,MACE,MACF6D,UAAUlK,OAAOqG,IAAP,CAAV,GACA;aACO;;YAEDoC,MAAMpC,IAAd,IAAsBrG,OAAOqG,IAAP,IAAekE;GAVvC;;ACHa,SAASC,cAAc3O,SAAS4O,YAAY;SAClD3G,KAAK2G,UAAZ,EAAwBxD,QAAQ,SAASZ,MAAM;QACvCC,QAAQmE,WAAWpE,IAAX;QACVC,UAAU,OAAO;cACXoE,aAAarE,MAAMoE,WAAWpE,IAAX,CAA3B;WACK;cACGsC,gBAAgBtC,IAAxB;;GALJ;;ACKa,SAASsE,WAAW/D,MAAM;YAK7BA,KAAKgE,SAAS3H,QAAQ2D,KAAK5G,MAArC;gBAIc4G,KAAKgE,SAAS3H,QAAQ2D,KAAK6D,UAAzC;MAGI7D,KAAKiE,gBAAgBhH,OAAOC,KAAK8C,KAAKkE,WAAjB,EAA8BpQ,QAAQ;cACnDkM,KAAKiE,cAAcjE,KAAKkE,WAAlC;;SAGKlE;;AAaF,SAASmE,iBACdhO,WACAkG,QACAqE,SACA0D,iBACArG,OACA;MAEMY,mBAAmBb,oBAAoBC,OAAO1B,QAAQlG,WAAWuK,QAAQC,aAAtD;MAKnB9D,YAAYD,qBAChB8D,QAAQ7D,WACR8B,kBACAtC,QACAlG,WACAuK,QAAQX,UAAUa,KAAKrE,mBACvBmE,QAAQX,UAAUa,KAAKtE,OANP;SASXwH,aAAa,eAAejH,SAAnC;YAIUR,QAAQ,EAAEyE,UAAUJ,QAAQC,gBAAgB,UAAU,WAA9C,CAAlB;SAEOD;;ACpDM,SAAS2D,kBAAkBrE,MAAMsE,aAAa;sBAC7BtE,KAAKhG,SAA3BqC,SADmD,cACnDA,QAAQlG,YAD2C,cAC3CA;MACRoO,QAAiB5K,KAAjB4K,OAAOC,QAAU7K,KAAV6K;MACTC,UAAU,SAAVA,SAAU,GAAA;WAAKC;;MAEfC,iBAAiBJ,MAAMpO,UAAU8D,KAAhB;MACjB2K,cAAcL,MAAMlI,OAAOpC,KAAb;MAEd4K,aAAa,CAAC,QAAQ,OAAT,EAAkB5Q,QAAQ+L,KAAKnD,SAA/B,MAA8C;MAC3DiI,cAAc9E,KAAKnD,UAAU5I,QAAQ,GAAvB,MAAgC;MAC9C8Q,kBAAkBJ,iBAAiB,MAAMC,cAAc;MACvDI,eAAeL,iBAAiB,MAAM,KAAKC,cAAc,MAAM;MAE/DK,sBAAsB,CAACX,cACzBG,UACAI,cAAcC,eAAeC,kBAC7BR,QACAC;MACEU,oBAAoB,CAACZ,cAAcG,UAAUF;SAE5C;UACCU,oBACJD,gBAAgB,CAACF,eAAeR,cAC5BjI,OAAOpD,OAAO,IACdoD,OAAOpD,IAHP;SAKDiM,kBAAkB7I,OAAOtD,GAAzB;YACGmM,kBAAkB7I,OAAOrD,MAAzB;WACDiM,oBAAoB5I,OAAOnD,KAA3B;;;ACxCX,IAAMiM,YAAYpR,aAAa,WAAWkC,KAAKvC,UAAUM,SAA1B;AAShB,SAASoR,aAAapF,MAAMU,SAAS;MAC1CxC,IAASwC,QAATxC,GAAGE,IAAMsC,QAANtC;MACH/B,SAAW2D,KAAKhG,QAAhBqC;MAGFgJ,8BAA8BlG,KAClCa,KAAKgE,SAASjE,WACd,SAAA,UAAA;WAAYjH,SAASuI,SAAS;GAFI,EAGlCiE;MACED,gCAAgClF,QAAW;YACrCG,KACN,+HADF;;MAIIgF,kBACJD,gCAAgClF,SAC5BkF,8BACA3E,QAAQ4E;MAERxO,eAAeH,gBAAgBqJ,KAAKgE,SAAS3H,MAA9B;MACfkJ,mBAAmBpL,sBAAsBrD,YAAtB;MAGnBsC,SAAS;cACHiD,OAAOyE;;MAGb9G,UAAUqK,kBACdrE,MACAxM,OAAOgS,mBAAmB,KAAK,CAACL,SAFlB;MAKV7L,QAAQ4E,MAAM,WAAW,QAAQ;MACjC3E,QAAQ6E,MAAM,UAAU,SAAS;MAKjCqH,mBAAmBnE,yBAAyB,WAAzB;MAWrBrI,OAAAA,QAAMF,MAAAA;MACNO,UAAU,UAAU;QAGlBxC,aAAarB,aAAa,QAAQ;YAC9B,CAACqB,aAAa0D,eAAeR,QAAQhB;WACtC;YACC,CAACuM,iBAAiBrL,SAASF,QAAQhB;;SAEtC;UACCgB,QAAQjB;;MAEZQ,UAAU,SAAS;QACjBzC,aAAarB,aAAa,QAAQ;aAC7B,CAACqB,aAAayD,cAAcP,QAAQd;WACtC;aACE,CAACqM,iBAAiBtL,QAAQD,QAAQd;;SAEtC;WACEc,QAAQf;;MAEbqM,mBAAmBG,kBAAkB;WAChCA,gBAAP,IAAA,iBAA0CxM,OAA1C,SAAqDF,MAArD;WACOO,KAAP,IAAgB;WACTC,KAAP,IAAgB;WACTyI,aAAa;SACf;QAEC0D,YAAYpM,UAAU,WAAW,KAAK;QACtCqM,aAAapM,UAAU,UAAU,KAAK;WACrCD,KAAP,IAAgBP,MAAM2M;WACfnM,KAAP,IAAgBN,OAAO0M;WAChB3D,aAAgB1I,QAAvB,OAAiCC;;MAI7BsK,aAAa;mBACF7D,KAAKnD;;OAIjBgH,aAAL,SAAA,CAAA,GAAuBA,YAAe7D,KAAK6D,UAA3C;OACKzK,SAAL,SAAA,CAAA,GAAmBA,QAAW4G,KAAK5G,MAAnC;OACK8K,cAAL,SAAA,CAAA,GAAwBlE,KAAKhG,QAAQ4L,OAAU5F,KAAKkE,WAApD;SAEOlE;;AClGM,SAAS6F,mBACtB9F,YACA+F,gBACAC,eACA;MACMC,aAAa7G,KAAKY,YAAW,SAAA,MAAA;QAAGsB,OAAH,KAAGA;WAAWA,SAASyE;GAAvC;MAEbG,aACJ,CAAC,CAACD,cACFjG,WAAUqB,KAAK,SAAA,UAAY;WAEvBtI,SAASuI,SAAS0E,iBAClBjN,SAASyH,WACTzH,SAASvB,QAAQyO,WAAWzO;GAJhC;MAQE,CAAC0O,YAAY;QACTD,cAAAA,MAAkBF,iBAAlB;QACAI,YAAAA,MAAiBH,gBAAjB;YACEzF,KACH4F,YADL,8BAC0CF,cAD1C,8DACgHA,cADhH,GAAA;;SAIKC;;ACxBM,SAASL,MAAM5F,MAAMU,SAAS;;MAEvC,CAACmF,mBAAmB7F,KAAKgE,SAASjE,WAAW,SAAS,cAArD,GAAsE;WAClEC;;MAGLiE,eAAevD,QAAQzL;MAGvB,OAAOgP,iBAAiB,UAAU;mBACrBjE,KAAKgE,SAAS3H,OAAO8J,cAAclC,YAAnC;QAGX,CAACA,cAAc;aACVjE;;SAEJ;QAGD,CAACA,KAAKgE,SAAS3H,OAAOnE,SAAS+L,YAA9B,GAA6C;cACxC3D,KACN,+DADF;aAGON;;;MAILnD,YAAYmD,KAAKnD,UAAUgB,MAAM,GAArB,EAA0B,CAA1B;sBACYmC,KAAKhG,SAA3BqC,SA5BmC,cA4BnCA,QAAQlG,YA5B2B,cA4B3BA;MACV0O,aAAa,CAAC,QAAQ,OAAT,EAAkB5Q,QAAQ4I,SAA1B,MAAyC;MAEtDuJ,MAAMvB,aAAa,WAAW;MAC9BwB,kBAAkBxB,aAAa,QAAQ;MACvCxM,OAAOgO,gBAAgBC,YAAhB;MACPC,UAAU1B,aAAa,SAAS;MAChC2B,SAAS3B,aAAa,WAAW;MACjC4B,mBAAmBxI,cAAcgG,YAAd,EAA4BmC,GAA5B;MAQrBjQ,UAAUqQ,MAAV,IAAoBC,mBAAmBpK,OAAOhE,IAAP,GAAc;SAClD2B,QAAQqC,OAAOhE,IAApB,KACEgE,OAAOhE,IAAP,KAAgBlC,UAAUqQ,MAAV,IAAoBC;;MAGpCtQ,UAAUkC,IAAV,IAAkBoO,mBAAmBpK,OAAOmK,MAAP,GAAgB;SAClDxM,QAAQqC,OAAOhE,IAApB,KACElC,UAAUkC,IAAV,IAAkBoO,mBAAmBpK,OAAOmK,MAAP;;OAEpCxM,QAAQqC,SAAStC,cAAciG,KAAKhG,QAAQqC,MAA3B;MAGhBqK,SAASvQ,UAAUkC,IAAV,IAAkBlC,UAAUiQ,GAAV,IAAiB,IAAIK,mBAAmB;MAInEnR,MAAMN,yBAAyBgL,KAAKgE,SAAS3H,MAAvC;MACNsK,mBAAmBnN,WAAWlE,IAAAA,WAAa+Q,eAAb,CAAX;MACnBO,mBAAmBpN,WAAWlE,IAAAA,WAAa+Q,kBAAb,OAAA,CAAX;MACrBQ,YACFH,SAAS1G,KAAKhG,QAAQqC,OAAOhE,IAApB,IAA4BsO,mBAAmBC;cAG9CjN,KAAKC,IAAID,KAAKmN,IAAIzK,OAAO+J,GAAP,IAAcK,kBAAkBI,SAAzC,GAAqD,CAA9D;OAEP5C,eAAeA;OACfjK,QAAQ4L,SAAb,sBAAA,CAAA,GAAA,eAAA,qBACGvN,MAAOsB,KAAK4K,MAAMsC,SAAX,CADV,GAAA,eAAA,qBAEGN,SAAU,EAFb,GAAA;SAKOvG;;AChFM,SAAS+G,qBAAqBnJ,WAAW;MAClDA,cAAc,OAAO;WAChB;aACEA,cAAc,SAAS;WACzB;;SAEFA;;ACkBT,IAAA,aAAe,CACb,cACA,QACA,YACA,aACA,OACA,WACA,eACA,SACA,aACA,cACA,UACA,gBACA,YACA,QACA,YAfa;AC5Bf,IAAMoJ,kBAAkBC,WAAW7G,MAAM,CAAjB;AAYT,SAAS8G,UAAUrK,WAA4B;MAAjBsK,UAAiB,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAP;MAC/CC,QAAQJ,gBAAgB/S,QAAQ4I,SAAxB;MACRuC,MAAM4H,gBACT5G,MAAMgH,QAAQ,CADL,EAETC,OAAOL,gBAAgB5G,MAAM,GAAGgH,KAAzB,CAFE;SAGLD,UAAU/H,IAAIkI,QAAJ,IAAgBlI;;ACZnC,IAAMmI,YAAY;QACV;aACK;oBACO;;AAUL,SAAS3G,KAAKZ,MAAMU,SAAS;MAEtCQ,kBAAkBlB,KAAKgE,SAASjE,WAAW,OAA3C,GAAqD;WAChDC;;MAGLA,KAAKwH,WAAWxH,KAAKnD,cAAcmD,KAAKa,mBAAmB;WAEtDb;;MAGHxD,aAAaJ,cACjB4D,KAAKgE,SAAS3H,QACd2D,KAAKgE,SAAS7N,WACduK,QAAQpE,SACRoE,QAAQnE,mBACRyD,KAAKW,aALY;MAQf9D,YAAYmD,KAAKnD,UAAUgB,MAAM,GAArB,EAA0B,CAA1B;MACZ4J,oBAAoBnJ,qBAAqBzB,SAArB;MACpBe,YAAYoC,KAAKnD,UAAUgB,MAAM,GAArB,EAA0B,CAA1B,KAAgC;MAE5C6J,YAAY,CAAA;UAERhH,QAAQiH,UAAhB;SACOJ,UAAUK;kBACD,CAAC/K,WAAW4K,iBAAZ;;SAETF,UAAUM;kBACDX,UAAUrK,SAAV;;SAET0K,UAAUO;kBACDZ,UAAUrK,WAAW,IAArB;;;kBAGA6D,QAAQiH;;YAGdtH,QAAQ,SAAC0H,MAAMX,OAAU;QAC7BvK,cAAckL,QAAQL,UAAU5T,WAAWsT,QAAQ,GAAG;aACjDpH;;gBAGGA,KAAKnD,UAAUgB,MAAM,GAArB,EAA0B,CAA1B;wBACQS,qBAAqBzB,SAArB;QAEdgC,gBAAgBmB,KAAKhG,QAAQqC;QAC7B2L,aAAahI,KAAKhG,QAAQ7D;QAG1BqO,QAAQ7K,KAAK6K;QACbyD,cACHpL,cAAc,UACb2H,MAAM3F,cAAc3F,KAApB,IAA6BsL,MAAMwD,WAAW/O,IAAjB,KAC9B4D,cAAc,WACb2H,MAAM3F,cAAc5F,IAApB,IAA4BuL,MAAMwD,WAAW9O,KAAjB,KAC7B2D,cAAc,SACb2H,MAAM3F,cAAc7F,MAApB,IAA8BwL,MAAMwD,WAAWjP,GAAjB,KAC/B8D,cAAc,YACb2H,MAAM3F,cAAc9F,GAApB,IAA2ByL,MAAMwD,WAAWhP,MAAjB;QAEzBkP,gBAAgB1D,MAAM3F,cAAc5F,IAApB,IAA4BuL,MAAMhI,WAAWvD,IAAjB;QAC5CkP,iBAAiB3D,MAAM3F,cAAc3F,KAApB,IAA6BsL,MAAMhI,WAAWtD,KAAjB;QAC9CkP,eAAe5D,MAAM3F,cAAc9F,GAApB,IAA2ByL,MAAMhI,WAAWzD,GAAjB;QAC1CsP,kBACJ7D,MAAM3F,cAAc7F,MAApB,IAA8BwL,MAAMhI,WAAWxD,MAAjB;QAE1BsP,sBACHzL,cAAc,UAAUqL,iBACxBrL,cAAc,WAAWsL,kBACzBtL,cAAc,SAASuL,gBACvBvL,cAAc,YAAYwL;QAGvBxD,aAAa,CAAC,OAAO,QAAR,EAAkB5Q,QAAQ4I,SAA1B,MAAyC;QAGtD0L,wBACJ,CAAC,CAAC7H,QAAQ8H,mBACR3D,cAAcjH,cAAc,WAAWsK,iBACtCrD,cAAcjH,cAAc,SAASuK,kBACrC,CAACtD,cAAcjH,cAAc,WAAWwK,gBACxC,CAACvD,cAAcjH,cAAc,SAASyK;QAGrCI,4BACJ,CAAC,CAAC/H,QAAQgI,4BACR7D,cAAcjH,cAAc,WAAWuK,kBACtCtD,cAAcjH,cAAc,SAASsK,iBACrC,CAACrD,cAAcjH,cAAc,WAAWyK,mBACxC,CAACxD,cAAcjH,cAAc,SAASwK;QAErCO,mBAAmBJ,yBAAyBE;QAE9CR,eAAeK,uBAAuBK,kBAAkB;WAErDnB,UAAU;UAEXS,eAAeK,qBAAqB;oBAC1BZ,UAAUN,QAAQ,CAAlB;;UAGVuB,kBAAkB;oBACR5B,qBAAqBnJ,SAArB;;WAGTf,YAAYA,aAAae,YAAY,MAAMA,YAAY;WAIvD5D,QAAQqC,SAAb,SAAA,CAAA,GACK2D,KAAKhG,QAAQqC,QACbqC,iBACDsB,KAAKgE,SAAS3H,QACd2D,KAAKhG,QAAQ7D,WACb6J,KAAKnD,SAHJ,CAFL;aASOiD,aAAaE,KAAKgE,SAASjE,WAAWC,MAAM,MAA5C;;GAjFX;SAoFOA;;ACzIM,SAAS4I,aAAa5I,MAAM;sBACXA,KAAKhG,SAA3BqC,SADiC,cACjCA,QAAQlG,YADyB,cACzBA;MACV0G,YAAYmD,KAAKnD,UAAUgB,MAAM,GAArB,EAA0B,CAA1B;MACZ2G,QAAQ7K,KAAK6K;MACbK,aAAa,CAAC,OAAO,QAAR,EAAkB5Q,QAAQ4I,SAA1B,MAAyC;MACtDxE,OAAOwM,aAAa,UAAU;MAC9B2B,SAAS3B,aAAa,SAAS;MAC/B5F,cAAc4F,aAAa,UAAU;MAEvCxI,OAAOhE,IAAP,IAAemM,MAAMrO,UAAUqQ,MAAV,CAAN,GAA0B;SACtCxM,QAAQqC,OAAOmK,MAApB,IACEhC,MAAMrO,UAAUqQ,MAAV,CAAN,IAA2BnK,OAAO4C,WAAP;;MAE3B5C,OAAOmK,MAAP,IAAiBhC,MAAMrO,UAAUkC,IAAV,CAAN,GAAwB;SACtC2B,QAAQqC,OAAOmK,MAApB,IAA8BhC,MAAMrO,UAAUkC,IAAV,CAAN;;SAGzB2H;;ACRF,SAAS6I,QAAQC,KAAK7J,aAAaJ,eAAeF,kBAAkB;MAEnEd,QAAQiL,IAAIlJ,MAAM,2BAAV;MACRF,QAAQ,CAAC7B,MAAM,CAAN;MACT8F,OAAO9F,MAAM,CAAN;MAGT,CAAC6B,OAAO;WACHoJ;;MAGLnF,KAAK1P,QAAQ,GAAb,MAAsB,GAAG;QACvBgB,UAAAA;YACI0O,MAAR;WACO;kBACO9E;;WAEP;WACA;;kBAEOF;;QAGRjG,OAAOqB,cAAc9E,OAAd;WACNyD,KAAKuG,WAAL,IAAoB,MAAMS;aACxBiE,SAAS,QAAQA,SAAS,MAAM;QAErCoF,OAAAA;QACApF,SAAS,MAAM;aACVhK,KAAKC,IACVnG,SAASmD,gBAAgB4D,cACzBhH,OAAOsI,eAAe,CAFjB;WAIF;aACEnC,KAAKC,IACVnG,SAASmD,gBAAgB2D,aACzB/G,OAAOqI,cAAc,CAFhB;;WAKFkN,OAAO,MAAMrJ;SACf;WAGEA;;;AAeJ,SAASsJ,YACdjN,SACA8C,eACAF,kBACAsK,eACA;MACMjP,UAAU,CAAC,GAAG,CAAJ;MAKVkP,YAAY,CAAC,SAAS,MAAV,EAAkBjV,QAAQgV,aAA1B,MAA6C;MAIzDE,YAAYpN,QAAO8B,MAAM,SAAb,EAAwBV,IAAI,SAAA,MAAA;WAAQiM,KAAKC,KAAL;GAApC;MAIZC,UAAUH,UAAUlV,QACxBkL,KAAKgK,WAAW,SAAA,MAAA;WAAQC,KAAKG,OAAO,MAAZ,MAAwB;GAAhD,CADc;MAIZJ,UAAUG,OAAV,KAAsBH,UAAUG,OAAV,EAAmBrV,QAAQ,GAA3B,MAAoC,IAAI;YACxDqM,KACN,8EADF;;MAOIkJ,aAAa;MACfC,MAAMH,YAAY,KAClB,CACEH,UACG/I,MAAM,GAAGkJ,OADZ,EAEGjC,OAAO,CAAC8B,UAAUG,OAAV,EAAmBzL,MAAM2L,UAAzB,EAAqC,CAArC,CAAD,CAFV,GAGA,CAACL,UAAUG,OAAV,EAAmBzL,MAAM2L,UAAzB,EAAqC,CAArC,CAAD,EAA0CnC,OACxC8B,UAAU/I,MAAMkJ,UAAU,CAA1B,CADF,CAJF,IAQA,CAACH,SAAD;QAGEM,IAAItM,IAAI,SAACuM,IAAItC,OAAU;QAErBnI,eAAemI,UAAU,IAAI,CAAC8B,YAAYA,aAC5C,WACA;QACAS,oBAAoB;WAEtBD,GAGGE,OAAO,SAACtM,GAAGC,GAAM;UACZD,EAAEA,EAAExJ,SAAS,CAAb,MAAoB,MAAM,CAAC,KAAK,GAAN,EAAWG,QAAQsJ,CAAnB,MAA0B,IAAI;UACxDD,EAAExJ,SAAS,CAAb,IAAkByJ;4BACE;eACbD;iBACEqM,mBAAmB;UAC1BrM,EAAExJ,SAAS,CAAb,KAAmByJ;4BACC;eACbD;aACF;eACEA,EAAE+J,OAAO9J,CAAT;;OAER,CAAA,CAfL,EAiBGJ,IAAI,SAAA,KAAA;aAAO0L,QAAQC,KAAK7J,aAAaJ,eAAeF,gBAAzC;KAjBd;GAPE;MA6BF0B,QAAQ,SAACqJ,IAAItC,OAAU;OACtB/G,QAAQ,SAAC+I,MAAMS,QAAW;UACvBvG,UAAU8F,IAAV,GAAiB;gBACXhC,KAAR,KAAkBgC,QAAQM,GAAGG,SAAS,CAAZ,MAAmB,MAAM,KAAK;;KAF5D;GADF;SAOO7P;;AAYM,SAAS+B,OAAOiE,MAAhB,MAAkC;MAAVjE,UAAU,KAAVA;MAC7Bc,YAA8CmD,KAA9CnD,2BAA8CmD,KAAnChG,SAAWqC,SADiB,cACjBA,QAAQlG,YADS,cACTA;MAChC8S,gBAAgBpM,UAAUgB,MAAM,GAAhB,EAAqB,CAArB;MAElB7D,UAAAA;MACAsJ,UAAU,CAACvH,OAAX,GAAoB;cACZ,CAAC,CAACA,SAAQ,CAAV;SACL;cACKiN,YAAYjN,SAAQM,QAAQlG,WAAW8S,aAAvC;;MAGRA,kBAAkB,QAAQ;WACrBlQ,OAAOiB,QAAQ,CAAR;WACPf,QAAQe,QAAQ,CAAR;aACNiP,kBAAkB,SAAS;WAC7BlQ,OAAOiB,QAAQ,CAAR;WACPf,QAAQe,QAAQ,CAAR;aACNiP,kBAAkB,OAAO;WAC3BhQ,QAAQe,QAAQ,CAAR;WACRjB,OAAOiB,QAAQ,CAAR;aACLiP,kBAAkB,UAAU;WAC9BhQ,QAAQe,QAAQ,CAAR;WACRjB,OAAOiB,QAAQ,CAAR;;OAGXqC,SAASA;SACP2D;;ACrLM,SAAS8J,gBAAgB9J,MAAMU,SAAS;MACjDnE,oBACFmE,QAAQnE,qBAAqB5F,gBAAgBqJ,KAAKgE,SAAS3H,MAA9B;MAK3B2D,KAAKgE,SAAS7N,cAAcoG,mBAAmB;wBAC7B5F,gBAAgB4F,iBAAhB;;MAMhBwN,gBAAgBzI,yBAAyB,WAAzB;MAChB0I,eAAehK,KAAKgE,SAAS3H,OAAOwF;MAClC9I,MAA0CiR,aAA1CjR,KAAKE,OAAqC+Q,aAArC/Q,MAAuBgR,YAAcD,aAA9BD,aAhBiC;eAiBxChR,MAAM;eACNE,OAAO;eACP8Q,aAAb,IAA8B;MAExBvN,aAAaJ,cACjB4D,KAAKgE,SAAS3H,QACd2D,KAAKgE,SAAS7N,WACduK,QAAQpE,SACRC,mBACAyD,KAAKW,aALY;eAUN5H,MAAMA;eACNE,OAAOA;eACP8Q,aAAb,IAA8BE;UAEtBzN,aAAaA;MAEfjF,QAAQmJ,QAAQwJ;MAClB7N,SAAS2D,KAAKhG,QAAQqC;MAEpBgD,QAAQ;aAAA,SAAA,QACJxC,WAAW;UACb6C,QAAQrD,OAAOQ,SAAP;UAEVR,OAAOQ,SAAP,IAAoBL,WAAWK,SAAX,KACpB,CAAC6D,QAAQyJ,qBACT;gBACQxQ,KAAKC,IAAIyC,OAAOQ,SAAP,GAAmBL,WAAWK,SAAX,CAA5B;;gCAEAA,WAAY6C,KAAtB;;eATU,SAAA,UAWF7C,WAAW;UACbkC,WAAWlC,cAAc,UAAU,SAAS;UAC9C6C,QAAQrD,OAAO0C,QAAP;UAEV1C,OAAOQ,SAAP,IAAoBL,WAAWK,SAAX,KACpB,CAAC6D,QAAQyJ,qBACT;gBACQxQ,KAAKmN,IACXzK,OAAO0C,QAAP,GACAvC,WAAWK,SAAX,KACGA,cAAc,UAAUR,OAAOpC,QAAQoC,OAAOnC,OAH3C;;gCAMA6E,UAAWW,KAArB;;;QAIEW,QAAQ,SAAA,WAAa;QACnBhI,OACJ,CAAC,QAAQ,KAAT,EAAgBpE,QAAQ4I,SAAxB,MAAuC,KAAK,YAAY;0BAC5CR,QAAWgD,MAAMhH,IAAN,EAAYwE,SAAZ,CAAzB;GAHF;OAMK7C,QAAQqC,SAASA;SAEf2D;;AChFM,SAASoK,MAAMpK,MAAM;MAC5BnD,YAAYmD,KAAKnD;MACjBoM,gBAAgBpM,UAAUgB,MAAM,GAAhB,EAAqB,CAArB;MAChBwM,iBAAiBxN,UAAUgB,MAAM,GAAhB,EAAqB,CAArB;MAGnBwM,gBAAgB;wBACYrK,KAAKhG,SAA3B7D,YADU,cACVA,WAAWkG,SADD,cACCA;QACbwI,aAAa,CAAC,UAAU,KAAX,EAAkB5Q,QAAQgV,aAA1B,MAA6C;QAC1D5Q,OAAOwM,aAAa,SAAS;QAC7B5F,cAAc4F,aAAa,UAAU;QAErCyF,eAAe;gCACTjS,MAAOlC,UAAUkC,IAAV,CAAjB;8BAEGA,MAAOlC,UAAUkC,IAAV,IAAkBlC,UAAU8I,WAAV,IAAyB5C,OAAO4C,WAAP,CADrD;;SAKGjF,QAAQqC,SAAb,SAAA,CAAA,GAA2BA,QAAWiO,aAAaD,cAAb,CAAtC;;SAGKrK;;ACnBM,SAASuK,KAAKvK,MAAM;MAC7B,CAAC6F,mBAAmB7F,KAAKgE,SAASjE,WAAW,QAAQ,iBAApD,GAAwE;WACpEC;;MAGHlD,UAAUkD,KAAKhG,QAAQ7D;MACvBqU,QAAQrL,KACZa,KAAKgE,SAASjE,WACd,SAAA,UAAA;WAAYjH,SAASuI,SAAS;GAFlB,EAGZ7E;MAGAM,QAAQ9D,SAASwR,MAAMzR,OACvB+D,QAAQ7D,OAAOuR,MAAMtR,SACrB4D,QAAQ/D,MAAMyR,MAAMxR,UACpB8D,QAAQ5D,QAAQsR,MAAMvR,MACtB;QAEI+G,KAAKuK,SAAS,MAAM;aACfvK;;SAGJuK,OAAO;SACP1G,WAAW,qBAAhB,IAAyC;SACpC;QAED7D,KAAKuK,SAAS,OAAO;aAChBvK;;SAGJuK,OAAO;SACP1G,WAAW,qBAAhB,IAAyC;;SAGpC7D;;AClCM,SAASyK,MAAMzK,MAAM;MAC5BnD,YAAYmD,KAAKnD;MACjBoM,gBAAgBpM,UAAUgB,MAAM,GAAhB,EAAqB,CAArB;sBACQmC,KAAKhG,SAA3BqC,SAH0B,cAG1BA,QAAQlG,YAHkB,cAGlBA;MACV2I,UAAU,CAAC,QAAQ,OAAT,EAAkB7K,QAAQgV,aAA1B,MAA6C;MAEvDyB,iBAAiB,CAAC,OAAO,MAAR,EAAgBzW,QAAQgV,aAAxB,MAA2C;SAE3DnK,UAAU,SAAS,KAA1B,IACE3I,UAAU8S,aAAV,KACCyB,iBAAiBrO,OAAOyC,UAAU,UAAU,QAA3B,IAAuC;OAEtDjC,YAAYyB,qBAAqBzB,SAArB;OACZ7C,QAAQqC,SAAStC,cAAcsC,MAAd;SAEf2D;;ACOT,IAAA,YAAe;;;;;;;;;SASN;;WAEE;;aAEE;;QAELoK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAyCE;;WAEC;;aAEE;;QAELrO;;;;YAII;;;;;;;;;;;;;;;;;;;mBAoBO;;WAER;;aAEE;;QAEL+N;;;;;;cAMM,CAAC,QAAQ,SAAS,OAAO,QAAzB;;;;;;;aAOD;;;;;;uBAMU;;;;;;;;;;;gBAYP;;WAEL;;aAEE;;QAELlB;;;;;;;;;;;;SAaC;;WAEE;;aAEE;;QAELhD;;aAEK;;;;;;;;;;;;;QAcL;;WAEG;;aAEE;;QAELhF;;;;;;;cAOM;;;;;aAKD;;;;;;;uBAOU;;;;;;;;oBAQH;;;;;;;;6BAQS;;;;;;;;;SAUpB;;WAEE;;aAEE;;QAEL6J;;;;;;;;;;;;QAaA;;WAEG;;aAEE;;QAELF;;;;;;;;;;;;;;;;;gBAkBQ;;WAEL;;aAEE;;QAELnF;;;;;;qBAMa;;;;;;OAMd;;;;;;OAMA;;;;;;;;;;;;;;;;;cAkBO;;WAEH;;aAEE;;QAELrB;;YAEII;;;;;;;qBAOShE;;;ACzUrB,IAAA,WAAe;;;;;aAKF;;;;;iBAMI;;;;;iBAMA;;;;;;mBAOE;;;;;;;YAQP,SAAA,WAAM;EAAA;;;;;;;;;YAUN,SAAA,WAAM;EAAA;;;;;;;;ACvDlB,IAOqBwK,SAAAA,WAAAA;mBASPxU,WAAWkG,QAAsB;;QAAdqE,UAAc,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAJ,CAAA;;SAyFzCyC,iBAAiB,WAAA;aAAMyH,sBAAsB,MAAKpK,MAA3B;;SAvFhBA,SAASqK,SAAS,KAAKrK,OAAOsK,KAAK,IAAjB,CAAT;SAGTpK,UAAL,SAAA,CAAA,GAAoBiK,QAAOI,UAAarK,OAAxC;SAGK3C,QAAQ;mBACE;iBACF;qBACI,CAAA;;SAIZ5H,YAAYA,aAAaA,UAAU6U,SAAS7U,UAAU,CAAV,IAAeA;SAC3DkG,SAASA,UAAUA,OAAO2O,SAAS3O,OAAO,CAAP,IAAYA;SAG/CqE,QAAQX,YAAY,CAAA;WAClB7C,KAAP,SAAA,CAAA,GACKyN,QAAOI,SAAShL,WAChBW,QAAQX,SAFb,CAAA,EAGGM,QAAQ,SAAA,MAAQ;YACZK,QAAQX,UAAUsB,IAAvB,IAAA,SAAA,CAAA,GAEMsJ,QAAOI,SAAShL,UAAUsB,IAA1B,KAAmC,CAAA,GAEnCX,QAAQX,YAAYW,QAAQX,UAAUsB,IAAlB,IAA0B,CAAA,CAJpD;KAJF;SAaKtB,YAAY9C,OAAOC,KAAK,KAAKwD,QAAQX,SAAzB,EACd5C,IAAI,SAAA,MAAA;;;SAEA,MAAKuD,QAAQX,UAAUsB,IAAvB,CAFA;KADU,EAMdhE,KAAK,SAACC,GAAGC,GAAJ;aAAUD,EAAE/F,QAAQgG,EAAEhG;KANb;SAYZwI,UAAUM,QAAQ,SAAA,iBAAmB;UACpC+D,gBAAgB7D,WAAW5L,YAAWyP,gBAAgB6G,MAA3B,GAAoC;wBACjDA,OACd,MAAK9U,WACL,MAAKkG,QACL,MAAKqE,SACL0D,iBACA,MAAKrG,KALP;;KAFJ;SAaKyC,OAAL;QAEMyC,gBAAgB,KAAKvC,QAAQuC;QAC/BA,eAAe;WAEZC,qBAAL;;SAGGnF,MAAMkF,gBAAgBA;;;;gCAKpB;aACAzC,OAAOzL,KAAK,IAAZ;;;;iCAEC;aACD+M,QAAQ/M,KAAK,IAAb;;;;8CAEc;aACdmO,qBAAqBnO,KAAK,IAA1B;;;;+CAEe;aACfkN,sBAAsBlN,KAAK,IAA3B;;;;;;;;;;;;;;;;;;;;;;;;;;AA1FU4V,OAoHZO,SAAS,OAAO1X,WAAW,cAAcA,SAAS2X,QAAQC;AApH9CT,OAsHZ1D,aAAaA;AAtHD0D,OAwHZI,WAAWA;;;;A7DjIpB,uBAAsB;;;A8DDtB,IAAMM,2BAAoD;EACxD;EACA;EACA;EACA;EACA;;AAGF,IAAMC,eAAc;EAClB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGF,IAAMC,kBAAiB;EACrB;EACA;EACA;EACA;EACA;EACA;EACA;;AASI,SAAUC,eAAc,OAAc;AAC1C,MAAM,iBAAiB,OAAO,UAAU,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE;AAExE,MAAI,iBAAiB,KAAK,cAAc,GAAG;AACzC,WAAO;;AAGT,MAAIC,cAAa,cAAc,GAAG;AAChC,WAAO;;AAGT,SAAO;AACT;AAEA,SAASC,gBAAkB,MAAY;AACrC,SAAO,SAAC,OAAc;AAAiB,WAAAF,eAAc,KAAK,MAAM;EAAzB;AACzC;AAEA,SAASC,cAAa,MAAa;AACjC,SAAOH,aAAY,SAAS,IAAmB;AACjD;AAGA,SAASK,UAAyC,MAAY;AAC5D,SAAO,SAAC,OAAc;AAAiB,WAAA,OAAO,UAAU;EAAjB;AACzC;AAEA,SAASC,iBAAgB,MAAa;AACpC,SAAOL,gBAAe,SAAS,IAAsB;AACvD;AAEA,SAASM,IAAG,OAAc;AACxB,MAAI,UAAU,MAAM;AAClB,WAAO;;AAGT,UAAQ,OAAO,OAAO;IACpB,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;;AAGF,MAAIA,IAAG,MAAM,KAAK,GAAG;AACnB,WAAO;;AAGT,MAAIA,IAAG,cAAc,KAAK,GAAG;AAC3B,WAAO;;AAGT,MAAM,UAAUL,eAAc,KAAK;AAGnC,MAAI,SAAS;AACX,WAAO;;AAIT,SAAO;AACT;AAEAK,IAAG,QAAQ,MAAM;AAEjBA,IAAG,UAAU,SAAC,QAAmB,WAAkC;AACjE,MAAI,CAACA,IAAG,MAAM,MAAM,KAAK,CAACA,IAAG,SAAS,SAAS,GAAG;AAChD,WAAO;;AAGT,SAAO,OAAO,MAAM,SAAA,GAAC;AAAI,WAAA,UAAU,CAAC;EAAX,CAAY;AACvC;AAEAA,IAAG,yBAAyB,SAAC,OAAc;AACzC,SAAAL,eAAc,KAAK,MAAM;AAAzB;AAGFK,IAAG,gBAAgBH,gBAAyB,eAAe;AAE3DG,IAAG,SAASF,UAAiB,QAAQ;AAErCE,IAAG,UAAU,SAAC,OAAc;AAC1B,SAAO,UAAU,QAAQ,UAAU;AACrC;AAEAA,IAAG,OAAOH,gBAAqB,MAAM;AAErCG,IAAG,UAAU,SAAC,OAAc;AAAc,SAAA,CAACA,IAAG,UAAU,KAAK;AAAnB;AAE1CA,IAAG,aAAa,SAAC,OAAc;AAC7B,SACEA,IAAG,OAAO,KAAK,KACf,CAACA,IAAG,YAAY,KAAK,KACpB,MAAsB,aAAa,KACpCA,IAAG,OAAQ,MAAsB,QAAQ,KACzCR,yBAAwB,MAAM,SAAA,UAAQ;AAAI,WAAA,YAAa;EAAb,CAAkC;AAEhF;AAEAQ,IAAG,QAAQ,SAAC,OAAc;AACxB,SACGA,IAAG,OAAO,KAAK,KAAK,MAAM,WAAW,KACrCA,IAAG,MAAM,KAAK,KAAK,MAAM,WAAW,KACpCA,IAAG,OAAO,KAAK,KAAK,CAACA,IAAG,IAAI,KAAK,KAAK,CAACA,IAAG,IAAI,KAAK,KAAK,OAAO,KAAK,KAAK,EAAE,WAAW,KACtFA,IAAG,IAAI,KAAK,KAAK,MAAM,SAAS,KAChCA,IAAG,IAAI,KAAK,KAAK,MAAM,SAAS;AAErC;AAEAA,IAAG,QAAQH,gBAAsB,OAAO;AAGxCG,IAAG,WAAWF,UAAmB,UAAU;AAE3CE,IAAG,YAAY,SAAC,OAAc;AAC5B,SACEA,IAAG,SAAS,KAAK,KACjBA,IAAG,SAAU,MAAoC,IAAI,KACrDA,IAAG,SAAU,MAAoC,KAAK;AAE1D;AAEAA,IAAG,oBAAoBH,gBAAkC,mBAAmB;AAE5EG,IAAG,aAAa,SAAI,UAAmB,QAAgB;AACrD,MAAI,CAAC,YAAY,CAAE,QAAqB;AACtC,WAAO;;AAGT,SAAO,OAAO,eAAe,QAAQ,MAAM,OAAO;AACpD;AAEAA,IAAG,WAAW,SAAC,OAAc;AAC3B,SACE,CAACA,IAAG,gBAAgB,KAAK,KAAKA,IAAG,SAAU,MAAoC,OAAO,QAAQ,CAAC;AAEnG;AAEAA,IAAG,MAAMH,gBAAsC,KAAK;AAEpDG,IAAG,MAAM,SAAC,OAAc;AACtB,SAAO,OAAO,MAAM,KAAe;AACrC;AAEAA,IAAG,OAAO,SAAC,OAAc;AACvB,SAAO,UAAU;AACnB;AAEAA,IAAG,kBAAkB,SAAC,OAAc;AAClC,SAAOA,IAAG,KAAK,KAAK,KAAKA,IAAG,UAAU,KAAK;AAC7C;AAEAA,IAAG,SAAS,SAAC,OAAc;AACzB,SAAOF,UAAiB,QAAQ,EAAE,KAAK,KAAK,CAACE,IAAG,IAAI,KAAK;AAC3D;AAEAA,IAAG,gBAAgB,SAAC,OAAc;AAChC,SAAOA,IAAG,OAAO,KAAK,KAAM,MAAiB,SAAS,KAAK,CAAC,OAAO,MAAM,OAAO,KAAK,CAAC;AACxF;AAGAA,IAAG,SAAS,SAAC,OAAc;AACzB,SAAO,CAACA,IAAG,gBAAgB,KAAK,MAAMA,IAAG,SAAS,KAAK,KAAK,OAAO,UAAU;AAC/E;AAEAA,IAAG,QAAQ,SAAC,QAAmB,OAAU;AACvC,MAAI,CAACA,IAAG,MAAM,MAAM,GAAG;AACrB,WAAO;;AAIT,SAAO,OAAO,QAAQ,KAAK,IAAI;AACjC;AAGAA,IAAG,gBAAgBH,gBAAyB,UAAU;AAEtDG,IAAG,cAAc,SAAC,OAAc;AAC9B,MAAIL,eAAc,KAAK,MAAM,UAAU;AACrC,WAAO;;AAGT,MAAM,YAAY,OAAO,eAAe,KAAK;AAE7C,SAAO,cAAc,QAAQ,cAAc,OAAO,eAAe,CAAA,CAAE;AACrE;AAEAK,IAAG,YAAY,SAAC,OAAc;AAC5B,SAAAA,IAAG,KAAK,KAAK,KAAKD,iBAAgB,OAAO,KAAK;AAA9C;AAEFC,IAAG,UAAUH,gBAAiC,SAAS;AAEvDG,IAAG,aAAa,SACd,QACA,KACA,WAAmC;AAEnC,MAAI,CAACA,IAAG,OAAO,MAAM,KAAK,CAAC,KAAK;AAC9B,WAAO;;AAGT,MAAM,QAAQ,OAAO,GAAG;AAExB,MAAIA,IAAG,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,KAAK;;AAGxB,SAAOA,IAAG,QAAQ,KAAK;AACzB;AAEAA,IAAG,SAASH,gBAAuB,QAAQ;AAE3CG,IAAG,MAAMH,gBAAiC,KAAK;AAE/CG,IAAG,SAASF,UAAiB,QAAQ;AAErCE,IAAG,SAASF,UAAiB,QAAQ;AAErCE,IAAG,YAAYF,UAAoB,WAAW;AAE9CE,IAAG,UAAUH,gBAA8C,SAAS;AAEpEG,IAAG,UAAUH,gBAAqC,SAAS;AAI3D,IAAA,cAAeG;;;ACtRf,SAASC,UAAyC,MAAY;AAC5D,SAAO,SAAC,OAAc;AAAiB,WAAA,OAAO,UAAU;EAAjB;AACzC;AAGO,IAAMC,cAAaD,UAAmB,UAAU;AAEhD,IAAME,UAAS,SAAC,OAAc;AACnC,SAAO,UAAU;AACnB;AAEO,IAAMC,WAAU,SAAC,OAAc;AACpC,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,MAAM;AAChE;AAEO,IAAMC,YAAW,SAAC,OAAc;AACrC,SAAO,CAACC,aAAY,KAAK,KAAK,CAACH,QAAO,KAAK,MAAMD,YAAW,KAAK,KAAK,OAAO,UAAU;AACzF;AAEO,IAAMI,eAAcL,UAAoB,WAAW;;;;;;;;;;;;;;;;;ACpB1D,SAASM,YAAW,MAAiB,OAAgB;AAC3C,MAAA,SAAW,KAAI;AAEvB,MAAI,WAAW,MAAM,QAAQ;AAC3B,WAAO;;AAGT,WAAS,QAAQ,QAAQ,YAAY,KAAK;AACxC,QAAI,CAACC,OAAM,KAAK,KAAK,GAAG,MAAM,KAAK,CAAC,GAAG;AACrC,aAAO;;;AAIX,SAAO;AACT;AAEA,SAASC,kBAAiB,MAAuB,OAAsB;AACrE,MAAI,KAAK,eAAe,MAAM,YAAY;AACxC,WAAO;;AAGT,MAAM,QAAQ,IAAI,SAAS,KAAK,MAAM;AACtC,MAAM,QAAQ,IAAI,SAAS,MAAM,MAAM;AAEvC,MAAI,QAAQ,KAAK;AAEjB,SAAO,SAAS;AACd,QAAI,MAAM,SAAS,KAAK,MAAM,MAAM,SAAS,KAAK,GAAG;AACnD,aAAO;;;AAIX,SAAO;AACT;AAEA,SAASC,UAAS,MAA6B,OAA4B;;AACzE,MAAI,KAAK,SAAS,MAAM,MAAM;AAC5B,WAAO;;;AAGT,aAAoB,KAAA,SAAA,KAAK,QAAO,CAAE,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA/B,UAAM,QAAK,GAAA;AACd,UAAI,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,GAAG;AACxB,eAAO;;;;;;;;;;;;;;;AAIX,aAAoB,KAAA,SAAA,KAAK,QAAO,CAAE,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA/B,UAAM,QAAK,GAAA;AACd,UAAI,CAACF,OAAM,MAAM,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG;AACzC,eAAO;;;;;;;;;;;;;;AAIX,SAAO;AACT;AAEA,SAASG,UAAS,MAAoB,OAAmB;;AACvD,MAAI,KAAK,SAAS,MAAM,MAAM;AAC5B,WAAO;;;AAGT,aAAoB,KAAA,SAAA,KAAK,QAAO,CAAE,GAAA,KAAA,GAAA,KAAA,GAAA,CAAA,GAAA,MAAA,KAAA,GAAA,KAAA,GAAE;AAA/B,UAAM,QAAK,GAAA;AACd,UAAI,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,GAAG;AACxB,eAAO;;;;;;;;;;;;;;AAIX,SAAO;AACT;AAEc,SAAPH,OAAuB,MAAe,OAAc;AACzD,MAAI,SAAS,OAAO;AAClB,WAAO;;AAGT,MAAI,QAAQI,UAAS,IAAI,KAAK,SAASA,UAAS,KAAK,GAAG;AACtD,QAAI,KAAK,gBAAgB,MAAM,aAAa;AAC1C,aAAO;;AAGT,QAAI,MAAM,QAAQ,IAAI,KAAK,MAAM,QAAQ,KAAK,GAAG;AAC/C,aAAOL,YAAW,MAAM,KAAK;;AAG/B,QAAI,gBAAgB,OAAO,iBAAiB,KAAK;AAC/C,aAAOG,UAAS,MAAM,KAAK;;AAG7B,QAAI,gBAAgB,OAAO,iBAAiB,KAAK;AAC/C,aAAOC,UAAS,MAAM,KAAK;;AAG7B,QAAI,YAAY,OAAO,IAAI,KAAK,YAAY,OAAO,KAAK,GAAG;AACzD,aAAOF,kBAAiB,MAAM,KAAK;;AAGrC,QAAII,SAAQ,IAAI,KAAKA,SAAQ,KAAK,GAAG;AACnC,aAAO,KAAK,WAAW,MAAM,UAAU,KAAK,UAAU,MAAM;;AAG9D,QAAI,KAAK,YAAY,OAAO,UAAU,SAAS;AAC7C,aAAO,KAAK,QAAO,MAAO,MAAM,QAAO;;AAGzC,QAAI,KAAK,aAAa,OAAO,UAAU,UAAU;AAC/C,aAAO,KAAK,SAAQ,MAAO,MAAM,SAAQ;;AAG3C,QAAM,WAAW,OAAO,KAAK,IAAI;AACjC,QAAM,YAAY,OAAO,KAAK,KAAK;AAEnC,QAAI,SAAS,WAAW,UAAU,QAAQ;AACxC,aAAO;;AAGT,aAAS,QAAQ,SAAS,QAAQ,YAAY,KAAK;AACjD,UAAI,CAAC,OAAO,UAAU,eAAe,KAAK,OAAO,SAAS,KAAK,CAAC,GAAG;AACjE,eAAO;;;AAIX,aAAS,QAAQ,SAAS,QAAQ,YAAY,KAAK;AACjD,UAAM,MAAM,SAAS,KAAK;AAE1B,UAAI,QAAQ,YAAY,KAAK,UAAU;AAKrC;;AAGF,UAAI,CAACL,OAAM,KAAK,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG;AACjC,eAAO;;;AAIX,WAAO;;AAGT,MAAI,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM,KAAK,GAAG;AAC7C,WAAO;;AAGT,SAAO,SAAS;AAClB;;;AC7IM,SAAUM,iBAAa;AAAC,MAAA,aAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAAkB;AAAlB,eAAA,EAAA,IAAA,UAAA,EAAA;;AAC5B,SAAO,WAAW,MAAM,SAAC,GAAU;AAAK,WAAA,YAAG,OAAO,CAAC,KAAK,YAAG,MAAM,CAAC,KAAK,YAAG,YAAY,CAAC;EAA/C,CAAgD;AAC1F;AAEM,SAAUC,eAAc,MAAY,OAAa,OAAY;AACjE,MAAI,CAACC,YAAW,MAAM,KAAK,GAAG;AAC5B,WAAO;;AAGT,MAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,KAAK,GAAG;AACjC,WAAO,CAAC,KAAK,KAAKC,UAAS,KAAK,CAAC,KAAK,MAAM,KAAKA,UAAS,KAAK,CAAC;;AAIlE,MAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,WAAW,GAAG;AACvC,WACE,CAAC,OAAO,QAAQ,IAAI,EAAE,KAAKC,UAAS,KAAK,CAAC,KAAK,OAAO,QAAQ,KAAK,EAAE,KAAKA,UAAS,KAAK,CAAC;;AAI7F,SAAO,UAAU;AACnB;AAEM,SAAUC,gBACd,cACA,MACA,SAAmB;AAEX,MAAA,SAAgC,QAAO,QAA/B,MAAwB,QAAO,KAA1B,WAAmB,QAAO,UAAhB,OAAS,QAAO;AAC/C,MAAM,OAAOC,QAAO,cAAc,GAAG;AACrC,MAAM,QAAQA,QAAO,MAAM,GAAG;AAE9B,MAAI,UACF,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,MAAM,MAAM,SAAS,cAAc,OAAO,QAAQ,OAAO;AAElF,MAAI,CAAC,YAAG,UAAU,MAAM,GAAG;AACzB,cAAU,WAAW,UAAU;;AAGjC,MAAI,CAAC,YAAG,UAAU,QAAQ,GAAG;AAC3B,cAAU,WAAW,SAAS;;AAGhC,SAAO;AACT;AAEM,SAAUC,eACd,cACA,MACA,SAAgC;AAExB,MAAA,MAAqB,QAAO,KAAvB,OAAgB,QAAO,MAAjB,QAAU,QAAO;AAEpC,MAAM,OAAOD,QAAO,cAAc,GAAG;AACrC,MAAM,QAAQA,QAAO,MAAM,GAAG;AAC9B,MAAM,UAAU,SAAS,UAAU,OAAO;AAC1C,MAAM,YAAY,SAAS,UAAU,QAAQ;AAI7C,MAAI,CAAC,YAAG,gBAAgB,KAAK,GAAG;AAC9B,QAAI,YAAG,QAAQ,OAAO,GAAG;AAEvB,UAAI,YAAG,MAAM,OAAO,KAAK,YAAG,YAAY,OAAO,GAAG;AAChD,eAAOL,eAAc,SAAS,WAAW,KAAK;;WAE3C;AACL,aAAOO,OAAM,WAAW,KAAK;;AAG/B,WAAO;;AAGT,MAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,KAAK,GAAG;AACjC,WAAO,CAAC,UAAU,MAAMC,kBAAiB,OAAO,CAAC;;AAGnD,MAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,WAAW,GAAG;AACvC,WAAOC,cAAa,OAAO,KAAK,OAAO,GAAG,OAAO,KAAK,SAAS,CAAC;;AAGlE,SACE,CAAC,CAAC,MAAM,KAAK,EAAE,MAAM,SAAA,GAAC;AAAI,WAAA,YAAG,UAAU,CAAC,KAAK,YAAG,QAAQ,CAAC;EAA/B,CAAgC,MACzD,SAAS,UACN,CAAC,YAAG,QAAQ,IAAI,KAAK,YAAG,QAAQ,KAAK,IACrC,YAAG,QAAQ,IAAI,KAAK,CAAC,YAAG,QAAQ,KAAK;AAE7C;AAEM,SAAUC,cAAsB,cAAoB,MAAY,IAAwB;MAAxB,KAAA,OAAA,SAAsB,CAAA,IAAE,IAAtB,MAAG,GAAA;AACzE,MAAI,OAAOL,QAAO,cAAc,GAAG;AACnC,MAAI,QAAQA,QAAO,MAAM,GAAG;AAE5B,MAAI,CAACJ,YAAW,MAAM,KAAK,GAAG;AAC5B,UAAM,IAAI,UAAU,6BAA6B;;AAGnD,MAAI,CAACF,eAAc,MAAM,KAAK,GAAG;AAC/B,UAAM,IAAI,UAAU,0BAA0B;;AAGhD,MAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,WAAW,GAAG;AACvC,WAAO,OAAO,KAAK,IAAI;AACvB,YAAQ,OAAO,KAAK,KAAK;;AAG3B,SAAO,CAAC,MAAM,KAAK;AACrB;AAEM,SAAUI,UAAS,OAAY;AACnC,SAAO,SAAC,IAA6B;QAA5B,MAAG,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AACjB,QAAI,YAAG,MAAM,KAAK,GAAG;AACnB,aACEI,OAAM,OAAO,KAAK,KAClB,MAAM,KAAK,SAAA,GAAC;AAAI,eAAAA,OAAM,GAAG,KAAK,KAAM,YAAG,MAAM,KAAK,KAAKC,kBAAiB,KAAK,EAAE,CAAC;MAAhE,CAAkE;;AAKtF,QAAI,YAAG,YAAY,KAAK,KAAK,MAAM,GAAG,GAAG;AACvC,aAAO,CAAC,CAAC,MAAM,GAAG,KAAKD,OAAM,MAAM,GAAG,GAAG,KAAK;;AAGhD,WAAOA,OAAM,OAAO,KAAK;EAC3B;AACF;AAEM,SAAUE,cAAa,MAAgB,OAAe;AAC1D,SAAO,MAAM,KAAK,SAAA,GAAC;AAAI,WAAA,CAAC,KAAK,SAAS,CAAC;EAAhB,CAAiB;AAC1C;AAEM,SAAUP,UAAS,OAAY;AACnC,SAAO,SAAC,OAAY;AAClB,QAAI,YAAG,MAAM,KAAK,GAAG;AACnB,aAAO,MAAM,KAAK,SAAA,GAAC;AAAI,eAAAK,OAAM,GAAG,KAAK,KAAM,YAAG,MAAM,KAAK,KAAKC,kBAAiB,KAAK,EAAE,CAAC;MAAhE,CAAkE;;AAG3F,WAAOD,OAAM,OAAO,KAAK;EAC3B;AACF;AAEM,SAAUI,oBAAsB,eAAwB,OAAQ;AACpE,SAAO,YAAG,MAAM,aAAa,IACzB,cAAc,KAAK,SAAA,GAAC;AAAI,WAAAJ,OAAM,GAAG,KAAK;EAAd,CAAe,IACvCA,OAAM,eAAe,KAAK;AAChC;AAEM,SAAUC,kBAAiB,MAAe;AAC9C,SAAO,SAAC,OAAc;AAAK,WAAA,KAAK,KAAK,SAAA,GAAC;AAAI,aAAAD,OAAM,GAAG,KAAK;IAAd,CAAe;EAA9B;AAC7B;AAEM,SAAUN,cAAU;AAAC,MAAA,aAAA,CAAA;WAAA,KAAA,GAAA,KAAA,UAAA,QAAA,MAA2B;AAA3B,eAAA,EAAA,IAAA,UAAA,EAAA;;AACzB,SACE,WAAW,MAAM,YAAG,KAAK,KACzB,WAAW,MAAM,YAAG,MAAM,KAC1B,WAAW,MAAM,YAAG,WAAW,KAC/B,WAAW,MAAM,YAAG,MAAM;AAE9B;AAEM,SAAUI,QAAgC,MAAS,UAAY;AAEnE,MAAI,YAAG,YAAY,IAAI,KAAK,YAAG,MAAM,IAAI,GAAG;AAE1C,QAAI,YAAG,OAAO,QAAQ,GAAG;AACvB,UAAM,QAAoB,SAAS,MAAM,GAAG;AAE5C,aAAO,MAAM,OAAO,SAAC,KAAK,GAAC;AAAK,eAAA,OAAO,IAAI,CAAC;MAAZ,GAAe,IAAI;;AAIrD,QAAI,YAAG,OAAO,QAAQ,GAAG;AACvB,aAAO,KAAK,QAAQ;;AAGtB,WAAO;;AAGT,SAAO;AACT;;;AClLc,SAAPO,aACL,cACA,MAAO;AAEP,MAAI,CAAC,cAAc,IAAI,EAAE,KAAK,YAAG,eAAe,GAAG;AACjD,UAAM,IAAI,MAAM,6BAA6B;;AAG/C,MAAI,CAAC,CAAC,cAAc,IAAI,EAAE,MAAM,SAAA,GAAC;AAAI,WAAA,YAAG,YAAY,CAAC,KAAK,YAAG,MAAM,CAAC;EAA/B,CAAgC,GAAG;AACtE,UAAM,IAAI,MAAM,iCAAiC;;AAGnD,MAAM,QAAQ,SAAC,KAAS,OAAa;AACnC,QAAI;AACF,aAAOC,eAAiB,cAAc,MAAM,EAAE,KAAK,MAAM,SAAS,MAAK,CAAE;aACzE,IAAM;AAEN,aAAO;;EAEX;AAEA,MAAM,UAAU,SAAC,KAAkB,QAAgB,UAAgB;AACjE,QAAI;AACF,UAAM,OAAOC,QAAO,cAAc,GAAG;AACrC,UAAM,QAAQA,QAAO,MAAM,GAAG;AAC9B,UAAM,YAAY,YAAG,QAAQ,MAAM;AACnC,UAAM,cAAc,YAAG,QAAQ,QAAQ;AAEvC,UAAI,aAAa,aAAa;AAC5B,YAAM,iBAAiB,cACnBC,oBAAmB,UAAU,IAAI,IACjC,CAACA,oBAAmB,QAAQ,IAAI;AACpC,YAAM,kBAAkBA,oBAAmB,QAAQ,KAAK;AAExD,eAAO,kBAAkB;;AAG3B,UAAI,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,KAAK,KAAK,CAAC,MAAM,KAAK,EAAE,MAAM,YAAG,WAAW,GAAG;AACxE,eAAO,CAACC,OAAM,MAAM,KAAK;;AAG3B,aAAO,SAAS;aAChB,IAAM;AAEN,aAAO;;EAEX;AAEA,MAAM,cAAc,SAAC,KAAiB,UAAiB,QAAc;AACnE,QAAI,CAAC,YAAG,QAAQ,GAAG,GAAG;AACpB,aAAO;;AAGT,QAAI;AACF,UAAM,OAAOF,QAAO,cAAc,GAAG;AACrC,UAAM,QAAQA,QAAO,MAAM,GAAG;AAC9B,UAAM,YAAY,YAAG,QAAQ,MAAM;AAEnC,aACEC,oBAAmB,UAAU,IAAI,MAChC,YAAYA,oBAAmB,QAAQ,KAAK,IAAI,CAAC;aAEpD,IAAM;AAEN,aAAO;;EAEX;AAMA,MAAM,YAAY,SAAC,KAAiB,QAAa;AAC/C,QAAI,CAAC,YAAG,QAAQ,GAAG,GAAG;AACpB,aAAO;;AAIT,QAAI,MAAwC;AAE1C,cAAQ,KAAK,qDAAqD;;AAGpE,WAAO,QAAQ,KAAK,MAAM;EAC5B;AAEA,MAAM,YAAY,SAAC,KAAQ,QAAgB,UAAgB;AACzD,QAAI,CAAC,YAAG,QAAQ,GAAG,GAAG;AACpB,aAAO;;AAGT,QAAI;AACF,aAAOE,gBAAkB,cAAc,MAAM,EAAE,KAAK,QAAQ,UAAU,MAAM,YAAW,CAAE;aACzF,IAAM;AAEN,aAAO;;EAEX;AAEA,MAAM,UAAU,SAAC,KAAO;AACtB,QAAI;AACI,UAAA,KAAgBC,cAAa,cAAc,MAAM,EAAE,IAAG,CAAE,GAAvD,OAAI,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AAElB,aAAO,CAAC,CAAC,KAAK,UAAU,CAAC,MAAM;aAC/B,IAAM;AAEN,aAAO;;EAEX;AAEA,MAAM,SAAS,SAAC,KAAO;AACrB,QAAI;AACI,UAAA,KAAgBA,cAAa,cAAc,MAAM,EAAE,IAAG,CAAE,GAAvD,OAAI,GAAA,CAAA,GAAE,QAAK,GAAA,CAAA;AAElB,aAAO,CAAC,KAAK,UAAU,CAAC,CAAC,MAAM;aAC/B,IAAM;AAEN,aAAO;;EAEX;AAEA,MAAM,YAAY,SAAC,KAAQ,QAAgB,UAAgB;AACzD,QAAI,CAAC,YAAG,QAAQ,GAAG,GAAG;AACpB,aAAO;;AAGT,QAAI;AACF,aAAOD,gBAAkB,cAAc,MAAM,EAAE,KAAK,QAAQ,UAAU,MAAM,YAAW,CAAE;aACzF,IAAM;AAEN,aAAO;;EAEX;AAEA,MAAM,UAAU,SAAC,KAAS,OAAa;AACrC,QAAI;AACF,aAAOJ,eAAiB,cAAc,MAAM,EAAE,KAAK,MAAM,WAAW,MAAK,CAAE;aAC3E,IAAM;AAEN,aAAO;;EAEX;AAEA,SAAO,EAAE,OAAO,SAAS,aAAa,WAAW,WAAW,SAAS,QAAQ,WAAW,QAAO;AACjG;;;AlEhJA,uBAAqB;AAErB,SAAS,QAAQ,GAAG,GAAG;AACrB,MAAI,IAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAI,IAAI,OAAO,sBAAsB,CAAC;AACtC,UAAM,IAAI,EAAE,OAAO,SAAUM,IAAG;AAC9B,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAC/C,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,eAAe,GAAG;AACzB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAC/C,QAAI,IAAI,QAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUA,IAAG;AAClD,sBAAgB,GAAGA,IAAG,EAAEA,EAAC,CAAC;AAAA,IAC5B,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUA,IAAG;AAChJ,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,UAAU,aAAa;AAC9C,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;AACA,SAAS,kBAAkB,QAAQ,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,aAAa,MAAM,CAAC;AACxB,eAAW,aAAa,WAAW,cAAc;AACjD,eAAW,eAAe;AAC1B,QAAI,WAAW;AAAY,iBAAW,WAAW;AACjD,WAAO,eAAe,QAAQ,eAAe,WAAW,GAAG,GAAG,UAAU;AAAA,EAC1E;AACF;AACA,SAAS,aAAa,aAAa,YAAY,aAAa;AAC1D,MAAI;AAAY,sBAAkB,YAAY,WAAW,UAAU;AACnE,MAAI;AAAa,sBAAkB,aAAa,WAAW;AAC3D,SAAO,eAAe,aAAa,aAAa;AAAA,IAC9C,UAAU;AAAA,EACZ,CAAC;AACD,SAAO;AACT;AACA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,QAAM,eAAe,GAAG;AACxB,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,UAAU,UAAU,YAAY;AACvC,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAC3D,UAAM,IAAI,UAAU,oDAAoD;AAAA,EAC1E;AACA,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW;AAAA,IACrE,aAAa;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO,eAAe,UAAU,aAAa;AAAA,IAC3C,UAAU;AAAA,EACZ,CAAC;AACD,MAAI;AAAY,oBAAgB,UAAU,UAAU;AACtD;AACA,SAAS,gBAAgB,GAAG;AAC1B,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAASC,iBAAgBC,IAAG;AACnG,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAC/C;AACA,SAAO,gBAAgB,CAAC;AAC1B;AACA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,oBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAASC,iBAAgBD,IAAGE,IAAG;AACtG,IAAAF,GAAE,YAAYE;AACd,WAAOF;AAAA,EACT;AACA,SAAO,gBAAgB,GAAG,CAAC;AAC7B;AACA,SAAS,4BAA4B;AACnC,MAAI,OAAO,YAAY,eAAe,CAAC,QAAQ;AAAW,WAAO;AACjE,MAAI,QAAQ,UAAU;AAAM,WAAO;AACnC,MAAI,OAAO,UAAU;AAAY,WAAO;AACxC,MAAI;AACF,YAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAY;AAAA,IAAC,CAAC,CAAC;AAC7E,WAAO;AAAA,EACT,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AACA,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU;AAAM,WAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AACT,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,yBAAyB,QAAQ,UAAU;AAClD,MAAI,UAAU;AAAM,WAAO,CAAC;AAC5B,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAC3D,MAAI,KAAK;AACT,MAAI,OAAO,uBAAuB;AAChC,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAC1D,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,YAAM,iBAAiB,CAAC;AACxB,UAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAChC,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG;AAAG;AAC9D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,MAAM;AACpC,MAAI,SAAS,QAAQ;AACnB,UAAM,IAAI,eAAe,2DAA2D;AAAA,EACtF;AACA,SAAO;AACT;AACA,SAAS,2BAA2B,MAAM,MAAM;AAC9C,MAAI,SAAS,OAAO,SAAS,YAAY,OAAO,SAAS,aAAa;AACpE,WAAO;AAAA,EACT,WAAW,SAAS,QAAQ;AAC1B,UAAM,IAAI,UAAU,0DAA0D;AAAA,EAChF;AACA,SAAO,uBAAuB,IAAI;AACpC;AACA,SAAS,aAAa,SAAS;AAC7B,MAAI,4BAA4B,0BAA0B;AAC1D,SAAO,SAAS,uBAAuB;AACrC,QAAI,QAAQ,gBAAgB,OAAO,GACjC;AACF,QAAI,2BAA2B;AAC7B,UAAI,YAAY,gBAAgB,IAAI,EAAE;AACtC,eAAS,QAAQ,UAAU,OAAO,WAAW,SAAS;AAAA,IACxD,OAAO;AACL,eAAS,MAAM,MAAM,MAAM,SAAS;AAAA,IACtC;AACA,WAAO,2BAA2B,MAAM,MAAM;AAAA,EAChD;AACF;AACA,SAAS,aAAa,OAAO,MAAM;AACjC,MAAI,OAAO,UAAU,YAAY,UAAU;AAAM,WAAO;AACxD,MAAI,OAAO,MAAM,OAAO,WAAW;AACnC,MAAI,SAAS,QAAW;AACtB,QAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAC5C,QAAI,OAAO,QAAQ;AAAU,aAAO;AACpC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AACpD;AACA,SAAS,eAAe,KAAK;AAC3B,MAAI,MAAM,aAAa,KAAK,QAAQ;AACpC,SAAO,OAAO,QAAQ,WAAW,MAAM,OAAO,GAAG;AACnD;AAEA,IAAI,WAAW,EAAC,MAAK,EAAC,SAAQ,GAAE,GAAE,iBAAgB,EAAC,SAAQ,GAAE,EAAC;AAE9D,IAAI,8BAA4B;AAAgH,IAAI,4BAA0B;AAAmE,SAAS,eAAe,WAAU,OAAM,UAAS,eAAc;AAAC,MAAG,OAAO,cAAY,WAAU;AAAC,WAAO;AAAA,EAAU;AAAC,MAAG,OAAO,cAAY,YAAW;AAAC,WAAO,UAAU,OAAM,UAAS,aAAa;AAAA,EAAE;AAAC,MAAG,QAAQ,SAAS,MAAI,MAAK;AAAC,WAAO,QAAQ,SAAS;AAAA,EAAE;AAAC,SAAO;AAAM;AAAC,SAAS,WAAW,OAAM,UAAS;AAAC,SAAO,OAAO,eAAe,KAAK,OAAM,QAAQ;AAAE;AAAC,SAAS,iBAAiB,OAAM,UAAS,eAAc,SAAQ;AAAC,MAAG,SAAQ;AAAC,WAAO,IAAI,MAAM,OAAO;AAAA,EAAE;AAAC,SAAO,IAAI,MAAM,YAAY,OAAO,MAAM,QAAQ,GAAE,IAAI,EAAE,OAAO,UAAS,0BAA0B,EAAE,OAAO,eAAc,IAAI,CAAC;AAAE;AAAC,SAAS,4BAA4B,eAAc,SAAQ;AAAC,MAAG,OAAO,kBAAgB,YAAW;AAAC,UAAM,IAAI,UAAU,2BAA2B;AAAA,EAAE;AAAC,MAAG,QAAQ,OAAO,KAAG,OAAO,YAAU,UAAS;AAAC,UAAM,IAAI,UAAU,yBAAyB;AAAA,EAAE;AAAC;AAAC,SAAS,aAAa,eAAc,WAAU,SAAQ;AAAC,8BAA4B,eAAc,OAAO;AAAE,SAAO,SAAS,OAAM,UAAS,eAAc;AAAC,aAAQ,OAAK,UAAU,QAAO,OAAK,IAAI,MAAM,OAAK,IAAE,OAAK,IAAE,CAAC,GAAE,OAAK,GAAE,OAAK,MAAK,QAAO;AAAC,WAAK,OAAK,CAAC,IAAE,UAAU,IAAI;AAAA,IAAE;AAAC,QAAG,eAAe,WAAU,OAAM,UAAS,aAAa,GAAE;AAAC,UAAG,WAAW,OAAM,QAAQ,GAAE;AAAC,eAAO,cAAc,MAAM,QAAO,CAAC,OAAM,UAAS,aAAa,EAAE,OAAO,IAAI,CAAC;AAAA,MAAE;AAAC,aAAO,iBAAiB,OAAM,UAAS,eAAc,OAAO;AAAA,IAAE;AACxiD,WAAO,cAAc,MAAM,QAAO,CAAC,OAAM,UAAS,aAAa,EAAE,OAAO,IAAI,CAAC;AAAA,EAAE;AAAE;AAEjF,IAAI,SAAS,EAAC,MAAK,QAAO,MAAK,QAAO,SAAQ,WAAU,MAAK,QAAO,SAAQ,WAAU,OAAM,QAAO;AAEnG,IAAI,YAAU,iBAAAG,QAAS,iBAAe;AAAU,SAAS,YAAW;AAAC,SAAO,CAAC,EAAE,OAAO,WAAS,eAAa,OAAO,YAAU,OAAO,SAAS;AAAe;AAAC,SAAS,WAAU;AAAC,SAAO,kBAAiB,UAAQ,OAAO,KAAK,UAAU,SAAS;AAAE;AAS/O,SAAS,IAAI,MAAK;AAAC,MAAI,QAAM,KAAK,OAAM,OAAK,KAAK,MAAK,YAAU,KAAK,MAAK,OAAK,cAAY,SAAO,QAAM,WAAU,aAAW,KAAK,OAAM,QAAM,eAAa,SAAO,QAAM;AAA0C,MAAI,QAAM,OAAK,QAAQ,QAAM,QAAQ,QAAM,QAAQ;AAAI,MAAG,SAAO,SAAO,MAAK;AAAC,YAAQ,eAAe,oBAAoB,OAAO,KAAK,GAAE,qDAAqD;AAAE,QAAG,MAAM,QAAQ,IAAI,GAAE;AAAC,WAAK,QAAQ,SAAS,GAAE;AAAC,YAAG,YAAG,YAAY,CAAC,KAAG,EAAE,KAAI;AAAC,gBAAM,MAAM,SAAQ,CAAC,EAAE,KAAI,EAAE,KAAK,CAAC;AAAA,QAAE,OAAM;AAAC,gBAAM,MAAM,SAAQ,CAAC,CAAC,CAAC;AAAA,QAAE;AAAA,MAAC,CAAC;AAAA,IAAE,OAAM;AAAC,YAAM,MAAM,SAAQ,CAAC,IAAI,CAAC;AAAA,IAAE;AAAC,YAAQ,SAAS;AAAA,EAAE;AAAoB;AAAC,SAAS,GAAG,SAAQ,OAAM,IAAG;AAAC,MAAI,UAAQ,UAAU,SAAO,KAAG,UAAU,CAAC,MAAI,SAAU,UAAU,CAAC,IAAE;AAAM,UAAQ,iBAAiB,OAAM,IAAG,OAAO;AAAE;AAAC,SAAS,IAAI,SAAQ,OAAM,IAAG;AAAC,MAAI,UAAQ,UAAU,SAAO,KAAG,UAAU,CAAC,MAAI,SAAU,UAAU,CAAC,IAAE;AAAM,UAAQ,oBAAoB,OAAM,IAAG,OAAO;AAAE;AAAC,SAAS,KAAK,SAAQ,OAAM,IAAG;AAAC,MAAI,UAAQ,UAAU,SAAO,KAAG,UAAU,CAAC,MAAI,SAAU,UAAU,CAAC,IAAE;AAAM,MAAI;AACvhC,YAAQ,SAAS,OAAO,GAAE;AAAC,OAAG,CAAC;AAAE,QAAI,SAAQ,OAAM,OAAO;AAAA,EAAE;AAAE,KAAG,SAAQ,OAAM,SAAQ,OAAO;AAAE;AAAC,SAAS,OAAM;AAAC;AAEjH,IAAI,qBAAgC,SAAS,kBAAiB;AAAC,YAAUC,qBAAmB,gBAAgB;AAAE,MAAI,SAAO,aAAaA,mBAAkB;AAAE,WAASA,sBAAoB;AAAC,oBAAgB,MAAKA,mBAAkB;AAAE,WAAO,OAAO,MAAM,MAAK,SAAS;AAAA,EAAE;AAAC,eAAaA,qBAAmB,CAAC,EAAC,KAAI,qBAAoB,OAAM,SAAS,oBAAmB;AAAC,QAAG,CAAC,UAAU;AAAE;AAAO,QAAG,CAAC,KAAK,MAAK;AAAC,WAAK,WAAW;AAAA,IAAE;AAAC,QAAG,CAAC,WAAU;AAAC,WAAK,aAAa;AAAA,IAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,sBAAqB,OAAM,SAAS,qBAAoB;AAAC,QAAG,CAAC,UAAU;AAAE;AAAO,QAAG,CAAC,WAAU;AAAC,WAAK,aAAa;AAAA,IAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,wBAAuB,OAAM,SAAS,uBAAsB;AAAC,QAAG,CAAC,UAAU,KAAG,CAAC,KAAK;AAAK;AAAO,QAAG,CAAC,WAAU;AAAC,uBAAAD,QAAS,uBAAuB,KAAK,IAAI;AAAA,IAAE;AAAC,QAAG,KAAK,QAAM,KAAK,KAAK,eAAa,SAAS,MAAK;AAAC,eAAS,KAAK,YAAY,KAAK,IAAI;AAAE,WAAK,OAAK;AAAA,IAAU;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,SAAS,aAAY;AAAC,QAAI,cAAY,KAAK,OAAM,KAAG,YAAY,IAAG,SAAO,YAAY;AAAO,QAAG,CAAC,KAAK,MAAK;AAAC,WAAK,OAAK,SAAS,cAAc,KAAK;AAA4B,UAAG,IAAG;AAAC,aAAK,KAAK,KAAG;AAAA,MAAG;AAAC,UAAG,QAAO;AAAC,aAAK,KAAK,MAAM,SAAO;AAAA,MAAO;AAAC,eAAS,KAAK,YAAY,KAAK,IAAI;AAAA,IAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,gBAAe,OAAM,SAAS,eAAc;AAAC,QAAG,CAAC,UAAU;AAAE,aAAO;AAAK,QAAI,eAAa,KAAK,OAAM,WAAS,aAAa,UAAS,SAAO,aAAa;AAAO,QAAG,CAAC,KAAK,MAAK;AAAC,WAAK,WAAW;AAAA,IAAE;AAA2B,QAAG,WAAU;AAAC,aAAmB,iBAAAA,QAAS,aAAa,UAAS,KAAK,IAAI;AAAA,IAAE;AAAC,QAAI,SAAO,iBAAAA,QAAS,oCAAoC,MAAK,SAAS,SAAO,IAAe,aAAAE,QAAM,cAAc,OAAM,MAAK,QAAQ,IAAE,SAAS,CAAC,GAAE,KAAK,IAAI;AAAE,WAAO,MAAM;AAAE,WAAO;AAAA,EAAK,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAAS,gBAAe;AAAC,QAAI,eAAa,KAAK,OAAM,cAAY,aAAa,aAAY,YAAU,aAAa,WAAU,SAAO,aAAa;AAAO,QAAG,CAAC,aAAY;AAAC,UAAG,UAAQ,cAAY,UAAS;AAAC,eAAO,KAAK,aAAa;AAAA,MAAE;AAAC,aAAO;AAAA,IAAK;AAAC,WAAO,KAAK,aAAa;AAAA,EAAE,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAAS,SAAQ;AAAC,QAAG,CAAC,WAAU;AAAC,aAAO;AAAA,IAAK;AAAC,WAAO,KAAK,cAAc;AAAA,EAAE,EAAC,CAAC,CAAC;AAAE,SAAOD;AAAmB,EAAE,aAAAC,QAAM,SAAS;AAAE,gBAAgB,oBAAmB,aAAY,EAAC,UAAS,kBAAAC,QAAU,UAAU,CAAC,kBAAAA,QAAU,SAAQ,kBAAAA,QAAU,KAAK,CAAC,GAAE,aAAY,kBAAAA,QAAU,MAAK,IAAG,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAO,kBAAAA,QAAU,MAAM,CAAC,GAAE,WAAU,kBAAAA,QAAU,QAAO,QAAO,kBAAAA,QAAU,KAAK,YAAW,QAAO,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAO,kBAAAA,QAAU,MAAM,CAAC,GAAE,QAAO,kBAAAA,QAAU,OAAM,CAAC;AAE93E,IAAI,eAA0B,SAAS,kBAAiB;AAAC,YAAUC,eAAa,gBAAgB;AAAE,MAAI,SAAO,aAAaA,aAAY;AAAE,WAASA,gBAAc;AAAC,oBAAgB,MAAKA,aAAY;AAAE,WAAO,OAAO,MAAM,MAAK,SAAS;AAAA,EAAE;AAAC,eAAaA,eAAa,CAAC,EAAC,KAAI,eAAc,KAAI,SAAS,MAAK;AAAC,QAAI,cAAY,KAAK,OAAM,YAAU,YAAY,WAAU,SAAO,YAAY;AAAO,QAAI,SAAO,OAAO,MAAM;AAAO,QAAIC,SAAM,EAAC,eAAc,QAAO,UAAS,YAAW,OAAM,OAAM;AAA4B,QAAG,UAAU,WAAW,KAAK,GAAE;AAAC,MAAAA,OAAM,SAAO;AAAE,MAAAA,OAAM,OAAK;AAAE,MAAAA,OAAM,QAAM;AAAE,MAAAA,OAAM,SAAO;AAAA,IAAO,WAAS,UAAU,WAAW,QAAQ,GAAE;AAAC,MAAAA,OAAM,OAAK;AAAE,MAAAA,OAAM,QAAM;AAAE,MAAAA,OAAM,MAAI;AAAE,MAAAA,OAAM,SAAO;AAAA,IAAO,WAAS,UAAU,WAAW,MAAM,GAAE;AAAC,MAAAA,OAAM,QAAM;AAAE,MAAAA,OAAM,MAAI;AAAE,MAAAA,OAAM,SAAO;AAAA,IAAE,WAAS,UAAU,WAAW,OAAO,GAAE;AAAC,MAAAA,OAAM,OAAK;AAAE,MAAAA,OAAM,MAAI;AAAA,IAAE;AAAC,WAAOA;AAAA,EAAM,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAAS,SAAQ;AAAC,QAAI,eAAa,KAAK,OAAM,YAAU,aAAa,WAAU,cAAY,aAAa,aAAY,SAAO,aAAa;AAAO,QAAI,gBAAc,OAAO,OAAM,QAAM,cAAc,OAAM,UAAQ,cAAc,SAAQ,SAAO,cAAc,QAAO,SAAO,cAAc,QAAO,WAAS,cAAc,UAAS,SAAO,cAAc;AAAO,QAAI,cAAY,EAAC,SAAgB,SAAiB;AAAE,QAAI;AAAO,QAAI,IAAE;AAAO,QAAI,IAAE;AAAiC,QAAG,UAAU,WAAW,KAAK,GAAE;AAAC,eAAO,OAAO,OAAO,IAAE,GAAE,GAAG,EAAE,OAAO,GAAE,GAAG,EAAE,OAAO,GAAE,IAAI;AAAE,kBAAY,SAAO;AAAE,kBAAY,aAAW;AAAO,kBAAY,cAAY;AAAA,IAAO,WAAS,UAAU,WAAW,QAAQ,GAAE;AAAC,eAAO,GAAG,OAAO,GAAE,GAAG,EAAE,OAAO,GAAE,GAAG,EAAE,OAAO,IAAE,GAAE,OAAO,EAAE,OAAO,CAAC;AAAE,kBAAY,MAAI;AAAE,kBAAY,aAAW;AAAO,kBAAY,cAAY;AAAA,IAAO,WAAS,UAAU,WAAW,MAAM,GAAE;AAAC,UAAE;AAAO,UAAE;AAAO,eAAO,OAAO,OAAO,GAAE,GAAG,EAAE,OAAO,IAAE,GAAE,KAAK,EAAE,OAAO,CAAC;AAAE,kBAAY,QAAM;AAAE,kBAAY,YAAU;AAAO,kBAAY,eAAa;AAAA,IAAO,WAAS,UAAU,WAAW,OAAO,GAAE;AAAC,UAAE;AAAO,UAAE;AAAO,eAAO,GAAG,OAAO,GAAE,GAAG,EAAE,OAAO,GAAE,GAAG,EAAE,OAAO,GAAE,OAAO,EAAE,OAAO,IAAE,CAAC;AAAE,kBAAY,OAAK;AAAE,kBAAY,YAAU;AAAO,kBAAY,eAAa;AAAA,IAAO;AAAC,WAAmB,aAAAH,QAAM,cAAc,OAAM,EAAC,WAAU,oBAAmB,OAAM,KAAK,YAAW,GAAe,aAAAA,QAAM,cAAc,QAAO,EAAC,KAAI,aAAY,OAAM,YAAW,GAAe,aAAAA,QAAM,cAAc,OAAM,EAAC,OAAM,GAAE,QAAO,GAAE,SAAQ,OAAM,OAAM,6BAA4B,GAAe,aAAAA,QAAM,cAAc,WAAU,EAAC,QAAc,MAAK,MAAK,CAAC,CAAC,CAAC,CAAC;AAAA,EAAE,EAAC,CAAC,CAAC;AAAE,SAAOE;AAAa,EAAE,aAAAF,QAAM,SAAS;AAAE,gBAAgB,cAAa,aAAY,EAAC,WAAU,kBAAAC,QAAU,OAAO,YAAW,aAAY,kBAAAA,QAAU,KAAK,YAAW,QAAO,kBAAAA,QAAU,OAAO,WAAU,CAAC;AAEjmF,IAAI,cAAY,CAAC,SAAQ,UAAS,OAAO;AAAE,SAAS,gBAAgB,MAAK;AAAC,MAAI,cAAY,KAAK,aAAY,SAAO,KAAK;AAAO,MAAI,QAAM,OAAO,OAAM,SAAO,OAAO,QAAO,QAAM,OAAO,OAAM,QAAM,yBAAyB,QAAO,WAAW;AAAE,SAAmB,aAAAD,QAAM,cAAc,UAAS,EAAC,cAAa,SAAQ,SAAQ,aAAY,OAAY,MAAK,SAAQ,GAAe,aAAAA,QAAM,cAAc,OAAM,EAAC,OAAM,GAAG,OAAO,OAAM,IAAI,GAAE,QAAO,GAAG,OAAO,QAAO,IAAI,GAAE,SAAQ,aAAY,SAAQ,OAAM,OAAM,8BAA6B,qBAAoB,WAAU,GAAe,aAAAA,QAAM,cAAc,KAAI,MAAkB,aAAAA,QAAM,cAAc,QAAO,EAAC,GAAE,g7BAA+6B,MAAK,MAAK,CAAC,CAAC,CAAC,CAAC;AAAE;AAAC,gBAAgB,YAAU,EAAC,aAAY,kBAAAC,QAAU,KAAK,YAAW,QAAO,kBAAAA,QAAU,OAAO,WAAU;AAEpqD,SAAS,iBAAiB,MAAK;AAAC,MAAI,UAAQ,KAAK,SAAQ,SAAO,KAAK,QAAO,cAAY,KAAK,aAAY,OAAK,KAAK,MAAK,kBAAgB,KAAK,iBAAgB,kBAAgB,KAAK,iBAAgB,QAAM,KAAK,OAAM,SAAO,KAAK;AAAO,MAAI,SAAO,EAAC,SAAqB,aAAAD,QAAM,eAAe,OAAO,IAAE,UAAqB,aAAAA,QAAM,cAAc,OAAM,EAAC,WAAU,sBAAqB,OAAM,OAAO,QAAO,GAAE,OAAO,EAAC;AAAE,MAAG,OAAM;AAAC,WAAO,QAAmB,aAAAA,QAAM,eAAe,KAAK,IAAE,QAAmB,aAAAA,QAAM,cAAc,OAAM,EAAC,WAAU,oBAAmB,OAAM,OAAO,MAAK,GAAE,KAAK;AAAA,EAAE;AAAC,MAAG,QAAO;AAAC,WAAO,SAAoB,aAAAA,QAAM,eAAe,MAAM,IAAE,SAAoB,aAAAA,QAAM,cAAc,OAAM,EAAC,WAAU,qBAAoB,OAAM,OAAO,OAAM,GAAE,MAAM;AAAA,EAAE;AAAC,OAAI,mBAAiB,oBAAkB,CAAC,YAAG,SAAS,EAAE,IAAI,GAAE;AAAC,WAAO,QAAmB,aAAAA,QAAM,cAAc,iBAAgB,EAAC,QAAO,OAAO,OAAM,YAAuB,CAAC;AAAA,EAAE;AAAC,SAAmB,aAAAA,QAAM,cAAc,OAAM,EAAC,WAAU,wBAAuB,OAAM,OAAO,UAAS,GAAE,OAAO,OAAM,OAAO,OAAM,OAAO,SAAQ,OAAO,MAAM;AAAE;AAAC,iBAAiB,YAAU,EAAC,SAAQ,kBAAAC,QAAU,KAAK,YAAW,QAAO,kBAAAA,QAAU,MAAK,aAAY,kBAAAA,QAAU,KAAK,YAAW,MAAK,kBAAAA,QAAU,MAAK,iBAAgB,kBAAAA,QAAU,KAAK,YAAW,iBAAgB,kBAAAA,QAAU,KAAK,YAAW,QAAO,kBAAAA,QAAU,OAAO,YAAW,OAAM,kBAAAA,QAAU,KAAI;AAE70C,IAAI,UAAqB,SAAS,kBAAiB;AAAC,YAAUG,UAAQ,gBAAgB;AAAE,MAAI,SAAO,aAAaA,QAAO;AAAE,WAASA,WAAS;AAAC,oBAAgB,MAAKA,QAAO;AAAE,WAAO,OAAO,MAAM,MAAK,SAAS;AAAA,EAAE;AAAC,eAAaA,UAAQ,CAAC,EAAC,KAAI,SAAQ,KAAI,SAAS,MAAK;AAAC,QAAI,cAAY,KAAK,OAAM,mBAAiB,YAAY,kBAAiB,YAAU,YAAY,WAAU,YAAU,YAAY,WAAU,YAAU,YAAY,WAAU,SAAO,YAAY,QAAO,SAAO,YAAY;AAAO,QAAI,SAAO,OAAO,MAAM,QAAO,UAAQ,OAAO,SAAQ,kBAAgB,OAAO,iBAAgB,iBAAe,OAAO,gBAAe,iBAAe,OAAO,gBAAe,uBAAqB,OAAO,sBAAqB,uBAAqB,OAAO;AAAqB,QAAI,UAAQ,CAAC;AAAE,QAAG,CAAC,WAAU;AAAC,UAAG,UAAU,WAAW,KAAK,GAAE;AAAC,gBAAQ,UAAQ,OAAO,OAAO,QAAO,IAAI;AAAA,MAAE,WAAS,UAAU,WAAW,QAAQ,GAAE;AAAC,gBAAQ,UAAQ,GAAG,OAAO,QAAO,QAAQ;AAAA,MAAE,WAAS,UAAU,WAAW,MAAM,GAAE;AAAC,gBAAQ,UAAQ,KAAK,OAAO,QAAO,QAAQ;AAAA,MAAE,WAAS,UAAU,WAAW,OAAO,GAAE;AAAC,gBAAQ,UAAQ,SAAS,OAAO,QAAO,IAAI;AAAA,MAAE;AAAA,IAAC;AAAC,QAAG,CAAC,OAAO,SAAQ,OAAO,IAAI,EAAE,QAAQ,MAAM,MAAI,IAAG;AAAC,gBAAQ,eAAe,eAAe,CAAC,GAAE,OAAO,GAAE,cAAc;AAAA,IAAE;AAAC,QAAG,WAAS,OAAO,SAAQ;AAAC,gBAAQ,eAAe,eAAe,CAAC,GAAE,OAAO,GAAE,cAAc;AAAA,IAAE;AAAC,QAAG,WAAS,OAAO,QAAM,CAAC,kBAAiB;AAAC,gBAAQ,eAAe,eAAe,CAAC,GAAE,OAAO,GAAE,oBAAoB;AAAA,IAAE;AAAC,QAAG,cAAY,UAAS;AAAC,gBAAQ,eAAe,eAAe,CAAC,GAAE,OAAO,GAAE,eAAe;AAAA,IAAE;AAAC,QAAG,WAAU;AAAC,gBAAQ,eAAe,eAAe,CAAC,GAAE,OAAO,GAAE,oBAAoB;AAAA,IAAE;AAAC,WAAO,eAAe,eAAe,CAAC,GAAE,OAAO,GAAE,OAAO;AAAA,EAAE,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAAS,SAAQ;AAAC,QAAI,eAAa,KAAK,OAAM,YAAU,aAAa,WAAU,UAAQ,aAAa,aAAY,YAAU,aAAa,WAAU,gBAAc,aAAa,eAAc,SAAO,aAAa;AAAO,QAAI,SAAO,CAAC;AAAE,QAAI,UAAQ,CAAC,WAAW;AAAE,QAAG,WAAU;AAAC,UAAgB,aAAAJ,QAAM,eAAe,SAAS,GAAE;AAAC,eAAO,UAAqB,aAAAA,QAAM,aAAa,WAAU,EAAC,QAAe,CAAC;AAAA,MAAE,OAAM;AAAC,eAAO,UAAQ,UAAU,EAAC,QAAe,CAAC;AAAA,MAAE;AAAA,IAAC,OAAM;AAAC,aAAO,UAAqB,aAAAA,QAAM,cAAc,kBAAiB,KAAK,KAAK;AAAA,IAAE;AAAC,QAAG,WAAS,OAAO,MAAK;AAAC,cAAQ,KAAK,iBAAiB;AAAA,IAAE;AAAC,QAAG,CAAC,WAAU;AAAC,aAAO,QAAmB,aAAAA,QAAM,cAAc,cAAa,KAAK,KAAK;AAAA,IAAE;AAAC,WAAmB,aAAAA,QAAM,cAAc,OAAM,EAAC,KAAI,eAAc,WAAU,QAAQ,KAAK,GAAG,GAAE,OAAM,KAAK,MAAK,GAAe,aAAAA,QAAM,cAAc,OAAM,EAAC,WAAU,kBAAiB,GAAE,OAAO,SAAQ,OAAO,KAAK,CAAC;AAAA,EAAE,EAAC,CAAC,CAAC;AAAE,SAAOI;AAAQ,EAAE,aAAAJ,QAAM,SAAS;AAAE,gBAAgB,SAAQ,aAAY,EAAC,WAAU,kBAAAC,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAK,kBAAAA,QAAU,OAAO,CAAC,GAAE,SAAQ,kBAAAA,QAAU,MAAK,kBAAiB,kBAAAA,QAAU,KAAK,YAAW,QAAO,kBAAAA,QAAU,MAAK,aAAY,kBAAAA,QAAU,KAAK,YAAW,WAAU,kBAAAA,QAAU,KAAK,YAAW,MAAK,kBAAAA,QAAU,MAAK,WAAU,kBAAAA,QAAU,OAAO,YAAW,iBAAgB,kBAAAA,QAAU,KAAK,YAAW,aAAY,kBAAAA,QAAU,KAAK,YAAW,eAAc,kBAAAA,QAAU,KAAK,YAAW,iBAAgB,kBAAAA,QAAU,MAAK,QAAO,kBAAAA,QAAU,OAAO,YAAW,QAAO,kBAAAA,QAAU,OAAO,YAAW,OAAM,kBAAAA,QAAU,KAAI,CAAC;AAEpnG,IAAI,sBAAiC,SAAS,kBAAiB;AAAC,YAAUI,sBAAoB,gBAAgB;AAAE,MAAI,SAAO,aAAaA,oBAAmB;AAAE,WAASA,uBAAqB;AAAC,oBAAgB,MAAKA,oBAAmB;AAAE,WAAO,OAAO,MAAM,MAAK,SAAS;AAAA,EAAE;AAAC,eAAaA,sBAAoB,CAAC,EAAC,KAAI,UAAS,OAAM,SAAS,SAAQ;AAAC,QAAI,cAAY,KAAK,OAAM,WAAS,YAAY,UAAS,cAAY,YAAY,aAAY,mBAAiB,YAAY,kBAAiB,mBAAiB,YAAY,kBAAiB,cAAY,YAAY,aAAY,gBAAc,YAAY,eAAc,QAAM,YAAY,OAAM,SAAO,YAAY;AAAO,QAAI;AAAkC,QAAG,UAAS;AAAC,UAAG,aAAAL,QAAM,SAAS,MAAM,QAAQ,MAAI,GAAE;AAAC,YAAG,CAAc,aAAAA,QAAM,eAAe,QAAQ,GAAE;AAAC,oBAAqB,aAAAA,QAAM,cAAc,QAAO,MAAK,QAAQ;AAAA,QAAE,OAAM;AAAC,cAAI,UAAQ,YAAG,UAAU,EAAE,SAAS,IAAI,IAAE,aAAW;AAAM,oBAAqB,aAAAA,QAAM,aAAa,aAAAA,QAAM,SAAS,KAAK,QAAQ,GAAE,gBAAgB,CAAC,GAAE,SAAQ,WAAW,CAAC;AAAA,QAAE;AAAA,MAAC,OAAM;AAAC,kBAAQ;AAAA,MAAS;AAAA,IAAC;AAAC,QAAG,CAAC,SAAQ;AAAC,aAAO;AAAA,IAAK;AAAC,WAAmB,aAAAA,QAAM,cAAc,QAAO,EAAC,KAAI,eAAc,OAAM,eAAe,eAAe,CAAC,GAAE,MAAM,GAAE,KAAK,GAAE,SAAQ,aAAY,cAAa,kBAAiB,cAAa,iBAAgB,GAAE,OAAO;AAAA,EAAE,EAAC,CAAC,CAAC;AAAE,SAAOK;AAAoB,EAAE,aAAAL,QAAM,SAAS;AAAE,gBAAgB,qBAAoB,aAAY,EAAC,UAAS,kBAAAC,QAAU,MAAK,aAAY,kBAAAA,QAAU,KAAK,YAAW,kBAAiB,kBAAAA,QAAU,KAAK,YAAW,kBAAiB,kBAAAA,QAAU,KAAK,YAAW,aAAY,kBAAAA,QAAU,KAAK,YAAW,eAAc,kBAAAA,QAAU,KAAK,YAAW,OAAM,kBAAAA,QAAU,QAAO,QAAO,kBAAAA,QAAU,OAAO,WAAU,CAAC;AAE3oD,IAAI,iBAAe,EAAC,QAAO,IAAG;AAAE,SAAS,UAAU,QAAO;AAAC,MAAI,cAAQ,iBAAAK,SAAU,gBAAe,OAAO,WAAS,CAAC,CAAC;AAAE,SAAO,EAAC,SAAQ,EAAC,QAAO,QAAO,SAAQ,eAAc,eAAc,UAAS,QAAO,QAAQ,OAAM,GAAE,iBAAgB,EAAC,MAAK,MAAM,UAAS,YAAW,KAAI,MAAM,YAAW,SAAQ,GAAE,SAAQ,EAAC,SAAQ,gBAAe,QAAO,2CAA0C,UAAS,KAAI,SAAQ,GAAE,UAAS,YAAW,YAAW,gBAAe,YAAW,UAAS,QAAO,QAAQ,OAAM,GAAE,gBAAe,EAAC,SAAQ,GAAE,YAAW,UAAS,GAAE,sBAAqB,EAAC,SAAQ,GAAE,YAAW,gCAA+B,YAAW,UAAS,GAAE,sBAAqB,EAAC,UAAS,OAAM,GAAE,gBAAe,EAAC,SAAQ,GAAE,YAAW,UAAS,GAAE,iBAAgB,EAAC,MAAK,OAAM,UAAS,SAAQ,KAAI,OAAM,WAAU,wBAAuB,GAAE,WAAU,EAAC,iBAAgB,QAAO,OAAM,QAAO,WAAU,IAAG,UAAS,KAAI,SAAQ,IAAG,UAAS,YAAW,QAAO,GAAE,GAAE,OAAM,EAAC,cAAa,kBAAiB,OAAM,QAAO,UAAS,IAAG,cAAa,GAAE,eAAc,GAAE,cAAa,GAAE,GAAE,SAAQ,EAAC,UAAS,GAAE,GAAE,OAAM,EAAC,iBAAgB,eAAc,QAAO,GAAE,cAAa,GAAE,OAAM,QAAO,UAAS,GAAE,QAAO,IAAG,SAAQ,QAAO,SAAQ,IAAG,UAAS,YAAW,OAAM,GAAE,KAAI,GAAE,OAAM,IAAG,kBAAiB,OAAM,GAAE,QAAO,EAAC,WAAU,kBAAiB,UAAS,IAAG,WAAU,IAAG,YAAW,EAAC,GAAE,OAAM,EAAC,OAAM,QAAO,SAAQ,eAAc,QAAO,IAAG,QAAO,GAAE,UAAS,YAAW,QAAO,GAAE,GAAE,QAAe;AAAE;AAEn5C,IAAI,YAAU,CAAC,SAAQ,QAAO,QAAQ;AAAE,IAAI,oBAAkB,CAAC,YAAW,OAAM,SAAQ,UAAS,MAAM;AAAE,IAAI,eAA0B,SAAS,kBAAiB;AAAC,YAAUC,eAAa,gBAAgB;AAAE,MAAI,SAAO,aAAaA,aAAY;AAAE,WAASA,cAAa,OAAM;AAAC,QAAI;AAAM,oBAAgB,MAAKA,aAAY;AAAE,YAAM,OAAO,KAAK,MAAK,KAAK;AAA4B,oBAAgB,uBAAuB,KAAK,GAAE,eAAc,SAAS,KAAI;AAAC,YAAM,WAAS;AAAA,IAAI,CAAC;AAAE,oBAAgB,uBAAuB,KAAK,GAAE,eAAc,SAAS,KAAI;AAAC,YAAM,WAAS;AAAA,IAAI,CAAC;AAAE,oBAAgB,uBAAuB,KAAK,GAAE,iBAAgB,SAAS,KAAI;AAAC,YAAM,aAAW;AAAA,IAAI,CAAC;AAAE,oBAAgB,uBAAuB,KAAK,GAAE,iBAAgB,SAAS,KAAI;AAAC,YAAM,aAAW;AAAA,IAAI,CAAC;AAAE,oBAAgB,uBAAuB,KAAK,GAAE,uBAAsB,WAAU;AAAC,UAAI,SAAO,MAAM,MAAM;AAAO,UAAI,WAAS,MAAM,MAAM;AAAmC,UAAG,MAAM,eAAc;AAAC,cAAM,cAAc,SAAS,OAAO;AAAA,MAAE;AAAC,YAAM,SAAS,EAAC,QAAO,WAAS,OAAO,UAAQ,OAAO,OAAK,OAAO,KAAI,GAAE,WAAU;AAAC,YAAI,YAAU,MAAM,MAAM;AAAO,iBAAS,cAAY,OAAO,OAAK,SAAO,SAAQ,MAAM,KAAK;AAAA,MAAE,CAAC;AAAA,IAAE,CAAC;AAAE,oBAAgB,uBAAuB,KAAK,GAAE,eAAc,WAAU;AAAC,UAAI,cAAY,MAAM,OAAM,QAAM,YAAY,OAAM,OAAK,YAAY;AAAK,UAAG,YAAG,SAAS,EAAE,IAAI;AAAE;AAAO,UAAI,cAAY,MAAM,OAAM,kBAAgB,YAAY,iBAAgB,SAAO,YAAY;AAAiC,UAAG,MAAM,UAAQ,WAAS,MAAM,UAAQ,WAAS,iBAAgB;AAAC,YAAI,EAAC,OAAM,SAAQ,MAAK,CAAC,EAAC,OAAY,QAAO,WAAS,OAAO,OAAK,YAAU,UAAS,CAAC,GAAE,OAAM,MAAM,MAAK,CAAC;AAAE,cAAM,OAAO;AAAA,MAAE;AAAA,IAAC,CAAC;AAAE,oBAAgB,uBAAuB,KAAK,GAAE,oBAAmB,WAAU;AAAC,UAAI,eAAa,MAAM,OAAM,QAAM,aAAa,OAAM,OAAK,aAAa;AAAK,UAAG,YAAG,SAAS,EAAE,IAAI,KAAG,SAAS;AAAE;AAAO,UAAI,SAAO,MAAM,MAAM;AAAiC,UAAG,MAAM,UAAQ,WAAS,WAAS,OAAO,MAAK;AAAC,YAAI,EAAC,OAAM,cAAa,MAAK,CAAC,EAAC,KAAI,iBAAgB,OAAM,MAAK,CAAC,GAAE,OAAM,MAAM,MAAK,CAAC;AAAE,qBAAa,MAAM,iBAAiB;AAAE,cAAM,OAAO;AAAA,MAAE;AAAA,IAAC,CAAC;AAAE,oBAAgB,uBAAuB,KAAK,GAAE,oBAAmB,WAAU;AAAC,UAAI,eAAa,MAAM,OAAM,QAAM,aAAa,OAAM,aAAW,aAAa,YAAW,OAAK,aAAa;AAAK,UAAG,YAAG,SAAS,EAAE,IAAI,KAAG,SAAS;AAAE;AAAO,UAAI,eAAa,MAAM,OAAM,SAAO,aAAa,QAAO,kBAAgB,aAAa;AAA0C,UAAG,MAAM,UAAQ,SAAQ;AAAC,YAAI,EAAC,OAAM,cAAa,MAAK,CAAC,EAAC,KAAI,iBAAgB,OAAM,MAAK,CAAC,GAAE,OAAM,MAAM,MAAK,CAAC;AAAE,YAAG,CAAC,YAAW;AAAC,gBAAM,OAAO,OAAO,IAAI;AAAA,QAAE,WAAS,CAAC,OAAO,SAAQ,OAAO,IAAI,EAAE,QAAQ,MAAM,MAAI,MAAI,CAAC,mBAAiB,CAAC,MAAM,mBAAkB;AAAC,gBAAM,oBAAkB,WAAW,WAAU;AAAC,mBAAO,MAAM;AAAkB,kBAAM,OAAO;AAAA,UAAE,GAAE,aAAW,GAAI;AAAA,QAAE;AAAA,MAAC;AAAA,IAAC,CAAC;AAAE,UAAM,QAAM,EAAC,kBAAiB,MAAM,WAAU,aAAY,OAAM,iBAAgB,MAAM,eAAe,YAAU,CAAC,CAAC,MAAM,QAAO,QAAO,OAAO,MAAK,eAAc,OAAO,KAAI;AAAE,UAAM,aAAW;AAAM,UAAM,aAAW;AAAM,QAAG,UAAU,GAAE;AAAC,aAAO,iBAAiB,QAAO,WAAU;AAAC,YAAG,MAAM,QAAO;AAAC,gBAAM,OAAO,SAAS,OAAO;AAAA,QAAE;AAAC,YAAG,MAAM,eAAc;AAAC,gBAAM,cAAc,SAAS,OAAO;AAAA,QAAE;AAAA,MAAC,CAAC;AAAA,IAAE;AAAC,WAAO;AAAA,EAAM;AAAC,eAAaA,eAAa,CAAC,EAAC,KAAI,qBAAoB,OAAM,SAAS,oBAAmB;AAAC,QAAG,CAAC,UAAU;AAAE;AAAO,QAAI,kBAAgB,KAAK,MAAM;AAAgB,QAAI,eAAa,KAAK,OAAM,WAAS,aAAa,UAAS,OAAK,aAAa,MAAK,SAAO,aAAa;AAAO,SAAK,aAAW;AAAK,QAAI,EAAC,OAAM,QAAO,MAAK,EAAC,aAAY,CAAC,CAAC,UAAS,WAAU,CAAC,CAAC,QAAO,cAAa,YAAG,SAAS,EAAE,IAAI,GAAE,iBAAgC,QAAO,KAAK,QAAO,SAAQ,KAAK,WAAU,GAAE,OAAM,KAAK,MAAK,CAAC;AAAE,QAAG,CAAC,KAAK,YAAW;AAAC,WAAK,WAAW;AAAE,WAAK,aAAW;AAAA,IAAK;AAAC,QAAG,CAAC,YAAU,UAAQ,CAAC,YAAG,SAAS,EAAE,IAAI;AAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,sBAAqB,OAAM,SAAS,mBAAmB,WAAU,WAAU;AAAC,QAAG,CAAC,UAAU;AAAE;AAAO,QAAI,eAAa,KAAK,OAAM,WAAS,aAAa,UAAS,OAAK,aAAa,MAAK,SAAO,aAAa,QAAO,iBAAe,aAAa;AAAe,QAAI,eAAaC,aAAY,WAAU,KAAK,KAAK,GAAE,cAAY,aAAa,aAAY,UAAQ,aAAa;AAAQ,QAAG,UAAU,SAAO,MAAK;AAAC,UAAI;AAC/oI,UAAG,YAAG,SAAS,EAAE,IAAI,GAAE;AAAC,sBAAY,OAAK,OAAO,UAAQ,OAAO;AAAA,MAAQ;AAAC,WAAK,OAAO,WAAW;AAAA,IAAE;AAAC,QAAG,UAAU,eAAe,aAAW,eAAe,YAAU,UAAU,WAAS,QAAO;AAAC,WAAK,sBAAsB,KAAK,KAAK;AAAA,IAAE;AAAC,QAAG,QAAQ,UAAS,OAAO,IAAI,KAAG,MAAK;AAAC,WAAK,OAAO,OAAO,IAAI;AAAA,IAAE,WAAS,YAAY,UAAS,OAAO,MAAK,OAAO,IAAI,KAAG,UAAS;AAAC,WAAK,OAAO,OAAO,IAAI;AAAA,IAAE;AAAC,QAAG,KAAK,UAAQ,QAAQ,UAAS,OAAO,OAAO,GAAE;AAAC,WAAK,OAAO,SAAS,OAAO;AAAA,IAAE;AAAC,QAAG,KAAK,eAAa,QAAQ,UAAS,OAAO,OAAO,KAAG,QAAQ,UAAS,OAAO,OAAO,IAAG;AAAC,WAAK,KAAK,YAAW,iBAAgB,KAAK,mBAAmB;AAAA,IAAE;AAAC,QAAG,QAAQ,eAAc,IAAI,GAAE;AAAC,WAAK,cAAc;AAAA,IAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,wBAAuB,OAAM,SAAS,uBAAsB;AAAC,QAAG,CAAC,UAAU;AAAE;AAAO,SAAK,aAAW;AAAM,QAAG,KAAK,QAAO;AAAC,WAAK,OAAO,SAAS,QAAQ;AAAA,IAAE;AAAC,QAAG,KAAK,eAAc;AAAC,WAAK,cAAc,SAAS,QAAQ;AAAA,IAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,cAAa,OAAM,SAAS,aAAY;AAAC,QAAI,SAAO;AAAK,QAAI,SAAO,UAAU,SAAO,KAAG,UAAU,CAAC,MAAI,SAAU,UAAU,CAAC,IAAE,KAAK;AAAO,QAAI,kBAAgB,KAAK,MAAM;AAAgB,QAAI,eAAa,KAAK,OAAM,cAAY,aAAa,aAAY,YAAU,aAAa,WAAU,YAAU,aAAa,WAAUC,UAAO,aAAa,QAAO,YAAU,aAAa,WAAU,iBAAe,aAAa;AAAe,QAAI,eAAa,cAAY,SAAO,cAAY,WAAS,SAAO,CAAC,SAAQ,cAAa,WAAU,QAAO,aAAY,cAAc;AAA4B,QAAG,cAAY,UAAS;AAAC,WAAK,SAAS,EAAC,QAAO,OAAO,KAAI,CAAC;AAAA,IAAE,WAAS,UAAQ,KAAK,YAAW;AAAC,UAAI,gBAAc,KAAK,SAAQN,SAAM,cAAc,OAAMO,QAAK,cAAc,MAAK,gBAAc,cAAc,QAAO,OAAK,yBAAyB,eAAc,SAAS;AAAE,UAAI,eAAO,QAAO,KAAK,YAAW,EAAC,WAAoB,WAAU,eAAe,EAAC,OAAM,eAAe,EAAC,SAAQ,CAAC,WAAU,SAAQ,KAAK,SAAQ,GAAEP,MAAK,GAAE,MAAK,eAAe,EAAC,SAAQ,CAAC,aAAY,UAAS,aAAY,GAAEO,KAAI,GAAE,QAAO,eAAe,EAAC,QAAO,MAAM,OAAOD,SAAO,IAAI,EAAC,GAAE,aAAa,EAAC,GAAE,IAAI,GAAE,UAAS,SAASE,UAAS,MAAK;AAAC,YAAI;AAAkB,eAAO,SAAO;AAAK,YAAG,GAAG,oBAAkB,OAAO,gBAAc,QAAM,sBAAoB,UAAQ,kBAAkB,cAAa;AAAC,iBAAO,SAAS,EAAC,aAAY,KAAI,CAAC;AAAE;AAAA,QAAO;AAAC,kBAAU,MAAK,SAAS;AAAE,YAAG,OAAO,YAAW;AAAC,iBAAO,SAAS,EAAC,kBAAiB,KAAK,WAAU,QAAO,OAAO,KAAI,CAAC;AAAA,QAAE;AAAC,YAAG,cAAY,KAAK,WAAU;AAAC,qBAAW,WAAU;AAAC,iBAAK,SAAS,OAAO;AAAA,UAAE,GAAE,CAAC;AAAA,QAAE;AAAA,MAAC,GAAE,UAAS,SAASC,UAAS,MAAK;AAAC,eAAO,SAAO;AAAK,YAAI,mBAAiB,OAAO,MAAM;AAAiB,YAAG,OAAO,cAAY,KAAK,cAAY,kBAAiB;AAAC,iBAAO,SAAS,EAAC,kBAAiB,KAAK,UAAS,CAAC;AAAA,QAAE;AAAA,MAAC,EAAC,CAAC;AAAA,IAAE;AAAC,QAAG,iBAAgB;AAAC,UAAI,gBAAc,CAAC,YAAG,UAAU,eAAe,MAAM,IAAE,eAAe,SAAO;AAAE,UAAI,eAAO,KAAK,QAAO,KAAK,YAAW,EAAC,WAAU,eAAe,aAAW,WAAU,WAAU,EAAC,OAAM,EAAC,SAAQ,MAAK,GAAE,QAAO,EAAC,QAAO,MAAM,OAAO,eAAc,IAAI,EAAC,GAAE,MAAK,EAAC,SAAQ,MAAK,EAAC,GAAE,UAAS,SAASD,UAAS,MAAK;AAAC,eAAO,gBAAc;AAAK,YAAG,OAAO,YAAW;AAAC,iBAAO,SAAS,EAAC,eAAc,OAAO,KAAI,CAAC;AAAA,QAAE;AAAC,kBAAU,MAAK,SAAS;AAAE,YAAG,cAAY,KAAK,WAAU;AAAC,qBAAW,WAAU;AAAC,iBAAK,SAAS,OAAO;AAAA,UAAE,GAAE,CAAC;AAAA,QAAE;AAAA,MAAC,EAAC,CAAC;AAAA,IAAE;AAAA,EAAC,EAAC,GAAE,EAAC,KAAI,iBAAgB,OAAM,SAAS,gBAAe;AAAC,QAAI,SAAO;AAAK,SAAK,qBAAmB,YAAY,WAAU;AAAC,UAAI;AAAkB,WAAI,oBAAkB,OAAO,gBAAc,QAAM,sBAAoB,UAAQ,kBAAkB,aAAY;AAAC,sBAAc,OAAO,kBAAkB;AAAE,eAAO,SAAS,EAAC,aAAY,MAAK,CAAC;AAAE,eAAO,WAAW;AAAA,MAAE;AAAA,IAAC,GAAE,EAAE;AAAA,EAAE,EAAC,GAAE,EAAC,KAAI,yBAAwB,OAAM,SAAS,sBAAsB,MAAK;AAAC,QAAI,SAAO,KAAK,QAAO,iBAAe,KAAK;AAAe,SAAK,SAAS,EAAC,iBAAgB,eAAe,YAAU,CAAC,CAAC,OAAM,CAAC;AAAA,EAAE,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAAS,OAAO,aAAY;AAAC,QAAI,SAAO,KAAK,MAAM;AAAO,QAAI,aAAW,WAAS,OAAO,OAAK,OAAO,UAAQ,OAAO;AAAQ,QAAG,CAAC,YAAG,UAAU,WAAW,GAAE;AAAC,mBAAW;AAAA,IAAY;AAAC,SAAK,SAAS,EAAC,QAAO,WAAU,CAAC;AAAA,EAAE,EAAC,GAAE,EAAC,KAAI,SAAQ,KAAI,SAAS,MAAK;AAAC,QAAI,QAAM,KAAK,MAAM;AAAM,WAAO,SAAO,UAAU,KAAG,uBAAsB,UAAQ,CAAC,CAAC,OAAO;AAAA,EAAkB,EAAC,GAAE,EAAC,KAAI,SAAQ,KAAI,SAAS,MAAK;AAAC,QAAI,eAAa,KAAK,OAAM,sBAAoB,aAAa,qBAAoB,QAAM,aAAa;AAAM,QAAG,UAAQ,WAAS,SAAS,KAAG,CAAC,qBAAoB;AAAC,aAAO;AAAA,IAAQ;AAAC,WAAO;AAAA,EAAM,EAAC,GAAE,EAAC,KAAI,WAAU,KAAI,SAAS,MAAK;AAAC,QAAI,UAAQ,KAAK,MAAM;AAAQ,eAAO,iBAAAL,SAAU,UAAS,WAAS,CAAC,CAAC;AAAA,EAAE,EAAC,GAAE,EAAC,KAAI,UAAS,KAAI,SAAS,MAAK;AAAC,QAAI,SAAO;AAAK,QAAI,eAAa,KAAK,OAAM,SAAO,aAAa,QAAO,kBAAgB,aAAa,iBAAgB,gBAAc,aAAa;AAAc,QAAI,SAAO,KAAK,MAAM;AAAO,QAAI,iBAAW,iBAAAA,SAAU,UAAU,MAAM,GAAE,MAAM;AAAE,QAAG,iBAAgB;AAAC,UAAI;AAAc,UAAG,EAAE,CAAC,OAAO,IAAI,EAAE,QAAQ,MAAM,MAAI,OAAK,EAAE,CAAC,OAAO,IAAI,EAAE,QAAQ,aAAa,MAAI,KAAI;AAAC,wBAAc,WAAW;AAAA,MAAgB,OAAM;AAAC,wBAAc,KAAK,cAAc;AAAA,MAAO;AAAC,iBAAW,UAAQ,eAAe,eAAe,CAAC,GAAE,WAAW,OAAO,GAAE,aAAa;AAAA,IAAE;AAA2B,QAAG,KAAK,QAAO;AAAC,UAAI,eAAa,OAAO,iBAAiB,KAAK,MAAM;AAA4B,UAAG,KAAK,eAAc;AAAC,mBAAW,UAAQ,eAAe,eAAe,CAAC,GAAE,WAAW,OAAO,GAAE,KAAK,aAAa;AAAA,MAAE,WAAS,EAAE,CAAC,YAAW,QAAQ,EAAE,QAAQ,aAAa,QAAQ,MAAI,KAAI;AAAC,aAAK,gBAAc,CAAC;AAAE,YAAG,CAAC,iBAAgB;AAAC,4BAAkB,QAAQ,SAAS,GAAE;AAAC,mBAAO,cAAc,CAAC,IAAE,aAAa,CAAC;AAAA,UAAE,CAAC;AAAE,qBAAW,UAAQ,eAAe,eAAe,CAAC,GAAE,WAAW,OAAO,GAAE,KAAK,aAAa;AAAE,eAAK,OAAO,MAAM,WAAS;AAAW,eAAK,OAAO,MAAM,MAAI;AAAO,eAAK,OAAO,MAAM,QAAM;AAAO,eAAK,OAAO,MAAM,SAAO;AAAO,eAAK,OAAO,MAAM,OAAK;AAAA,QAAO;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAW,EAAC,GAAE,EAAC,KAAI,UAAS,KAAI,SAAS,MAAK;AAAC,QAAG,CAAC,UAAU;AAAE,aAAO;AAAK,QAAI,SAAO,KAAK,MAAM;AAAO,QAAG,QAAO;AAAC,UAAG,YAAG,WAAW,MAAM,GAAE;AAAC,eAAO;AAAA,MAAO;AAAC,aAAO,SAAS,cAAc,MAAM;AAAA,IAAE;AAAC,WAAO,KAAK,YAAU,KAAK;AAAA,EAAW,EAAC,GAAE,EAAC,KAAI,UAAS,OAAM,SAAS,SAAQ;AAAC,QAAI,eAAa,KAAK,OAAM,mBAAiB,aAAa,kBAAiB,kBAAgB,aAAa,iBAAgB,SAAO,aAAa;AAAO,QAAI,eAAa,KAAK,OAAM,WAAS,aAAa,UAAS,YAAU,aAAa,WAAU,UAAQ,aAAa,SAAQ,mBAAiB,aAAa,kBAAiB,SAAO,aAAa,QAAO,YAAU,aAAa,WAAU,KAAG,aAAa,IAAG,OAAK,aAAa,MAAK,kBAAgB,aAAa,iBAAgB,QAAM,aAAa,OAAM,SAAO,aAAa,QAAO,QAAM,aAAa;AAAM,QAAI,UAAqB,aAAAN,QAAM,cAAc,qBAAoB,EAAC,aAAY,KAAK,aAAY,kBAAiB,KAAK,kBAAiB,kBAAiB,KAAK,kBAAiB,aAAY,KAAK,aAAY,eAAc,KAAK,eAAc,OAAY,QAAO,KAAK,OAAO,QAAO,GAAE,QAAQ;AAAE,QAAI,SAAO,CAAC;AAAE,QAAG,iBAAgB;AAAC,aAAO,kBAAgB;AAAA,IAAQ,OAAM;AAAC,aAAO,oBAAkB;AAAA,IAAQ;AAAC,WAAmB,aAAAA,QAAM,cAAc,QAAO,MAAkB,aAAAA,QAAM,cAAc,oBAAmB,EAAC,aAAY,CAAC,CAAC,UAAS,IAAM,WAAU,kBAAiB,QAAO,KAAK,eAAc,QAAc,QAAO,KAAK,OAAO,QAAQ,OAAM,GAAe,aAAAA,QAAM,cAAc,SAAQ,EAAC,WAAoB,SAAgB,kBAAkC,QAAc,aAAY,KAAK,aAAY,WAAU,aAAW,qBAAmB,UAAS,MAAU,WAAU,kBAAiB,iBAAgC,aAAY,KAAK,aAAY,eAAc,KAAK,eAAc,iBAAgC,QAAc,QAAO,KAAK,QAAO,MAAW,CAAC,GAAE,OAAO,eAAe,GAAE,OAAO,iBAAiB;AAAA,EAAE,EAAC,CAAC,CAAC;AAAE,SAAOO;AAAa,EAAE,aAAAP,QAAM,SAAS;AAAE,gBAAgB,cAAa,aAAY,EAAC,UAAS,kBAAAC,QAAU,MAAK,UAAS,kBAAAA,QAAU,MAAK,UAAS,kBAAAA,QAAU,MAAK,WAAU,aAAa,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAK,kBAAAA,QAAU,OAAO,CAAC,GAAE,SAAS,OAAM;AAAC,SAAO,CAAC,MAAM;AAAQ,CAAC,GAAE,SAAQ,aAAa,kBAAAA,QAAU,MAAK,SAAS,OAAM;AAAC,SAAO,CAAC,MAAM;AAAU,CAAC,GAAE,OAAM,kBAAAA,QAAU,MAAK,kBAAiB,kBAAAA,QAAU,MAAK,aAAY,kBAAAA,QAAU,MAAK,qBAAoB,kBAAAA,QAAU,MAAK,OAAM,kBAAAA,QAAU,MAAM,CAAC,SAAQ,OAAO,CAAC,GAAE,YAAW,kBAAAA,QAAU,QAAO,QAAO,kBAAAA,QAAU,MAAK,WAAU,kBAAAA,QAAU,MAAK,WAAU,kBAAAA,QAAU,MAAK,IAAG,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAO,kBAAAA,QAAU,MAAM,CAAC,GAAE,QAAO,kBAAAA,QAAU,QAAO,MAAK,kBAAAA,QAAU,MAAK,SAAQ,kBAAAA,QAAU,QAAO,WAAU,kBAAAA,QAAU,MAAM,CAAC,OAAM,aAAY,WAAU,UAAS,gBAAe,cAAa,QAAO,cAAa,YAAW,SAAQ,eAAc,aAAY,QAAO,QAAQ,CAAC,GAAE,iBAAgB,kBAAAA,QAAU,MAAK,OAAM,kBAAAA,QAAU,QAAO,QAAO,kBAAAA,QAAU,QAAO,QAAO,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAO,kBAAAA,QAAU,MAAM,CAAC,GAAE,OAAM,kBAAAA,QAAU,MAAK,gBAAe,kBAAAA,QAAU,MAAM,EAAC,QAAO,kBAAAA,QAAU,QAAO,WAAU,kBAAAA,QAAU,MAAM,CAAC,OAAM,aAAY,WAAU,UAAS,gBAAe,cAAa,QAAO,cAAa,YAAW,SAAQ,eAAc,aAAY,MAAM,CAAC,GAAE,UAAS,kBAAAA,QAAU,KAAI,CAAC,EAAC,CAAC;AAAE,gBAAgB,cAAa,gBAAe,EAAC,UAAS,OAAM,UAAS,MAAK,OAAM,OAAM,kBAAiB,OAAM,aAAY,OAAM,qBAAoB,OAAM,OAAM,SAAQ,YAAW,KAAI,WAAU,MAAK,WAAU,OAAM,QAAO,IAAG,WAAU,UAAS,iBAAgB,OAAM,QAAO,CAAC,GAAE,QAAO,MAAK,gBAAe,EAAC,UAAS,MAAK,EAAC,CAAC;;;AVjNrmS,aAAuB;ACAvB,aAAuB;ACAvB,aAAuB;ACAvB,IAAAY,gBAAqC;;;;AhBA9B,IAAM,UAAU;EACrB,MAAM;EACN,OAAO;EACP,MAAM;EACN,OAAO;EACP,MAAM;EACN,MAAM;EACN,IAAI;EACJ,OAAO;EACP,MAAM;EACN,QAAQ;AACV;AAEO,IAAM,SAAS;EACpB,YAAY;EACZ,aAAa;EACb,QAAQ;EACR,SAAS;EACT,YAAY;EACZ,UAAU;EACV,aAAa;EACb,kBAAkB;EAClB,OAAO;AACT;AAEO,IAAM,YAAY;EACvB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,UAAU;EACV,OAAO;AACT;AAEO,IAAM,SAAS;EACpB,cAAc;EACd,gBAAgB;EAChB,UAAU;EACV,SAAS;AACX;AAEO,IAAMC,UAAS;EACpB,MAAM;EACN,OAAO;EACP,SAAS;EACT,SAAS;EACT,QAAQ;EACR,SAAS;EACT,UAAU;EACV,OAAO;AACT;AE/CO,SAASC,aAAY;AAH5B,MAAA;AAIE,SAAO,CAAC,EAAE,OAAO,WAAW,iBAAe,KAAA,OAAO,aAAP,OAAA,SAAA,GAAiB;AAC9D;AAKO,SAASC,eAAc,SAA6B;AACzD,MAAI,CAAC,SAAS;AACZ,WAAO;EACT;AAEA,SAAO,QAAQ,sBAAsB;AACvC;AAKO,SAAS,kBAAkB,SAAS,OAAe;AACxD,QAAM,EAAE,MAAM,gBAAgB,IAAI;AAElC,MAAI,CAAC,QAAQ,CAAC,iBAAiB;AAC7B,WAAO;EACT;AAEA,MAAI,QAAQ;AACV,UAAM,UAAU;MACd,KAAK;MACL,KAAK;MACL,gBAAgB;MAChB,gBAAgB;MAChB,gBAAgB;IAClB,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACtB,UAAM,SAAS,KAAK,MAAM,QAAQ,SAAS,CAAC;AAE5C,QAAI,QAAQ,SAAS,MAAM,GAAG;AAC5B,cAAQ,QAAQ,SAAS,CAAC,IAAI,QAAQ,MAAM,KAAK;IACnD;AAEA,WAAO,QAAQ,MAAM;EACvB;AAEA,SAAO,KAAK;IACV,KAAK;IACL,KAAK;IACL,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;EAClB;AACF;AAKO,SAAS,WAAW,SAAmD;AAC5E,MAAI,OAAO,YAAY,UAAU;AAC/B,QAAI;AACF,aAAO,SAAS,cAAc,OAAO;IACvC,SAAS,OAAY;AACnB,UAAI,MAAuC;AAEzC,gBAAQ,MAAM,KAAK;MACrB;AAEA,aAAO;IACT;EACF;AAEA,SAAO;AACT;AAKO,SAASC,0BAAyB,IAA6C;AACpF,MAAI,CAAC,MAAM,GAAG,aAAa,GAAG;AAC5B,WAAO;EACT;AAEA,SAAO,iBAAiB,EAAE;AAC5B;AAKO,SAASC,iBACd,SACA,SACA,aACA;AACA,MAAI,CAAC,SAAS;AACZ,WAAO,eAAe;EACxB;AAEA,QAAM,aAAS,oBAAAC,SAAa,OAAO;AAEnC,MAAI,QAAQ;AACV,QAAI,OAAO,WAAW,eAAe,CAAC,GAAG;AACvC,UAAI,aAAa;AACf,eAAO;MACT;AAEA,aAAO,eAAe;IACxB;AAEA,UAAM,eAAe,OAAO,eAAe,OAAO;AAElD,QAAI,CAAC,gBAAgB,CAAC,SAAS;AAC7B,aAAO,MAAM,WAAW;AAExB,aAAO,eAAe;IACxB;EACF;AAEA,SAAO;AACT;AAKO,SAAS,sBAAsB,SAA6B,SAA2B;AAC5F,MAAI,CAAC,SAAS;AACZ,WAAO;EACT;AAEA,QAAM,SAASD,iBAAgB,SAAS,OAAO;AAE/C,SAAO,SAAS,CAAC,OAAO,WAAW,eAAe,CAAC,IAAI;AACzD;AAKO,SAAS,sBAAsB,SAA+B;AACnE,SAAO,QAAQ,iBAAiB,SAAS;AAC3C;AAKO,SAAS,YAAY,IAA+B,OAAe,SAAkB;AAC1F,MAAI,CAAC,MAAM,EAAE,cAAc,cAAc;AACvC,WAAO;EACT;AAEA,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,SAASD,0BAAyB,EAAE;AAE1C,MAAI,aAAa,UAAU,aAAa,QAAQ;AAC9C,WAAO;EACT;AAEA,MAAI,UAAU,OAAO,aAAa,MAAM;AACtC,WAAO;EACT;AAEA,MAAI,CAAC,GAAG,YAAY;AAClB,WAAO;EACT;AAEA,SAAO,YAAY,GAAG,YAAY,IAAI;AACxC;AAKO,SAAS,iBAAiB,SAA8C;AAzK/E,MAAA;AA0KE,MAAI,CAAC,SAAS;AACZ,WAAO;EACT;AAEA,MAAI,gBAAoC;AAExC,SAAO,eAAe;AACpB,QAAI,kBAAkB,SAAS,MAAM;AACnC;IACF;AAEA,QAAI,yBAAyB,aAAa;AACxC,YAAM,EAAE,SAAS,WAAW,IAAI,iBAAiB,aAAa;AAE9D,UAAI,YAAY,UAAU,eAAe,UAAU;AACjD,eAAO;MACT;IACF;AAEA,qBAAgB,KAAA,cAAc,kBAAd,OAAA,KAA+B;EACjD;AAEA,SAAO;AACT;AAKO,SAAS,mBACd,SACAG,SACA,SACQ;AA1MV,MAAA,IAAA,IAAA;AA2ME,QAAM,cAAcJ,eAAc,OAAO;AACzC,QAAM,SAASE,iBAAgB,SAAS,OAAO;AAC/C,QAAM,kBAAkB,sBAAsB,SAAS,OAAO;AAC9D,QAAM,gBAAgB,YAAY,OAAO;AACzC,MAAI,YAAY;AAChB,MAAI,OAAM,KAAA,eAAA,OAAA,SAAA,YAAa,QAAb,OAAA,KAAoB;AAE9B,MAAI,mBAAmB,eAAe;AACpC,UAAM,aAAY,KAAA,WAAA,OAAA,SAAA,QAAS,cAAT,OAAA,KAAsB;AACxC,UAAM,mBAAmB,KAAA,UAAA,OAAA,SAAA,OAAwB,cAAxB,OAAA,KAAqC;AAE9D,UAAM,YAAY;EACpB,WAAW,kBAAkB,aAAa;AACxC,gBAAY,OAAO;AAEnB,QAAI,CAAC,mBAAmB,CAAC,YAAY,OAAO,GAAG;AAC7C,aAAO;IACT;AAEA,QAAI,CAAC,OAAO,WAAW,eAAe,CAAC,GAAG;AACxC,aAAO,eAAe,EAAE;IAC1B;EACF;AAEA,SAAO,KAAK,MAAM,MAAME,OAAM;AAChC;AAKO,SAAS,YAAY,SAA6BA,SAAgB,SAA0B;AAzOnG,MAAA;AA0OE,MAAI,CAAC,SAAS;AACZ,WAAO;EACT;AAEA,QAAM,EAAE,YAAY,GAAG,YAAY,EAAE,KAAI,SAAA,oBAAAD,SAAa,OAAO,MAApB,OAAA,KAAyB,CAAC;AACnE,MAAI,MAAM,QAAQ,sBAAsB,EAAE,MAAM;AAEhD,MAAI,CAAC,CAAC,cAAc,sBAAsB,SAAS,OAAO,KAAK,sBAAsB,OAAO,IAAI;AAC9F,WAAO;EACT;AAEA,QAAM,SAAS,KAAK,MAAM,MAAMC,OAAM;AAEtC,SAAO,SAAS,IAAI,IAAI;AAC1B;AAEO,SAAS,iBAAwC;AA1PxD,MAAA;AA2PE,UAAO,KAAA,SAAS,qBAAT,OAAA,KAA6B,SAAS;AAC/C;AAKO,SAAS,SACd,OACA,SACe;AACf,QAAM,EAAE,UAAU,QAAQ,IAAI;AAE9B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,EAAE,UAAU,IAAI;AAEtB,UAAM,QAAQ,QAAQ,YAAY,QAAQ,YAAY,YAAY;AAElE,kBAAAC,QAAO,IAAI,SAAwB,OAAO,EAAE,UAAU,QAAQ,MAAM,KAAK,SAAS,GAAG,CAAA,UAAS;AAC5F,UAAI,SAAS,MAAM,YAAY,6CAA6C;AAC1E,eAAO,OAAO,KAAK;MACrB;AAEA,aAAO,QAAQ;IACjB,CAAC;EACH,CAAC;AACH;AC/OO,IAAMC,aAAY,mCAAiB;AAKnC,SAAS,WAAW,YAAoB,UAAU,WAAmB;AAC1E,MAAI,UAAU;AAEd,MAAI,OAAO,WAAW,aAAa;AACjC,cAAU;EACZ,WAES,SAAS,cAAc;AAC9B,cAAU;EACZ,WAAW,OAAO,KAAK,SAAS,GAAG;AACjC,cAAU;EACZ,WAES,QAAQ,OAAO,KAAK,KAAK,UAAU,SAAS,OAAO,GAAG;AAC7D,cAAU;EACZ,WAES,OAAO,OAAO,mBAAmB,aAAa;AACrD,cAAU;EACZ,WAES,OAAO,QAAQ;AACtB,cAAU;EACZ,WAES,qDAAqD,KAAK,SAAS,GAAG;AAC7E,cAAU;EACZ;AAEA,SAAO;AACT;AAKO,SAASC,eAAc,OAAwB;AACpD,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,EAAE,YAAY;AACxE;AAEO,SAAS,iBAAiB,OAAkB,UAAmC,CAAC,GAAW;AAChG,QAAM,EAAE,cAAc,MAAM,MAAM,IAAI;AACtC,MAAI,WAAO,uBAAAC,SAAU,KAAK;AAE1B,MAAI,CAAC,MAAM;AACT,YACE,8BAAe,KAAK,KACpB,CAAC,OAAO,OAAO,MAAM,KAAK,EAAE,UAC5BD,eAAc,MAAM,IAAI,MAAM,YAC9B;AACA,YAAM,YAAa,MAAM,KAAY,CAAC,CAAC;AAEvC,aAAO,iBAAiB,WAAW,OAAO;IAC5C,OAAO;AACL,iBAAO,uBAAAC,SAAU,YAAY;IAC/B;EACF,YAAY,KAAK,SAAS,QAAQ,KAAK,KAAK,SAAS,SAAS,MAAM,QAAQ,OAAO;AACjF,WAAO,KAAK,QAAQ,UAAU,KAAK,SAAS,CAAC,EAAE,QAAQ,WAAW,MAAM,SAAS,CAAC;EACpF;AAEA,SAAO;AACT;AAEO,SAAS,aAAa,QAAiC,MAA+B;AAC3F,MAAI,CAAC,YAAG,YAAY,MAAM,KAAK,CAAC,YAAG,MAAM,IAAI,GAAG;AAC9C,WAAO;EACT;AAEA,SAAO,OAAO,KAAK,MAAM,EAAE,MAAM,CAAA,MAAK,KAAK,SAAS,CAAC,CAAC;AACxD;AAKO,SAAS,SAAS,KAA4B;AACnD,QAAM,iBAAiB;AACvB,QAAM,YAAY,IAAI,QAAQ,gBAAgB,CAAC,IAAI,GAAG,GAAG,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC;AAEpF,QAAM,SAAS,4CAA4C,KAAK,SAAS;AAEzE,SAAO,SAAS,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;AACjG;AAQO,SAAS,WAAW,MAAqB;AAC9C,SAAO,KAAK,iBAAiB,KAAK,cAAc;AAClD;AAOO,SAAS,WAAoB;AAClC,SAAO,CAAC,CAAC,UAAU,UAAU,WAAW,OAAO,EAAE,SAAS,WAAW,CAAC;AACxE;AAKO,SAASC,KAAI,EAAE,MAAM,QAAQ,OAAO,OAAO,OAAO,MAAM,GAAe;AAE5E,QAAM,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ;AAE7D,MAAI,OAAO;AACT,QAAI,SAAS,MAAM;AACjB,cAAQ;QACN,oBAAoB,KAAK;QACzB;MACF;AAEA,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,aAAK,QAAQ,CAAA,MAAK;AAChB,cAAI,YAAG,YAAY,CAAC,KAAK,EAAE,KAAK;AAC9B,kBAAM,MAAM,SAAS,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;UACvC,OAAO;AACL,kBAAM,MAAM,SAAS,CAAC,CAAC,CAAC;UAC1B;QACF,CAAC;MACH,OAAO;AACL,cAAM,MAAM,SAAS,CAAC,IAAI,CAAC;MAC7B;AAEA,cAAQ,SAAS;IACnB,OAAO;AACL,cAAQ,MAAM,6BAA6B;IAC7C;EACF;AAEF;AAKO,SAASC,QAAO;AACrB,SAAO;AACT;AAKO,SAAS,WAAgC,OAAU;AACxD,SAAO,OAAO,KAAK,KAAK;AAC1B;AAKO,SAAS,KACd,UACG,QACH;AACA,MAAI,CAAC,YAAG,YAAY,KAAK,GAAG;AAC1B,UAAM,IAAI,UAAU,oBAAoB;EAC1C;AAEA,QAAM,SAAc,CAAC;AAErB,aAAW,OAAO,OAAO;AAEvB,QAAI,CAAC,EAAE,eAAe,KAAK,OAAO,GAAG,GAAG;AACtC,UAAI,CAAC,OAAO,SAAS,GAAmB,GAAG;AACzC,eAAO,GAAG,IAAI,MAAM,GAAG;MACzB;IACF;EACF;AAEA,SAAO;AACT;AAKO,SAAS,KACd,UACG,QACH;AACA,MAAI,CAAC,YAAG,YAAY,KAAK,GAAG;AAC1B,UAAM,IAAI,UAAU,oBAAoB;EAC1C;AAEA,MAAI,CAAC,OAAO,QAAQ;AAClB,WAAO;EACT;AAEA,QAAM,SAAc,CAAC;AAErB,aAAW,OAAO,OAAO;AAEvB,QAAI,CAAC,EAAE,eAAe,KAAK,OAAO,GAAG,GAAG;AACtC,UAAI,OAAO,SAAS,GAAmB,GAAG;AACxC,eAAO,GAAG,IAAI,MAAM,GAAG;MACzB;IACF;EACF;AAEA,SAAO;AACT;AAEO,SAAS,qBAAqB,OAAkB,MAAc,OAA0B;AAC7F,QAAM,WAAW,CAAC,SAChB,KAAK,QAAQ,UAAU,OAAO,IAAI,CAAC,EAAE,QAAQ,WAAW,OAAO,KAAK,CAAC;AAEvE,MAAIH,eAAc,KAAK,MAAM,UAAU;AACrC,WAAO,SAAS,KAAe;EACjC;AAEA,MAAI,KAAC,8BAAe,KAAK,GAAG;AAC1B,WAAO;EACT;AAEA,QAAM,EAAE,SAAS,IAAI,MAAM;AAE3B,MAAIA,eAAc,QAAQ,MAAM,YAAY,SAAS,SAAS,QAAQ,GAAG;AACvE,eAAO,4BAAa,OAAuB;MACzC,UAAU,SAAS,QAAQ;IAC7B,CAAC;EACH;AAEA,MAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,eAAO,4BAAa,OAAuB;MACzC,UAAU,SAAS,IAAI,CAAC,UAAqB;AAC3C,YAAI,OAAO,UAAU,UAAU;AAC7B,iBAAO,SAAS,KAAK;QACvB;AAEA,eAAO,qBAAqB,OAAO,MAAM,KAAK;MAChD,CAAC;IACH,CAAC;EACH;AAEA,MAAIA,eAAc,MAAM,IAAI,MAAM,cAAc,CAAC,OAAO,OAAO,MAAM,KAAK,EAAE,QAAQ;AAClF,UAAM,YAAa,MAAM,KAAY,CAAC,CAAC;AAEvC,WAAO,qBAAqB,WAAW,MAAM,KAAK;EACpD;AAEA,SAAO;AACT;AAEO,SAAS,aAAa,SAAuC;AAClE,QAAM,EAAE,aAAa,WAAW,mBAAmB,mBAAmB,MAAM,OAAO,IAAI;AAEvF,SACE,CAAC,KAAK,qBACL,CAAC,eAAe,qBAAqB,cAAc,UAAU,YAC9D,KAAK,cAAc,aAClB,CAAC,KAAK,WAAW,CAAC,YAAY,MAAM;EACrC,sBAAsB,aACrB,CAAC,UAAU,QAAQ,UAAU,OAAO,EAAuB,SAAS,SAAS;AAElF;AErSO,IAAM,sBAAoC;EAC/C,SAAS;IACP,iBAAiB;MACf,mBAAmB;IACrB;EACF;EACA,gBAAgB;IACd,QAAQ;IACR,UAAU;EACZ;AACF;AAEO,IAAM,gBAAwB;EACnC,MAAM;EACN,OAAO;EACP,MAAM;EACN,MAAM;EACN,uBAAuB;EACvB,MAAM;EACN,MAAM;AACR;AAEO,IAAM,cAAc;EACzB,OAAO;EACP,WAAW;EACX,QAAQ;EACR,eAAe;EACf,mBAAmB;EACnB,gBAAgB;EAChB,qBAAqB;EACrB,wBAAwB;EACxB,kBAAkB;EAClB,gBAAgB;EAChB,iBAAiB;EACjB,YAAY;EACZ,SAAS;EACT,QAAQ;EACR,cAAc;EACd,gBAAgB;EAChB,iBAAiB;EACjB,kBAAkB;AACpB;AAEO,IAAM,eAAe;EAC1B,YAAY;EACZ,OAAO;EACP,mBAAmB;EACnB,gBAAgB;EAChB,qBAAqB;EACrB,kBAAkB;EAClB,wBAAwB;EACxB,YAAYG,MAAK;EACjB,gBAAgB;EAChB,KAAK;EACL,cAAc;EACd,gBAAgB;EAChB,mBAAmB;EACnB,gBAAgB;EAChB,cAAc;EACd,iBAAiB;EACjB,kBAAkB;EAClB,OAAO,CAAC;AACV;AC7DA,IAAMC,kBAAiB;EACrB,YAAY;EACZ,iBAAiB;EACjB,YAAY;EACZ,cAAc;EACd,cAAc;EACd,iBAAiB;EACjB,WAAW;EACX,OAAO;EACP,QAAQ;AACV;AAEA,IAAM,aAAa;EACjB,iBAAiB;EACjB,QAAQ;EACR,cAAc;EACd,OAAO;EACP,QAAQ;EACR,UAAU;EACV,YAAY;EACZ,SAAS;EACT,kBAAkB;AACpB;AAEA,IAAM,YAAY;EAChB,cAAc;EACd,UAAU;AACZ;AAEe,SAARC,WAA2B,OAAc,MAAkB;AAlClE,MAAA,IAAA,IAAA,IAAA,IAAA;AAmCE,QAAM,EAAE,cAAc,OAAO,IAAI;AACjC,QAAM,yBAAqB,kBAAAC,UAAU,KAAA,KAAK,iBAAL,OAAA,KAAqB,CAAC,GAAG,gBAAA,OAAA,eAAgB,CAAC,CAAC;AAChF,QAAM,mBAAe,kBAAAA,SAAU,UAAA,OAAA,SAAU,CAAC,IAAG,KAAA,KAAK,WAAL,OAAA,KAAe,CAAC,CAAC;AAC9D,QAAM,cAAU,kBAAAA,SAAUF,iBAAgB,aAAa,WAAW,CAAC,CAAC;AACpE,QAAMG,cAAa,KAAK,cAAc,YAAY,KAAK;AACvD,MAAI,EAAE,MAAM,IAAI;AAEhB,MAAI,OAAO,aAAa,KAAK;AAC3B,YAAQ;EACV;AAEA,MAAI,WAAW,SAAS;AACtB,YACE,OAAO,QAAQ,UAAU,YAAY,OAAO,aAAa,QAAQ,QAC7D,OAAO,aAAa,KACpB,QAAQ;EAChB;AAEA,QAAM,UAAU;IACd,QAAQ;IACR,MAAM;IACN,UAAU;IACV,UAAU;IACV,OAAO;IACP,KAAK;IACL,QAAQ,QAAQ;EAClB;AAEA,QAAM,gBAAgB;IACpB,QAAQ;MACN,GAAG;MACH,SAASA,cAAa,SAAS;MAC/B,QAAQ,QAAQ;MAChB,UAAU;MACV,OAAO,QAAQ;MACf,QAAQ,QAAQ;IAClB;IACA,aAAa;MACX,WAAW;MACX,iBAAiB,QAAQ;MACzB,cAAc;MACd,SAAS;MACT,QAAQ;MACR,MAAM;MACN,SAAS;MACT,UAAU;MACV,KAAK;MACL,WAAW;MACX,OAAO;IACT;IACA,aAAa;MACX,WAAW;MACX,iBAAiB,QAAQ,SAAS,QAAQ,YAAY,EAAE,KAAK,GAAG,CAAC;MACjE,QAAQ,aAAa,QAAQ,YAAY;MACzC,cAAc;MACd,WAAW;MACX,SAAS;MACT,QAAQ;MACR,MAAM;MACN,SAAS;MACT,UAAU;MACV,KAAK;MACL,iBAAiB;MACjB,OAAO;IACT;IACA,SAAS;MACP,iBAAiB,QAAQ;MACzB,cAAc;MACd,WAAW;MACX,OAAO,QAAQ;MACf,UAAU;MACV,UAAU;MACV,SAAS;MACT,UAAU;MACV;IACF;IACA,kBAAkB;MAChB,YAAY;MACZ,WAAW;IACb;IACA,cAAc;MACZ,UAAU;MACV,QAAQ;IACV;IACA,gBAAgB;MACd,SAAS;IACX;IACA,eAAe;MACb,YAAY;MACZ,SAAS;MACT,gBAAgB;MAChB,WAAW;IACb;IACA,qBAAqB;MACnB,MAAM;IACR;IACA,YAAY;MACV,GAAG;MACH,iBAAiB,QAAQ;MACzB,cAAc;MACd,OAAO;IACT;IACA,YAAY;MACV,GAAG;MACH,OAAO,QAAQ;MACf,YAAY;MACZ,aAAa;IACf;IACA,aAAa;MACX,GAAG;MACH,OAAO,QAAQ;MACf,QAAQ;MACR,SAAS;MACT,UAAU;MACV,OAAO;MACP,KAAK;MACL,OAAO;IACT;IACA,YAAY;MACV,GAAG;MACH,OAAO,QAAQ;MACf,UAAU;IACZ;IACA,SAAS;MACP,GAAG;MACH,iBAAiB,QAAQ;MACzB,cAAc;IAChB;IACA,eAAe;MACb,GAAG;IACL;IACA,qBAAqB;MACnB,GAAG;MACH,iBAAiB,QAAQ;IAC3B;IACA,WAAW;MACT,GAAG;MACH,iBAAiB;IACnB;IACA,iBAAiB;MACf,GAAG;MACH,WAAW,gBAAgB,QAAQ,YAAY,KAAK,QAAQ,eAAe;IAC7E;IACA,eAAe;MACb,OAAO;QACL,QAAO,MAAA,MAAA,KAAA,sBAAA,OAAA,SAAA,mBAAoB,WAApB,OAAA,SAAA,GAA4B,UAA5B,OAAA,SAAA,GAAmC,UAAnC,OAAA,KAA4C,QAAQ;MAC7D;MACA,SAAS;QACP,QAAQ,QAAQ,SAAS;MAC3B;IACF;IACA;EACF;AAEA,aAAO,kBAAAD,SAAU,eAAe,YAAY;AAC9C;AFlLA,SAAS,aAAa,OAAc;AAClC,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;AACF;AAEO,SAAS,cAAc,OAAc,aAAgC;AAlC5E,MAAA,IAAA,IAAA,IAAA,IAAA,IAAA;AAmCE,QAAM,OAAO,eAAA,OAAA,cAAe,CAAC;AAC7B,QAAM,aAAaA,kBAAAA,QAAU,IAAI,CAAC,aAAa,aAAa,KAAK,GAAG,IAAI,GAAG;IACzE,mBAAmBE,YAAG;EACxB,CAAC;AAED,QAAM,eAAeH,WAAU,OAAO,UAAU;AAChD,QAAMT,gBAAe;IACnB,WAAW,WAAW,MAAM;IAC5B,WAAW;EACb;AACA,QAAM,eAAeU,kBAAAA,QAAU,IAAI;IACjC;KACA,KAAA,MAAM,iBAAN,OAAA,KAAsB,CAAC;KACvB,KAAA,WAAW,iBAAX,OAAA,KAA2B,CAAC;EAC9B,CAAC;AAGD,eAAa,SAAS,WAAW;AACjC,eAAa,aAASA,kBAAAA,UAAU,KAAA,aAAa,WAAb,OAAA,KAAuB,CAAC,GAAG,aAAa,aAAa;AAErF,eAAa,WAAU,MAAA,KAAA,MAAM,qBAAN,OAAA,KAA0B,WAAW,qBAArC,OAAA,KAAyD;AAEhF,MAAI,WAAW,mBAAmB,aAAa,gBAAgB;AAC7D,iBAAa,eAAe,YAAY,WAAW;EACrD;AAEA,MAAIV,iBAAgB,aAAa,QAAQ,iBAAiB;AACxD,iBAAa,QAAQ,gBAAgB,oBAAoB;EAC3D;AAEA,SAAO;IACL,GAAG;IACH,QAAQU,kBAAAA,QAAU,IAAI,CAAC,gBAAe,KAAA,MAAM,WAAN,OAAA,KAAgB,CAAC,GAAG,WAAW,UAAU,CAAC,CAAC,CAAC;IAClF;IACA,QAAQ,KAAK,cAAc,eAAe;EAC5C;AACF;AAKO,SAAS,aAAa,MAAY,QAAiB,OAAgB;AACxE,MAAI,CAACE,YAAG,YAAY,IAAI,GAAG;AACzB,IAAAN,KAAI;MACF,OAAO;MACP,MAAM;MACN,MAAM;MACN;IACF,CAAC;AAED,WAAO;EACT;AAEA,MAAI,CAAC,KAAK,QAAQ;AAChB,IAAAA,KAAI;MACF,OAAO;MACP,MAAM;MACN,MAAM;MACN;IACF,CAAC;AAED,WAAO;EACT;AAEA,SAAO;AACT;AAKO,SAAS,cAAc,OAAoB,QAAiB,OAAgB;AACjF,MAAI,CAACM,YAAG,MAAM,KAAK,GAAG;AACpB,IAAAN,KAAI;MACF,OAAO;MACP,MAAM;MACN,MAAM;MACN;IACF,CAAC;AAED,WAAO;EACT;AAEA,SAAO,MAAM,MAAM,CAAA,MAAK,aAAa,GAAG,KAAK,CAAC;AAChD;AGzGA,IAAM,eAAsB;EAC1B,QAAQ;EACR,YAAY;EACZ,OAAO;EACP,WAAW,UAAU;EACrB,QAAQ;EACR,MAAM;EACN,QAAQX,QAAO;AACjB;AACA,IAAM,YAAY,WAAW,KAAK,cAAc,cAAc,MAAM,CAAC;AAErE,IAAM,QAAN,MAAY;EAOV,YAAY,SAAwB;AANpC,kBAAA,MAAQ,cAAA;AACR,kBAAA,MAAQ,eAAA;AACR,kBAAA,MAAQ,QAAyB,oBAAI,IAAI,CAAA;AACzC,kBAAA,MAAQ,UAAA;AACR,kBAAA,MAAQ,SAA0B,oBAAI,IAAI,CAAA;AAqG1C,kBAAA,MAAO,eAAc,CAAC,aAAuB;AAC3C,WAAK,WAAW;IAClB,CAAA;AAEA,kBAAA,MAAO,YAAW,CAACkB,WAAuB;AACxC,YAAM,EAAE,MAAM,OAAO,IAAI,KAAK,SAAS;AACvC,YAAM,QAAQ;QACZ,MAAMA,OAAM;QACZ;MACF;AAEA,WAAK,KAAK,IAAI,SAASA,MAAK;AAE5B,UAAI,WAAWlB,QAAO,WAAW,CAAC,QAAQkB,OAAM,QAAQ;AACtD,cAAM,SAASlB,QAAO;MACxB;AAEA,WAAK,SAAS,KAAK;IACrB,CAAA;AAeA,kBAAA,MAAO,aAAY,CAAC,SAAkD;AACpE,UAAI,SAAS,UAAU;AACrB,eAAO,KAAK;MACd;AAEA,aAAO,KAAK;IACd,CAAA;AAEA,kBAAA,MAAO,aAAY,CAAC,MAA4B,WAAuB;AACrE,UAAI,SAAS,UAAU;AACrB,aAAK,eAAe;MACtB,OAAO;AACL,aAAK,gBAAgB;MACvB;IACF,CAAA;AAEA,kBAAA,MAAO,kBAAiB,MAAM;AAC5B,WAAK,eAAe;AACpB,WAAK,gBAAgB;IACvB,CAAA;AAEA,kBAAA,MAAO,SAAQ,CAAC,SAAwB,SAAS;AAC/C,YAAM,EAAE,OAAO,OAAO,IAAI,KAAK,SAAS;AAExC,UAAI,WAAWA,QAAO,SAAS;AAC7B;MACF;AAEA,WAAK,SAAS;QACZ,GAAG,KAAK,aAAa,EAAE,QAAQ,QAAQ,OAAO,OAAO,QAAQ,GAAG,OAAO,CAAC;MAC1E,CAAC;IACH,CAAA;AAEA,kBAAA,MAAO,MAAK,CAAC,cAAsB;AACjC,YAAM,EAAE,YAAY,OAAO,IAAI,KAAK,SAAS;AAE7C,UAAI,cAAc,WAAWA,QAAO,SAAS;AAC3C;MACF;AAEA,YAAM,OAAO,KAAK,SAAS,EAAE,SAAS;AAEtC,WAAK,SAAS;QACZ,GAAG,KAAK,aAAa,EAAE,QAAQ,QAAQ,IAAI,OAAO,UAAU,CAAC;QAC7D,QAAQ,OAAO,SAASA,QAAO;MACjC,CAAC;IACH,CAAA;AAEA,kBAAA,MAAO,QAAO,MAAa,KAAK,SAAS,CAAA;AAEzC,kBAAA,MAAO,QAAO,MAAM;AAClB,YAAM,EAAE,OAAO,OAAO,IAAI,KAAK,SAAS;AAExC,UAAI,WAAWA,QAAO,SAAS;AAC7B;MACF;AAEA,WAAK,SAAS,KAAK,aAAa,EAAE,QAAQ,QAAQ,MAAM,OAAO,QAAQ,EAAE,CAAC,CAAC;IAC7E,CAAA;AAEA,kBAAA,MAAO,QAAO,MAAM;AAClB,YAAM,EAAE,OAAO,IAAI,KAAK,SAAS;AAEjC,UAAI,WAAWA,QAAO,SAAS;AAC7B;MACF;AAEA,WAAK,SAAS;QACZ,GAAG,KAAK,aAAa,EAAE,QAAQ,QAAQ,QAAQ,WAAW,UAAU,QAAQ,CAAC;MAC/E,CAAC;IACH,CAAA;AAEA,kBAAA,MAAO,QAAO,MAAM;AAClB,YAAM,EAAE,OAAO,OAAO,IAAI,KAAK,SAAS;AAExC,UAAI,WAAWA,QAAO,SAAS;AAC7B;MACF;AAEA,WAAK,SAAS;QACZ,GAAG,KAAK,aAAa,EAAE,QAAQ,QAAQ,MAAM,OAAO,QAAQ,EAAE,CAAC;MACjE,CAAC;IACH,CAAA;AAEA,kBAAA,MAAO,SAAQ,CAAC,UAAU,UAAU;AAClC,YAAM,EAAE,WAAW,IAAI,KAAK,SAAS;AAErC,UAAI,YAAY;AACd;MACF;AAEA,WAAK,SAAS;QACZ,GAAG,KAAK,aAAa,EAAE,QAAQ,QAAQ,OAAO,OAAO,EAAE,CAAC;QACxD,QAAQ,UAAUA,QAAO,UAAUA,QAAO;MAC5C,CAAC;IACH,CAAA;AAEA,kBAAA,MAAO,QAAO,MAAM;AAClB,YAAM,EAAE,OAAO,IAAI,KAAK,SAAS;AAEjC,UAAI,WAAWA,QAAO,SAAS;AAC7B;MACF;AAEA,WAAK,SAAS;QACZ,QAAQ,QAAQ;QAChB,WAAW,UAAU;QACrB,QAAQA,QAAO;MACjB,CAAC;IACH,CAAA;AAEA,kBAAA,MAAO,SAAQ,CAAC,cAAuB;AACrC,YAAM,EAAE,OAAO,KAAK,IAAI,KAAK,SAAS;AAEtC,WAAK,SAAS;QACZ,GAAG,KAAK;UACN;YACE,QAAQ,QAAQ;YAChB,OAAOiB,YAAG,OAAO,SAAS,IAAI,YAAY;UAC5C;UACA;QACF;QACA,QAAQ,OAAOjB,QAAO,UAAUA,QAAO;MACzC,CAAC;IACH,CAAA;AAEA,kBAAA,MAAO,QAAO,CAAC,UAAU,UAAU;AACjC,YAAM,EAAE,OAAO,OAAO,IAAI,KAAK,SAAS;AAExC,UAAK,CAACA,QAAO,UAAUA,QAAO,OAAO,EAAoB,SAAS,MAAM,GAAG;AACzE;MACF;AAEA,WAAK,SAAS;QACZ,GAAG,KAAK,aAAa,EAAE,QAAQ,QAAQ,MAAM,OAAO,SAAS,UAAU,IAAI,GAAG,CAAC;QAC/E,QAAQA,QAAO;MACjB,CAAC;IACH,CAAA;AAEA,kBAAA,MAAO,UAAS,CAAC,UAA0B;AA9S7C,UAAA,IAAA;AA+SI,UAAI,CAAC,aAAa,OAAO,SAAS,GAAG;AACnC,cAAM,IAAI,MAAM,mCAAmC,UAAU,KAAK,IAAI,CAAC,EAAE;MAC3E;AAEA,WAAK,SAAS;QACZ,GAAG,KAAK;UACN;YACE,GAAG,KAAK,SAAS;YACjB,GAAG;YACH,SAAQ,KAAA,MAAM,WAAN,OAAA,KAAgB,QAAQ;YAChC,SAAQ,KAAA,MAAM,WAAN,OAAA,KAAgB;UAC1B;UACA;QACF;MACF,CAAC;IACH,CAAA;AA9RE,UAAM,EAAE,aAAa,OAAO,WAAW,QAAQ,CAAC,EAAE,IAAI,WAAA,OAAA,UAAW,CAAC;AAElE,SAAK;MACH;QACE,QAAQ,QAAQ;QAChB,YAAYiB,YAAG,OAAO,SAAS;QAC/B;QACA,OAAOA,YAAG,OAAO,SAAS,IAAI,YAAY;QAC1C,WAAW,UAAU;QACrB,QAAQ;QACR,QAAQ,MAAM,SAASjB,QAAO,QAAQA,QAAO;MAC/C;MACA;IACF;AAEA,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,SAAS,KAAK;EACrB;EAEO,WAAkB;AACvB,QAAI,CAAC,KAAK,MAAM,MAAM;AACpB,aAAO,EAAE,GAAG,aAAa;IAC3B;AAEA,WAAO;MACL,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK;MACpC,YAAY,KAAK,MAAM,IAAI,YAAY,KAAK;MAC5C,OAAO,SAAS,KAAK,MAAM,IAAI,OAAO,GAAG,EAAE;MAC3C,WAAW,KAAK,MAAM,IAAI,WAAW,KAAK;MAC1C,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK;MACpC,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK;MAChC,QAAS,KAAK,MAAM,IAAI,QAAQ,KAAgB;IAClD;EACF;EAEQ,aAAa,OAAuB,QAAiB,OAAc;AArE7E,QAAA,IAAA,IAAA,IAAA,IAAA;AAsEI,UAAM,EAAE,QAAQ,YAAY,OAAO,MAAM,OAAO,IAAI,KAAK,SAAS;AAClE,UAAM,WAAWiB,YAAG,OAAO,MAAM,KAAK,IAAI,MAAM,QAAQ;AACxD,UAAM,YAAY,cAAc,CAAC,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,IAAI;AAErF,WAAO;MACL,SAAQ,KAAA,MAAM,WAAN,OAAA,KAAgB;MACxB;MACA,OAAO;MACP,YAAW,KAAA,MAAM,cAAN,OAAA,KAAmB,UAAU;MACxC,SAAQ,KAAA,MAAM,WAAN,OAAA,KAAgB;MACxB,OAAM,KAAA,MAAM,SAAN,OAAA,KAAc;MACpB,QAAQ,cAAc,OAAOjB,QAAO,YAAY,KAAA,MAAM,WAAN,OAAA,KAAgB;IAClE;EACF;EAEQ,WAAwB;AAC9B,UAAM,QAAQ,KAAK,KAAK,IAAI,OAAO;AAEnC,WAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC;EACzC;EAEQ,gBAAgB,UAA0B;AAChD,UAAM,SAAS,KAAK,UAAU,QAAQ;AACtC,UAAM,QAAQ,KAAK,UAAU,KAAK,SAAS,CAAC;AAE5C,WAAO,WAAW;EACpB;EAEQ,SAAS,WAAyC,UAAmB,OAAO;AAClF,UAAM,QAAQ,KAAK,SAAS;AAE5B,UAAM;MACJ;MACA;MACA;MACA,SAAS;MACT;MACA;IACF,IAAI;MACF,GAAG;MACH,GAAG;IACL;AAEA,SAAK,MAAM,IAAI,UAAU,MAAM;AAC/B,SAAK,MAAM,IAAI,SAAS,KAAK;AAC7B,SAAK,MAAM,IAAI,aAAa,SAAS;AACrC,SAAK,MAAM,IAAI,UAAU,MAAM;AAC/B,SAAK,MAAM,IAAI,QAAQ,IAAI;AAC3B,SAAK,MAAM,IAAI,UAAU,MAAM;AAE/B,QAAI,SAAS;AACX,WAAK,MAAM,IAAI,cAAc,UAAU,UAAU;AACjD,WAAK,MAAM,IAAI,cAAc,UAAU,UAAU;IACnD;AAEA,QAAI,KAAK,YAAY,KAAK,gBAAgB,KAAK,GAAG;AAChD,WAAK,SAAS,KAAK,SAAS,CAAC;IAC/B;EACF;EAsBO,aAA2B;AAChC,WAAO;MACL,OAAO,KAAK;MACZ,IAAI,KAAK;MACT,MAAM,KAAK;MACX,MAAM,KAAK;MACX,MAAM,KAAK;MACX,MAAM,KAAK;MACX,OAAO,KAAK;MACZ,MAAM,KAAK;IACb;EACF;AA8JF;AAIe,SAAR,YAA6B,SAAwB;AAC1D,SAAO,IAAI,MAAM,OAAO;AAC1B;AE/TA,SAAS,iBAAiB,EAAE,OAAO,GAAU;AAC3C,SACE;IAAC;IAAA;MACC,KAAI;MACJ,WAAU;MACV,gBAAa;MACb,OAAO;IAAA;EACT;AAEJ;AAEA,IAAO,oBAAQ;ADgBf,IAAqB,iBAArB,cAAkD,iBAA+B;EAAjF,cAAA;AAAA,UAAA,GAAA,SAAA;AACE,kBAAA,MAAA,YAAW,KAAA;AACX,kBAAA,MAAA,eAAA;AACA,kBAAA,MAAA,eAAA;AACA,kBAAA,MAAA,cAAA;AACA,kBAAA,MAAA,SAAQ;MACN,aAAa;MACb,oBAAoB;MACpB,eAAe;IACjB,CAAA;AAgEA,kBAAA,MAAA,iBAAgB,MAAM;AACpB,YAAM,EAAE,YAAY,gBAAgB,UAAU,IAAI,KAAK;AACvD,YAAM,mBAAmB;QACvB,UAAU;QACV,UAAU;QACV,UAAU;QACV,UAAU;MACZ;AAEA,aACE,mBACC,aAAa,iBAAiB,SAAS,SAAS,IAAI,cAAc,UAAU;IAEjF,CAAA;AA+CA,kBAAA,MAAA,mBAAkB,CAAC,UAAsB;AACvC,YAAM,EAAE,mBAAmB,IAAI,KAAK;AACpC,YAAM,EAAE,QAAQ,MAAM,UAAU,KAAK,MAAM,IAAI,KAAK;AAEpD,YAAM,UAAU,aAAa,UAAU,MAAM,UAAU,MAAM;AAC7D,YAAM,UAAU,aAAa,UAAU,MAAM,UAAU,MAAM;AAC7D,YAAM,oBAAoB,WAAW,OAAO,WAAW,MAAM;AAC7D,YAAM,mBAAmB,WAAW,QAAQ,WAAW,OAAO;AAC9D,YAAM,cAAc,oBAAoB;AAExC,UAAI,gBAAgB,oBAAoB;AACtC,aAAK,YAAY,EAAE,oBAAoB,YAAY,CAAC;MACtD;IACF,CAAA;AAEA,kBAAA,MAAA,gBAAe,MAAM;AACnB,YAAM,EAAE,OAAO,IAAI,KAAK;AACxB,YAAM,UAAU,WAAW,MAAM;AAEjC,UAAI,KAAK,iBAAiB,UAAU;AAClC,cAAM,EAAE,YAAY,IAAI,KAAK;AAE7B,YAAI,CAAC,aAAa;AAChB,eAAK,YAAY,EAAE,aAAa,MAAM,eAAe,MAAM,CAAC;QAC9D;AAEA,qBAAa,KAAK,aAAa;AAE/B,aAAK,gBAAgB,OAAO,WAAW,MAAM;AAC3C,eAAK,YAAY,EAAE,aAAa,OAAO,eAAe,KAAK,CAAC;QAC9D,GAAG,EAAE;MACP,WAAW,YAAY,SAAS,QAAQ,GAAG;AACzC,aAAK,YAAY,CAAC,CAAC;MACrB;IACF,CAAA;AAEA,kBAAA,MAAA,gBAAe,MAAM;AACnB,mBAAa,KAAK,aAAa;AAE/B,WAAK,gBAAgB,OAAO,WAAW,MAAM;AAC3C,YAAI,CAAC,KAAK,UAAU;AAClB;QACF;AAEA,aAAK,YAAY;MACnB,GAAG,GAAG;IACR,CAAA;EAAA;EAxKA,oBAAoB;AAClB,UAAM,EAAE,OAAO,kBAAkB,yBAAyB,OAAO,OAAO,IAAI,KAAK;AACjF,UAAM,UAAU,WAAW,MAAM;AAEjC,SAAK,eAAeI,iBAAgB,WAAA,OAAA,UAAW,SAAS,MAAM,wBAAwB,IAAI;AAC1F,SAAK,WAAW;AAEhB,QAAI,MAAuC;AACzC,UAAI,CAAC,oBAAoB,sBAAsB,SAAS,IAAI,GAAG;AAC7D,QAAAO,KAAI;UACF,OAAO;UACP,MAAM,CAAC,EAAE,KAAK,UAAU,OAAO,KAAK,aAAa,CAAC;UAClD;QACF,CAAC;MACH;IACF;AAEA,WAAO,iBAAiB,UAAU,KAAK,YAAY;EACrD;EAEA,mBAAmB,eAA6B;AAhElD,QAAA;AAiEI,UAAM,EAAE,wBAAwB,WAAW,iBAAiB,OAAO,IAAI,KAAK;AAC5E,UAAM,EAAE,QAAQ,IAAI,YAAY,eAAe,KAAK,KAAK;AAEzD,QAAI,QAAQ,QAAQ,KAAK,QAAQ,wBAAwB,GAAG;AAC1D,YAAM,UAAU,WAAW,MAAM;AAEjC,WAAK,eAAeP,iBAAgB,WAAA,OAAA,UAAW,SAAS,MAAM,wBAAwB,IAAI;IAC5F;AAEA,QAAI,QAAQ,aAAa,UAAU,OAAO,GAAG;AAC3C,OAAA,KAAA,KAAK,iBAAL,OAAA,SAAA,GAAmB,iBAAiB,UAAU,KAAK,cAAc,EAAE,SAAS,KAAK,CAAA;AAEjF,iBAAW,MAAM;AACf,cAAM,EAAE,YAAY,IAAI,KAAK;AAE7B,YAAI,CAAC,aAAa;AAChB,eAAK,YAAY,EAAE,eAAe,KAAK,CAAC;QAC1C;MACF,GAAG,GAAG;IACR;AAEA,QAAI,QAAQ,iBAAiB,KAAK,QAAQ,gBAAgB,KAAK,QAAQ,WAAW,GAAG;AACnF,UAAI,mBAAmB,cAAc,UAAU,SAAS;AACtD,eAAO,iBAAiB,aAAa,KAAK,iBAAiB,KAAK;MAClE,WAAW,cAAc,UAAU,SAAS;AAC1C,eAAO,oBAAoB,aAAa,KAAK,eAAe;MAC9D;IACF;EACF;EAEA,uBAAuB;AA/FzB,QAAA;AAgGI,SAAK,WAAW;AAEhB,WAAO,oBAAoB,aAAa,KAAK,eAAe;AAC5D,WAAO,oBAAoB,UAAU,KAAK,YAAY;AAEtD,iBAAa,KAAK,aAAa;AAC/B,iBAAa,KAAK,aAAa;AAC/B,KAAA,KAAA,KAAK,iBAAL,OAAA,SAAA,GAAmB,oBAAoB,UAAU,KAAK,YAAA;EACxD;EAiBA,IAAI,gBAAgB;AAClB,UAAM,EAAE,mBAAmB,IAAI,KAAK;AACpC,UAAM,EAAE,qBAAqB,WAAW,OAAO,IAAI,KAAK;AAExD,QAAI,aAAa,OAAO;AAExB,QAAI,SAAS,GAAG;AACd,mBAAa,cAAc,WAAW,OAAO,sBAAsB,OAAO;IAC5E;AAEA,WAAO;MACL,QAAQ,sBAAsB,YAAY;MAC1C,QAAQ,kBAAkB;MAC1B,eAAe,qBAAqB,SAAS;MAC7C,GAAG;IACL;EACF;EAEA,IAAI,kBAAmC;AA3IzC,QAAA,IAAA,IAAA;AA4II,UAAM,EAAE,cAAc,IAAI,KAAK;AAC/B,UAAM;MACJ,yBAAyB;MACzB;MACA,mBAAmB;MACnB;MACA;IACF,IAAI,KAAK;AACT,UAAM,UAAU,WAAW,MAAM;AACjC,UAAM,cAAcF,eAAc,OAAO;AACzC,UAAM,gBAAgB,YAAY,OAAO;AACzC,UAAM,MAAM,mBAAmB,SAAS,kBAAkB,sBAAsB;AAEhF,WAAO;MACL,GAAI,SAAS,IAAI,OAAO,kBAAkB,OAAO;MACjD,QAAQ,KAAK,QAAO,KAAA,eAAA,OAAA,SAAA,YAAa,WAAb,OAAA,KAAuB,KAAK,mBAAmB,CAAC;MACpE,MAAM,KAAK,QAAO,KAAA,eAAA,OAAA,SAAA,YAAa,SAAb,OAAA,KAAqB,KAAK,gBAAgB;MAC5D,SAAS,gBAAgB,IAAI;MAC7B,eAAe,kBAAkB,SAAS;MAC1C,UAAU,gBAAgB,UAAU;MACpC;MACA,YAAY;MACZ,OAAO,KAAK,QAAO,KAAA,eAAA,OAAA,SAAA,YAAa,UAAb,OAAA,KAAsB,KAAK,mBAAmB,CAAC;IACpE;EACF;EAkDA,YAAY,OAAuB;AACjC,QAAI,CAAC,KAAK,UAAU;AAClB;IACF;AAEA,SAAK,SAAS,CAAA,mBAAkB,EAAE,GAAG,eAAe,GAAG,MAAM,EAAE;EACjE;EAEA,SAAS;AACP,UAAM,EAAE,cAAc,IAAI,KAAK;AAC/B,UAAM,EAAE,gBAAgB,UAAU,IAAI,KAAK;AAC3C,UAAM,EAAE,eAAe,eAAe,gBAAgB,IAAI;AAE1D,QAAI,cAAc,GAAG;AACnB,aAAO;IACT;AAEA,QAAIiB,aAAY,cAAc,YAAY,iBACxC,qBAAC,mBAAA,EAAU,QAAQ,gBAAA,CAAiB;AAItC,QAAI,WAAW,MAAM,UAAU;AAC7B,YAAM,EAAE,cAAc,QAAQ,GAAG,cAAc,IAAI;AAEnDA,mBAAY,qBAAC,OAAA,EAAI,OAAO,EAAE,GAAG,cAAc,EAAA,GAAIA,UAAU;AACzD,aAAO,cAAc;IACvB;AAEA,WACE;MAAC;MAAA;QACC,WAAU;QACV,gBAAa;QACb,SAAS;QACT,MAAK;QACL,OAAO;MAAA;MAENA;IACH;EAEJ;AACF;AEpPA,IAAqB,gBAArB,cAAiD,kBAAiB;EAAlE,cAAA;AAAA,UAAA,GAAA,SAAA;AACE,kBAAA,MAAA,QAA2B,IAAA;EAAA;EAE3B,oBAAoB;AAClB,UAAM,EAAE,GAAG,IAAI,KAAK;AAEpB,QAAI,CAAClB,WAAU,GAAG;AAChB;IACF;AAEA,SAAK,OAAO,SAAS,cAAc,KAAK;AACxC,SAAK,KAAK,KAAK;AAEf,aAAS,KAAK,YAAY,KAAK,IAAI;AAEnC,QAAI,CAACO,YAAW;AACd,WAAK,cAAc;IACrB;EACF;EAEA,qBAAqB;AACnB,QAAI,CAACP,WAAU,GAAG;AAChB;IACF;AAEA,QAAI,CAACO,YAAW;AACd,WAAK,cAAc;IACrB;EACF;EAEA,uBAAuB;AACrB,QAAI,CAACP,WAAU,KAAK,CAAC,KAAK,MAAM;AAC9B;IACF;AAEA,QAAI,CAACO,YAAW;AAEL,MAAA,iCAAuB,KAAK,IAAI;IAC3C;AAEA,QAAI,KAAK,KAAK,eAAe,SAAS,MAAM;AAC1C,eAAS,KAAK,YAAY,KAAK,IAAI;AACnC,WAAK,OAAO;IACd;EACF;EAEA,gBAAgB;AACd,QAAI,CAACP,WAAU,GAAG;AAChB;IACF;AAEA,UAAM,EAAE,SAAS,IAAI,KAAK;AAE1B,QAAI,KAAK,MAAM;AACJ,MAAA,8CAAoC,MAAM,UAAU,KAAK,IAAI;IACxE;EACF;EAEA,gBAAgB;AACd,QAAI,CAACA,WAAU,KAAK,CAACO,YAAW;AAC9B,aAAO;IACT;AAEA,UAAM,EAAE,SAAS,IAAI,KAAK;AAE1B,QAAI,CAAC,KAAK,MAAM;AACd,aAAO;IACT;AAEA,WAAgB,uBAAa,UAAU,KAAK,IAAI;EAClD;EAEA,SAAS;AACP,QAAI,CAACA,YAAW;AACd,aAAO;IACT;AAEA,WAAO,KAAK,cAAc;EAC5B;AACF;AErFA,IAAqB,QAArB,MAA2B;EAIzB,YAAY,SAAsB,SAAuB;AAHzD,kBAAA,MAAA,SAAA;AACA,kBAAA,MAAA,SAAA;AAeA,kBAAA,MAAA,eAAc,CAACY,aAAkC;AAC/C,YAAM,EAAE,SAAS,IAAIA;AAErB,UAAI,aAAa,QAAQ,WAAW,GAAG;AACrC,eAAO;MACT;AAEA,aAAO,KAAK,aAAaA,QAAO;IAClC,CAAA;AAEA,kBAAA,MAAA,gBAAe,CAACA,aAAkC;AAChD,YAAM,gBAAgB;AACtB,YAAM,WAAWA,SAAQ,SAAS,YAAY;AAE9C,YAAM,UACH,cAAc,KAAK,QAAQ,KAAK,CAACA,SAAQ,aAAa,UAAU,KAChE,aAAa,OAAO,CAAC,CAACA,SAAQ,aAAa,MAAM;AAEpD,aAAO,WAAW,KAAK,UAAUA,QAAO;IAC1C,CAAA;AAEA,kBAAA,MAAA,wBAAuB,MACrB,CAAC,EAAE,MAAM,KAAK,KAAK,QAAQ,iBAAiB,GAAG,GAAG,CAAC,EAAE,OAAO,KAAK,WAAW,CAAA;AAE9E,kBAAA,MAAA,iBAAgB,CAAC,UAAyB;AACxC,YAAM,EAAE,OAAO,MAAM,IAAI,KAAK;AAE9B,UAAI,MAAM,SAAS,MAAM;AACvB,aAAK,aAAa,KAAK;MACzB;IACF,CAAA;AAEA,kBAAA,MAAA,gBAAe,CAAC,UAAyB;AACvC,YAAM,eAAe;AACrB,YAAM,WAAW,KAAK,qBAAqB;AAC3C,YAAM,EAAE,SAAS,IAAI;AAErB,UAAI,CAAC,SAAS,QAAQ;AACpB;MACF;AAEA,UAAI,IAAI,SAAS,gBAAgB,SAAS,QAAQ,SAAS,aAA4B,IAAI;AAE3F,UAAI,MAAM,MAAO,CAAC,YAAY,IAAI,MAAM,SAAS,QAAS;AACxD,YAAI;MACN,WAAW,YAAY,MAAM,GAAG;AAC9B,YAAI,SAAS,SAAS;MACxB,OAAO;AACL,aAAK,WAAW,KAAK;MACvB;AAEA,eAAS,CAAC,EAAE,MAAM;IACpB,CAAA;AAGA,kBAAA,MAAA,YAAW,CAACA,aAAyB;AACnC,YAAM,SAASA,SAAQ,eAAe,KAAKA,SAAQ,gBAAgB;AACnE,YAAM,QAAQ,OAAO,iBAAiBA,QAAO;AAE7C,UAAI,UAAU,CAACA,SAAQ,WAAW;AAChC,eAAO;MACT;AAEA,aACG,UAAU,MAAM,iBAAiB,UAAU,MAAM,aAClD,MAAM,iBAAiB,SAAS,MAAM;IAE1C,CAAA;AAEA,kBAAA,MAAA,aAAY,CAACA,aAAkC;AAC7C,UAAI,gBAAoCA;AAExC,aAAO,eAAe;AACpB,YAAI,yBAAyB,aAAa;AACxC,cAAI,kBAAkB,SAAS,MAAM;AACnC;UACF;AAEA,cAAI,KAAK,SAAS,aAAa,GAAG;AAChC,mBAAO;UACT;AAEA,0BAAgB,cAAc;QAChC;MACF;AAEA,aAAO;IACT,CAAA;AAEA,kBAAA,MAAA,eAAc,MAAM;AAClB,aAAO,oBAAoB,WAAW,KAAK,aAAa;IAC1D,CAAA;AAEA,kBAAA,MAAA,cAAa,CAAC,WAAwB;AACpC,UAAI,SAAS,kBAAkB,QAAQ;AACrC,eAAO,MAAM;AACb,eAAO,sBAAsB,MAAM,KAAK,WAAW,MAAM,CAAC;MAC5D;IACF,CAAA;AAEA,kBAAA,MAAA,YAAW,MAAM;AACf,YAAM,EAAE,SAAS,IAAI,KAAK;AAE1B,UAAI,CAAC,UAAU;AACb;MACF;AAEA,YAAM,SAAS,KAAK,QAAQ,cAAc,QAAQ;AAElD,UAAI,QAAQ;AACV,eAAO,sBAAsB,MAAM,KAAK,WAAW,MAAqB,CAAC;MAC3E;IACF,CAAA;AA5HE,QAAI,EAAE,mBAAmB,cAAc;AACrC,YAAM,IAAI,UAAU,mDAAmD;IACzE;AAEA,SAAK,UAAU;AACf,SAAK,UAAU;AAEf,WAAO,iBAAiB,WAAW,KAAK,eAAe,KAAK;AAE5D,SAAK,SAAS;EAChB;AAmHF;AChIA,IAAqB,gBAArB,cAAiD,iBAAuB;EAGtE,YAAY,OAAoB;AAC9B,UAAM,KAAK;AAHb,kBAAA,MAAQ,UAA6B,IAAA;AA2ErC,kBAAA,MAAA,gBAAe,CAAC,MAA0B;AACxC,WAAK,SAAS;IAChB,CAAA;AAxEE,QAAI,MAAM,iBAAiB;AACzB;IACF;AAEA,UAAM,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACrE,UAAM,QAAQ,SAAS,cAAc,OAAO;AAE5C,UAAM,KAAK;AAEX,QAAI,MAAM,OAAO;AACf,YAAM,aAAa,SAAS,MAAM,KAAK;IACzC;AAEA,UAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BZ,UAAM,YAAY,SAAS,eAAe,GAAG,CAAC;AAE9C,SAAK,YAAY,KAAK;EACxB;EAEA,oBAAoB;AAClB,UAAM,EAAE,YAAY,IAAI,KAAK;AAE7B,QAAI,MAAuC;AACzC,UAAI,CAACH,YAAG,WAAW,KAAK,MAAM,GAAG;AAC/B,gBAAQ,KAAK,mCAAmC;MAClD;IACF;AAEA,eAAW,MAAM;AACf,UAAIA,YAAG,WAAW,KAAK,MAAM,KAAK,aAAa;AAC7C,aAAK,OAAO,MAAM;MACpB;IACF,GAAG,CAAC;EACN;EAEA,uBAAuB;AACrB,UAAM,QAAQ,SAAS,eAAe,0BAA0B;AAEhE,QAAI,SAAA,OAAA,SAAA,MAAO,YAAY;AACrB,YAAM,WAAW,YAAY,KAAK;IACpC;EACF;EAMA,SAAS;AACP,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,IAAI,KAAK;AACT,UAAM,QAAQ,iBAAiB,OAAO,IAAI;AAC1C,UAAM,cAAc;MAClB,cAAc;MACd,SAAS;MACT,cAAc;MACd,KAAK,KAAK;MACV;IACF;AACA,QAAI;AAEJ,QAAI,iBAAiB;AACnB,YAAM,kBAAkB;AAExB,kBACE;QAAC;QAAA;UACC;UACA;UACA;UACA;UACA;UACC,GAAG;QAAA;MACN;IAEJ,OAAO;AACL,kBACE;QAAC;QAAA;UACC,KAAI;UACJ,WAAU;UACV,gBAAa;UACb,OAAO,OAAO;UACd,MAAK;UACJ,GAAG;QAAA;QAEJ,qBAAC,QAAA,EAAK,OAAO,OAAO,YAAA,CAAa;QACjC,qBAAC,QAAA,EAAK,OAAO,OAAO,YAAA,CAAa;MACnC;IAEJ;AAEA,WAAO;EACT;AACF;AGtIA,SAAS,0BAA0B,EAAE,QAAQ,GAAG,MAAM,GAAU;AAC9D,QAAM,EAAE,OAAO,QAAQ,OAAO,GAAG,MAAM,IAAI;AAE3C,SACEI,cAAAA,QAAA,cAAC,UAAA,EAAO,OAAc,MAAK,UAAU,GAAG,MAAA,GACtCA,cAAAA,QAAA;IAAC;IAAA;MACC,QAAQ,OAAO,WAAW,WAAW,GAAG,MAAM,OAAO;MACrD,qBAAoB;MACpB,SAAQ;MACR,SAAQ;MACR,OAAO,OAAO,UAAU,WAAW,GAAG,KAAK,OAAO;MAClD,OAAM;IAAA;IAENA,cAAAA,QAAA,cAAC,KAAA,MACCA,cAAAA,QAAA;MAAC;MAAA;QACC,GAAE;QACF,MAAM;MAAA;IACR,CACF;EACF,CACF;AAEJ;AAEA,IAAO,sBAAQ;ADtBf,SAAS,wBAAwB,OAA2B;AAC1D,QAAM,EAAE,WAAW,YAAY,OAAO,YAAY,cAAc,WAAW,MAAM,aAAa,IAC5F;AACF,QAAM,EAAE,SAAS,gBAAgB,iBAAiB,YAAY,gBAAgB,QAAQ,MAAM,IAC1F;AACF,QAAM,SAA0C,CAAC;AAEjD,SAAO,UACL;IAAC;IAAA;MACC,gBAAa;MACb,OAAO,OAAO;MACd,MAAK;MACJ,GAAG;IAAA;EACN;AAGF,MAAI,kBAAkB,CAAC,YAAY;AACjC,WAAO,OACL;MAAC;MAAA;QACC,aAAU;QACV,gBAAa;QACb,OAAO,OAAO;QACd,MAAK;QACJ,GAAG;MAAA;IACN;EAEJ;AAEA,MAAI,CAAC,kBAAkB,QAAQ,GAAG;AAChC,WAAO,OACL,qBAAC,UAAA,EAAO,gBAAa,eAAc,OAAO,OAAO,YAAY,MAAK,UAAU,GAAG,UAAA,CAAW;EAE9F;AAEA,SAAO,QAAQ,CAAC,mBACd,qBAAC,qBAAA,EAAY,gBAAa,gBAAe,QAAQ,OAAO,aAAc,GAAG,WAAA,CAAY;AAGvF,SACE;IAAC;IAAA;MACC,KAAI;MACJ,cAAY,iBAAiB,SAAA,OAAA,QAAS,OAAO;MAC7C,WAAU;MACV,OAAO,OAAO;MACb,GAAG;IAAA;IAEJ,qBAAC,OAAA,EAAI,OAAO,OAAO,iBAAA,GAChB,SACC,qBAAC,MAAA,EAAG,cAAY,iBAAiB,KAAK,GAAG,OAAO,OAAO,aAAA,GACpD,KACH,GAEF,qBAAC,OAAA,EAAI,OAAO,OAAO,eAAA,GAAiB,OAAQ,CAC9C;IACC,CAAC,cACA,qBAAC,OAAA,EAAI,OAAO,OAAO,cAAA,GACjB,qBAAC,OAAA,EAAI,OAAO,OAAO,oBAAA,GAAsB,OAAO,IAAK,GACpD,OAAO,MACP,OAAO,OACV;IAED,OAAO;EACV;AAEJ;AAEA,IAAO,oBAAQ;ADlEf,IAAqB,iBAArB,cAAkD,iBAAwB;EAA1E,cAAA;AAAA,UAAA,GAAA,SAAA;AACE,kBAAA,MAAA,mBAAkB,CAAC,UAAyC;AAC1D,YAAM,eAAe;AACrB,YAAM,EAAE,QAAQ,IAAI,KAAK;AAEzB,cAAQ,KAAK;IACf,CAAA;AAEA,kBAAA,MAAA,oBAAmB,CAAC,UAAyC;AAC3D,YAAM,eAAe;AACrB,YAAM,EAAE,QAAQ,IAAI,KAAK;AAEzB,cAAQ,MAAM,cAAc;IAC9B,CAAA;AAEA,kBAAA,MAAA,sBAAqB,CAAC,UAAyC;AAC7D,YAAM,eAAe;AACrB,YAAM,EAAE,YAAY,QAAQ,IAAI,KAAK;AAErC,UAAI,CAAC,YAAY;AACf,gBAAQ,MAAM,gBAAgB;AAE9B;MACF;AAEA,cAAQ,KAAK;IACf,CAAA;AAEA,kBAAA,MAAA,mBAAkB,CAAC,UAAyC;AAC1D,YAAM,eAAe;AACrB,YAAM,EAAE,QAAQ,IAAI,KAAK;AAEzB,cAAQ,KAAK;IACf,CAAA;AAEA,kBAAA,MAAA,oBAAmB,MAAM;AACvB,YAAM,EAAE,YAAY,OAAO,YAAY,eAAe,MAAM,KAAK,IAAI,KAAK;AAC1E,YAAM,EAAE,MAAM,OAAO,MAAM,MAAM,uBAAuB,KAAK,IAAI,KAAK;AAEtE,YAAM,WAAW,iBAAiB,IAAI;AACtC,YAAM,YAAY,iBAAiB,KAAK;AACxC,YAAM,WAAW,iBAAiB,IAAI;AACtC,YAAM,WAAW,iBAAiB,IAAI;AACtC,YAAM,WAAW,iBAAiB,IAAI;AAEtC,UAAI,UAAU;AACd,UAAI,cAAc;AAElB,UAAI,YAAY;AACd,kBAAU;AACV,sBAAc;AAEd,YAAI,KAAK,gBAAgB,CAAC,YAAY;AACpC,gBAAM,oBAAoB,iBAAiB,uBAAuB;YAChE,MAAM,QAAQ;YACd,OAAO;UACT,CAAC;AAED,oBAAU,qBAAqB,uBAAuB,QAAQ,GAAG,IAAI;AACrE,wBAAc;QAChB;AAEA,YAAI,YAAY;AACd,oBAAU;AACV,wBAAc;QAChB;MACF;AAEA,aAAO;QACL,WAAW;UACT,cAAc;UACd,UAAU;UACV,eAAe;UACf,SAAS,KAAK;UACd,MAAM;UACN,OAAO;QACT;QACA,YAAY;UACV,cAAc;UACd,UAAU;UACV,eAAe;UACf,SAAS,KAAK;UACd,MAAM;UACN,OAAO;QACT;QACA,cAAc;UACZ,cAAc;UACd,UAAU;UACV,eAAe;UACf,SAAS,KAAK;UACd,MAAM;UACN,OAAO;QACT;QACA,WAAW;UACT,cAAc;UACd,UAAU;UACV,eAAe;UACf,SAAS,KAAK;UACd,MAAM;UACN,OAAO;QACT;QACA,cAAc;UACZ,cAAc;UACd,KAAK;UACL,MAAM;QACR;MACF;IACF,CAAA;EAAA;EAEA,SAAS;AACP,UAAM,EAAE,YAAY,OAAO,YAAY,eAAe,MAAM,KAAK,IAAI,KAAK;AAC1E,UAAM,EAAE,iBAAiB,kBAAkB,GAAG,UAAU,IAAI;AAC5D,QAAI;AAEJ,QAAI,kBAAkB;AACpB,YAAM,cAAc;QAClB,GAAG,KAAK,iBAAiB;QACzB;QACA;QACA;QACA;QACA,MAAM;QACN;MACF;AAEA,YAAM,mBAAmB;AAEzB,kBAAY,qBAAC,kBAAA,EAAkB,GAAG,YAAA,CAAa;IACjD,OAAO;AACL,kBACE;QAAC;QAAA;UACE,GAAG,KAAK,iBAAiB;UAC1B;UACA;UACA;UACA;UACA;QAAA;MACF;IAEJ;AAEA,WAAO;EACT;AACF;AHtIA,IAAqB,cAArB,cAA+C,iBAAqB;EAApE,cAAA;AAAA,UAAA,GAAA,SAAA;AACE,kBAAA,MAAA,SAAsB,IAAA;AACtB,kBAAA,MAAA,WAA8B,IAAA;AA8I9B,kBAAA,MAAA,0BAAyB,CAAC,UAAyC;AACjE,YAAM,EAAE,MAAM,MAAM,IAAI,KAAK;AAE7B,UAAI,MAAM,SAAS,gBAAgB,KAAK,UAAU,SAAS;AACzD;MACF;AAEA,YAAM,OAAO,EAAE,WAAW,UAAU,QAAQ,CAAC;IAC/C,CAAA;AAEA,kBAAA,MAAA,iBAAgB,CAAC,YAAyB;AACxC,WAAK,UAAU;IACjB,CAAA;AAEA,kBAAA,MAAA,aAAuC,CAAC,QAAQ,SAAS;AA/K3D,UAAA;AAgLI,YAAM,EAAE,QAAQ,WAAW,MAAM,MAAM,IAAI,KAAK;AAEhD,UAAI,SAAS,WAAW;AACtB,cAAM,UAAU,UAAU,MAAM;MAClC,OAAO;AACL,cAAM,UAAU,WAAW,MAAM;MACnC;AAEA,UACE,MAAM,UAAU,QAAQ,MACvB,MAAM,UAAU,SAAS,KAAK,KAAK,cAAc,aAClD,cAAc,UAAU,MACxB;AACA,cAAM,OAAO;UACX;UACA,WAAW,UAAU;QACvB,CAAC;MACH;AAEA,WAAI,KAAA,KAAK,iBAAL,OAAA,SAAA,GAAmB,WAAW;AAChC,aAAK,aAAa,UAAU,QAAQ,IAAI;MAC1C;IACF,CAAA;AAQA,kBAAA,MAAA,iBAAgB,CAAC,gBAA6B;AAC5C,YAAM,EAAE,YAAY,SAAS,OAAO,MAAM,KAAK,IAAI,KAAK;AAExD,aACE;QAAC;QAAA;UACC;UACA;UACA;UACA,YAAY,QAAQ,MAAM;UAC1B,eAAe,KAAK;UACpB;UACA;UACC,GAAG;QAAA;MACN;IAEJ,CAAA;EAAA;EAxMA,oBAAoB;AAClB,UAAM,EAAE,OAAO,MAAM,IAAI,KAAK;AAE9B,IAAAV,KAAI;MACF,OAAO,QAAQ,KAAK;MACpB,MAAM,CAAC,EAAE,KAAK,SAAS,OAAO,KAAK,MAAM,CAAC;MAC1C;IACF,CAAC;EACH;EAEA,mBAAmB,eAA0B;AA/B/C,QAAA;AAgCI,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,cAAAW;MACA;MACA;MACA;IACF,IAAI,KAAK;AACT,UAAM,EAAE,SAAS,YAAY,IAAIC,YAAY,eAAe,KAAK,KAAK;AACtE,UAAM,QAAQ,QAAQ,KAAK;AAE3B,UAAM,aACJ,cAAc,WAAW,QAAQ,UAAU,QAAQ,KAAK,WAAW,QAAQ;AAC7E,UAAM,kBACJ,QAAQ,QAAQ,KAAK,QAAQ,OAAO,KAAK,QAAQ,WAAW,KAAK,QAAQ,QAAQ;AACnF,UAAM,YAAY,YAAY,aAAa,CAAC,UAAU,SAAS,UAAU,IAAI,GAAG,UAAU,IAAI;AAC9F,UAAM,gBAAgB,QAAQ,UAAU;MACtC,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;IACV,CAAC;AACD,UAAM,eAAe,cAAc,UAAU,cAAc;AAE3D,QAAI,kBAAkB,aAAa,eAAe;AAChD,eAAS;QACP,GAAG;QACH,OAAO,cAAc;QACrB,WAAW,UAAU;QACrB,MAAM,cAAc;QACpB,MAAM,OAAO;MACf,CAAC;IACH;AAEA,QACE,KAAK,cAAc,YACnB,WAAWvB,QAAO,WAClB,QAAQ,OAAO,KACf,WAAW,QAAQ,SACnB,cAAc,UAAU,MACxB;AACA,YAAM,OAAO,EAAE,WAAW,UAAU,MAAM,CAAC;IAC7C;AAEA,QAAI,iBAAiB;AACnB,YAAM,UAAU,WAAW,KAAK,MAAM;AACtC,YAAM,gBAAgB,CAAC,CAAC;AACxB,YAAM,oBAAoB,iBAAiB,iBAAiB,OAAO;AAEnE,UAAI,mBAAmB;AACrB,YACE,YAAY,UAAUA,QAAO,OAAOA,QAAO,OAAO,KAClD,YAAY,aAAa,UAAU,MAAM,UAAU,KAAK,GACxD;AACA,mBAAS;YACP,GAAG;YACH;YACA,MAAM,OAAO;UACf,CAAC;QACH;MACF,OAAO;AAEL,gBAAQ,KAAK,gBAAgB,uBAAuB,sBAAsB,IAAI;AAC9E,iBAAS;UACP,GAAG;UACH,MAAM,OAAO;UACb;QACF,CAAC;AAED,YAAI,CAAC,YAAY;AACf,gBAAM,OAAO,EAAE,OAAO,SAAS,WAAW,QAAQ,OAAO,KAAK,GAAG,CAAC;QACpE;MACF;IACF;AAEA,QAAI,YAAY,aAAa,UAAU,MAAM,UAAU,KAAK,GAAG;AAC7D,YAAM,OAAO;QACX,WAAW,WAAW,IAAI,KAAK,aAAa,UAAU,UAAU,UAAU;MAC5E,CAAC;IACH;AAEA,QAAI,QAAQ,OAAO,GAAG;AACpB,MAAAW,KAAI;QACF,OAAO,QAAQ,SAAS;QACxB,MAAM,CAAC,EAAE,KAAK,SAAS,OAAO,KAAK,MAAM,CAAC;QAC1C;MACF,CAAC;IACH;AAEA,QAAI,QAAQ,aAAa,UAAU,MAAM,GAAG;AAC1C,eAAS;QACP,GAAG;QACH;QACA,MAAM,OAAO;MACf,CAAC;IACH;AAEA,QAAI,QAAQ,aAAa,UAAU,OAAO,GAAG;AAC3C,eAAS;QACP,GAAG;QACH;QACA,MAAM,OAAO;MACf,CAAC;AAED,UAAIW,iBAAgB,KAAK,SAAS;AAChC,aAAK,QAAQ,IAAI,MAAM,KAAK,SAAS,EAAE,UAAU,wBAAwB,CAAC;AAC1E,aAAK,MAAM,SAAS;MACtB;IACF;AAEA,QAAI,YAAY,aAAa,CAAC,UAAU,SAAS,UAAU,IAAI,GAAG,UAAU,IAAI,GAAG;AACjF,OAAA,KAAA,KAAK,UAAL,OAAA,SAAA,GAAY,YAAA;AACZ,YAAM,eAAe;IACvB;EACF;EAEA,uBAAuB;AA1JzB,QAAA;AA2JI,KAAA,KAAA,KAAK,UAAL,OAAA,SAAA,GAAY,YAAA;EACd;EA4CA,IAAI,OAAO;AACT,UAAM,EAAE,WAAW,KAAK,IAAI,KAAK;AAEjC,WAAO,WAAW,IAAI,KAAK,cAAc,UAAU;EACrD;EAmBA,SAAS;AACP,UAAM,EAAE,YAAY,OAAO,OAAO,OAAO,cAAAA,eAAc,MAAM,KAAK,IAAI,KAAK;AAC3E,UAAM,SAAS,WAAW,KAAK,MAAM;AAErC,QAAI,CAAC,aAAa,IAAI,KAAK,CAACL,YAAG,WAAW,MAAM,GAAG;AACjD,aAAO;IACT;AAEA,WACE,qBAAC,OAAA,EAAI,KAAK,eAAe,KAAK,IAAI,WAAU,sBAAA,GAC1C;MAAC;MAAA;QACE,GAAG,KAAK;QACT,WAAW,KAAK;QAChB;QACA,WAAW,KAAK;QAChB,IAAI,sBAAsB,KAAK;QAC/B,MAAM,KAAK;QACX,WAAW,KAAK;QAChB,QAAQ,KAAK;MAAA;MAEb;QAAC;QAAA;UACC,iBAAiB,KAAK;UACtB;UACA;UACA,YAAY,QAAQ,MAAM;UAC1B,QAAQ,KAAK;UACb;UACA,gBAAgB,KAAK;UACrB,aAAaK;UACb;UACA;UACA,QAAQ,KAAK;QAAA;MACf;IACF,CACF;EAEJ;AACF;AVxOA,IAAM,UAAN,cAA4B,iBAAwB;EAMlD,YAAY,OAAc;AACxB,UAAM,KAAK;AANb,kBAAA,MAAiB,SAAA;AACjB,kBAAA,MAAiB,OAAA;AAyLjB,kBAAA,MAAA,YAAW,CAAC,SAAwB;AAClC,YAAM,EAAE,SAAS,IAAI,KAAK;AAE1B,UAAIL,YAAG,SAAS,QAAQ,GAAG;AACzB,iBAAS,IAAI;MACf;IACF,CAAA;AAKA,kBAAA,MAAA,kBAAiB,CAAC,UAAyB;AACzC,YAAM,EAAE,OAAO,UAAU,IAAI,KAAK;AAClC,YAAM,EAAE,MAAM,IAAI,KAAK;AACvB,YAAM,OAAO,MAAM,KAAK;AAExB,UAAI,cAAc,UAAU,SAAS;AACnC,YAAI,MAAM,SAAS,YAAY,QAAQ,CAAC,KAAK,mBAAmB;AAC9D,eAAK,MAAM,MAAM,UAAU;QAC7B;MACF;IACF,CAAA;AAEA,kBAAA,MAAA,sBAAqB,MAAM;AACzB,YAAM,EAAE,MAAM,IAAI,KAAK;AACvB,YAAM,EAAE,MAAM,IAAI,KAAK;AAEvB,YAAM,OAAO,cAAc,KAAK,OAAO,MAAM,KAAK,CAAC;AAEnD,UAAI,CAAC,KAAK,qBAAqB;AAC7B,aAAK,QAAQ,MAAM,SAAS;MAC9B;IACF,CAAA;AAKA,kBAAA,MAAA,aAAY,CAAC,UAAiB;AAC5B,WAAK,SAAS,KAAK;IACrB,CAAA;AAzNE,UAAM,EAAE,OAAO,YAAY,MAAM,MAAM,UAAU,IAAI;AAErD,SAAK,QAAQ,YAAY;MACvB,GAAG;MACH,YAAY,OAAOA,YAAG,OAAO,SAAS;IACxC,CAAC;AACD,SAAK,UAAU,KAAK,MAAM,WAAW;AAErC,UAAM,EAAE,YAAY,IAAI,KAAK;AAE7B,IAAAN,KAAI;MACF,OAAO;MACP,MAAM;QACJ,EAAE,KAAK,SAAS,OAAO,KAAK,MAAM;QAClC,EAAE,KAAK,SAAS,OAAO,KAAK,MAAM;MACpC;MACA;IACF,CAAC;AAGD,gBAAY,KAAK,SAAS;AAE1B,QAAI,YAAY;AACd,iBAAW,KAAK,OAAO;IACzB;AAEA,SAAK,QAAQ,KAAK,MAAM,SAAS;EACnC;EAEA,oBAAoB;AAClB,QAAI,CAACV,WAAU,GAAG;AAChB;IACF;AAEA,UAAM,EAAE,OAAO,mBAAmB,KAAK,MAAM,IAAI,KAAK;AACtD,UAAM,EAAE,MAAM,IAAI,KAAK;AAEvB,QAAI,cAAc,OAAO,KAAK,KAAK,KAAK;AACtC,YAAM;IACR;AAEA,QAAI,CAAC,mBAAmB;AACtB,eAAS,KAAK,iBAAiB,WAAW,KAAK,gBAAgB,EAAE,SAAS,KAAK,CAAC;IAClF;EACF;EAEA,mBAAmB,eAAsB,eAAsB;AAC7D,QAAI,CAACA,WAAU,GAAG;AAChB;IACF;AAEA,UAAM,EAAE,QAAQ,YAAY,OAAO,OAAO,IAAI,KAAK;AACnD,UAAM,EAAE,OAAO,KAAK,WAAW,MAAM,IAAI,KAAK;AAC9C,UAAM,EAAE,WAAW,mBAAmB,OAAO,cAAc,IAAI;AAC/D,UAAM,EAAE,OAAO,UAAU,OAAO,MAAM,QAAAuB,QAAO,IAAI,KAAK;AACtD,UAAM,EAAE,SAAS,aAAa,IAAID,YAAY,eAAe,KAAK,KAAK;AACvE,UAAM,EAAE,SAAS,YAAY,IAAIA,YAAY,eAAe,KAAK,KAAK;AACtE,UAAM,OAAO,cAAc,KAAK,OAAO,MAAM,KAAK,CAAC;AAEnD,UAAM,eAAe,CAAC,MAAQ,eAAe,KAAK;AAClD,UAAM,mBAAmBN,YAAG,OAAO,SAAS,KAAK,aAAa,WAAW;AACzE,UAAM,SAAS,WAAW,KAAK,MAAM;AAErC,QAAI,cAAc;AAChB,UAAI,cAAc,OAAO,KAAK,GAAG;AAC/B,iBAAS,KAAK;MAChB,OAAO;AAEL,gBAAQ,KAAK,uBAAuB,KAAK;MAC3C;IACF;AAEA,QAAI,aAAa,KAAK,GAAG;AACvB,UAAI,KAAK;AACP,cAAM,SAAS;MACjB,OAAO;AACL,aAAK;MACP;IACF;AAEA,QAAI,kBAAkB;AACpB,UAAI,aACFA,YAAG,OAAO,iBAAiB,KAAK,oBAAoB,YAAY,QAAQ,OAAO,QAAQ;AAEzF,UAAI,WAAW,QAAQ,MAAM;AAC3B,qBAAa,QAAQ;MACvB;AAEA,UAAI,CAAE,CAACjB,QAAO,UAAUA,QAAO,OAAO,EAAoB,SAAS,MAAM,GAAG;AAC1E,QAAAwB,QAAO;UACL,QAAQ,WAAW,QAAQ,QAAQ,QAAQ,QAAQ;UACnD,OAAO;UACP,WAAW,UAAU;QACvB,CAAC;MACH;IACF;AAGA,QAAI,CAAC,cAAc,WAAWxB,QAAO,WAAW,UAAU,KAAK,CAAC,QAAQ;AACtE,WAAK,MAAM,OAAO,EAAE,OAAO,QAAQ,EAAE,CAAC;AACtC,WAAK,SAAS;QACZ,GAAG,KAAK;QACR,MAAM,OAAO;QACb;MACF,CAAC;IACH;AAEA,UAAM,eAAe;MACnB,GAAG,KAAK;MACR;MACA;IACF;AACA,UAAM,gBAAgB,QAAQ,UAAU;MACtC,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;IACV,CAAC;AAED,QAAI,iBAAiB,QAAQ,UAAUA,QAAO,MAAM,GAAG;AACrD,YAAM,eAAe,cAAc,KAAK,OAAO,MAAM,cAAc,KAAK,CAAC;AAEzE,WAAK,SAAS;QACZ,GAAG;QACH,OAAO,cAAc;QACrB,WAAW,UAAU;QACrB,MAAM;QACN,MAAM,OAAO;MACf,CAAC;IACH;AAEA,QAAI,QAAQ,UAAU,CAACA,QAAO,UAAUA,QAAO,OAAO,CAAC,GAAG;AACxD,YAAM,eAAe,cAAc,KAAK,OAAO,MAAM,cAAc,KAAK,CAAC;AAEzE,UAAI,CAAC,YAAY;AACf,aAAK,SAAS;UACZ,GAAG;UACH,OAAO,cAAc;UACrB,WAAW,UAAU;UACrB,MAAM;UACN,MAAM,OAAO;QACf,CAAC;MACH;AAEA,WAAK,SAAS;QACZ,GAAG;QACH,MAAM,OAAO;;QAEb,MAAM;QACN,OAAO,cAAc;MACvB,CAAC;AACD,YAAM;IACR,WAAW,YAAY,UAAU,CAACA,QAAO,MAAMA,QAAO,KAAK,GAAGA,QAAO,OAAO,GAAG;AAC7E,WAAK,SAAS;QACZ,GAAG;QACH,MAAM,OAAO;MACf,CAAC;IACH,WAAW,QAAQ,QAAQ,KAAK,QAAQ,UAAU,QAAQ,KAAK,GAAG;AAChE,WAAK,SAAS;QACZ,GAAG;QACH,MAAM,OAAO;MACf,CAAC;IACH;AAEA,SAAK,aAAa,aAAa;EACjC;EAEA,uBAAuB;AACrB,UAAM,EAAE,kBAAkB,IAAI,KAAK;AAEnC,QAAI,CAAC,mBAAmB;AACtB,eAAS,KAAK,oBAAoB,WAAW,KAAK,cAAc;IAClE;EACF;EA8CA,aAAa,eAAsB;AACjC,UAAM,EAAE,OAAO,WAAW,OAAO,IAAI,KAAK;AAC1C,UAAM;MACJ;MACA,yBAAyB;MACzB;MACA,eAAe;MACf,oBAAoB;MACpB;IACF,IAAI,KAAK;AACT,UAAM,OAAO,cAAc,KAAK,OAAO,MAAM,KAAK,CAAC;AAEnD,UAAM,SAAS,WAAW,KAAK,MAAM;AACrC,UAAM,qBAAqB,aAAa;MACtC,aAAa,UAAU;MACvB;MACA,mBAAmB,cAAc;MACjC;MACA;MACA;IACF,CAAC;AAED,QAAI,WAAWA,QAAO,WAAW,oBAAoB;AACnD,YAAM,kBAAkB,sBAAsB,QAAQ,sBAAsB;AAC5E,YAAMK,gBAAeD,iBAAgB,QAAQ,sBAAsB;AACnE,UAAI,UAAU,KAAK,MAAM,YAAY,QAAQ,cAAc,sBAAsB,CAAC,KAAK;AAEvF,MAAAO,KAAI;QACF,OAAO;QACP,MAAM;UACJ,EAAE,KAAK,SAAS,OAAO,MAAM;UAC7B,EAAE,KAAK,aAAa,OAAO,UAAU;UACrC,EAAE,KAAK,UAAU,OAAO,OAAO;QACjC;QACA;MACF,CAAC;AAED,YAAM,eAAe,KAAK,MAAM,UAAU,QAAQ;AAClD,YAAM,gBAAgB,KAAK,MAAM,UAAU,SAAS;AAEpD,UAAI,cAAc,UAAU,UAAU,cAAc;AAClD,cAAM,EAAE,SAAS,UAAU,IAAI;AAE/B,YAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,SAAS,KAAK,CAAC,iBAAiB;AACvD,oBAAU,KAAK,MAAM,QAAQ,OAAO,MAAM,YAAY;QACxD;MACF,WAAW,cAAc,UAAU,WAAW,eAAe;AAC3D,cAAM,EAAE,SAAS,SAAS,UAAU,IAAI;AAExC,YAAI,CAAC,OAAO,SAAS,MAAM,EAAE,SAAS,SAAS,KAAK,CAAC,WAAW,CAAC,iBAAiB;AAChF,oBAAU,KAAK,MAAM,QAAQ,OAAO,MAAM,YAAY;QACxD,OAAO;AACL,qBAAW,KAAK;QAClB;MACF;AAEA,gBAAU,WAAW,IAAI,UAAU;AAEnC,UAAI,WAAWX,QAAO,SAAS;AAC7B,iBAAS,SAAS,EAAE,SAASK,eAAyB,UAAU,eAAe,CAAC,EAAE;UAChF,MAAM;AACJ,uBAAW,MAAM;AA7T7B,kBAAA;AA8Tc,eAAA,KAAA,KAAK,MAAM,UAAU,SAAS,MAA9B,OAAA,SAAA,GAAiC,SAAS,OAAA;YAC5C,GAAG,EAAE;UACP;QACF;MACF;IACF;EACF;EAEA,SAAS;AACP,QAAI,CAACJ,WAAU,GAAG;AAChB,aAAO;IACT;AAEA,UAAM,EAAE,OAAO,WAAW,OAAO,IAAI,KAAK;AAC1C,UAAM;MACJ,aAAa;MACb,QAAQ;MACR;MACA,oBAAoB;MACpB;IACF,IAAI,KAAK;AACT,UAAM,YAAY,WAAWD,QAAO;AACpC,UAAM,UAAqC,CAAC;AAE5C,QAAI,aAAa,MAAM,KAAK,GAAG;AAC7B,YAAM,OAAO,cAAc,KAAK,OAAO,MAAM,KAAK,CAAC;AAEnD,cAAQ,OACN;QAAC;QAAA;UACE,GAAG,KAAK;UACT,UAAU,KAAK;UACf;UACA;UACA,SAAS,KAAK;UACd;UACA,cAAc,CAAC,KAAK,qBAAqB,UAAU,KAAK;UACxD;UACA,OAAO,KAAK;QAAA;MACd;AAGF,cAAQ,UACN,qBAAC,eAAA,EAAO,IAAG,uBAAA,GACT;QAAC;QAAA;UACE,GAAG;UACJ;UACA;UACA;UACA,gBAAgB,KAAK;QAAA;MACvB,CACF;IAEJ;AAEA,WACE,qBAAC,OAAA,EAAI,WAAU,gBAAA,GACZ,QAAQ,MACR,QAAQ,OACX;EAEJ;AACF;AA3VE,cAJI,SAIG,gBAAe,YAAA;AA6VxB,IAAO,qBAAQ;", "names": ["scroll", "noop", "scrollParent", "innerText", "isMergeableObject", "deepmerge", "isValidElement", "is", "i", "checker", "isOfType", "is", "equal", "import_react", "import_react_dom", "import_deepmerge", "import_deepmerge", "React", "React3", "ReactDOM", "window", "document", "navigator", "timeoutDuration", "longerTimeoutBrowsers", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "userAgent", "indexOf", "microtaskDebounce", "fn", "called", "Promise", "resolve", "then", "taskDebounce", "scheduled", "supportsMicroTasks", "isFunction", "functionToCheck", "getType", "toString", "call", "getStyleComputedProperty", "element", "property", "nodeType", "ownerDocument", "defaultView", "css", "getComputedStyle", "getParentNode", "nodeName", "parentNode", "host", "getScrollParent", "body", "overflow", "overflowX", "overflowY", "test", "getReferenceNode", "reference", "referenceNode", "isIE11", "MSInputMethodContext", "documentMode", "isIE10", "isIE", "version", "getOffsetParent", "documentElement", "noOffsetParent", "offsetParent", "nextElement<PERSON><PERSON>ling", "isOffsetContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "element1root", "getScroll", "side", "upperSide", "html", "scrollingElement", "includeScroll", "rect", "subtract", "scrollTop", "scrollLeft", "modifier", "top", "bottom", "left", "right", "getBordersSize", "styles", "axis", "sideA", "sideB", "parseFloat", "getSize", "computedStyle", "Math", "max", "parseInt", "getWindowSizes", "getClientRect", "offsets", "width", "height", "getBoundingClientRect", "e", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getOffsetRectRelativeToArbitraryNode", "children", "parent", "fixedPosition", "runIsIE", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "excludeScroll", "relativeOffset", "innerWidth", "innerHeight", "offset", "isFixed", "getFixedPositionOffsetParent", "parentElement", "el", "getBoundaries", "popper", "padding", "boundariesElement", "boundaries", "boundariesNode", "isPaddingNumber", "getArea", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "Object", "keys", "map", "key", "sort", "a", "b", "area", "filtered<PERSON><PERSON>s", "filter", "computedPlacement", "variation", "split", "getReferenceOffsets", "state", "commonOffsetParent", "getOuterSizes", "x", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "replace", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "find", "arr", "check", "Array", "prototype", "findIndex", "prop", "value", "cur", "match", "obj", "runModifiers", "modifiers", "data", "ends", "modifiersToRun", "undefined", "slice", "for<PERSON>ach", "warn", "enabled", "update", "isDestroyed", "options", "positionFixed", "flip", "originalPlacement", "position", "isCreated", "onCreate", "onUpdate", "isModifierEnabled", "modifierName", "some", "name", "getSupportedPropertyName", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "prefix", "to<PERSON><PERSON><PERSON>", "style", "destroy", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "disableEventListeners", "removeOnDestroy", "<PERSON><PERSON><PERSON><PERSON>", "getWindow", "attachToScrollParents", "event", "callback", "scrollParents", "isBody", "target", "addEventListener", "passive", "push", "setupEventListeners", "updateBound", "scrollElement", "eventsEnabled", "enableEventListeners", "scheduleUpdate", "removeEventListeners", "removeEventListener", "isNumeric", "n", "isNaN", "isFinite", "setStyles", "unit", "setAttributes", "attributes", "setAttribute", "applyStyle", "instance", "arrowElement", "arrowStyles", "applyStyleOnLoad", "modifierOptions", "getRoundedOffsets", "shouldRound", "round", "floor", "noRound", "v", "referenceWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isVertical", "isVariation", "sameWidthParity", "bothOddWidth", "horizontalToInteger", "verticalToInteger", "isFirefox", "computeStyle", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "devicePixelRatio", "prefixedProperty", "invertTop", "invertLeft", "arrow", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "requested", "querySelector", "len", "sideCapitalized", "toLowerCase", "altSide", "opSide", "arrowElementSize", "center", "popperMarginSide", "popperBorderSide", "sideValue", "min", "getOppositeVariation", "validPlacements", "placements", "clockwise", "counter", "index", "concat", "reverse", "BEHAVIORS", "flipped", "placementOpposite", "flipOrder", "behavior", "FLIP", "CLOCKWISE", "COUNTERCLOCKWISE", "step", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariationByRef", "flipVariations", "flippedVariationByContent", "flipVariationsByContent", "flippedVariation", "keepTogether", "toValue", "str", "size", "parseOffset", "basePlacement", "useHeight", "fragments", "frag", "trim", "divider", "search", "splitRegex", "ops", "op", "mergeWithPrevious", "reduce", "index2", "preventOverflow", "transformProp", "popperStyles", "transform", "priority", "escapeWithReference", "shift", "shiftvariation", "shiftOffsets", "hide", "bound", "inner", "subtractLength", "<PERSON><PERSON>", "requestAnimationFrame", "debounce", "bind", "De<PERSON>ults", "j<PERSON>y", "onLoad", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DOM_PROPERTIES_TO_CHECK", "objectTypes", "primitiveTypes", "getObjectType", "isObjectType", "isObjectOfType", "isOfType", "isPrimitiveType", "is", "isOfType", "isFunction", "isNull", "isRegex", "isObject", "isUndefined", "equalArray", "equal", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "equalMap", "equalSet", "isObject", "isRegex", "canHaveLength", "checkEquality", "isSameType", "hasValue", "hasEntry", "compareNumbers", "nested", "compareValues", "equal", "isEqualPredicate", "hasExtraKeys", "getIterables", "includesOrEqualsTo", "treeChanges", "compareValues", "nested", "includesOrEqualsTo", "equal", "compareNumbers", "getIterables", "r", "_getPrototypeOf", "o", "_setPrototypeOf", "p", "ReactDOM", "ReactFloaterPortal", "React", "PropTypes", "FloaterArrow", "arrow", "Floater", "ReactFloaterWrapper", "deepmerge", "ReactFloater", "treeChanges", "offset", "flip", "onCreate", "onUpdate", "import_react", "STATUS", "canUseDOM", "getClientRect", "getStyleComputedProperty", "getScrollParent", "scrollParent", "offset", "scroll", "isReact16", "getObjectType", "innerText", "log", "noop", "defaultOptions", "getStyles", "deepmerge", "hideBeacon", "is", "steps", "spotlight", "element", "React", "shouldScroll", "treeChanges", "update"]}