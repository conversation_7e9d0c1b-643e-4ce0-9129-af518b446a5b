import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
后端测试运行脚本
提供统一的测试执行入口和报告生成
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import List, Optional


def run_command(cmd: List[str], cwd: Optional[str] = None) -> int:
    """运行命令并返回退出码"""
    logger.info(f"执行命令: {' '.join(cmd)}")
    if cwd:
        logger.info(f"工作目录: {cwd}")
    
    try:
        result = subprocess.run(
            cmd,
            cwd=cwd,
            check=False,
            capture_output=False
        )
        return result.returncode
    except Exception as e:
        logger.info(f"命令执行失败: {e}")
        return 1


def setup_test_environment():
    """设置测试环境"""
    logger.info("🔧 设置测试环境...")
    
    # 设置环境变量
    os.environ['TESTING'] = 'true'
    os.environ['LOG_LEVEL'] = 'DEBUG'
    
    # 创建必要的目录
    directories = ['reports', 'logs', 'data/test']
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    logger.info("✅ 测试环境设置完成")


def run_unit_tests(coverage: bool = False, verbose: bool = False) -> int:
    """运行单元测试"""
    logger.info("🧪 运行单元测试...")
    
    cmd = ['python', '-m', 'pytest', 'tests/', '-m', 'unit']
    
    if coverage:
        cmd.extend(['--cov=app', '--cov-report=html', '--cov-report=term'])
    
    if verbose:
        cmd.append('-v')
    
    return run_command(cmd)


def run_integration_tests(verbose: bool = False) -> int:
    """运行集成测试"""
    logger.info("🔗 运行集成测试...")
    
    cmd = ['python', '-m', 'pytest', 'tests/', '-m', 'integration']
    
    if verbose:
        cmd.append('-v')
    
    return run_command(cmd)


def run_api_tests(verbose: bool = False) -> int:
    """运行API测试"""
    logger.info("🌐 运行API测试...")
    
    cmd = ['python', '-m', 'pytest', 'tests/', '-m', 'api']
    
    if verbose:
        cmd.append('-v')
    
    return run_command(cmd)


def run_websocket_tests(verbose: bool = False) -> int:
    """运行WebSocket测试"""
    logger.info("🔌 运行WebSocket测试...")
    
    cmd = ['python', '-m', 'pytest', 'tests/', '-m', 'websocket']
    
    if verbose:
        cmd.append('-v')
    
    return run_command(cmd)


def run_performance_tests(verbose: bool = False) -> int:
    """运行性能测试"""
    logger.info("⚡ 运行性能测试...")
    
    cmd = ['python', '-m', 'pytest', 'tests/', '-m', 'performance']
    
    if verbose:
        cmd.append('-v')
    
    return run_command(cmd)


def run_all_tests(coverage: bool = False, verbose: bool = False) -> int:
    """运行所有测试"""
    logger.info("🚀 运行所有测试...")
    
    cmd = ['python', '-m', 'pytest', 'tests/']
    
    if coverage:
        cmd.extend(['--cov=app', '--cov-report=html', '--cov-report=term'])
    
    if verbose:
        cmd.append('-v')
    
    return run_command(cmd)


def generate_test_report():
    """生成测试报告"""
    logger.info("📊 生成测试报告...")
    
    # 生成HTML覆盖率报告
    if Path('htmlcov').exists():
        logger.info("✅ HTML覆盖率报告已生成: htmlcov/index.html")
    
    # 生成JUnit XML报告（如果配置了）
    if Path('reports/junit.xml').exists():
        logger.info("✅ JUnit XML报告已生成: reports/junit.xml")
    
    logger.info("📋 测试报告生成完成")


def check_dependencies():
    """检查测试依赖"""
    logger.info("🔍 检查测试依赖...")
    
    required_packages = [
        'pytest',
        'pytest-asyncio',
        'httpx',
        'pytest-cov'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.info(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        logger.info("请运行: pip install -r requirements.txt")
        return False
    
    logger.info("✅ 所有测试依赖已安装")
    return True


def cleanup_test_artifacts():
    """清理测试产物"""
    logger.info("🧹 清理测试产物...")
    
    # 清理缓存
    cache_dirs = ['.pytest_cache', '__pycache__', '.coverage']
    for cache_dir in cache_dirs:
        if Path(cache_dir).exists():
            subprocess.run(['rm', '-rf', cache_dir], check=False)
    
    # 清理测试数据库
    test_db_files = Path('.').glob('test_*.db')
    for db_file in test_db_files:
        db_file.unlink(missing_ok=True)
    
    logger.info("✅ 测试产物清理完成")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='后端测试运行脚本')
    
    # 测试类型选项
    parser.add_argument('--unit', action='store_true', help='只运行单元测试')
    parser.add_argument('--integration', action='store_true', help='只运行集成测试')
    parser.add_argument('--api', action='store_true', help='只运行API测试')
    parser.add_argument('--websocket', action='store_true', help='只运行WebSocket测试')
    parser.add_argument('--performance', action='store_true', help='只运行性能测试')
    parser.add_argument('--all', action='store_true', help='运行所有测试（默认）')
    
    # 其他选项
    parser.add_argument('--coverage', action='store_true', help='生成覆盖率报告')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--cleanup', action='store_true', help='清理测试产物')
    parser.add_argument('--check-deps', action='store_true', help='检查测试依赖')
    
    args = parser.parse_args()
    
    # 检查依赖
    if args.check_deps or not check_dependencies():
        if not check_dependencies():
            sys.exit(1)
        return
    
    # 清理测试产物
    if args.cleanup:
        cleanup_test_artifacts()
        return
    
    # 设置测试环境
    setup_test_environment()
    
    start_time = time.time()
    exit_code = 0
    
    try:
        # 根据参数运行相应的测试
        if args.unit:
            exit_code = run_unit_tests(args.coverage, args.verbose)
        elif args.integration:
            exit_code = run_integration_tests(args.verbose)
        elif args.api:
            exit_code = run_api_tests(args.verbose)
        elif args.websocket:
            exit_code = run_websocket_tests(args.verbose)
        elif args.performance:
            exit_code = run_performance_tests(args.verbose)
        else:
            # 默认运行所有测试
            exit_code = run_all_tests(args.coverage, args.verbose)
        
        # 生成测试报告
        if args.coverage or exit_code == 0:
            generate_test_report()
        
    except KeyboardInterrupt:
        logger.info("\n❌ 测试被用户中断")
        exit_code = 130
    except Exception as e:
        logger.info(f"❌ 测试执行出错: {e}")
        exit_code = 1
    
    end_time = time.time()
    duration = end_time - start_time
    
    logger.info(f"\n⏱️  测试总耗时: {duration:.2f}秒")
    
    if exit_code == 0:
        logger.info("✅ 所有测试通过")
    else:
        logger.info(f"❌ 测试失败，退出码: {exit_code}")
    
    sys.exit(exit_code)


if __name__ == '__main__':
    main()