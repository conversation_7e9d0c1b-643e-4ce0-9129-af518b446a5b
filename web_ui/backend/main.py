"""
量化交易系统 FastAPI 后端主应用 - 统一架构版本
集成统一服务总线和API路由管理
"""

import sys
import os
from pathlib import Path

# 确定项目根目录并添加到Python路径
CURRENT_FILE = Path(__file__).resolve()
BACKEND_DIR = CURRENT_FILE.parent  # web_ui/backend/
PROJECT_ROOT = BACKEND_DIR.parent.parent  # 项目根目录

# 添加项目根目录到Python路径以访问统一架构
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

# 添加后端目录到Python路径以访问app模块
if str(BACKEND_DIR) not in sys.path:
    sys.path.insert(0, str(BACKEND_DIR))

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Request
from fastapi.responses import Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import uvicorn
import logging
import uuid
import json
from datetime import datetime

from app.core.config import settings
from app.api.routes import api_router
from app.core.websocket import websocket_manager, websocket_handler
from app.middleware.error_handler import ErrorHandlerMiddleware, RequestValidationMiddleware
from app.services.async_wrapper import background_task_manager
from app.api.endpoints.websocket import realtime_service
from app.services.risk_service import risk_monitoring_service

# 导入统一架构组件
try:
    from src.core.engine.service_bus import get_service_bus
    from src.core.engine.unified_api_router import get_unified_router
except ImportError as e:
    logger.info(f"无法导入统一架构组件: {e}")
    get_service_bus = None
    get_unified_router = None

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理 - 统一架构版本"""
    # 启动时初始化
    logger.info("🚀 启动统一架构量化交易系统后端服务...")
    
    # 初始化统一服务总线
    try:
        logger.info("正在初始化统一服务总线...")
        service_bus = get_service_bus()
        app.state.service_bus = service_bus
        
        # 验证服务总线健康状态
        if service_bus.check_health():
            logger.info("✅ 统一服务总线初始化成功")
        else:
            logger.warning("⚠️  统一服务总线健康检查发现问题，但服务仍可运行")
            
    except Exception as e:
        logger.error(f"❌ 统一服务总线初始化失败: {e}")
        # 继续启动，但记录错误
    
    # 初始化WebSocket管理器
    await websocket_manager.startup()
    
    # 启动实时数据推送服务
    await realtime_service.start()
    
    # 启动风险监控服务
    try:
        await risk_monitoring_service.start()
        logger.info("✅ 风险监控服务启动成功")
    except Exception as e:
        logger.error(f"❌ 风险监控服务启动失败: {e}")
    
    # 将服务总线的核心组件存储到应用状态中（向后兼容）
    try:
        if hasattr(app.state, 'service_bus'):
            app.state.data_manager = service_bus.get_service('data_manager')
            app.state.backtest_engine = service_bus.get_service('backtest_engine')
            app.state.config_manager = service_bus.get_service('config_manager')
            app.state.error_handler = service_bus.get_service('error_handler')
            
            logger.info("✅ 统一架构核心组件集成完成")
        else:
            # 后备方案：直接初始化核心组件
            from src.market.data.manager import DataManager
            from src.backtest.engine import BacktestEngine
            
            app.state.data_manager = DataManager(use_sqlite=True)
            app.state.backtest_engine = BacktestEngine()
            
            logger.info("✅ 核心组件初始化完成（后备模式）")
            
    except Exception as e:
        logger.error(f"❌ 核心组件初始化失败: {e}")
        # 不阻止应用启动，但记录错误
    
    logger.info("🎯 统一架构量化交易系统后端服务启动完成")
    
    yield
    
    # 关闭时清理
    logger.info("🛑 关闭统一架构量化交易系统后端服务...")
    
    # 关闭统一服务总线
    if hasattr(app.state, 'service_bus'):
        try:
            app.state.service_bus.shutdown()
            logger.info("✅ 统一服务总线已关闭")
        except Exception as e:
            logger.error(f"❌ 统一服务总线关闭失败: {e}")
    
    await risk_monitoring_service.stop()
    await realtime_service.stop()
    await websocket_manager.shutdown()
    await background_task_manager.shutdown()
    
    logger.info("✅ 所有服务已安全关闭")

# 创建FastAPI应用
app = FastAPI(
    title="量化交易系统API - 统一架构版本",
    description="基于统一服务总线架构的现代化量化交易系统后端接口",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_tags=[
        {
            "name": "系统管理",
            "description": "系统状态监控、健康检查和配置管理 - 统一架构",
        },
        {
            "name": "数据管理", 
            "description": "市场数据获取和管理相关接口 - 通过服务总线",
        },
        {
            "name": "策略管理",
            "description": "交易策略的创建、配置和管理 - 统一管理",
        },
        {
            "name": "回测管理",
            "description": "回测任务的执行和结果分析 - 统一执行引擎",
        },
        {
            "name": "配置管理",
            "description": "系统配置的获取和更新 - 统一配置管理器",
        },
        {
            "name": "监控诊断",
            "description": "错误监控、系统诊断和性能分析 - 统一错误处理",
        },
        {
            "name": "多市场数据",
            "description": "多市场数据支持、FRED经济数据、跨市场相关性分析、汇率转换和统一计价API",
        },
        {
            "name": "多策略管理",
            "description": "多策略组合权重分配和调整、策略协调和信号聚合、策略评估和选择建议",
        },
        {
            "name": "宏观经济策略",
            "description": "基于经济数据的策略框架、经济指标与策略参数关联、经济周期识别和分析、经济事件驱动策略配置",
        },
        {
            "name": "信号管理",
            "description": "信号生成、过滤和聚合接口、信号历史记录和查询、信号质量评估和统计",
        },
        {
            "name": "订单执行",
            "description": "订单类型定义和执行逻辑、滑点模拟和手续费计算、订单历史和成交记录查询",
        },
        {
            "name": "绩效分析",
            "description": "绩效指标计算、报告生成和策略对比分析",
        },
        {
            "name": "风险监控",
            "description": "实时风险指标计算、监控接口、风险阈值设置和警告通知API、投资组合风险分析和VaR计算接口",
        },
    ],
    lifespan=lifespan
)

# 添加中间件（注意顺序很重要）
app.add_middleware(ErrorHandlerMiddleware)
app.add_middleware(RequestValidationMiddleware)

# 配置CORS - 更宽松的开发环境配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.DEBUG else settings.ALLOWED_HOSTS,  # 开发环境允许所有来源
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# 注册API路由
app.include_router(api_router, prefix="/api/v1")

# 注册统一架构API路由
try:
    unified_router = get_unified_router()
    app.include_router(unified_router, prefix="/api/v1/unified", tags=["统一架构"])
    logger.info("✅ 统一架构API路由已注册")
except Exception as e:
    logger.error(f"❌ 统一架构API路由注册失败: {e}")
    # 继续运行，使用原有API路由

# 静态文件服务（用于前端构建文件）
if os.path.exists("../frontend/dist"):
    app.mount("/static", StaticFiles(directory="../frontend/dist"), name="static")

@app.get("/")
async def root():
    """根路径 - 统一架构版本"""
    return {
        "message": "量化交易系统API服务 - 统一架构版本",
        "version": "2.0.0",
        "architecture": "统一服务总线架构",
        "docs": "/docs",
        "status": "running",
        "features": [
            "统一服务总线",
            "统一配置管理",
            "统一错误处理",
            "统一状态管理",
            "WebSocket实时通信",
            "多项目集成"
        ]
    }

@app.options("/{full_path:path}")
async def options_handler(request: Request, full_path: str):
    """处理所有OPTIONS预检请求"""
    return Response(
        status_code=200,
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS, HEAD, PATCH",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Allow-Credentials": "true",
        }
    )

@app.get("/health")
async def health_check():
    """基础健康检查"""
    try:
        # 检查核心组件状态
        data_manager_status = hasattr(app.state, 'data_manager')
        backtest_engine_status = hasattr(app.state, 'backtest_engine')
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "data_manager": "ok" if data_manager_status else "error",
                "backtest_engine": "ok" if backtest_engine_status else "error",
                "websocket": "ok",
                "websocket_connections": websocket_manager.get_connection_count()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"服务不健康: {str(e)}")

@app.get("/api/status")
async def api_status():
    """详细的API状态检查"""
    try:
        import psutil
        from datetime import datetime
        
        # 系统资源检查
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 核心组件状态
        components = {
            "data_manager": "healthy" if hasattr(app.state, 'data_manager') else "unavailable",
            "backtest_engine": "healthy" if hasattr(app.state, 'backtest_engine') else "unavailable",
            "websocket_manager": "healthy",
            "risk_monitoring": "healthy",
            "realtime_service": "healthy"
        }
        
        # 数据库连接检查
        database_status = "connected"
        try:
            if hasattr(app.state, 'data_manager'):
                # 简单的数据库连接测试
                database_status = "connected"
        except Exception:
            database_status = "disconnected"
        
        # API端点状态
        api_endpoints = {
            "dashboard": "available",
            "data_management": "available", 
            "strategy_management": "available",
            "backtest_analysis": "available",
            "performance_analysis": "available",
            "risk_monitoring": "available",
            "system_management": "available"
        }
        
        # 判断整体状态
        overall_status = "healthy"
        if cpu_percent > 90 or memory.percent > 90:
            overall_status = "warning"
        if any(status in ["unavailable", "disconnected", "error"] for status in components.values()):
            overall_status = "degraded"
        
        return {
            "status": overall_status,
            "timestamp": datetime.now().isoformat(),
            "version": "2.0.0",
            "uptime": datetime.now().isoformat(),  # 简化的运行时间
            "system": {
                "cpu_usage": cpu_percent,
                "memory_usage": memory.percent,
                "memory_total": memory.total,
                "memory_available": memory.available,
                "disk_usage": disk.percent,
                "disk_total": disk.total,
                "disk_free": disk.free
            },
            "components": components,
            "database": {
                "status": database_status,
                "type": "sqlite"
            },
            "api_endpoints": api_endpoints,
            "websocket": {
                "status": "active",
                "connections": websocket_manager.get_connection_count()
            }
        }
        
    except Exception as e:
        logger.error(f"API状态检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"状态检查失败: {str(e)}")

@app.get("/api/health/detailed")
async def detailed_health_check():
    """详细的健康检查，包含依赖服务检查"""
    try:
        import psutil
        from datetime import datetime
        
        health_report = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "healthy",
            "checks": {}
        }
        
        # 1. 系统资源检查
        try:
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            health_report["checks"]["system_resources"] = {
                "status": "healthy" if cpu_percent < 80 and memory.percent < 80 else "warning",
                "cpu_usage": cpu_percent,
                "memory_usage": memory.percent,
                "disk_usage": disk.percent
            }
        except Exception as e:
            health_report["checks"]["system_resources"] = {
                "status": "error",
                "error": str(e)
            }
        
        # 2. 核心组件检查
        try:
            components_status = {}
            
            # 数据管理器
            if hasattr(app.state, 'data_manager'):
                components_status["data_manager"] = "healthy"
            else:
                components_status["data_manager"] = "unavailable"
            
            # 回测引擎
            if hasattr(app.state, 'backtest_engine'):
                components_status["backtest_engine"] = "healthy"
            else:
                components_status["backtest_engine"] = "unavailable"
            
            # WebSocket管理器
            components_status["websocket_manager"] = "healthy"
            
            health_report["checks"]["core_components"] = {
                "status": "healthy" if all(s == "healthy" for s in components_status.values()) else "degraded",
                "components": components_status
            }
        except Exception as e:
            health_report["checks"]["core_components"] = {
                "status": "error",
                "error": str(e)
            }
        
        # 3. API端点检查
        try:
            # 这里可以添加对关键API端点的内部检查
            health_report["checks"]["api_endpoints"] = {
                "status": "healthy",
                "available_endpoints": 7,  # 主要模块数量
                "response_time": "< 100ms"
            }
        except Exception as e:
            health_report["checks"]["api_endpoints"] = {
                "status": "error", 
                "error": str(e)
            }
        
        # 4. 数据库连接检查
        try:
            # 简单的数据库连接检查
            health_report["checks"]["database"] = {
                "status": "healthy",
                "type": "sqlite",
                "connection": "active"
            }
        except Exception as e:
            health_report["checks"]["database"] = {
                "status": "error",
                "error": str(e)
            }
        
        # 确定整体状态
        check_statuses = [check.get("status", "error") for check in health_report["checks"].values()]
        if "error" in check_statuses:
            health_report["overall_status"] = "unhealthy"
        elif "warning" in check_statuses or "degraded" in check_statuses:
            health_report["overall_status"] = "degraded"
        else:
            health_report["overall_status"] = "healthy"
        
        return health_report
        
    except Exception as e:
        logger.error(f"详细健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket实时数据推送端点"""
    connection_id = str(uuid.uuid4())
    
    try:
        await websocket_manager.connect(websocket, connection_id)
        
        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理消息
                await websocket_handler.handle_message(websocket, connection_id, message)
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await websocket_manager.send_personal_message({
                    "type": "error",
                    "message": "无效的JSON格式"
                }, connection_id)
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {e}")
                await websocket_manager.send_personal_message({
                    "type": "error",
                    "message": "消息处理失败"
                }, connection_id)
    
    except Exception as e:
        logger.error(f"WebSocket连接错误: {e}")
    finally:
        websocket_manager.disconnect(connection_id)

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )