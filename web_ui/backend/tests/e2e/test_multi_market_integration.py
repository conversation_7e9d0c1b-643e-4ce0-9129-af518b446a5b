import logging
logger = logging.getLogger(__name__)
"""
多市场数据集成测试
测试美股、A股、加密货币、经济数据的集成功能
"""

import pytest
import time
from fastapi.testclient import TestClient
from unittest.mock import patch, Mock


@pytest.mark.integration
class TestMultiMarketDataIntegration:
    """多市场数据集成测试"""
    
    def test_us_stock_data_integration(self, client: TestClient, mock_data_manager):
        """测试美股数据集成"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 模拟美股数据
            us_stock_data = {
                'symbol': 'AAPL',
                'market': 'US',
                'currency': 'USD',
                'data': [
                    {
                        'timestamp': '2023-01-01',
                        'open': 130.28,
                        'high': 133.41,
                        'low': 129.89,
                        'close': 131.96,
                        'volume': 70790813,
                        'adjusted_close': 131.96
                    },
                    {
                        'timestamp': '2023-01-02',
                        'open': 131.99,
                        'high': 132.41,
                        'low': 124.17,
                        'close': 126.04,
                        'volume': 63896155,
                        'adjusted_close': 126.04
                    }
                ]
            }
            
            mock_data_manager.get_market_data.return_value = us_stock_data
            
            # 获取美股数据
            response = client.get(
                "/api/v1/market-data/AAPL",
                params={
                    "start_date": "2023-01-01",
                    "end_date": "2023-01-31",
                    "market": "US"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证美股数据特征
            assert data["symbol"] == "AAPL"
            assert data["market"] == "US"
            assert data["currency"] == "USD"
            assert len(data["data"]) == 2
            
            # 验证数据字段
            first_record = data["data"][0]
            assert "adjusted_close" in first_record
            assert first_record["volume"] > 60000000  # 美股通常交易量较大
            
            logger.info("✅ 美股数据集成测试通过")
    
    def test_china_stock_data_integration(self, client: TestClient, mock_data_manager):
        """测试A股数据集成"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 模拟A股数据
            china_stock_data = {
                'symbol': '000001.SZ',
                'name': '平安银行',
                'market': 'CN',
                'currency': 'CNY',
                'data': [
                    {
                        'timestamp': '2023-01-01',
                        'open': 12.50,
                        'high': 12.88,
                        'low': 12.35,
                        'close': 12.76,
                        'volume': 45678900,
                        'turnover': 582345678.90,
                        'pe_ratio': 8.5,
                        'pb_ratio': 0.85
                    },
                    {
                        'timestamp': '2023-01-02',
                        'open': 12.76,
                        'high': 13.05,
                        'low': 12.60,
                        'close': 12.95,
                        'volume': 52341200,
                        'turnover': 675432109.80,
                        'pe_ratio': 8.6,
                        'pb_ratio': 0.87
                    }
                ]
            }
            
            mock_data_manager.get_market_data.return_value = china_stock_data
            
            # 获取A股数据
            response = client.get(
                "/api/v1/market-data/000001.SZ",
                params={
                    "start_date": "2023-01-01",
                    "end_date": "2023-01-31",
                    "market": "CN"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证A股数据特征
            assert data["symbol"] == "000001.SZ"
            assert data["market"] == "CN"
            assert data["currency"] == "CNY"
            assert "平安银行" in data["name"]
            
            # 验证A股特有字段
            first_record = data["data"][0]
            assert "turnover" in first_record  # 成交额
            assert "pe_ratio" in first_record  # 市盈率
            assert "pb_ratio" in first_record  # 市净率
            
            logger.info("✅ A股数据集成测试通过")
    
    def test_crypto_data_integration(self, client: TestClient, mock_data_manager):
        """测试加密货币数据集成"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 模拟加密货币数据
            crypto_data = {
                'symbol': 'BTCUSDT',
                'name': 'Bitcoin',
                'market': 'CRYPTO',
                'currency': 'USDT',
                'data': [
                    {
                        'timestamp': '2023-01-01T00:00:00Z',
                        'open': 16625.50,
                        'high': 16750.25,
                        'low': 16580.75,
                        'close': 16688.90,
                        'volume': 1234.567,  # BTC数量
                        'quote_volume': 20567890.12,  # USDT数量
                        'trades_count': 45678,
                        'market_cap': 321456789012.34
                    },
                    {
                        'timestamp': '2023-01-01T01:00:00Z',
                        'open': 16688.90,
                        'high': 16720.15,
                        'low': 16650.30,
                        'close': 16705.80,
                        'volume': 987.654,
                        'quote_volume': 16432109.87,
                        'trades_count': 38901,
                        'market_cap': 321789012345.67
                    }
                ]
            }
            
            mock_data_manager.get_market_data.return_value = crypto_data
            
            # 获取加密货币数据
            response = client.get(
                "/api/v1/market-data/BTCUSDT",
                params={
                    "start_date": "2023-01-01",
                    "end_date": "2023-01-02",
                    "market": "CRYPTO",
                    "interval": "1h"  # 加密货币支持小时级数据
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证加密货币数据特征
            assert data["symbol"] == "BTCUSDT"
            assert data["market"] == "CRYPTO"
            assert data["currency"] == "USDT"
            assert "Bitcoin" in data["name"]
            
            # 验证加密货币特有字段
            first_record = data["data"][0]
            assert "quote_volume" in first_record  # 计价货币交易量
            assert "trades_count" in first_record  # 交易笔数
            assert "market_cap" in first_record  # 市值
            assert first_record["close"] > 16000  # BTC价格范围验证
            
            logger.info("✅ 加密货币数据集成测试通过")
    
    def test_economic_data_integration(self, client: TestClient, mock_data_manager):
        """测试经济数据集成"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 模拟FRED经济数据
            economic_data = {
                'series_id': 'GDP',
                'title': 'Gross Domestic Product',
                'units': 'Billions of Dollars',
                'frequency': 'Quarterly',
                'data': [
                    {
                        'date': '2023-01-01',
                        'value': 26854.6,
                        'revision': None
                    },
                    {
                        'date': '2023-04-01',
                        'value': 27063.0,
                        'revision': None
                    },
                    {
                        'date': '2023-07-01',
                        'value': 27267.8,
                        'revision': None
                    }
                ]
            }
            
            mock_data_manager.get_economic_data.return_value = economic_data
            
            # 获取经济数据
            response = client.get(
                "/api/v1/economic-data/GDP",
                params={
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证经济数据特征
            assert data["series_id"] == "GDP"
            assert data["title"] == "Gross Domestic Product"
            assert data["units"] == "Billions of Dollars"
            assert data["frequency"] == "Quarterly"
            
            # 验证数据值
            assert len(data["data"]) == 3
            first_record = data["data"][0]
            assert first_record["value"] > 25000  # GDP值验证
            
            logger.info("✅ 经济数据集成测试通过")


@pytest.mark.integration
class TestCrossMarketAnalysis:
    """跨市场分析测试"""
    
    def test_cross_market_correlation_analysis(self, client: TestClient, mock_data_manager):
        """测试跨市场相关性分析"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 模拟跨市场相关性分析结果
            correlation_data = {
                'analysis_type': 'cross_market_correlation',
                'markets': ['US', 'CN', 'CRYPTO'],
                'symbols': ['AAPL', '000001.SZ', 'BTCUSDT'],
                'period': '2023-01-01 to 2023-12-31',
                'correlations': {
                    'AAPL_vs_000001.SZ': 0.65,
                    'AAPL_vs_BTCUSDT': 0.23,
                    '000001.SZ_vs_BTCUSDT': 0.18
                },
                'statistics': {
                    'highest_correlation': {
                        'pair': 'AAPL_vs_000001.SZ',
                        'value': 0.65
                    },
                    'lowest_correlation': {
                        'pair': '000001.SZ_vs_BTCUSDT',
                        'value': 0.18
                    }
                }
            }
            
            mock_data_manager.analyze_cross_market_correlation.return_value = correlation_data
            
            # 请求跨市场相关性分析
            analysis_request = {
                'symbols': ['AAPL', '000001.SZ', 'BTCUSDT'],
                'start_date': '2023-01-01',
                'end_date': '2023-12-31',
                'analysis_type': 'correlation'
            }
            
            response = client.post("/api/v1/analysis/cross-market", json=analysis_request)
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证分析结果
            assert data["analysis_type"] == "cross_market_correlation"
            assert len(data["markets"]) == 3
            assert len(data["symbols"]) == 3
            
            # 验证相关性数据
            correlations = data["correlations"]
            assert "AAPL_vs_000001.SZ" in correlations
            assert "AAPL_vs_BTCUSDT" in correlations
            assert "000001.SZ_vs_BTCUSDT" in correlations
            
            # 验证相关性值在合理范围内
            for pair, correlation in correlations.items():
                assert -1 <= correlation <= 1, f"相关性值 {correlation} 超出范围"
            
            logger.info("✅ 跨市场相关性分析测试通过")
    
    def test_currency_conversion_integration(self, client: TestClient, mock_data_manager):
        """测试货币转换集成"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 模拟汇率数据
            exchange_rates = {
                'base_currency': 'USD',
                'rates': {
                    'CNY': 7.2345,
                    'EUR': 0.8567,
                    'JPY': 148.92,
                    'GBP': 0.7834
                },
                'timestamp': '2023-01-01T12:00:00Z'
            }
            
            mock_data_manager.get_exchange_rates.return_value = exchange_rates
            
            # 获取汇率数据
            response = client.get("/api/v1/exchange-rates/USD")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证汇率数据
            assert data["base_currency"] == "USD"
            assert "CNY" in data["rates"]
            assert "EUR" in data["rates"]
            assert data["rates"]["CNY"] > 7.0  # USD/CNY汇率验证
            
            # 测试货币转换
            conversion_request = {
                'amount': 1000,
                'from_currency': 'USD',
                'to_currency': 'CNY',
                'date': '2023-01-01'
            }
            
            mock_data_manager.convert_currency.return_value = {
                'original_amount': 1000,
                'converted_amount': 7234.5,
                'from_currency': 'USD',
                'to_currency': 'CNY',
                'exchange_rate': 7.2345,
                'conversion_date': '2023-01-01'
            }
            
            response = client.post("/api/v1/currency/convert", json=conversion_request)
            
            assert response.status_code == 200
            conversion_data = response.json()
            
            # 验证转换结果
            assert conversion_data["original_amount"] == 1000
            assert conversion_data["converted_amount"] == 7234.5
            assert conversion_data["from_currency"] == "USD"
            assert conversion_data["to_currency"] == "CNY"
            
            logger.info("✅ 货币转换集成测试通过")


@pytest.mark.integration
class TestMultiMarketStrategy:
    """多市场策略测试"""
    
    def test_global_diversification_strategy(self, client: TestClient, mock_strategy_manager, mock_backtest_engine):
        """测试全球分散化策略"""
        with patch('app.services.strategy_manager.StrategyManager', return_value=mock_strategy_manager), \
             patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            
            # 创建全球分散化策略
            global_strategy = {
                'name': '全球分散化策略',
                'type': 'GlobalDiversificationStrategy',
                'description': '跨市场分散投资策略',
                'parameters': {
                    'markets': ['US', 'CN', 'CRYPTO'],
                    'allocation': {
                        'US': 0.5,
                        'CN': 0.3,
                        'CRYPTO': 0.2
                    },
                    'rebalance_frequency': 'monthly',
                    'symbols': {
                        'US': ['AAPL', 'GOOGL', 'MSFT'],
                        'CN': ['000001.SZ', '000002.SZ'],
                        'CRYPTO': ['BTCUSDT', 'ETHUSDT']
                    }
                }
            }
            
            mock_strategy_manager.create_strategy.return_value = {
                **global_strategy,
                'id': 'global-strategy-1',
                'created_at': '2023-01-01T00:00:00Z'
            }
            
            # 创建策略
            response = client.post("/api/v1/strategies", json=global_strategy)
            assert response.status_code == 201
            created_strategy = response.json()
            
            # 验证策略配置
            assert created_strategy['name'] == '全球分散化策略'
            assert created_strategy['type'] == 'GlobalDiversificationStrategy'
            assert len(created_strategy['parameters']['markets']) == 3
            
            # 启动多市场回测
            backtest_config = {
                'strategy_name': created_strategy['name'],
                'parameters': created_strategy['parameters'],
                'start_date': '2023-01-01',
                'end_date': '2023-12-31',
                'initial_capital': 1000000,
                'base_currency': 'USD'
            }
            
            mock_backtest_engine.start_backtest.return_value = {
                'session_id': 'global-backtest-1',
                'status': 'started',
                'progress': 0
            }
            
            response = client.post("/api/v1/backtest", json=backtest_config)
            assert response.status_code == 201
            session_id = response.json()['session_id']
            
            # 模拟回测完成
            mock_backtest_engine.get_backtest_status.return_value = {
                'session_id': session_id,
                'status': 'completed',
                'progress': 100
            }
            
            # 模拟多市场回测结果
            mock_result = {
                'session_id': session_id,
                'strategy_config': created_strategy,
                'total_return': 0.18,
                'metrics': {
                    'total_return': 0.18,
                    'sharpe_ratio': 1.35,
                    'max_drawdown': 0.09,
                    'volatility': 0.14
                },
                'market_performance': {
                    'US': {
                        'return': 0.15,
                        'allocation': 0.5,
                        'contribution': 0.075
                    },
                    'CN': {
                        'return': 0.22,
                        'allocation': 0.3,
                        'contribution': 0.066
                    },
                    'CRYPTO': {
                        'return': 0.35,
                        'allocation': 0.2,
                        'contribution': 0.070
                    }
                },
                'currency_impact': {
                    'USD_CNY': -0.02,
                    'USD_USDT': 0.001
                }
            }
            
            mock_backtest_engine.get_backtest_result.return_value = mock_result
            
            # 获取回测结果
            response = client.get(f"/api/v1/backtest/{session_id}/result")
            assert response.status_code == 200
            result = response.json()
            
            # 验证多市场回测结果
            assert result['total_return'] == 0.18
            assert 'market_performance' in result
            assert 'currency_impact' in result
            
            # 验证各市场表现
            market_perf = result['market_performance']
            assert 'US' in market_perf
            assert 'CN' in market_perf
            assert 'CRYPTO' in market_perf
            
            # 验证分配权重
            total_allocation = sum(market['allocation'] for market in market_perf.values())
            assert abs(total_allocation - 1.0) < 0.001, "总分配权重应该等于1"
            
            logger.info("✅ 全球分散化策略测试通过")
    
    def test_arbitrage_strategy_across_markets(self, client: TestClient, mock_strategy_manager, mock_backtest_engine):
        """测试跨市场套利策略"""
        with patch('app.services.strategy_manager.StrategyManager', return_value=mock_strategy_manager), \
             patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            
            # 创建跨市场套利策略
            arbitrage_strategy = {
                'name': '跨市场套利策略',
                'type': 'CrossMarketArbitrageStrategy',
                'description': '利用不同市场间的价格差异进行套利',
                'parameters': {
                    'primary_market': 'US',
                    'secondary_market': 'CN',
                    'symbol_pairs': [
                        {'us_symbol': 'BABA', 'cn_symbol': '09988.HK'},
                        {'us_symbol': 'JD', 'cn_symbol': '09618.HK'}
                    ],
                    'min_spread_threshold': 0.02,  # 最小价差阈值2%
                    'max_position_size': 0.1,  # 最大仓位10%
                    'holding_period': 5  # 持有期5天
                }
            }
            
            mock_strategy_manager.create_strategy.return_value = {
                **arbitrage_strategy,
                'id': 'arbitrage-strategy-1',
                'created_at': '2023-01-01T00:00:00Z'
            }
            
            # 创建策略
            response = client.post("/api/v1/strategies", json=arbitrage_strategy)
            assert response.status_code == 201
            created_strategy = response.json()
            
            # 启动套利策略回测
            backtest_config = {
                'strategy_name': created_strategy['name'],
                'parameters': created_strategy['parameters'],
                'start_date': '2023-01-01',
                'end_date': '2023-12-31',
                'initial_capital': 500000
            }
            
            mock_backtest_engine.start_backtest.return_value = {
                'session_id': 'arbitrage-backtest-1',
                'status': 'started',
                'progress': 0
            }
            
            response = client.post("/api/v1/backtest", json=backtest_config)
            assert response.status_code == 201
            session_id = response.json()['session_id']
            
            # 模拟套利回测结果
            mock_result = {
                'session_id': session_id,
                'strategy_config': created_strategy,
                'total_return': 0.08,
                'metrics': {
                    'total_return': 0.08,
                    'sharpe_ratio': 2.1,  # 套利策略通常有较高夏普比率
                    'max_drawdown': 0.03,  # 较小的回撤
                    'win_rate': 0.78,  # 较高的胜率
                    'average_holding_period': 4.2
                },
                'arbitrage_opportunities': {
                    'total_opportunities': 45,
                    'executed_trades': 35,
                    'success_rate': 0.78,
                    'average_spread': 0.035,
                    'largest_spread': 0.087
                },
                'trades': [
                    {
                        'timestamp': '2023-02-15T09:30:00Z',
                        'type': 'arbitrage_open',
                        'us_trade': {
                            'symbol': 'BABA',
                            'side': 'BUY',
                            'quantity': 100,
                            'price': 85.50
                        },
                        'cn_trade': {
                            'symbol': '09988.HK',
                            'side': 'SELL',
                            'quantity': 100,
                            'price': 88.20
                        },
                        'spread': 0.032
                    }
                ]
            }
            
            mock_backtest_engine.get_backtest_result.return_value = mock_result
            
            # 获取套利回测结果
            response = client.get(f"/api/v1/backtest/{session_id}/result")
            assert response.status_code == 200
            result = response.json()
            
            # 验证套利策略结果
            assert result['total_return'] == 0.08
            assert result['metrics']['sharpe_ratio'] > 2.0  # 套利策略应有高夏普比率
            assert result['metrics']['max_drawdown'] < 0.05  # 套利策略回撤应较小
            
            # 验证套利机会统计
            arbitrage_stats = result['arbitrage_opportunities']
            assert arbitrage_stats['total_opportunities'] > 0
            assert arbitrage_stats['success_rate'] > 0.7
            assert arbitrage_stats['average_spread'] > 0.02
            
            logger.info("✅ 跨市场套利策略测试通过")


@pytest.mark.integration
class TestDataSourceFailover:
    """数据源故障切换测试"""
    
    def test_data_source_failover_mechanism(self, client: TestClient, mock_data_manager):
        """测试数据源故障切换机制"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 模拟主数据源故障
            mock_data_manager.get_data_sources_status.return_value = {
                'yahoo': {'status': 'disconnected', 'last_update': '2023-01-01T10:00:00Z'},
                'akshare': {'status': 'connected', 'last_update': '2023-01-01T12:00:00Z'},
                'binance': {'status': 'connected', 'last_update': '2023-01-01T12:00:00Z'}
            }
            
            # 检查数据源状态
            response = client.get("/api/v1/data-sources/status")
            assert response.status_code == 200
            status = response.json()
            
            # 验证主数据源故障
            assert status['yahoo']['status'] == 'disconnected'
            assert status['akshare']['status'] == 'connected'
            
            # 模拟故障切换后的数据获取
            mock_data_manager.get_market_data.return_value = {
                'symbol': 'AAPL',
                'data_source': 'akshare',  # 切换到备用数据源
                'data': [
                    {
                        'timestamp': '2023-01-01',
                        'open': 130.0,
                        'high': 135.0,
                        'low': 128.0,
                        'close': 133.0,
                        'volume': 1000000
                    }
                ],
                'failover_info': {
                    'primary_source': 'yahoo',
                    'fallback_source': 'akshare',
                    'failover_reason': 'primary_source_unavailable'
                }
            }
            
            # 尝试获取数据（应该自动切换到备用数据源）
            response = client.get(
                "/api/v1/market-data/AAPL",
                params={
                    "start_date": "2023-01-01",
                    "end_date": "2023-01-31"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证故障切换成功
            assert data['data_source'] == 'akshare'
            assert 'failover_info' in data
            assert data['failover_info']['primary_source'] == 'yahoo'
            assert data['failover_info']['fallback_source'] == 'akshare'
            
            logger.info("✅ 数据源故障切换测试通过")
    
    def test_data_source_recovery(self, client: TestClient, mock_data_manager):
        """测试数据源恢复"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 模拟数据源恢复
            mock_data_manager.get_data_sources_status.return_value = {
                'yahoo': {'status': 'connected', 'last_update': '2023-01-01T13:00:00Z'},
                'akshare': {'status': 'connected', 'last_update': '2023-01-01T13:00:00Z'}
            }
            
            # 检查数据源状态
            response = client.get("/api/v1/data-sources/status")
            assert response.status_code == 200
            status = response.json()
            
            # 验证所有数据源都已恢复
            assert status['yahoo']['status'] == 'connected'
            assert status['akshare']['status'] == 'connected'
            
            # 模拟恢复后的数据获取（应该切换回主数据源）
            mock_data_manager.get_market_data.return_value = {
                'symbol': 'AAPL',
                'data_source': 'yahoo',  # 切换回主数据源
                'data': [
                    {
                        'timestamp': '2023-01-01',
                        'open': 130.0,
                        'high': 135.0,
                        'low': 128.0,
                        'close': 133.0,
                        'volume': 1000000
                    }
                ],
                'recovery_info': {
                    'recovered_source': 'yahoo',
                    'recovery_time': '2023-01-01T13:00:00Z'
                }
            }
            
            # 获取数据验证恢复
            response = client.get(
                "/api/v1/market-data/AAPL",
                params={
                    "start_date": "2023-01-01",
                    "end_date": "2023-01-31"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证已切换回主数据源
            assert data['data_source'] == 'yahoo'
            assert 'recovery_info' in data
            
            logger.info("✅ 数据源恢复测试通过")


@pytest.mark.integration
@pytest.mark.slow
class TestLargeScaleMultiMarketTest:
    """大规模多市场测试"""
    
    def test_large_scale_multi_market_backtest(self, client: TestClient, mock_data_manager, mock_backtest_engine):
        """测试大规模多市场回测"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager), \
             patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            
            # 创建大规模多市场策略
            large_scale_strategy = {
                'name': '大规模多市场策略',
                'type': 'LargeScaleMultiMarketStrategy',
                'parameters': {
                    'markets': ['US', 'CN', 'CRYPTO', 'EU', 'JP'],
                    'symbols_per_market': {
                        'US': 50,
                        'CN': 30,
                        'CRYPTO': 20,
                        'EU': 25,
                        'JP': 15
                    },
                    'total_symbols': 140,
                    'rebalance_frequency': 'weekly'
                }
            }
            
            mock_strategy_manager = Mock()
            mock_strategy_manager.create_strategy.return_value = {
                **large_scale_strategy,
                'id': 'large-scale-strategy-1'
            }
            
            with patch('app.services.strategy_manager.StrategyManager', return_value=mock_strategy_manager):
                # 创建策略
                response = client.post("/api/v1/strategies", json=large_scale_strategy)
                assert response.status_code == 201
                
                # 启动大规模回测
                backtest_config = {
                    'strategy_name': large_scale_strategy['name'],
                    'parameters': large_scale_strategy['parameters'],
                    'start_date': '2022-01-01',
                    'end_date': '2023-12-31',
                    'initial_capital': 10000000  # 1000万初始资金
                }
                
                mock_backtest_engine.start_backtest.return_value = {
                    'session_id': 'large-scale-session-1',
                    'status': 'started',
                    'progress': 0,
                    'estimated_duration': 3600  # 预计1小时
                }
                
                start_time = time.time()
                response = client.post("/api/v1/backtest", json=backtest_config)
                backtest_start_time = time.time() - start_time
                
                assert response.status_code == 201
                session_id = response.json()['session_id']
                
                logger.info(f"大规模回测启动时间: {backtest_start_time:.3f}秒")
                assert backtest_start_time < 5.0, "大规模回测启动时间应该小于5秒"
                
                # 模拟大规模回测结果
                mock_result = {
                    'session_id': session_id,
                    'total_symbols_processed': 140,
                    'total_data_points': 140 * 365 * 2,  # 140个标的 * 2年 * 365天
                    'total_return': 0.22,
                    'metrics': {
                        'total_return': 0.22,
                        'sharpe_ratio': 1.45,
                        'max_drawdown': 0.11,
                        'volatility': 0.16
                    },
                    'market_breakdown': {
                        'US': {'return': 0.18, 'weight': 0.35},
                        'CN': {'return': 0.25, 'weight': 0.25},
                        'CRYPTO': {'return': 0.45, 'weight': 0.15},
                        'EU': {'return': 0.12, 'weight': 0.15},
                        'JP': {'return': 0.08, 'weight': 0.10}
                    },
                    'performance_stats': {
                        'processing_time': 1800,  # 30分钟
                        'data_throughput': '156 MB/s',
                        'calculations_per_second': 50000
                    }
                }
                
                mock_backtest_engine.get_backtest_result.return_value = mock_result
                
                # 获取大规模回测结果
                response = client.get(f"/api/v1/backtest/{session_id}/result")
                assert response.status_code == 200
                result = response.json()
                
                # 验证大规模回测结果
                assert result['total_symbols_processed'] == 140
                assert result['total_data_points'] > 100000
                assert result['total_return'] == 0.22
                
                # 验证性能统计
                perf_stats = result['performance_stats']
                assert 'processing_time' in perf_stats
                assert 'data_throughput' in perf_stats
                assert 'calculations_per_second' in perf_stats
                
                logger.info("✅ 大规模多市场回测测试通过")
                logger.info(f"  处理标的数量: {result['total_symbols_processed']}")
                logger.info(f"  数据点总数: {result['total_data_points']:,}")
                logger.info(f"  总收益率: {result['total_return']:.2%}")
                logger.info(f"  处理时间: {perf_stats['processing_time']}秒")