import logging
logger = logging.getLogger(__name__)
"""
完整回测流程端到端测试
测试从策略创建到回测执行再到结果分析的完整流程
"""

import pytest
import asyncio
import time
from fastapi.testclient import TestClient
from httpx import AsyncClient
from unittest.mock import patch, Mock
import json


@pytest.mark.integration
class TestCompleteBacktestFlow:
    """完整回测流程测试"""
    
    def test_complete_backtest_workflow(self, client: TestClient, mock_data_manager, mock_strategy_manager, mock_backtest_engine):
        """测试完整的回测工作流程"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager), \
             patch('app.services.strategy_manager.StrategyManager', return_value=mock_strategy_manager), \
             patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            
            # 步骤1: 检查数据源状态
            logger.info("步骤1: 检查数据源状态")
            response = client.get("/api/v1/data-sources/status")
            assert response.status_code == 200
            data_sources = response.json()
            assert "yahoo" in data_sources
            assert data_sources["yahoo"]["status"] == "connected"
            
            # 步骤2: 获取市场数据
            logger.info("步骤2: 获取市场数据")
            mock_data_manager.get_market_data.return_value = {
                'symbol': 'AAPL',
                'data': [
                    {
                        'timestamp': '2023-01-01',
                        'open': 130.0,
                        'high': 135.0,
                        'low': 128.0,
                        'close': 133.0,
                        'volume': 1000000
                    }
                ]
            }
            
            response = client.get(
                "/api/v1/market-data/AAPL",
                params={
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31"
                }
            )
            assert response.status_code == 200
            market_data = response.json()
            assert market_data["symbol"] == "AAPL"
            assert len(market_data["data"]) > 0
            
            # 步骤3: 创建策略
            logger.info("步骤3: 创建策略")
            strategy_config = {
                "name": "端到端测试策略",
                "type": "MovingAverageStrategy",
                "description": "用于端到端测试的移动平均策略",
                "parameters": {
                    "short_window": 10,
                    "long_window": 20,
                    "stop_loss": 0.05,
                    "take_profit": 0.1
                }
            }
            
            mock_strategy_manager.create_strategy.return_value = {
                **strategy_config,
                "id": "e2e-strategy-1",
                "created_at": "2023-01-01T00:00:00Z"
            }
            
            response = client.post("/api/v1/strategies", json=strategy_config)
            assert response.status_code == 201
            created_strategy = response.json()
            strategy_id = created_strategy["id"]
            
            # 步骤4: 验证策略参数
            logger.info("步骤4: 验证策略参数")
            mock_strategy_manager.validate_strategy.return_value = {
                "valid": True,
                "errors": [],
                "warnings": []
            }
            
            response = client.post("/api/v1/strategies/validate", json=strategy_config)
            assert response.status_code == 200
            validation_result = response.json()
            assert validation_result["valid"] is True
            
            # 步骤5: 启动回测
            logger.info("步骤5: 启动回测")
            backtest_config = {
                "strategy_name": created_strategy["name"],
                "parameters": created_strategy["parameters"],
                "start_date": "2023-01-01",
                "end_date": "2023-12-31",
                "initial_capital": 100000,
                "symbols": ["AAPL"]
            }
            
            mock_backtest_engine.start_backtest.return_value = {
                "session_id": "e2e-session-1",
                "status": "started",
                "progress": 0
            }
            
            response = client.post("/api/v1/backtest", json=backtest_config)
            assert response.status_code == 201
            backtest_info = response.json()
            session_id = backtest_info["session_id"]
            
            # 步骤6: 监控回测进度
            logger.info("步骤6: 监控回测进度")
            # 模拟回测进度更新
            progress_updates = [25, 50, 75, 100]
            
            for progress in progress_updates:
                mock_backtest_engine.get_backtest_status.return_value = {
                    "session_id": session_id,
                    "status": "running" if progress < 100 else "completed",
                    "progress": progress,
                    "current_date": f"2023-{progress//4 + 1:02d}-01",
                    "estimated_time_remaining": max(0, (100 - progress) * 10)
                }
                
                response = client.get(f"/api/v1/backtest/{session_id}/status")
                assert response.status_code == 200
                status = response.json()
                assert status["progress"] == progress
                
                logger.info(f"  回测进度: {progress}%")
                
                # 模拟等待
                time.sleep(0.1)
            
            # 步骤7: 获取回测结果
            logger.info("步骤7: 获取回测结果")
            mock_backtest_result = {
                "session_id": session_id,
                "strategy_config": created_strategy,
                "start_date": "2023-01-01",
                "end_date": "2023-12-31",
                "initial_capital": 100000,
                "final_value": 115000,
                "total_return": 0.15,
                "metrics": {
                    "total_return": 0.15,
                    "annualized_return": 0.14,
                    "sharpe_ratio": 1.2,
                    "max_drawdown": 0.08,
                    "volatility": 0.12,
                    "win_rate": 0.65
                },
                "trades": [
                    {
                        "timestamp": "2023-01-15T09:30:00Z",
                        "symbol": "AAPL",
                        "side": "BUY",
                        "quantity": 100,
                        "price": 150.0,
                        "commission": 1.0
                    },
                    {
                        "timestamp": "2023-01-20T15:30:00Z",
                        "symbol": "AAPL",
                        "side": "SELL",
                        "quantity": 100,
                        "price": 155.0,
                        "commission": 1.0
                    }
                ],
                "equity_curve": [
                    {"date": "2023-01-01", "value": 100000},
                    {"date": "2023-01-02", "value": 100500},
                    {"date": "2023-12-31", "value": 115000}
                ]
            }
            
            mock_backtest_engine.get_backtest_result.return_value = mock_backtest_result
            
            response = client.get(f"/api/v1/backtest/{session_id}/result")
            assert response.status_code == 200
            result = response.json()
            
            # 验证回测结果
            assert result["session_id"] == session_id
            assert result["total_return"] == 0.15
            assert result["metrics"]["sharpe_ratio"] == 1.2
            assert len(result["trades"]) == 2
            assert len(result["equity_curve"]) == 3
            
            # 步骤8: 分析结果并生成报告
            logger.info("步骤8: 分析结果")
            
            # 验证关键指标
            metrics = result["metrics"]
            assert metrics["total_return"] > 0, "策略应该盈利"
            assert metrics["sharpe_ratio"] > 1, "夏普比率应该大于1"
            assert metrics["max_drawdown"] < 0.2, "最大回撤应该小于20%"
            assert metrics["win_rate"] > 0.5, "胜率应该大于50%"
            
            # 验证交易记录
            trades = result["trades"]
            assert len(trades) > 0, "应该有交易记录"
            
            for trade in trades:
                assert "timestamp" in trade
                assert "symbol" in trade
                assert "side" in trade
                assert "quantity" in trade
                assert "price" in trade
                assert trade["side"] in ["BUY", "SELL"]
                assert trade["quantity"] > 0
                assert trade["price"] > 0
            
            # 验证权益曲线
            equity_curve = result["equity_curve"]
            assert len(equity_curve) > 0, "应该有权益曲线数据"
            assert equity_curve[0]["value"] == 100000, "初始资金应该正确"
            assert equity_curve[-1]["value"] == 115000, "最终资金应该正确"
            
            logger.info("✅ 完整回测流程测试通过")
    
    def test_backtest_error_handling(self, client: TestClient, mock_backtest_engine):
        """测试回测错误处理"""
        with patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            # 测试无效的回测配置
            invalid_config = {
                "strategy_name": "",  # 空策略名
                "parameters": {},
                "start_date": "2023-12-31",  # 开始日期晚于结束日期
                "end_date": "2023-01-01",
                "initial_capital": -1000  # 负数资金
            }
            
            response = client.post("/api/v1/backtest", json=invalid_config)
            assert response.status_code == 422
            
            # 测试回测执行失败
            mock_backtest_engine.start_backtest.side_effect = Exception("回测引擎错误")
            
            valid_config = {
                "strategy_name": "测试策略",
                "parameters": {"short_window": 10, "long_window": 20},
                "start_date": "2023-01-01",
                "end_date": "2023-12-31",
                "initial_capital": 100000
            }
            
            response = client.post("/api/v1/backtest", json=valid_config)
            assert response.status_code == 500
    
    def test_backtest_cancellation_flow(self, client: TestClient, mock_backtest_engine):
        """测试回测取消流程"""
        with patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            # 启动回测
            mock_backtest_engine.start_backtest.return_value = {
                "session_id": "cancel-test-session",
                "status": "started",
                "progress": 0
            }
            
            backtest_config = {
                "strategy_name": "取消测试策略",
                "parameters": {"short_window": 10, "long_window": 20},
                "start_date": "2023-01-01",
                "end_date": "2023-12-31",
                "initial_capital": 100000
            }
            
            response = client.post("/api/v1/backtest", json=backtest_config)
            assert response.status_code == 201
            session_id = response.json()["session_id"]
            
            # 模拟回测运行中
            mock_backtest_engine.get_backtest_status.return_value = {
                "session_id": session_id,
                "status": "running",
                "progress": 30
            }
            
            response = client.get(f"/api/v1/backtest/{session_id}/status")
            assert response.status_code == 200
            assert response.json()["status"] == "running"
            
            # 取消回测
            mock_backtest_engine.cancel_backtest.return_value = True
            
            response = client.post(f"/api/v1/backtest/{session_id}/cancel")
            assert response.status_code == 200
            assert response.json()["success"] is True
            
            # 验证回测状态变为已取消
            mock_backtest_engine.get_backtest_status.return_value = {
                "session_id": session_id,
                "status": "cancelled",
                "progress": 30
            }
            
            response = client.get(f"/api/v1/backtest/{session_id}/status")
            assert response.status_code == 200
            assert response.json()["status"] == "cancelled"


@pytest.mark.integration
class TestMultiStrategyBacktest:
    """多策略回测测试"""
    
    def test_multiple_strategies_comparison(self, client: TestClient, mock_strategy_manager, mock_backtest_engine):
        """测试多个策略的对比回测"""
        with patch('app.services.strategy_manager.StrategyManager', return_value=mock_strategy_manager), \
             patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            
            # 创建多个策略
            strategies = [
                {
                    "name": "移动平均策略",
                    "type": "MovingAverageStrategy",
                    "parameters": {"short_window": 10, "long_window": 20}
                },
                {
                    "name": "RSI策略",
                    "type": "RSIStrategy",
                    "parameters": {"rsi_period": 14, "oversold": 30, "overbought": 70}
                },
                {
                    "name": "均值回归策略",
                    "type": "MeanReversionStrategy",
                    "parameters": {"lookback_period": 20, "entry_threshold": 2.0}
                }
            ]
            
            created_strategies = []
            
            for i, strategy in enumerate(strategies):
                mock_strategy_manager.create_strategy.return_value = {
                    **strategy,
                    "id": f"multi-strategy-{i+1}",
                    "created_at": "2023-01-01T00:00:00Z"
                }
                
                response = client.post("/api/v1/strategies", json=strategy)
                assert response.status_code == 201
                created_strategies.append(response.json())
            
            # 为每个策略启动回测
            backtest_sessions = []
            
            for i, strategy in enumerate(created_strategies):
                backtest_config = {
                    "strategy_name": strategy["name"],
                    "parameters": strategy["parameters"],
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31",
                    "initial_capital": 100000
                }
                
                mock_backtest_engine.start_backtest.return_value = {
                    "session_id": f"multi-session-{i+1}",
                    "status": "started",
                    "progress": 0
                }
                
                response = client.post("/api/v1/backtest", json=backtest_config)
                assert response.status_code == 201
                backtest_sessions.append(response.json())
            
            # 等待所有回测完成并收集结果
            results = []
            
            for i, session in enumerate(backtest_sessions):
                session_id = session["session_id"]
                
                # 模拟回测完成
                mock_backtest_engine.get_backtest_status.return_value = {
                    "session_id": session_id,
                    "status": "completed",
                    "progress": 100
                }
                
                # 模拟不同的回测结果
                mock_result = {
                    "session_id": session_id,
                    "strategy_config": created_strategies[i],
                    "total_return": 0.1 + i * 0.05,  # 不同的收益率
                    "metrics": {
                        "total_return": 0.1 + i * 0.05,
                        "sharpe_ratio": 1.0 + i * 0.2,
                        "max_drawdown": 0.05 + i * 0.02,
                        "win_rate": 0.6 + i * 0.05
                    }
                }
                
                mock_backtest_engine.get_backtest_result.return_value = mock_result
                
                response = client.get(f"/api/v1/backtest/{session_id}/result")
                assert response.status_code == 200
                results.append(response.json())
            
            # 分析和比较结果
            logger.info("策略对比结果:")
            for result in results:
                strategy_name = result["strategy_config"]["name"]
                total_return = result["metrics"]["total_return"]
                sharpe_ratio = result["metrics"]["sharpe_ratio"]
                max_drawdown = result["metrics"]["max_drawdown"]
                
                logger.info(f"  {strategy_name}:")
                logger.info(f"    总收益率: {total_return:.2%}")
                logger.info(f"    夏普比率: {sharpe_ratio:.2f}")
                logger.info(f"    最大回撤: {max_drawdown:.2%}")
            
            # 验证结果
            assert len(results) == 3
            
            # 验证每个策略都有不同的表现
            returns = [r["metrics"]["total_return"] for r in results]
            assert len(set(returns)) == 3, "每个策略应该有不同的收益率"
            
            # 找出最佳策略
            best_strategy = max(results, key=lambda x: x["metrics"]["sharpe_ratio"])
            logger.info(f"最佳策略: {best_strategy['strategy_config']['name']}")
            
            assert best_strategy["metrics"]["sharpe_ratio"] > 1.0, "最佳策略夏普比率应该大于1"
    
    def test_concurrent_backtests(self, client: TestClient, mock_backtest_engine):
        """测试并发回测"""
        with patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            import threading
            import queue
            
            # 准备多个回测配置
            backtest_configs = [
                {
                    "strategy_name": f"并发测试策略{i+1}",
                    "parameters": {"short_window": 5 + i, "long_window": 15 + i},
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31",
                    "initial_capital": 100000
                }
                for i in range(3)
            ]
            
            # 结果队列
            results_queue = queue.Queue()
            
            def run_backtest(config, index):
                """运行单个回测"""
                try:
                    mock_backtest_engine.start_backtest.return_value = {
                        "session_id": f"concurrent-session-{index}",
                        "status": "started",
                        "progress": 0
                    }
                    
                    response = client.post("/api/v1/backtest", json=config)
                    results_queue.put((index, response.status_code, response.json()))
                except Exception as e:
                    results_queue.put((index, 500, {"error": str(e)}))
            
            # 启动并发回测
            threads = []
            for i, config in enumerate(backtest_configs):
                thread = threading.Thread(target=run_backtest, args=(config, i))
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            # 收集结果
            results = []
            while not results_queue.empty():
                results.append(results_queue.get())
            
            # 验证结果
            assert len(results) == 3, "应该有3个回测结果"
            
            for index, status_code, result in results:
                assert status_code == 201, f"回测{index+1}应该成功启动"
                assert "session_id" in result, f"回测{index+1}应该返回session_id"
            
            logger.info("✅ 并发回测测试通过")


@pytest.mark.integration
class TestLargeDatasetBacktest:
    """大数据量回测测试"""
    
    @pytest.mark.slow
    def test_large_dataset_performance(self, client: TestClient, mock_data_manager, mock_backtest_engine):
        """测试大数据量回测性能"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager), \
             patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            
            # 模拟大量市场数据
            large_dataset = {
                'symbol': 'AAPL',
                'data': [
                    {
                        'timestamp': f'2020-{(i//30)+1:02d}-{(i%30)+1:02d}',
                        'open': 100 + (i % 50),
                        'high': 105 + (i % 50),
                        'low': 95 + (i % 50),
                        'close': 102 + (i % 50),
                        'volume': 1000000 + (i * 1000)
                    }
                    for i in range(1000)  # 1000天的数据
                ]
            }
            
            mock_data_manager.get_market_data.return_value = large_dataset
            
            # 获取大数据集
            start_time = time.time()
            response = client.get(
                "/api/v1/market-data/AAPL",
                params={
                    "start_date": "2020-01-01",
                    "end_date": "2023-12-31"
                }
            )
            data_fetch_time = time.time() - start_time
            
            assert response.status_code == 200
            market_data = response.json()
            assert len(market_data["data"]) == 1000
            
            logger.info(f"大数据集获取时间: {data_fetch_time:.3f}秒")
            assert data_fetch_time < 5.0, "大数据集获取时间应该小于5秒"
            
            # 启动大数据量回测
            backtest_config = {
                "strategy_name": "大数据量测试策略",
                "parameters": {"short_window": 20, "long_window": 50},
                "start_date": "2020-01-01",
                "end_date": "2023-12-31",
                "initial_capital": 1000000  # 更大的初始资金
            }
            
            mock_backtest_engine.start_backtest.return_value = {
                "session_id": "large-data-session",
                "status": "started",
                "progress": 0
            }
            
            start_time = time.time()
            response = client.post("/api/v1/backtest", json=backtest_config)
            backtest_start_time = time.time() - start_time
            
            assert response.status_code == 201
            session_id = response.json()["session_id"]
            
            logger.info(f"大数据量回测启动时间: {backtest_start_time:.3f}秒")
            assert backtest_start_time < 2.0, "回测启动时间应该小于2秒"
            
            # 模拟回测进度监控
            progress_checks = 0
            max_progress_checks = 10
            
            while progress_checks < max_progress_checks:
                mock_backtest_engine.get_backtest_status.return_value = {
                    "session_id": session_id,
                    "status": "completed" if progress_checks == max_progress_checks - 1 else "running",
                    "progress": min(100, (progress_checks + 1) * 10),
                    "processed_days": min(1000, (progress_checks + 1) * 100)
                }
                
                response = client.get(f"/api/v1/backtest/{session_id}/status")
                assert response.status_code == 200
                
                status = response.json()
                logger.info(f"回测进度: {status['progress']}%")
                
                progress_checks += 1
                time.sleep(0.1)
            
            # 获取大数据量回测结果
            mock_large_result = {
                "session_id": session_id,
                "total_return": 0.25,
                "metrics": {
                    "total_return": 0.25,
                    "sharpe_ratio": 1.5,
                    "max_drawdown": 0.12,
                    "total_trades": 150,
                    "win_rate": 0.68
                },
                "trades": [
                    {
                        "timestamp": f"2020-{i//12+1:02d}-15T09:30:00Z",
                        "symbol": "AAPL",
                        "side": "BUY" if i % 2 == 0 else "SELL",
                        "quantity": 100,
                        "price": 100 + (i % 50),
                        "commission": 1.0
                    }
                    for i in range(150)  # 150笔交易
                ]
            }
            
            mock_backtest_engine.get_backtest_result.return_value = mock_large_result
            
            start_time = time.time()
            response = client.get(f"/api/v1/backtest/{session_id}/result")
            result_fetch_time = time.time() - start_time
            
            assert response.status_code == 200
            result = response.json()
            
            logger.info(f"大数据量结果获取时间: {result_fetch_time:.3f}秒")
            assert result_fetch_time < 3.0, "结果获取时间应该小于3秒"
            
            # 验证结果完整性
            assert result["metrics"]["total_trades"] == 150
            assert len(result["trades"]) == 150
            assert result["total_return"] == 0.25
            
            logger.info("✅ 大数据量回测性能测试通过")


@pytest.mark.integration
class TestSystemIntegration:
    """系统集成测试"""
    
    def test_full_system_health_check(self, client: TestClient):
        """测试完整系统健康检查"""
        # 检查API根路径
        response = client.get("/")
        assert response.status_code in [200, 404]  # 根据实际实现调整
        
        # 检查健康检查端点
        response = client.get("/health")
        if response.status_code == 200:
            health_data = response.json()
            assert "status" in health_data
        
        # 检查API文档
        response = client.get("/docs")
        assert response.status_code == 200
        
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        logger.info("✅ 系统健康检查通过")
    
    def test_api_version_compatibility(self, client: TestClient):
        """测试API版本兼容性"""
        # 测试v1 API端点
        v1_endpoints = [
            "/api/v1/strategies",
            "/api/v1/data-sources/status",
            "/api/v1/system/status"
        ]
        
        for endpoint in v1_endpoints:
            response = client.get(endpoint)
            # 应该返回200或者需要参数的422，而不是404
            assert response.status_code in [200, 422], f"端点 {endpoint} 不可访问"
        
        logger.info("✅ API版本兼容性测试通过")
    
    def test_error_handling_consistency(self, client: TestClient):
        """测试错误处理一致性"""
        # 测试404错误
        response = client.get("/api/v1/nonexistent")
        assert response.status_code == 404
        error_data = response.json()
        assert "detail" in error_data
        
        # 测试422错误
        response = client.post("/api/v1/strategies", json={"invalid": "data"})
        assert response.status_code == 422
        error_data = response.json()
        assert "detail" in error_data
        
        # 测试方法不允许错误
        response = client.patch("/api/v1/strategies")
        assert response.status_code == 405
        
        logger.info("✅ 错误处理一致性测试通过")