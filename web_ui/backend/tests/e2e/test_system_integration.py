import logging
logger = logging.getLogger(__name__)
"""
系统集成测试
测试前后端完整集成和系统级功能
"""

import pytest
import asyncio
import time
import json
from fastapi.testclient import TestClient
from httpx import AsyncClient
from unittest.mock import patch, Mock, AsyncMock
import threading
import queue


@pytest.mark.integration
class TestFrontendBackendIntegration:
    """前后端集成测试"""
    
    def test_api_cors_configuration(self, client: TestClient):
        """测试CORS配置"""
        # 测试预检请求
        response = client.options(
            "/api/v1/strategies",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            }
        )
        
        # 验证CORS头
        assert response.status_code in [200, 204]
        if "Access-Control-Allow-Origin" in response.headers:
            assert response.headers["Access-Control-Allow-Origin"] in ["*", "http://localhost:3000"]
        
        # 测试实际请求的CORS头
        response = client.get(
            "/api/v1/strategies",
            headers={"Origin": "http://localhost:3000"}
        )
        
        if "Access-Control-Allow-Origin" in response.headers:
            assert response.headers["Access-Control-Allow-Origin"] in ["*", "http://localhost:3000"]
        
        logger.info("✅ CORS配置测试通过")
    
    def test_api_response_format_consistency(self, client: TestClient, mock_data_manager):
        """测试API响应格式一致性"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 测试多个端点的响应格式
            endpoints_and_expected_fields = [
                ("/api/v1/data-sources/status", ["yahoo", "akshare"]),
                ("/api/v1/system/status", ["status", "uptime"]),
            ]
            
            for endpoint, expected_fields in endpoints_and_expected_fields:
                response = client.get(endpoint)
                
                # 验证响应状态码
                assert response.status_code == 200, f"端点 {endpoint} 响应失败"
                
                # 验证响应是JSON格式
                try:
                    data = response.json()
                except json.JSONDecodeError:
                    pytest.fail(f"端点 {endpoint} 返回的不是有效JSON")
                
                # 验证必需字段存在
                for field in expected_fields:
                    if field not in data:
                        logger.info(f"警告: 端点 {endpoint} 缺少字段 {field}")
        
        logger.info("✅ API响应格式一致性测试通过")
    
    def test_error_response_format_consistency(self, client: TestClient):
        """测试错误响应格式一致性"""
        # 测试不同类型的错误响应
        error_scenarios = [
            ("/api/v1/nonexistent", 404),
            ("/api/v1/market-data/INVALID", 404),
        ]
        
        for endpoint, expected_status in error_scenarios:
            response = client.get(endpoint)
            
            assert response.status_code == expected_status
            
            # 验证错误响应格式
            try:
                error_data = response.json()
                assert "detail" in error_data, f"错误响应缺少detail字段: {endpoint}"
            except json.JSONDecodeError:
                pytest.fail(f"错误响应不是有效JSON: {endpoint}")
        
        logger.info("✅ 错误响应格式一致性测试通过")


@pytest.mark.integration
class TestWebSocketIntegration:
    """WebSocket集成测试"""
    
    def test_websocket_real_time_data_flow(self, client: TestClient):
        """测试WebSocket实时数据流"""
        with client.websocket_connect("/ws/realtime") as websocket:
            # 订阅市场数据
            subscribe_message = {
                "type": "subscribe",
                "channel": "market_data",
                "symbols": ["AAPL", "GOOGL"]
            }
            websocket.send_json(subscribe_message)
            
            # 接收订阅确认
            response = websocket.receive_json()
            assert response["type"] == "subscription_confirmed"
            assert response["channel"] == "market_data"
            
            # 模拟实时数据推送（在实际环境中，这会由后端自动推送）
            # 这里我们测试连接的稳定性
            
            # 发送心跳
            ping_message = {"type": "ping"}
            websocket.send_json(ping_message)
            
            # 接收心跳响应
            pong_response = websocket.receive_json()
            assert pong_response["type"] == "pong"
            
            logger.info("✅ WebSocket实时数据流测试通过")
    
    def test_websocket_backtest_progress_updates(self, client: TestClient, mock_backtest_engine):
        """测试WebSocket回测进度更新"""
        with patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            with client.websocket_connect("/ws/realtime") as websocket:
                # 订阅回测进度
                subscribe_message = {
                    "type": "subscribe",
                    "channel": "backtest_progress",
                    "session_id": "test-session-1"
                }
                websocket.send_json(subscribe_message)
                
                # 接收订阅确认
                response = websocket.receive_json()
                assert response["type"] == "subscription_confirmed"
                assert response["channel"] == "backtest_progress"
                
                # 启动回测（通过HTTP API）
                backtest_config = {
                    "strategy_name": "WebSocket测试策略",
                    "parameters": {"short_window": 10, "long_window": 20},
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31",
                    "initial_capital": 100000
                }
                
                mock_backtest_engine.start_backtest.return_value = {
                    "session_id": "test-session-1",
                    "status": "started",
                    "progress": 0
                }
                
                # 使用另一个客户端启动回测
                http_response = client.post("/api/v1/backtest", json=backtest_config)
                assert http_response.status_code == 201
                
                # 在实际环境中，这里会收到WebSocket推送的进度更新
                # 由于测试环境限制，我们验证连接仍然活跃
                
                logger.info("✅ WebSocket回测进度更新测试通过")
    
    def test_websocket_connection_resilience(self, client: TestClient):
        """测试WebSocket连接弹性"""
        # 测试多个并发连接
        connections = []
        
        try:
            for i in range(3):
                ws = client.websocket_connect("/ws/realtime")
                connection = ws.__enter__()
                connections.append((ws, connection))
                
                # 发送订阅消息
                subscribe_message = {
                    "type": "subscribe",
                    "channel": "system_status"
                }
                connection.send_json(subscribe_message)
                
                # 验证连接正常
                response = connection.receive_json()
                assert response["type"] == "subscription_confirmed"
            
            logger.info(f"✅ 成功建立 {len(connections)} 个WebSocket连接")
            
        finally:
            # 清理连接
            for ws, connection in connections:
                try:
                    ws.__exit__(None, None, None)
                except:
                    pass
        
        logger.info("✅ WebSocket连接弹性测试通过")


@pytest.mark.integration
class TestDatabaseIntegration:
    """数据库集成测试"""
    
    def test_database_transaction_consistency(self, client: TestClient, temp_db, mock_strategy_manager):
        """测试数据库事务一致性"""
        with patch('app.services.strategy_manager.StrategyManager', return_value=mock_strategy_manager):
            # 创建策略
            strategy_config = {
                "name": "事务测试策略",
                "type": "MovingAverageStrategy",
                "parameters": {"short_window": 10, "long_window": 20}
            }
            
            mock_strategy_manager.create_strategy.return_value = {
                **strategy_config,
                "id": "transaction-test-1",
                "created_at": "2023-01-01T00:00:00Z"
            }
            
            response = client.post("/api/v1/strategies", json=strategy_config)
            assert response.status_code == 201
            strategy_id = response.json()["id"]
            
            # 更新策略
            update_data = {"name": "更新后的策略名称"}
            mock_strategy_manager.update_strategy.return_value = {
                **strategy_config,
                **update_data,
                "id": strategy_id,
                "updated_at": "2023-01-01T01:00:00Z"
            }
            
            response = client.put(f"/api/v1/strategies/{strategy_id}", json=update_data)
            assert response.status_code == 200
            
            # 验证更新成功
            updated_strategy = response.json()
            assert updated_strategy["name"] == "更新后的策略名称"
            
            logger.info("✅ 数据库事务一致性测试通过")
    
    def test_database_concurrent_access(self, client: TestClient, temp_db, mock_strategy_manager):
        """测试数据库并发访问"""
        with patch('app.services.strategy_manager.StrategyManager', return_value=mock_strategy_manager):
            import threading
            import queue
            
            results_queue = queue.Queue()
            
            def create_strategy(index):
                """并发创建策略"""
                strategy_config = {
                    "name": f"并发测试策略{index}",
                    "type": "MovingAverageStrategy",
                    "parameters": {"short_window": 5 + index, "long_window": 15 + index}
                }
                
                mock_strategy_manager.create_strategy.return_value = {
                    **strategy_config,
                    "id": f"concurrent-strategy-{index}",
                    "created_at": "2023-01-01T00:00:00Z"
                }
                
                try:
                    response = client.post("/api/v1/strategies", json=strategy_config)
                    results_queue.put((index, response.status_code, response.json()))
                except Exception as e:
                    results_queue.put((index, 500, {"error": str(e)}))
            
            # 启动并发线程
            threads = []
            for i in range(5):
                thread = threading.Thread(target=create_strategy, args=(i,))
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            # 收集结果
            results = []
            while not results_queue.empty():
                results.append(results_queue.get())
            
            # 验证结果
            assert len(results) == 5
            for index, status_code, result in results:
                assert status_code == 201, f"策略{index}创建失败"
                assert "id" in result, f"策略{index}缺少ID"
            
            logger.info("✅ 数据库并发访问测试通过")


@pytest.mark.integration
class TestCacheIntegration:
    """缓存集成测试"""
    
    def test_cache_performance_improvement(self, client: TestClient, mock_data_manager):
        """测试缓存性能提升"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            mock_data_manager.get_market_data.return_value = {
                'symbol': 'AAPL',
                'data': [{'timestamp': '2023-01-01', 'close': 150.0}]
            }
            
            # 第一次请求（缓存未命中）
            start_time = time.time()
            response1 = client.get(
                "/api/v1/market-data/AAPL",
                params={"start_date": "2023-01-01", "end_date": "2023-01-31"}
            )
            first_request_time = time.time() - start_time
            
            assert response1.status_code == 200
            
            # 第二次请求（缓存命中）
            start_time = time.time()
            response2 = client.get(
                "/api/v1/market-data/AAPL",
                params={"start_date": "2023-01-01", "end_date": "2023-01-31"}
            )
            second_request_time = time.time() - start_time
            
            assert response2.status_code == 200
            
            # 验证响应内容一致
            assert response1.json() == response2.json()
            
            logger.info(f"首次请求时间: {first_request_time:.3f}秒")
            logger.info(f"缓存命中时间: {second_request_time:.3f}秒")
            
            # 在模拟环境中，缓存效果可能不明显，但至少不应该更慢
            assert second_request_time <= first_request_time * 1.5
            
            logger.info("✅ 缓存性能提升测试通过")
    
    def test_cache_invalidation(self, client: TestClient, mock_data_manager, mock_strategy_manager):
        """测试缓存失效机制"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager), \
             patch('app.services.strategy_manager.StrategyManager', return_value=mock_strategy_manager):
            
            # 获取策略列表（缓存）
            mock_strategy_manager.get_strategies.return_value = [
                {"id": "strategy-1", "name": "原始策略"}
            ]
            
            response1 = client.get("/api/v1/strategies")
            assert response1.status_code == 200
            strategies1 = response1.json()
            assert len(strategies1) == 1
            assert strategies1[0]["name"] == "原始策略"
            
            # 创建新策略（应该使缓存失效）
            new_strategy = {
                "name": "新策略",
                "type": "MovingAverageStrategy",
                "parameters": {"short_window": 10, "long_window": 20}
            }
            
            mock_strategy_manager.create_strategy.return_value = {
                **new_strategy,
                "id": "strategy-2",
                "created_at": "2023-01-01T00:00:00Z"
            }
            
            response = client.post("/api/v1/strategies", json=new_strategy)
            assert response.status_code == 201
            
            # 更新模拟返回值
            mock_strategy_manager.get_strategies.return_value = [
                {"id": "strategy-1", "name": "原始策略"},
                {"id": "strategy-2", "name": "新策略"}
            ]
            
            # 再次获取策略列表（应该返回更新后的数据）
            response2 = client.get("/api/v1/strategies")
            assert response2.status_code == 200
            strategies2 = response2.json()
            assert len(strategies2) == 2
            
            logger.info("✅ 缓存失效机制测试通过")


@pytest.mark.integration
class TestSystemMonitoring:
    """系统监控集成测试"""
    
    def test_system_health_monitoring(self, client: TestClient):
        """测试系统健康监控"""
        with patch('app.services.system_monitor.SystemMonitor') as mock_monitor:
            mock_monitor.return_value.get_system_status.return_value = {
                "status": "healthy",
                "uptime": 86400,
                "memory": {"used": 512, "total": 1024, "percentage": 50},
                "cpu": {"usage": 25.5},
                "services": {
                    "database": "healthy",
                    "cache": "healthy",
                    "websocket": "healthy"
                }
            }
            
            # 获取系统状态
            response = client.get("/api/v1/system/status")
            assert response.status_code == 200
            
            status = response.json()
            assert status["status"] == "healthy"
            assert "uptime" in status
            assert "memory" in status
            assert "cpu" in status
            assert "services" in status
            
            # 验证各服务状态
            services = status["services"]
            for service_name, service_status in services.items():
                assert service_status in ["healthy", "warning", "error"], \
                    f"服务 {service_name} 状态无效: {service_status}"
            
            logger.info("✅ 系统健康监控测试通过")
    
    def test_performance_metrics_collection(self, client: TestClient):
        """测试性能指标收集"""
        with patch('app.services.system_monitor.SystemMonitor') as mock_monitor:
            mock_monitor.return_value.get_performance_metrics.return_value = {
                "cpu": {"usage": 25.5, "cores": 8},
                "memory": {"used": 512, "total": 1024, "percentage": 50},
                "disk": {"used": 100, "total": 500, "percentage": 20},
                "network": {"inbound": 1024, "outbound": 512},
                "api": {
                    "requests_per_second": 45.2,
                    "average_response_time": 0.125,
                    "error_rate": 0.02
                }
            }
            
            # 获取性能指标
            response = client.get("/api/v1/system/metrics")
            assert response.status_code == 200
            
            metrics = response.json()
            
            # 验证指标完整性
            required_metrics = ["cpu", "memory", "disk", "network"]
            for metric in required_metrics:
                assert metric in metrics, f"缺少性能指标: {metric}"
            
            # 验证指标值合理性
            assert 0 <= metrics["cpu"]["usage"] <= 100
            assert 0 <= metrics["memory"]["percentage"] <= 100
            assert 0 <= metrics["disk"]["percentage"] <= 100
            
            if "api" in metrics:
                api_metrics = metrics["api"]
                assert api_metrics["requests_per_second"] >= 0
                assert api_metrics["average_response_time"] >= 0
                assert 0 <= api_metrics["error_rate"] <= 1
            
            logger.info("✅ 性能指标收集测试通过")
    
    def test_alert_system_integration(self, client: TestClient):
        """测试告警系统集成"""
        with patch('app.services.system_monitor.SystemMonitor') as mock_monitor:
            # 模拟系统告警状态
            mock_monitor.return_value.get_system_status.return_value = {
                "status": "warning",
                "alerts": [
                    {
                        "level": "warning",
                        "message": "内存使用率过高",
                        "metric": "memory_usage",
                        "value": 85.5,
                        "threshold": 80.0,
                        "timestamp": "2023-01-01T12:00:00Z"
                    },
                    {
                        "level": "info",
                        "message": "数据同步完成",
                        "source": "data_manager",
                        "timestamp": "2023-01-01T11:30:00Z"
                    }
                ]
            }
            
            # 获取系统状态和告警
            response = client.get("/api/v1/system/status")
            assert response.status_code == 200
            
            status = response.json()
            assert status["status"] == "warning"
            assert "alerts" in status
            assert len(status["alerts"]) == 2
            
            # 验证告警格式
            for alert in status["alerts"]:
                assert "level" in alert
                assert "message" in alert
                assert "timestamp" in alert
                assert alert["level"] in ["info", "warning", "error"]
            
            logger.info("✅ 告警系统集成测试通过")


@pytest.mark.integration
@pytest.mark.slow
class TestFullSystemIntegration:
    """完整系统集成测试"""
    
    def test_complete_system_workflow(self, client: TestClient, mock_data_manager, mock_strategy_manager, mock_backtest_engine):
        """测试完整系统工作流程"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager), \
             patch('app.services.strategy_manager.StrategyManager', return_value=mock_strategy_manager), \
             patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            
            logger.info("开始完整系统工作流程测试...")
            
            # 步骤1: 系统健康检查
            logger.info("步骤1: 系统健康检查")
            with patch('app.services.system_monitor.SystemMonitor') as mock_monitor:
                mock_monitor.return_value.get_system_status.return_value = {
                    "status": "healthy",
                    "services": {"database": "healthy", "cache": "healthy"}
                }
                
                response = client.get("/api/v1/system/status")
                assert response.status_code == 200
                assert response.json()["status"] == "healthy"
            
            # 步骤2: 数据源检查
            logger.info("步骤2: 数据源检查")
            mock_data_manager.get_data_sources_status.return_value = {
                'yahoo': {'status': 'connected'},
                'akshare': {'status': 'connected'}
            }
            
            response = client.get("/api/v1/data-sources/status")
            assert response.status_code == 200
            
            # 步骤3: 获取市场数据
            logger.info("步骤3: 获取市场数据")
            mock_data_manager.get_market_data.return_value = {
                'symbol': 'AAPL',
                'data': [{'timestamp': '2023-01-01', 'close': 150.0}]
            }
            
            response = client.get(
                "/api/v1/market-data/AAPL",
                params={"start_date": "2023-01-01", "end_date": "2023-01-31"}
            )
            assert response.status_code == 200
            
            # 步骤4: 创建策略
            logger.info("步骤4: 创建策略")
            strategy_config = {
                "name": "完整测试策略",
                "type": "MovingAverageStrategy",
                "parameters": {"short_window": 10, "long_window": 20}
            }
            
            mock_strategy_manager.create_strategy.return_value = {
                **strategy_config,
                "id": "complete-test-strategy",
                "created_at": "2023-01-01T00:00:00Z"
            }
            
            response = client.post("/api/v1/strategies", json=strategy_config)
            assert response.status_code == 201
            strategy_id = response.json()["id"]
            
            # 步骤5: 启动回测
            logger.info("步骤5: 启动回测")
            backtest_config = {
                "strategy_name": strategy_config["name"],
                "parameters": strategy_config["parameters"],
                "start_date": "2023-01-01",
                "end_date": "2023-12-31",
                "initial_capital": 100000
            }
            
            mock_backtest_engine.start_backtest.return_value = {
                "session_id": "complete-test-session",
                "status": "started",
                "progress": 0
            }
            
            response = client.post("/api/v1/backtest", json=backtest_config)
            assert response.status_code == 201
            session_id = response.json()["session_id"]
            
            # 步骤6: 监控回测进度
            logger.info("步骤6: 监控回测进度")
            mock_backtest_engine.get_backtest_status.return_value = {
                "session_id": session_id,
                "status": "completed",
                "progress": 100
            }
            
            response = client.get(f"/api/v1/backtest/{session_id}/status")
            assert response.status_code == 200
            assert response.json()["status"] == "completed"
            
            # 步骤7: 获取回测结果
            logger.info("步骤7: 获取回测结果")
            mock_backtest_engine.get_backtest_result.return_value = {
                "session_id": session_id,
                "total_return": 0.15,
                "metrics": {"sharpe_ratio": 1.2, "max_drawdown": 0.08}
            }
            
            response = client.get(f"/api/v1/backtest/{session_id}/result")
            assert response.status_code == 200
            result = response.json()
            assert result["total_return"] == 0.15
            
            # 步骤8: 清理资源
            logger.info("步骤8: 清理资源")
            mock_strategy_manager.delete_strategy.return_value = True
            
            response = client.delete(f"/api/v1/strategies/{strategy_id}")
            assert response.status_code == 200
            
            logger.info("✅ 完整系统工作流程测试通过")
    
    def test_system_stress_test(self, client: TestClient, mock_data_manager):
        """测试系统压力测试"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            mock_data_manager.get_data_sources_status.return_value = {
                'yahoo': {'status': 'connected'}
            }
            
            logger.info("开始系统压力测试...")
            
            # 并发请求测试
            import threading
            import queue
            
            results_queue = queue.Queue()
            request_count = 20
            
            def make_request(index):
                """发送请求"""
                try:
                    response = client.get("/api/v1/data-sources/status")
                    results_queue.put((index, response.status_code, time.time()))
                except Exception as e:
                    results_queue.put((index, 500, time.time()))
            
            # 启动并发请求
            start_time = time.time()
            threads = []
            
            for i in range(request_count):
                thread = threading.Thread(target=make_request, args=(i,))
                threads.append(thread)
                thread.start()
            
            # 等待所有请求完成
            for thread in threads:
                thread.join()
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # 收集结果
            results = []
            while not results_queue.empty():
                results.append(results_queue.get())
            
            # 分析结果
            successful_requests = sum(1 for _, status, _ in results if status == 200)
            success_rate = successful_requests / len(results)
            throughput = len(results) / total_time
            
            logger.info(f"压力测试结果:")
            logger.info(f"  总请求数: {len(results)}")
            logger.info(f"  成功请求数: {successful_requests}")
            logger.info(f"  成功率: {success_rate:.2%}")
            logger.info(f"  总耗时: {total_time:.3f}秒")
            logger.info(f"  吞吐量: {throughput:.2f} 请求/秒")
            
            # 验证系统在压力下的表现
            assert success_rate >= 0.95, f"成功率过低: {success_rate:.2%}"
            assert throughput >= 10, f"吞吐量过低: {throughput:.2f} 请求/秒"
            
            logger.info("✅ 系统压力测试通过")
    
    def test_system_recovery_after_failure(self, client: TestClient, mock_data_manager):
        """测试系统故障后恢复"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            logger.info("开始系统故障恢复测试...")
            
            # 模拟系统正常状态
            mock_data_manager.get_data_sources_status.return_value = {
                'yahoo': {'status': 'connected'}
            }
            
            response = client.get("/api/v1/data-sources/status")
            assert response.status_code == 200
            logger.info("系统正常状态确认")
            
            # 模拟系统故障
            mock_data_manager.get_data_sources_status.side_effect = Exception("系统故障")
            
            response = client.get("/api/v1/data-sources/status")
            assert response.status_code == 500
            logger.info("系统故障状态确认")
            
            # 模拟系统恢复
            mock_data_manager.get_data_sources_status.side_effect = None
            mock_data_manager.get_data_sources_status.return_value = {
                'yahoo': {'status': 'connected'}
            }
            
            response = client.get("/api/v1/data-sources/status")
            assert response.status_code == 200
            logger.info("系统恢复状态确认")
            
            logger.info("✅ 系统故障恢复测试通过")