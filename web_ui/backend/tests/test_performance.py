import logging
logger = logging.getLogger(__name__)
"""
性能和负载测试
测试API的性能表现和负载承受能力
"""

import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from fastapi.testclient import TestClient
from httpx import AsyncClient
from unittest.mock import patch, Mock
import statistics
from typing import List, Dict, Any


@pytest.mark.performance
class TestAPIPerformance:
    """API 性能测试"""
    
    @pytest.mark.slow
    def test_market_data_api_response_time(self, client: TestClient, mock_data_manager):
        """测试市场数据API响应时间"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            response_times = []
            
            # 执行多次请求测量响应时间
            for _ in range(10):
                start_time = time.time()
                response = client.get(
                    "/api/v1/market-data/AAPL",
                    params={
                        "start_date": "2023-01-01",
                        "end_date": "2023-12-31"
                    }
                )
                end_time = time.time()
                
                assert response.status_code == 200
                response_times.append(end_time - start_time)
            
            # 分析响应时间
            avg_time = statistics.mean(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            
            logger.info(f"市场数据API响应时间统计:")
            logger.info(f"  平均: {avg_time:.3f}s")
            logger.info(f"  最大: {max_time:.3f}s")
            logger.info(f"  最小: {min_time:.3f}s")
            
            # 断言响应时间在合理范围内
            assert avg_time < 1.0, f"平均响应时间过长: {avg_time:.3f}s"
            assert max_time < 2.0, f"最大响应时间过长: {max_time:.3f}s"
    
    @pytest.mark.slow
    def test_strategy_api_throughput(self, client: TestClient, mock_strategy_manager):
        """测试策略API吞吐量"""
        with patch('app.services.strategy_manager.StrategyManager', return_value=mock_strategy_manager):
            request_count = 50
            start_time = time.time()
            
            # 并发执行请求
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = []
                for i in range(request_count):
                    future = executor.submit(
                        lambda: client.get("/api/v1/strategies")
                    )
                    futures.append(future)
                
                # 等待所有请求完成
                successful_requests = 0
                for future in as_completed(futures):
                    response = future.result()
                    if response.status_code == 200:
                        successful_requests += 1
            
            end_time = time.time()
            total_time = end_time - start_time
            throughput = successful_requests / total_time
            
            logger.info(f"策略API吞吐量测试:")
            logger.info(f"  总请求数: {request_count}")
            logger.info(f"  成功请求数: {successful_requests}")
            logger.info(f"  总耗时: {total_time:.3f}s")
            logger.info(f"  吞吐量: {throughput:.2f} 请求/秒")
            
            # 断言吞吐量满足要求
            assert successful_requests >= request_count * 0.95, "成功率过低"
            assert throughput >= 10, f"吞吐量过低: {throughput:.2f} 请求/秒"
    
    @pytest.mark.slow
    async def test_async_api_performance(self, async_client: AsyncClient, mock_backtest_engine):
        """测试异步API性能"""
        with patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            request_count = 20
            start_time = time.time()
            
            # 创建异步任务
            tasks = []
            for i in range(request_count):
                task = async_client.get(f"/api/v1/backtest/session-{i}/status")
                tasks.append(task)
            
            # 并发执行所有请求
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # 统计成功请求
            successful_requests = sum(
                1 for response in responses 
                if hasattr(response, 'status_code') and response.status_code == 200
            )
            
            throughput = successful_requests / total_time
            
            logger.info(f"异步API性能测试:")
            logger.info(f"  总请求数: {request_count}")
            logger.info(f"  成功请求数: {successful_requests}")
            logger.info(f"  总耗时: {total_time:.3f}s")
            logger.info(f"  吞吐量: {throughput:.2f} 请求/秒")
            
            # 断言性能满足要求
            assert successful_requests >= request_count * 0.9, "异步请求成功率过低"
            assert throughput >= 15, f"异步API吞吐量过低: {throughput:.2f} 请求/秒"


@pytest.mark.performance
class TestLoadTesting:
    """负载测试"""
    
    @pytest.mark.slow
    def test_concurrent_backtest_requests(self, client: TestClient, mock_backtest_engine, sample_backtest_config):
        """测试并发回测请求"""
        with patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            concurrent_users = 5
            requests_per_user = 3
            
            def user_simulation():
                """模拟用户行为"""
                results = []
                for _ in range(requests_per_user):
                    start_time = time.time()
                    response = client.post("/api/v1/backtest", json=sample_backtest_config)
                    end_time = time.time()
                    
                    results.append({
                        'status_code': response.status_code,
                        'response_time': end_time - start_time
                    })
                    
                    # 模拟用户思考时间
                    time.sleep(0.1)
                
                return results
            
            # 并发执行用户模拟
            with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
                futures = [
                    executor.submit(user_simulation) 
                    for _ in range(concurrent_users)
                ]
                
                all_results = []
                for future in as_completed(futures):
                    user_results = future.result()
                    all_results.extend(user_results)
            
            # 分析结果
            successful_requests = sum(1 for r in all_results if r['status_code'] == 201)
            response_times = [r['response_time'] for r in all_results]
            avg_response_time = statistics.mean(response_times)
            
            logger.info(f"并发回测请求负载测试:")
            logger.info(f"  并发用户数: {concurrent_users}")
            logger.info(f"  每用户请求数: {requests_per_user}")
            logger.info(f"  总请求数: {len(all_results)}")
            logger.info(f"  成功请求数: {successful_requests}")
            logger.info(f"  平均响应时间: {avg_response_time:.3f}s")
            
            # 断言负载测试通过
            success_rate = successful_requests / len(all_results)
            assert success_rate >= 0.95, f"成功率过低: {success_rate:.2%}"
            assert avg_response_time < 2.0, f"平均响应时间过长: {avg_response_time:.3f}s"
    
    @pytest.mark.slow
    def test_memory_usage_under_load(self, client: TestClient, mock_data_manager):
        """测试负载下的内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 执行大量请求
            request_count = 100
            for i in range(request_count):
                response = client.get(
                    "/api/v1/market-data/AAPL",
                    params={
                        "start_date": "2023-01-01",
                        "end_date": "2023-12-31"
                    }
                )
                assert response.status_code == 200
                
                # 每10个请求检查一次内存
                if i % 10 == 0:
                    current_memory = process.memory_info().rss / 1024 / 1024
                    memory_increase = current_memory - initial_memory
                    
                    logger.info(f"请求 {i+1}: 内存使用 {current_memory:.1f}MB (+{memory_increase:.1f}MB)")
                    
                    # 断言内存增长在合理范围内
                    assert memory_increase < 100, f"内存增长过多: {memory_increase:.1f}MB"
        
        final_memory = process.memory_info().rss / 1024 / 1024
        total_increase = final_memory - initial_memory
        
        logger.info(f"负载测试内存使用统计:")
        logger.info(f"  初始内存: {initial_memory:.1f}MB")
        logger.info(f"  最终内存: {final_memory:.1f}MB")
        logger.info(f"  总增长: {total_increase:.1f}MB")
        
        # 断言总内存增长在合理范围内
        assert total_increase < 50, f"总内存增长过多: {total_increase:.1f}MB"


@pytest.mark.performance
class TestDatabasePerformance:
    """数据库性能测试"""
    
    @pytest.mark.slow
    def test_database_query_performance(self, temp_db, mock_data_manager):
        """测试数据库查询性能"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 模拟大量数据查询
            query_times = []
            
            for i in range(20):
                start_time = time.time()
                
                # 模拟数据库查询
                mock_data_manager.get_market_data(
                    symbol="AAPL",
                    start_date="2023-01-01",
                    end_date="2023-12-31"
                )
                
                end_time = time.time()
                query_times.append(end_time - start_time)
            
            avg_query_time = statistics.mean(query_times)
            max_query_time = max(query_times)
            
            logger.info(f"数据库查询性能测试:")
            logger.info(f"  查询次数: {len(query_times)}")
            logger.info(f"  平均查询时间: {avg_query_time:.3f}s")
            logger.info(f"  最大查询时间: {max_query_time:.3f}s")
            
            # 断言查询性能满足要求
            assert avg_query_time < 0.1, f"平均查询时间过长: {avg_query_time:.3f}s"
            assert max_query_time < 0.5, f"最大查询时间过长: {max_query_time:.3f}s"
    
    @pytest.mark.slow
    def test_concurrent_database_access(self, temp_db, mock_data_manager):
        """测试并发数据库访问"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            concurrent_queries = 10
            
            def execute_query():
                """执行数据库查询"""
                start_time = time.time()
                mock_data_manager.get_market_data(
                    symbol="AAPL",
                    start_date="2023-01-01",
                    end_date="2023-12-31"
                )
                end_time = time.time()
                return end_time - start_time
            
            # 并发执行查询
            with ThreadPoolExecutor(max_workers=concurrent_queries) as executor:
                futures = [
                    executor.submit(execute_query) 
                    for _ in range(concurrent_queries)
                ]
                
                query_times = [future.result() for future in as_completed(futures)]
            
            avg_time = statistics.mean(query_times)
            max_time = max(query_times)
            
            logger.info(f"并发数据库访问测试:")
            logger.info(f"  并发查询数: {concurrent_queries}")
            logger.info(f"  平均查询时间: {avg_time:.3f}s")
            logger.info(f"  最大查询时间: {max_time:.3f}s")
            
            # 断言并发性能满足要求
            assert avg_time < 0.2, f"并发平均查询时间过长: {avg_time:.3f}s"
            assert max_time < 1.0, f"并发最大查询时间过长: {max_time:.3f}s"


@pytest.mark.performance
class TestCachePerformance:
    """缓存性能测试"""
    
    @pytest.mark.slow
    def test_cache_hit_performance(self, client: TestClient, mock_data_manager):
        """测试缓存命中性能"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 第一次请求（缓存未命中）
            start_time = time.time()
            response1 = client.get(
                "/api/v1/market-data/AAPL",
                params={
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31"
                }
            )
            first_request_time = time.time() - start_time
            
            assert response1.status_code == 200
            
            # 第二次请求（缓存命中）
            start_time = time.time()
            response2 = client.get(
                "/api/v1/market-data/AAPL",
                params={
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31"
                }
            )
            second_request_time = time.time() - start_time
            
            assert response2.status_code == 200
            
            logger.info(f"缓存性能测试:")
            logger.info(f"  首次请求时间: {first_request_time:.3f}s")
            logger.info(f"  缓存命中时间: {second_request_time:.3f}s")
            logger.info(f"  性能提升: {first_request_time / second_request_time:.1f}x")
            
            # 断言缓存提升了性能
            # 注意：在模拟环境中可能看不到明显差异
            assert second_request_time <= first_request_time, "缓存没有提升性能"
    
    def test_cache_memory_usage(self, client: TestClient, mock_data_manager):
        """测试缓存内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024
        
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 请求不同的数据以填充缓存
            symbols = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA']
            
            for symbol in symbols:
                response = client.get(
                    f"/api/v1/market-data/{symbol}",
                    params={
                        "start_date": "2023-01-01",
                        "end_date": "2023-12-31"
                    }
                )
                assert response.status_code == 200
            
            final_memory = process.memory_info().rss / 1024 / 1024
            memory_increase = final_memory - initial_memory
            
            logger.info(f"缓存内存使用测试:")
            logger.info(f"  初始内存: {initial_memory:.1f}MB")
            logger.info(f"  最终内存: {final_memory:.1f}MB")
            logger.info(f"  内存增长: {memory_increase:.1f}MB")
            
            # 断言缓存内存使用在合理范围内
            assert memory_increase < 20, f"缓存内存使用过多: {memory_increase:.1f}MB"


@pytest.mark.performance
class TestScalabilityTesting:
    """可扩展性测试"""
    
    @pytest.mark.slow
    def test_increasing_load_performance(self, client: TestClient, mock_data_manager):
        """测试递增负载下的性能"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            load_levels = [1, 5, 10, 20]
            results = {}
            
            for load in load_levels:
                logger.info(f"测试负载级别: {load} 并发用户")
                
                def make_request():
                    start_time = time.time()
                    response = client.get("/api/v1/strategies")
                    end_time = time.time()
                    return {
                        'status_code': response.status_code,
                        'response_time': end_time - start_time
                    }
                
                # 并发执行请求
                with ThreadPoolExecutor(max_workers=load) as executor:
                    futures = [executor.submit(make_request) for _ in range(load * 2)]
                    load_results = [future.result() for future in as_completed(futures)]
                
                # 统计结果
                successful_requests = sum(1 for r in load_results if r['status_code'] == 200)
                response_times = [r['response_time'] for r in load_results]
                avg_response_time = statistics.mean(response_times)
                
                results[load] = {
                    'success_rate': successful_requests / len(load_results),
                    'avg_response_time': avg_response_time,
                    'throughput': successful_requests / max(response_times)
                }
                
                logger.info(f"  成功率: {results[load]['success_rate']:.2%}")
                logger.info(f"  平均响应时间: {results[load]['avg_response_time']:.3f}s")
                logger.info(f"  吞吐量: {results[load]['throughput']:.2f} 请求/秒")
            
            # 分析可扩展性
            logger.info("\n可扩展性分析:")
            for load in load_levels:
                result = results[load]
                logger.info(f"负载 {load}: 成功率 {result['success_rate']:.2%}, "
                      f"响应时间 {result['avg_response_time']:.3f}s")
            
            # 断言系统在不同负载下都能正常工作
            for load, result in results.items():
                assert result['success_rate'] >= 0.9, f"负载 {load} 下成功率过低"
                assert result['avg_response_time'] < 3.0, f"负载 {load} 下响应时间过长"


@pytest.mark.performance
class TestResourceUtilization:
    """资源利用率测试"""
    
    @pytest.mark.slow
    def test_cpu_utilization(self, client: TestClient, mock_data_manager):
        """测试CPU利用率"""
        import psutil
        
        # 监控CPU使用率
        cpu_percentages = []
        
        def monitor_cpu():
            for _ in range(10):
                cpu_percent = psutil.cpu_percent(interval=0.1)
                cpu_percentages.append(cpu_percent)
        
        # 在后台监控CPU的同时执行请求
        import threading
        monitor_thread = threading.Thread(target=monitor_cpu)
        monitor_thread.start()
        
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 执行一些CPU密集型操作
            for _ in range(20):
                response = client.get("/api/v1/strategies")
                assert response.status_code == 200
        
        monitor_thread.join()
        
        avg_cpu = statistics.mean(cpu_percentages)
        max_cpu = max(cpu_percentages)
        
        logger.info(f"CPU利用率测试:")
        logger.info(f"  平均CPU使用率: {avg_cpu:.1f}%")
        logger.info(f"  最大CPU使用率: {max_cpu:.1f}%")
        
        # 断言CPU使用率在合理范围内
        assert avg_cpu < 80, f"平均CPU使用率过高: {avg_cpu:.1f}%"
        assert max_cpu < 95, f"最大CPU使用率过高: {max_cpu:.1f}%"
    
    def test_file_descriptor_usage(self, client: TestClient, mock_data_manager):
        """测试文件描述符使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_fds = process.num_fds() if hasattr(process, 'num_fds') else 0
        
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 执行大量请求
            for _ in range(50):
                response = client.get("/api/v1/strategies")
                assert response.status_code == 200
        
        final_fds = process.num_fds() if hasattr(process, 'num_fds') else 0
        fd_increase = final_fds - initial_fds
        
        logger.info(f"文件描述符使用测试:")
        logger.info(f"  初始文件描述符: {initial_fds}")
        logger.info(f"  最终文件描述符: {final_fds}")
        logger.info(f"  增长数量: {fd_increase}")
        
        # 断言文件描述符没有泄漏
        assert fd_increase < 10, f"文件描述符可能泄漏: 增长 {fd_increase}"