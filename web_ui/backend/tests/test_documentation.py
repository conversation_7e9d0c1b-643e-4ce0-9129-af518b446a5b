import logging
logger = logging.getLogger(__name__)
"""
API 文档和使用示例测试
验证API文档的准确性和示例代码的可用性
"""

import pytest
import json
from fastapi.testclient import TestClient
from unittest.mock import patch


@pytest.mark.api
class TestAPIDocumentation:
    """API 文档测试"""
    
    def test_openapi_schema_generation(self, client: TestClient):
        """测试OpenAPI模式生成"""
        response = client.get("/openapi.json")
        
        assert response.status_code == 200
        schema = response.json()
        
        # 验证基本模式结构
        assert "openapi" in schema
        assert "info" in schema
        assert "paths" in schema
        assert "components" in schema
        
        # 验证API信息
        assert schema["info"]["title"] == "量化交易系统API"
        assert "version" in schema["info"]
        assert "description" in schema["info"]
    
    def test_swagger_ui_accessibility(self, client: TestClient):
        """测试Swagger UI可访问性"""
        response = client.get("/docs")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        
        # 验证页面包含Swagger UI元素
        content = response.text
        assert "Swagger UI" in content
        assert "量化交易系统API" in content
    
    def test_redoc_accessibility(self, client: TestClient):
        """测试ReDoc可访问性"""
        response = client.get("/redoc")
        
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]
        
        # 验证页面包含ReDoc元素
        content = response.text
        assert "ReDoc" in content
    
    def test_api_endpoints_documented(self, client: TestClient):
        """测试所有API端点都有文档"""
        response = client.get("/openapi.json")
        schema = response.json()
        paths = schema["paths"]
        
        # 验证主要端点都有文档
        expected_endpoints = [
            "/api/v1/market-data/{symbol}",
            "/api/v1/strategies",
            "/api/v1/backtest",
            "/api/v1/risk/calculate",
            "/api/v1/system/status"
        ]
        
        for endpoint in expected_endpoints:
            assert endpoint in paths, f"端点 {endpoint} 缺少文档"
            
            # 验证端点有描述
            for method_info in paths[endpoint].values():
                if isinstance(method_info, dict):
                    assert "summary" in method_info or "description" in method_info, \
                        f"端点 {endpoint} 缺少描述"
    
    def test_request_response_schemas(self, client: TestClient):
        """测试请求和响应模式定义"""
        response = client.get("/openapi.json")
        schema = response.json()
        
        # 验证组件模式定义
        components = schema.get("components", {})
        schemas = components.get("schemas", {})
        
        # 验证主要数据模型都有定义
        expected_schemas = [
            "MarketDataResponse",
            "StrategyConfig",
            "BacktestConfig",
            "BacktestResult",
            "RiskMetrics"
        ]
        
        for schema_name in expected_schemas:
            if schema_name in schemas:
                schema_def = schemas[schema_name]
                assert "type" in schema_def, f"模式 {schema_name} 缺少类型定义"
                assert "properties" in schema_def, f"模式 {schema_name} 缺少属性定义"
    
    def test_error_responses_documented(self, client: TestClient):
        """测试错误响应有文档"""
        response = client.get("/openapi.json")
        schema = response.json()
        paths = schema["paths"]
        
        # 检查主要端点的错误响应文档
        for path, methods in paths.items():
            for method, method_info in methods.items():
                if isinstance(method_info, dict) and "responses" in method_info:
                    responses = method_info["responses"]
                    
                    # 验证有错误响应文档
                    error_codes = ["400", "404", "422", "500"]
                    has_error_doc = any(code in responses for code in error_codes)
                    
                    if not has_error_doc:
                        logger.info(f"警告: {method.upper()} {path} 缺少错误响应文档")


@pytest.mark.api
class TestAPIExamples:
    """API 使用示例测试"""
    
    def test_market_data_example(self, client: TestClient, mock_data_manager, sample_market_data):
        """测试市场数据API使用示例"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            mock_data_manager.get_market_data.return_value = sample_market_data
            
            # 示例：获取AAPL的市场数据
            response = client.get(
                "/api/v1/market-data/AAPL",
                params={
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应格式符合文档
            assert "symbol" in data
            assert "data" in data
            assert isinstance(data["data"], list)
            
            if data["data"]:
                first_record = data["data"][0]
                required_fields = ["timestamp", "open", "high", "low", "close", "volume"]
                for field in required_fields:
                    assert field in first_record, f"市场数据缺少字段: {field}"
    
    def test_strategy_management_example(self, client: TestClient, mock_strategy_manager, sample_strategy_config):
        """测试策略管理API使用示例"""
        with patch('app.services.strategy_manager.StrategyManager', return_value=mock_strategy_manager):
            # 示例1：创建策略
            response = client.post("/api/v1/strategies", json=sample_strategy_config)
            
            assert response.status_code == 201
            created_strategy = response.json()
            assert "id" in created_strategy
            strategy_id = created_strategy["id"]
            
            # 示例2：获取策略列表
            response = client.get("/api/v1/strategies")
            assert response.status_code == 200
            strategies = response.json()
            assert isinstance(strategies, list)
            
            # 示例3：更新策略
            update_data = {
                "name": "更新后的策略名称",
                "parameters": {"short_window": 15}
            }
            mock_strategy_manager.update_strategy.return_value = {
                **created_strategy,
                **update_data
            }
            
            response = client.put(f"/api/v1/strategies/{strategy_id}", json=update_data)
            assert response.status_code == 200
            updated_strategy = response.json()
            assert updated_strategy["name"] == update_data["name"]
            
            # 示例4：删除策略
            mock_strategy_manager.delete_strategy.return_value = True
            response = client.delete(f"/api/v1/strategies/{strategy_id}")
            assert response.status_code == 200
    
    def test_backtest_workflow_example(self, client: TestClient, mock_backtest_engine, sample_backtest_config):
        """测试回测工作流使用示例"""
        with patch('app.services.backtest_engine.BacktestEngine', return_value=mock_backtest_engine):
            # 示例1：启动回测
            response = client.post("/api/v1/backtest", json=sample_backtest_config)
            
            assert response.status_code == 201
            backtest_info = response.json()
            assert "session_id" in backtest_info
            session_id = backtest_info["session_id"]
            
            # 示例2：查询回测状态
            response = client.get(f"/api/v1/backtest/{session_id}/status")
            assert response.status_code == 200
            status = response.json()
            assert "status" in status
            assert "progress" in status
            
            # 示例3：获取回测结果
            response = client.get(f"/api/v1/backtest/{session_id}/result")
            assert response.status_code == 200
            result = response.json()
            assert "metrics" in result
            assert "trades" in result
            assert "equity_curve" in result
            
            # 示例4：获取回测历史
            mock_backtest_engine.get_backtest_history.return_value = [
                {
                    "session_id": session_id,
                    "strategy_name": "测试策略",
                    "status": "completed",
                    "created_at": "2023-01-01T00:00:00Z"
                }
            ]
            
            response = client.get("/api/v1/backtest/history")
            assert response.status_code == 200
            history = response.json()
            assert isinstance(history, list)
    
    def test_risk_management_example(self, client: TestClient, mock_risk_manager):
        """测试风险管理API使用示例"""
        with patch('app.services.risk_manager.RiskManager', return_value=mock_risk_manager):
            # 示例：计算投资组合风险指标
            portfolio_data = {
                "positions": [
                    {"symbol": "AAPL", "quantity": 100, "price": 150.0},
                    {"symbol": "GOOGL", "quantity": 50, "price": 90.0}
                ],
                "cash": 10000
            }
            
            response = client.post("/api/v1/risk/calculate", json=portfolio_data)
            
            assert response.status_code == 200
            risk_metrics = response.json()
            
            # 验证风险指标格式
            expected_metrics = ["var_95", "var_99", "expected_shortfall", "max_drawdown"]
            for metric in expected_metrics:
                if metric in risk_metrics:
                    assert isinstance(risk_metrics[metric], (int, float)), \
                        f"风险指标 {metric} 应该是数值类型"
    
    def test_data_source_management_example(self, client: TestClient, mock_data_manager):
        """测试数据源管理API使用示例"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 示例1：获取数据源状态
            response = client.get("/api/v1/data-sources/status")
            
            assert response.status_code == 200
            status = response.json()
            
            # 验证状态格式
            for source_name, source_info in status.items():
                assert "status" in source_info
                assert "last_update" in source_info
            
            # 示例2：启动数据同步
            sync_request = {
                "source": "yahoo",
                "symbols": ["AAPL", "GOOGL"]
            }
            
            mock_data_manager.sync_data.return_value = {
                "task_id": "sync-task-1",
                "status": "started",
                "progress": 0
            }
            
            response = client.post("/api/v1/data-sources/sync", json=sync_request)
            assert response.status_code == 201
            sync_info = response.json()
            assert "task_id" in sync_info
    
    def test_system_monitoring_example(self, client: TestClient):
        """测试系统监控API使用示例"""
        with patch('app.services.system_monitor.SystemMonitor') as mock_monitor:
            mock_monitor.return_value.get_system_status.return_value = {
                "status": "healthy",
                "uptime": 86400,
                "memory": {"used": 512, "total": 1024, "percentage": 50},
                "cpu": {"usage": 25.5}
            }
            
            # 示例：获取系统状态
            response = client.get("/api/v1/system/status")
            
            assert response.status_code == 200
            system_status = response.json()
            
            # 验证系统状态格式
            assert "status" in system_status
            assert "uptime" in system_status
            assert "memory" in system_status
            assert "cpu" in system_status


@pytest.mark.api
class TestCodeExamples:
    """代码示例测试"""
    
    def test_python_client_example(self):
        """测试Python客户端示例代码"""
        # 这里可以测试文档中的Python客户端示例
        example_code = '''
import requests

# 获取市场数据
response = requests.get(
    "http://localhost:8000/api/v1/market-data/AAPL",
    params={
        "start_date": "2023-01-01",
        "end_date": "2023-12-31"
    }
)

if response.status_code == 200:
    data = response.json()
    logger.info(f"获取到 {data['symbol']} 的 {len(data['data'])} 条数据")
else:
    logger.info(f"请求失败: {response.status_code}")
'''
        
        # 验证示例代码语法正确
        try:
            compile(example_code, '<string>', 'exec')
        except SyntaxError as e:
            pytest.fail(f"Python客户端示例代码语法错误: {e}")
    
    def test_javascript_client_example(self):
        """测试JavaScript客户端示例代码"""
        # 这里可以验证JavaScript示例的语法
        example_code = '''
// 获取市场数据
async function getMarketData(symbol, startDate, endDate) {
    try {
        const response = await fetch(
            `http://localhost:8000/api/v1/market-data/${symbol}?start_date=${startDate}&end_date=${endDate}`
        );
        
        if (response.ok) {
            const data = await response.json();
            console.log(`获取到 ${data.symbol} 的 ${data.data.length} 条数据`);
            return data;
        } else {
            console.error(`请求失败: ${response.status}`);
        }
    } catch (error) {
        console.error('网络错误:', error);
    }
}

// 使用示例
getMarketData('AAPL', '2023-01-01', '2023-12-31');
'''
        
        # 这里只是验证代码格式，实际项目中可以使用JS解析器
        assert 'async function' in example_code
        assert 'await fetch' in example_code
        assert 'response.json()' in example_code
    
    def test_curl_examples(self):
        """测试cURL示例命令"""
        examples = [
            # 获取市场数据
            'curl -X GET "http://localhost:8000/api/v1/market-data/AAPL?start_date=2023-01-01&end_date=2023-12-31"',
            
            # 创建策略
            '''curl -X POST "http://localhost:8000/api/v1/strategies" \\
     -H "Content-Type: application/json" \\
     -d '{
       "name": "测试策略",
       "type": "MovingAverageStrategy",
       "parameters": {
         "short_window": 10,
         "long_window": 20
       }
     }' ''',
            
            # 启动回测
            '''curl -X POST "http://localhost:8000/api/v1/backtest" \\
     -H "Content-Type: application/json" \\
     -d '{
       "strategy_name": "测试策略",
       "start_date": "2023-01-01",
       "end_date": "2023-12-31",
       "initial_capital": 100000
     }' '''
        ]
        
        for example in examples:
            # 验证cURL命令格式
            assert example.startswith('curl'), f"无效的cURL命令: {example[:50]}..."
            assert 'http://localhost:8000/api/v1/' in example


@pytest.mark.api
class TestDocumentationAccuracy:
    """文档准确性测试"""
    
    def test_response_format_matches_documentation(self, client: TestClient, mock_data_manager, sample_market_data):
        """测试响应格式与文档一致"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            mock_data_manager.get_market_data.return_value = sample_market_data
            
            # 获取API文档中的响应模式
            doc_response = client.get("/openapi.json")
            schema = doc_response.json()
            
            # 实际API调用
            api_response = client.get(
                "/api/v1/market-data/AAPL",
                params={
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31"
                }
            )
            
            assert api_response.status_code == 200
            actual_data = api_response.json()
            
            # 验证实际响应与文档模式匹配
            # 这里可以添加更详细的模式验证逻辑
            assert "symbol" in actual_data
            assert "data" in actual_data
    
    def test_parameter_validation_matches_documentation(self, client: TestClient):
        """测试参数验证与文档一致"""
        # 测试必需参数缺失
        response = client.get("/api/v1/market-data/AAPL")
        assert response.status_code == 422
        
        error_detail = response.json()
        assert "detail" in error_detail
        
        # 验证错误信息格式与文档一致
        # 这里可以添加更详细的验证逻辑
    
    def test_status_codes_match_documentation(self, client: TestClient, mock_data_manager):
        """测试状态码与文档一致"""
        with patch('app.services.data_manager.DataManager', return_value=mock_data_manager):
            # 测试成功响应
            mock_data_manager.get_market_data.return_value = {"symbol": "AAPL", "data": []}
            response = client.get(
                "/api/v1/market-data/AAPL",
                params={"start_date": "2023-01-01", "end_date": "2023-12-31"}
            )
            assert response.status_code == 200
            
            # 测试404响应
            response = client.get("/api/v1/nonexistent")
            assert response.status_code == 404
            
            # 测试422响应
            response = client.post("/api/v1/strategies", json={"invalid": "data"})
            assert response.status_code == 422