import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
简化的后端启动脚本
"""

import uvicorn
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

def start_backend():
    """启动后端服务"""
    logger.info("🚀 启动量化交易系统后端服务...")
    logger.info("📍 服务地址: http://localhost:8000")
    logger.info("📚 API文档: http://localhost:8000/docs")
    logger.info("🔄 实时监控: http://localhost:8000/health")
    logger.info()
    
    try:
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        logger.info("\n🛑 后端服务已停止")
    except Exception as e:
        logger.info(f"❌ 后端启动失败: {e}")

if __name__ == "__main__":
    start_backend()