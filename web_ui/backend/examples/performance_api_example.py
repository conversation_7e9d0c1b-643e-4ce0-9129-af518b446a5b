import logging
logger = logging.getLogger(__name__)
#!/usr/bin/env python3
"""
绩效分析API使用示例

此脚本演示如何使用绩效分析API进行各种量化交易分析任务。
"""

import requests
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 配置
API_BASE_URL = "http://localhost:8000/api/v1"

def generate_sample_portfolio():
    """生成用于演示的样本投资组合数据"""
    # 创建一年的日度数据
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # 模拟投资组合表现
    np.random.seed(42)
    daily_returns = np.random.normal(0.0008, 0.02, len(dates))  # ~20%年化收益率，20%波动率
    
    portfolio_values = [100000]  # 起始资金10万美元
    for ret in daily_returns[1:]:
        portfolio_values.append(portfolio_values[-1] * (1 + ret))
    
    # 格式化为API格式
    portfolio_data = [
        {'timestamp': date.strftime('%Y-%m-%d'), 'value': value}
        for date, value in zip(dates, portfolio_values)
    ]
    
    return portfolio_data

def generate_sample_benchmark():
    """生成样本基准数据（例如，标普500）"""
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # 模拟具有较低收益和波动率的基准
    np.random.seed(123)
    daily_returns = np.random.normal(0.0005, 0.015, len(dates))  # ~12%年化收益率，15%波动率
    
    benchmark_values = [100000]
    for ret in daily_returns[1:]:
        benchmark_values.append(benchmark_values[-1] * (1 + ret))
    
    benchmark_data = [
        {'timestamp': date.strftime('%Y-%m-%d'), 'value': value}
        for date, value in zip(dates, benchmark_values)
    ]
    
    return benchmark_data

def generate_sample_trades():
    """Generate sample trade data."""
    trade_dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='W')
    trades = []
    
    np.random.seed(456)
    for i, date in enumerate(trade_dates):
        # Alternate between buy and sell
        side = 'BUY' if i % 2 == 0 else 'SELL'
        quantity = np.random.randint(50, 200)
        price = 100 + np.random.normal(0, 10)
        
        # Calculate P&L for sell orders
        pnl = None
        if side == 'SELL':
            pnl = np.random.normal(200, 500)  # Random P&L
        
        trades.append({
            'timestamp': date.strftime('%Y-%m-%d'),
            'symbol': 'PORTFOLIO',
            'side': side,
            'quantity': quantity,
            'price': price,
            'commission': 5.0,
            'pnl': pnl
        })
    
    return trades

def example_1_basic_performance_analysis():
    """示例1：基础绩效分析"""
    logger.info("=" * 60)
    logger.info("示例1：基础绩效分析")
    logger.info("=" * 60)
    
    # 生成样本数据
    portfolio_data = generate_sample_portfolio()
    benchmark_data = generate_sample_benchmark()
    trades_data = generate_sample_trades()
    
    # 准备API请求
    request_data = {
        "portfolio_values": portfolio_data,
        "trades": trades_data,
        "benchmark_values": benchmark_data,
        "risk_free_rate": 0.02,
        "frequency": "daily"
    }
    
    # 发起API调用
    try:
        response = requests.post(
            f"{API_BASE_URL}/performance/analyze",
            json=request_data,
            timeout=30
        )
        
        if response.status_code == 200:
            metrics = response.json()
            
            logger.info("📊 绩效指标:")
            logger.info(f"   总收益率: {metrics['basic_returns']['total_return']:.2%}")
            logger.info(f"   年化收益率: {metrics['basic_returns']['annualized_return']:.2%}")
            logger.info(f"   夏普比率: {metrics['risk_adjusted']['sharpe_ratio']:.3f}")
            logger.info(f"   最大回撤: {metrics['drawdowns']['max_drawdown']:.2%}")
            logger.info(f"   波动率: {metrics['risk_metrics']['volatility']:.2%}")
            
            if 'benchmark' in metrics:
                logger.info(f"   相对基准Alpha: {metrics['benchmark']['alpha']:.2%}")
                logger.info(f"   相对基准Beta: {metrics['benchmark']['beta']:.3f}")
            
            logger.info(f"   总交易次数: {metrics['trade_stats']['total_trades']}")
            logger.info(f"   胜率: {metrics['trade_stats']['win_rate']:.1%}")
            
            return metrics
        else:
            logger.info(f"❌ API错误: {response.status_code}")
            logger.info(f"   {response.text}")
            return None
            
    except Exception as e:
        logger.info(f"❌ 请求失败: {e}")
        return None

def example_2_generate_comprehensive_report():
    """Example 2: Generate comprehensive performance report."""
    logger.info("\n" + "=" * 60)
    logger.info("Example 2: Generate Comprehensive Report")
    logger.info("=" * 60)
    
    # Generate sample data
    portfolio_data = generate_sample_portfolio()
    benchmark_data = generate_sample_benchmark()
    trades_data = generate_sample_trades()
    
    # Prepare report request
    request_data = {
        "portfolio_values": portfolio_data,
        "trades": trades_data,
        "benchmark_values": benchmark_data,
        "format": "json",
        "config": {
            "title": "2023 Portfolio Performance Report",
            "subtitle": "Quantitative Trading Strategy Analysis",
            "author": "Trading Team",
            "include_charts": True,
            "include_monthly_returns": True,
            "include_trade_analysis": True,
            "include_rolling_metrics": True,
            "include_benchmark_comparison": True
        }
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/performance/generate-report",
            json=request_data,
            timeout=60
        )
        
        if response.status_code == 200:
            report = response.json()
            
            logger.info("📋 Report Generated Successfully:")
            logger.info(f"   Report ID: {report['report_id']}")
            logger.info(f"   Format: {report['format']}")
            logger.info(f"   Generated: {report['generated_at']}")
            
            if 'content' in report:
                content = report['content']
                logger.info(f"   Report Sections: {list(content.keys())}")
                
                # Display summary
                if 'summary' in content:
                    summary = content['summary']
                    logger.info(f"   Period: {summary['period']['start_date']} to {summary['period']['end_date']}")
                    logger.info(f"   Key Metrics:")
                    for metric, value in summary['key_metrics'].items():
                        if value is not None:
                            if 'pct' in metric or 'return' in metric:
                                logger.info(f"     {metric}: {value:.2f}%")
                            else:
                                logger.info(f"     {metric}: {value:.3f}")
            
            return report
        else:
            logger.info(f"❌ Report generation failed: {response.status_code}")
            logger.info(f"   {response.text}")
            return None
            
    except Exception as e:
        logger.info(f"❌ Request failed: {e}")
        return None

def example_3_compare_strategies():
    """Example 3: Compare multiple trading strategies."""
    logger.info("\n" + "=" * 60)
    logger.info("Example 3: Strategy Comparison")
    logger.info("=" * 60)
    
    # Generate data for different strategies
    strategies = []
    
    # Strategy 1: Aggressive (higher risk/return)
    np.random.seed(100)
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    returns = np.random.normal(0.001, 0.025, len(dates))
    values = [100000]
    for ret in returns[1:]:
        values.append(values[-1] * (1 + ret))
    
    strategies.append({
        'name': 'Aggressive Momentum',
        'portfolio_values': [
            {'timestamp': date.strftime('%Y-%m-%d'), 'value': value}
            for date, value in zip(dates, values)
        ],
        'metadata': {'description': 'High-risk momentum strategy'}
    })
    
    # Strategy 2: Conservative (lower risk/return)
    np.random.seed(200)
    returns = np.random.normal(0.0004, 0.012, len(dates))
    values = [100000]
    for ret in returns[1:]:
        values.append(values[-1] * (1 + ret))
    
    strategies.append({
        'name': 'Conservative Value',
        'portfolio_values': [
            {'timestamp': date.strftime('%Y-%m-%d'), 'value': value}
            for date, value in zip(dates, values)
        ],
        'metadata': {'description': 'Low-risk value strategy'}
    })
    
    # Strategy 3: Balanced
    np.random.seed(300)
    returns = np.random.normal(0.0007, 0.018, len(dates))
    values = [100000]
    for ret in returns[1:]:
        values.append(values[-1] * (1 + ret))
    
    strategies.append({
        'name': 'Balanced Multi-Factor',
        'portfolio_values': [
            {'timestamp': date.strftime('%Y-%m-%d'), 'value': value}
            for date, value in zip(dates, values)
        ],
        'metadata': {'description': 'Balanced multi-factor approach'}
    })
    
    # Prepare comparison request
    request_data = {
        "strategies": strategies,
        "comparison_methods": [
            "sharpe_ratio",
            "total_return", 
            "max_drawdown",
            "win_rate"
        ],
        "ranking_method": "weighted_score",
        "risk_free_rate": 0.02
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/performance/compare-strategies",
            json=request_data,
            timeout=60
        )
        
        if response.status_code == 200:
            comparison = response.json()
            
            logger.info("🏆 Strategy Comparison Results:")
            logger.info(f"   Strategies Compared: {comparison['summary']['strategies_compared']}")
            logger.info(f"   Top Strategy: {comparison['summary']['top_strategy']}")
            
            logger.info("\n📊 Rankings by Metric:")
            for metric, ranking in comparison['rankings'].items():
                logger.info(f"   {metric}: {' > '.join(ranking)}")
            
            logger.info("\n💰 Key Metrics by Strategy:")
            for strategy, metrics in comparison['key_metrics'].items():
                logger.info(f"   {strategy}:")
                for metric, value in metrics.items():
                    if value is not None:
                        if 'pct' in metric:
                            logger.info(f"     {metric}: {value:.2f}%")
                        else:
                            logger.info(f"     {metric}: {value:.3f}")
            
            if 'detailed_analysis' in comparison:
                analysis = comparison['detailed_analysis']
                if 'optimal_weights' in analysis:
                    logger.info("\n⚖️  Optimal Portfolio Weights:")
                    for strategy, weight in analysis['optimal_weights'].items():
                        logger.info(f"   {strategy}: {weight:.1%}")
                
                if 'recommendations' in analysis:
                    logger.info("\n💡 Recommendations:")
                    for rec in analysis['recommendations']:
                        logger.info(f"   • {rec.get('reason', 'No reason provided')}")
            
            return comparison
        else:
            logger.info(f"❌ Strategy comparison failed: {response.status_code}")
            logger.info(f"   {response.text}")
            return None
            
    except Exception as e:
        logger.info(f"❌ Request failed: {e}")
        return None

def example_4_rolling_metrics():
    """Example 4: Calculate rolling performance metrics."""
    logger.info("\n" + "=" * 60)
    logger.info("Example 4: Rolling Performance Metrics")
    logger.info("=" * 60)
    
    # Generate sample data
    portfolio_data = generate_sample_portfolio()
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/performance/rolling-metrics",
            params={
                'portfolio_values': json.dumps(portfolio_data),
                'window': 60,  # 60-day rolling window
                'frequency': 'daily'
            },
            timeout=30
        )
        
        if response.status_code == 200:
            rolling_data = response.json()
            
            logger.info("📈 Rolling Metrics (60-day window):")
            logger.info(f"   Data Points: {len(rolling_data['dates'])}")
            logger.info(f"   Window Size: {rolling_data['window_size']} days")
            
            # Calculate some statistics
            metrics = rolling_data['metrics']
            for metric_name, values in metrics.items():
                values_array = np.array(values)
                logger.info(f"   {metric_name}:")
                logger.info(f"     Average: {np.mean(values_array):.3f}")
                logger.info(f"     Min: {np.min(values_array):.3f}")
                logger.info(f"     Max: {np.max(values_array):.3f}")
                logger.info(f"     Std Dev: {np.std(values_array):.3f}")
            
            return rolling_data
        else:
            logger.info(f"❌ Rolling metrics failed: {response.status_code}")
            logger.info(f"   {response.text}")
            return None
            
    except Exception as e:
        logger.info(f"❌ Request failed: {e}")
        return None

def example_5_monthly_returns():
    """Example 5: Calculate monthly returns table."""
    logger.info("\n" + "=" * 60)
    logger.info("Example 5: Monthly Returns Analysis")
    logger.info("=" * 60)
    
    # Generate sample data
    portfolio_data = generate_sample_portfolio()
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/performance/monthly-returns",
            params={
                'portfolio_values': json.dumps(portfolio_data)
            },
            timeout=30
        )
        
        if response.status_code == 200:
            monthly_data = response.json()
            
            logger.info("📅 Monthly Returns Analysis:")
            
            # Display summary
            summary = monthly_data['summary']
            logger.info(f"   Years Covered: {summary['years_covered']}")
            logger.info(f"   Total Months: {summary['months_covered']}")
            logger.info(f"   Positive Months: {summary['positive_months']}")
            logger.info(f"   Negative Months: {summary['negative_months']}")
            logger.info(f"   Best Month: {summary['best_month']:.2%}")
            logger.info(f"   Worst Month: {summary['worst_month']:.2%}")
            logger.info(f"   Average Monthly Return: {summary['avg_monthly_return']:.2%}")
            
            # Display monthly returns table
            logger.info("\n📊 Monthly Returns Table:")
            monthly_returns = monthly_data['monthly_returns']
            
            # Print header
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Annual']
            logger.info(f"{'Year':<6} " + " ".join(f"{month:>7}" for month in months))
            logger.info("-" * 100)
            
            # Print data for each year
            for year, year_data in monthly_returns.items():
                row = f"{year:<6} "
                for month in months:
                    value = year_data.get(month, 0)
                    if value != 0:
                        row += f"{value:>7.1%} "
                    else:
                        row += f"{'--':>7} "
                logger.info(row)
            
            return monthly_data
        else:
            logger.info(f"❌ Monthly returns failed: {response.status_code}")
            logger.info(f"   {response.text}")
            return None
            
    except Exception as e:
        logger.info(f"❌ Request failed: {e}")
        return None

def main():
    """运行所有示例"""
    logger.info("🚀 绩效分析API示例")
    logger.info("=" * 60)
    logger.info("此脚本演示绩效分析API的各种用法")
    logger.info("确保FastAPI服务器在 http://localhost:8000 上运行")
    logger.info()
    
    # 检查服务器是否可用
    try:
        response = requests.get(f"{API_BASE_URL}/performance/health", timeout=5)
        if response.status_code == 200:
            logger.info("✅ API服务器可用")
        else:
            logger.info("❌ API服务器响应错误")
            return
    except:
        logger.info("❌ 无法连接到API服务器")
        logger.info("请使用以下命令启动服务器: uvicorn main:app --reload")
        return
    
    # 运行示例
    examples = [
        example_1_basic_performance_analysis,
        example_2_generate_comprehensive_report,
        example_3_compare_strategies,
        example_4_rolling_metrics,
        example_5_monthly_returns
    ]
    
    for example in examples:
        try:
            example()
        except KeyboardInterrupt:
            logger.info("\n⏹️  示例被用户中断")
            break
        except Exception as e:
            logger.info(f"❌ 示例失败: {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info("✅ 示例完成!")
    logger.info("查看API文档: http://localhost:8000/docs")

if __name__ == "__main__":
    main()