"""
多市场数据服务
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any, Tuple, Union
import asyncio
from concurrent.futures import ThreadPoolExecutor
import json

from src.market.data.adapters.manager import DataSourceManager
from src.market.data.adapters.fred_adapter import FREDAdapter
from src.market.data.adapters.base import DataSourceConfig
from src.models.market_data import UnifiedMarketData, EconomicData
from app.models.multi_market import (
    MarketType, EconomicIndicatorCategory, MarketDataPoint,
    PerformanceMetrics, ArbitrageOpportunity, EconomicIndicator,
    DataCoverageInfo, MarketStatus, CurrencyPair, ExchangeRate
)

logger = logging.getLogger(__name__)


class MultiMarketService:
    """多市场数据服务类"""
    
    # 市场映射配置
    MARKET_MAPPING = {
        MarketType.US: {
            "name": "美国股票市场",
            "timezone": "US/Eastern",
            "currency": "USD",
            "adapters": ["yahoo_finance", "alpha_vantage"],
            "trading_hours": {"open": "09:30", "close": "16:00"}
        },
        MarketType.CN: {
            "name": "中国A股市场",
            "timezone": "Asia/Shanghai",
            "currency": "CNY",
            "adapters": ["akshare", "tushare"],
            "trading_hours": {"open": "09:30", "close": "15:00"}
        },
        MarketType.HK: {
            "name": "香港股票市场",
            "timezone": "Asia/Hong_Kong",
            "currency": "HKD",
            "adapters": ["yahoo_finance", "futu"],
            "trading_hours": {"open": "09:30", "close": "16:00"}
        },
        MarketType.CRYPTO: {
            "name": "加密货币市场",
            "timezone": "UTC",
            "currency": "USD",
            "adapters": ["binance", "coinbase"],
            "trading_hours": {"open": "00:00", "close": "23:59"}
        },
        MarketType.FOREX: {
            "name": "外汇市场",
            "timezone": "UTC",
            "currency": "USD",
            "adapters": ["forex_api", "yahoo_finance"],
            "trading_hours": {"open": "00:00", "close": "23:59"}
        },
        MarketType.COMMODITY: {
            "name": "商品市场",
            "timezone": "US/Eastern",
            "currency": "USD",
            "adapters": ["yahoo_finance", "quandl"],
            "trading_hours": {"open": "09:00", "close": "17:00"}
        },
        MarketType.ECONOMIC: {
            "name": "经济数据",
            "timezone": "US/Eastern",
            "currency": "USD",
            "adapters": ["fred"],
            "trading_hours": {"open": "00:00", "close": "23:59"}
        }
    }
    
    # 主要货币汇率符号映射
    CURRENCY_PAIRS = {
        ("USD", "CNY"): "USDCNY=X",
        ("USD", "EUR"): "USDEUR=X",
        ("USD", "JPY"): "USDJPY=X",
        ("USD", "GBP"): "USDGBP=X",
        ("USD", "HKD"): "USDHKD=X",
        ("EUR", "USD"): "EURUSD=X",
        ("GBP", "USD"): "GBPUSD=X",
        ("JPY", "USD"): "JPYUSD=X",
        ("CNY", "USD"): "CNYUSD=X",
        ("HKD", "USD"): "HKDUSD=X"
    }
    
    def __init__(self, data_manager):
        """初始化多市场服务"""
        self.data_manager = data_manager
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 初始化FRED适配器用于经济数据
        self.fred_adapter = None
        self._init_fred_adapter()
        
        # 缓存
        self._exchange_rate_cache = {}
        self._cache_expiry = {}
        
        logger.info("多市场服务初始化完成")
    
    def _init_fred_adapter(self):
        """初始化FRED适配器"""
        try:
            # 从配置中获取FRED API密钥
            from app.core.config import settings
            
            fred_config = DataSourceConfig(
                name="fred",
                adapter_class="src.data.adapters.fred_adapter.FREDAdapter",
                enabled=True,
                priority=1,
                credentials={"api_key": settings.FRED_API_KEY},
                rate_limit={"requests_per_minute": 120}
            )
            
            self.fred_adapter = FREDAdapter(fred_config)
            logger.info("FRED适配器初始化成功")
            
        except Exception as e:
            logger.warning(f"FRED适配器初始化失败: {e}")
            self.fred_adapter = None
    
    def get_supported_markets(self) -> List[Dict[str, Any]]:
        """获取支持的市场列表"""
        markets = []
        
        for market_type, config in self.MARKET_MAPPING.items():
            markets.append({
                "code": market_type.value,
                "name": config["name"],
                "timezone": config["timezone"],
                "currency": config["currency"],
                "trading_hours": config["trading_hours"],
                "adapters": config["adapters"]
            })
        
        return markets
    
    def get_market_symbols(
        self, 
        market: str, 
        limit: Optional[int] = None,
        search: Optional[str] = None
    ) -> List[str]:
        """获取指定市场的标的列表"""
        try:
            # 获取市场对应的适配器
            market_adapters = self._get_market_adapters(market)
            
            all_symbols = set()
            
            for adapter_name in market_adapters:
                try:
                    adapter = self.data_manager.get_adapter(adapter_name)
                    if adapter and adapter.is_healthy:
                        symbols = adapter.get_available_symbols()
                        all_symbols.update(symbols)
                except Exception as e:
                    logger.warning(f"获取适配器 {adapter_name} 标的失败: {e}")
                    continue
            
            symbols_list = list(all_symbols)
            
            # 应用搜索过滤
            if search:
                search_lower = search.lower()
                symbols_list = [s for s in symbols_list if search_lower in s.lower()]
            
            # 应用数量限制
            if limit:
                symbols_list = symbols_list[:limit]
            
            return sorted(symbols_list)
            
        except Exception as e:
            logger.error(f"获取市场 {market} 标的列表失败: {e}")
            return []
    
    def _get_market_adapters(self, market: str) -> List[str]:
        """获取市场对应的适配器列表"""
        try:
            market_type = MarketType(market)
            return self.MARKET_MAPPING[market_type]["adapters"]
        except (ValueError, KeyError):
            logger.warning(f"未知市场类型: {market}")
            return []
    
    def fetch_multi_market_data(
        self,
        symbols: List[str],
        start_date: date,
        end_date: date,
        interval: str = "1d",
        markets: Optional[List[str]] = None,
        unified_currency: Optional[str] = "USD",
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """获取多市场数据"""
        try:
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())
            
            data_result = {}
            metadata = {}
            total_count = 0
            
            for symbol in symbols:
                try:
                    # 获取数据
                    data = self.data_manager.get_market_data(
                        symbol, start_datetime, end_datetime, 
                        market=None, use_cache=use_cache
                    )
                    
                    if data is not None and not data.empty:
                        # 转换为标准格式
                        market_data_points = []
                        
                        for _, row in data.iterrows():
                            timestamp = row.get('timestamp', row.name)
                            if hasattr(timestamp, 'to_pydatetime'):
                                timestamp = timestamp.to_pydatetime()
                            elif not isinstance(timestamp, datetime):
                                timestamp = pd.to_datetime(timestamp).to_pydatetime()
                            
                            # 确定市场类型
                            market_type = self._detect_market_type(symbol)
                            currency = self._get_market_currency(market_type)
                            
                            point = MarketDataPoint(
                                timestamp=timestamp,
                                open=float(row.get('open', 0)),
                                high=float(row.get('high', 0)),
                                low=float(row.get('low', 0)),
                                close=float(row.get('close', 0)),
                                volume=int(row.get('volume', 0)),
                                adj_close=float(row.get('adj_close', row.get('close', 0))),
                                market=market_type,
                                currency=currency
                            )
                            
                            market_data_points.append(point)
                        
                        # 货币转换
                        if unified_currency and unified_currency != currency:
                            market_data_points = self._convert_currency_for_data(
                                market_data_points, currency, unified_currency
                            )
                        
                        data_result[symbol] = market_data_points
                        total_count += len(market_data_points)
                        
                        # 添加元数据
                        metadata[symbol] = {
                            "market": market_type,
                            "currency": currency,
                            "data_points": len(market_data_points),
                            "date_range": {
                                "start": start_date.isoformat(),
                                "end": end_date.isoformat()
                            }
                        }
                    else:
                        # 尝试从数据源获取
                        success, result = self.data_manager.fetch_data_from_source(
                            symbol, start_datetime, end_datetime, interval
                        )
                        
                        if success:
                            # 重新获取数据
                            data = self.data_manager.get_market_data(
                                symbol, start_datetime, end_datetime, use_cache=False
                            )
                            
                            if data is not None and not data.empty:
                                # 重复上面的处理逻辑
                                market_data_points = []
                                market_type = self._detect_market_type(symbol)
                                currency = self._get_market_currency(market_type)
                                
                                for _, row in data.iterrows():
                                    timestamp = row.get('timestamp', row.name)
                                    if hasattr(timestamp, 'to_pydatetime'):
                                        timestamp = timestamp.to_pydatetime()
                                    elif not isinstance(timestamp, datetime):
                                        timestamp = pd.to_datetime(timestamp).to_pydatetime()
                                    
                                    point = MarketDataPoint(
                                        timestamp=timestamp,
                                        open=float(row.get('open', 0)),
                                        high=float(row.get('high', 0)),
                                        low=float(row.get('low', 0)),
                                        close=float(row.get('close', 0)),
                                        volume=int(row.get('volume', 0)),
                                        adj_close=float(row.get('adj_close', row.get('close', 0))),
                                        market=market_type,
                                        currency=currency
                                    )
                                    
                                    market_data_points.append(point)
                                
                                if unified_currency and unified_currency != currency:
                                    market_data_points = self._convert_currency_for_data(
                                        market_data_points, currency, unified_currency
                                    )
                                
                                data_result[symbol] = market_data_points
                                total_count += len(market_data_points)
                                
                                metadata[symbol] = {
                                    "market": market_type,
                                    "currency": currency,
                                    "data_points": len(market_data_points),
                                    "date_range": {
                                        "start": start_date.isoformat(),
                                        "end": end_date.isoformat()
                                    }
                                }
                        
                        if symbol not in data_result:
                            logger.warning(f"未找到标的 {symbol} 的数据")
                            data_result[symbol] = []
                            metadata[symbol] = {
                                "market": "unknown",
                                "currency": "unknown",
                                "data_points": 0,
                                "error": "数据不可用"
                            }
                
                except Exception as e:
                    logger.error(f"获取标的 {symbol} 数据失败: {e}")
                    data_result[symbol] = []
                    metadata[symbol] = {
                        "market": "unknown",
                        "currency": "unknown",
                        "data_points": 0,
                        "error": str(e)
                    }
            
            return {
                "data": data_result,
                "metadata": metadata,
                "count": total_count
            }
            
        except Exception as e:
            logger.error(f"获取多市场数据失败: {e}")
            raise
    
    def _detect_market_type(self, symbol: str) -> str:
        """检测标的所属市场类型"""
        symbol_upper = symbol.upper()
        
        # 简单的市场检测逻辑
        if symbol_upper.endswith('.SS') or symbol_upper.endswith('.SZ'):
            return MarketType.CN.value
        elif symbol_upper.endswith('.HK'):
            return MarketType.HK.value
        elif any(crypto in symbol_upper for crypto in ['BTC', 'ETH', 'USDT', 'BNB']):
            return MarketType.CRYPTO.value
        elif any(forex in symbol_upper for forex in ['USD', 'EUR', 'JPY', 'GBP', '=X']):
            return MarketType.FOREX.value
        elif any(commodity in symbol_upper for commodity in ['GC=F', 'CL=F', 'SI=F']):
            return MarketType.COMMODITY.value
        else:
            return MarketType.US.value  # 默认为美股
    
    def _get_market_currency(self, market_type: str) -> str:
        """获取市场的主要货币"""
        try:
            market_enum = MarketType(market_type)
            return self.MARKET_MAPPING[market_enum]["currency"]
        except (ValueError, KeyError):
            return "USD"  # 默认货币
    
    def _convert_currency_for_data(
        self, 
        data_points: List[MarketDataPoint], 
        from_currency: str, 
        to_currency: str
    ) -> List[MarketDataPoint]:
        """为数据点进行货币转换"""
        try:
            # 获取汇率（这里简化处理，实际应该获取历史汇率）
            exchange_rate = self._get_exchange_rate(from_currency, to_currency)
            
            if exchange_rate is None:
                logger.warning(f"无法获取 {from_currency} 到 {to_currency} 的汇率")
                return data_points
            
            # 转换价格
            converted_points = []
            for point in data_points:
                converted_point = MarketDataPoint(
                    timestamp=point.timestamp,
                    open=point.open * exchange_rate,
                    high=point.high * exchange_rate,
                    low=point.low * exchange_rate,
                    close=point.close * exchange_rate,
                    volume=point.volume,  # 成交量不转换
                    adj_close=point.adj_close * exchange_rate if point.adj_close else None,
                    market=point.market,
                    currency=to_currency
                )
                converted_points.append(converted_point)
            
            return converted_points
            
        except Exception as e:
            logger.error(f"货币转换失败: {e}")
            return data_points
    
    def get_exchange_rates(
        self, 
        base_currency: str, 
        target_currencies: List[str],
        date: Optional[date] = None
    ) -> Dict[str, float]:
        """获取汇率信息"""
        rates = {}
        
        for target_currency in target_currencies:
            if base_currency == target_currency:
                rates[target_currency] = 1.0
                continue
            
            rate = self._get_exchange_rate(base_currency, target_currency, date)
            if rate is not None:
                rates[target_currency] = rate
            else:
                rates[target_currency] = None
        
        return rates
    
    def _get_exchange_rate(
        self, 
        from_currency: str, 
        to_currency: str,
        date: Optional[date] = None
    ) -> Optional[float]:
        """获取汇率"""
        if from_currency == to_currency:
            return 1.0
        
        # 检查缓存
        cache_key = f"{from_currency}_{to_currency}_{date or 'latest'}"
        if cache_key in self._exchange_rate_cache:
            cache_time = self._cache_expiry.get(cache_key, datetime.min)
            if datetime.now() - cache_time < timedelta(hours=1):  # 缓存1小时
                return self._exchange_rate_cache[cache_key]
        
        try:
            # 尝试从货币对符号获取汇率
            pair_symbol = self.CURRENCY_PAIRS.get((from_currency, to_currency))
            
            if pair_symbol:
                # 从数据管理器获取汇率数据
                end_date = date or datetime.now().date()
                start_date = end_date - timedelta(days=7)  # 获取最近一周的数据
                
                start_datetime = datetime.combine(start_date, datetime.min.time())
                end_datetime = datetime.combine(end_date, datetime.max.time())
                
                rate_data = self.data_manager.get_market_data(
                    pair_symbol, start_datetime, end_datetime
                )
                
                if rate_data is not None and not rate_data.empty:
                    # 获取最新的汇率
                    latest_rate = float(rate_data.iloc[-1]['close'])
                    
                    # 缓存结果
                    self._exchange_rate_cache[cache_key] = latest_rate
                    self._cache_expiry[cache_key] = datetime.now()
                    
                    return latest_rate
            
            # 尝试反向汇率
            reverse_pair = self.CURRENCY_PAIRS.get((to_currency, from_currency))
            if reverse_pair:
                end_date = date or datetime.now().date()
                start_date = end_date - timedelta(days=7)
                
                start_datetime = datetime.combine(start_date, datetime.min.time())
                end_datetime = datetime.combine(end_date, datetime.max.time())
                
                rate_data = self.data_manager.get_market_data(
                    reverse_pair, start_datetime, end_datetime
                )
                
                if rate_data is not None and not rate_data.empty:
                    reverse_rate = float(rate_data.iloc[-1]['close'])
                    rate = 1.0 / reverse_rate
                    
                    # 缓存结果
                    self._exchange_rate_cache[cache_key] = rate
                    self._cache_expiry[cache_key] = datetime.now()
                    
                    return rate
            
            # 如果都没有找到，返回None
            logger.warning(f"无法获取 {from_currency} 到 {to_currency} 的汇率")
            return None
            
        except Exception as e:
            logger.error(f"获取汇率失败: {e}")
            return None
    
    def convert_currency(
        self, 
        amount: float, 
        from_currency: str, 
        to_currency: str,
        date: Optional[date] = None
    ) -> Dict[str, Any]:
        """执行货币转换"""
        try:
            exchange_rate = self._get_exchange_rate(from_currency, to_currency, date)
            
            if exchange_rate is None:
                raise ValueError(f"无法获取 {from_currency} 到 {to_currency} 的汇率")
            
            converted_amount = amount * exchange_rate
            conversion_date = date or datetime.now().date()
            
            return {
                "converted_amount": converted_amount,
                "exchange_rate": exchange_rate,
                "conversion_date": conversion_date
            }
            
        except Exception as e:
            logger.error(f"货币转换失败: {e}")
            raise
    
    def analyze_cross_market_correlation(
        self,
        symbols: List[str],
        start_date: date,
        end_date: date,
        method: str = "pearson",
        unified_currency: Optional[str] = "USD"
    ) -> Dict[str, Any]:
        """分析跨市场相关性"""
        try:
            # 获取所有标的的数据
            multi_data = self.fetch_multi_market_data(
                symbols, start_date, end_date, 
                unified_currency=unified_currency
            )
            
            # 构建价格矩阵
            price_data = {}
            
            for symbol, data_points in multi_data["data"].items():
                if data_points:
                    prices = []
                    timestamps = []
                    
                    for point in data_points:
                        prices.append(point.close)
                        timestamps.append(point.timestamp)
                    
                    price_series = pd.Series(prices, index=timestamps, name=symbol)
                    price_data[symbol] = price_series
            
            if len(price_data) < 2:
                raise ValueError("至少需要2个有效标的进行相关性分析")
            
            # 创建DataFrame并对齐时间序列
            df = pd.DataFrame(price_data)
            df = df.dropna()  # 删除缺失值
            
            if df.empty:
                raise ValueError("没有足够的重叠数据进行相关性分析")
            
            # 计算收益率
            returns = df.pct_change().dropna()
            
            # 计算相关性矩阵
            if method == "pearson":
                correlation_matrix = returns.corr(method='pearson')
            elif method == "spearman":
                correlation_matrix = returns.corr(method='spearman')
            elif method == "kendall":
                correlation_matrix = returns.corr(method='kendall')
            else:
                raise ValueError(f"不支持的相关性方法: {method}")
            
            # 转换为字典格式
            corr_dict = {}
            for symbol1 in correlation_matrix.index:
                corr_dict[symbol1] = {}
                for symbol2 in correlation_matrix.columns:
                    corr_value = correlation_matrix.loc[symbol1, symbol2]
                    corr_dict[symbol1][symbol2] = float(corr_value) if not pd.isna(corr_value) else None
            
            # 计算统计信息
            corr_values = []
            significant_pairs = []
            
            for i, symbol1 in enumerate(symbols):
                for j, symbol2 in enumerate(symbols):
                    if i < j:  # 避免重复和自相关
                        corr_value = corr_dict.get(symbol1, {}).get(symbol2)
                        if corr_value is not None:
                            corr_values.append(corr_value)
                            
                            # 识别显著相关的标的对（相关性绝对值 > 0.7）
                            if abs(corr_value) > 0.7:
                                significant_pairs.append({
                                    "symbol1": symbol1,
                                    "symbol2": symbol2,
                                    "correlation": corr_value,
                                    "strength": "强相关" if abs(corr_value) > 0.8 else "中等相关"
                                })
            
            statistics = {
                "mean_correlation": float(np.mean(corr_values)) if corr_values else 0.0,
                "max_correlation": float(np.max(corr_values)) if corr_values else 0.0,
                "min_correlation": float(np.min(corr_values)) if corr_values else 0.0,
                "std_correlation": float(np.std(corr_values)) if corr_values else 0.0,
                "significant_pairs": significant_pairs
            }
            
            return {
                "correlation_matrix": corr_dict,
                "statistics": statistics
            }
            
        except Exception as e:
            logger.error(f"跨市场相关性分析失败: {e}")
            raise
    
    def get_economic_indicator_categories(self) -> List[Dict[str, Any]]:
        """获取经济指标分类"""
        if not self.fred_adapter:
            return []
        
        try:
            categories = self.fred_adapter.get_categories()
            
            result = []
            for category_name, series_list in categories.items():
                result.append({
                    "name": category_name,
                    "code": category_name.upper().replace(" ", "_"),
                    "series_count": len(series_list),
                    "sample_series": series_list[:5]  # 前5个作为示例
                })
            
            return result
            
        except Exception as e:
            logger.error(f"获取经济指标分类失败: {e}")
            return []
    
    def get_economic_indicators(
        self, 
        category: Optional[str] = None,
        search: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """获取经济指标列表"""
        if not self.fred_adapter:
            return []
        
        try:
            if search:
                # 搜索指标
                search_results = self.fred_adapter.search_series(search, limit)
                return search_results
            elif category:
                # 按分类获取
                series_ids = self.fred_adapter.get_series_by_category(category)
                
                indicators = []
                for series_id in series_ids[:limit]:
                    series_info = self.fred_adapter.get_series_info(series_id)
                    if series_info:
                        indicators.append({
                            "id": series_id,
                            "title": series_info.get("title", series_id),
                            "category": category,
                            "units": series_info.get("units", ""),
                            "frequency": series_info.get("frequency", ""),
                            "seasonal_adjustment": series_info.get("seasonal_adjustment", ""),
                            "last_updated": series_info.get("last_updated", ""),
                            "notes": series_info.get("notes", "")[:200] if series_info.get("notes") else ""
                        })
                
                return indicators
            else:
                # 获取热门指标
                popular_series = self.fred_adapter.get_popular_series()
                
                indicators = []
                for series_id in popular_series[:limit]:
                    series_info = self.fred_adapter.get_series_info(series_id)
                    if series_info:
                        indicators.append({
                            "id": series_id,
                            "title": series_info.get("title", series_id),
                            "category": "热门指标",
                            "units": series_info.get("units", ""),
                            "frequency": series_info.get("frequency", ""),
                            "seasonal_adjustment": series_info.get("seasonal_adjustment", ""),
                            "last_updated": series_info.get("last_updated", ""),
                            "notes": series_info.get("notes", "")[:200] if series_info.get("notes") else ""
                        })
                
                return indicators
            
        except Exception as e:
            logger.error(f"获取经济指标列表失败: {e}")
            return []
    
    def fetch_economic_data(
        self,
        series_ids: List[str],
        start_date: date,
        end_date: date,
        frequency: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取经济数据"""
        if not self.fred_adapter:
            raise ValueError("FRED适配器未初始化")
        
        try:
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())
            
            # 批量获取经济数据
            economic_data_dict = self.fred_adapter.fetch_multiple_series(
                series_ids, start_datetime, end_datetime
            )
            
            # 转换为API响应格式
            data_result = {}
            metadata = {}
            total_count = 0
            
            for series_id, economic_data_list in economic_data_dict.items():
                data_points = []
                
                for econ_data in economic_data_list:
                    data_points.append({
                        "series_id": econ_data.series_id,
                        "timestamp": econ_data.timestamp,
                        "value": econ_data.value,
                        "unit": econ_data.unit,
                        "frequency": econ_data.frequency,
                        "seasonal_adjustment": econ_data.seasonal_adjustment,
                        "notes": econ_data.notes
                    })
                
                data_result[series_id] = data_points
                total_count += len(data_points)
                
                # 获取系列信息作为元数据
                series_info = self.fred_adapter.get_series_info(series_id)
                metadata[series_id] = {
                    "title": series_info.get("title", series_id) if series_info else series_id,
                    "units": series_info.get("units", "") if series_info else "",
                    "frequency": series_info.get("frequency", "") if series_info else "",
                    "data_points": len(data_points),
                    "date_range": {
                        "start": start_date.isoformat(),
                        "end": end_date.isoformat()
                    }
                }
            
            return {
                "data": data_result,
                "metadata": metadata,
                "count": total_count
            }
            
        except Exception as e:
            logger.error(f"获取经济数据失败: {e}")
            raise
    
    def search_economic_indicators(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """搜索经济指标"""
        if not self.fred_adapter:
            return []
        
        try:
            return self.fred_adapter.search_series(query, limit)
        except Exception as e:
            logger.error(f"搜索经济指标失败: {e}")
            return []
    
    def get_latest_economic_value(self, series_id: str) -> Optional[Dict[str, Any]]:
        """获取经济指标最新值"""
        if not self.fred_adapter:
            return None
        
        try:
            return self.fred_adapter.get_latest_value(series_id)
        except Exception as e:
            logger.error(f"获取经济指标最新值失败: {e}")
            return None
    
    def get_multi_market_status(self) -> Dict[str, Any]:
        """获取多市场状态"""
        market_status = {}
        
        for market_type, config in self.MARKET_MAPPING.items():
            try:
                adapters = config["adapters"]
                healthy_adapters = 0
                total_symbols = 0
                
                for adapter_name in adapters:
                    adapter = self.data_manager.get_adapter(adapter_name)
                    if adapter:
                        if adapter.is_healthy:
                            healthy_adapters += 1
                        
                        try:
                            symbols = adapter.get_available_symbols()
                            total_symbols += len(symbols)
                        except:
                            
        return []  # 返回空数据，避免系统错误
                market_status[market_type.value] = {
                    "name": config["name"],
                    "healthy": healthy_adapters > 0,
                    "healthy_adapters": healthy_adapters,
                    "total_adapters": len(adapters),
                    "symbol_count": total_symbols,
                    "currency": config["currency"],
                    "timezone": config["timezone"],
                    "trading_hours": config["trading_hours"],
                    "last_update": datetime.now()
                }
                
            except Exception as e:
                market_status[market_type.value] = {
                    "name": config["name"],
                    "healthy": False,
                    "error": str(e),
                    "last_update": datetime.now()
                }
        
        return market_status
    
    def sync_multi_market_data(
        self,
        symbols: List[str],
        markets: Optional[List[str]] = None,
        start_date: date = None,
        end_date: date = None,
        interval: str = "1d"
    ) -> Dict[str, Any]:
        """同步多市场数据"""
        try:
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())
            
            sync_results = {
                "successful_symbols": [],
                "failed_symbols": [],
                "total_records": 0,
                "errors": []
            }
            
            for symbol in symbols:
                try:
                    # 尝试从数据源获取数据
                    success, result = self.data_manager.fetch_data_from_source(
                        symbol, start_datetime, end_datetime, interval
                    )
                    
                    if success:
                        sync_results["successful_symbols"].append(symbol)
                        if isinstance(result, list):
                            sync_results["total_records"] += len(result)
                    else:
                        sync_results["failed_symbols"].append(symbol)
                        sync_results["errors"].append({
                            "symbol": symbol,
                            "error": str(result)
                        })
                        
                except Exception as e:
                    sync_results["failed_symbols"].append(symbol)
                    sync_results["errors"].append({
                        "symbol": symbol,
                        "error": str(e)
                    })
            
            return sync_results
            
        except Exception as e:
            logger.error(f"多市场数据同步失败: {e}")
            raise
    
    def get_data_coverage_report(self) -> Dict[str, Any]:
        """获取数据覆盖报告"""
        try:
            coverage_report = {
                "markets": {},
                "total_symbols": 0,
                "total_adapters": 0,
                "healthy_adapters": 0,
                "generated_at": datetime.now().isoformat()
            }
            
            for market_type, config in self.MARKET_MAPPING.items():
                market_coverage = {
                    "name": config["name"],
                    "adapters": [],
                    "total_symbols": 0,
                    "healthy": False
                }
                
                for adapter_name in config["adapters"]:
                    adapter = self.data_manager.get_adapter(adapter_name)
                    coverage_report["total_adapters"] += 1
                    
                    if adapter:
                        adapter_info = {
                            "name": adapter_name,
                            "healthy": adapter.is_healthy,
                            "symbol_count": 0,
                            "status": adapter.get_status()
                        }
                        
                        if adapter.is_healthy:
                            coverage_report["healthy_adapters"] += 1
                            market_coverage["healthy"] = True
                            
                            try:
                                symbols = adapter.get_available_symbols()
                                adapter_info["symbol_count"] = len(symbols)
                                market_coverage["total_symbols"] += len(symbols)
                            except:
                                
        return []  # 返回空数据，避免系统错误
                        market_coverage["adapters"].append(adapter_info)
                
                coverage_report["markets"][market_type.value] = market_coverage
                coverage_report["total_symbols"] += market_coverage["total_symbols"]
            
            return coverage_report
            
        except Exception as e:
            logger.error(f"获取数据覆盖报告失败: {e}")
            raise
    
    def compare_multi_market_performance(
        self,
        symbols: List[str],
        start_date: date,
        end_date: date,
        unified_currency: str = "USD"
    ) -> Dict[str, Any]:
        """对比多市场绩效"""
        try:
            # 获取数据
            multi_data = self.fetch_multi_market_data(
                symbols, start_date, end_date, 
                unified_currency=unified_currency
            )
            
            performance_comparison = {}
            
            for symbol, data_points in multi_data["data"].items():
                if not data_points:
                    continue
                
                # 计算绩效指标
                prices = [point.close for point in data_points]
                
                if len(prices) < 2:
                    continue
                
                start_price = prices[0]
                end_price = prices[-1]
                total_return = (end_price - start_price) / start_price
                
                # 计算年化收益率
                days = (end_date - start_date).days
                if days > 0:
                    annualized_return = (1 + total_return) ** (365.25 / days) - 1
                else:
                    annualized_return = 0
                
                # 计算波动率
                returns = []
                for i in range(1, len(prices)):
                    daily_return = (prices[i] - prices[i-1]) / prices[i-1]
                    returns.append(daily_return)
                
                volatility = np.std(returns) * np.sqrt(252) if returns else 0  # 年化波动率
                
                # 计算夏普比率（假设无风险利率为2%）
                risk_free_rate = 0.02
                sharpe_ratio = (annualized_return - risk_free_rate) / volatility if volatility > 0 else 0
                
                # 计算最大回撤
                peak = prices[0]
                max_drawdown = 0
                
                for price in prices:
                    if price > peak:
                        peak = price
                    drawdown = (peak - price) / peak
                    if drawdown > max_drawdown:
                        max_drawdown = drawdown
                
                performance_comparison[symbol] = {
                    "market": multi_data["metadata"][symbol]["market"],
                    "total_return": total_return,
                    "annualized_return": annualized_return,
                    "volatility": volatility,
                    "sharpe_ratio": sharpe_ratio,
                    "max_drawdown": max_drawdown,
                    "start_price": start_price,
                    "end_price": end_price,
                    "currency": unified_currency
                }
            
            return performance_comparison
            
        except Exception as e:
            logger.error(f"多市场绩效对比失败: {e}")
            raise
    
    def analyze_arbitrage_opportunities(
        self,
        symbol: str,
        markets: List[str],
        threshold: float = 0.01
    ) -> List[Dict[str, Any]]:
        """分析跨市场套利机会"""
        try:
            # 获取不同市场的最新价格
            opportunities = []
            market_prices = {}
            
            # 这里简化处理，实际应该获取实时价格
            for market in markets:
                try:
                    # 根据市场调整标的符号
                    market_symbol = self._adjust_symbol_for_market(symbol, market)
                    
                    # 获取最新数据
                    end_date = datetime.now().date()
                    start_date = end_date - timedelta(days=7)
                    
                    data = self.fetch_multi_market_data(
                        [market_symbol], start_date, end_date
                    )
                    
                    if market_symbol in data["data"] and data["data"][market_symbol]:
                        latest_point = data["data"][market_symbol][-1]
                        market_prices[market] = {
                            "price": latest_point.close,
                            "currency": latest_point.currency,
                            "timestamp": latest_point.timestamp
                        }
                        
                except Exception as e:
                    logger.warning(f"获取市场 {market} 价格失败: {e}")
                    continue
            
            # 分析套利机会
            market_list = list(market_prices.keys())
            
            for i in range(len(market_list)):
                for j in range(i + 1, len(market_list)):
                    market1 = market_list[i]
                    market2 = market_list[j]
                    
                    price1_info = market_prices[market1]
                    price2_info = market_prices[market2]
                    
                    # 统一货币（简化处理）
                    price1 = price1_info["price"]
                    price2 = price2_info["price"]
                    
                    # 计算价差
                    if price1 > 0 and price2 > 0:
                        price_diff = abs(price1 - price2)
                        profit_pct = price_diff / min(price1, price2)
                        
                        if profit_pct > threshold:
                            # 确定买卖方向
                            if price1 < price2:
                                buy_market = market1
                                sell_market = market2
                                buy_price = price1
                                sell_price = price2
                            else:
                                buy_market = market2
                                sell_market = market1
                                buy_price = price2
                                sell_price = price1
                            
                            opportunities.append({
                                "symbol": symbol,
                                "buy_market": buy_market,
                                "sell_market": sell_market,
                                "buy_price": buy_price,
                                "sell_price": sell_price,
                                "price_difference": price_diff,
                                "profit_percentage": profit_pct,
                                "timestamp": datetime.now()
                            })
            
            return opportunities
            
        except Exception as e:
            logger.error(f"套利机会分析失败: {e}")
            return []
    
    def _adjust_symbol_for_market(self, symbol: str, market: str) -> str:
        """根据市场调整标的符号"""
        # 简化的符号调整逻辑
        if market == MarketType.CN.value:
            if not (symbol.endswith('.SS') or symbol.endswith('.SZ')):
                # 假设是上交所
                return f"{symbol}.SS"
        elif market == MarketType.HK.value:
            if not symbol.endswith('.HK'):
                return f"{symbol}.HK"
        
        return symbol