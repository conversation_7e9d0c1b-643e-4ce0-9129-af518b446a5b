"""
宏观经济策略API接口

扩展策略框架支持经济数据输入，实现经济指标与策略参数关联API，
添加经济周期识别和分析接口，实现经济事件驱动策略配置API
"""

from fastapi import APIRouter, Depends, HTTPException, Request, Query
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timedelta
import json
import logging
import sys
import os
from pathlib import Path

from app.models.economic_strategy import (
    EconomicStrategyConfig, EconomicStrategyCreateRequest, EconomicStrategyUpdateRequest,
    EconomicStrategyResponse, EconomicStrategyListResponse, EconomicIndicatorRequest,
    EconomicCycleAnalysisResponse, EconomicEventRequest, EconomicCorrelationRequest,
    EconomicCorrelationResponse, EconomicTrendAnalysisResponse
)
from app.services.async_wrapper import AsyncWrapper

router = APIRouter()
async_wrapper = AsyncWrapper()
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def get_economic_strategy_manager(request: Request):
    """获取宏观经济策略管理器依赖"""
    return EconomicStrategyManager()

class EconomicStrategyManager:
    """宏观经济策略管理器"""
    
    def __init__(self):
        self.economic_strategies_cache = {}
        self.economic_data_manager = None
        self._discover_economic_strategies()
        self._initialize_economic_data_manager()
    
    def _initialize_economic_data_manager(self):
        """初始化经济数据管理器"""
        try:
            from src.market.data.adapters.fred_adapter import FREDAdapter
            self.economic_data_manager = FREDAdapter()
            logger.info("经济数据管理器初始化成功")
        except Exception as e:
            logger.error(f"经济数据管理器初始化失败: {e}")
            self.economic_data_manager = None
    
    def _discover_economic_strategies(self):
        """发现可用的宏观经济策略"""
        try:
            # 导入经济策略模块
            from src.market.strategies.examples import (
                economic_cycle_strategy,
                inflation_hedge_strategy,
                asset_allocation_strategy
            )
            
            # 注册经济策略
            self.economic_strategies_cache = {
                'economic_cycle_strategy': {
                    'class': getattr(economic_cycle_strategy, 'EconomicCycleStrategy', None),
                    'module': 'economic_cycle_strategy',
                    'category': 'economic_cycle',
                    'description': '基于经济周期阶段调整投资策略',
                    'economic_indicators': [
                        'GDPC1', 'UNRATE', 'CPIAUCSL', 'FEDFUNDS', 'DGS10', 'DGS2',
                        'HOUST', 'INDPRO', 'NAPMPI', 'SP500', 'VIXCLS', 'UMCSENT'
                    ]
                },
                'inflation_hedge_strategy': {
                    'class': getattr(inflation_hedge_strategy, 'InflationHedgeStrategy', None),
                    'module': 'inflation_hedge_strategy',
                    'category': 'inflation_hedge',
                    'description': '基于通胀预期的对冲策略',
                    'economic_indicators': [
                        'CPIAUCSL', 'CPILFESL', 'PCEPI', 'T5YIE', 'T10YIE', 'DFEDTARU'
                    ]
                },
                'asset_allocation_strategy': {
                    'class': getattr(asset_allocation_strategy, 'AssetAllocationStrategy', None),
                    'module': 'asset_allocation_strategy',
                    'category': 'asset_allocation',
                    'description': '基于宏观经济环境的资产配置策略',
                    'economic_indicators': [
                        'GDPC1', 'UNRATE', 'CPIAUCSL', 'FEDFUNDS', 'DGS10', 'DEXUSEU', 'DEXCHUS'
                    ]
                }
            }
            
            # 过滤掉无效的策略
            self.economic_strategies_cache = {
                k: v for k, v in self.economic_strategies_cache.items() 
                if v['class'] is not None
            }
            
            logger.info(f"发现 {len(self.economic_strategies_cache)} 个宏观经济策略")
            
        except Exception as e:
            logger.error(f"宏观经济策略发现失败: {e}")
            # 使用模拟数据作为后备
            self.economic_strategies_cache = {
                'economic_cycle_strategy': {
                    'class': None,
                    'module': 'economic_cycle_strategy',
                    'category': 'economic_cycle',
                    'description': '基于经济周期阶段调整投资策略',
                    'economic_indicators': ['GDPC1', 'UNRATE', 'CPIAUCSL', 'FEDFUNDS']
                }
            }
    
    def get_economic_strategy_list(self, category: Optional[str] = None):
        """获取宏观经济策略列表"""
        strategies = []
        
        for strategy_id, strategy_info in self.economic_strategies_cache.items():
            if category and strategy_info['category'] != category:
                continue
                
            # 获取策略参数定义
            parameters = self._get_economic_strategy_parameters(strategy_id)
            
            strategy_data = {
                "id": strategy_id,
                "name": self._format_strategy_name(strategy_id),
                "description": strategy_info['description'],
                "category": strategy_info['category'],
                "parameters": parameters,
                "economic_indicators": strategy_info['economic_indicators'],
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "is_active": True
            }
            strategies.append(strategy_data)
        
        return strategies
    
    def get_economic_strategy_by_id(self, strategy_id: str):
        """根据ID获取宏观经济策略"""
        if strategy_id not in self.economic_strategies_cache:
            return None
            
        strategy_info = self.economic_strategies_cache[strategy_id]
        parameters = self._get_economic_strategy_parameters(strategy_id)
        
        return {
            "id": strategy_id,
            "name": self._format_strategy_name(strategy_id),
            "description": strategy_info['description'],
            "category": strategy_info['category'],
            "parameters": parameters,
            "economic_indicators": strategy_info['economic_indicators'],
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "is_active": True
        }
    
    def _get_economic_strategy_parameters(self, strategy_id: str) -> Dict[str, Any]:
        """获取宏观经济策略参数定义"""
        try:
            strategy_info = self.economic_strategies_cache.get(strategy_id)
            if not strategy_info or not strategy_info['class']:
                return self._get_default_economic_parameters(strategy_id)
            
            # 创建策略实例获取参数定义
            strategy_class = strategy_info['class']
            temp_strategy = strategy_class()
            param_definitions = temp_strategy.get_parameter_definitions()
            
            parameters = {}
            for name, param_def in param_definitions.items():
                parameters[name] = {
                    "type": param_def.param_type.value if hasattr(param_def.param_type, 'value') else str(param_def.param_type),
                    "default": param_def.default_value,
                    "required": param_def.required,
                    "description": param_def.description,
                    "min_value": getattr(param_def, 'min_value', None),
                    "max_value": getattr(param_def, 'max_value', None),
                    "choices": getattr(param_def, 'choices', None)
                }
            
            return parameters
            
        except Exception as e:
            logger.error(f"获取宏观经济策略参数失败 {strategy_id}: {e}")
            return self._get_default_economic_parameters(strategy_id)
    
    def _get_default_economic_parameters(self, strategy_id: str) -> Dict[str, Any]:
        """获取默认宏观经济策略参数"""
        if strategy_id == 'economic_cycle_strategy':
            return {
                "correlation_threshold": {"type": "float", "default": 0.7, "min": 0.0, "max": 1.0, "description": "相关性阈值"},
                "trend_lookback_days": {"type": "int", "default": 90, "min": 30, "max": 365, "description": "趋势分析回看天数"},
                "buy_threshold": {"type": "float", "default": 0.3, "min": -1.0, "max": 1.0, "description": "买入信号阈值"},
                "sell_threshold": {"type": "float", "default": -0.3, "min": -1.0, "max": 1.0, "description": "卖出信号阈值"},
                "min_cycle_confidence": {"type": "float", "default": 0.6, "min": 0.0, "max": 1.0, "description": "最小周期识别置信度"},
                "base_position_size": {"type": "int", "default": 100, "min": 1, "description": "基础仓位大小"}
            }
        elif strategy_id == 'inflation_hedge_strategy':
            return {
                "inflation_threshold": {"type": "float", "default": 3.0, "min": 0.0, "max": 10.0, "description": "通胀阈值(%)"},
                "hedge_ratio": {"type": "float", "default": 0.5, "min": 0.0, "max": 1.0, "description": "对冲比例"},
                "rebalance_frequency": {"type": "int", "default": 30, "min": 1, "max": 365, "description": "再平衡频率(天)"}
            }
        return {}
    
    def _format_strategy_name(self, strategy_id: str) -> str:
        """格式化宏观经济策略名称"""
        name_map = {
            'economic_cycle_strategy': '经济周期策略',
            'inflation_hedge_strategy': '通胀对冲策略',
            'asset_allocation_strategy': '宏观资产配置策略'
        }
        return name_map.get(strategy_id, strategy_id.replace('_', ' ').title())
    
    def get_economic_indicators_list(self):
        """获取支持的经济指标列表"""
        indicators = {
            # GDP和增长指标
            'GDPC1': {'name': '实际GDP', 'category': 'growth', 'unit': 'Billions of Chained 2012 Dollars', 'frequency': 'Quarterly'},
            'GDPPOT': {'name': '潜在GDP', 'category': 'growth', 'unit': 'Billions of Chained 2012 Dollars', 'frequency': 'Quarterly'},
            
            # 就业指标
            'UNRATE': {'name': '失业率', 'category': 'employment', 'unit': 'Percent', 'frequency': 'Monthly'},
            'CIVPART': {'name': '劳动参与率', 'category': 'employment', 'unit': 'Percent', 'frequency': 'Monthly'},
            'PAYEMS': {'name': '非农就业人数', 'category': 'employment', 'unit': 'Thousands of Persons', 'frequency': 'Monthly'},
            
            # 通胀指标
            'CPIAUCSL': {'name': 'CPI消费者价格指数', 'category': 'inflation', 'unit': 'Index 1982-1984=100', 'frequency': 'Monthly'},
            'CPILFESL': {'name': '核心CPI', 'category': 'inflation', 'unit': 'Index 1982-1984=100', 'frequency': 'Monthly'},
            'PCEPI': {'name': 'PCE价格指数', 'category': 'inflation', 'unit': 'Index 2012=100', 'frequency': 'Monthly'},
            
            # 利率和货币政策
            'FEDFUNDS': {'name': '联邦基金利率', 'category': 'monetary', 'unit': 'Percent', 'frequency': 'Monthly'},
            'DGS10': {'name': '10年期国债收益率', 'category': 'monetary', 'unit': 'Percent', 'frequency': 'Daily'},
            'DGS2': {'name': '2年期国债收益率', 'category': 'monetary', 'unit': 'Percent', 'frequency': 'Daily'},
            
            # 先行指标
            'HOUST': {'name': '新屋开工', 'category': 'leading', 'unit': 'Thousands of Units', 'frequency': 'Monthly'},
            'INDPRO': {'name': '工业生产指数', 'category': 'leading', 'unit': 'Index 2017=100', 'frequency': 'Monthly'},
            'NAPMPI': {'name': 'ISM制造业PMI', 'category': 'leading', 'unit': 'Index', 'frequency': 'Monthly'},
            
            # 金融市场指标
            'SP500': {'name': '标普500指数', 'category': 'financial', 'unit': 'Index', 'frequency': 'Daily'},
            'VIXCLS': {'name': 'VIX恐慌指数', 'category': 'financial', 'unit': 'Index', 'frequency': 'Daily'},
            
            # 消费者信心
            'UMCSENT': {'name': '密歇根消费者信心指数', 'category': 'sentiment', 'unit': 'Index 1966:Q1=100', 'frequency': 'Monthly'}
        }
        
        return indicators
    
    def analyze_economic_cycle(self, lookback_days: int = 365):
        """分析当前经济周期阶段"""
        try:
            if not self.economic_data_manager:
                raise ValueError("经济数据管理器未初始化")
            
            # 创建经济上下文
            from src.market.strategies.economic_base import EconomicContext
            economic_context = EconomicContext(economic_data_manager=self.economic_data_manager)
            
            # 获取经济周期阶段
            cycle_phase = economic_context.get_economic_cycle_phase()
            
            # 获取关键经济指标数据
            key_indicators = ['GDPC1', 'UNRATE', 'CPIAUCSL', 'FEDFUNDS', 'DGS10', 'DGS2']
            economic_data = economic_context.get_multiple_economic_data(key_indicators, lookback_days)
            
            # 分析趋势
            trends = {}
            for indicator in key_indicators:
                if indicator in economic_data and not economic_data[indicator].empty:
                    trends[indicator] = economic_context.get_economic_trend(indicator, 90)
            
            # 计算相关性
            correlations = {}
            if 'DGS10' in economic_data and 'DGS2' in economic_data:
                correlations['yield_curve_spread'] = self._calculate_yield_curve_spread(
                    economic_data['DGS10'], economic_data['DGS2']
                )
            
            return {
                'cycle_phase': cycle_phase,
                'analysis_date': datetime.now().isoformat(),
                'lookback_days': lookback_days,
                'trends': trends,
                'correlations': correlations,
                'indicators_analyzed': len(economic_data),
                'confidence': self._calculate_analysis_confidence(trends, correlations)
            }
            
        except Exception as e:
            logger.error(f"经济周期分析失败: {e}")
            return {
                'cycle_phase': 'UNKNOWN',
                'analysis_date': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def _calculate_yield_curve_spread(self, long_term_data, short_term_data):
        """计算收益率曲线利差"""
        try:
            if long_term_data.empty or short_term_data.empty:
                return None
            
            # 获取最新数据
            latest_10y = long_term_data['value'].iloc[-1] if not long_term_data.empty else None
            latest_2y = short_term_data['value'].iloc[-1] if not short_term_data.empty else None
            
            if latest_10y is not None and latest_2y is not None:
                spread = latest_10y - latest_2y
                return {
                    'spread': spread,
                    'shape': 'INVERTED' if spread < 0 else 'NORMAL' if spread > 1 else 'FLAT'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"收益率曲线利差计算失败: {e}")
            return None
    
    def _calculate_analysis_confidence(self, trends, correlations):
        """计算分析置信度"""
        try:
            total_indicators = len(trends)
            if total_indicators == 0:
                return 0.0
            
            # 统计有效趋势
            valid_trends = sum(1 for trend in trends.values() if trend != 'UNKNOWN')
            trend_confidence = valid_trends / total_indicators
            
            # 相关性数据可用性
            correlation_confidence = 1.0 if correlations else 0.5
            
            # 综合置信度
            overall_confidence = (trend_confidence * 0.7 + correlation_confidence * 0.3)
            
            return min(1.0, overall_confidence)
            
        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            return 0.0
    
    def analyze_economic_correlation(self, indicators: List[str], lookback_days: int = 365):
        """分析经济指标相关性"""
        try:
            if not self.economic_data_manager:
                raise ValueError("经济数据管理器未初始化")
            
            from src.market.strategies.economic_base import EconomicContext
            economic_context = EconomicContext(economic_data_manager=self.economic_data_manager)
            
            # 获取经济数据
            economic_data = economic_context.get_multiple_economic_data(indicators, lookback_days)
            
            # 计算相关性矩阵
            correlation_matrix = {}
            for i, indicator1 in enumerate(indicators):
                correlation_matrix[indicator1] = {}
                for j, indicator2 in enumerate(indicators):
                    if i != j:
                        correlation = economic_context.get_economic_indicator_correlation(
                            indicator1, indicator2, lookback_days
                        )
                        correlation_matrix[indicator1][indicator2] = correlation
                    else:
                        correlation_matrix[indicator1][indicator2] = 1.0
            
            return {
                'correlation_matrix': correlation_matrix,
                'indicators': indicators,
                'lookback_days': lookback_days,
                'analysis_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"经济指标相关性分析失败: {e}")
            return {
                'error': str(e),
                'indicators': indicators,
                'analysis_date': datetime.now().isoformat()
            }
    
    def analyze_economic_trend(self, indicator: str, lookback_days: int = 90):
        """分析单个经济指标趋势"""
        try:
            if not self.economic_data_manager:
                raise ValueError("经济数据管理器未初始化")
            
            from src.market.strategies.economic_base import EconomicContext
            economic_context = EconomicContext(economic_data_manager=self.economic_data_manager)
            
            # 获取经济数据
            data = economic_context.get_economic_data(indicator, lookback_days)
            
            if data.empty:
                return {
                    'indicator': indicator,
                    'trend': 'UNKNOWN',
                    'error': '无可用数据'
                }
            
            # 分析趋势
            trend = economic_context.get_economic_trend(indicator, lookback_days)
            
            # 计算统计信息
            values = data['value'].dropna()
            stats = {
                'current_value': values.iloc[-1] if not values.empty else None,
                'mean': values.mean(),
                'std': values.std(),
                'min': values.min(),
                'max': values.max(),
                'data_points': len(values)
            }
            
            return {
                'indicator': indicator,
                'trend': trend,
                'lookback_days': lookback_days,
                'statistics': stats,
                'analysis_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"经济指标趋势分析失败 {indicator}: {e}")
            return {
                'indicator': indicator,
                'trend': 'UNKNOWN',
                'error': str(e),
                'analysis_date': datetime.now().isoformat()
            }
    
    def get_economic_events_calendar(self, start_date: datetime, end_date: datetime):
        """获取经济事件日历（模拟实现）"""
        try:
            # 这里应该连接到真实的经济事件数据源
            # 目前返回模拟数据
            events = [
                {
                    'date': '2024-01-15',
                    'event': 'CPI数据发布',
                    'importance': 'HIGH',
                    'expected_impact': 'MARKET_MOVING',
                    'indicators_affected': ['CPIAUCSL', 'FEDFUNDS']
                },
                {
                    'date': '2024-01-20',
                    'event': 'GDP数据发布',
                    'importance': 'HIGH',
                    'expected_impact': 'MARKET_MOVING',
                    'indicators_affected': ['GDPC1']
                },
                {
                    'date': '2024-01-25',
                    'event': '就业数据发布',
                    'importance': 'MEDIUM',
                    'expected_impact': 'MODERATE',
                    'indicators_affected': ['UNRATE', 'PAYEMS']
                }
            ]
            
            return {
                'events': events,
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"获取经济事件日历失败: {e}")
            return {
                'events': [],
                'error': str(e)
            }

@router.get("/", response_model=EconomicStrategyListResponse, summary="获取宏观经济策略列表")
async def get_economic_strategies(
    category: Optional[str] = Query(None, description="策略分类筛选"),
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(100, description="限制数量"),
    manager = Depends(get_economic_strategy_manager)
):
    """获取宏观经济策略列表"""
    try:
        strategies = await async_wrapper.run_in_executor(
            manager.get_economic_strategy_list,
            category
        )
        
        # 分页处理
        total = len(strategies)
        strategies = strategies[skip:skip + limit]
        
        return EconomicStrategyListResponse(
            strategies=strategies,
            total=total,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"获取宏观经济策略列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取宏观经济策略列表失败: {str(e)}")

@router.get("/{strategy_id}", response_model=EconomicStrategyResponse, summary="获取宏观经济策略详情")
async def get_economic_strategy(
    strategy_id: str,
    manager = Depends(get_economic_strategy_manager)
):
    """获取指定宏观经济策略的详细信息"""
    try:
        strategy = await async_wrapper.run_in_executor(
            manager.get_economic_strategy_by_id,
            strategy_id
        )
        
        if not strategy:
            raise HTTPException(status_code=404, detail=f"宏观经济策略 {strategy_id} 不存在")
        
        return EconomicStrategyResponse(**strategy)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取宏观经济策略详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取宏观经济策略详情失败: {str(e)}")

@router.get("/indicators/list", summary="获取支持的经济指标列表")
async def get_economic_indicators(
    category: Optional[str] = Query(None, description="指标分类筛选"),
    manager = Depends(get_economic_strategy_manager)
):
    """获取支持的经济指标列表"""
    try:
        indicators = await async_wrapper.run_in_executor(
            manager.get_economic_indicators_list
        )
        
        # 按分类筛选
        if category:
            indicators = {
                k: v for k, v in indicators.items() 
                if v.get('category') == category
            }
        
        return {"indicators": indicators}
        
    except Exception as e:
        logger.error(f"获取经济指标列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取经济指标列表失败: {str(e)}")

@router.post("/cycle/analyze", response_model=EconomicCycleAnalysisResponse, summary="分析经济周期")
async def analyze_economic_cycle(
    lookback_days: int = Query(365, description="分析回看天数"),
    manager = Depends(get_economic_strategy_manager)
):
    """分析当前经济周期阶段"""
    try:
        analysis = await async_wrapper.run_in_executor(
            manager.analyze_economic_cycle,
            lookback_days
        )
        
        return EconomicCycleAnalysisResponse(**analysis)
        
    except Exception as e:
        logger.error(f"经济周期分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"经济周期分析失败: {str(e)}")

@router.post("/correlation/analyze", response_model=EconomicCorrelationResponse, summary="分析经济指标相关性")
async def analyze_economic_correlation(
    request: EconomicCorrelationRequest,
    manager = Depends(get_economic_strategy_manager)
):
    """分析经济指标之间的相关性"""
    try:
        analysis = await async_wrapper.run_in_executor(
            manager.analyze_economic_correlation,
            request.indicators,
            request.lookback_days
        )
        
        return EconomicCorrelationResponse(**analysis)
        
    except Exception as e:
        logger.error(f"经济指标相关性分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"经济指标相关性分析失败: {str(e)}")

@router.post("/trend/analyze", response_model=EconomicTrendAnalysisResponse, summary="分析经济指标趋势")
async def analyze_economic_trend(
    indicator: str = Query(..., description="经济指标ID"),
    lookback_days: int = Query(90, description="分析回看天数"),
    manager = Depends(get_economic_strategy_manager)
):
    """分析单个经济指标的趋势"""
    try:
        analysis = await async_wrapper.run_in_executor(
            manager.analyze_economic_trend,
            indicator,
            lookback_days
        )
        
        return EconomicTrendAnalysisResponse(**analysis)
        
    except Exception as e:
        logger.error(f"经济指标趋势分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"经济指标趋势分析失败: {str(e)}")

@router.get("/events/calendar", summary="获取经济事件日历")
async def get_economic_events_calendar(
    start_date: datetime = Query(..., description="开始日期"),
    end_date: datetime = Query(..., description="结束日期"),
    importance: Optional[str] = Query(None, description="事件重要性筛选"),
    manager = Depends(get_economic_strategy_manager)
):
    """获取经济事件日历"""
    try:
        events = await async_wrapper.run_in_executor(
            manager.get_economic_events_calendar,
            start_date,
            end_date
        )
        
        # 按重要性筛选
        if importance and 'events' in events:
            events['events'] = [
                event for event in events['events'] 
                if event.get('importance') == importance
            ]
        
        return events
        
    except Exception as e:
        logger.error(f"获取经济事件日历失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取经济事件日历失败: {str(e)}")

@router.post("/", response_model=EconomicStrategyResponse, summary="创建宏观经济策略")
async def create_economic_strategy(strategy: EconomicStrategyCreateRequest):
    """创建新的宏观经济策略"""
    try:
        # 这里需要实现创建宏观经济策略的逻辑
        new_strategy = {
            "id": strategy.name.lower().replace(" ", "_"),
            "name": strategy.name,
            "description": strategy.description,
            "category": strategy.category,
            "parameters": strategy.parameters,
            "economic_indicators": strategy.economic_indicators,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "is_active": True
        }
        
        return EconomicStrategyResponse(**new_strategy)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建宏观经济策略失败: {str(e)}")

@router.put("/{strategy_id}", response_model=EconomicStrategyResponse, summary="更新宏观经济策略")
async def update_economic_strategy(strategy_id: str, strategy: EconomicStrategyUpdateRequest):
    """更新指定宏观经济策略"""
    try:
        # 这里需要实现更新宏观经济策略的逻辑
        updated_strategy = {
            "id": strategy_id,
            "name": strategy.name,
            "description": strategy.description,
            "category": strategy.category,
            "parameters": strategy.parameters,
            "economic_indicators": strategy.economic_indicators,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "is_active": strategy.is_active
        }
        
        return EconomicStrategyResponse(**updated_strategy)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新宏观经济策略失败: {str(e)}")

@router.delete("/{strategy_id}", summary="删除宏观经济策略")
async def delete_economic_strategy(strategy_id: str):
    """删除指定宏观经济策略"""
    try:
        # 这里需要实现删除宏观经济策略的逻辑
        return {"message": f"宏观经济策略 {strategy_id} 已删除"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除宏观经济策略失败: {str(e)}")

@router.post("/{strategy_id}/validate", summary="验证宏观经济策略参数")
async def validate_economic_strategy_parameters(
    strategy_id: str, 
    parameters: Dict[str, Any],
    manager = Depends(get_economic_strategy_manager)
):
    """验证宏观经济策略参数的有效性"""
    try:
        # 获取策略信息
        strategy = await async_wrapper.run_in_executor(
            manager.get_economic_strategy_by_id,
            strategy_id
        )
        
        if not strategy:
            raise HTTPException(status_code=404, detail=f"宏观经济策略 {strategy_id} 不存在")
        
        # 验证参数
        validation_errors = []
        strategy_params = strategy.get('parameters', {})
        
        for param_name, param_value in parameters.items():
            if param_name not in strategy_params:
                validation_errors.append(f"未知参数: {param_name}")
                continue
            
            param_def = strategy_params[param_name]
            
            # 类型验证
            expected_type = param_def.get('type')
            if expected_type == 'int' and not isinstance(param_value, int):
                validation_errors.append(f"参数 {param_name} 应为整数类型")
            elif expected_type == 'float' and not isinstance(param_value, (int, float)):
                validation_errors.append(f"参数 {param_name} 应为数值类型")
            elif expected_type == 'str' and not isinstance(param_value, str):
                validation_errors.append(f"参数 {param_name} 应为字符串类型")
            
            # 范围验证
            if 'min' in param_def and param_value < param_def['min']:
                validation_errors.append(f"参数 {param_name} 不能小于 {param_def['min']}")
            if 'max' in param_def and param_value > param_def['max']:
                validation_errors.append(f"参数 {param_name} 不能大于 {param_def['max']}")
            
            # 选择验证
            if 'choices' in param_def and param_value not in param_def['choices']:
                validation_errors.append(f"参数 {param_name} 必须是以下值之一: {param_def['choices']}")
        
        return {
            "is_valid": len(validation_errors) == 0,
            "errors": validation_errors,
            "warnings": []
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"宏观经济策略参数验证失败: {e}")
        raise HTTPException(status_code=500, detail=f"参数验证失败: {str(e)}")