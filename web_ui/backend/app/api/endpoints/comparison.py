"""
策略对比分析API接口

提供增强的策略对比功能，包括异步处理、缓存优化、进度跟踪等。
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Union, Any
from datetime import datetime
from enum import Enum
import json
import uuid
import asyncio
import hashlib
import numpy as np
from pathlib import Path

from src.analysis.comparison import 策略对比器, 策略数据, 对比方法, 排名方法
from src.market.data.cache import get_cache_manager
from app.services.async_wrapper import AsyncWrapper
from app.core.config import get_settings

router = APIRouter()
async_wrapper = AsyncWrapper()
cache_manager = get_cache_manager()
settings = get_settings()

# 对比任务状态
class ComparisonTaskStatus(str, Enum):
    """对比任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

# 对比方法枚举
class 对比方法枚举(str, Enum):
    """策略对比方法"""
    夏普比率 = "sharpe_ratio"
    索提诺比率 = "sortino_ratio"
    卡尔玛比率 = "calmar_ratio"
    总收益率 = "total_return"
    最大回撤 = "max_drawdown"
    胜率 = "win_rate"
    盈利因子 = "profit_factor"
    波动率 = "volatility"
    相关性 = "correlation"

# 排名方法枚举
class 排名方法枚举(str, Enum):
    """排名方法"""
    加权评分 = "weighted_score"
    风险调整收益 = "risk_adjusted_return"
    一致性 = "consistency"
    分散化收益 = "diversification_benefit"
    多目标优化 = "multi_objective"

# 请求模型
class 增强对比请求(BaseModel):
    """增强的策略对比请求"""
    策略列表: List[Dict] = Field(..., description="策略数据列表")
    对比方法列表: List[对比方法枚举] = Field(
        default=[对比方法枚举.夏普比率, 对比方法枚举.总收益率],
        description="对比方法列表"
    )
    排名方法: 排名方法枚举 = Field(
        default=排名方法枚举.加权评分,
        description="排名方法"
    )
    无风险利率: float = Field(default=0.02, description="无风险利率")
    使用缓存: bool = Field(default=True, description="是否使用缓存")
    异步处理: bool = Field(default=False, description="是否异步处理")
    包含相关性矩阵: bool = Field(default=True, description="是否包含相关性矩阵")
    包含风险收益分析: bool = Field(default=True, description="是否包含风险收益分析")
    自定义权重: Optional[Dict[str, float]] = Field(None, description="自定义指标权重")

class 对比任务请求(BaseModel):
    """对比任务请求"""
    任务名称: Optional[str] = Field(None, description="任务名称")
    对比请求: 增强对比请求 = Field(..., description="对比请求参数")

# 响应模型
class 对比排名(BaseModel):
    """对比排名结果"""
    排名: int
    策略ID: str
    策略名称: str
    评分: float
    指标: Dict[str, float]
    百分位排名: float

class 相关性矩阵(BaseModel):
    """相关性矩阵"""
    矩阵: Dict[str, Dict[str, float]]
    平均相关性: float
    最大相关性: float
    最小相关性: float

class 风险收益点(BaseModel):
    """风险收益点"""
    策略ID: str
    策略名称: str
    收益值: float
    风险值: float
    夏普比率: float
    有效边界距离: float

class 详细指标(BaseModel):
    """详细指标"""
    策略ID: str
    策略名称: str
    指标: Dict[str, float]
    百分位数: Dict[str, float]
    Z分数: Dict[str, float]

class 增强对比结果(BaseModel):
    """增强的对比结果"""
    任务ID: str
    排名: List[对比排名]
    相关性矩阵: Optional[相关性矩阵] = None
    风险收益分析: Optional[List[风险收益点]] = None
    详细指标: List[详细指标]
    汇总统计: Dict[str, Any]
    对比元数据: Dict[str, Any]
    缓存命中: bool = False

class ComparisonTask(BaseModel):
    """对比任务"""
    task_id: str
    task_name: Optional[str]
    status: ComparisonTaskStatus
    progress: float
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    result: Optional[EnhancedComparisonResult] = None

class ComparisonTaskResponse(BaseModel):
    """对比任务响应"""
    task_id: str
    status: ComparisonTaskStatus
    message: str

class ComparisonTaskListResponse(BaseModel):
    """对比任务列表响应"""
    tasks: List[ComparisonTask]
    total: int

# 全局任务存储（生产环境应使用Redis或数据库）
comparison_tasks: Dict[str, ComparisonTask] = {}

def 生成缓存键(请求: 增强对比请求) -> str:
    """生成缓存键"""
    # 使用新的缓存管理器生成更智能的缓存键
    策略名称列表 = [策略.get("名称", f"策略_{i}") for i, 策略 in enumerate(请求.策略列表)]
    
    return cache_manager.generate_comparison_cache_key(
        strategy_names=策略名称列表,
        comparison_methods=[方法.value for 方法 in 请求.对比方法列表],
        ranking_method=请求.排名方法.value,
        risk_free_rate=请求.无风险利率,
        custom_weights=请求.自定义权重
    )

async def 执行对比(
    任务ID: str,
    请求: 增强对比请求
) -> 增强对比结果:
    """执行策略对比分析"""
    
    # 更新任务状态
    if 任务ID in comparison_tasks:
        comparison_tasks[任务ID].status = ComparisonTaskStatus.RUNNING
        comparison_tasks[任务ID].started_at = datetime.now()
        comparison_tasks[任务ID].progress = 0.1
    
    try:
        # 检查是否应该使用增量缓存
        策略数量 = len(请求.策略列表)
        方法数量 = len(请求.对比方法列表)
        使用增量 = cache_manager.should_use_incremental_cache(策略数量, 方法数量)
        
        # 检查完整缓存
        缓存命中 = False
        if 请求.使用缓存:
            缓存键 = 生成缓存键(请求)
            缓存结果 = await cache_manager.get(缓存键)
            if 缓存结果:
                缓存命中 = True
                结果 = 增强对比结果(**缓存结果, 缓存命中=True)
                
                # 更新任务状态
                if 任务ID in comparison_tasks:
                    comparison_tasks[任务ID].status = ComparisonTaskStatus.COMPLETED
                    comparison_tasks[任务ID].completed_at = datetime.now()
                    comparison_tasks[任务ID].progress = 1.0
                    comparison_tasks[任务ID].result = 结果
                
                return 结果
        
        # 如果启用增量缓存，尝试获取部分结果
        partial_results = {}
        if use_incremental and request.use_cache:
            base_cache_key = generate_cache_key(request)
            
            # 尝试获取不同子集的缓存结果
            for i in range(2, len(request.strategies)):  # 至少2个策略的组合
                from itertools import combinations
                for strategy_combo in combinations(range(len(request.strategies)), i):
                    strategy_subset = [request.strategies[j].get("name", f"strategy_{j}") 
                                     for j in strategy_combo]
                    
                    subset_result = await cache_manager.get_incremental_cache(
                        base_cache_key, strategy_subset
                    )
                    if subset_result:
                        partial_results[tuple(sorted(strategy_subset))] = subset_result
        
        # 转换策略数据
        策略列表 = []
        for i, 策略字典 in enumerate(请求.策略列表):
            # 更新进度
            if 任务ID in comparison_tasks:
                comparison_tasks[任务ID].progress = 0.1 + (i / len(请求.策略列表)) * 0.3
            
            名称 = 策略字典.get('名称', f'策略_{i+1}')
            组合价值 = _转换为序列(策略字典['组合价值'])
            
            交易记录 = None
            if '交易记录' in 策略字典 and 策略字典['交易记录']:
                交易记录 = _转换交易数据(策略字典['交易记录'])
            
            基准价值 = None
            if '基准价值' in 策略字典 and 策略字典['基准价值']:
                基准价值 = _转换为序列(策略字典['基准价值'])
            
            元数据 = 策略字典.get('元数据', {})
            
            策略数据对象 = 策略数据(
                名称=名称,
                组合价值=组合价值,
                交易记录=交易记录,
                基准价值=基准价值,
                元数据=元数据
            )
            策略列表.append(策略数据对象)
        
        # 更新进度
        if task_id in comparison_tasks:
            comparison_tasks[task_id].progress = 0.4
        
        # 映射对比方法
        方法映射 = {
            对比方法枚举.夏普比率: 对比方法.夏普比率,
            对比方法枚举.索提诺比率: 对比方法.索提诺比率,
            对比方法枚举.卡尔玛比率: 对比方法.卡尔玛比率,
            对比方法枚举.总收益率: 对比方法.总收益率,
            对比方法枚举.最大回撤: 对比方法.最大回撤,
            对比方法枚举.胜率: 对比方法.胜率,
            对比方法枚举.盈利因子: 对比方法.盈利因子
        }
        对比方法列表 = [方法映射[方法] for 方法 in 请求.对比方法列表]
        
        # 映射排名方法
        排名映射 = {
            排名方法枚举.加权评分: 排名方法.加权评分,
            排名方法枚举.风险调整收益: 排名方法.风险调整收益,
            排名方法枚举.一致性: 排名方法.一致性,
            排名方法枚举.分散化收益: 排名方法.分散化收益,
            排名方法枚举.多目标优化: 排名方法.多目标优化
        }
        排名方法类型 = 排名映射[请求.排名方法]
        
        # 创建对比器
        对比器 = 策略对比器(无风险利率=请求.无风险利率)
        
        # 更新进度
        if task_id in comparison_tasks:
            comparison_tasks[task_id].progress = 0.5
        
        # 执行对比分析（可能利用部分结果进行增量计算）
        对比结果 = await async_wrapper.run_in_executor(
            _执行增量对比,
            对比器,
            策略列表,
            对比方法列表,
            排名方法类型,
            partial_results
        )
        
        # 更新进度
        if task_id in comparison_tasks:
            comparison_tasks[task_id].progress = 0.7
        
        # 构建增强的结果
        ranking = []
        for i, (strategy_name, score) in enumerate(comparison_result.ranking):
            strategy_id = next(
                (s.metadata.get('id', f'strategy_{j}') for j, s in enumerate(strategies) if s.name == strategy_name),
                f'strategy_{i}'
            )
            
            metrics = comparison_result.detailed_metrics.get(strategy_name, {})
            percentile_rank = (len(comparison_result.ranking) - i) / len(comparison_result.ranking) * 100
            
            ranking.append(ComparisonRanking(
                rank=i + 1,
                strategy_id=strategy_id,
                strategy_name=strategy_name,
                score=score,
                metrics=metrics,
                percentile_rank=percentile_rank
            ))
        
        # 构建相关性矩阵
        correlation_matrix = None
        if request.include_correlation_matrix and hasattr(comparison_result, 'correlation_matrix'):
            matrix = comparison_result.correlation_matrix
            correlation_matrix = CorrelationMatrix(
                matrix=matrix,
                average_correlation=np.mean([v for row in matrix.values() for k, v in row.items() if k != row]),
                max_correlation=max([v for row in matrix.values() for k, v in row.items() if k != row]),
                min_correlation=min([v for row in matrix.values() for k, v in row.items() if k != row])
            )
        
        # 构建风险收益分析
        risk_return_analysis = None
        if request.include_risk_return_analysis:
            risk_return_analysis = []
            for strategy in strategies:
                returns = strategy.portfolio_values.pct_change().dropna()
                annual_return = returns.mean() * 252
                annual_volatility = returns.std() * np.sqrt(252)
                sharpe_ratio = (annual_return - request.risk_free_rate) / annual_volatility if annual_volatility > 0 else 0
                
                risk_return_analysis.append(RiskReturnPoint(
                    strategy_id=strategy.metadata.get('id', strategy.name),
                    strategy_name=strategy.name,
                    return_value=annual_return,
                    risk_value=annual_volatility,
                    sharpe_ratio=sharpe_ratio,
                    efficient_frontier_distance=0.0  # 可以后续计算
                ))
        
        # 构建详细指标
        detailed_metrics = []
        for strategy_name, metrics in comparison_result.detailed_metrics.items():
            strategy_id = next(
                (s.metadata.get('id', f'strategy_{j}') for j, s in enumerate(strategies) if s.name == strategy_name),
                strategy_name
            )
            
            # 计算百分位数和Z分数
            all_values = {metric: [m.get(metric, 0) for m in comparison_result.detailed_metrics.values()] 
                         for metric in metrics.keys()}
            
            percentiles = {}
            z_scores = {}
            for metric, value in metrics.items():
                values_list = all_values[metric]
                if len(values_list) > 1:
                    percentiles[metric] = (sorted(values_list).index(value) + 1) / len(values_list) * 100
                    mean_val = np.mean(values_list)
                    std_val = np.std(values_list)
                    z_scores[metric] = (value - mean_val) / std_val if std_val > 0 else 0
                else:
                    percentiles[metric] = 50.0
                    z_scores[metric] = 0.0
            
            detailed_metrics.append(DetailedMetrics(
                strategy_id=strategy_id,
                strategy_name=strategy_name,
                metrics=metrics,
                percentiles=percentiles,
                z_scores=z_scores
            ))
        
        # 构建汇总统计
        summary_statistics = {
            "total_strategies": len(strategies),
            "comparison_methods_used": len(request.comparison_methods),
            "ranking_method": request.ranking_method,
            "average_score": np.mean([r.score for r in ranking]),
            "score_std": np.std([r.score for r in ranking]),
            "best_strategy": ranking[0].strategy_name if ranking else None,
            "worst_strategy": ranking[-1].strategy_name if ranking else None
        }
        
        # 构建元数据
        comparison_metadata = {
            "request_id": task_id,
            "processing_time": datetime.now().isoformat(),
            "cache_used": cache_hit,
            "risk_free_rate": request.risk_free_rate,
            "custom_weights": request.custom_weights
        }
        
        # 更新进度
        if task_id in comparison_tasks:
            comparison_tasks[task_id].progress = 0.9
        
        # 构建最终结果
        result = EnhancedComparisonResult(
            task_id=task_id,
            ranking=ranking,
            correlation_matrix=correlation_matrix,
            risk_return_analysis=risk_return_analysis,
            detailed_metrics=detailed_metrics,
            summary_statistics=summary_statistics,
            comparison_metadata=comparison_metadata,
            cache_hit=cache_hit
        )
        
        # 缓存结果（包括增量缓存）
        if request.use_cache and not cache_hit:
            cache_key = generate_cache_key(request)
            await cache_manager.set(
                cache_key, 
                result.dict(exclude={'cache_hit'}),
                expire_seconds=3600  # 1小时缓存
            )
            
            # 如果使用增量缓存，也缓存部分结果
            if use_incremental:
                from itertools import combinations
                strategy_names = [s.name for s in strategies]
                
                # 缓存不同大小的子集
                for subset_size in range(2, len(strategies)):
                    for strategy_combo in combinations(range(len(strategies)), subset_size):
                        subset_names = [strategy_names[i] for i in strategy_combo]
                        
                        # 构建子集结果（这里简化处理，实际应该重新计算）
                        subset_ranking = [(name, score) for name, score in comparison_result.ranking 
                                        if name in subset_names]
                        subset_detailed = {name: metrics for name, metrics in comparison_result.detailed_metrics.items()
                                         if name in subset_names}
                        
                        subset_result = {
                            'ranking': subset_ranking,
                            'detailed_metrics': subset_detailed
                        }
                        
                        await cache_manager.set_incremental_cache(
                            cache_key, subset_names, subset_result, expire_seconds=1800
                        )
        
        # 更新任务状态
        if task_id in comparison_tasks:
            comparison_tasks[task_id].status = ComparisonTaskStatus.COMPLETED
            comparison_tasks[task_id].completed_at = datetime.now()
            comparison_tasks[task_id].progress = 1.0
            comparison_tasks[task_id].result = result
        
        return result
        
    except Exception as e:
        # 更新任务状态为失败
        if task_id in comparison_tasks:
            comparison_tasks[task_id].status = ComparisonTaskStatus.FAILED
            comparison_tasks[task_id].error_message = str(e)
            comparison_tasks[task_id].completed_at = datetime.now()
        
        raise HTTPException(status_code=500, detail=f"策略对比分析失败: {str(e)}")

def _转换为序列(数据):
    """转换数据为pandas Series"""
    import pandas as pd
    if isinstance(数据, dict):
        return pd.Series(数据)
    elif isinstance(数据, list):
        return pd.Series(数据)
    else:
        return pd.Series(数据)

def _转换交易数据(交易数据):
    """转换交易数据"""
    from src.models.trading import Trade
    交易列表 = []
    for 交易字典 in 交易数据:
        交易 = Trade(**交易字典)
        交易列表.append(交易)
    return 交易列表

# API端点

@router.post("/compare-strategies", response_model=增强对比结果)
async def 同步对比策略(请求: 增强对比请求):
    """
    同步策略对比分析
    
    立即执行策略对比并返回结果，适用于少量策略的快速对比。
    """
    任务ID = str(uuid.uuid4())
    
    # 创建临时任务记录
    comparison_tasks[任务ID] = ComparisonTask(
        task_id=任务ID,
        task_name=None,
        status=ComparisonTaskStatus.PENDING,
        progress=0.0,
        created_at=datetime.now()
    )
    
    try:
        结果 = await 执行对比(任务ID, 请求)
        return 结果
    finally:
        # 清理临时任务记录
        if 任务ID in comparison_tasks:
            del comparison_tasks[任务ID]

@router.post("/compare-strategies-async", response_model=ComparisonTaskResponse)
async def 异步对比策略(
    请求: 对比任务请求,
    background_tasks: BackgroundTasks
):
    """
    异步策略对比分析
    
    创建后台任务执行策略对比，适用于大量策略或复杂分析。
    """
    任务ID = str(uuid.uuid4())
    任务名称 = 请求.任务名称 or f"策略对比_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # 创建任务记录
    comparison_tasks[任务ID] = ComparisonTask(
        task_id=任务ID,
        task_name=任务名称,
        status=ComparisonTaskStatus.PENDING,
        progress=0.0,
        created_at=datetime.now()
    )
    
    # 添加后台任务
    background_tasks.add_task(执行对比, 任务ID, 请求.对比请求)
    
    return ComparisonTaskResponse(
        task_id=任务ID,
        status=ComparisonTaskStatus.PENDING,
        message=f"对比任务已创建: {任务名称}"
    )

@router.get("/comparison-tasks/{task_id}", response_model=ComparisonTask)
async def get_comparison_task(task_id: str):
    """获取对比任务状态和结果"""
    if task_id not in comparison_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return comparison_tasks[task_id]

@router.get("/comparison-tasks", response_model=ComparisonTaskListResponse)
async def list_comparison_tasks(
    status: Optional[ComparisonTaskStatus] = None,
    limit: int = 50,
    offset: int = 0
):
    """获取对比任务列表"""
    tasks = list(comparison_tasks.values())
    
    # 按状态过滤
    if status:
        tasks = [task for task in tasks if task.status == status]
    
    # 按创建时间排序
    tasks.sort(key=lambda x: x.created_at, reverse=True)
    
    # 分页
    total = len(tasks)
    tasks = tasks[offset:offset + limit]
    
    return ComparisonTaskListResponse(tasks=tasks, total=total)

@router.delete("/comparison-tasks/{task_id}")
async def cancel_comparison_task(task_id: str):
    """取消对比任务"""
    if task_id not in comparison_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = comparison_tasks[task_id]
    if task.status in [ComparisonTaskStatus.COMPLETED, ComparisonTaskStatus.FAILED]:
        raise HTTPException(status_code=400, detail="任务已完成，无法取消")
    
    # 标记为取消状态
    task.status = ComparisonTaskStatus.CANCELLED
    task.completed_at = datetime.now()
    
    return {"message": "任务已取消"}

@router.delete("/comparison-tasks")
async def cleanup_comparison_tasks():
    """清理已完成的对比任务"""
    completed_tasks = [
        task_id for task_id, task in comparison_tasks.items()
        if task.status in [ComparisonTaskStatus.COMPLETED, ComparisonTaskStatus.FAILED, ComparisonTaskStatus.CANCELLED]
    ]
    
    for task_id in completed_tasks:
        del comparison_tasks[task_id]
    
    return {"message": f"已清理 {len(completed_tasks)} 个任务"}


def _执行增量对比(
    对比器,
    策略列表,
    对比方法列表,
    排名方法,
    部分结果=None
):
    """执行增量对比分析"""
    # 如果有部分结果可以复用，进行优化计算
    if 部分结果:
        # 这里可以实现更复杂的增量计算逻辑
        # 暂时使用标准对比方法
        pass
    
    # 执行标准对比
    return 对比器.对比策略(策略列表, 对比方法列表, 排名方法)


# 添加缓存管理API端点

@router.get("/cache/stats")
async def 获取缓存统计():
    """获取缓存统计信息"""
    return cache_manager.get_cache_stats()


@router.delete("/cache/clear")
async def 清空缓存():
    """清空所有缓存"""
    await cache_manager.clear()
    return {"message": "缓存已清空"}


@router.get("/cache/incremental-info/{task_id}")
async def 获取增量缓存信息(任务ID: str):
    """获取增量缓存信息"""
    if 任务ID not in comparison_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    任务 = comparison_tasks[任务ID]
    if not 任务.result:
        raise HTTPException(status_code=400, detail="任务尚未完成")
    
    策略数量 = len(任务.result.详细指标) if 任务.result.详细指标 else 0
    使用增量 = cache_manager.should_use_incremental_cache(策略数量, 4)
    
    return {
        "task_id": 任务ID,
        "strategy_count": 策略数量,
        "uses_incremental_cache": 使用增量,
        "cache_priority": cache_manager.calculate_cache_priority({
            "strategies": list(任务.result.详细指标.keys()) if 任务.result.详细指标 else [],
            "comparison_methods": ["sharpe_ratio", "total_return", "max_drawdown", "win_rate"],
            "custom_weights": None
        })
    }