import logging
logger = logging.getLogger(__name__)
"""
策略对比配置管理API接口

提供对比配置的保存、加载、管理功能。
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from datetime import datetime
import json
import uuid
from pathlib import Path

from app.core.config import get_settings

router = APIRouter()
settings = get_settings()

# 配置存储路径
CONFIG_DIR = Path(settings.DATA_DIR) / "comparison_configs"
CONFIG_DIR.mkdir(parents=True, exist_ok=True)

# 数据模型
class ComparisonConfig(BaseModel):
    """对比配置模型"""
    id: Optional[str] = None
    name: str = Field(..., description="配置名称")
    description: Optional[str] = Field(None, description="配置描述")
    selected_strategies: List[str] = Field(..., description="选中的策略ID列表")
    comparison_methods: List[str] = Field(..., description="对比方法列表")
    ranking_method: str = Field(..., description="排名方法")
    risk_free_rate: float = Field(default=0.02, description="无风险利率")
    time_range: Optional[List[str]] = Field(None, description="时间范围")
    custom_weights: Optional[Dict[str, float]] = Field(None, description="自定义权重")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: Optional[str] = Field(None, description="创建者")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签")

class ComparisonConfigResponse(BaseModel):
    """配置响应模型"""
    config: ComparisonConfig
    message: str

class ComparisonConfigListResponse(BaseModel):
    """配置列表响应模型"""
    configs: List[ComparisonConfig]
    total: int
    page: int
    page_size: int

class ComparisonConfigSearchRequest(BaseModel):
    """配置搜索请求"""
    query: Optional[str] = Field(None, description="搜索关键词")
    tags: Optional[List[str]] = Field(None, description="标签过滤")
    created_by: Optional[str] = Field(None, description="创建者过滤")
    date_from: Optional[datetime] = Field(None, description="创建时间起始")
    date_to: Optional[datetime] = Field(None, description="创建时间结束")

def load_config(config_id: str) -> Optional[ComparisonConfig]:
    """加载配置"""
    config_file = CONFIG_DIR / f"{config_id}.json"
    if not config_file.exists():
        return None
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return ComparisonConfig(**data)
    except Exception as e:
        logger.info(f"加载配置失败: {e}")
        return None

def save_config(config: ComparisonConfig) -> bool:
    """保存配置"""
    try:
        config_file = CONFIG_DIR / f"{config.id}.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config.dict(), f, ensure_ascii=False, indent=2, default=str)
        return True
    except Exception as e:
        logger.info(f"保存配置失败: {e}")
        return False

def delete_config(config_id: str) -> bool:
    """删除配置"""
    try:
        config_file = CONFIG_DIR / f"{config_id}.json"
        if config_file.exists():
            config_file.unlink()
        return True
    except Exception as e:
        logger.info(f"删除配置失败: {e}")
        return False

def list_all_configs() -> List[ComparisonConfig]:
    """列出所有配置"""
    configs = []
    for config_file in CONFIG_DIR.glob("*.json"):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            config = ComparisonConfig(**data)
            configs.append(config)
        except Exception as e:
            logger.info(f"加载配置文件 {config_file} 失败: {e}")
            continue
    
    return configs

def search_configs(search_request: ComparisonConfigSearchRequest) -> List[ComparisonConfig]:
    """搜索配置"""
    configs = list_all_configs()
    
    # 关键词搜索
    if search_request.query:
        query = search_request.query.lower()
        configs = [
            config for config in configs
            if (query in config.name.lower()) or
               (config.description and query in config.description.lower()) or
               any(query in tag.lower() for tag in config.tags)
        ]
    
    # 标签过滤
    if search_request.tags:
        configs = [
            config for config in configs
            if any(tag in config.tags for tag in search_request.tags)
        ]
    
    # 创建者过滤
    if search_request.created_by:
        configs = [
            config for config in configs
            if config.created_by == search_request.created_by
        ]
    
    # 时间范围过滤
    if search_request.date_from:
        configs = [
            config for config in configs
            if config.created_at and config.created_at >= search_request.date_from
        ]
    
    if search_request.date_to:
        configs = [
            config for config in configs
            if config.created_at and config.created_at <= search_request.date_to
        ]
    
    return configs

# API端点

@router.post("/", response_model=ComparisonConfigResponse)
async def create_comparison_config(config: ComparisonConfig):
    """创建对比配置"""
    # 生成ID和时间戳
    config.id = str(uuid.uuid4())
    config.created_at = datetime.now()
    config.updated_at = datetime.now()
    
    # 验证配置
    if not config.selected_strategies:
        raise HTTPException(status_code=400, detail="必须选择至少一个策略")
    
    if len(config.selected_strategies) < 2:
        raise HTTPException(status_code=400, detail="对比分析需要至少两个策略")
    
    if not config.comparison_methods:
        raise HTTPException(status_code=400, detail="必须选择至少一种对比方法")
    
    # 检查名称重复
    existing_configs = list_all_configs()
    if any(c.name == config.name for c in existing_configs):
        raise HTTPException(status_code=400, detail="配置名称已存在")
    
    # 保存配置
    if not save_config(config):
        raise HTTPException(status_code=500, detail="保存配置失败")
    
    return ComparisonConfigResponse(
        config=config,
        message="配置创建成功"
    )

@router.get("/{config_id}", response_model=ComparisonConfigResponse)
async def get_comparison_config(config_id: str):
    """获取对比配置"""
    config = load_config(config_id)
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    return ComparisonConfigResponse(
        config=config,
        message="获取配置成功"
    )

@router.put("/{config_id}", response_model=ComparisonConfigResponse)
async def update_comparison_config(config_id: str, config_update: ComparisonConfig):
    """更新对比配置"""
    existing_config = load_config(config_id)
    if not existing_config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    # 保持原有的ID和创建时间
    config_update.id = config_id
    config_update.created_at = existing_config.created_at
    config_update.updated_at = datetime.now()
    
    # 验证配置
    if not config_update.selected_strategies:
        raise HTTPException(status_code=400, detail="必须选择至少一个策略")
    
    if len(config_update.selected_strategies) < 2:
        raise HTTPException(status_code=400, detail="对比分析需要至少两个策略")
    
    # 检查名称重复（排除自己）
    existing_configs = list_all_configs()
    if any(c.name == config_update.name and c.id != config_id for c in existing_configs):
        raise HTTPException(status_code=400, detail="配置名称已存在")
    
    # 保存配置
    if not save_config(config_update):
        raise HTTPException(status_code=500, detail="更新配置失败")
    
    return ComparisonConfigResponse(
        config=config_update,
        message="配置更新成功"
    )

@router.delete("/{config_id}")
async def delete_comparison_config(config_id: str):
    """删除对比配置"""
    config = load_config(config_id)
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    if not delete_config(config_id):
        raise HTTPException(status_code=500, detail="删除配置失败")
    
    return {"message": "配置删除成功"}

@router.get("/", response_model=ComparisonConfigListResponse)
async def list_comparison_configs(
    page: int = 1,
    page_size: int = 20,
    sort_by: str = "created_at",
    sort_order: str = "desc"
):
    """获取对比配置列表"""
    configs = list_all_configs()
    
    # 排序
    reverse = sort_order.lower() == "desc"
    if sort_by == "created_at":
        configs.sort(key=lambda x: x.created_at or datetime.min, reverse=reverse)
    elif sort_by == "updated_at":
        configs.sort(key=lambda x: x.updated_at or datetime.min, reverse=reverse)
    elif sort_by == "name":
        configs.sort(key=lambda x: x.name, reverse=reverse)
    
    # 分页
    total = len(configs)
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    configs = configs[start_idx:end_idx]
    
    return ComparisonConfigListResponse(
        configs=configs,
        total=total,
        page=page,
        page_size=page_size
    )

@router.post("/search", response_model=ComparisonConfigListResponse)
async def search_comparison_configs(
    search_request: ComparisonConfigSearchRequest,
    page: int = 1,
    page_size: int = 20
):
    """搜索对比配置"""
    configs = search_configs(search_request)
    
    # 按创建时间倒序排列
    configs.sort(key=lambda x: x.created_at or datetime.min, reverse=True)
    
    # 分页
    total = len(configs)
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    configs = configs[start_idx:end_idx]
    
    return ComparisonConfigListResponse(
        configs=configs,
        total=total,
        page=page,
        page_size=page_size
    )

@router.post("/{config_id}/duplicate", response_model=ComparisonConfigResponse)
async def duplicate_comparison_config(config_id: str, new_name: str):
    """复制对比配置"""
    original_config = load_config(config_id)
    if not original_config:
        raise HTTPException(status_code=404, detail="原配置不存在")
    
    # 检查新名称是否重复
    existing_configs = list_all_configs()
    if any(c.name == new_name for c in existing_configs):
        raise HTTPException(status_code=400, detail="配置名称已存在")
    
    # 创建新配置
    new_config = ComparisonConfig(**original_config.dict())
    new_config.id = str(uuid.uuid4())
    new_config.name = new_name
    new_config.description = f"复制自: {original_config.name}"
    new_config.created_at = datetime.now()
    new_config.updated_at = datetime.now()
    
    # 保存新配置
    if not save_config(new_config):
        raise HTTPException(status_code=500, detail="复制配置失败")
    
    return ComparisonConfigResponse(
        config=new_config,
        message="配置复制成功"
    )

@router.get("/{config_id}/export")
async def export_comparison_config(config_id: str):
    """导出对比配置"""
    config = load_config(config_id)
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    # 返回JSON格式的配置
    from fastapi.responses import JSONResponse
    return JSONResponse(
        content=config.dict(),
        headers={
            "Content-Disposition": f"attachment; filename=comparison_config_{config.name}_{config_id}.json"
        }
    )

@router.post("/import", response_model=ComparisonConfigResponse)
async def import_comparison_config(config_data: dict):
    """导入对比配置"""
    try:
        # 验证并创建配置对象
        config = ComparisonConfig(**config_data)
        
        # 重新生成ID和时间戳
        config.id = str(uuid.uuid4())
        config.created_at = datetime.now()
        config.updated_at = datetime.now()
        
        # 检查名称重复，如果重复则自动重命名
        existing_configs = list_all_configs()
        original_name = config.name
        counter = 1
        while any(c.name == config.name for c in existing_configs):
            config.name = f"{original_name}_导入_{counter}"
            counter += 1
        
        # 保存配置
        if not save_config(config):
            raise HTTPException(status_code=500, detail="导入配置失败")
        
        return ComparisonConfigResponse(
            config=config,
            message="配置导入成功"
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"导入配置失败: {str(e)}")

@router.get("/tags/list")
async def list_config_tags():
    """获取所有配置标签"""
    configs = list_all_configs()
    all_tags = set()
    
    for config in configs:
        all_tags.update(config.tags)
    
    return {"tags": sorted(list(all_tags))}

@router.get("/stats/summary")
async def get_config_stats():
    """获取配置统计信息"""
    configs = list_all_configs()
    
    # 统计信息
    total_configs = len(configs)
    
    # 按创建者统计
    creators = {}
    for config in configs:
        creator = config.created_by or "未知"
        creators[creator] = creators.get(creator, 0) + 1
    
    # 按标签统计
    tags = {}
    for config in configs:
        for tag in config.tags:
            tags[tag] = tags.get(tag, 0) + 1
    
    # 按对比方法统计
    methods = {}
    for config in configs:
        for method in config.comparison_methods:
            methods[method] = methods.get(method, 0) + 1
    
    # 最近创建的配置
    recent_configs = sorted(configs, key=lambda x: x.created_at or datetime.min, reverse=True)[:5]
    
    return {
        "total_configs": total_configs,
        "creators": creators,
        "popular_tags": dict(sorted(tags.items(), key=lambda x: x[1], reverse=True)[:10]),
        "popular_methods": dict(sorted(methods.items(), key=lambda x: x[1], reverse=True)),
        "recent_configs": [
            {
                "id": config.id,
                "name": config.name,
                "created_at": config.created_at,
                "strategy_count": len(config.selected_strategies)
            }
            for config in recent_configs
        ]
    }