import logging
logger = logging.getLogger(__name__)
"""
后端应用路径配置模块
统一管理项目路径设置，确保所有模块都能正确导入
"""

import sys
import os
from pathlib import Path

# 获取项目根目录（从 web_ui/backend/app/core/ 向上四级到项目根目录）
CURRENT_FILE = Path(__file__).resolve()
# 目录命名统一为 web_ui
BACKEND_DIR = CURRENT_FILE.parent.parent.parent  # 到web_ui/backend/
PROJECT_ROOT = BACKEND_DIR.parent.parent        # 到项目根目录

# 将项目根目录添加到Python路径
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

# 确保可以导入src模块
try:
    import src
    logger.info(f"✓ 项目路径配置成功: {PROJECT_ROOT}")
except ImportError as e:
    logger.info(f"✗ 项目路径配置失败: {e}")
    logger.info(f"项目根目录: {PROJECT_ROOT}")
    logger.info(f"当前文件: {CURRENT_FILE}")
    logger.info(f"后端目录: {BACKEND_DIR}")
    logger.info(f"当前Python路径: {sys.path}")

# 导出路径变量供其他模块使用
__all__ = ['PROJECT_ROOT', 'BACKEND_DIR']