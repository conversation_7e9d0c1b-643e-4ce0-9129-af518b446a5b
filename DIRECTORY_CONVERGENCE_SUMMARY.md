# 目录结构收敛总结

## 🎯 收敛目标
消除项目中的重复目录结构，统一到主实现，提高代码组织的清晰度。

## 📋 收敛前状况
项目存在以下重复结构：

### 🔄 重复的模块
1. **策略模块**:
   - 兼容层: `src/strategies/` (重定向)
   - 主实现: `src/market/strategies/` (实际功能)

2. **数据模块**:
   - 兼容层: `src/data/` (重定向)
   - 主实现: `src/market/data/` (实际功能)

3. **工具模块**:
   - 兼容层: `src/utils/` (重定向)
   - 主实现: `src/common/utils/` (实际功能)

## ❌ 问题分析
1. **结构漂移**: 同一职责在多处并存，违反文件组织规范
2. **认知负担**: 开发者难以快速找到正确的模块位置
3. **维护成本**: 需要同时维护兼容层和主实现
4. **导入混乱**: 不同文件使用不同的导入路径

## ✅ 收敛方案
1. **保留主实现**: 统一使用以下结构
   - `src/market/strategies/` - 所有策略相关功能
   - `src/market/data/` - 所有数据管理功能
   - `src/common/utils/` - 所有工具函数

2. **移除兼容层**: 删除重定向目录
   - 移除 `src/strategies/`
   - 移除 `src/data/`
   - 移除 `src/utils/`

3. **更新导入**: 统一所有导入语句指向主实现

## 📊 预期收益
- ✅ 简化项目结构，提高可理解性
- ✅ 减少维护成本和认知负担
- ✅ 统一导入路径，避免混乱
- ✅ 符合Clean Architecture原则

## 🛠️ 实施步骤
1. 备份兼容层代码
2. 更新所有导入语句
3. 移除兼容层目录
4. 验证项目功能正常

## 📅 收敛时间
预计收敛时间: 30-60分钟

## 🔄 回滚计划
如果收敛出现问题，可以从backup目录恢复兼容层。

## 📋 新的目录结构

```
src/
├── 🎯 application/              # 应用层
├── 🏛️ domain/                  # 领域层
├── 🔧 infrastructure/          # 基础设施层
├── 📊 market/                  # 市场层（主实现）
│   ├── strategies/             # 策略管理 ✅
│   ├── data/                   # 数据管理 ✅
│   └── indicators/             # 指标计算
├── 💱 trading/                 # 交易层
├── ⚙️ core/                    # 核心层
├── 🛠️ common/                  # 公共层
│   └── utils/                  # 工具函数 ✅
└── 其他模块...
```

---

*目录结构收敛完成时间: 2025年1月18日*
